services:
  business:
    build:
      context: ..
      dockerfile: docker/business/Dockerfile
      args:
        - BUILD_CONTEXT=gestao
    container_name: amigo-business
    ports:
      - "5000:5000"
    env_file:
      # Use .env.business for development/local
      # Use .env.business.production for production
      - .env.business
    networks:
      - amigo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  product:
    build:
      context: ..
      dockerfile: docker/product/Dockerfile
      args:
        - BUILD_CONTEXT=produto/ux-gabi
    container_name: amigo-product
    ports:
      - "5001:5001"
    env_file:
      # Use .env.product for development/local
      # Use .env.product.production for production
      - .env.product
    networks:
      - amigo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5001/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  amigo-network:
    driver: bridge
