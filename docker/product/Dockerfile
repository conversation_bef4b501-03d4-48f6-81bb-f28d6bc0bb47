FROM python:3.9-slim

# Build arguments
ARG BUILD_CONTEXT=produto/ux-gabi

# Configurar timezone, criar usuário e instalar dependências do sistema
ENV TZ=America/Sao_Paulo
RUN ln -snf /usr/share/zoneinfo/"$TZ" /etc/localtime && echo "$TZ" > /etc/timezone && \
    useradd --create-home --shell /bin/bash app && \
    apt-get update && apt-get install -y \
        gcc \
        curl \
    && rm -rf /var/lib/apt/lists/*

# Configurar diretório de trabalho
WORKDIR /app

# Copiar requirements e instalar dependências Python
COPY docker/product/requirements.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copiar código da aplicação (self-contained)
COPY ${BUILD_CONTEXT}/ ./

# Criar diretórios e configurar permissões
RUN mkdir -p /app/data /app/logs && \
    chown -R app:app /app
USER app

# Expor porta
EXPOSE 5001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD ["python", "-c", "import requests; requests.get('http://localhost:5001/health', timeout=5)"]

# Comando de inicialização
CMD ["python", "app.py", "--host", "0.0.0.0", "--port", "5001"]
