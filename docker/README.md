# DataHub Amigo One - Docker Setup

Este diretório contém a configuração Docker para executar as aplicações DataHub Amigo One em containers isolados.

## 📋 Pré-requisitos

- Docker Desktop instalado e rodando
- Docker Compose (incluído no Docker Desktop)
- Colima (se estiver no macOS como alternativa ao Docker Desktop)

## 🏗️ Arquitetura

O projeto está dividido em dois domínios independentes:

- **Business Domain** (porta 5000): Gestão de negócios e onboarding de clientes
- **Product Domain** (porta 5001): Análise de uso e produto após onboarding

## 🚀 Como usar

### Desenvolvimento (SQLite)

#### 1. Navegar para o diretório Docker
```bash
cd docker
```

#### 2. Iniciar todos os containers
```bash
docker-compose up -d
```

#### 3. Verificar status
```bash
docker-compose ps
```

#### 4. Acessar as aplicações
- **Business Domain:** http://localhost:5000
- **Product Domain:** http://localhost:5001

### Produção (PostgreSQL)

#### Opção 1: Variáveis de Ambiente (.env files)
```bash
# Copiar e configurar arquivos de produção
cp .env.business.production .env.business
cp .env.product.production .env.product

# Editar com suas credenciais PostgreSQL
nano .env.business
nano .env.product

# Iniciar containers
docker-compose -f docker-compose.production.yml up -d
```

#### Opção 2: Docker Secrets (MAIS SEGURO) ⭐
```bash
# Verificar se secrets estão configurados
./manage-secrets.sh verify

# Aplicar permissões seguras
./manage-secrets.sh secure

# Iniciar com Docker Secrets
./manage-secrets.sh start

# Ver logs
./manage-secrets.sh logs
```

#### 3. Verificar conectividade com PostgreSQL
```bash
# Ver logs para verificar conexão com banco
docker-compose logs -f business
docker-compose logs -f product

# Ou usando secrets
./manage-secrets.sh logs business
./manage-secrets.sh logs product
```

## 📝 Comandos principais

### Gerenciamento de containers

```bash
# Iniciar todos os containers
docker-compose up -d

# Iniciar apenas um container específico
docker-compose up -d business    # Apenas Business
docker-compose up -d product     # Apenas Product

# Parar containers
docker-compose stop              # Parar todos
docker-compose stop business     # Parar apenas Business
docker-compose stop product      # Parar apenas Product

# Reiniciar containers
docker-compose restart           # Reiniciar todos
docker-compose restart business  # Reiniciar apenas Business

# Remover containers
docker-compose down              # Parar e remover containers
docker-compose down --volumes    # Incluir volumes
docker-compose down --rmi all    # Incluir imagens
```

### Logs e monitoramento

```bash
# Ver logs em tempo real
docker-compose logs -f           # Todos os containers
docker-compose logs -f business  # Apenas Business
docker-compose logs -f product   # Apenas Product

# Ver últimas 50 linhas de log
docker-compose logs --tail=50 business

# Verificar status e saúde dos containers
docker-compose ps
```

### Build e desenvolvimento

```bash
# Rebuild após mudanças no código
docker-compose up -d --build

# Rebuild apenas um container
docker-compose build business
docker-compose up -d business

# Rebuild forçado (sem cache)
docker-compose build --no-cache business
```

## 🔧 Estrutura de arquivos

```
docker/
├── README.md                    # Este arquivo
├── docker-compose.yml          # Configuração principal
├── business/
│   ├── Dockerfile              # Configuração do container Business
│   └── requirements.txt        # Dependências Python do Business
└── product/
    ├── Dockerfile              # Configuração do container Product
    └── requirements.txt        # Dependências Python do Product
```

## 🌐 URLs de acesso

| Serviço | URL | Descrição |
|---------|-----|-----------|
| Business | http://localhost:5000 | Domínio de negócios |
| Product | http://localhost:5001 | Domínio de produto |
| Business Health | http://localhost:5000/health | Health check |
| Product Health | http://localhost:5001/health | Health check |

## 🔍 Troubleshooting

### Container não inicia
```bash
# Verificar logs de erro
docker-compose logs business

# Verificar se as portas estão em uso
lsof -i :5000
lsof -i :5001

# Rebuild forçado
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Problemas de permissão
```bash
# No macOS/Linux, verificar se o Docker tem permissões
sudo docker-compose up -d
```

### Limpar tudo e recomeçar
```bash
# CUIDADO: Remove tudo relacionado ao projeto
docker-compose down --rmi all --volumes --remove-orphans
docker-compose up -d --build
```

## 📊 Monitoramento

### Verificar recursos utilizados
```bash
# Ver uso de CPU/Memória
docker stats

# Ver apenas containers do projeto
docker stats amigo-business amigo-product
```

### Health checks
```bash
# Verificar saúde via curl
curl http://localhost:5000/health
curl http://localhost:5001/health
```

## 🔄 Fluxo de desenvolvimento típico

1. **Primeira execução:**
   ```bash
   cd docker
   docker-compose up -d --build
   docker-compose ps
   ```

2. **Desenvolvimento diário:**
   ```bash
   # Iniciar
   docker-compose up -d
   
   # Após mudanças no código
   docker-compose up -d --build
   
   # Ver logs se necessário
   docker-compose logs -f
   
   # Parar ao final do dia
   docker-compose stop
   ```

3. **Debugging:**
   ```bash
   # Ver logs específicos
   docker-compose logs -f business
   
   # Entrar no container para debug
   docker-compose exec business bash
   ```

## ⚙️ Configurações avançadas

### Variáveis de ambiente
As aplicações usam as seguintes variáveis (definidas no docker-compose.yml):
- `FLASK_ENV=production`
- `SECRET_KEY` (gerada automaticamente se não definida)

### Volumes
- Dados persistentes em `/app/data`
- Logs em `/app/logs`

### Rede
- Rede interna: `amigo-network`
- Comunicação entre containers disponível

## 📚 Comandos úteis de referência

```bash
# Ver todas as imagens Docker
docker images

# Ver todos os containers (incluindo parados)
docker ps -a

# Limpar imagens não utilizadas
docker image prune

# Limpar tudo não utilizado
docker system prune

# Ver uso de espaço
docker system df
```

---

**💡 Dica:** Para facilitar o uso diário, você pode criar aliases no seu shell:

```bash
# Adicionar ao ~/.bashrc ou ~/.zshrc
alias amigo-start="cd docker && docker-compose up -d"
alias amigo-stop="cd docker && docker-compose stop"
alias amigo-logs="cd docker && docker-compose logs -f"
alias amigo-status="cd docker && docker-compose ps"
```
Segurança aplicada:
✅ Permissões 600 nos arquivos de secrets (apenas owner pode ler/escrever)
✅ Permissões 700 no diretório secrets (apenas owner pode acessar)
✅ Secrets não são copiados para imagens Docker
✅ Credenciais são montadas como arquivos temporários nos containers