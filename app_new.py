"""
DataHub Amigo One - Product Domain
Simplified Flask Application without shared dependencies
"""
from flask import Flask, jsonify
import logging
import os
import secrets
from pathlib import Path

# Configure logging first
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def create_app(environment='development'):
    """Create and configure Flask application"""
    app = Flask(__name__)
    
    # Load configuration
    try:
        from config import get_product_config
        config = get_product_config(environment)
        app.config.from_object(config)
        app.secret_key = config.SECRET_KEY
        logger.info("Configuration loaded successfully")
    except ImportError as e:
        logger.warning(f"Failed to load configuration: {e}")
        # Generate secure random key instead of hardcoded fallback
        app.secret_key = secrets.token_hex(32)
        logger.info("Using generated secure key")

    # Register blueprints
    try:
        # Register main routes
        from routes.main_routes import main_routes
        app.register_blueprint(main_routes)
        logger.info("Main routes registered successfully")

        # Register API routes
        from routes.api_routes import api_routes
        app.register_blueprint(api_routes)
        logger.info("API routes registered successfully")

        # Register health routes
        from routes.health_routes import health_routes
        app.register_blueprint(health_routes)
        logger.info("Health routes registered successfully")

    except ImportError as e:
        logger.error(f"Failed to register blueprints: {e}")

    # Setup basic monitoring
    setup_basic_monitoring(app)
    
    return app

def setup_basic_monitoring(app):
    """Setup basic performance monitoring without external dependencies"""
    try:
        @app.before_request
        def track_request_start():
            from flask import g
            import time
            g.start_time = time.time()

        @app.after_request
        def track_request_end(response):
            from flask import g
            import time

            if hasattr(g, 'start_time'):
                response_time = time.time() - g.start_time
                # Simple logging instead of complex metrics
                print(f"Request completed in {response_time:.3f}s")

            # Add basic security headers
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-XSS-Protection'] = '1; mode=block'

            return response

        logger.info("Basic monitoring setup completed")
        return True
    except Exception as e:
        logger.warning(f"Could not setup monitoring: {e}")
        return False

# Create Flask app
app = create_app()

if __name__ == '__main__':
    logger.info("Starting DataHub Amigo One - Product Domain")

    # Get environment configuration
    environment = os.getenv('FLASK_ENV', 'development')
    debug_mode = environment == 'development'

    # Configure app for development
    if debug_mode:
        app.config['DEBUG'] = True
        app.config['TEMPLATES_AUTO_RELOAD'] = True
        app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0
        logger.info("🚀 Running in DEVELOPMENT mode")
        logger.info("📝 Template auto-reload enabled")
        logger.info("🔄 Static file caching disabled")
    else:
        app.config['DEBUG'] = False
        logger.info("🔒 Running in PRODUCTION mode")

    port = int(os.getenv('PORT', 5001))
    logger.info(f"🌐 Server running on http://localhost:{port}")

    app.run(host='0.0.0.0', port=port, debug=debug_mode, use_reloader=debug_mode)
