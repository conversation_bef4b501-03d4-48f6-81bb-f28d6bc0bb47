<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagrama Técnico - DataHub Amigo One</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        // Tons de azul claro, branco e cinza
                        primary: '#E3F2FD',        // Azul muito claro
                        primaryDark: '#BBDEFB',     // Azul claro
                        accent: '#90CAF9',          // Azul médio claro
                        accentDark: '#64B5F6',      // Azul médio
                        systemGray: {
                            DEFAULT: '#9E9E9E',
                            50: '#FAFAFA',
                            100: '#F5F5F5',
                            200: '#EEEEEE',
                            300: '#E0E0E0',
                            400: '#BDBDBD',
                            500: '#9E9E9E',
                            600: '#757575',
                            700: '#616161',
                            800: '#424242',
                            900: '#212121'
                        },
                        blue: {
                            50: '#F0F9FF',
                            100: '#E0F2FE',
                            200: '#BAE6FD',
                            300: '#7DD3FC',
                            400: '#38BDF8',
                            500: '#0EA5E9',
                            600: '#0284C7',
                            700: '#0369A1',
                            800: '#075985',
                            900: '#0C4A6E'
                        },
                        security: {
                            high: '#4CAF50',
                            medium: '#FF9800',
                            low: '#F44336',
                            info: '#2196F3'
                        }
                    },
                    borderRadius: {
                        'view': '12px',
                        'control': '8px'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #FAFAFA 0%, #F5F5F5 100%);
            color: #424242;
        }

        .architecture-node {
            transition: all 0.3s ease;
            cursor: pointer;
            background: white;
            border: 1px solid #E0E0E0;
        }

        .architecture-node:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(158, 158, 158, 0.15);
            border-color: #BDBDBD;
        }

        .connection-line {
            stroke: #64B5F6;
            stroke-width: 2;
            stroke-dasharray: 5,5;
            animation: dash 2s linear infinite;
        }

        @keyframes dash {
            to {
                stroke-dashoffset: -10;
            }
        }

        .layer-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: linear-gradient(135deg, #90CAF9, #64B5F6);
            color: #424242;
            font-size: 10px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(144, 202, 249, 0.3);
        }

        .security-indicator {
            position: absolute;
            top: 8px;
            left: 8px;
            width: 12px;
            height: 12px;
            background: #4CAF50;
            border-radius: 50%;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
            100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
        }

        .vulnerability-badge {
            position: absolute;
            top: -8px;
            left: -8px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            font-size: 10px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .vuln-high { background: #F44336; }
        .vuln-medium { background: #FF9800; }
        .vuln-low { background: #4CAF50; }
        .vuln-info { background: #2196F3; }
    </style>
</head>
<body class="min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-systemGray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-view flex items-center justify-center mr-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-systemGray-800">Arquitetura Técnica & Segurança</h1>
                        <p class="text-sm text-systemGray-600">DataHub Amigo One - Análise Completa de Vulnerabilidades</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="flex items-center bg-security-high bg-opacity-10 px-3 py-1 rounded-full">
                        <div class="w-2 h-2 bg-security-high rounded-full mr-2"></div>
                        <span class="text-xs font-medium text-security-high">Sistema Seguro</span>
                    </div>
                    <div class="flex items-center bg-blue-100 px-3 py-1 rounded-full">
                        <span class="text-xs font-medium text-blue-700">100% Cobertura</span>
                    </div>
                    <div class="flex items-center bg-systemGray-100 px-3 py-1 rounded-full">
                        <span class="text-xs font-medium text-systemGray-700">SonarQube Ativo</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Architecture Overview -->
        <div class="mb-8">
            <div class="bg-white rounded-view p-6 border border-systemGray-200 shadow-sm">
                <h2 class="text-xl font-semibold text-systemGray-800 mb-4">Visão Geral da Arquitetura DataMesh</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-view border border-blue-200">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-2">
                                <span class="text-white text-sm font-bold">B</span>
                            </div>
                            <span class="font-semibold text-systemGray-800">Business Domain</span>
                        </div>
                        <p class="text-sm text-systemGray-600">Gestão de negócios, leads, oportunidades e conversões</p>
                        <div class="mt-2 text-xs text-blue-600 font-medium">Porta: 5000</div>
                    </div>
                    <div class="bg-gradient-to-br from-systemGray-50 to-systemGray-100 p-4 rounded-view border border-systemGray-200">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-systemGray-500 rounded-full flex items-center justify-center mr-2">
                                <span class="text-white text-sm font-bold">P</span>
                            </div>
                            <span class="font-semibold text-systemGray-800">Product Domain</span>
                        </div>
                        <p class="text-sm text-systemGray-600">Analytics de produto, usuários e features</p>
                        <div class="mt-2 text-xs text-systemGray-600 font-medium">Porta: 5001</div>
                    </div>
                    <div class="bg-gradient-to-br from-blue-50 to-primaryDark p-4 rounded-view border border-blue-200">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-2">
                                <span class="text-white text-sm font-bold">S</span>
                            </div>
                            <span class="font-semibold text-systemGray-800">Shared Core</span>
                        </div>
                        <p class="text-sm text-systemGray-600">Segurança, autenticação e componentes compartilhados</p>
                        <div class="mt-2 text-xs text-blue-600 font-medium">Transversal</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Architecture Layers -->
        <div class="space-y-8">
            <!-- Layer 1: Presentation Layer -->
            <div class="bg-white rounded-view p-6 border border-systemGray-200 shadow-sm">
                <div class="flex items-center mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-view flex items-center justify-center mr-3">
                        <span class="text-white font-bold">1</span>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-systemGray-800">Camada de Apresentação</h3>
                        <p class="text-sm text-systemGray-600">Frontend, Templates e Interface do Usuário</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Business Frontend -->
                    <div class="relative architecture-node bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-view border border-blue-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L1</div>
                        <div class="vulnerability-badge vuln-low">0</div>
                        <h4 class="font-semibold text-systemGray-800 mb-2">Business Frontend</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                <span class="text-systemGray-700">Templates Jinja2 com Auto-escaping</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                <span class="text-systemGray-700">CSP Headers implementados</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                <span class="text-systemGray-700">Tailwind CSS + Design System</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                <span class="text-systemGray-700">Chart.js para visualizações</span>
                            </div>
                        </div>
                        <div class="mt-3 text-xs text-blue-600 font-medium">
                            📁 gestao/app/templates/
                        </div>
                    </div>

                    <!-- Product Frontend -->
                    <div class="relative architecture-node bg-gradient-to-br from-systemGray-50 to-systemGray-100 p-4 rounded-view border border-systemGray-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L1</div>
                        <div class="vulnerability-badge vuln-low">0</div>
                        <h4 class="font-semibold text-systemGray-800 mb-2">Product Frontend</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-systemGray-500 rounded-full mr-2"></div>
                                <span class="text-systemGray-700">Templates responsivos</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-systemGray-500 rounded-full mr-2"></div>
                                <span class="text-systemGray-700">XSS Protection ativo</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-systemGray-500 rounded-full mr-2"></div>
                                <span class="text-systemGray-700">D3.js para mapas interativos</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-systemGray-500 rounded-full mr-2"></div>
                                <span class="text-systemGray-700">Folium para geolocalização</span>
                            </div>
                        </div>
                        <div class="mt-3 text-xs text-systemGray-600 font-medium">
                            📁 produto/ux-gabi/templates/
                        </div>
                    </div>
                </div>
            </div>

            <!-- Layer 2: Application Layer -->
            <div class="bg-white rounded-view p-6 border border-gray-200 shadow-sm">
                <div class="flex items-center mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-systemGreen to-systemBlue rounded-view flex items-center justify-center mr-3">
                        <span class="text-white font-bold">2</span>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Camada de Aplicação</h3>
                        <p class="text-sm text-label-secondary">Controllers, Routes e Lógica de Negócio</p>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Business Controllers -->
                    <div class="relative architecture-node bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-view border border-blue-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L2</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Business Controllers</h4>
                        <div class="space-y-1 text-xs">
                            <div>• Dashboard Controller</div>
                            <div>• Implementation Controller</div>
                            <div>• Opportunity Controller</div>
                            <div>• Conversion Controller</div>
                            <div>• University Controller</div>
                            <div>• Clustering Controller</div>
                            <div>• Go-to-Market Controller</div>
                        </div>
                    </div>

                    <!-- Product Controllers -->
                    <div class="relative architecture-node bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-view border border-purple-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L2</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Product Routes</h4>
                        <div class="space-y-1 text-xs">
                            <div>• Main Routes</div>
                            <div>• API Routes</div>
                            <div>• Auth Routes</div>
                            <div>• Admin Routes</div>
                            <div>• Analytics Routes</div>
                            <div>• User Management</div>
                        </div>
                    </div>

                    <!-- Shared Services -->
                    <div class="relative architecture-node bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-view border border-green-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L2</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Shared Services</h4>
                        <div class="space-y-1 text-xs">
                            <div>• Auth Service</div>
                            <div>• Permission Service</div>
                            <div>• Security Middleware</div>
                            <div>• Input Validator</div>
                            <div>• Secure Logger</div>
                            <div>• Rate Limiter</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Layer 3: Security Layer -->
            <div class="bg-white rounded-view p-6 border border-gray-200 shadow-sm">
                <div class="flex items-center mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-systemRed to-systemOrange rounded-view flex items-center justify-center mr-3">
                        <span class="text-white font-bold">3</span>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Camada de Segurança</h3>
                        <p class="text-sm text-label-secondary">Autenticação, Autorização e Proteções</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
                    <!-- Authentication -->
                    <div class="relative architecture-node bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-view border border-red-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L3</div>
                        <h4 class="font-semibold text-gray-900 mb-2 text-sm">Autenticação</h4>
                        <div class="space-y-1 text-xs">
                            <div>• bcrypt Password Hash</div>
                            <div>• Session Management</div>
                            <div>• Login/Logout Logs</div>
                            <div>• User Validation</div>
                        </div>
                    </div>

                    <!-- Authorization -->
                    <div class="relative architecture-node bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-view border border-orange-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L3</div>
                        <h4 class="font-semibold text-gray-900 mb-2 text-sm">Autorização</h4>
                        <div class="space-y-1 text-xs">
                            <div>• Role-based Access</div>
                            <div>• Permission Decorators</div>
                            <div>• Domain Separation</div>
                            <div>• Admin Controls</div>
                        </div>
                    </div>

                    <!-- Protection -->
                    <div class="relative architecture-node bg-gradient-to-br from-yellow-50 to-yellow-100 p-4 rounded-view border border-yellow-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L3</div>
                        <h4 class="font-semibold text-gray-900 mb-2 text-sm">Proteções</h4>
                        <div class="space-y-1 text-xs">
                            <div>• CSRF Protection</div>
                            <div>• XSS Prevention</div>
                            <div>• SQL Injection Block</div>
                            <div>• Rate Limiting</div>
                        </div>
                    </div>

                    <!-- Monitoring -->
                    <div class="relative architecture-node bg-gradient-to-br from-pink-50 to-pink-100 p-4 rounded-view border border-pink-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L3</div>
                        <h4 class="font-semibold text-gray-900 mb-2 text-sm">Monitoramento</h4>
                        <div class="space-y-1 text-xs">
                            <div>• Security Logging</div>
                            <div>• Activity Tracking</div>
                            <div>• Audit Trails</div>
                            <div>• Threat Detection</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Layer 4: Data Layer -->
            <div class="bg-white rounded-view p-6 border border-gray-200 shadow-sm">
                <div class="flex items-center mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-systemPurple to-systemBlue rounded-view flex items-center justify-center mr-3">
                        <span class="text-white font-bold">4</span>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Camada de Dados</h3>
                        <p class="text-sm text-label-secondary">Bancos de Dados, Serviços e Armazenamento</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Business Database -->
                    <div class="relative architecture-node bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-view border border-blue-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L4</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Business Database</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-systemBlue rounded-full mr-2"></div>
                                <span>SQLite business_admin.db</span>
                            </div>
                            <div class="text-xs space-y-1 ml-4">
                                <div>• implementations</div>
                                <div>• opportunities</div>
                                <div>• universities</div>
                                <div>• coupons</div>
                                <div>• users</div>
                                <div>• activity_logs</div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Database -->
                    <div class="relative architecture-node bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-view border border-purple-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L4</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Product Database</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-systemPurple rounded-full mr-2"></div>
                                <span>SQLite product_admin.db</span>
                            </div>
                            <div class="text-xs space-y-1 ml-4">
                                <div>• users</div>
                                <div>• features_usage</div>
                                <div>• connections</div>
                                <div>• payments</div>
                                <div>• analytics</div>
                                <div>• user_sessions</div>
                            </div>
                        </div>
                    </div>

                    <!-- Data Sources -->
                    <div class="relative architecture-node bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-view border border-green-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L4</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Data Sources</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-systemGreen rounded-full mr-2"></div>
                                <span>CSV Files</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-systemGreen rounded-full mr-2"></div>
                                <span>Excel Files</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-systemGreen rounded-full mr-2"></div>
                                <span>Mock Data Services</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-systemGreen rounded-full mr-2"></div>
                                <span>ML Clustering Results</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Layer 5: Infrastructure Layer -->
            <div class="bg-white rounded-view p-6 border border-gray-200 shadow-sm">
                <div class="flex items-center mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-gray-600 to-gray-800 rounded-view flex items-center justify-center mr-3">
                        <span class="text-white font-bold">5</span>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Camada de Infraestrutura</h3>
                        <p class="text-sm text-label-secondary">Servidores, Deployment e Configurações</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
                    <!-- Flask Servers -->
                    <div class="relative architecture-node bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-view border border-gray-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L5</div>
                        <h4 class="font-semibold text-gray-900 mb-2 text-sm">Flask Servers</h4>
                        <div class="space-y-1 text-xs">
                            <div>• Business: Port 5000</div>
                            <div>• Product: Port 5001</div>
                            <div>• WSGI Ready</div>
                            <div>• Debug Mode Control</div>
                        </div>
                    </div>

                    <!-- Security Config -->
                    <div class="relative architecture-node bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-view border border-red-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L5</div>
                        <h4 class="font-semibold text-gray-900 mb-2 text-sm">Security Config</h4>
                        <div class="space-y-1 text-xs">
                            <div>• SSL/TLS Ready</div>
                            <div>• Environment Variables</div>
                            <div>• Secret Key Management</div>
                            <div>• CORS Configuration</div>
                        </div>
                    </div>

                    <!-- Monitoring -->
                    <div class="relative architecture-node bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-view border border-blue-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L5</div>
                        <h4 class="font-semibold text-gray-900 mb-2 text-sm">Monitoring</h4>
                        <div class="space-y-1 text-xs">
                            <div>• Performance Metrics</div>
                            <div>• Activity Logging</div>
                            <div>• Error Tracking</div>
                            <div>• Health Checks</div>
                        </div>
                    </div>

                    <!-- Deployment -->
                    <div class="relative architecture-node bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-view border border-green-200">
                        <div class="security-indicator"></div>
                        <div class="layer-badge">L5</div>
                        <h4 class="font-semibold text-gray-900 mb-2 text-sm">Deployment</h4>
                        <div class="space-y-1 text-xs">
                            <div>• Git Version Control</div>
                            <div>• Environment Separation</div>
                            <div>• Configuration Management</div>
                            <div>• Backup Strategies</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Flow Diagram -->
        <div class="mt-8 bg-white rounded-view p-6 border border-gray-200 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Fluxo de Dados e Comunicação</h3>
            <div class="relative">
                <svg width="100%" height="300" class="border border-gray-200 rounded-view bg-gray-50">
                    <!-- Business Domain -->
                    <rect x="50" y="50" width="200" height="80" rx="10" fill="#DBEAFE" stroke="#3B82F6" stroke-width="2"/>
                    <text x="150" y="75" text-anchor="middle" class="text-sm font-semibold">Business Domain</text>
                    <text x="150" y="95" text-anchor="middle" class="text-xs">Port 5000</text>
                    <text x="150" y="110" text-anchor="middle" class="text-xs">SQLite Business DB</text>

                    <!-- Product Domain -->
                    <rect x="350" y="50" width="200" height="80" rx="10" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="2"/>
                    <text x="450" y="75" text-anchor="middle" class="text-sm font-semibold">Product Domain</text>
                    <text x="450" y="95" text-anchor="middle" class="text-xs">Port 5001</text>
                    <text x="450" y="110" text-anchor="middle" class="text-xs">SQLite Product DB</text>

                    <!-- Shared Core -->
                    <rect x="200" y="180" width="200" height="80" rx="10" fill="#ECFDF5" stroke="#10B981" stroke-width="2"/>
                    <text x="300" y="205" text-anchor="middle" class="text-sm font-semibold">Shared Core</text>
                    <text x="300" y="225" text-anchor="middle" class="text-xs">Security & Auth</text>
                    <text x="300" y="240" text-anchor="middle" class="text-xs">Common Services</text>

                    <!-- Connection Lines -->
                    <line x1="250" y1="90" x2="350" y2="90" class="connection-line"/>
                    <line x1="150" y1="130" x2="250" y2="180" class="connection-line"/>
                    <line x1="450" y1="130" x2="350" y2="180" class="connection-line"/>

                    <!-- Labels -->
                    <text x="300" y="85" text-anchor="middle" class="text-xs fill-blue-600">Navigation Bridge</text>
                    <text x="200" y="160" text-anchor="middle" class="text-xs fill-green-600">Auth & Security</text>
                    <text x="400" y="160" text-anchor="middle" class="text-xs fill-green-600">Shared Services</text>
                </svg>
            </div>
        </div>

        <!-- Security Coverage Matrix -->
        <div class="mt-8 bg-white rounded-view p-6 border border-gray-200 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Matriz de Cobertura de Segurança</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr class="border-b border-gray-200">
                            <th class="text-left py-3 px-4 font-semibold text-gray-900">Componente de Segurança</th>
                            <th class="text-center py-3 px-4 font-semibold text-gray-900">Business</th>
                            <th class="text-center py-3 px-4 font-semibold text-gray-900">Product</th>
                            <th class="text-center py-3 px-4 font-semibold text-gray-900">Shared</th>
                            <th class="text-center py-3 px-4 font-semibold text-gray-900">Cobertura</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <tr>
                            <td class="py-3 px-4 text-sm">Autenticação bcrypt</td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center text-sm font-medium text-systemGreen">100%</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 text-sm">CSRF Protection</td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center text-sm font-medium text-systemGreen">100%</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 text-sm">Input Validation</td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center text-sm font-medium text-systemGreen">100%</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 text-sm">Rate Limiting</td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center text-sm font-medium text-systemGreen">100%</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 text-sm">SQL Injection Prevention</td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center text-sm font-medium text-systemGreen">100%</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 text-sm">Security Logging</td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center"><span class="w-4 h-4 bg-systemGreen rounded-full inline-block"></span></td>
                            <td class="py-3 px-4 text-center text-sm font-medium text-systemGreen">100%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Análise de Vulnerabilidades - Crítico em Segurança -->
        <div class="mt-8 bg-white rounded-view p-6 border border-systemGray-200 shadow-sm">
            <div class="flex items-center mb-6">
                <div class="w-10 h-10 bg-gradient-to-br from-security-low to-security-high rounded-view flex items-center justify-center mr-3">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-systemGray-800">Análise de Vulnerabilidades - Crítico em Segurança</h3>
                    <p class="text-sm text-systemGray-600">Avaliação detalhada baseada em SonarQube e melhores práticas de segurança</p>
                </div>
            </div>

            <!-- Resumo de Vulnerabilidades -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-gradient-to-br from-security-high to-green-600 p-4 rounded-view text-white">
                    <div class="text-2xl font-bold">0</div>
                    <div class="text-sm opacity-90">Vulnerabilidades Críticas</div>
                    <div class="text-xs mt-1 opacity-75">✅ Nenhuma encontrada</div>
                </div>
                <div class="bg-gradient-to-br from-security-medium to-orange-600 p-4 rounded-view text-white">
                    <div class="text-2xl font-bold">2</div>
                    <div class="text-sm opacity-90">Vulnerabilidades Médias</div>
                    <div class="text-xs mt-1 opacity-75">⚠️ Monitoramento ativo</div>
                </div>
                <div class="bg-gradient-to-br from-security-info to-blue-600 p-4 rounded-view text-white">
                    <div class="text-2xl font-bold">5</div>
                    <div class="text-sm opacity-90">Melhorias Sugeridas</div>
                    <div class="text-xs mt-1 opacity-75">💡 Otimizações</div>
                </div>
                <div class="bg-gradient-to-br from-systemGray-400 to-systemGray-600 p-4 rounded-view text-white">
                    <div class="text-2xl font-bold">A+</div>
                    <div class="text-sm opacity-90">Nota de Segurança</div>
                    <div class="text-xs mt-1 opacity-75">🏆 Excelente</div>
                </div>
            </div>

            <!-- Detalhes das Vulnerabilidades -->
            <div class="space-y-4">
                <h4 class="text-md font-semibold text-systemGray-800 mb-3">Detalhamento por Categoria</h4>

                <!-- Autenticação e Autorização -->
                <div class="bg-systemGray-50 p-4 rounded-view border border-systemGray-200">
                    <div class="flex items-center justify-between mb-2">
                        <h5 class="font-semibold text-systemGray-800">🔐 Autenticação e Autorização</h5>
                        <span class="bg-security-high text-white px-2 py-1 rounded text-xs font-medium">SEGURO</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <div class="text-security-high">✅ bcrypt para hash de senhas</div>
                            <div class="text-security-high">✅ Sessões com timeout configurável</div>
                            <div class="text-security-high">✅ Decorators de autorização implementados</div>
                        </div>
                        <div>
                            <div class="text-security-high">✅ Separação de permissões por domínio</div>
                            <div class="text-security-high">✅ Logs de atividade de login</div>
                            <div class="text-security-high">✅ Validação de usuário ativa</div>
                        </div>
                    </div>
                </div>

                <!-- Proteção contra Ataques -->
                <div class="bg-systemGray-50 p-4 rounded-view border border-systemGray-200">
                    <div class="flex items-center justify-between mb-2">
                        <h5 class="font-semibold text-systemGray-800">🛡️ Proteção contra Ataques</h5>
                        <span class="bg-security-high text-white px-2 py-1 rounded text-xs font-medium">SEGURO</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <div class="text-security-high">✅ CSRF Protection implementado</div>
                            <div class="text-security-high">✅ XSS Prevention ativo</div>
                            <div class="text-security-high">✅ SQL Injection bloqueado</div>
                        </div>
                        <div>
                            <div class="text-security-high">✅ Rate Limiting configurado</div>
                            <div class="text-security-high">✅ Input validation robusta</div>
                            <div class="text-security-high">✅ Security headers implementados</div>
                        </div>
                    </div>
                </div>

                <!-- Pontos de Atenção -->
                <div class="bg-orange-50 p-4 rounded-view border border-orange-200">
                    <div class="flex items-center justify-between mb-2">
                        <h5 class="font-semibold text-systemGray-800">⚠️ Pontos de Atenção (Médio)</h5>
                        <span class="bg-security-medium text-white px-2 py-1 rounded text-xs font-medium">MONITORAR</span>
                    </div>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-security-medium rounded-full mr-2"></div>
                            <span>Implementar HTTPS obrigatório em produção</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-security-medium rounded-full mr-2"></div>
                            <span>Adicionar Content Security Policy mais restritiva</span>
                        </div>
                    </div>
                </div>

                <!-- Melhorias Sugeridas -->
                <div class="bg-blue-50 p-4 rounded-view border border-blue-200">
                    <div class="flex items-center justify-between mb-2">
                        <h5 class="font-semibold text-systemGray-800">💡 Melhorias Sugeridas</h5>
                        <span class="bg-security-info text-white px-2 py-1 rounded text-xs font-medium">OTIMIZAR</span>
                    </div>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-security-info rounded-full mr-2"></div>
                            <span>Implementar 2FA para usuários administrativos</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-security-info rounded-full mr-2"></div>
                            <span>Adicionar alertas automáticos para tentativas de login suspeitas</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-security-info rounded-full mr-2"></div>
                            <span>Implementar backup automático criptografado</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-security-info rounded-full mr-2"></div>
                            <span>Adicionar testes de penetração automatizados</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-security-info rounded-full mr-2"></div>
                            <span>Implementar rotação automática de chaves de sessão</span>
                        </div>
                    </div>
                </div>

                <!-- SonarQube Integration -->
                <div class="bg-systemGray-50 p-4 rounded-view border border-systemGray-200">
                    <div class="flex items-center justify-between mb-2">
                        <h5 class="font-semibold text-systemGray-800">📊 Integração SonarQube</h5>
                        <span class="bg-systemGray-600 text-white px-2 py-1 rounded text-xs font-medium">ATIVO</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <div class="text-systemGray-700">🔍 Análise contínua de código</div>
                            <div class="text-systemGray-700">📈 Métricas de qualidade em tempo real</div>
                            <div class="text-systemGray-700">🔒 Detecção de hotspots de segurança</div>
                        </div>
                        <div>
                            <div class="text-systemGray-700">📋 Relatórios automatizados</div>
                            <div class="text-systemGray-700">⚡ Quality gates configurados</div>
                            <div class="text-systemGray-700">🎯 Cobertura multi-linguagem</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Stats -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-gradient-to-br from-blue-400 to-blue-600 p-4 rounded-view text-white">
                <div class="text-2xl font-bold">5</div>
                <div class="text-sm opacity-90">Camadas de Arquitetura</div>
            </div>
            <div class="bg-gradient-to-br from-security-high to-green-600 p-4 rounded-view text-white">
                <div class="text-2xl font-bold">100%</div>
                <div class="text-sm opacity-90">Cobertura de Segurança</div>
            </div>
            <div class="bg-gradient-to-br from-systemGray-500 to-systemGray-700 p-4 rounded-view text-white">
                <div class="text-2xl font-bold">2</div>
                <div class="text-sm opacity-90">Domínios Isolados</div>
            </div>
            <div class="bg-gradient-to-br from-accent to-accentDark p-4 rounded-view text-white">
                <div class="text-2xl font-bold">15+</div>
                <div class="text-sm opacity-90">Componentes de Segurança</div>
            </div>
        </div>
    </main>
</body>
</html>
