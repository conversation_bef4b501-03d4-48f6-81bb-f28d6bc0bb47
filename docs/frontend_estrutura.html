<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arquitetura do Frontend - DataHub Amigo One</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f8fafc; color: #222; margin: 0; padding: 0; }
        .container { max-width: 900px; margin: 40px auto; background: #fff; border-radius: 10px; box-shadow: 0 2px 8px #0001; padding: 32px; }
        h1, h2, h3 { color: #2563eb; }
        h1 { font-size: 2.2rem; margin-bottom: 0.5em; }
        h2 { font-size: 1.5rem; margin-top: 2em; }
        h3 { font-size: 1.1rem; margin-top: 1.5em; }
        pre { background: #f1f5f9; border-radius: 6px; padding: 12px; overflow-x: auto; font-size: 0.95em; }
        code { color: #0f172a; }
        .domain { margin-bottom: 2.5em; }
        .evidence { background: #e0e7ef; border-left: 4px solid #2563eb; padding: 10px 18px; margin: 1em 0; border-radius: 6px; }
        .concept { background: #fef9c3; border-left: 4px solid #facc15; padding: 10px 18px; margin: 1em 0; border-radius: 6px; }
        ul { margin-left: 1.5em; }
    </style>
</head>
<body>
<div class="container">
    <h1>Arquitetura do Frontend<br>DataHub Amigo One</h1>
    <p>Este documento detalha como o frontend do projeto está estruturado, dividido por domínios (<b>Business</b> e <b>Product</b>), apresentando evidências de código e explicando os conceitos por trás da implementação.</p>

    <h2>1. Visão Geral</h2>
    <div class="concept">
        <b>O frontend do DataHub Amigo One é gerado no servidor usando Jinja2 (Flask), com templates HTML organizados por domínio e funcionalidade.</b> O backend processa dados, renderiza os templates e envia HTML pronto ao navegador.
    </div>
    <ul>
        <li>Templates organizados em <code>gestao/app/templates/</code> (Business) e <code>produto/ux-gabi/templates/</code> (Product)</li>
        <li>Uso extensivo de macros, componentes e herança de templates</li>
        <li>Estilo e interatividade via TailwindCSS e JavaScript</li>
    </ul>

    <h2>2. Domínio Business</h2>
    <div class="domain">
        <h3>Estrutura de Templates</h3>
        <ul>
            <li><b>Base:</b> <code>base.html</code> (estrutura principal, herança)</li>
            <li><b>Funcionais:</b> <code>dashboard/index.html</code>, <code>leads/index.html</code>, <code>opportunities/index.html</code>, etc.</li>
            <li><b>Componentes:</b> <code>components/</code>, <code>macros/</code> (cards, tooltips, etc.)</li>
        </ul>
        <div class="evidence">
            <b>Exemplo de herança de template:</b>
            <pre><code>{% extends "base.html" %}
{% block content %}
    &lt;h1&gt;Dashboard&lt;/h1&gt;
    &lt;p&gt;Total de Leads: {{ total_leads }}&lt;/p&gt;
{% endblock %}</code></pre>
        </div>
        <div class="evidence">
            <b>Exemplo de macro/component:</b>
            <pre><code>{% from "macros/components.html" import kpi_card %}
{{ kpi_card(title="MRR Total", value=mrr_total) }}</code></pre>
        </div>
        <div class="concept">
            <b>Conceito:</b> O backend Flask processa dados, formata e injeta nos templates Jinja2. O HTML final já vem "pronto" para o navegador, com dados dinâmicos.
        </div>
        <h3>Renderização no Backend</h3>
        <div class="evidence">
            <b>Exemplo de renderização no controller:</b>
            <pre><code>from flask import render_template

@app.route('/dashboard')
def dashboard():
    total_leads = ...
    return render_template('dashboard/index.html', total_leads=total_leads)</code></pre>
        </div>
        <h3>Organização dos Arquivos</h3>
        <ul>
            <li><code>gestao/app/templates/</code> - Templates HTML</li>
            <li><code>gestao/app/static/</code> - CSS, JS, imagens</li>
        </ul>
        <div class="concept">
            <b>Limitação:</b> Como o HTML é gerado no backend, não é possível desacoplar totalmente frontend e backend sem reescrever para consumir APIs REST e usar frameworks JS modernos (React, Vue, etc).
        </div>
    </div>

    <h2>3. Domínio Product</h2>
    <div class="domain">
        <h3>Estrutura de Templates</h3>
        <ul>
            <li><b>Base:</b> <code>base.html</code> (estrutura principal)</li>
            <li><b>Funcionais:</b> <code>dashboard.html</code>, <code>usuarios.html</code>, <code>contabilidade.html</code>, etc.</li>
        </ul>
        <div class="evidence">
            <b>Exemplo de template Product:</b>
            <pre><code>{% extends "base.html" %}
{% block content %}
    &lt;h2&gt;Usuários do Produto&lt;/h2&gt;
    &lt;ul&gt;
        {% for user in users %}
            &lt;li&gt;{{ user.nome }}&lt;/li&gt;
        {% endfor %}
    &lt;/ul&gt;
{% endblock %}</code></pre>
        </div>
        <h3>Renderização no Backend</h3>
        <div class="evidence">
            <b>Exemplo de renderização Product:</b>
            <pre><code>from flask import render_template

@app.route('/usuarios')
def usuarios():
    users = ...
    return render_template('usuarios.html', users=users)</code></pre>
        </div>
        <h3>Organização dos Arquivos</h3>
        <ul>
            <li><code>produto/ux-gabi/templates/</code> - Templates HTML</li>
            <li><code>produto/ux-gabi/static/</code> - CSS, JS, imagens</li>
        </ul>
        <div class="concept">
            <b>Conceito:</b> O domínio Product segue o mesmo padrão do Business, com renderização server-side e uso de Jinja2.
        </div>
    </div>

    <h2>4. Macros, Herança e Componentes nos Templates</h2>
    <div class="domain">
        <h3>Herança de Templates</h3>
        <div class="concept">
            <b>Herança</b> permite que todos os templates compartilhem uma estrutura base (ex: menus, rodapé, estilos globais), evitando repetição de código.
        </div>
        <div class="evidence">
            <b>Exemplo:</b>
            <pre><code>{% extends "base.html" %}
{% block content %}
    &lt;h1&gt;Página Específica&lt;/h1&gt;
{% endblock %}</code></pre>
        </div>
        <h3>Macros</h3>
        <div class="concept">
            <b>Macros</b> são "funções" reutilizáveis dentro dos templates, ideais para criar componentes visuais padronizados (cards, tooltips, KPIs, etc.).
        </div>
        <div class="evidence">
            <b>Definição de macro:</b>
            <pre><code>{% macro kpi_card(title, value) %}
&lt;div class="kpi-card"&gt;
    &lt;h4&gt;{{ title }}&lt;/h4&gt;
    &lt;p&gt;{{ value }}&lt;/p&gt;
&lt;/div&gt;
{% endmacro %}</code></pre>
            <b>Uso do macro:</b>
            <pre><code>{% from "macros/components.html" import kpi_card %}
{{ kpi_card("MRR Total", mrr_total) }}</code></pre>
        </div>
        <h3>Componentes</h3>
        <div class="concept">
            <b>Componentes</b> são blocos de HTML (parciais) que podem ser incluídos em vários templates, como mensagens de erro, cards, tabelas, etc.
        </div>
        <div class="evidence">
            <b>Inclusão de componente:</b>
            <pre><code>{% include "components/error_message.html" %}</code></pre>
        </div>
        <ul>
            <li>Macros ficam em <code>macros/components.html</code></li>
            <li>Componentes ficam em <code>components/</code></li>
            <li>Templates funcionais estendem <code>base.html</code> e usam macros/componentes conforme necessário</li>
        </ul>
    </div>

    <h2>5. Jinja2: Funcionamento e Fluxo End-to-End</h2>
    <div class="domain">
        <h3>O que é Jinja2?</h3>
        <div class="concept">
            <b>Jinja2</b> é o motor de templates do Flask. Ele permite misturar lógica de apresentação (for, if, filtros) com HTML, gerando páginas dinâmicas no servidor.
        </div>
        <h3>Como o Jinja2 é implementado no projeto?</h3>
        <ol>
            <li><b>Controller/Route</b> processa dados no Python</li>
            <li>Chama <code>render_template('template.html', dados...)</code></li>
            <li>O Flask/Jinja2 localiza o template (ex: <code>dashboard/index.html</code>)</li>
            <li>O template pode <b>estender</b> <code>base.html</code>, <b>importar macros</b> e <b>incluir componentes</b></li>
            <li>Jinja2 substitui variáveis (<code>{{ }}</code>), executa blocos (<code>{% %}</code>), aplica filtros e monta o HTML final</li>
            <li>O HTML pronto é enviado ao navegador</li>
        </ol>
        <div class="evidence">
            <b>Fluxo End-to-End:</b>
            <pre><code># Controller (Python)
@app.route('/dashboard')
def dashboard():
    mrr_total = calcular_mrr()
    return render_template('dashboard/index.html', mrr_total=mrr_total)

# dashboard/index.html
{% extends "base.html" %}
{% from "macros/components.html" import kpi_card %}
{% block content %}
    {{ kpi_card("MRR Total", mrr_total) }}
{% endblock %}

# macros/components.html
{% macro kpi_card(title, value) %}
&lt;div class="kpi-card"&gt;
    &lt;h4&gt;{{ title }}&lt;/h4&gt;
    &lt;p&gt;{{ value }}&lt;/p&gt;
&lt;/div&gt;
{% endmacro %}
</code></pre>
        </div>
        <h3>Resumo Visual do Fluxo</h3>
        <div class="evidence">
            <pre><code>Usuário faz requisição → Controller processa dados → render_template → Jinja2 monta HTML (herança, macros, componentes) → HTML pronto enviado ao navegador</code></pre>
        </div>
        <div class="concept">
            <b>Vantagem:</b> Permite criar páginas dinâmicas, reutilizar componentes e manter o código organizado e DRY.
        </div>
    </div>

    <h2>6. Estilo e Interatividade</h2>
    <div class="domain">
        <h3>CSS e JS</h3>
        <ul>
            <li>TailwindCSS para estilização rápida e responsiva</li>
            <li>JavaScript para interatividade (ex: gráficos, navegação, animações)</li>
        </ul>
        <div class="evidence">
            <b>Exemplo de uso de Tailwind:</b>
            <pre><code>&lt;div class="bg-blue-100 rounded-lg shadow p-6"&gt;Conteúdo&lt;/div&gt;</code></pre>
        </div>
        <div class="evidence">
            <b>Exemplo de JS para gráficos:</b>
            <pre><code>&lt;script&gt;
    var chartData = {{ chart_data|safe }};
    createChart(chartData);
&lt;/script&gt;</code></pre>
        </div>
        <div class="concept">
            <b>Conceito:</b> O JS é usado para gráficos dinâmicos e pequenas interações, mas o HTML principal é sempre gerado no backend.
        </div>
    </div>

    <h2>7. Limitações e Considerações</h2>
    <div class="concept">
        <b>Limitação:</b> Como o frontend é gerado no backend (server-side rendering), não é possível desacoplar completamente frontend e backend sem reescrever para consumir APIs REST e usar frameworks modernos de frontend.
    </div>
    <ul>
        <li>Qualquer mudança de dados exige reload da página</li>
        <li>Não há SPA (Single Page Application)</li>
        <li>Frontend depende do ciclo de vida do backend Flask</li>
    </ul>

    <h2>8. Resumo Visual</h2>
    <div class="evidence">
        <b>Fluxo de renderização:</b>
        <pre><code>Usuário faz requisição → Flask processa dados → Jinja2 renderiza template → HTML enviado ao navegador</code></pre>
    </div>
    <div class="evidence">
        <b>Organização de pastas:</b>
        <pre><code>gestao/app/templates/           # Domínio Business
produto/ux-gabi/templates/     # Domínio Product
</code></pre>
    </div>

    <h2>9. Pontos Adicionais Relevantes</h2>
    <div class="domain">
        <ul>
            <li><b>Segurança:</b> Como o HTML é gerado no servidor, dados sensíveis não ficam expostos em APIs públicas, reduzindo riscos de exposição direta.</li>
            <li><b>SEO:</b> O server-side rendering facilita a indexação por mecanismos de busca, pois o HTML já chega "pronto" para o crawler.</li>
            <li><b>Internacionalização:</b> É possível usar filtros e macros Jinja2 para adaptar textos e formatos de dados conforme o idioma do usuário.</li>
            <li><b>Controle de Sessão:</b> O backend gerencia autenticação e sessão de forma centralizada, facilitando o controle de acesso.</li>
            <li><b>Facilidade de Prototipação:</b> Alterações rápidas em templates permitem prototipar novas telas sem necessidade de build ou deploy de frontend separado.</li>
            <li><b>Integração com Bibliotecas Python:</b> Permite uso direto de bibliotecas de visualização, manipulação de dados e lógica de negócio no backend.</li>
        </ul>
    </div>

    <h2>10. Tabela Comparativa: Benefícios vs. Prejuízos</h2>
    <table style="width:100%; border-collapse:collapse; margin: 1.5em 0;">
        <thead>
            <tr style="background:#e0e7ef;">
                <th style="padding:8px; border:1px solid #cbd5e1;">Benefícios</th>
                <th style="padding:8px; border:1px solid #cbd5e1;">Prejuízos / Limitações</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td style="padding:8px; border:1px solid #cbd5e1;">Renderização rápida para o usuário (SSR)</td>
                <td style="padding:8px; border:1px solid #cbd5e1;">Dificuldade de criar experiências SPA modernas</td>
            </tr>
            <tr>
                <td style="padding:8px; border:1px solid #cbd5e1;">SEO facilitado (HTML pronto para indexação)</td>
                <td style="padding:8px; border:1px solid #cbd5e1;">Frontend e backend fortemente acoplados</td>
            </tr>
            <tr>
                <td style="padding:8px; border:1px solid #cbd5e1;">Segurança de dados sensíveis (menos exposição via API)</td>
                <td style="padding:8px; border:1px solid #cbd5e1;">Dificuldade de manutenção em times grandes e multidisciplinares</td>
            </tr>
            <tr>
                <td style="padding:8px; border:1px solid #cbd5e1;">Facilidade de prototipação e deploy rápido</td>
                <td style="padding:8px; border:1px solid #cbd5e1;">Menor reutilização do backend para outros canais (mobile, etc)</td>
            </tr>
            <tr>
                <td style="padding:8px; border:1px solid #cbd5e1;">Controle centralizado de sessão e autenticação</td>
                <td style="padding:8px; border:1px solid #cbd5e1;">Necessidade de reload completo para atualizar dados</td>
            </tr>
            <tr>
                <td style="padding:8px; border:1px solid #cbd5e1;">Integração direta com bibliotecas Python</td>
                <td style="padding:8px; border:1px solid #cbd5e1;">Dificuldade de escalar frontend e backend separadamente</td>
            </tr>
            <tr>
                <td style="padding:8px; border:1px solid #cbd5e1;">Menos dependências externas (não precisa de build JS)</td>
                <td style="padding:8px; border:1px solid #cbd5e1;">Menos flexibilidade para usar frameworks JS modernos</td>
            </tr>
        </tbody>
    </table>

    <h2>11. Estrutura do Backend</h2>
    <div class="domain">
        <h3>Organização de Pastas</h3>
        <ul>
            <li><b>gestao/app/</b> - Núcleo do domínio Business</li>
            <li><b>gestao/app/controllers/</b> - Lógica de negócio e endpoints de cada funcionalidade</li>
            <li><b>gestao/app/models/</b> - Modelos de dados (ORM ou lógica de dados)</li>
            <li><b>gestao/app/services/</b> - Serviços auxiliares, regras de negócio, processamento de dados</li>
            <li><b>gestao/app/routes/</b> - Rotas agrupadas (ex: autenticação, admin)</li>
            <li><b>gestao/app/api/</b> - APIs REST/HTTP específicas</li>
            <li><b>gestao/app/middleware/</b> - Middlewares (ex: logger de atividades)</li>
            <li><b>gestao/app/extensions.py</b> - Extensões Flask e integrações</li>
            <li><b>gestao/app/config.py</b> - Configurações do domínio</li>
        </ul>
        <div class="evidence">
            <b>Exemplo de controller:</b>
            <pre><code># gestao/app/controllers/dashboard.py
from flask import Blueprint, render_template

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
def index():
    # Processa dados e retorna template
    return render_template('dashboard/index.html', ...)
</code></pre>
        </div>
        <div class="evidence">
            <b>Exemplo de service:</b>
            <pre><code># gestao/app/services/business_rules.py
class BusinessRulesService:
    def calculate_mrr(self, ...):
        # Lógica de negócio
        pass
</code></pre>
        </div>
        <div class="evidence">
            <b>Exemplo de model:</b>
            <pre><code># gestao/app/models/opportunity.py
class Opportunity:
    def __init__(self, ...):
        ...
</code></pre>
        </div>
        <h3>Conceitos por trás da arquitetura</h3>
        <ul>
            <li><b>Separação de responsabilidades:</b> Controllers cuidam das rotas e lógica de apresentação, services concentram regras de negócio, models representam dados.</li>
            <li><b>Modularidade:</b> Cada funcionalidade tem seu controller, facilitando manutenção e testes.</li>
            <li><b>Reutilização:</b> Services e models podem ser usados por múltiplos controllers.</li>
            <li><b>Extensibilidade:</b> Novas funcionalidades podem ser adicionadas criando novos controllers/services.</li>
            <li><b>Segurança e Middleware:</b> Middlewares para logging, autenticação, monitoramento, etc.</li>
            <li><b>Configuração centralizada:</b> Arquivos de config para cada domínio, facilitando deploy em múltiplos ambientes.</li>
        </ul>
        <div class="evidence">
            <b>Exemplo de rota de autenticação:</b>
            <pre><code># gestao/app/routes/auth_routes.py
from flask import Blueprint, render_template, request, redirect, url_for

auth_routes = Blueprint('auth_routes', __name__)

@auth_routes.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        # Autentica usuário
        ...
    return render_template('auth/login_business.html')
</code></pre>
        </div>
        <div class="concept">
            <b>Resumo:</b> O backend é organizado para garantir clareza, escalabilidade e facilidade de manutenção, seguindo boas práticas de arquitetura MVC (Model-View-Controller) e princípios de clean code.
        </div>
    </div>

    <h2>12. Consumo de Dados e Integração com Banco de Dados</h2>
    <div class="domain">
        <h3>Como o projeto consome dados hoje</h3>
        <ul>
            <li><b>Modo Arquivo:</b> Por padrão, ambos os domínios (Business e Product) consomem dados a partir de arquivos CSV ou Excel localizados em <code>data/sources/</code> (ex: <code>base_dados.csv</code>, <code>base_dados.xlsx</code>).</li>
            <li><b>Serviços de Leitura:</b> Serviços como <code>data_loader.py</code> e <code>data_processing.py</code> são responsáveis por carregar, processar e entregar os dados para os controllers.</li>
            <li><b>Configuração:</b> O arquivo de configuração de cada domínio define qual fonte de dados será usada (<code>DATA_SOURCE_MODE = 'files'</code> ou <code>'database'</code>).</li>
        </ul>
        <div class="evidence">
            <b>Exemplo de configuração:</b>
            <pre><code># gestao/app/config.py
DATA_SOURCE_MODE = os.getenv('DATA_SOURCE_MODE', 'files')
DATA_FILE = os.path.join(APP_ROOT, 'data', 'sources', 'base_dados.csv')
</code></pre>
        </div>
        <h3>Integração com Banco de Dados Postgres</h3>
        <ul>
            <li><b>Modo Database:</b> Quando <code>DATA_SOURCE_MODE = 'database'</code>, o projeto utiliza adaptadores e serviços para conectar ao banco Postgres.</li>
            <li><b>Domain Core:</b> A lógica de conexão, pooling, inicialização e abstração de queries está isolada em cada domínio no módulo <code>domain/shared/datamesh-core/database/</code> (ex: <code>connection_manager.py</code>, <code>sqlite_manager.py</code>, <code>data_adapter.py</code>).</li>
            <li><b>Configuração:</b> As credenciais e parâmetros de conexão são definidos nos arquivos de configuração de cada domínio, podendo ser herdados do shared ou sobrescritos localmente.</li>
        </ul>
        <div class="evidence">
            <b>Exemplo de configuração de banco:</b>
            <pre><code># gestao/app/config.py
DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_PORT = os.getenv('DB_PORT', '5432')
DB_NAME = os.getenv('DB_NAME', 'amigo_datahub')
DB_USER = os.getenv('DB_USER', 'app_user')
DB_PASSWORD = os.getenv('DB_PASSWORD', '')
</code></pre>
        </div>
        <h3>Análise por Domínio</h3>
        <ul>
            <li><b>Business:</b> Usa <code>data_loader.py</code>, <code>data_processing.py</code> e <code>database_adapter.py</code> para consumir dados de arquivos ou banco, conforme configuração. Utiliza classes utilitárias e adaptadores do seu próprio <code>gestao/shared/datamesh-core</code>.</li>
            <li><b>Product:</b> Estrutura semelhante, com seus próprios serviços de dados isolados, utilizando utilitários do seu próprio <code>produto/ux-gabi/shared/datamesh-core</code> para conexão e manipulação de dados.</li>
        </ul>
        <h3>Componentes Isolados por Domínio</h3>
        <ul>
            <li><b>Conexão e Pooling:</b> Cada domínio possui seu próprio <code>domain/shared/datamesh-core/database/connection_manager.py</code> e <code>init_databases.py</code> para lógica de conexão e inicialização de bancos.</li>
            <li><b>Abstração de Queries:</b> <code>data_adapter.py</code> e <code>sqlite_service.py</code> isolados por domínio fornecem métodos para executar queries de forma padronizada.</li>
            <li><b>Configuração Base:</b> Cada domínio tem seu próprio <code>domain/shared/datamesh-core/config/base_config.py</code> com configurações específicas do domínio.</li>
            <li><b>Segurança e Permissões:</b> Cada domínio possui seu próprio <code>domain/shared/datamesh-core/security/</code> para serviços de autenticação, autorização e gestão de usuários.</li>
            <li><b>Utilitários:</b> Funções auxiliares para logging, validação, monitoramento isoladas por domínio.</li>
        </ul>
        <div class="concept">
            <b>Resumo:</b> O projeto foi desenhado para consumir dados tanto de arquivos quanto de banco de dados, com lógica de conexão, configuração e segurança centralizada no módulo <code>shared</code>, promovendo reutilização e padronização entre os domínios Business e Product.
        </div>
    </div>

    <h2>13. Implementação de CSS no Projeto</h2>
    <div class="domain">
        <h3>Uso de TailwindCSS</h3>
        <ul>
            <li><b>TailwindCSS</b> é a principal biblioteca de utilitários CSS usada para estilização rápida e responsiva.</li>
            <li>Incluído via CDN diretamente nos templates base (<code>&lt;script src="https://cdn.tailwindcss.com"&gt;&lt;/script&gt;</code>).</li>
            <li>Permite aplicar estilos diretamente nas classes dos elementos HTML, sem necessidade de escrever CSS customizado para a maioria dos casos.</li>
        </ul>
        <div class="evidence">
            <b>Exemplo de uso de Tailwind em template:</b>
            <pre><code>&lt;div class="bg-blue-100 rounded-lg shadow p-6"&gt;Conteúdo estilizado&lt;/div&gt;</code></pre>
        </div>
        <h3>CSS Customizado</h3>
        <ul>
            <li>Para estilos específicos ou ajustes finos, arquivos CSS customizados podem ser adicionados em <code>gestao/app/static/css/</code> ou <code>produto/ux-gabi/static/css/</code>.</li>
            <li>Esses arquivos são referenciados nos templates base usando a tag <code>&lt;link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}"&gt;</code>.</li>
        </ul>
        <div class="evidence">
            <b>Exemplo de inclusão de CSS customizado:</b>
            <pre><code>&lt;link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}"&gt;</code></pre>
        </div>
        <h3>Organização dos Arquivos</h3>
        <ul>
            <li><code>gestao/app/static/css/styles.css</code> - Estilos customizados do domínio Business</li>
            <li><code>produto/ux-gabi/static/css/</code> - Estilos customizados do domínio Product</li>
        </ul>
        <h3>Aplicação nos Templates</h3>
        <ul>
            <li>O template base (<code>base.html</code>) já inclui Tailwind e pode referenciar CSS customizado.</li>
            <li>Todos os templates filhos herdam essas configurações e podem usar classes utilitárias do Tailwind ou estilos customizados conforme necessário.</li>
        </ul>
        <div class="concept">
            <b>Resumo:</b> O projeto prioriza o uso de TailwindCSS para agilidade e padronização visual, complementando com CSS customizado apenas quando necessário para ajustes ou componentes específicos.
        </div>
    </div>

    <h2>14. Integração JavaScript com Templates Jinja2 e Backend</h2>
    <div class="domain">
        <h3>Comunicação JavaScript-Templates Jinja2</h3>
        <div class="concept">
            <b>O JavaScript no projeto Amigo One atua como uma camada de interatividade sobre o HTML renderizado pelo backend Flask/Jinja2.</b> Ele complementa a renderização server-side com funcionalidades dinâmicas, gráficos interativos e melhorias de UX.
        </div>
        
        <h3>1. Injeção de Dados do Backend para JavaScript via Jinja2</h3>
        <div class="evidence">
            <b>Exemplo 1: Dados JSON Complexos</b>
            <pre><code>&lt;!-- Template: gestao/app/templates/dashboard/index.html --&gt;
&lt;script&gt;
    // Dados processados no backend e injetados via Jinja2
    const chartData = {{ chart_data|tojson }};
    const totalLeads = parseInt({{ total_leads|e }}) || 0;
    const totalOpportunities = parseInt({{ total_opportunities|e }}) || 0;
    
    // Usar dados no JavaScript
    createFunnelChart('funnelChart', funnelLabels, [totalLeads, totalOpportunities]);
&lt;/script&gt;</code></pre>
        </div>
        
        <div class="evidence">
            <b>Exemplo 2: Dados de Gráficos</b>
            <pre><code>&lt;!-- Template: gestao/app/templates/implementations/index.html --&gt;
&lt;script&gt;
    // Dados de fases vindos do backend
    const phasesData = {{ phases_data|tojson }};
    const statusData = {{ status_data|tojson }};
    const timelineData = {{ timeline_data|tojson }};
    
    // Criar gráficos com dados do backend
    if (Object.keys(phasesData).length > 0) {
        const phaseLabels = Object.keys(phasesData);
        const phaseValues = Object.values(phasesData).map(v => parseInt(v));
        
        AmigoDH.createPieChart('phasesChart', phaseLabels, phaseValues, {
            backgroundColor: AmigoDH.colors.blue
        });
    }
&lt;/script&gt;</code></pre>
        </div>
        
        <div class="evidence">
            <b>Exemplo 3: Dados do Domínio Product</b>
            <pre><code>&lt;!-- Template: produto/ux-gabi/templates/dashboard.html --&gt;
&lt;script&gt;
    // Dados completos do dashboard injetados pelo backend
    const dashboardData = {{ data | tojson |e }};
    
    // Usar dados para inicializar gráficos
    initializeCharts(dashboardData);
&lt;/script&gt;</code></pre>
        </div>
        
        <h3>2. Comunicação JavaScript-Backend via APIs</h3>
        <div class="evidence">
            <b>Exemplo 1: Busca de Dados via Fetch API</b>
            <pre><code>// produto/ux-gabi/static/js/dashboard-charts.js
document.addEventListener('DOMContentLoaded', function() {
    // Buscar dados do dashboard via API
    fetch('/api/dashboard-data')
        .then(response => response.json())
        .then(data => {
            // Processar dados recebidos do backend
            initializeRemainingCharts(data);
        })
        .catch(error => console.error('Erro ao carregar dados:', error));
});</code></pre>
        </div>
        
        <div class="evidence">
            <b>Exemplo 2: Envio de Dados para o Backend</b>
            <pre><code>// Enviar preferências do usuário
function saveUserPreference(preference) {
    fetch('/api/user-preferences', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken()
        },
        body: JSON.stringify(preference)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage('Preferência salva com sucesso');
        }
    });
}</code></pre>
        </div>
        
        <h3>3. Filtros Jinja2 para Segurança</h3>
        <div class="evidence">
            <b>Exemplo 1: Filtro |tojson para Dados Complexos</b>
            <pre><code>&lt;!-- Template Jinja2 --&gt;
&lt;script&gt;
    // Dados seguros convertidos para JSON
    const chartData = {{ chart_data|tojson }};
    const userPreferences = {{ user_preferences|tojson|safe }};
    
    // Dados simples com escape
    const userName = "{{ user.name|e }}";
    const userId = {{ user.id|e }};
&lt;/script&gt;</code></pre>
        </div>
        
        <div class="evidence">
            <b>Exemplo 2: Filtro |e para Escape de HTML</b>
            <pre><code>&lt;!-- Template: gestao/app/templates/churn/index.html --&gt;
&lt;tr onclick="highlightUniversity('{{ university.name|e }}')"&gt;
    &lt;td&gt;{{ university.name|e }}&lt;/td&gt;
    &lt;td&gt;{{ university.cancellations|e }}&lt;/td&gt;
    &lt;td&gt;{{ university.risk_rate|e }}%&lt;/td&gt;
&lt;/tr&gt;</code></pre>
        </div>
        
        <h3>4. Fluxo Completo de Comunicação</h3>
        <div class="evidence">
            <b>Backend → Template → JavaScript</b>
            <pre><code># gestao/app/controllers/dashboard.py
@app.route('/dashboard')
def dashboard():
    # Processar dados no backend
    chart_data = {
        'funnel_stages': {
            'leads': 150,
            'opportunities': 80,
            'implementations': 45,
            'active_clients': 30
        },
        'timeline_data': {
            'months': ['Jan', 'Fev', 'Mar'],
            'counts': [10, 15, 20]
        }
    }
    
    return render_template('dashboard/index.html', 
                         chart_data=chart_data,
                         total_leads=150,
                         total_opportunities=80)

# Template: dashboard/index.html
&lt;script&gt;
    // Dados injetados pelo Jinja2
    const chartData = {{ chart_data|tojson }};
    const totalLeads = parseInt({{ total_leads|e }}) || 0;
    
    // Usar dados no JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        createFunnelChart('funnelChart', 
                         Object.keys(chartData.funnel_stages),
                         Object.values(chartData.funnel_stages));
    });
&lt;/script&gt;</code></pre>
        </div>
        
        <h3>5. APIs para Comunicação Assíncrona</h3>
        <div class="evidence">
            <b>Exemplo 1: API para Dados Dinâmicos</b>
            <pre><code># gestao/app/api/dashboard_api.py
@app.route('/api/dashboard-data')
def get_dashboard_data():
    # Processar dados em tempo real
    data = {
        'features': {
            'medical_records': get_medical_records_stats(),
            'agenda': get_agenda_stats()
        },
        'users': get_user_stats()
    }
    return jsonify(data)

// JavaScript consumindo a API
fetch('/api/dashboard-data')
    .then(response => response.json())
    .then(data => {
        // Atualizar gráficos com dados frescos
        updateMedicalRecordsChart(data.features.medical_records);
        updateUserStats(data.users);
    });</code></pre>
        </div>
        
        <h3>6. Tratamento de Erros e Validação</h3>
        <div class="evidence">
            <b>Exemplo 1: Validação de Dados do Backend</b>
            <pre><code>&lt;!-- Template: gestao/app/templates/dashboard/index.html --&gt;
&lt;script&gt;
    let chartData;
    try {
        // Validar e sanitizar dados antes de usar
        const rawData = '{{ chart_data|tojson }}';
        chartData = JSON.parse(rawData);
    } catch (error) {
        console.error('Error parsing chart data:', error);
        chartData = {};
    }
    
    // Verificar se dados são válidos
    if (chartData.errors && chartData.errors.funnel_stages) {
        showErrorMessage(chartData.errors.funnel_stages.message);
    }
&lt;/script&gt;</code></pre>
        </div>
        
        <div class="evidence">
            <b>Exemplo 2: Fallback para Dados Ausentes</b>
            <pre><code>&lt;!-- Template: gestao/app/templates/implementations/index.html --&gt;
&lt;script&gt;
    const phasesData = {{ phases_data|tojson }};
    
    if (Object.keys(phasesData).length === 0) {
        document.getElementById('phasesChart').innerHTML = 
            '&lt;div class="text-gray-500"&gt;Dados de fases não disponíveis&lt;/div&gt;';
    } else {
        // Criar gráfico com dados válidos
        createPhasesChart(phasesData);
    }
&lt;/script&gt;</code></pre>
        </div>
        
        <h3>7. Padrões de Organização JavaScript</h3>
        <div class="evidence">
            <b>Estrutura modular e organizada</b>
            <pre><code>// Objeto global para funções compartilhadas
window.AmigoDH = window.AmigoDH || {};

// Definir funções no objeto global
AmigoDH.formatCurrency = function(value) {
    return 'R$ ' + parseFloat(value).toLocaleString('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
};

AmigoDH.createLineChart = function(canvasId, labels, data, label, options) {
    // Implementação da função de criação de gráfico
};

// Uso em templates
&lt;script&gt;
    document.addEventListener('DOMContentLoaded', function() {
        // Verificar se as funções estão disponíveis
        if (window.AmigoDH && typeof AmigoDH.createLineChart === 'function') {
            AmigoDH.createLineChart('timelineChart', timelineLabels, timelineData);
        }
    });
&lt;/script&gt;</code></pre>
        </div>
        
        <h3>8. Segurança e Validação</h3>
        <div class="evidence">
            <b>Implementação de segurança no JavaScript</b>
            <pre><code>// Validação de entrada para segurança
function validateInput(input) {
    // Remove caracteres potencialmente perigosos
    return input.replace(/[<>"'&]/g, '');
}

// Content Security Policy configurado nos templates
&lt;meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net; 
               style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; 
               img-src 'self' data:; 
               font-src 'self' data:;"&gt;

// Sanitização de dados antes de usar
let chartData;
try {
    const rawData = '{{ chart_data|tojson }}';
    chartData = JSON.parse(rawData);
} catch (error) {
    console.error('Error parsing chart data:', error);
    chartData = {};
}</code></pre>
        </div>
        
        <h3>9. Resumo do Fluxo de Comunicação</h3>
        <div class="evidence">
            <b>Fluxo completo de dados:</b>
            <pre><code>Backend (Python/Flask) → Jinja2 Template → HTML + Dados JSON → JavaScript → Interatividade</code></pre>
        </div>
        
        <div class="evidence">
            <b>Fluxo detalhado:</b>
            <pre><code>Backend (Python/Flask)
    ↓ (processa dados)
Controller (render_template)
    ↓ (injeta dados)
Template Jinja2 ({{ dados|tojson }})
    ↓ (converte para JSON)
JavaScript (const data = {{ dados|tojson }})
    ↓ (usa dados)
Gráficos/Interatividade</code></pre>
        </div>
        
        <h3>10. Pontos-Chave da Integração</h3>
        <ul>
            <li><b>Dados complexos:</b> Usam <code>|tojson</code> para conversão segura</li>
            <li><b>Dados simples:</b> Usam <code>|e</code> para escape de HTML</li>
            <li><b>APIs:</b> Usam <code>fetch()</code> para comunicação assíncrona</li>
            <li><b>Segurança:</b> Validação e sanitização em ambos os lados</li>
            <li><b>Fallbacks:</b> Tratamento de erros e dados ausentes</li>
            <li><b>Modularidade:</b> Código JavaScript organizado em módulos reutilizáveis</li>
            <li><b>Performance:</b> Lazy loading e debounce para otimização</li>
        </ul>
        
        <div class="concept">
            <b>Esta arquitetura permite que o JavaScript receba dados processados do backend de forma segura e os utilize para criar interfaces interativas e gráficos dinâmicos, complementando a renderização server-side com funcionalidades client-side.</b>
        </div>
    </div>

    <h2>15. Análise da Arquitetura Frontend Híbrida: Acoplamento vs Desacoplamento</h2>
    <div class="domain">
        <h3>Arquitetura Híbrida Identificada</h3>
        <div class="concept">
            <b>O projeto Amigo One apresenta uma arquitetura híbrida onde coexistem dois padrões de comunicação frontend-backend:</b> o padrão acoplado (Server-Side Rendering com Jinja2) e o padrão desacoplado (APIs REST + JavaScript).
        </div>
        
        <h3>1. Padrões de Comunicação Identificados</h3>
        
        <h4>1.1 Padrão Acoplado (Server-Side Rendering)</h4>
        <div class="evidence">
            <b>Características:</b>
            <ul>
                <li>Dados processados no backend Python/Flask</li>
                <li>Renderização via Jinja2 templates</li>
                <li>HTML enviado "pronto" para o navegador</li>
                <li>JavaScript recebe dados via injeção Jinja2</li>
            </ul>
        </div>
        
        <div class="evidence">
            <b>Exemplo Prático - Domínio Business:</b>
            <pre><code># gestao/app/controllers/dashboard.py
@app.route('/dashboard')
def dashboard():
    # Processar dados complexos no backend
    total_leads = df[lead_id_col].dropna().nunique()
    mrr_total, finalized_count, _, _ = business_rules.calculate_mrr()
    chart_data = analytics.prepare_chart_data()
    
    # Renderizar template com dados processados
    return render_template('dashboard/index.html',
                          total_leads=total_leads,
                          chart_data=json.dumps(chart_data),
                          mrr_total=format_currency(mrr_total))

# Template recebe dados prontos
&lt;script&gt;
    const chartData = {{ chart_data|tojson }};
    const totalLeads = parseInt({{ total_leads|e }}) || 0;
    createFunnelChart('funnelChart', funnelLabels, [totalLeads]);
&lt;/script&gt;</code></pre>
        </div>
        
        <h4>1.2 Padrão Desacoplado (API + JavaScript)</h4>
        <div class="evidence">
            <b>Características:</b>
            <ul>
                <li>Backend expõe APIs REST/JSON</li>
                <li>JavaScript consome APIs via fetch()</li>
                <li>Atualizações dinâmicas sem reload</li>
                <li>Separação clara entre frontend e backend</li>
            </ul>
        </div>
        
        <div class="evidence">
            <b>Exemplo Prático - Domínio Product:</b>
            <pre><code># produto/ux-gabi/routes/api_routes.py
@api_routes.route('/dashboard-data')
def dashboard_data():
    """API endpoint for dashboard data"""
    try:
        data = get_empty_analytics_data()
        return jsonify({
            'status': 'success',
            'data': data,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# JavaScript consome API
document.addEventListener('DOMContentLoaded', function() {
    fetch('/api/dashboard-data')
        .then(response => response.json())
        .then(data => {
            initializeRemainingCharts(data);
        })
        .catch(error => console.error('Erro ao carregar dados:', error));
});</code></pre>
        </div>
        
        <h4>1.3 Padrão Híbrido (Mistura de Ambos)</h4>
        <div class="evidence">
            <b>Características:</b>
            <ul>
                <li>Dados básicos via server-side rendering</li>
                <li>Dados dinâmicos via APIs</li>
                <li>JavaScript combina ambos os fluxos</li>
                <li>Flexibilidade para diferentes tipos de dados</li>
            </ul>
        </div>
        
        <div class="evidence">
            <b>Exemplo Prático - Página Híbrida:</b>
            <pre><code># Backend renderiza template com dados básicos
return render_template('dashboard.html', 
                      user_info=user_info,
                      basic_metrics=basic_metrics)

# JavaScript busca dados dinâmicos
fetch('/api/dashboard-data')</code></pre>
        </div>
        
        <h3>2. Mapeamento por Domínio</h3>
        
        <h4>2.1 Domínio Business (Gestão)</h4>
        <div class="evidence">
            <b>Padrão Principal: Acoplado (Server-Side Rendering)</b>
            <ul>
                <li><b>Dashboard:</b> <code>render_template('dashboard/index.html', ...)</code></li>
                <li><b>Leads:</b> <code>render_template('leads/index.html', ...)</code></li>
                <li><b>Implementations:</b> <code>render_template('implementations/index.html', ...)</code></li>
                <li><b>Opportunities:</b> <code>render_template('opportunities/index.html', ...)</code></li>
            </ul>
        </div>
        
        <div class="evidence">
            <b>APIs Complementares (Funcionalidades Específicas):</b>
            <ul>
                <li><code>/conversion/api/filtered-data</code> - Filtros dinâmicos</li>
                <li><code>/clustering/api/*</code> - Análise de clusters</li>
                <li><code>/usuarios-ativos/api/*</code> - Métricas de usuários ativos</li>
                <li><code>/universities/api/*</code> - Dados de universidades</li>
                <li><code>/intelligence/api/*</code> - Análises de inteligência</li>
            </ul>
        </div>
        
        <h4>2.2 Domínio Product (Produto)</h4>
        <div class="evidence">
            <b>Padrão Principal: Híbrido (Mistura de Ambos)</b>
            <ul>
                <li><b>Dashboard:</b> <code>render_template('dashboard.html', **dashboard_data)</code></li>
                <li><b>Usuários:</b> <code>render_template('usuarios.html', data={'users': users_data})</code></li>
                <li><b>Agenda:</b> <code>render_template('agenda.html', **agenda_data)</code></li>
            </ul>
        </div>
        
        <div class="evidence">
            <b>APIs Desacopladas (Dados Dinâmicos):</b>
            <ul>
                <li><code>/api/dashboard-data</code> - Dados do dashboard</li>
                <li><code>/api/users-data</code> - Dados de usuários</li>
                <li><code>/api/features-data</code> - Dados de funcionalidades</li>
                <li><code>/api/connections-data</code> - Dados de conexões</li>
                <li><code>/api/accounting-data</code> - Dados contábeis</li>
                <li><code>/api/analytics-data</code> - Dados analíticos completos</li>
            </ul>
        </div>
        
        <h3>3. Análise de Padrões por Funcionalidade</h3>
        
        <h4>3.1 Gráficos e Visualizações</h4>
        <div class="evidence">
            <b>Domínio Business:</b>
            <pre><code># Dados injetados via Jinja2
const phasesData = {{ phases_data|tojson }};
const statusData = {{ status_data|tojson }};
const timelineData = {{ timeline_data|tojson }};

# Criar gráficos com dados do backend
if (Object.keys(phasesData).length > 0) {
    AmigoDH.createPieChart('phasesChart', phaseLabels, phaseValues);
}</code></pre>
        </div>
        
        <div class="evidence">
            <b>Domínio Product:</b>
            <pre><code># Dados básicos via Jinja2 + dinâmicos via API
const dashboardData = {{ data | tojson |e }};

// Buscar dados adicionais via API
fetch('/api/dashboard-data')
    .then(response => response.json())
    .then(apiData => {
        // Combinar dados
        const combinedData = { ...dashboardData, ...apiData };
        initializeRemainingCharts(combinedData);
    });</code></pre>
        </div>
        
        <h4>3.2 Filtros e Interações</h4>
        <div class="evidence">
            <b>Filtros Dinâmicos via API:</b>
            <pre><code>// Filtro de conversão
fetch('/conversion/api/filtered-data', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(filterCriteria)
})
.then(response => response.json())
.then(data => updateConversionChart(data));

// Filtro de clustering
fetch('/clustering/api/scatter-data', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(clusteringParams)
})
.then(response => response.json())
.then(data => updateScatterPlot(data));</code></pre>
        </div>
        
        <h4>3.3 Dashboards</h4>
        <div class="evidence">
            <b>Business - Server-Side Rendering:</b>
            <pre><code># Backend processa todos os dados
@app.route('/dashboard')
def dashboard():
    # Processamento complexo no backend
    mrr_total, finalized_count, _, _ = business_rules.calculate_mrr()
    chart_data = analytics.prepare_chart_data()
    
    return render_template('dashboard/index.html',
                          mrr_total=format_currency(mrr_total),
                          chart_data=json.dumps(chart_data))</code></pre>
        </div>
        
        <div class="evidence">
            <b>Product - Híbrido:</b>
            <pre><code># Backend renderiza dados básicos
return render_template('dashboard.html', **dashboard_data)

# JavaScript busca dados dinâmicos
fetch('/api/dashboard-data')
    .then(response => response.json())
    .then(data => {
        // Atualizar componentes dinâmicos
        updateRealTimeMetrics(data.realtime);
        updateUserActivity(data.activity);
    });</code></pre>
        </div>
        
        <h3>4. Vantagens e Desvantagens de Cada Padrão</h3>
        
        <h4>4.1 Padrão Acoplado (Jinja2 + Server-Side)</h4>
        <div class="evidence">
            <b>✅ Vantagens:</b>
            <ul>
                <li><b>Renderização rápida:</b> HTML já vem "pronto" do servidor</li>
                <li><b>SEO otimizado:</b> Conteúdo indexável pelos motores de busca</li>
                <li><b>Menos chamadas HTTP:</b> Dados já incluídos na página</li>
                <li><b>Dados processados:</b> Lógica complexa executada no backend</li>
                <li><b>Segurança:</b> Dados sensíveis não expostos via API</li>
                <li><b>Compatibilidade:</b> Funciona mesmo com JavaScript desabilitado</li>
            </ul>
        </div>
        
        <div class="evidence">
            <b>❌ Desvantagens:</b>
            <ul>
                <li><b>Acoplamento forte:</b> Frontend e backend inseparáveis</li>
                <li><b>Atualizações limitadas:</b> Necessidade de reload para novos dados</li>
                <li><b>Menos interatividade:</b> Experiência menos dinâmica</li>
                <li><b>Dificuldade de manutenção:</b> Mudanças afetam ambos os lados</li>
                <li><b>Escalabilidade limitada:</b> Difícil escalar frontend e backend separadamente</li>
            </ul>
        </div>
        
        <h4>4.2 Padrão Desacoplado (API + JavaScript)</h4>
        <div class="evidence">
            <b>✅ Vantagens:</b>
            <ul>
                <li><b>Atualizações dinâmicas:</b> Dados atualizados sem reload</li>
                <li><b>Reutilização:</b> APIs podem ser usadas por diferentes frontends</li>
                <li><b>Flexibilidade:</b> Fácil integração com mobile apps, outros sistemas</li>
                <li><b>Separação de responsabilidades:</b> Frontend e backend independentes</li>
                <li><b>Escalabilidade:</b> Possibilidade de escalar componentes separadamente</li>
                <li><b>Experiência rica:</b> Interfaces mais interativas e responsivas</li>
            </ul>
        </div>
        
        <div class="evidence">
            <b>❌ Desvantagens:</b>
            <ul>
                <li><b>Mais chamadas HTTP:</b> Overhead de comunicação</li>
                <li><b>Complexidade adicional:</b> Gerenciamento de estado, loading states</li>
                <li><b>Problemas de SEO:</b> Conteúdo dinâmico pode não ser indexado</li>
                <li><b>Dependência de JavaScript:</b> Não funciona com JS desabilitado</li>
                <li><b>Segurança:</b> APIs podem expor dados sensíveis se mal configuradas</li>
                <li><b>Performance inicial:</b> Tempo de carregamento pode ser maior</li>
            </ul>
        </div>
        
        <h3>5. Implementação Atual vs Ideal</h3>
        
        <h4>5.1 Estado Atual (Híbrido Inconsistente)</h4>
        <div class="evidence">
            <b>Problemas Identificados:</b>
            <ul>
                <li><b>Mistura de padrões:</b> Sem padronização clara por funcionalidade</li>
                <li><b>APIs espalhadas:</b> Endpoints em diferentes controllers e rotas</li>
                <li><b>Inconsistência:</b> Mesma funcionalidade implementada de formas diferentes</li>
                <li><b>Documentação limitada:</b> APIs não padronizadas nem documentadas</li>
                <li><b>Manutenção complexa:</b> Dificuldade para entender qual padrão usar</li>
            </ul>
        </div>
        
        <div class="evidence">
            <b>Exemplo de Inconsistência:</b>
            <pre><code># Mesma funcionalidade implementada de formas diferentes

# Forma 1: Server-side rendering
@app.route('/dashboard')
def dashboard():
    return render_template('dashboard.html', data=process_data())

# Forma 2: API + JavaScript
@app.route('/api/dashboard-data')
def api_dashboard():
    return jsonify(process_data())

# Forma 3: Híbrido
@app.route('/dashboard')
def dashboard():
    return render_template('dashboard.html', basic_data=basic_data)

# JavaScript busca dados adicionais
fetch('/api/dashboard-data')</code></pre>
        </div>
        
        <h4>5.2 Estado Ideal (Híbrido Estruturado)</h4>
        <div class="evidence">
            <b>Recomendações de Estruturação:</b>
            <ul>
                <li><b>Padronização por domínio:</b> Padrão claro por área de negócio</li>
                <li><b>Organização de APIs:</b> Estrutura hierárquica e documentada</li>
                <li><b>Separação de responsabilidades:</b> Dados estáticos vs dinâmicos</li>
                <li><b>Documentação:</b> APIs padronizadas e bem documentadas</li>
                <li><b>Consistência:</b> Mesma funcionalidade sempre implementada igual</li>
            </ul>
        </div>
        
        <div class="evidence">
            <b>Estrutura Sugerida:</b>
            <pre><code># Organização de APIs por domínio
/api/
├── business/              # APIs do domínio Business
│   ├── dashboard/
│   │   ├── metrics        # Métricas básicas
│   │   └── realtime       # Dados em tempo real
│   ├── leads/
│   │   ├── list          # Lista de leads
│   │   └── analytics     # Análises de leads
│   └── implementations/
│       ├── status        # Status de implantações
│       └── timeline      # Timeline de progresso
└── product/               # APIs do domínio Product
    ├── dashboard/
    │   ├── overview      # Visão geral
    │   └── details       # Detalhes específicos
    ├── users/
    │   ├── stats         # Estatísticas de usuários
    │   └── activity      # Atividade dos usuários
    └── analytics/
        ├── features      # Uso de funcionalidades
        └── performance   # Métricas de performance</code></pre>
        </div>
        
        <h3>6. Estratégia de Migração e Evolução</h3>
        
        <h4>6.1 Critérios para Escolha do Padrão</h4>
        <div class="evidence">
            <b>Server-Side Rendering (Acoplado):</b>
            <ul>
                <li><b>Dados estáticos:</b> Informações que não mudam frequentemente</li>
                <li><b>SEO crítico:</b> Páginas que precisam ser indexadas</li>
                <li><b>Performance inicial:</b> Quando o tempo de carregamento é crítico</li>
                <li><b>Dados sensíveis:</b> Informações que não devem ser expostas via API</li>
                <li><b>Compatibilidade:</b> Quando precisa funcionar sem JavaScript</li>
            </ul>
        </div>
        
        <div class="evidence">
            <b>API + JavaScript (Desacoplado):</b>
            <ul>
                <li><b>Dados dinâmicos:</b> Informações que mudam em tempo real</li>
                <li><b>Interatividade:</b> Funcionalidades que requerem atualizações frequentes</li>
                <li><b>Reutilização:</b> Quando os dados serão usados por múltiplos frontends</li>
                <li><b>Experiência rica:</b> Interfaces que precisam ser altamente interativas</li>
                <li><b>Escalabilidade:</b> Quando frontend e backend precisam escalar independentemente</li>
            </ul>
        </div>
        
        <h4>6.2 Roadmap de Evolução</h4>
        <div class="evidence">
            <b>Fase 1: Padronização (Curto Prazo)</b>
            <ul>
                <li>Documentar padrões atuais</li>
                <li>Estabelecer convenções de nomenclatura</li>
                <li>Criar estrutura de APIs organizada</li>
                <li>Implementar documentação de APIs</li>
            </ul>
        </div>
        
        <div class="evidence">
            <b>Fase 2: Consolidação (Médio Prazo)</b>
            <ul>
                <li>Migrar funcionalidades inconsistentes</li>
                <li>Padronizar por domínio de negócio</li>
                <li>Implementar testes automatizados</li>
                <li>Otimizar performance</li>
            </ul>
        </div>
        
        <div class="evidence">
            <b>Fase 3: Otimização (Longo Prazo)</b>
            <ul>
                <li>Implementar cache inteligente</li>
                <li>Adicionar funcionalidades avançadas</li>
                <li>Otimizar para mobile</li>
                <li>Implementar PWA (Progressive Web App)</li>
            </ul>
        </div>
        
        <h3>7. Exemplos Práticos de Implementação</h3>
        
        <h4>7.1 Dashboard Híbrido Otimizado</h4>
        <div class="evidence">
            <b>Backend - Dados Básicos + API:</b>
            <pre><code># Controller principal
@app.route('/dashboard')
def dashboard():
    # Dados básicos processados no servidor
    basic_data = {
        'user_info': get_user_info(),
        'basic_metrics': get_basic_metrics(),
        'static_charts': get_static_chart_data()
    }
    
    return render_template('dashboard.html', **basic_data)

# API para dados dinâmicos
@app.route('/api/dashboard/realtime')
def dashboard_realtime():
    return jsonify({
        'realtime_metrics': get_realtime_metrics(),
        'user_activity': get_user_activity(),
        'system_status': get_system_status()
    })</code></pre>
        </div>
        
        <div class="evidence">
            <b>Frontend - Combinação Inteligente:</b>
            <pre><code>&lt;script&gt;
    // Dados básicos via Jinja2
    const basicData = {{ basic_metrics|tojson }};
    const userInfo = {{ user_info|tojson }};
    
    // Inicializar com dados básicos
    initializeBasicCharts(basicData);
    displayUserInfo(userInfo);
    
    // Buscar dados dinâmicos
    function loadRealtimeData() {
        fetch('/api/dashboard/realtime')
            .then(response => response.json())
            .then(data => {
                updateRealtimeMetrics(data.realtime_metrics);
                updateActivityFeed(data.user_activity);
            })
            .catch(error => console.error('Erro ao carregar dados:', error));
    }
    
    // Atualizar a cada 30 segundos
    setInterval(loadRealtimeData, 30000);
    
    // Carregar dados iniciais
    loadRealtimeData();
&lt;/script&gt;</code></pre>
        </div>
        
        <h4>7.2 API Estruturada e Documentada</h4>
        <div class="evidence">
            <b>Estrutura de API Organizada:</b>
            <pre><code># produto/ux-gabi/routes/api_routes.py
from flask import Blueprint, jsonify, request
from datetime import datetime

api_routes = Blueprint('api_routes', __name__, url_prefix='/api')

# Grupo: Dashboard APIs
@api_routes.route('/dashboard/overview')
def dashboard_overview():
    """Retorna visão geral do dashboard"""
    return jsonify({
        'status': 'success',
        'data': get_dashboard_overview(),
        'timestamp': datetime.now().isoformat()
    })

@api_routes.route('/dashboard/realtime')
def dashboard_realtime():
    """Retorna dados em tempo real do dashboard"""
    return jsonify({
        'status': 'success',
        'data': get_realtime_data(),
        'timestamp': datetime.now().isoformat()
    })

# Grupo: User Analytics APIs
@api_routes.route('/users/analytics')
def users_analytics():
    """Retorna análises de usuários"""
    filters = request.args.get('filters', {})
    return jsonify({
        'status': 'success',
        'data': get_user_analytics(filters),
        'timestamp': datetime.now().isoformat()
    })</code></pre>
        </div>
        
        <h3>8. Resumo e Recomendações</h3>
        
        <div class="concept">
            <b>A arquitetura híbrida atual do projeto Amigo One oferece flexibilidade mas pode beneficiar de uma padronização mais clara.</b> A mistura de padrões acoplados e desacoplados permite otimizar cada funcionalidade conforme suas necessidades específicas.
        </div>
        
        <h4>8.1 Pontos-Chave da Análise</h4>
        <ul>
            <li><b>Domínio Business:</b> Mais acoplado, focado em server-side rendering para dashboards principais</li>
            <li><b>Domínio Product:</b> Mais desacoplado, com APIs para funcionalidades dinâmicas</li>
            <li><b>Padrão Geral:</b> Híbrido evolutivo, com APIs complementando o server-side rendering</li>
            <li><b>Oportunidade:</b> Padronização e organização para facilitar manutenção e desenvolvimento</li>
        </ul>
        
        <h4>8.2 Recomendações Práticas</h4>
        <ul>
            <li><b>Padronizar por funcionalidade:</b> Usar critérios claros para escolher entre SSR e API</li>
            <li><b>Organizar APIs:</b> Estrutura hierárquica e documentação consistente</li>
            <li><b>Separar responsabilidades:</b> Dados estáticos via SSR, dinâmicos via API</li>
            <li><b>Implementar gradualmente:</b> Migração incremental sem quebrar funcionalidades existentes</li>
            <li><b>Documentar padrões:</b> Criar guias de desenvolvimento para a equipe</li>
        </ul>
        
        <div class="evidence">
            <b>Esta arquitetura híbrida permite aproveitar os benefícios de ambos os padrões, criando uma experiência de usuário rica e performática, enquanto mantém a flexibilidade para evolução futura do sistema.</b>
        </div>
    </div>
</div>
</body>
</html> 