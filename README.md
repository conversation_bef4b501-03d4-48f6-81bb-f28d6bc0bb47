# 🚀 DataHub Amigo One - Arquitetura DataMesh

[![Security](https://img.shields.io/badge/Security-Enterprise%20Grade-green)](https://github.com/brunodeabreu-art/dataapp)
[![Architecture](https://img.shields.io/badge/Architecture-DataMesh-blue)](https://github.com/brunodeabreu-art/dataapp)
[![Domains](https://img.shields.io/badge/Domains-2%20Active-orange)](https://github.com/brunodeabreu-art/dataapp)
[![Users](https://img.shields.io/badge/Max%20Users-100-purple)](https://github.com/brunodeabreu-art/dataapp)

## 📋 **Visão Geral**

O DataHub Amigo One é uma solução empresarial baseada na arquitetura **DataMesh** que separa responsabilidades entre domínios de negócio, garantindo escalabilidade, segurança e governança de dados.

### 🏗️ **Arquitetura**

```
┌─────────────────┐    ┌─────────────────┐
│   BUSINESS      │    │    PRODUCT      │
│   DOMAIN        │    │    DOMAIN       │
│                 │    │                 │
│ • Leads         │    │ • User Analytics│
│ • Opportunities │    │ • Features      │
│ • Implementations│   │ • Payments      │
│ • Universities  │    │ • Medical Data  │
│ • Conversions   │    │ • Performance   │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────┬─────────┬─────┘
                 │         │
         ┌───────▼─────────▼───────┐
         │   SHARED DATAMESH CORE  │
         │                         │
         │ • Authentication        │
         │ • Security              │
         │ • Monitoring            │
         │ • Data Governance       │
         │ • Compliance            │
         └─────────────────────────┘
```

## 🎯 **Domínios**

### 🏢 **Business Domain** (Porto 5000)
- **Foco:** Gestão comercial e pipeline de vendas
- **Usuários:** Equipe comercial, gestores, administradores
- **Funcionalidades:** CRM, análise de conversão, gestão de universidades

### 🔬 **Product Domain** (Porto 5001)
- **Foco:** Analytics de produto e experiência do usuário
- **Usuários:** Product managers, desenvolvedores, analistas
- **Funcionalidades:** Métricas de uso, análise de features, dados médicos

## 📊 **Capacidade e Performance**

| Métrica | Valor |
|---------|-------|
| **Usuários Máximos** | 100 usuários |
| **Usuários Simultâneos** | 30 usuários |
| **Consultas por Hora/Usuário** | 10 consultas |
| **Total Consultas/Hora** | 1.000 consultas |
| **Uptime Target** | 99.9% |

## 🔒 **Segurança Enterprise**

- ✅ **CSRF Protection** - Proteção contra ataques cross-site
- ✅ **HTTPS Enforcement** - Comunicação criptografada
- ✅ **Secure Random Generators** - Geradores criptograficamente seguros
- ✅ **Resource Integrity** - Verificação de integridade de recursos
- ✅ **Environment Variables** - Credenciais seguras
- ✅ **Rate Limiting** - Proteção contra ataques DDoS
- ✅ **Input Validation** - Sanitização de dados
- ✅ **Audit Logging** - Rastreamento de ações

## 🚀 **Quick Start**

### Pré-requisitos
```bash
Python 3.8+
Flask 2.0+
SQLite 3
```

### Instalação
```bash
# Clone o repositório
git clone https://github.com/brunodeabreu-art/dataapp.git
cd dataapp

# Instale dependências
pip install -r requirements.txt

# Configure variáveis de ambiente
cp .env.template .env
# Edite .env com suas configurações

# Inicie Business Domain
cd gestao
python business.py

# Inicie Product Domain (novo terminal)
cd produto/ux-gabi
python app.py
```

### Acesso
- **Business Domain:** http://localhost:5000
- **Product Domain:** http://localhost:5001

## 📁 **Estrutura do Projeto**

```
dataapp/
├── 📊 gestao/                    # Business Domain (Completamente Isolado)
│   ├── app/                      # Aplicação Flask
│   ├── data/                     # Dados de negócio
│   ├── shared/                   # Componentes locais do business
│   │   ├── datamesh-core/        # Core DataMesh para business
│   │   ├── config_manager.py     # Configurações de segurança
│   │   └── design-system.py      # Sistema de design
│   └── business.py               # Ponto de entrada
├── 🔬 produto/ux-gabi/           # Product Domain (Completamente Isolado)
│   ├── templates/                # Templates do produto
│   ├── static/                   # Assets estáticos
│   ├── shared/                   # Componentes locais do product
│   │   ├── datamesh-core/        # Core DataMesh para product
│   │   ├── config_manager.py     # Configurações de segurança
│   │   └── design-system.py      # Sistema de design
│   └── app.py                    # Ponto de entrada
└── 📋 docs/                      # Documentação
```

## 🛡️ **Governança de Dados**

### Princípios DataMesh
1. **Domain Ownership** - Cada domínio é responsável por seus dados
2. **Data as a Product** - Dados tratados como produtos
3. **Federated Governance** - Governança federada

### Compliance
- **LGPD** - Lei Geral de Proteção de Dados
- **SOX** - Sarbanes-Oxley Act
- **ISO 27001** - Gestão de Segurança da Informação

## ☁️ **AWS Architecture Recomendada**

### Para 100 usuários / 30 simultâneos / 1.000 consultas/hora

| Serviço | Configuração | Custo Mensal (USD) |
|---------|--------------|-------------------|
| **EC2** | t3.small (2 instâncias) | $30 |
| **RDS** | db.t3.micro (PostgreSQL) | $15 |
| **ALB** | Application Load Balancer | $20 |
| **CloudFront** | CDN para assets estáticos | $5 |
| **S3** | Armazenamento de dados | $5 |
| **CloudWatch** | Monitoramento e logs | $10 |
| **Route 53** | DNS e health checks | $5 |
| **Total** | | **~$90/mês** |

## 🔧 **Tecnologias**

- **Backend:** Python 3.8+, Flask 2.0+
- **Database:** SQLite (dev), PostgreSQL (prod)
- **Frontend:** HTML5, TailwindCSS, JavaScript
- **Security:** bcrypt, CSRF tokens, HTTPS
- **Monitoring:** Custom metrics, CloudWatch
- **Deployment:** Docker, AWS EC2

## 📈 **Roadmap**

### Q1 2024
- [ ] Migração para PostgreSQL
- [ ] Implementação de cache Redis
- [ ] Dashboard de monitoramento avançado

### Q2 2024
- [ ] API GraphQL
- [ ] Integração com AWS Cognito
- [ ] Machine Learning pipeline

### Q3 2024
- [ ] Mobile app
- [ ] Real-time notifications
- [ ] Advanced analytics

## 👥 **Contribuição**

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 **Licença**

Este projeto está licenciado sob a MIT License - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 📞 **Suporte**

- **Email:** <EMAIL>
- **Slack:** #datahub-support
- **Documentação:** [docs.datahub.com](https://docs.datahub.com)

---

**DataHub Amigo One** - Transformando dados em insights acionáveis 🚀
