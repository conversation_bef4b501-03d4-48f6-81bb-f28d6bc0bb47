# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp

# Database
*.db
*.sqlite
*.sqlite3

# Node modules (if any)
node_modules/

# Backup files
*.bak
*.backup

# Session files
flask_session/

# Local configuration
config_local.py
.env.local

# Data files (optional - uncomment if you don't want to track data)
# *.csv
# *.xlsx
# *.json

# Cache
.cache/
*.cache

# =============================================================================
# SECURITY-FOCUSED ADDITIONS - DATAHUB AMIGO ONE
# =============================================================================

# CREDENCIAIS E DADOS SENSÍVEIS
# =============================================================================
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
*.der
*secret*
*password*
*credential*
*token*
.env.*
!.env.example
config/secrets.py
config/production.py
secrets/
credentials/
keys/
certs/
*.keystore
*.jks

# ARQUIVOS DE CONFIGURAÇÃO SENSÍVEIS
# =============================================================================
*.conf
*.ini
!requirements.txt
!setup.cfg
config.json
settings.json
production_settings.py
local_config.py
dev_settings.py
debug_config.py

# DADOS REAIS E BACKUPS SENSÍVEIS
# =============================================================================
data/real/
data/production/
data/backup/
data/exports/
data/sensitive/
backup/
backups/
*.dump
*.sql
real_data_*
production_*
sensitive_*
*_real_data_*
*_production_*
*_sensitive_*

# UPLOADS E ARQUIVOS DE USUÁRIO
# =============================================================================
uploads/
media/
static/uploads/
user_uploads/
files/
documents/
attachments/

# MONITORAMENTO E LOGS SENSÍVEIS
# =============================================================================
monitoring/
metrics/
alerts/
.prometheus/
audit_logs/
security_logs/

# ANÁLISE DE CÓDIGO E RELATÓRIOS
# =============================================================================
.sonar/
.scannerwork/
sonar-project.properties.local
# sonar-project.properties - PERMITIDO para configuração do projeto
.sonarqube/
sonarqube/
sonar-scanner/
.sonar-scanner/
sonar-reports/
sonar-cache/
.sonar-cache/
sonar-temp/
.sonar-temp/
sonar-logs/
.sonar-logs/
sonar-output/
.sonar-output/
sonar-results/
.sonar-results/
sonar-analysis/
.sonar-analysis/
sonar-workspace/
.sonar-workspace/
sonar-working-dir/
.sonar-working-dir/
sonar-scanner-work/
.sonar-scanner-work/
sonar-scanner-cache/
.sonar-scanner-cache/
sonar-scanner-temp/
.sonar-scanner-temp/
sonar-scanner-logs/
.sonar-scanner-logs/
sonar-scanner-output/
.sonar-scanner-output/
sonar-scanner-results/
.sonar-scanner-results/
sonar-scanner-analysis/
.sonar-scanner-analysis/
sonar-scanner-workspace/
.sonar-scanner-workspace/
sonar-scanner-working-dir/
.sonar-scanner-working-dir/
security_report.html
vulnerability_report.*
penetration_test.*
code_quality_report.*
sonar_report.*
sonar_analysis.*
sonar_results.*
sonar_output.*
sonar_logs.*
sonar_cache.*
sonar_temp.*
sonar_workspace.*
sonar_working_dir.*
sonar_scanner_*
.sonar_*
sonar_*

# DOCKER E DEPLOYMENT
# =============================================================================
.dockerignore
docker-compose.override.yml
docker-compose.prod.yml
.docker/
deployment/secrets/
deploy/secrets/
k8s/secrets/

# ESPECÍFICOS DO DATAHUB
# =============================================================================
gestao/data/real/
produto/data/real/
config/production/
admin_credentials.*
user_data_export.*
database_backup.*

# ARQUIVOS DE SEGURANÇA CRÍTICOS
# =============================================================================
.env.security
.env.production
.env.staging
security_config.py.local
passwords.txt
credentials.txt
admin_passwords.*
user_passwords.*
security_fixes_report.txt
function_complexity_report.txt

# ARQUIVOS TEMPORÁRIOS E SISTEMA
# =============================================================================
*.pid
*.seed
*.pid.lock
.grunt
bower_components
.lock-wscript
.nyc_output
.sass-cache/
*.css.map
*.sass.map
*.scss.map

# EXCEÇÕES (arquivos que DEVEM ser incluídos)
# =============================================================================
!.gitkeep
!.gitignore
!README.md
!requirements.txt
!setup.py
!Dockerfile
!docker-compose.yml
!.github/
!docs/
!tests/
!examples/
!data/mock/
!data/samples/
!data/templates/
