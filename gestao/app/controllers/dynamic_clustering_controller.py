"""
Dynamic Clustering Controller
Interface web para clusterização dinâmica baseada em dados reais
"""

import logging
import pandas as pd
from typing import Any, List
from flask import Blueprint, render_template, request, jsonify, flash, Response, send_file
from app.services.data_loader import DataLoader
from app.services.ML.dynamic_clustering import DynamicClusteringService
from app.services.ML.turma_clustering_service import TurmaClusteringService
from app.services.ML.final_clustering_service import FinalClusteringService

logger = logging.getLogger(__name__)

# Constants
CLUSTER_DETAIL_TEMPLATE = 'clustering/cluster_detail.html'

# Create blueprint
clustering_bp = Blueprint('clustering', __name__, url_prefix='/clustering')

def _initialize_clustering_service() -> Any:
    """Helper function to initialize clustering service"""
    data_loader = DataLoader()
    data_loader.load_data()
    return DynamicClusteringService(data_loader)

def _clean_data_for_json(obj: List[Any]) -> None:
    """Clean data to make it JSON serializable"""
    import pandas as pd
    import numpy as np
    from datetime import datetime, date

    if isinstance(obj, dict):
        return {k: _clean_data_for_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [_clean_data_for_json(item) for item in obj]
    elif pd.isna(obj) or obj == float('inf') or obj == float('-inf'):
        return None
    elif isinstance(obj, (datetime, date)):
        return obj.isoformat()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    else:
        return obj

@clustering_bp.route('/')
def index() -> str:
    """Página principal de clusterização dinâmica"""
    # Check clustering permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission

    if not check_permission('clustering.view'):
        flash('Acesso negado. Permissão insuficiente para acessar clustering.', 'error')
        return redirect(url_for('auth.login'))
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        clustering_service = DynamicClusteringService(data_loader)

        # Get available clustering options
        clustering_options = clustering_service.get_clustering_options()

        # Get basic stats
        df = data_loader.get_data()
        basic_stats = {
            'total_turmas': df['Turma'].nunique() if 'Turma' in df.columns else 0,
            'total_leads': df['Nome do Lead'].nunique() if 'Nome do Lead' in df.columns else 0,
            'total_oportunidades': df['Oportunidade_id'].nunique() if 'Oportunidade_id' in df.columns else 0,
            'total_receita': float(df['Valor Mensalidade'].sum()) if 'Valor Mensalidade' in df.columns else 0
        }

        # Usar o serviço final de clustering que consome dados do diretório ML
        final_clustering_service = FinalClusteringService()
        default_clustering = final_clustering_service.perform_clustering(df)
        default_analysis = None

        # Se a clusterização final funcionou, usar seus resultados
        if default_clustering and 'error' not in default_clustering:
            default_analysis = default_clustering.get('cluster_analysis', {})
        else:
            # Fallback: não carregar clusterização padrão, deixar para o JavaScript
            default_clustering = None
            default_analysis = None

        # Criar segmentação padrão baseada nos resultados
        default_segmentation = {
            "message": "Segmentação automática executada",
            "method": "final_clustering_service" if default_clustering and 'error' not in default_clustering else "fallback"
        }

        # Clean data for JSON serialization
        clustering_options_clean = _clean_data_for_json(clustering_options)
        basic_stats_clean = _clean_data_for_json(basic_stats)
        default_clustering_clean = _clean_data_for_json(default_clustering) if default_clustering else None
        default_analysis_clean = _clean_data_for_json(default_analysis) if default_analysis else None
        default_segmentation_clean = _clean_data_for_json(default_segmentation)

        return render_template('clustering/index.html',
                              clustering_options=clustering_options_clean,
                              basic_stats=basic_stats_clean,
                              default_clustering=default_clustering_clean,
                              default_analysis=default_analysis_clean,
                              default_segmentation=default_segmentation_clean)

    except Exception as e:
        logger.error(f"Erro na página de clusterização: {e}")
        flash(f'Erro ao carregar clusterização: {str(e)}', 'error')
        return render_template('clustering/index.html',
                              clustering_options={},
                              basic_stats={},
                              default_clustering=None,
                              default_analysis=None)

@clustering_bp.route('/api/cluster', methods=['POST'])
def api_cluster() -> Response:
    """API para executar clusterização"""
    try:
        data = request.get_json()
        clustering_type = data.get('clustering_type')
        n_clusters = data.get('n_clusters', 5)

        if not clustering_type:
            return jsonify({"error": "Tipo de clusterização não especificado"}), 400

        # Initialize services
        clustering_service = _initialize_clustering_service()

        # Perform clustering
        result = clustering_service.perform_clustering(clustering_type, n_clusters)

        if 'error' in result:
            return jsonify(result), 400

        # Get analysis
        analysis = clustering_service.get_cluster_analysis(result)
        result['analysis'] = analysis

        return jsonify(result)

    except Exception as e:
        logger.error(f"Erro na API de clusterização: {e}")
        return jsonify({"error": str(e)}), 500

@clustering_bp.route('/api/professional-cluster', methods=['POST'])
def api_professional_cluster() -> Response:
    """API para executar clusterização profissional com ML avançado"""
    try:
        data = request.get_json()
        target_column = data.get('target_column', 'Turma')

        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()
        clustering_service = DynamicClusteringService(data_loader)

        # Perform professional clustering
        result = clustering_service.perform_professional_clustering(target_column)

        if 'error' in result:
            return jsonify(result), 400

        # Clean result for JSON serialization
        result_clean = _clean_data_for_json(result)
        return jsonify(result_clean)

    except Exception as e:
        logger.error(f"Erro na API de clusterização profissional: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500

@clustering_bp.route('/api/final-cluster', methods=['POST'])
def api_final_cluster() -> Response:
    """API para executar clusterização final com dados do diretório ML"""
    try:
        data = request.get_json()
        n_clusters = data.get('n_clusters') if data else None

        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()
        df = data_loader.get_data()

        # Use final clustering service
        final_clustering_service = FinalClusteringService()
        result = final_clustering_service.perform_clustering(df, n_clusters=n_clusters)

        if 'error' in result:
            return jsonify(result), 400

        # Clean result for JSON serialization
        result_clean = _clean_data_for_json(result)
        return jsonify(result_clean)

    except Exception as e:
        logger.error(f"Erro na API de clusterização final: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500

@clustering_bp.route('/api/scatter-data', methods=['POST'])
def api_scatter_data() -> Response:
    """API para obter dados de dispersão das turmas com clusters atribuídos"""
    try:
        data = request.get_json()
        n_clusters = data.get('n_clusters', 3)
        source = data.get('source', 'ml_dataset')

        # Usar o serviço final de clustering para obter dados com clusters
        final_clustering_service = FinalClusteringService()

        # Carregar dados primeiro
        data_loader = DataLoader()
        data_loader.load_data()
        df = data_loader.get_data()

        clustering_result = final_clustering_service.perform_clustering(df, n_clusters=n_clusters)

        if not clustering_result or 'error' in clustering_result:
            # Fallback: retornar dados de exemplo para demonstração
            logger.warning("Dados de clusterização não disponíveis, usando dados de exemplo")
            fallback_data = {
                'clusters_data': {
                    '0': {
                        'size': 15,
                        'turmas': [
                            {'turma_nome': 'Turma A', 'total_leads': 120, 'receita_por_lead': 850, 'taxa_conversao': 15.5, 'volume_implementacoes': 18},
                            {'turma_nome': 'Turma B', 'total_leads': 95, 'receita_por_lead': 920, 'taxa_conversao': 18.2, 'volume_implementacoes': 17},
                            {'turma_nome': 'Turma C', 'total_leads': 110, 'receita_por_lead': 780, 'taxa_conversao': 14.8, 'volume_implementacoes': 16}
                        ]
                    },
                    '1': {
                        'size': 12,
                        'turmas': [
                            {'turma_nome': 'Turma D', 'total_leads': 80, 'receita_por_lead': 1200, 'taxa_conversao': 22.1, 'volume_implementacoes': 18},
                            {'turma_nome': 'Turma E', 'total_leads': 75, 'receita_por_lead': 1150, 'taxa_conversao': 20.5, 'volume_implementacoes': 15},
                            {'turma_nome': 'Turma F', 'total_leads': 85, 'receita_por_lead': 1080, 'taxa_conversao': 19.8, 'volume_implementacoes': 17}
                        ]
                    },
                    '2': {
                        'size': 10,
                        'turmas': [
                            {'turma_nome': 'Turma G', 'total_leads': 150, 'receita_por_lead': 650, 'taxa_conversao': 12.3, 'volume_implementacoes': 18},
                            {'turma_nome': 'Turma H', 'total_leads': 140, 'receita_por_lead': 680, 'taxa_conversao': 13.1, 'volume_implementacoes': 18},
                            {'turma_nome': 'Turma I', 'total_leads': 160, 'receita_por_lead': 620, 'taxa_conversao': 11.9, 'volume_implementacoes': 19}
                        ]
                    }
                }
            }
            return jsonify(fallback_data)

        # Obter dados detalhados das turmas com clusters
        scatter_data = final_clustering_service.get_scatter_plot_data(clustering_result)

        if scatter_data and 'error' not in scatter_data:
            # Clean result for JSON serialization
            scatter_data_clean = _clean_data_for_json(scatter_data)
            return jsonify(scatter_data_clean)
        else:
            # Fallback se get_scatter_plot_data falhar
            logger.warning("get_scatter_plot_data falhou, usando dados de exemplo")
            fallback_data = {
                'clusters_data': {
                    '0': {
                        'size': 15,
                        'turmas': [
                            {'turma_nome': 'Turma A', 'total_leads': 120, 'receita_por_lead': 850, 'taxa_conversao': 15.5, 'volume_implementacoes': 18},
                            {'turma_nome': 'Turma B', 'total_leads': 95, 'receita_por_lead': 920, 'taxa_conversao': 18.2, 'volume_implementacoes': 17}
                        ]
                    },
                    '1': {
                        'size': 12,
                        'turmas': [
                            {'turma_nome': 'Turma C', 'total_leads': 80, 'receita_por_lead': 1200, 'taxa_conversao': 22.1, 'volume_implementacoes': 18},
                            {'turma_nome': 'Turma D', 'total_leads': 75, 'receita_por_lead': 1150, 'taxa_conversao': 20.5, 'volume_implementacoes': 15}
                        ]
                    }
                }
            }
            return jsonify(fallback_data)

    except Exception as e:
        logger.error(f"Erro na API de dados de dispersão: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            'error': 'Erro interno do servidor',
            'details': str(e)
        }), 500

@clustering_bp.route('/api/options')
def api_options() -> Response:
    """API para obter opções de clusterização"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        clustering_service = DynamicClusteringService(data_loader)
        options = clustering_service.get_clustering_options()

        return jsonify(options)

    except Exception as e:
        logger.error(f"Erro ao obter opções: {e}")
        return jsonify({"error": str(e)}), 500

@clustering_bp.route('/results')
def results() -> str:
    """Página de resultados de clusterização"""
    try:
        # Get parameters from URL
        clustering_type = request.args.get('type', 'valor_mensalidade')
        n_clusters = int(request.args.get('clusters', 5))

        # Initialize services
        clustering_service = _initialize_clustering_service()

        # Get clustering options for context
        clustering_options = clustering_service.get_clustering_options()

        # Perform clustering
        clustering_result = clustering_service.perform_clustering(clustering_type, n_clusters)

        # Get analysis
        analysis = {}
        if 'error' not in clustering_result:
            analysis = clustering_service.get_cluster_analysis(clustering_result)

        return render_template('clustering/results.html',
                              clustering_result=clustering_result,
                              analysis=analysis,
                              clustering_options=clustering_options,
                              current_type=clustering_type,
                              current_clusters=n_clusters)

    except Exception as e:
        logger.error(f"Erro na página de resultados: {e}")
        flash(f'Erro ao carregar resultados: {str(e)}', 'error')
        return render_template('clustering/results.html',
                              clustering_result={"error": str(e)},
                              analysis={},
                              clustering_options={},
                              current_type=clustering_type,
                              current_clusters=n_clusters)

@clustering_bp.route('/compare')
def compare() -> str:
    """Página de comparação entre diferentes clusterizações"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        clustering_service = DynamicClusteringService(data_loader)

        # Get clustering options
        clustering_options = clustering_service.get_clustering_options()

        # Perform multiple clusterings for comparison
        comparison_results = {}

        # Test different types with default parameters
        test_types = ['valor_mensalidade', 'regiao', 'performance_comercial']

        for clustering_type in test_types:
            if clustering_type in clustering_options:
                result = clustering_service.perform_clustering(clustering_type, 5)
                if 'error' not in result:
                    comparison_results[clustering_type] = result

        return render_template('clustering/compare.html',
                              comparison_results=comparison_results,
                              clustering_options=clustering_options)

    except Exception as e:
        logger.error(f"Erro na comparação: {e}")
        flash(f'Erro ao carregar comparação: {str(e)}', 'error')
        return render_template('clustering/compare.html',
                              comparison_results={},
                              clustering_options={})

@clustering_bp.route('/export/<clustering_type>')
def export_clustering(clustering_type: str) -> str:
    """Exportar resultados de clusterização"""
    try:
        n_clusters = int(request.args.get('clusters', 5))

        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        clustering_service = DynamicClusteringService(data_loader)

        # Perform clustering
        result = clustering_service.perform_clustering(clustering_type, n_clusters)

        if 'error' in result:
            return jsonify(result), 400

        # Format for export
        export_data = {
            'clustering_info': {
                'type': clustering_type,
                'n_clusters': n_clusters,
                'timestamp': str(pd.Timestamp.now())
            },
            'clusters': result['clusters'],
            'summary': result['summary']
        }

        return jsonify(export_data)

    except Exception as e:
        logger.error(f"Erro na exportação: {e}")
        return jsonify({"error": str(e)}), 500

@clustering_bp.route('/cluster/<int:cluster_id>')
def cluster_detail(cluster_id: int) -> str:
    """Detalhes de um cluster específico"""
    try:
        clustering_type = request.args.get('type', 'valor_mensalidade')
        n_clusters = int(request.args.get('clusters', 5))

        # Initialize services
        clustering_service = _initialize_clustering_service()

        # Perform clustering
        clustering_result = clustering_service.perform_clustering(clustering_type, n_clusters)

        if 'error' in clustering_result:
            flash(f'Erro na clusterização: {clustering_result["error"]}', 'error')
            return render_template(CLUSTER_DETAIL_TEMPLATE,
                                  cluster={},
                                  clustering_type=clustering_type)

        # Find the specific cluster
        cluster = None
        for c in clustering_result.get('clusters', []):
            if c['cluster_id'] == cluster_id:
                cluster = c
                break

        if not cluster:
            flash('Cluster não encontrado', 'error')
            return render_template(CLUSTER_DETAIL_TEMPLATE,
                                  cluster={},
                                  clustering_type=clustering_type)

        # Get detailed data for this cluster
        data_loader = DataLoader()
        data_loader.load_data()
        df = data_loader.get_data()
        cluster_turmas = cluster.get('turmas', [])
        cluster_data = df[df['Turma'].isin(cluster_turmas)] if cluster_turmas else pd.DataFrame()

        # Calculate additional metrics
        cluster_metrics = {}
        if not cluster_data.empty:
            cluster_metrics = {
                'total_records': len(cluster_data),
                'unique_leads': cluster_data['Nome do Lead'].nunique() if 'Nome do Lead' in cluster_data.columns else 0,
                'avg_mensalidade': cluster_data['Valor Mensalidade'].mean() if 'Valor Mensalidade' in cluster_data.columns else 0,
                'total_receita': cluster_data['Valor Mensalidade'].sum() if 'Valor Mensalidade' in cluster_data.columns else 0,
                'status_distribution': cluster_data['Status_Implantacao'].value_counts().to_dict() if 'Status_Implantacao' in cluster_data.columns else {}
            }

        return render_template(CLUSTER_DETAIL_TEMPLATE,
                              cluster=cluster,
                              cluster_metrics=cluster_metrics,
                              clustering_type=clustering_type,
                              cluster_data=cluster_data.head(100).to_dict('records') if not cluster_data.empty else [])

    except Exception as e:
        logger.error(f"Erro nos detalhes do cluster: {e}")
        flash(f'Erro ao carregar detalhes: {str(e)}', 'error')
        return render_template(CLUSTER_DETAIL_TEMPLATE,
                              cluster={},
                              cluster_metrics={},
                              clustering_type=clustering_type,
                              cluster_data=[])

# Novos endpoints para o serviço de clusterização de turmas

@clustering_bp.route('/api/turma-clustering/summary')
def api_turma_clustering_summary() -> Response:
    """API para obter resumo da clusterização de turmas"""
    try:
        turma_service = TurmaClusteringService()
        summary = turma_service.get_cluster_summary()

        if 'error' in summary:
            return jsonify(summary), 400

        return jsonify(summary)

    except Exception as e:
        logger.error(f"Erro ao obter resumo de clusterização de turmas: {e}")
        return jsonify({"error": str(e)}), 500

@clustering_bp.route('/api/turma-clustering/turma/<turma_nome>')
def api_turma_cluster(turma_nome: str) -> Response:
    """API para obter cluster de uma turma específica"""
    try:
        turma_service = TurmaClusteringService()
        result = turma_service.get_turma_cluster(turma_nome)

        if 'error' in result:
            return jsonify(result), 404

        return jsonify(result)

    except Exception as e:
        logger.error(f"Erro ao obter cluster da turma {turma_nome}: {e}")
        return jsonify({"error": str(e)}), 500

@clustering_bp.route('/api/turma-clustering/cluster/<int:cluster_id>')
def api_cluster_turmas(cluster_id: int) -> Response:
    """API para obter todas as turmas de um cluster"""
    try:
        turma_service = TurmaClusteringService()
        result = turma_service.get_cluster_turmas(cluster_id)

        if 'error' in result:
            return jsonify(result), 404

        return jsonify(result)

    except Exception as e:
        logger.error(f"Erro ao obter turmas do cluster {cluster_id}: {e}")
        return jsonify({"error": str(e)}), 500

@clustering_bp.route('/api/turma-clustering/insights')
def api_clustering_insights() -> Response:
    """API para obter insights da clusterização"""
    try:
        turma_service = TurmaClusteringService()
        insights = turma_service.get_clustering_insights()

        if 'error' in insights:
            return jsonify(insights), 400

        return jsonify(insights)

    except Exception as e:
        logger.error(f"Erro ao obter insights de clusterização: {e}")
        return jsonify({"error": str(e)}), 500

@clustering_bp.route('/turma-analysis')
def turma_analysis() -> str:
    """Página de análise de clusterização de turmas"""
    try:
        turma_service = TurmaClusteringService()

        # Carregar dados
        if not turma_service.load_clustering_data():
            flash('Dados de clusterização não disponíveis. Execute a clusterização primeiro.', 'warning')
            return render_template('clustering/turma_analysis.html',
                                  summary=None,
                                  insights=None)

        # Obter resumo dos clusters
        summary = turma_service.get_cluster_summary()

        # Obter insights
        insights = turma_service.get_clustering_insights()

        # Limpar dados para JSON
        summary_clean = _clean_data_for_json(summary) if 'error' not in summary else summary
        insights_clean = _clean_data_for_json(insights) if 'error' not in insights else insights

        return render_template('clustering/turma_analysis.html',
                              summary=summary_clean,
                              insights=insights_clean)

    except Exception as e:
        logger.error(f"Erro na página de análise de turmas: {e}")
        flash(f'Erro ao carregar análise: {str(e)}', 'error')
        return render_template('clustering/turma_analysis.html',
                              summary=None,
                              insights=None)

@clustering_bp.route('/api/turma-mapping')
def api_turma_mapping() -> Response:
    """API para servir o arquivo de mapeamento de turmas"""
    try:
        import os
        from flask import current_app

        # Caminho para o arquivo de mapeamento
        mapping_file = os.path.join(current_app.root_path, '..', 'data', 'ML', 'datasets', 'turma_id_mapping.csv')

        if os.path.exists(mapping_file):
            return send_file(mapping_file, mimetype='text/csv')
        else:
            return jsonify({"error": "Arquivo de mapeamento não encontrado"}), 404

    except Exception as e:
        logger.error(f"Erro ao servir mapeamento de turmas: {e}")
        return jsonify({"error": str(e)}), 500

@clustering_bp.route('/api/clustering-data')
def api_clustering_data() -> Response:
    """API para fornecer dados dos clusters para os modais"""
    try:
        import json
        import os
        from flask import current_app

        # Caminho para o arquivo JSON de clustering
        clustering_file = os.path.join(current_app.root_path, '..', 'data', 'ML', 'results', 'clustering_results.json')

        if os.path.exists(clustering_file):
            with open(clustering_file, 'r', encoding='utf-8') as f:
                clustering_data = json.load(f)
            return jsonify(clustering_data)
        else:
            # Fallback: tentar gerar os dados
            logger.warning("Arquivo de clustering não encontrado, tentando gerar...")

            # Executar script de geração
            import subprocess
            script_path = os.path.join(current_app.root_path, '..', 'scripts', 'simple_clustering.py')
            if os.path.exists(script_path):
                subprocess.run(['python', script_path], cwd=os.path.join(current_app.root_path, '..'))

                # Tentar carregar novamente
                if os.path.exists(clustering_file):
                    with open(clustering_file, 'r', encoding='utf-8') as f:
                        clustering_data = json.load(f)
                    return jsonify(clustering_data)

            return jsonify({"error": "Dados de clustering não disponíveis"}), 404

    except Exception as e:
        logger.error(f"Erro ao fornecer dados de clustering: {e}")
        return jsonify({"error": str(e)}), 500

@clustering_bp.route('/api/test-clustering-data')
def test_clustering_data() -> Response:
    """Endpoint de teste para verificar se o JSON está sendo carregado"""
    try:
        import json
        import os

        # Caminho direto para o arquivo JSON
        clustering_file = '/Users/<USER>/Desktop/Amigo_dataapp/one-interno/gestao/data/ML/results/clustering_results.json'

        if os.path.exists(clustering_file):
            with open(clustering_file, 'r', encoding='utf-8') as f:
                clustering_data = json.load(f)

            # Retornar apenas um resumo para teste
            return jsonify({
                "status": "success",
                "total_turmas": clustering_data['metadata']['total_turmas'],
                "n_clusters": clustering_data['metadata']['n_clusters'],
                "clusters_available": list(clustering_data['clusters_data'].keys()),
                "sample_cluster_0_turmas": len(clustering_data['clusters_data']['0']['turmas']) if '0' in clustering_data['clusters_data'] else 0
            })
        else:
            return jsonify({"error": f"Arquivo não encontrado: {clustering_file}"}), 404

    except Exception as e:
        return jsonify({"error": str(e)}), 500