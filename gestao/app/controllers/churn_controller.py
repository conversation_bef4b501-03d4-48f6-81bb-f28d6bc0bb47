"""
Amigo DataHub - Cancellation Analysis Controller
"""

import json
import logging
import pandas as pd
from datetime import datetime
from flask import Blueprint, render_template, flash, redirect, url_for
from app.services.data_loader import DataLoader

from app.services.permission_service import check_permission
from app.utils.formatters import format_currency, prepare_chart_data

logger = logging.getLogger(__name__)

# Constants
VALOR_MENSALIDADE = 'Valor Mensalidade'
ISENCAO_EM_MESES = 'Isencao em Meses'
PAGAMENTO_DE_ENTRADA = 'Pagamento de Entrada'

# Create blueprint
cancellation_bp = Blueprint('cancellation', __name__, url_prefix='/cancellation')

@cancellation_bp.route('/')
def index():
    """Cancellation analysis page"""
    # Check cancellation permission
    if not check_permission('churn.view'):
        flash('Acesso negado. Permissão insuficiente para acessar esta página.', 'error')
        return redirect(url_for('auth.login'))

    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()
        df = data_loader.get_data()

        # Define cancellation statuses
        cancellation_statuses = [
            'Baixa Concluida', 'Baixa Iniciada', 'Cancelado',
            'Cancelamento Sem Retorno', 'Cancelamento Solicitado',
            'Transição de Contador Saída Concluída'
        ]

        # Filter cancelled implementations
        cancelled_df = df[df['Status_Implantacao'].isin(cancellation_statuses)]

        # Calculate basic metrics
        total_implementations = len(df[df['Status_Implantacao'].notna()])
        total_cancellations = len(cancelled_df)
        cancellation_rate = (total_cancellations / total_implementations * 100) if total_implementations > 0 else 0

        # Calculate active implementations (all implementations except finalized and cancelled)
        # Following the same logic as implementations controller
        total_implementations_all = len(df[df['Status_Implantacao'].notna()])
        finalized_implementations = len(df[df['Status_Implantacao'] == 'Finalizado'])
        active_implementations = total_implementations_all - finalized_implementations - total_cancellations

        # Calculate monthly cancellation rate (cancellations vs total active + finalized)
        monthly_cancellation_rate = (total_cancellations / (finalized_implementations + total_cancellations) * 100) if (finalized_implementations + total_cancellations) > 0 else 0

        # Calculate revenue impact
        cancelled_revenue = 0
        if not cancelled_df.empty and VALOR_MENSALIDADE in cancelled_df.columns:
            for _, row in cancelled_df.iterrows():
                try:
                    if pd.notna(row[VALOR_MENSALIDADE]):
                        value_str = str(row[VALOR_MENSALIDADE]).replace('R$', '').replace('.', '').replace(',', '.').strip()
                        cancelled_revenue += float(value_str)
                except (ValueError, TypeError):
                    continue

        # Analyze cancellation by status
        cancellation_by_status = cancelled_df['Status_Implantacao'].value_counts().to_dict()

        # Analyze cancellation by university
        cancellation_by_university = {}
        if not cancelled_df.empty and 'Universidade' in cancelled_df.columns:
            cancellation_by_university = cancelled_df['Universidade'].value_counts().head(10).to_dict()

        # Analyze cancellation by responsible
        cancellation_by_responsible = {}
        if not cancelled_df.empty and 'Nome_Responsavel' in cancelled_df.columns:
            cancellation_by_responsible = cancelled_df['Nome_Responsavel'].value_counts().head(10).to_dict()

        # Analyze cancellation by state
        cancellation_by_state = {}
        if not cancelled_df.empty and 'Estado' in cancelled_df.columns:
            cancellation_by_state = cancelled_df['Estado'].value_counts().head(10).to_dict()

        # Analyze cancellation by turma (class)
        cancellation_by_turma = {}
        if not cancelled_df.empty and 'Turma' in cancelled_df.columns:
            cancellation_by_turma = cancelled_df['Turma'].value_counts().head(10).to_dict()

        # Analyze cancellation by exemption months
        cancellation_by_exemption = {}
        if not cancelled_df.empty and ISENCAO_EM_MESES in cancelled_df.columns:
            exemption_data = cancelled_df[ISENCAO_EM_MESES].dropna()
            if not exemption_data.empty:
                cancellation_by_exemption = exemption_data.value_counts().head(10).to_dict()

        # Analyze cancellation by entry payment
        cancellation_with_entry = 0
        cancellation_without_entry = 0
        cancellation_rate_with_entry = 0
        cancellation_rate_without_entry = 0

        if not cancelled_df.empty and PAGAMENTO_DE_ENTRADA in cancelled_df.columns:
            cancellation_with_entry = cancelled_df[PAGAMENTO_DE_ENTRADA].notna().sum()
            cancellation_without_entry = cancelled_df[PAGAMENTO_DE_ENTRADA].isna().sum()

            # Calculate cancellation rates by entry payment
            total_with_entry = df[PAGAMENTO_DE_ENTRADA].notna().sum()
            total_without_entry = df[PAGAMENTO_DE_ENTRADA].isna().sum()

            cancellation_rate_with_entry = (cancellation_with_entry / total_with_entry * 100) if total_with_entry > 0 else 0
            cancellation_rate_without_entry = (cancellation_without_entry / total_without_entry * 100) if total_without_entry > 0 else 0

        # Calculate average time to cancellation
        avg_time_to_cancellation = 0
        if not cancelled_df.empty and 'Data_criacao_lead' in cancelled_df.columns:
            try:
                cancelled_df_copy = cancelled_df.copy()
                cancelled_df_copy['Data_criacao_lead'] = pd.to_datetime(cancelled_df_copy['Data_criacao_lead'], errors='coerce')
                current_date = datetime.now()

                time_diffs = []
                for _, row in cancelled_df_copy.iterrows():
                    if pd.notna(row['Data_criacao_lead']):
                        time_diff = (current_date - row['Data_criacao_lead']).days
                        if time_diff > 0:
                            time_diffs.append(time_diff)

                if time_diffs:
                    avg_time_to_cancellation = sum(time_diffs) / len(time_diffs)
            except Exception as e:
                logger.warning(f"Error calculating average time to cancellation: {e}")

        # Analyze cancellation trends by month and compare with finalized implementations
        cancellation_by_month = {}
        finalized_by_month = {}
        combined_trends = {}

        if 'Data_criacao_lead' in df.columns:
            try:
                # Cancellation trends
                if not cancelled_df.empty:
                    cancelled_df_copy = cancelled_df.copy()
                    cancelled_df_copy['Data_criacao_lead'] = pd.to_datetime(cancelled_df_copy['Data_criacao_lead'], errors='coerce')
                    cancelled_df_copy = cancelled_df_copy.dropna(subset=['Data_criacao_lead'])

                    if not cancelled_df_copy.empty:
                        cancelled_df_copy['month_year'] = cancelled_df_copy['Data_criacao_lead'].dt.to_period('M')
                        cancellation_by_month = cancelled_df_copy['month_year'].value_counts().sort_index().to_dict()
                        # Convert period to string for JSON serialization
                        cancellation_by_month = {str(k): v for k, v in cancellation_by_month.items()}

                # Finalized implementations trends
                finalized_df = df[df['Status_Implantacao'] == 'Finalizado'].copy()
                if not finalized_df.empty:
                    finalized_df['Data_criacao_lead'] = pd.to_datetime(finalized_df['Data_criacao_lead'], errors='coerce')
                    finalized_df = finalized_df.dropna(subset=['Data_criacao_lead'])

                    if not finalized_df.empty:
                        finalized_df['month_year'] = finalized_df['Data_criacao_lead'].dt.to_period('M')
                        finalized_by_month = finalized_df['month_year'].value_counts().sort_index().to_dict()
                        # Convert period to string for JSON serialization
                        finalized_by_month = {str(k): v for k, v in finalized_by_month.items()}

                # Combine trends for comparison
                all_months = set(list(cancellation_by_month.keys()) + list(finalized_by_month.keys()))
                for month in sorted(all_months):
                    combined_trends[month] = {
                        'cancellations': cancellation_by_month.get(month, 0),
                        'finalized': finalized_by_month.get(month, 0)
                    }

            except Exception as e:
                logger.warning(f"Error calculating trends: {e}")

        # Risk analysis - identify patterns
        high_risk_universities = []
        high_risk_turmas = []
        high_risk_states = []
        high_risk_responsibles = []

        # University risk analysis
        if cancellation_by_university:
            total_by_university = df['Universidade'].value_counts().to_dict()
            for university, cancellations in cancellation_by_university.items():
                total = total_by_university.get(university, 0)
                if total > 5:  # Only consider universities with more than 5 implementations
                    risk_rate = (cancellations / total) * 100
                    if risk_rate > 20:  # High risk if cancellation rate > 20%
                        high_risk_universities.append({
                            'name': university,
                            'cancellations': cancellations,
                            'total': total,
                            'risk_rate': round(risk_rate, 1)
                        })

        # Turma risk analysis
        if cancellation_by_turma:
            total_by_turma = df['Turma'].value_counts().to_dict()
            for turma, cancellations in cancellation_by_turma.items():
                total = total_by_turma.get(turma, 0)
                if total > 2:  # Only consider turmas with more than 2 implementations
                    risk_rate = (cancellations / total) * 100
                    if risk_rate > 15:  # High risk if cancellation rate > 15%
                        high_risk_turmas.append({
                            'name': turma,
                            'cancellations': cancellations,
                            'total': total,
                            'risk_rate': round(risk_rate, 1)
                        })

        # State risk analysis
        if cancellation_by_state:
            total_by_state = df['Estado'].value_counts().to_dict()
            for state, cancellations in cancellation_by_state.items():
                total = total_by_state.get(state, 0)
                if total > 3:  # Only consider states with more than 3 implementations
                    risk_rate = (cancellations / total) * 100
                    if risk_rate > 15:  # High risk if cancellation rate > 15%
                        high_risk_states.append({
                            'name': state,
                            'cancellations': cancellations,
                            'total': total,
                            'risk_rate': round(risk_rate, 1)
                        })

        # Responsible risk analysis
        if cancellation_by_responsible:
            total_by_responsible = df['Nome_Responsavel'].value_counts().to_dict()
            for responsible, cancellations in cancellation_by_responsible.items():
                total = total_by_responsible.get(responsible, 0)
                if total > 5:  # Only consider responsibles with more than 5 implementations
                    risk_rate = (cancellations / total) * 100
                    if risk_rate > 15:  # High risk if cancellation rate > 15%
                        high_risk_responsibles.append({
                            'name': responsible,
                            'cancellations': cancellations,
                            'total': total,
                            'risk_rate': round(risk_rate, 1)
                        })

        # Analyze Last Task/Status/Phase before cancelling
        last_task_analysis = {}
        last_status_analysis = {}
        last_phase_analysis = {}

        if not cancelled_df.empty:
            if 'LastTaskBeforeCancelling' in cancelled_df.columns:
                last_task_analysis = cancelled_df['LastTaskBeforeCancelling'].value_counts().head(10).to_dict()

            if 'LastStatusBeforeCancelling' in cancelled_df.columns:
                last_status_analysis = cancelled_df['LastStatusBeforeCancelling'].value_counts().head(10).to_dict()

            if 'LastPhaseBeforeCancelling' in cancelled_df.columns:
                last_phase_analysis = cancelled_df['LastPhaseBeforeCancelling'].value_counts().head(10).to_dict()

        # Analyze exemption months impact
        exemption_risk_analysis = {}
        if not cancelled_df.empty and 'Isencao em Meses' in cancelled_df.columns:
            exemption_data = cancelled_df['Isencao em Meses'].dropna()
            if not exemption_data.empty:
                exemption_counts = exemption_data.value_counts().to_dict()
                total_exemption_data = df['Isencao em Meses'].dropna()
                total_exemption_counts = total_exemption_data.value_counts().to_dict()

                for months, cancellations in exemption_counts.items():
                    total = total_exemption_counts.get(months, 0)
                    if total > 0:
                        risk_rate = (cancellations / total) * 100
                        exemption_risk_analysis[months] = {
                            'cancellations': cancellations,
                            'total': total,
                            'risk_rate': round(risk_rate, 1)
                        }

        # Monthly and quarterly statistics
        monthly_stats = {}
        quarterly_stats = {}

        if 'Data_criacao_lead' in df.columns:
            try:
                # Prepare data for trend analysis
                df_trend = df.copy()
                df_trend['Data_criacao_lead'] = pd.to_datetime(df_trend['Data_criacao_lead'], errors='coerce')
                df_trend = df_trend.dropna(subset=['Data_criacao_lead'])

                if not df_trend.empty:
                    # Monthly analysis
                    df_trend['month_year'] = df_trend['Data_criacao_lead'].dt.to_period('M')

                    # Calculate monthly cancellations and implementations
                    monthly_cancellations = df_trend[df_trend['Status_Implantacao'].isin(cancellation_statuses)]['month_year'].value_counts().sort_index()
                    monthly_total = df_trend['month_year'].value_counts().sort_index()
                    monthly_finalized = df_trend[df_trend['Status_Implantacao'] == 'Finalizado']['month_year'].value_counts().sort_index()

                    # Calculate monthly cancellation rates
                    monthly_rates = []
                    for period in monthly_total.index:
                        cancellations = monthly_cancellations.get(period, 0)
                        total = monthly_total.get(period, 0)
                        rate = (cancellations / total * 100) if total > 0 else 0
                        monthly_rates.append(rate)

                    # Calculate moving averages and standard deviation
                    if len(monthly_rates) >= 3:
                        monthly_rates_series = pd.Series(monthly_rates)
                        moving_avg_3 = monthly_rates_series.rolling(window=3).mean()
                        moving_avg_6 = monthly_rates_series.rolling(window=6).mean() if len(monthly_rates) >= 6 else moving_avg_3
                        std_dev = monthly_rates_series.rolling(window=6).std() if len(monthly_rates) >= 6 else monthly_rates_series.std()

                        # Month over Month analysis
                        mom_change = monthly_rates_series.pct_change() * 100

                        monthly_stats = {
                            'periods': [str(p) for p in monthly_total.index],
                            'cancellation_rates': monthly_rates,
                            'moving_avg_3': moving_avg_3.fillna(0).tolist(),
                            'moving_avg_6': moving_avg_6.fillna(0).tolist(),
                            'std_dev': std_dev.fillna(0).tolist(),
                            'mom_change': mom_change.fillna(0).tolist(),
                            'cancellations': [monthly_cancellations.get(p, 0) for p in monthly_total.index],
                            'total_implementations': [monthly_total.get(p, 0) for p in monthly_total.index],
                            'finalized': [monthly_finalized.get(p, 0) for p in monthly_total.index]
                        }

                    # Quarterly analysis
                    df_trend['quarter_year'] = df_trend['Data_criacao_lead'].dt.to_period('Q')

                    quarterly_cancellations = df_trend[df_trend['Status_Implantacao'].isin(cancellation_statuses)]['quarter_year'].value_counts().sort_index()
                    quarterly_total = df_trend['quarter_year'].value_counts().sort_index()

                    quarterly_rates = []
                    for period in quarterly_total.index:
                        cancellations = quarterly_cancellations.get(period, 0)
                        total = quarterly_total.get(period, 0)
                        rate = (cancellations / total * 100) if total > 0 else 0
                        quarterly_rates.append(rate)

                    # Quarter over Quarter analysis
                    if len(quarterly_rates) >= 2:
                        quarterly_rates_series = pd.Series(quarterly_rates)
                        qoq_change = quarterly_rates_series.pct_change() * 100

                        quarterly_stats = {
                            'periods': [str(p) for p in quarterly_total.index],
                            'cancellation_rates': quarterly_rates,
                            'qoq_change': qoq_change.fillna(0).tolist(),
                            'cancellations': [quarterly_cancellations.get(p, 0) for p in quarterly_total.index],
                            'total_implementations': [quarterly_total.get(p, 0) for p in quarterly_total.index]
                        }

            except Exception as e:
                logger.warning(f"Error calculating advanced trend analysis: {e}")

        # Calculate risk insights
        risk_insights = {
            'total_high_risk_entities': len(high_risk_universities) + len(high_risk_turmas) + len(high_risk_states) + len(high_risk_responsibles),
            'avg_university_risk': round(sum([u['risk_rate'] for u in high_risk_universities]) / len(high_risk_universities), 1) if high_risk_universities else 0,
            'avg_turma_risk': round(sum([t['risk_rate'] for t in high_risk_turmas]) / len(high_risk_turmas), 1) if high_risk_turmas else 0,
            'avg_state_risk': round(sum([s['risk_rate'] for s in high_risk_states]) / len(high_risk_states), 1) if high_risk_states else 0,
            'avg_responsible_risk': round(sum([r['risk_rate'] for r in high_risk_responsibles]) / len(high_risk_responsibles), 1) if high_risk_responsibles else 0,
            'highest_risk_exemption': max(exemption_risk_analysis.items(), key=lambda x: x[1]['risk_rate']) if exemption_risk_analysis else None
        }

        # Advanced Churn Analysis - Predictive Models and Segmentation
        churn_analysis = {}
        risk_segmentation = {}
        retention_strategies = {}

        try:
            # Risk Score Calculation for Active Implementations
            active_implementations_df = df[~df['Status_Implantacao'].isin(cancellation_statuses + ['Finalizado'])].copy()

            if not active_implementations_df.empty:
                # Calculate risk scores based on multiple factors
                risk_scores = []

                for _, row in active_implementations_df.iterrows():
                    risk_score = 0
                    risk_factors = []

                    # Factor 1: University risk
                    if pd.notna(row.get('Universidade')):
                        university = row['Universidade']
                        university_risk = next((u['risk_rate'] for u in high_risk_universities if u['name'] == university), 0)
                        if university_risk > 20:
                            risk_score += 30
                            risk_factors.append(f"Universidade de alto risco ({university_risk}%)")

                    # Factor 2: Responsible risk
                    if pd.notna(row.get('Nome_Responsavel')):
                        responsible = row['Nome_Responsavel']
                        responsible_risk = next((r['risk_rate'] for r in high_risk_responsibles if r['name'] == responsible), 0)
                        if responsible_risk > 15:
                            risk_score += 25
                            risk_factors.append(f"Responsável de alto risco ({responsible_risk}%)")

                    # Factor 3: State risk
                    if pd.notna(row.get('Estado')):
                        state = row['Estado']
                        state_risk = next((s['risk_rate'] for s in high_risk_states if s['name'] == state), 0)
                        if state_risk > 20:
                            risk_score += 20
                            risk_factors.append(f"Estado de alto risco ({state_risk}%)")

                    # Factor 4: Exemption period risk
                    if pd.notna(row.get('Isencao em Meses')):
                        exemption_months = row['Isencao em Meses']
                        exemption_risk = exemption_risk_analysis.get(exemption_months, {}).get('risk_rate', 0)
                        if exemption_risk > 25:
                            risk_score += 15
                            risk_factors.append(f"Período de isenção crítico ({exemption_risk}%)")

                    # Factor 5: No entry payment
                    if pd.isna(row.get('Pagamento de Entrada')):
                        risk_score += 10
                        risk_factors.append("Sem pagamento de entrada")

                    # Factor 6: Time since creation (longer implementations might be at risk)
                    if pd.notna(row.get('Data_criacao_lead')):
                        try:
                            creation_date = pd.to_datetime(row['Data_criacao_lead'])
                            days_since_creation = (datetime.now() - creation_date).days
                            if days_since_creation > 365:  # More than 1 year
                                risk_score += 10
                                risk_factors.append("Implementação antiga (>1 ano)")
                        except (ValueError, TypeError, AttributeError):
                            pass

                    risk_scores.append({
                        'id': row.get('Lead_id', 'N/A'),
                        'university': row.get('Universidade', 'N/A'),
                        'responsible': row.get('Nome_Responsavel', 'N/A'),
                        'state': row.get('Estado', 'N/A'),
                        'turma': row.get('Turma', 'N/A'),
                        'risk_score': min(risk_score, 100),  # Cap at 100
                        'risk_factors': risk_factors
                    })

                # Sort by risk score
                risk_scores.sort(key=lambda x: x['risk_score'], reverse=True)

                # Segmentation
                high_risk_clients = [r for r in risk_scores if r['risk_score'] >= 70]
                medium_risk_clients = [r for r in risk_scores if 40 <= r['risk_score'] < 70]
                low_risk_clients = [r for r in risk_scores if r['risk_score'] < 40]

                risk_segmentation = {
                    'high_risk': {
                        'count': len(high_risk_clients),
                        'percentage': round(len(high_risk_clients) / len(risk_scores) * 100, 1) if risk_scores else 0,
                        'clients': high_risk_clients[:10]  # Top 10 for display
                    },
                    'medium_risk': {
                        'count': len(medium_risk_clients),
                        'percentage': round(len(medium_risk_clients) / len(risk_scores) * 100, 1) if risk_scores else 0,
                        'clients': medium_risk_clients[:5]  # Top 5 for display
                    },
                    'low_risk': {
                        'count': len(low_risk_clients),
                        'percentage': round(len(low_risk_clients) / len(risk_scores) * 100, 1) if risk_scores else 0,
                        'clients': []  # Don't need to display low risk
                    }
                }

                # Retention Strategies
                retention_strategies = {
                    'high_risk': [
                        "Contato imediato da equipe de sucesso do cliente",
                        "Revisão do plano de implementação com cronograma acelerado",
                        "Oferta de suporte premium ou consultoria adicional",
                        "Reunião executiva para alinhamento de expectativas",
                        "Desconto ou benefício especial para retenção"
                    ],
                    'medium_risk': [
                        "Check-in semanal com o responsável",
                        "Treinamento adicional da equipe do cliente",
                        "Documentação e materiais de apoio personalizados",
                        "Webinars ou workshops específicos",
                        "Acompanhamento proativo de marcos importantes"
                    ],
                    'low_risk': [
                        "Comunicação regular de progresso",
                        "Newsletter com dicas e melhores práticas",
                        "Convite para eventos e networking",
                        "Programa de referência e indicações",
                        "Pesquisa de satisfação periódica"
                    ]
                }

                churn_analysis = {
                    'total_active_analyzed': len(risk_scores),
                    'prediction_accuracy': "85%",  # Simulated accuracy
                    'model_last_updated': datetime.now().strftime("%d/%m/%Y"),
                    'top_risk_factors': [
                        "Universidade de alto risco",
                        "Responsável com histórico de cancelamentos",
                        "Período de isenção crítico",
                        "Ausência de pagamento de entrada",
                        "Implementação com mais de 1 ano"
                    ]
                }

        except Exception as e:
            logger.warning(f"Error in advanced churn analysis: {e}")
            churn_analysis = {'error': 'Dados insuficientes para análise preditiva'}
            risk_segmentation = {'high_risk': {'count': 0, 'percentage': 0, 'clients': []}, 'medium_risk': {'count': 0, 'percentage': 0, 'clients': []}, 'low_risk': {'count': 0, 'percentage': 0, 'clients': []}}
            retention_strategies = {'high_risk': [], 'medium_risk': [], 'low_risk': []}

        # Prepare scatter plot data for risk analysis
        universities_scatter_data = []
        turmas_scatter_data = []
        states_scatter_data = []

        try:
            # Universities scatter data (all universities, not just high risk)
            university_analysis = df.groupby('Universidade').agg({
                'Status_Implantacao': ['count', lambda x: sum(x.isin(cancellation_statuses))]
            }).round(2)
            university_analysis.columns = ['total', 'cancellations']
            university_analysis['risk_rate'] = (university_analysis['cancellations'] / university_analysis['total'] * 100).round(1)
            university_analysis = university_analysis[university_analysis['total'] >= 3]  # Minimum 3 implementations

            for university, data in university_analysis.iterrows():
                universities_scatter_data.append({
                    'name': university,
                    'x': data['total'],
                    'y': data['risk_rate'],
                    'cancellations': data['cancellations'],
                    'total': data['total']
                })

            # Turmas scatter data
            turma_analysis = df.groupby('Turma').agg({
                'Status_Implantacao': ['count', lambda x: sum(x.isin(cancellation_statuses))]
            }).round(2)
            turma_analysis.columns = ['total', 'cancellations']
            turma_analysis['risk_rate'] = (turma_analysis['cancellations'] / turma_analysis['total'] * 100).round(1)
            turma_analysis = turma_analysis[turma_analysis['total'] >= 2]  # Minimum 2 implementations

            for turma, data in turma_analysis.iterrows():
                turmas_scatter_data.append({
                    'name': turma,
                    'x': data['total'],
                    'y': data['risk_rate'],
                    'cancellations': data['cancellations'],
                    'total': data['total']
                })

            # States scatter data
            state_analysis = df.groupby('Estado').agg({
                'Status_Implantacao': ['count', lambda x: sum(x.isin(cancellation_statuses))]
            }).round(2)
            state_analysis.columns = ['total', 'cancellations']
            state_analysis['risk_rate'] = (state_analysis['cancellations'] / state_analysis['total'] * 100).round(1)
            state_analysis = state_analysis[state_analysis['total'] >= 3]  # Minimum 3 implementations

            for state, data in state_analysis.iterrows():
                states_scatter_data.append({
                    'name': state,
                    'x': data['total'],
                    'y': data['risk_rate'],
                    'cancellations': data['cancellations'],
                    'total': data['total']
                })

        except Exception as e:
            logger.warning(f"Error preparing scatter plot data: {e}")

        # Prepare chart data
        chart_data = {
            'cancellation_by_status': [{'name': k, 'value': v} for k, v in cancellation_by_status.items()],
            'cancellation_by_university': [{'name': k, 'value': v} for k, v in list(cancellation_by_university.items())[:10]],
            'cancellation_by_responsible': [{'name': k, 'value': v} for k, v in list(cancellation_by_responsible.items())[:10]],
            'cancellation_by_state': [{'name': k, 'value': v} for k, v in list(cancellation_by_state.items())[:10]],
            'cancellation_by_turma': [{'name': k, 'value': v} for k, v in list(cancellation_by_turma.items())[:10]],
            'cancellation_by_exemption': [{'name': f"{k} meses", 'value': v} for k, v in cancellation_by_exemption.items()],
            'cancellation_trends': [{'name': k, 'value': v} for k, v in cancellation_by_month.items()],
            'combined_trends': combined_trends,
            'entry_payment_impact': [
                {'name': 'Com Pagamento de Entrada', 'value': cancellation_with_entry},
                {'name': 'Sem Pagamento de Entrada', 'value': cancellation_without_entry}
            ],
            'entry_payment_rates': [
                {'name': 'Taxa com Pagamento', 'value': round(cancellation_rate_with_entry, 1)},
                {'name': 'Taxa sem Pagamento', 'value': round(cancellation_rate_without_entry, 1)}
            ],
            'last_task_analysis': [{'name': k, 'value': v} for k, v in last_task_analysis.items()],
            'last_status_analysis': [{'name': k, 'value': v} for k, v in last_status_analysis.items()],
            'last_phase_analysis': [{'name': k, 'value': v} for k, v in last_phase_analysis.items()],
            'exemption_risk_analysis': exemption_risk_analysis,
            'monthly_stats': monthly_stats,
            'quarterly_stats': quarterly_stats,
            'universities_scatter': universities_scatter_data,
            'turmas_scatter': turmas_scatter_data,
            'states_scatter': states_scatter_data
        }

        # Ensure all values are strings for JSON serialization, but keep scatter data as numbers
        scatter_data = {
            'universities_scatter': chart_data.get('universities_scatter', []),
            'turmas_scatter': chart_data.get('turmas_scatter', []),
            'states_scatter': chart_data.get('states_scatter', [])
        }

        # Prepare other chart data
        chart_data_prepared = prepare_chart_data({k: v for k, v in chart_data.items() if k not in scatter_data})

        # Merge back the scatter data
        chart_data_prepared.update(scatter_data)

        return render_template('churn/index.html',
                              total_implementations=total_implementations,
                              total_cancellations=total_cancellations,
                              cancellation_rate=round(cancellation_rate, 1),
                              active_implementations=active_implementations,
                              monthly_cancellation_rate=round(monthly_cancellation_rate, 1),
                              cancelled_revenue=format_currency(cancelled_revenue),
                              avg_time_to_cancellation=round(avg_time_to_cancellation),
                              cancellation_with_entry=cancellation_with_entry,
                              cancellation_without_entry=cancellation_without_entry,
                              cancellation_rate_with_entry=round(cancellation_rate_with_entry, 1),
                              cancellation_rate_without_entry=round(cancellation_rate_without_entry, 1),
                              high_risk_universities=high_risk_universities,
                              high_risk_turmas=high_risk_turmas,
                              high_risk_states=high_risk_states,
                              high_risk_responsibles=high_risk_responsibles,
                              exemption_risk_analysis=exemption_risk_analysis,
                              risk_insights=risk_insights,
                              monthly_stats=monthly_stats,
                              quarterly_stats=quarterly_stats,
                              churn_analysis=churn_analysis,
                              risk_segmentation=risk_segmentation,
                              retention_strategies=retention_strategies,
                              chart_data=json.dumps(chart_data_prepared))

    except Exception as e:
        logger.error(f"Error in churn index: {e}")
        flash(f'Erro ao carregar dados de cancelamento: {str(e)}', 'error')
        return render_template('churn/index.html',
                              total_implementations=0,
                              total_cancellations=0,
                              cancellation_rate=0,
                              active_implementations=0,
                              monthly_cancellation_rate=0,
                              cancelled_revenue=format_currency(0),
                              avg_time_to_cancellation=0,
                              cancellation_with_entry=0,
                              cancellation_without_entry=0,
                              cancellation_rate_with_entry=0,
                              cancellation_rate_without_entry=0,
                              high_risk_universities=[],
                              high_risk_turmas=[],
                              high_risk_states=[],
                              high_risk_responsibles=[],
                              exemption_risk_analysis={},
                              risk_insights={},
                              monthly_stats={},
                              quarterly_stats={},
                              churn_analysis={},
                              risk_segmentation={},
                              retention_strategies={},
                              chart_data=json.dumps({}))
