"""
Amigo DataHub - University Controller
"""

import json
import logging
import math
import pandas as pd
from flask import Blueprint, render_template, flash, jsonify, request, redirect, url_for
from app.services.data_loader import DataLoader
from app.services.business_rules import BusinessRulesService
from app.services.analytics import AnalyticsService
from app.utils.formatters import format_currency, prepare_chart_data, format_date
from typing import Dict, List, Any, Optional, Union, Tuple
import pandas as pd
from flask import Response

logger = logging.getLogger(__name__)

# Create blueprints
university_bp = Blueprint('university', __name__, url_prefix='/universities')
university_api_bp = Blueprint('university_api', __name__, url_prefix='/api')

@university_bp.route('/')
def index() -> str:
    """University index page"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        business_rules = BusinessRulesService(data_loader)
        analytics = AnalyticsService(data_loader, business_rules)

        # Identify active universities
        total_universities, active_universities_count = business_rules.identify_active_universities()

        # Identify active students
        total_students, active_students_count = business_rules.identify_active_students()

        # Calculate MRR
        mrr_total, finalized_count, _, _ = business_rules.calculate_mrr()

        # Calculate potential revenue
        potential_revenue = business_rules.calculate_potential_revenue()

        # Calculate active implementations
        active_implementations_count = business_rules.calculate_active_implementations()

        # Count finalized implementations
        finalized_implementations_count = finalized_count

        # Calculate average ticket
        avg_ticket = business_rules.calculate_average_ticket(mrr_total, finalized_count)

        # Calculate average revenue per university
        avg_revenue_per_university = mrr_total / active_universities_count if active_universities_count > 0 else 0

        # Calculate average conversion rate
        _, _, _, _, opps_with_impl, avg_conversion_rate = business_rules.calculate_conversion_rates()

        # Format values for display
        recurring_revenue_formatted = format_currency(mrr_total)
        potential_revenue_formatted = format_currency(potential_revenue)
        avg_ticket_formatted = format_currency(avg_ticket)
        avg_revenue_per_university_formatted = format_currency(avg_revenue_per_university)

        # Get top university data
        top_university_name, top_university_percentage = analytics.get_top_university_data()

        # Get top course data
        top_course_name, top_course_count = analytics.get_top_course_data()

        # Get top revenue data
        top_revenue_university, top_revenue_value = analytics.get_top_revenue_data()

        # Get status positions
        _, status_positions = business_rules.calculate_implementation_status()

        # Get MRR by university
        mrr_by_university, mrr_percentage_by_university, _ = analytics.get_mrr_by_university()

        # Prepare chart data
        chart_data = analytics.prepare_chart_data()

        # Ensure all values are strings
        chart_data = prepare_chart_data(chart_data)

        return render_template('universities/index.html',
                              total_universities=total_universities,
                              active_universities_count=active_universities_count,
                              total_students=total_students,
                              active_students_count=active_students_count,
                              recurring_revenue=recurring_revenue_formatted,
                              potential_revenue=potential_revenue_formatted,
                              active_implementations_count=active_implementations_count,
                              finalized_implementations_count=finalized_implementations_count,
                              avg_ticket=avg_ticket_formatted,
                              avg_revenue_per_university=avg_revenue_per_university_formatted,
                              avg_conversion_rate=avg_conversion_rate,

                              status_positions=status_positions,
                              chart_data=json.dumps(chart_data),
                              top_university_name=top_university_name,
                              top_university_percentage=top_university_percentage,
                              top_course_name=top_course_name,
                              top_course_count=top_course_count,
                              top_revenue_university=top_revenue_university,
                              top_revenue_value=top_revenue_value,
                              mrr_by_university=mrr_by_university,
                              mrr_percentage_by_university=mrr_percentage_by_university)
    except Exception as e:
        logger.error(f"Error in university index: {e}")
        flash(f'Error loading university data: {str(e)}', 'error')
        return render_template('universities/index.html',
                              total_universities=0,
                              active_universities_count=0,
                              total_students=0,
                              active_students_count=0,
                              recurring_revenue="R$ 0,00",
                              potential_revenue="R$ 0,00",
                              active_implementations_count=0,
                              finalized_implementations_count=0,
                              avg_ticket="R$ 0,00",
                              avg_revenue_per_university="R$ 0,00",
                              avg_conversion_rate=0,

                              status_positions={},
                              chart_data=json.dumps({}),
                              top_university_name="Not available",
                              top_university_percentage="0",
                              top_course_name="Not available",
                              top_course_count="0",
                              top_revenue_university="Not available",
                              top_revenue_value="R$ 0,00",
                              mrr_by_university={},
                              mrr_percentage_by_university={})

@university_bp.route('/<university>')
def detail(university: List[Any]) -> str:
    """
    # Check universities permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission
    
    if not check_permission('universities.view'):
        flash('Acesso negado. Permissão insuficiente para acessar esta página.', 'error')
        return redirect(url_for('auth.login'))
University detail page"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        business_rules = BusinessRulesService(data_loader)
        analytics = AnalyticsService(data_loader, business_rules)

        df = data_loader.get_data()

        # Check if Universidade column exists
        if 'Universidade' not in df.columns:
            logger.error(f"Column 'Universidade' not found in dataframe for university: {university}")
            flash('Estrutura de dados inválida: coluna Universidade não encontrada', 'error')
            return redirect(url_for('university.index'))

        # Filter data for the specified university
        university_df = df[df['Universidade'] == university].copy()

        if university_df.empty:
            logger.warning(f"No data found for university: {university}")
            flash('Universidade não encontrada ou sem dados disponíveis', 'warning')
            return redirect(url_for('university.index'))

        # Replace NaN values with None
        for col in university_df.columns:
            university_df[col] = university_df[col].apply(lambda x: None if pd.isna(x) or (isinstance(x, float) and math.isnan(x)) else x)

        # Get university details
        university_city = university_df['Cidade'].iloc[0] if 'Cidade' in university_df.columns else 'N/A'
        university_state = university_df['Estado'].iloc[0] if 'Estado' in university_df.columns else 'N/A'
        university_status = 'Ativa' if len(university_df) > 0 else 'Inativa'

        # Count implementations and opportunities
        finalized_implementations = []
        active_implementations = []
        opportunities = []

        # Get implementation status
        if 'Status_Implantacao' in university_df.columns and 'Oportunidade_id' in university_df.columns:
            # Finalized implementations
            finalized_df = university_df[university_df['Status_Implantacao'] == 'Finalizado'].drop_duplicates(subset=['Oportunidade_id'])
            finalized_count = len(finalized_df)

            for _, row in finalized_df.iterrows():
                finalized_implementations.append({
                    'lead_name': row.get('Nome do Lead', 'N/A'),
                    'product': row.get('Produto', 'N/A'),
                    'finalization_date': format_date(row.get('Data_Finalizacao')),
                    'monthly_value': format_currency(row.get('Valor Mensalidade', 0)),
                    'responsible': row.get('ResponsableOnboarding', 'N/A')
                })

            # Active implementations
            active_df = university_df[(university_df['Status_Implantacao'] == 'Em Andamento') |
                                     (university_df['Status_Implantacao'].isna() & university_df['Fase_Implantacao'].notna())].drop_duplicates(subset=['Oportunidade_id'])
            active_count = len(active_df)

            for _, row in active_df.iterrows():
                active_implementations.append({
                    'lead_name': row.get('Nome do Lead', 'N/A'),
                    'product': row.get('Produto', 'N/A'),
                    'phase': row.get('Fase_Implantacao', 'N/A'),
                    'expected_end_date': format_date(row.get('DataPrevistaDeFinalização')),
                    'responsible': row.get('ResponsableOnboarding', 'N/A')
                })

            # Opportunities (no implementation phase)
            opp_df = university_df[(university_df['Fase_Implantacao'].isna()) &
                                  (university_df['Oportunidade_id'].notna())].drop_duplicates(subset=['Oportunidade_id'])
            opportunity_count = len(opp_df)

            for _, row in opp_df.iterrows():
                opportunities.append({
                    'lead_name': row.get('Nome do Lead', 'N/A'),
                    'product': row.get('Produto', 'N/A'),
                    'stage': row.get('Etapa do funil Comercial', 'N/A'),
                    'value': format_currency(row.get('Valor Mensalidade', 0)),
                    'responsible': row.get('Nome_Responsavel', 'N/A')
                })
        else:
            finalized_count = 0
            active_count = 0
            opportunity_count = 0

        # Calculate MRR and potential revenue
        mrr_total = 0
        if 'Valor Mensalidade' in university_df.columns and 'Status_Implantacao' in university_df.columns:
            finalized_df = university_df[university_df['Status_Implantacao'] == 'Finalizado']
            for _, row in finalized_df.iterrows():
                try:
                    value = float(row['Valor Mensalidade']) if row['Valor Mensalidade'] else 0
                    mrr_total += value
                except (ValueError, TypeError):
                    pass

        # Calculate potential revenue (active implementations)
        potential_revenue = 0
        if 'Valor Mensalidade' in university_df.columns:
            active_df = university_df[(university_df['Status_Implantacao'] == 'Em Andamento') |
                                     (university_df['Status_Implantacao'].isna() & university_df['Fase_Implantacao'].notna())]
            for _, row in active_df.iterrows():
                try:
                    value = float(row['Valor Mensalidade']) if row['Valor Mensalidade'] else 0
                    potential_revenue += value
                except (ValueError, TypeError):
                    pass

        # Calculate conversion rate
        total_opps = finalized_count + active_count + opportunity_count
        conversion_rate = round((finalized_count / total_opps) * 100, 1) if total_opps > 0 else 0

        # Get status distribution for chart
        status_data = {}
        if 'Status_Implantacao' in university_df.columns:
            status_counts = university_df['Status_Implantacao'].value_counts().to_dict()
            for status, count in status_counts.items():
                if status and not pd.isna(status):
                    status_data[status] = count

        # Get products distribution for chart
        products_data = {}
        if 'Produto' in university_df.columns:
            product_counts = university_df['Produto'].value_counts().to_dict()
            for product, count in product_counts.items():
                if product and not pd.isna(product):
                    products_data[product] = count

        # Get courses data
        courses = []
        if 'Curso' in university_df.columns:
            course_groups = university_df.groupby('Curso')

            for course_name, course_df in course_groups:
                if course_name and not pd.isna(course_name):
                    # Count students (unique leads)
                    student_count = course_df['Nome do Lead'].nunique() if 'Nome do Lead' in course_df.columns else 0

                    # Count implementations
                    impl_count = 0
                    if 'Fase_Implantacao' in course_df.columns:
                        impl_count = course_df['Fase_Implantacao'].notna().sum()

                    # Calculate revenue
                    course_revenue = 0
                    if 'Valor Mensalidade' in course_df.columns and 'Status_Implantacao' in course_df.columns:
                        finalized_course_df = course_df[course_df['Status_Implantacao'] == 'Finalizado']
                        for _, row in finalized_course_df.iterrows():
                            try:
                                value = float(row['Valor Mensalidade']) if row['Valor Mensalidade'] else 0
                                course_revenue += value
                            except (ValueError, TypeError):
                                pass

                    # Get real class data based on the course
                    classes = []

                    # Group by Turma if it exists, otherwise create sample classes
                    if 'Turma' in course_df.columns:
                        class_groups = course_df.groupby('Turma')

                        for class_name, class_df in class_groups:
                            if class_name and not pd.isna(class_name):
                                # Count students in this class
                                class_students = class_df['Nome do Lead'].nunique() if 'Nome do Lead' in class_df.columns else 0

                                # Determine class status based on implementation status
                                class_status = 'Inativo'
                                if 'Status_Implantacao' in class_df.columns:
                                    if (class_df['Status_Implantacao'] == 'Finalizado').any():
                                        class_status = 'Ativo'
                                    elif (class_df['Status_Implantacao'] == 'Em Andamento').any():
                                        class_status = 'Pendente'

                                # Get period from data or generate it
                                period = class_df['Período'].iloc[0] if 'Período' in class_df.columns and not class_df['Período'].empty else f'{2023 + (len(classes)//2)}.{(len(classes)%2)+1}'

                                # Get product
                                product = class_df['Produto'].iloc[0] if 'Produto' in class_df.columns and not class_df['Produto'].empty else list(products_data.keys())[len(classes) % len(products_data)] if products_data else 'Amigo One Contábil (Mensalidade)'

                                classes.append({
                                    'id': f'class-{course_name}-{class_name}'.replace(' ', '-').lower(),
                                    'name': class_name,
                                    'period': period,
                                    'students': class_students,
                                    'status': class_status,
                                    'product': product,
                                    'implementation_ids': class_df['Implantacao_id'].dropna().tolist() if 'Implantacao_id' in class_df.columns else [],
                                    'opportunity_ids': class_df['Oportunidade_id'].dropna().tolist() if 'Oportunidade_id' in class_df.columns else []
                                })

                    # If no classes were found or Turma column doesn't exist, create sample classes
                    if not classes:
                        # Create 3 sample classes for demonstration
                        for i in range(3):
                            class_students = max(10, student_count // 3)

                            classes.append({
                                'id': f'class-{course_name}-{i+1}'.replace(' ', '-').lower(),
                                'name': f'Turma {i+1}',
                                'period': f'{2023 + (i//2)}.{(i%2)+1}',
                                'students': class_students,
                                'status': 'Ativo' if i == 0 else ('Pendente' if i == 1 else 'Inativo'),
                                'product': 'Amigo One Contábil (Mensalidade)',
                                'implementation_ids': [],
                                'opportunity_ids': []
                            })

                    courses.append({
                        'id': f'course-{course_name}'.replace(' ', '-').lower(),
                        'name': course_name,
                        'students': student_count,
                        'implementations': impl_count,
                        'revenue': format_currency(course_revenue),
                        'classes': classes,
                        'implementation_ids': course_df['Implantacao_id'].dropna().tolist() if 'Implantacao_id' in course_df.columns else [],
                        'opportunity_ids': course_df['Oportunidade_id'].dropna().tolist() if 'Oportunidade_id' in course_df.columns else []
                    })

        return render_template('universities/detail.html',
                              university_name=university,
                              university_city=university_city,
                              university_state=university_state,
                              university_status=university_status,
                              finalized_count=finalized_count,
                              active_count=active_count,
                              opportunity_count=opportunity_count,
                              mrr_total=format_currency(mrr_total),
                              potential_revenue=format_currency(potential_revenue),
                              conversion_rate=conversion_rate,
                              finalized_implementations=finalized_implementations,
                              active_implementations=active_implementations,
                              opportunities=opportunities,
                              status_data=status_data,
                              products_data=products_data,
                              courses=courses)
    except Exception as e:
        logger.error(f"Error in university detail page: {e}")
        flash(f'Error loading university details: {str(e)}', 'error')
        return redirect(url_for('university.index'))

@university_api_bp.route('/university/<university>/details', methods=['GET'])
def university_details_api(university: List[Any]) -> Any:
    """API endpoint to get university details"""
    return get_university_details(university)

@university_bp.route('/api/university/<university>/details', methods=['GET'])
def university_details_legacy(university: List[Any]) -> Any:
    """Legacy API endpoint to get university details (for compatibility)"""
    return get_university_details(university)

def get_university_details(university: List[Any]) -> Optional[Dict[str, Any]]:
    """Common function to get university details"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        df = data_loader.get_data()

        # Check if Universidade column exists
        if 'Universidade' not in df.columns:
            logger.error(f"Column 'Universidade' not found in dataframe for university: {university}")
            return jsonify({
                'error': 'Estrutura de dados inválida: coluna Universidade não encontrada',
                'university': university
            }), 500

        # Filter data for the specified university
        university_df = df[df['Universidade'] == university].copy()

        if university_df.empty:
            logger.warning(f"No data found for university: {university}")
            return jsonify({
                'error': 'Universidade não encontrada ou sem dados disponíveis',
                'university': university,
                'finalized_count': 0,
                'active_count': 0,
                'opportunity_count': 0,
                'finalized_implementations': [],
                'active_implementations': [],
                'opportunities': []
            }), 200  # Return 200 with empty data instead of 404

        # Replace NaN values with None (which will be converted to null in JSON)
        for col in university_df.columns:
            university_df[col] = university_df[col].apply(lambda x: None if pd.isna(x) or (isinstance(x, float) and math.isnan(x)) else x)

        # Get finalized implementations
        finalized_implementations = []
        if 'Status_Implantacao' in university_df.columns:
            finalized_df = university_df[university_df['Status_Implantacao'] == 'Finalizado'].copy()

            for _, row in finalized_df.iterrows():
                # Handle potential NaN values
                lead_name = row.get('Nome do Lead')
                product = row.get('Produto')
                value = row.get('Valor Mensalidade')
                finalized_date = row.get('Data_Finalizacao')

                impl = {
                    'lead_name': 'N/A' if lead_name is None else lead_name,
                    'product': 'N/A' if product is None else product,
                    'value': format_currency(0 if value is None else value),
                    'finalized_date': format_date(finalized_date)
                }
                finalized_implementations.append(impl)

        # Get active implementations
        active_implementations = []
        if 'Status_Implantacao' in university_df.columns:
            active_df = university_df[(university_df['Status_Implantacao'] != 'Finalizado') &
                                     (university_df['Status_Implantacao'] != 'Cancelado') &
                                     (university_df['Status_Implantacao'].notna())].copy()

            # Get status positions for progress calculation
            _, status_positions = BusinessRulesService(data_loader).calculate_implementation_status()

            for _, row in active_df.iterrows():
                # Handle potential NaN values
                lead_name = row.get('Nome do Lead')
                product = row.get('Produto')
                status = row.get('Status_Implantacao')

                # Calculate progress based on status position
                progress = 0
                if status is not None and status in status_positions:
                    position = int(status_positions[status])
                    # Calculate progress (0-100%)
                    progress = min(100, max(0, (position / 10) * 100))

                impl = {
                    'lead_name': 'N/A' if lead_name is None else lead_name,
                    'product': 'N/A' if product is None else product,
                    'status': 'N/A' if status is None else status,
                    'progress': progress
                }
                active_implementations.append(impl)

        # Get opportunities
        opportunities = []
        if 'Etapa do funil Comercial' in university_df.columns:
            opp_df = university_df[(university_df['Etapa do funil Comercial'].notna()) &
                                  ((university_df['Fase_Implantacao'].isna()) |
                                   (university_df['Fase_Implantacao'].isnull()))].copy()

            for _, row in opp_df.iterrows():
                # Handle potential NaN values
                lead_name = row.get('Nome do Lead')
                product = row.get('Produto')
                stage = row.get('Etapa do funil Comercial')
                value = row.get('Valor Mensalidade')

                opp = {
                    'lead_name': 'N/A' if lead_name is None else lead_name,
                    'product': 'N/A' if product is None else product,
                    'stage': 'N/A' if stage is None else stage,
                    'value': format_currency(0 if value is None else value)
                }
                opportunities.append(opp)

        # Prepare response
        response = {
            'university': university,
            'finalized_count': len(finalized_implementations),
            'active_count': len(active_implementations),
            'opportunity_count': len(opportunities),
            'finalized_implementations': finalized_implementations,
            'active_implementations': active_implementations,
            'opportunities': opportunities
        }

        # Ensure no NaN values in the response
        response_json = json.dumps(response, default=lambda x: None if isinstance(x, float) and math.isnan(x) else x)
        return jsonify(json.loads(response_json))
    except Exception as e:
        logger.error(f"Error getting university details: {e}")
        return jsonify({
            'error': str(e),
            'university': university,
            'finalized_count': 0,
            'active_count': 0,
            'opportunity_count': 0,
            'finalized_implementations': [],
            'active_implementations': [],
            'opportunities': []
        }), 200  # Return 200 with error message and empty data
