"""
Amigo DataHub - Business Rules Controller
"""

from flask import Blueprint, render_template
from typing import Dict, List, Any, Optional, Union, Tuple

# Create blueprint
business_rules_bp = Blueprint('business_rules', __name__, url_prefix='/business-rules')

@business_rules_bp.route('/')
def index() -> str:
    """Business rules documentation page"""
    return render_template('business_rules.html')
