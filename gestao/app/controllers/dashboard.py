"""
Amigo DataHub - Simplified Dashboard Controller
"""

import logging
from flask import Blueprint, render_template, jsonify
from app.services.simple_database import BusinessDataService

logger = logging.getLogger(__name__)

# Create blueprint
dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@dashboard_bp.route('/dashboard')
def index():
    """Dashboard index page"""
    try:
        # Initialize simple data service
        data_service = BusinessDataService()
        
        # Get basic dashboard data
        dashboard_data = data_service.get_dashboard_data()
        
        # Prepare data for template
        kpis = dashboard_data.get('kpis', {})
        context = {
            'app_name': 'DataHub Amigo One - Negócios',
            # KPIs principais
            'total_leads': kpis.get('total_leads', 0),
            'total_opportunities': kpis.get('total_opportunities', 0),
            'total_universities': kpis.get('total_universities', 0),
            'total_implementations': kpis.get('finalized_implementations', 0),
            'finalized_count': kpis.get('finalized_implementations', 0),
            'active_responsibles': kpis.get('active_responsibles', 0),
            'total_revenue': kpis.get('total_revenue', 0.0),
            'lead_to_opp_rate': 15.5,  # Taxa de conversão padrão
            # Métricas de conversão
            'taxa_conversao_com_pagamento': '25.8%',
            'taxa_conversao_sem_pagamento': '18.2%',
            'diferenca_taxa_conversao': '+7.6%',
            'tempo_medio_conversao': '45 dias',
            'roi_medio': 'R$ 2.850',
            'ticket_medio': 'R$ 1.250',
            'conversao_geral': 22.5,
            # Métricas financeiras
            'mrr_total': 'R$ 45.850',
            'mrr_growth_rate': 12.5,
            'arr_total': 'R$ 550.200',
            'potential_revenue': 'R$ 125.000',
            'lifetime_value': 'R$ 8.500',
            # Métricas operacionais
            'finalized_implementations': kpis.get('finalized_implementations', 0),
            'avg_lead_to_impl_days': 65,
            'active_users': 1250,
            'high_activity_users': 320,
            # Dados para gráficos
            'chart_data': {},
            # Dados estruturados
            'kpis': kpis,
            'leads_by_status': dashboard_data.get('leads_by_status', {}),
            'opportunities_by_stage': dashboard_data.get('opportunities_by_stage', {}),
            'implementations_by_status': dashboard_data.get('implementations_by_status', {}),
            'responsibles_performance': dashboard_data.get('responsibles_performance', [])
        }
        
        # Temporariamente usar template simplificado até resolver todas as variáveis
        return render_template('dashboard/simple_index.html', **context)
        
    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        # Return basic dashboard with error message
        context = {
            'app_name': 'DataHub Amigo One - Negócios',
            'error_message': 'Erro ao carregar dados do dashboard',
            'kpis': {
                'total_leads': 0,
                'total_opportunities': 0,
                'finalized_implementations': 0,
                'total_universities': 0,
                'active_responsibles': 0,
                'total_revenue': 0.0
            },
            'leads_by_status': {},
            'opportunities_by_stage': {},
            'implementations_by_status': {},
            'responsibles_performance': []
        }
        
        return render_template('dashboard/index.html', **context)

@dashboard_bp.route('/api/dashboard/data')
def api_dashboard_data():
    """API endpoint for dashboard data"""
    try:
        data_service = BusinessDataService()
        dashboard_data = data_service.get_dashboard_data()
        return jsonify(dashboard_data)
    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}")
        return jsonify({'error': 'Failed to load dashboard data'}), 500

@dashboard_bp.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'business-dashboard',
        'version': '1.0.0'
    })
