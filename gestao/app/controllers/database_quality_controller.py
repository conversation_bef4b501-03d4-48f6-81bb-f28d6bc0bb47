"""
Amigo DataHub - Database Quality Controller
"""

import pandas as pd
import numpy as np
import logging
from flask import Blueprint, render_template, flash
from app.services.data_loader import DataLoader
from typing import Dict, List, Any, Optional, Union, Tuple
import pandas as pd

logger = logging.getLogger(__name__)

# Create blueprint
database_quality_bp = Blueprint('database_quality', __name__, url_prefix='/database-quality')

def define_business_rules() -> Dict[str, Any]:
    """
    Define business rules for data quality analysis

    Returns:
        dict: Business rules for data quality analysis
    """
    return {
        # Required fields by entity
        'required_fields': {
            'Lead': ['Nome_Lead', 'Email_Lead', 'Telefone_Lead', 'Universidade', 'Curso'],
            'Oportunidade': ['ID_Lead', 'Produto', 'Valor', 'Nome_Responsavel'],
            'Implantacao': ['ID_Oportunidade', 'Status_Implantacao', 'ResponsableOnboarding', 'Valor_Mensalidade'],
            'Universidade': ['Nome_Universidade', 'Cidade', 'Estado'],
            'Turma': ['Universidade', 'Curso', 'Periodo', 'Quantidade_Alunos'],
            'Responsavel': ['Nome_Responsavel', 'Funcao']
        },

        # Optional fields by entity
        'optional_fields': {
            'Lead': ['Data_Criacao_Lead', 'Origem_Lead', 'Observacoes'],
            'Oportunidade': ['Data_Criacao_Oportunidade', 'Etapa_Funil', 'Observacoes'],
            'Implantacao': ['Data_Criacao_Implantacao', 'Fase_Implantacao', 'ResponsableOngoing', 'ResponsableContabil', 'Data_Finalizacao'],
            'Universidade': ['Telefone', 'Email', 'Site'],
            'Turma': ['Data_Inicio', 'Data_Fim'],
            'Responsavel': ['Email', 'Telefone', 'Departamento']
        },

        # Fields with specific validation rules
        'validation_rules': {
            'Email_Lead': {'type': 'email', 'required': True},
            'Telefone_Lead': {'type': 'phone', 'required': True},
            'Valor': {'type': 'numeric', 'min': 0, 'required': True},
            'Valor_Mensalidade': {'type': 'numeric', 'min': 0, 'required': True},
            'Data_Criacao_Lead': {'type': 'date', 'required': False},
            'Data_Criacao_Oportunidade': {'type': 'date', 'required': False},
            'Data_Criacao_Implantacao': {'type': 'date', 'required': False},
            'Data_Finalizacao': {'type': 'date', 'required': False}
        }
    }

@database_quality_bp.route('/')
def index() -> str:
    """Database quality analysis page"""
    try:
        # Initialize data loader
        data_loader = DataLoader()
        data_loader.load_data()

        # Get data
        df = data_loader.get_data()

        # Define business rules for required fields
        business_rules = define_business_rules()

        # Analyze data quality
        quality_metrics = analyze_data_quality(df)

        # Analyze missing data
        missing_data = analyze_missing_data(df, business_rules)

        # Analyze data consistency
        consistency_issues = analyze_data_consistency(df)

        # Analyze data completeness by column
        completeness_by_column = analyze_completeness_by_column(df, business_rules)

        # Analyze data completeness by entity
        completeness_by_entity = analyze_completeness_by_entity(df, business_rules)

        # Get data statistics
        data_statistics = get_data_statistics(df)

        # Get data distribution
        data_distribution = get_data_distribution(df)

        # Analyze data quality by responsible
        responsible_quality = analyze_responsible_quality(df, business_rules)

        return render_template('database_quality/index.html',
                              quality_metrics=quality_metrics,
                              missing_data=missing_data,
                              consistency_issues=consistency_issues,
                              completeness_by_column=completeness_by_column,
                              completeness_by_entity=completeness_by_entity,
                              data_statistics=data_statistics,
                              data_distribution=data_distribution,
                              responsible_quality=responsible_quality,
                              business_rules=business_rules)
    except Exception as e:
        logger.error(f"Error in database quality analysis: {e}")
        flash(f'Error analyzing database quality: {str(e)}', 'error')
        return render_template('database_quality/index.html',
                              quality_metrics={},
                              missing_data={},
                              consistency_issues={},
                              completeness_by_column={},
                              completeness_by_entity={},
                              data_statistics={},
                              data_distribution={})

def analyze_data_quality(df: Dict[str, Any]) -> Dict[str, Any]:
    """
    # Check data_quality permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission
    
    if not check_permission('data_quality.view'):
        flash('Acesso negado. Permissão insuficiente para acessar esta página.', 'error')
        return redirect(url_for('auth.login'))

    Analyze overall data quality

    Args:
        df (DataFrame): The data to analyze

    Returns:
        dict: Quality metrics
    """
    try:
        total_rows = len(df)
        total_columns = len(df.columns)
        total_cells = total_rows * total_columns

        # Count missing values
        missing_values = df.isna().sum().sum()
        missing_percentage = (missing_values / total_cells) * 100 if total_cells > 0 else 0

        # Count duplicate rows
        duplicate_rows = df.duplicated().sum()
        duplicate_percentage = (duplicate_rows / total_rows) * 100 if total_rows > 0 else 0

        # Calculate overall quality score (100% - missing% - duplicate%)
        quality_score = max(0, 100 - missing_percentage - duplicate_percentage)

        # Determine quality level
        if quality_score >= 90:
            quality_level = "Excelente"
        elif quality_score >= 80:
            quality_level = "Bom"
        elif quality_score >= 70:
            quality_level = "Razoável"
        elif quality_score >= 60:
            quality_level = "Precisa de Atenção"
        else:
            quality_level = "Crítico"

        return {
            'total_rows': total_rows,
            'total_columns': total_columns,
            'total_cells': total_cells,
            'missing_values': int(missing_values),
            'missing_percentage': round(missing_percentage, 2),
            'duplicate_rows': int(duplicate_rows),
            'duplicate_percentage': round(duplicate_percentage, 2),
            'quality_score': round(quality_score, 2),
            'quality_level': quality_level
        }
    except Exception as e:
        logger.error(f"Error analyzing data quality: {e}")
        return {}

def analyze_missing_data(df: Dict[str, Any], business_rules: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Analyze missing data by column

    Args:
        df (DataFrame): The data to analyze
        business_rules (dict, optional): Business rules for data quality analysis

    Returns:
        dict: Missing data analysis
    """
    try:
        # Calculate missing values by column
        missing_by_column = df.isna().sum().to_dict()

        # Calculate missing percentage by column
        total_rows = len(df)
        missing_percentage_by_column = {col: (count / total_rows) * 100 for col, count in missing_by_column.items()}

        # Get all required fields if business rules are provided
        required_fields = []
        if business_rules:
            for entity_fields in business_rules['required_fields'].values():
                required_fields.extend(entity_fields)

        # Sort columns by missing percentage (descending)
        sorted_columns = sorted(missing_percentage_by_column.items(), key=lambda x: x[1], reverse=True)

        # Get top 10 columns with most missing data
        top_missing_columns = {}
        for col, pct in sorted_columns[:10]:
            if pct > 0:
                is_required = col in required_fields if business_rules else False
                top_missing_columns[col] = {
                    'count': missing_by_column[col],
                    'percentage': round(pct, 2),
                    'required': is_required
                }

        # Count critical columns (considering if they are required)
        critical_columns = 0
        for col, pct in missing_percentage_by_column.items():
            is_required = col in required_fields if business_rules else False
            if (is_required and pct > 20) or pct > 50:
                critical_columns += 1

        return {
            'top_missing_columns': top_missing_columns,
            'total_columns_with_missing': sum(1 for pct in missing_percentage_by_column.values() if pct > 0),
            'critical_columns': critical_columns,
            'required_fields_with_missing': sum(1 for col in required_fields if col in df.columns and missing_percentage_by_column.get(col, 0) > 0) if business_rules else 0
        }
    except Exception as e:
        logger.error(f"Error analyzing missing data: {e}")
        return {}

def analyze_data_consistency(df: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze data consistency issues

    Args:
        df (DataFrame): The data to analyze

    Returns:
        dict: Consistency issues
    """
    try:
        consistency_issues = {}

        # Check for date consistency issues
        date_columns = [col for col in df.columns if 'data' in col.lower() or 'date' in col.lower()]
        for col in date_columns:
            if col in df.columns:
                # Check if column has mixed types
                if df[col].dtype == 'object' and not pd.api.types.is_datetime64_any_dtype(df[col]):
                    try:
                        # Try to convert to datetime
                        pd.to_datetime(df[col], errors='raise')
                    except:
                        consistency_issues[col] = "Formato de data inconsistente"

        # Check for numeric consistency issues
        numeric_columns = [col for col in df.columns if 'valor' in col.lower() or 'price' in col.lower() or 'amount' in col.lower()]
        for col in numeric_columns:
            if col in df.columns:
                # Check if column has mixed types
                if df[col].dtype == 'object':
                    try:
                        # Try to convert to numeric
                        pd.to_numeric(df[col], errors='raise')
                    except:
                        consistency_issues[col] = "Formato numérico inconsistente"

        # Check for status consistency
        status_columns = [col for col in df.columns if 'status' in col.lower() or 'fase' in col.lower() or 'etapa' in col.lower()]
        for col in status_columns:
            if col in df.columns and df[col].dtype == 'object':
                # Check for inconsistent capitalization or extra spaces
                values = df[col].dropna().unique()
                normalized_values = [str(v).strip().lower() for v in values]
                if len(set(normalized_values)) < len(values):
                    consistency_issues[col] = "Valores de status inconsistentes (capitalização ou espaços)"

        return consistency_issues
    except Exception as e:
        logger.error(f"Error analyzing data consistency: {e}")
        return {}

def analyze_completeness_by_column(df: Dict[str, Any], business_rules: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Analyze data completeness by column

    Args:
        df (DataFrame): The data to analyze
        business_rules (dict, optional): Business rules for data quality analysis

    Returns:
        dict: Completeness by column
    """
    try:
        total_rows = len(df)
        completeness = {}

        # Get all required and optional fields if business rules are provided
        required_fields = []
        optional_fields = []
        if business_rules:
            for entity_fields in business_rules['required_fields'].values():
                required_fields.extend(entity_fields)
            for entity_fields in business_rules['optional_fields'].values():
                optional_fields.extend(entity_fields)

        for col in df.columns:
            non_null_count = df[col].count()
            completeness_pct = (non_null_count / total_rows) * 100 if total_rows > 0 else 0

            # Check if field is required or optional
            is_required = col in required_fields if business_rules else False
            is_optional = col in optional_fields if business_rules else False
            field_status = "Obrigatório" if is_required else "Opcional" if is_optional else "Não Classificado"

            # Determine completeness level (stricter for required fields)
            if is_required:
                if completeness_pct >= 98:
                    level = "Excelente"
                elif completeness_pct >= 90:
                    level = "Bom"
                elif completeness_pct >= 80:
                    level = "Razoável"
                elif completeness_pct >= 70:
                    level = "Precisa de Atenção"
                else:
                    level = "Crítico"
            else:
                if completeness_pct >= 95:
                    level = "Excelente"
                elif completeness_pct >= 85:
                    level = "Bom"
                elif completeness_pct >= 70:
                    level = "Razoável"
                elif completeness_pct >= 50:
                    level = "Precisa de Atenção"
                else:
                    level = "Crítico"

            completeness[col] = {
                'percentage': round(completeness_pct, 2),
                'level': level,
                'status': field_status,
                'required': is_required
            }

        # Sort by completeness percentage (ascending)
        sorted_completeness = {k: v for k, v in sorted(completeness.items(), key=lambda item: item[1]['percentage'])}

        return sorted_completeness
    except Exception as e:
        logger.error(f"Error analyzing completeness by column: {e}")
        return {}

def analyze_completeness_by_entity(df: Dict[str, Any], business_rules: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Analyze data completeness by entity (Lead, Opportunity, Implementation)

    Args:
        df (DataFrame): The data to analyze
        business_rules (dict, optional): Business rules for data quality analysis

    Returns:
        dict: Completeness by entity
    """
    try:
        # Define entity columns based on business rules if provided
        if business_rules:
            entity_columns = {
                entity: business_rules['required_fields'].get(entity, []) + business_rules['optional_fields'].get(entity, [])
                for entity in business_rules['required_fields'].keys()
            }
        else:
            # Fallback to column name pattern matching
            entity_columns = {
                'Lead': [col for col in df.columns if 'lead' in col.lower()],
                'Oportunidade': [col for col in df.columns if 'oportunidade' in col.lower() or 'opportunity' in col.lower()],
                'Implantação': [col for col in df.columns if 'implantacao' in col.lower() or 'implementation' in col.lower()],
                'Universidade': [col for col in df.columns if 'universidade' in col.lower() or 'university' in col.lower()],
                'Responsável': [col for col in df.columns if 'responsavel' in col.lower() or 'responsible' in col.lower() or 'responsable' in col.lower()]
            }

        completeness_by_entity = {}

        for entity, columns in entity_columns.items():
            # Filter to only include columns that exist in the dataframe
            existing_columns = [col for col in columns if col in df.columns]

            if existing_columns:
                # Get required fields for this entity
                required_columns = business_rules['required_fields'].get(entity, []) if business_rules else []
                required_existing = [col for col in required_columns if col in df.columns]

                # Calculate average completeness for all entity columns
                entity_df = df[existing_columns].copy()
                completeness_pct = (entity_df.count().sum() / (len(entity_df) * len(existing_columns))) * 100

                # Calculate average completeness for required columns only
                required_completeness_pct = 0
                if required_existing:
                    required_df = df[required_existing].copy()
                    required_completeness_pct = (required_df.count().sum() / (len(required_df) * len(required_existing))) * 100

                # Determine completeness level (weighted more by required fields)
                if required_completeness_pct > 0:
                    # Weight required fields more heavily (70% required, 30% all)
                    weighted_pct = (required_completeness_pct * 0.7) + (completeness_pct * 0.3)
                else:
                    weighted_pct = completeness_pct

                if weighted_pct >= 95:
                    level = "Excelente"
                elif weighted_pct >= 85:
                    level = "Bom"
                elif weighted_pct >= 70:
                    level = "Razoável"
                elif weighted_pct >= 50:
                    level = "Precisa de Atenção"
                else:
                    level = "Crítico"

                completeness_by_entity[entity] = {
                    'percentage': round(weighted_pct, 2),
                    'all_fields_percentage': round(completeness_pct, 2),
                    'required_fields_percentage': round(required_completeness_pct, 2) if required_existing else 0,
                    'level': level,
                    'columns_count': len(existing_columns),
                    'required_columns_count': len(required_existing)
                }

        return completeness_by_entity
    except Exception as e:
        logger.error(f"Error analyzing completeness by entity: {e}")
        return {}

def get_data_statistics(df: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Get basic statistics for numeric columns

    Args:
        df (DataFrame): The data to analyze

    Returns:
        dict: Data statistics
    """
    try:
        # Select numeric columns
        numeric_df = df.select_dtypes(include=['number'])

        if not numeric_df.empty:
            # Calculate statistics
            stats = numeric_df.describe().to_dict()

            # Format statistics
            formatted_stats = {}
            for col, col_stats in stats.items():
                formatted_stats[col] = {stat: round(value, 2) if isinstance(value, (int, float)) else value
                                       for stat, value in col_stats.items()}

            return formatted_stats
        else:
            return {}
    except Exception as e:
        logger.error(f"Error getting data statistics: {e}")
        return {}

def get_data_distribution(df: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Get data distribution for categorical columns

    Args:
        df (DataFrame): The data to analyze

    Returns:
        dict: Data distribution
    """
    try:
        # Select categorical columns
        categorical_df = df.select_dtypes(include=['object'])

        distribution = {}

        for col in categorical_df.columns:
            # Get value counts
            value_counts = categorical_df[col].value_counts().head(10).to_dict()

            # Calculate percentages
            total = categorical_df[col].count()
            percentages = {value: (count / total) * 100 for value, count in value_counts.items()}

            distribution[col] = {
                'counts': value_counts,
                'percentages': {value: round(pct, 2) for value, pct in percentages.items()}
            }

        return distribution
    except Exception as e:
        logger.error(f"Error getting data distribution: {e}")
        return {}

def analyze_responsible_quality(df: Dict[str, Any], business_rules: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Analyze data quality by responsible

    Args:
        df (DataFrame): The data to analyze
        business_rules (dict, optional): Business rules for data quality analysis

    Returns:
        dict: Data quality by responsible
    """
    try:
        # Check if responsible column exists
        if 'Nome_Responsavel' not in df.columns:
            return {}

        # Get all responsibles
        responsibles = df['Nome_Responsavel'].dropna().unique()

        # Define fields to analyze by implementation status
        implementation_fields = {
            'all': ['ID_Oportunidade', 'Status_Implantacao', 'Fase_Implantacao', 'ResponsableOnboarding',
                   'ResponsableOngoing', 'ResponsableContabil', 'Valor_Mensalidade', 'Data_Criacao_Implantacao'],
            'finalized': ['Data_Finalizacao', 'Valor_Mensalidade', 'ResponsableOngoing', 'ResponsableContabil']
        }

        # Get required fields if business rules are provided
        required_fields = []
        if business_rules and 'required_fields' in business_rules:
            if 'Implantacao' in business_rules['required_fields']:
                required_fields = business_rules['required_fields']['Implantacao']

        # Initialize results
        results = {}

        for responsible in responsibles:
            # Filter data for this responsible
            responsible_df = df[df['Nome_Responsavel'] == responsible]

            # Count implementations
            total_implementations = len(responsible_df)
            if total_implementations == 0:
                continue

            # Count finalized implementations
            finalized_implementations = len(responsible_df[responsible_df['Status_Implantacao'] == 'Finalizado'])

            # Calculate completeness for all implementations
            all_fields_completeness = {}
            for field in implementation_fields['all']:
                if field in responsible_df.columns:
                    non_null_count = responsible_df[field].count()
                    completeness_pct = (non_null_count / total_implementations) * 100
                    is_required = field in required_fields if required_fields else False

                    all_fields_completeness[field] = {
                        'percentage': round(completeness_pct, 2),
                        'required': is_required
                    }

            # Calculate completeness for finalized implementations
            finalized_fields_completeness = {}
            if finalized_implementations > 0:
                finalized_df = responsible_df[responsible_df['Status_Implantacao'] == 'Finalizado']
                for field in implementation_fields['finalized']:
                    if field in finalized_df.columns:
                        non_null_count = finalized_df[field].count()
                        completeness_pct = (non_null_count / finalized_implementations) * 100
                        is_required = field in required_fields if required_fields else False

                        finalized_fields_completeness[field] = {
                            'percentage': round(completeness_pct, 2),
                            'required': is_required
                        }

            # Calculate overall completeness score
            all_scores = [data['percentage'] for data in all_fields_completeness.values()]
            finalized_scores = [data['percentage'] for data in finalized_fields_completeness.values()]

            overall_score = 0
            if all_scores:
                # Weight required fields more heavily
                required_scores = [data['percentage'] for field, data in all_fields_completeness.items()
                                  if data['required']]

                if required_scores:
                    # 70% required, 30% all
                    overall_score = (sum(required_scores) / len(required_scores) * 0.7) + \
                                   (sum(all_scores) / len(all_scores) * 0.3)
                else:
                    overall_score = sum(all_scores) / len(all_scores)

            # Determine quality level
            if overall_score >= 95:
                level = "Excelente"
            elif overall_score >= 85:
                level = "Bom"
            elif overall_score >= 70:
                level = "Razoável"
            elif overall_score >= 50:
                level = "Precisa de Atenção"
            else:
                level = "Crítico"

            # Find fields with lowest completeness
            problem_fields = []
            for field, data in all_fields_completeness.items():
                threshold = 90 if data['required'] else 70
                if data['percentage'] < threshold:
                    problem_fields.append({
                        'field': field,
                        'percentage': data['percentage'],
                        'required': data['required']
                    })

            # Sort problem fields by percentage (ascending)
            problem_fields = sorted(problem_fields, key=lambda x: x['percentage'])

            # Store results
            results[responsible] = {
                'total_implementations': total_implementations,
                'finalized_implementations': finalized_implementations,
                'all_fields_completeness': all_fields_completeness,
                'finalized_fields_completeness': finalized_fields_completeness,
                'overall_score': round(overall_score, 2),
                'level': level,
                'problem_fields': problem_fields[:5]  # Top 5 problem fields
            }

        return results
    except Exception as e:
        logger.error(f"Error analyzing responsible data quality: {e}")
        return {}
