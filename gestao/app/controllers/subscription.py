"""
Amigo DataHub - Subscription Controller
"""

import json
import logging
from flask import Blueprint, render_template, flash
from app.services.data_loader import DataLoader
from app.services.business_rules import BusinessRulesService
from app.services.analytics import AnalyticsService
from app.utils.formatters import format_currency, prepare_chart_data
from typing import Dict, List, Any, Optional, Union, Tuple

logger = logging.getLogger(__name__)

# Create blueprint
subscription_bp = Blueprint('subscription', __name__, url_prefix='/subscriptions')

@subscription_bp.route('/')
def index() -> str:
    """Subscription index page"""
    # Check subscriptions permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission

    if not check_permission('subscriptions.view'):
        flash('Acesso negado. Permissão insuficiente para acessar esta página.', 'error')
        return redirect(url_for('auth.login'))

    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        business_rules = BusinessRulesService(data_loader)
        analytics = AnalyticsService(data_loader, business_rules)

        # Calculate MRR
        mrr_total, finalized_count, responsaveis_mrr, produtos_mrr = business_rules.calculate_mrr()

        # Calculate average ticket
        ticket_medio = business_rules.calculate_average_ticket(mrr_total, finalized_count)

        # Format values for display
        mrr_formatado = format_currency(mrr_total)
        ticket_medio_formatado = format_currency(ticket_medio)

        # Calculate MRR forecast
        mrr_forecast = business_rules.calculate_mrr_forecast()

        # Format forecast values
        mrr_forecast_formatted = {k: format_currency(v) for k, v in mrr_forecast.items()}

        # Get MRR by responsible
        mrr_by_responsible = analytics.get_mrr_by_responsible()

        # Get MRR by product
        mrr_by_product = analytics.get_mrr_by_product()

        # Get MRR by university
        mrr_by_university, mrr_percentage_by_university, _ = analytics.get_mrr_by_university()

        # Prepare chart data
        chart_data = {
            'mrr_forecast': mrr_forecast,
            'mrr_by_responsible': mrr_by_responsible,
            'mrr_by_product': mrr_by_product,
            'mrr_by_university': mrr_by_university
        }

        # Ensure all values are strings
        chart_data = prepare_chart_data(chart_data)

        return render_template('subscriptions/index.html',
                              mrr_total=mrr_formatado,
                              finalized_count=finalized_count,
                              ticket_medio=ticket_medio_formatado,
                              mrr_forecast=mrr_forecast_formatted,
                              mrr_by_responsible=mrr_by_responsible,
                              mrr_by_product=mrr_by_product,
                              mrr_by_university=mrr_by_university,
                              mrr_percentage_by_university=mrr_percentage_by_university,
                              chart_data=json.dumps(chart_data))
    except Exception as e:
        logger.error(f"Error in subscription index: {e}")
        flash(f'Error loading subscription data: {str(e)}', 'error')

        # Initialize empty dictionaries for all data to avoid tuple errors
        mrr_by_university_empty = {}
        mrr_percentage_by_university_empty = {}

        return render_template('subscriptions/index.html',
                              mrr_total="R$ 0,00",
                              finalized_count=0,
                              ticket_medio="R$ 0,00",
                              mrr_forecast={},
                              mrr_by_responsible={},
                              mrr_by_product={},
                              mrr_by_university=mrr_by_university_empty,
                              mrr_percentage_by_university=mrr_percentage_by_university_empty,
                              chart_data=json.dumps({}))
