"""
Amigo DataHub - Responsible Controller
"""

import logging
import pandas as pd
import numpy as np
from flask import Blueprint, render_template, flash, jsonify, request, redirect, url_for
from app.services.data_loader import DataLoader
from app.controllers.google_auth import require_auth
from app.services.business_rules import BusinessRulesService
from app.services.analytics import AnalyticsService
from app.utils.formatters import format_currency, prepare_chart_data, format_date
from typing import Dict, List, Any, Optional, Union, Tuple
import pandas as pd
from flask import Response

logger = logging.getLogger(__name__)

# Create blueprint
responsible_bp = Blueprint('responsible', __name__, url_prefix='/responsibles')

@responsible_bp.route('/')
@require_auth
def index() -> str:
    """Responsible index page"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        business_rules = BusinessRulesService(data_loader)
        analytics = AnalyticsService(data_loader, business_rules)

        df = data_loader.get_data()

        # Check for filter parameters
        class_filter = request.args.get('class')
        university_filter = request.args.get('university')

        # Apply filters if provided
        filtered_df = df.copy()
        if class_filter:
            class_name = class_filter.replace('-', ' ')
            # Usar uma comparação segura para evitar ambiguidade
            mask = filtered_df['Turma'].fillna('').astype(str).str.lower() == class_name.lower()
            filtered_df = filtered_df[mask]

        if university_filter:
            # Usar uma comparação segura para evitar ambiguidade
            mask = filtered_df['Universidade'].fillna('').astype(str).str.lower() == university_filter.lower()
            filtered_df = filtered_df[mask]

        # Prepare data for responsibles
        responsibles_data = {}

        # Sales responsibles (Nome_Responsavel)
        if 'Nome_Responsavel' in filtered_df.columns:
            sales_responsibles = filtered_df[pd.notna(filtered_df['Nome_Responsavel'])]['Nome_Responsavel'].unique()

            for responsible in sales_responsibles:
                if responsible not in responsibles_data:
                    responsibles_data[responsible] = {
                        'id': responsible.lower().replace(' ', '-'),
                        'name': responsible,
                        'role': 'Vendas',
                        'opportunities': 0,
                        'implementations': 0,
                        'finalized': 0,
                        'active': 0,
                        'revenue': 0,
                        'mrr': 0,
                        'universities': set(),
                        'courses': set(),
                        'classes': set()
                    }

                resp_df = filtered_df[filtered_df['Nome_Responsavel'] == responsible]

                # Count opportunities
                if 'Oportunidade_id' in resp_df.columns:
                    responsibles_data[responsible]['opportunities'] = resp_df['Oportunidade_id'].nunique()

                # Count implementations
                if 'Status_Implantacao' in resp_df.columns:
                    impl_df = resp_df[pd.notna(resp_df['Status_Implantacao'])]
                    responsibles_data[responsible]['implementations'] = impl_df['Oportunidade_id'].nunique()

                    # Count finalized implementations
                    finalized_df = impl_df[impl_df['Status_Implantacao'] == 'Finalizado']
                    responsibles_data[responsible]['finalized'] = finalized_df['Oportunidade_id'].nunique()

                    # Count active implementations
                    active_df = impl_df[(impl_df['Status_Implantacao'] != 'Finalizado') &
                                       (impl_df['Status_Implantacao'] != 'Cancelado')]
                    responsibles_data[responsible]['active'] = active_df['Oportunidade_id'].nunique()

                    # Calculate revenue and MRR
                    if 'Valor Mensalidade' in impl_df.columns:
                        # Log para depuração
                        logger.info(f"Calculando receita e MRR para {responsible} (Vendas)")
                        logger.info(f"Total de registros: {len(impl_df)}")

                        # Para receita, considerar vendas finalizadas
                        finalized_status = impl_df['Status_Implantacao'].fillna('').astype(str).str.lower() == 'finalizado'
                        finalized_df = impl_df[finalized_status]
                        logger.info(f"Registros finalizados: {len(finalized_df)}")

                        # Deduplicate by Oportunidade_id to avoid counting the same opportunity multiple times
                        unique_finalized_df = finalized_df.drop_duplicates(subset=['Oportunidade_id']) if 'Oportunidade_id' in finalized_df.columns else finalized_df
                        logger.info(f"Registros finalizados após deduplicação: {len(unique_finalized_df)}")

                        # Para MRR, considerar APENAS vendas finalizadas (conforme solicitado)
                        # Usando a mesma lógica da receita
                        mrr_df = unique_finalized_df.copy()
                        logger.info(f"Registros para MRR (apenas finalizados): {len(mrr_df)}")

                        # Calcular receita (vendas finalizadas)
                        revenue_total = 0
                        for _, row in unique_finalized_df.iterrows():
                            try:
                                if pd.notna(row.get('Valor Mensalidade')):
                                    value_str = str(row['Valor Mensalidade']).replace('R$', '').replace('.', '').replace(',', '.').strip()
                                    if value_str and not value_str.isspace():
                                        value = float(value_str)
                                        responsibles_data[responsible]['revenue'] += value
                                        revenue_total += value
                                        logger.info(f"Adicionando valor {value} à receita de {responsible}")
                            except (ValueError, TypeError) as e:
                                logger.warning(f"Error converting value '{row.get('Valor Mensalidade', 'N/A')}' to float: {e}")

                        logger.info(f"Receita total para {responsible}: {revenue_total}")

                        # Calcular MRR (apenas vendas finalizadas)
                        mrr_total = 0
                        for _, row in mrr_df.iterrows():
                            try:
                                if pd.notna(row.get('Valor Mensalidade')):
                                    value_str = str(row['Valor Mensalidade']).replace('R$', '').replace('.', '').replace(',', '.').strip()
                                    if value_str and not value_str.isspace():
                                        value = float(value_str)
                                        responsibles_data[responsible]['mrr'] += value
                                        mrr_total += value
                                        logger.info(f"Adicionando valor {value} ao MRR de {responsible}")
                            except (ValueError, TypeError) as e:
                                logger.warning(f"Error converting value '{row.get('Valor Mensalidade', 'N/A')}' to float: {e}")

                        logger.info(f"MRR total para {responsible}: {mrr_total}")

                # Collect universities, courses, and classes
                if 'Universidade' in resp_df.columns:
                    for univ in resp_df[pd.notna(resp_df['Universidade'])]['Universidade'].unique():
                        responsibles_data[responsible]['universities'].add(univ)

                if 'Curso' in resp_df.columns:
                    for course in resp_df[pd.notna(resp_df['Curso'])]['Curso'].unique():
                        responsibles_data[responsible]['courses'].add(course)

                if 'Turma' in resp_df.columns:
                    for class_name in resp_df[pd.notna(resp_df['Turma'])]['Turma'].unique():
                        responsibles_data[responsible]['classes'].add(class_name)

        # Onboarding responsibles (ResponsableOnboarding)
        if 'ResponsableOnboarding' in filtered_df.columns:
            onboarding_responsibles = filtered_df[pd.notna(filtered_df['ResponsableOnboarding'])]['ResponsableOnboarding'].unique()

            for responsible in onboarding_responsibles:
                if responsible not in responsibles_data:
                    responsibles_data[responsible] = {
                        'id': responsible.lower().replace(' ', '-'),
                        'name': responsible,
                        'role': 'Onboarding',
                        'opportunities': 0,
                        'implementations': 0,
                        'finalized': 0,
                        'active': 0,
                        'revenue': 0,
                        'mrr': 0,
                        'universities': set(),
                        'courses': set(),
                        'classes': set()
                    }
                elif responsibles_data[responsible]['role'] == 'Vendas':
                    responsibles_data[responsible]['role'] = 'Vendas/Onboarding'

                resp_df = filtered_df[filtered_df['ResponsableOnboarding'] == responsible]

                # Count implementations
                if 'Status_Implantacao' in resp_df.columns:
                    impl_df = resp_df[pd.notna(resp_df['Status_Implantacao'])]
                    responsibles_data[responsible]['implementations'] = impl_df['Oportunidade_id'].nunique()

                    # Count finalized implementations
                    finalized_df = impl_df[impl_df['Status_Implantacao'] == 'Finalizado']
                    responsibles_data[responsible]['finalized'] = finalized_df['Oportunidade_id'].nunique()

                    # Count active implementations
                    active_df = impl_df[(impl_df['Status_Implantacao'] != 'Finalizado') &
                                       (impl_df['Status_Implantacao'] != 'Cancelado')]
                    responsibles_data[responsible]['active'] = active_df['Oportunidade_id'].nunique()

                    # Calculate revenue and MRR
                    if 'Valor Mensalidade' in impl_df.columns:
                        # Log para depuração
                        logger.info(f"Calculando receita e MRR para {responsible} (Onboarding)")
                        logger.info(f"Total de registros: {len(impl_df)}")

                        # Para receita, considerar vendas finalizadas
                        finalized_status = impl_df['Status_Implantacao'].fillna('').astype(str).str.lower() == 'finalizado'
                        finalized_df = impl_df[finalized_status]
                        logger.info(f"Registros finalizados: {len(finalized_df)}")

                        # Deduplicate by Oportunidade_id to avoid counting the same opportunity multiple times
                        unique_finalized_df = finalized_df.drop_duplicates(subset=['Oportunidade_id']) if 'Oportunidade_id' in finalized_df.columns else finalized_df
                        logger.info(f"Registros finalizados após deduplicação: {len(unique_finalized_df)}")

                        # Para MRR, considerar APENAS vendas finalizadas (conforme solicitado)
                        # Usando a mesma lógica da receita
                        mrr_df = unique_finalized_df.copy()
                        logger.info(f"Registros para MRR (apenas finalizados): {len(mrr_df)}")

                        # Calcular receita (vendas finalizadas)
                        revenue_total = 0
                        for _, row in unique_finalized_df.iterrows():
                            try:
                                if pd.notna(row.get('Valor Mensalidade')):
                                    value_str = str(row['Valor Mensalidade']).replace('R$', '').replace('.', '').replace(',', '.').strip()
                                    if value_str and not value_str.isspace():
                                        value = float(value_str)
                                        responsibles_data[responsible]['revenue'] += value
                                        revenue_total += value
                                        logger.info(f"Adicionando valor {value} à receita de {responsible}")
                            except (ValueError, TypeError) as e:
                                logger.warning(f"Error converting value '{row.get('Valor Mensalidade', 'N/A')}' to float: {e}")

                        logger.info(f"Receita total para {responsible}: {revenue_total}")

                        # Calcular MRR (apenas vendas finalizadas)
                        mrr_total = 0
                        for _, row in mrr_df.iterrows():
                            try:
                                if pd.notna(row.get('Valor Mensalidade')):
                                    value_str = str(row['Valor Mensalidade']).replace('R$', '').replace('.', '').replace(',', '.').strip()
                                    if value_str and not value_str.isspace():
                                        value = float(value_str)
                                        responsibles_data[responsible]['mrr'] += value
                                        mrr_total += value
                                        logger.info(f"Adicionando valor {value} ao MRR de {responsible}")
                            except (ValueError, TypeError) as e:
                                logger.warning(f"Error converting value '{row.get('Valor Mensalidade', 'N/A')}' to float: {e}")

                        logger.info(f"MRR total para {responsible}: {mrr_total}")

                # Collect universities, courses, and classes
                if 'Universidade' in resp_df.columns:
                    for univ in resp_df[pd.notna(resp_df['Universidade'])]['Universidade'].unique():
                        responsibles_data[responsible]['universities'].add(univ)

                if 'Curso' in resp_df.columns:
                    for course in resp_df[pd.notna(resp_df['Curso'])]['Curso'].unique():
                        responsibles_data[responsible]['courses'].add(course)

                if 'Turma' in resp_df.columns:
                    for class_name in resp_df[pd.notna(resp_df['Turma'])]['Turma'].unique():
                        responsibles_data[responsible]['classes'].add(class_name)

        # Process other responsible types (ResponsableOngoing, ResponsableContabil, ResponsableSocietário)
        other_responsible_columns = ['ResponsableOngoing', 'ResponsableContabil', 'ResponsableSocietário']
        role_mapping = {
            'ResponsableOngoing': 'Ongoing',
            'ResponsableContabil': 'Contábil',
            'ResponsableSocietário': 'Societário'
        }

        for col in other_responsible_columns:
            if col in filtered_df.columns:
                resp_list = filtered_df[pd.notna(filtered_df[col])][col].unique()

                for responsible in resp_list:
                    role = role_mapping.get(col, 'Outro')

                    if responsible not in responsibles_data:
                        responsibles_data[responsible] = {
                            'id': responsible.lower().replace(' ', '-'),
                            'name': responsible,
                            'role': role,
                            'opportunities': 0,
                            'implementations': 0,
                            'finalized': 0,
                            'active': 0,
                            'revenue': 0,
                            'mrr': 0,
                            'universities': set(),
                            'courses': set(),
                            'classes': set()
                        }
                    elif responsibles_data[responsible]['role'] != role:
                        responsibles_data[responsible]['role'] += f"/{role}"

                    resp_df = filtered_df[filtered_df[col] == responsible]

                    # Count implementations
                    if 'Status_Implantacao' in resp_df.columns:
                        impl_df = resp_df[pd.notna(resp_df['Status_Implantacao'])]
                        responsibles_data[responsible]['implementations'] += impl_df['Oportunidade_id'].nunique()

                        # Count finalized implementations
                        finalized_df = impl_df[impl_df['Status_Implantacao'] == 'Finalizado']
                        responsibles_data[responsible]['finalized'] += finalized_df['Oportunidade_id'].nunique()

                        # Count active implementations
                        active_df = impl_df[(impl_df['Status_Implantacao'] != 'Finalizado') &
                                           (impl_df['Status_Implantacao'] != 'Cancelado')]
                        responsibles_data[responsible]['active'] += active_df['Oportunidade_id'].nunique()

                        # Calculate revenue and MRR
                        if 'Valor Mensalidade' in impl_df.columns:
                            # Log para depuração
                            logger.info(f"Calculando receita e MRR para {responsible} ({role})")
                            logger.info(f"Total de registros: {len(impl_df)}")

                            # Para receita, considerar vendas finalizadas
                            finalized_status = impl_df['Status_Implantacao'].fillna('').astype(str).str.lower() == 'finalizado'
                            finalized_df = impl_df[finalized_status]
                            logger.info(f"Registros finalizados: {len(finalized_df)}")

                            # Deduplicate by Oportunidade_id to avoid counting the same opportunity multiple times
                            unique_finalized_df = finalized_df.drop_duplicates(subset=['Oportunidade_id']) if 'Oportunidade_id' in finalized_df.columns else finalized_df
                            logger.info(f"Registros finalizados após deduplicação: {len(unique_finalized_df)}")

                            # Para MRR, considerar APENAS vendas finalizadas (conforme solicitado)
                            # Usando a mesma lógica da receita
                            mrr_df = unique_finalized_df.copy()
                            logger.info(f"Registros para MRR (apenas finalizados): {len(mrr_df)}")

                            # Calcular receita (vendas finalizadas)
                            revenue_total = 0
                            for _, row in unique_finalized_df.iterrows():
                                try:
                                    if pd.notna(row.get('Valor Mensalidade')):
                                        value_str = str(row['Valor Mensalidade']).replace('R$', '').replace('.', '').replace(',', '.').strip()
                                        if value_str and not value_str.isspace():
                                            value = float(value_str)
                                            responsibles_data[responsible]['revenue'] += value
                                            revenue_total += value
                                            logger.info(f"Adicionando valor {value} à receita de {responsible}")
                                except (ValueError, TypeError) as e:
                                    logger.warning(f"Error converting value '{row.get('Valor Mensalidade', 'N/A')}' to float: {e}")

                            logger.info(f"Receita total para {responsible}: {revenue_total}")

                            # Calcular MRR (apenas vendas finalizadas)
                            mrr_total = 0
                            for _, row in mrr_df.iterrows():
                                try:
                                    if pd.notna(row.get('Valor Mensalidade')):
                                        value_str = str(row['Valor Mensalidade']).replace('R$', '').replace('.', '').replace(',', '.').strip()
                                        if value_str and not value_str.isspace():
                                            value = float(value_str)
                                            responsibles_data[responsible]['mrr'] += value
                                            mrr_total += value
                                            logger.info(f"Adicionando valor {value} ao MRR de {responsible}")
                                except (ValueError, TypeError) as e:
                                    logger.warning(f"Error converting value '{row.get('Valor Mensalidade', 'N/A')}' to float: {e}")

                            logger.info(f"MRR total para {responsible}: {mrr_total}")

                    # Collect universities, courses, and classes
                    if 'Universidade' in resp_df.columns:
                        for univ in resp_df[pd.notna(resp_df['Universidade'])]['Universidade'].unique():
                            responsibles_data[responsible]['universities'].add(univ)

                    if 'Curso' in resp_df.columns:
                        for course in resp_df[pd.notna(resp_df['Curso'])]['Curso'].unique():
                            responsibles_data[responsible]['courses'].add(course)

                    if 'Turma' in resp_df.columns:
                        for class_name in resp_df[pd.notna(resp_df['Turma'])]['Turma'].unique():
                            responsibles_data[responsible]['classes'].add(class_name)

        # Convert sets to counts and format revenue and MRR
        responsibles_list = []
        for responsible, data in responsibles_data.items():
            data['universities'] = len(data['universities'])
            data['courses'] = len(data['courses'])
            data['classes'] = len(data['classes'])
            data['revenue_formatted'] = format_currency(data['revenue'])
            data['mrr_formatted'] = format_currency(data['mrr'])
            responsibles_list.append(data)

        # Sort by revenue (highest first)
        responsibles_list = sorted(responsibles_list, key=lambda x: x['revenue'], reverse=True)

        # Prepare data for charts
        role_counts = {}
        for resp in responsibles_list:
            # Verificar se role não é None antes de fazer split
            role_value = resp.get('role', '')
            if role_value and isinstance(role_value, str):
                roles = role_value.split('/')
                for role in roles:
                    role = role.strip()
                    if role in role_counts:
                        role_counts[role] += 1
                    else:
                        role_counts[role] = 1
            else:
                # Se role for None ou vazio, adicionar como "Não especificado"
                if 'Não especificado' in role_counts:
                    role_counts['Não especificado'] += 1
                else:
                    role_counts['Não especificado'] = 1

        role_data = [{'name': role, 'value': count} for role, count in role_counts.items()]

        # Prepare implementation status data
        implementation_status = {
            'Finalizadas': sum(r['finalized'] for r in responsibles_list),
            'Ativas': sum(r['active'] for r in responsibles_list)
        }

        implementation_data = [{'name': status, 'value': count} for status, count in implementation_status.items()]

        # Calculate total revenue
        total_revenue = sum(r['revenue'] for r in responsibles_list)

        # Get top performers by revenue
        top_revenue_data = [{'name': r['name'], 'value': r['revenue']} for r in sorted(responsibles_list, key=lambda x: x['revenue'], reverse=True)[:5]]

        # Analyze data quality for responsible fields
        data_quality = analyze_responsible_data_quality(df)

        return render_template('responsibles/index.html',
                              responsibles=responsibles_list,
                              total_responsibles=len(responsibles_list),
                              total_revenue=format_currency(total_revenue),
                              role_data=role_data,
                              implementation_data=implementation_data,
                              top_revenue_data=top_revenue_data,
                              class_filter=class_filter,
                              university_filter=university_filter,
                              data_quality=data_quality)
    except Exception as e:
        logger.error(f"Error in responsible index page: {e}")
        flash(f'Error loading responsible data: {str(e)}', 'error')
        return render_template('responsibles/index.html',
                              responsibles=[],
                              total_responsibles=0,
                              total_revenue=format_currency(0),
                              role_data=[],
                              implementation_data=[],
                              top_revenue_data=[],
                              class_filter=None,
                              university_filter=None)

def define_business_rules() -> Dict[str, Any]:
    """
    # Check responsibles permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission
    
    if not check_permission('responsibles.view'):
        flash('Acesso negado. Permissão insuficiente para acessar esta página.', 'error')
        return redirect(url_for('auth.login'))

    Define business rules for responsible data quality analysis

    Returns:
        dict: Business rules for responsible data quality analysis
    """
    return {
        # Required fields by entity
        'required_fields': {
            'Responsavel': ['Nome_Responsavel', 'ResponsableOnboarding']
        },

        # Optional fields by entity
        'optional_fields': {
            'Responsavel': ['ResponsableOngoing', 'ResponsableContabil', 'ResponsableSocietário']
        },

        # Fields with specific validation rules
        'validation_rules': {
            'Nome_Responsavel': {'type': 'text', 'required': True},
            'ResponsableOnboarding': {'type': 'text', 'required': True},
            'ResponsableOngoing': {'type': 'text', 'required': False},
            'ResponsableContabil': {'type': 'text', 'required': False},
            'ResponsableSocietário': {'type': 'text', 'required': False}
        }
    }

def analyze_responsible_data_quality(df: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze data quality for responsible-related fields

    Args:
        df (DataFrame): The data to analyze

    Returns:
        dict: Data quality metrics for responsible fields
    """
    try:
        # Get business rules
        business_rules = define_business_rules()

        # Define responsible-related columns
        responsible_columns = business_rules['required_fields']['Responsavel'] + business_rules['optional_fields']['Responsavel']

        # Initialize results
        results = {
            'completeness': {},
            'consistency': {},
            'overall_score': 0,
            'fields_count': len(responsible_columns),
            'fields_with_issues': 0,
            'recommendations': [],
            'responsible_quality': {}  # New field for quality by responsible
        }

        # Check completeness for each column
        total_rows = len(df)
        completeness_scores = []

        for col in responsible_columns:
            is_required = col in business_rules['required_fields']['Responsavel']

            if col in df.columns:
                non_null_count = df[col].count()
                completeness_pct = (non_null_count / total_rows) * 100 if total_rows > 0 else 0

                # Determine completeness level (stricter for required fields)
                if is_required:
                    if completeness_pct >= 98:
                        level = "Excelente"
                        score = 5
                    elif completeness_pct >= 90:
                        level = "Bom"
                        score = 4
                    elif completeness_pct >= 80:
                        level = "Razoável"
                        score = 3
                    elif completeness_pct >= 70:
                        level = "Precisa de Atenção"
                        score = 2
                    else:
                        level = "Crítico"
                        score = 1
                        results['fields_with_issues'] += 1
                        results['recommendations'].append(f"Preencher o campo obrigatório {col} que está com {completeness_pct:.1f}% de preenchimento")
                else:
                    if completeness_pct >= 95:
                        level = "Excelente"
                        score = 5
                    elif completeness_pct >= 85:
                        level = "Bom"
                        score = 4
                    elif completeness_pct >= 70:
                        level = "Razoável"
                        score = 3
                    elif completeness_pct >= 50:
                        level = "Precisa de Atenção"
                        score = 2
                    else:
                        level = "Crítico"
                        score = 1
                        results['fields_with_issues'] += 1
                        results['recommendations'].append(f"Melhorar o preenchimento do campo {col} que está com {completeness_pct:.1f}% de preenchimento")

                results['completeness'][col] = {
                    'percentage': round(completeness_pct, 2),
                    'level': level,
                    'score': score,
                    'required': is_required
                }

                completeness_scores.append(score)
            else:
                results['completeness'][col] = {
                    'percentage': 0,
                    'level': 'Campo Ausente',
                    'score': 0,
                    'required': is_required
                }
                results['fields_with_issues'] += 1
                if is_required:
                    results['recommendations'].append(f"Adicionar o campo obrigatório {col} que está ausente na base de dados")
                else:
                    results['recommendations'].append(f"Considerar adicionar o campo {col} que está ausente na base de dados")

        # Check consistency for each column
        for col in responsible_columns:
            if col in df.columns:
                # Check for inconsistent values (e.g., same person with different spellings)
                values = df[col].dropna().astype(str)
                normalized_values = values.str.lower().str.strip()

                # Count unique values before and after normalization
                unique_before = values.nunique()
                unique_after = normalized_values.nunique()

                if unique_before > unique_after:
                    diff = unique_before - unique_after
                    results['consistency'][col] = {
                        'status': 'Inconsistente',
                        'details': f"Encontradas {diff} variações de escrita para os mesmos responsáveis"
                    }
                    results['fields_with_issues'] += 1
                    results['recommendations'].append(f"Padronizar as variações de escrita no campo {col}")
                else:
                    results['consistency'][col] = {
                        'status': 'Consistente',
                        'details': "Sem problemas de consistência detectados"
                    }

        # Calculate overall score (average of completeness scores, weighted by required/optional)
        if completeness_scores:
            # Weight required fields more heavily (70% required, 30% optional)
            required_scores = []
            optional_scores = []

            for col, data in results['completeness'].items():
                if data['required']:
                    required_scores.append(data['score'])
                else:
                    optional_scores.append(data['score'])

            if required_scores and optional_scores:
                required_avg = sum(required_scores) / len(required_scores)
                optional_avg = sum(optional_scores) / len(optional_scores)
                results['overall_score'] = round((required_avg * 0.7) + (optional_avg * 0.3), 2)
            elif required_scores:
                results['overall_score'] = round(sum(required_scores) / len(required_scores), 2)
            else:
                results['overall_score'] = round(sum(optional_scores) / len(optional_scores), 2)

        # Add overall quality level
        if results['overall_score'] >= 4.5:
            results['overall_level'] = "Excelente"
        elif results['overall_score'] >= 3.5:
            results['overall_level'] = "Bom"
        elif results['overall_score'] >= 2.5:
            results['overall_level'] = "Razoável"
        elif results['overall_score'] >= 1.5:
            results['overall_level'] = "Precisa de Atenção"
        else:
            results['overall_level'] = "Crítico"

        # Add general recommendations if needed
        if results['fields_with_issues'] > 0:
            results['recommendations'].append("Implementar validações de entrada para garantir o preenchimento correto dos campos de responsáveis")

        # Analyze quality by responsible
        if 'Nome_Responsavel' in df.columns:
            responsibles = df['Nome_Responsavel'].dropna().unique()

            for responsible in responsibles:
                # Filter data for this responsible
                responsible_df = df[df['Nome_Responsavel'] == responsible]

                # Count implementations
                implementations = 0
                if 'Status_Implantacao' in responsible_df.columns:
                    implementations = responsible_df['Status_Implantacao'].count()

                # Count finalized implementations
                finalized = 0
                if 'Status_Implantacao' in responsible_df.columns:
                    finalized = len(responsible_df[responsible_df['Status_Implantacao'] == 'Finalizado'])

                # Calculate completeness for implementation fields
                implementation_fields = ['ID_Oportunidade', 'Status_Implantacao', 'Fase_Implantacao',
                                        'ResponsableOnboarding', 'Valor_Mensalidade']

                field_completeness = {}
                problem_fields = []

                for field in implementation_fields:
                    if field in responsible_df.columns and implementations > 0:
                        non_null_count = responsible_df[field].count()
                        completeness_pct = (non_null_count / implementations) * 100

                        field_completeness[field] = round(completeness_pct, 2)

                        # Check if this is a problem field
                        is_required = field in business_rules['required_fields']['Responsavel']
                        threshold = 90 if is_required else 70

                        if completeness_pct < threshold:
                            problem_fields.append({
                                'field': field,
                                'percentage': round(completeness_pct, 2),
                                'required': is_required
                            })

                # Calculate overall score for this responsible
                field_scores = []
                for field, pct in field_completeness.items():
                    if pct >= 95:
                        field_scores.append(5)
                    elif pct >= 85:
                        field_scores.append(4)
                    elif pct >= 70:
                        field_scores.append(3)
                    elif pct >= 50:
                        field_scores.append(2)
                    else:
                        field_scores.append(1)

                responsible_score = 0
                if field_scores:
                    responsible_score = sum(field_scores) / len(field_scores)

                # Determine quality level
                if responsible_score >= 4.5:
                    level = "Excelente"
                elif responsible_score >= 3.5:
                    level = "Bom"
                elif responsible_score >= 2.5:
                    level = "Razoável"
                elif responsible_score >= 1.5:
                    level = "Precisa de Atenção"
                else:
                    level = "Crítico"

                # Store results
                results['responsible_quality'][responsible] = {
                    'implementations': implementations,
                    'finalized': finalized,
                    'field_completeness': field_completeness,
                    'problem_fields': sorted(problem_fields, key=lambda x: x['percentage'])[:3],  # Top 3 problem fields
                    'score': round(responsible_score, 2),
                    'level': level
                }

        return results
    except Exception as e:
        logger.error(f"Error analyzing responsible data quality: {e}")
        return {
            'completeness': {},
            'consistency': {},
            'overall_score': 0,
            'fields_count': 0,
            'fields_with_issues': 0,
            'recommendations': ["Erro ao analisar qualidade dos dados"],
            'responsible_quality': {}
        }

@responsible_bp.route('/<responsible_id>')
def detail(responsible_id: int) -> str:
    """Responsible detail page"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        business_rules = BusinessRulesService(data_loader)
        analytics = AnalyticsService(data_loader, business_rules)

        df = data_loader.get_data()

        # Replace hyphens with spaces for matching
        responsible_name = responsible_id.replace('-', ' ')

        # Find the responsible in different columns
        responsible_columns = ['Nome_Responsavel', 'ResponsableOnboarding', 'ResponsableOngoing', 'ResponsableContabil', 'ResponsableSocietário']

        found = False
        for col in responsible_columns:
            if col in df.columns:
                # Usar uma comparação segura para evitar ambiguidade
                mask = df[col].fillna('').astype(str).str.lower() == responsible_name.lower()
                resp_df = df[mask]
                if len(resp_df) > 0:
                    found = True
                    responsible_name = resp_df[col].iloc[0]  # Get the correct case
                    break

        if not found:
            logger.error(f"Responsible not found: {responsible_id}")
            flash(f'Responsável não encontrado: {responsible_id}', 'error')
            return redirect(url_for('responsible.index'))

        # Determine the role(s)
        roles = []
        for col, role_name in [('Nome_Responsavel', 'Vendas'),
                              ('ResponsableOnboarding', 'Onboarding'),
                              ('ResponsableOngoing', 'Ongoing'),
                              ('ResponsableContabil', 'Contábil'),
                              ('ResponsableSocietário', 'Societário')]:
            if col in df.columns:
                # Corrigindo o erro de ambiguidade
                filtered_col = df[pd.notna(df[col])][col]
                if not filtered_col.empty and any(filtered_col.str.lower() == responsible_name.lower()):
                    roles.append(role_name)

        role = '/'.join(roles) if roles else 'Não especificado'

        # Get all data for this responsible
        resp_df = pd.DataFrame()
        for col in responsible_columns:
            if col in df.columns:
                # Usar uma comparação segura para evitar ambiguidade
                mask = df[col].fillna('').astype(str).str.lower() == responsible_name.lower()
                temp_df = df[mask]
                resp_df = pd.concat([resp_df, temp_df])

        resp_df = resp_df.drop_duplicates()

        # Count opportunities
        opportunity_count = resp_df['Oportunidade_id'].nunique() if 'Oportunidade_id' in resp_df.columns else 0

        # Count implementations
        implementation_count = 0
        finalized_count = 0
        active_count = 0

        if 'Status_Implantacao' in resp_df.columns:
            impl_df = resp_df[pd.notna(resp_df['Status_Implantacao'])]
            implementation_count = impl_df['Oportunidade_id'].nunique()

            # Count finalized implementations
            finalized_df = impl_df[impl_df['Status_Implantacao'] == 'Finalizado']
            finalized_count = finalized_df['Oportunidade_id'].nunique()

            # Count active implementations
            active_df = impl_df[(impl_df['Status_Implantacao'] != 'Finalizado') &
                               (impl_df['Status_Implantacao'] != 'Cancelado')]
            active_count = active_df['Oportunidade_id'].nunique()

        # Calculate revenue and MRR
        revenue = 0
        mrr = 0
        if 'Valor Mensalidade' in resp_df.columns and 'Status_Implantacao' in resp_df.columns:
            # Log para depuração
            logger.info(f"Calculando receita e MRR para {responsible_name}")
            logger.info(f"Total de registros: {len(resp_df)}")
            logger.info(f"Colunas disponíveis: {resp_df.columns.tolist()}")

            # Verificar valores únicos de Status_Implantacao
            status_values = resp_df['Status_Implantacao'].unique()
            logger.info(f"Valores únicos de Status_Implantacao: {status_values}")

            # Para receita, considerar apenas vendas finalizadas
            finalized_status = resp_df['Status_Implantacao'].fillna('').astype(str).str.lower() == 'finalizado'
            finalized_df = resp_df[finalized_status]
            logger.info(f"Registros finalizados: {len(finalized_df)}")

            # Deduplicate by Oportunidade_id to avoid counting the same opportunity multiple times
            unique_finalized_df = None
            if 'Oportunidade_id' in finalized_df.columns:
                unique_finalized_df = finalized_df.drop_duplicates(subset=['Oportunidade_id'])
                logger.info(f"Registros finalizados após deduplicação: {len(unique_finalized_df)}")
            else:
                unique_finalized_df = finalized_df

            # Para MRR, considerar APENAS vendas finalizadas (conforme solicitado)
            # Usando a mesma lógica da receita
            mrr_df = unique_finalized_df.copy()

            if 'Oportunidade_id' in mrr_df.columns:
                mrr_df = mrr_df.drop_duplicates(subset=['Oportunidade_id'])
                logger.info(f"Registros para MRR após deduplicação: {len(mrr_df)}")

            # Calcular receita total (vendas finalizadas)
            for _, row in unique_finalized_df.iterrows():
                try:
                    if pd.notna(row.get('Valor Mensalidade')):
                        value_str = str(row['Valor Mensalidade']).replace('R$', '').replace('.', '').replace(',', '.').strip()
                        logger.info(f"Valor para receita: {value_str}")
                        if value_str and not value_str.isspace():
                            value = float(value_str)
                            revenue += value
                            logger.info(f"Receita acumulada: {revenue}")
                except (ValueError, TypeError) as e:
                    logger.warning(f"Error converting value '{row.get('Valor Mensalidade', 'N/A')}' to float: {e}")

            # Calcular MRR (todas as vendas ativas)
            for _, row in mrr_df.iterrows():
                try:
                    if pd.notna(row.get('Valor Mensalidade')):
                        value_str = str(row['Valor Mensalidade']).replace('R$', '').replace('.', '').replace(',', '.').strip()
                        logger.info(f"Valor para MRR: {value_str}")
                        if value_str and not value_str.isspace():
                            value = float(value_str)
                            mrr += value
                            logger.info(f"MRR acumulado: {mrr}")
                except (ValueError, TypeError) as e:
                    logger.warning(f"Error converting value '{row.get('Valor Mensalidade', 'N/A')}' to float: {e}")

        # Get universities, courses, and classes
        universities = []
        if 'Universidade' in resp_df.columns:
            for univ in resp_df[pd.notna(resp_df['Universidade'])]['Universidade'].unique():
                univ_df = resp_df[resp_df['Universidade'] == univ]

                # Count students
                student_count = univ_df['Nome do Lead'].nunique() if 'Nome do Lead' in univ_df.columns else 0

                # Count implementations
                univ_impl_count = 0
                if 'Status_Implantacao' in univ_df.columns:
                    univ_impl_df = univ_df[pd.notna(univ_df['Status_Implantacao'])]
                    univ_impl_count = univ_impl_df['Oportunidade_id'].nunique()

                universities.append({
                    'name': univ,
                    'students': student_count,
                    'implementations': univ_impl_count
                })

        # Sort universities by implementation count
        universities = sorted(universities, key=lambda x: x['implementations'], reverse=True)

        # Get courses
        courses = []
        if 'Curso' in resp_df.columns:
            for course in resp_df[pd.notna(resp_df['Curso'])]['Curso'].unique():
                course_df = resp_df[resp_df['Curso'] == course]

                # Count students
                student_count = course_df['Nome do Lead'].nunique() if 'Nome do Lead' in course_df.columns else 0

                # Count implementations
                course_impl_count = 0
                if 'Status_Implantacao' in course_df.columns:
                    course_impl_df = course_df[pd.notna(course_df['Status_Implantacao'])]
                    course_impl_count = course_impl_df['Oportunidade_id'].nunique()

                courses.append({
                    'name': course,
                    'students': student_count,
                    'implementations': course_impl_count
                })

        # Sort courses by implementation count
        courses = sorted(courses, key=lambda x: x['implementations'], reverse=True)

        # Get classes
        classes = []
        if 'Turma' in resp_df.columns:
            for class_name in resp_df[pd.notna(resp_df['Turma'])]['Turma'].unique():
                class_df = resp_df[resp_df['Turma'] == class_name]

                # Count students
                student_count = class_df['Nome do Lead'].nunique() if 'Nome do Lead' in class_df.columns else 0

                # Count implementations
                class_impl_count = 0
                if 'Status_Implantacao' in class_df.columns:
                    class_impl_df = class_df[pd.notna(class_df['Status_Implantacao'])]
                    class_impl_count = class_impl_df['Oportunidade_id'].nunique()

                # Get university and course
                university = class_df['Universidade'].iloc[0] if 'Universidade' in class_df.columns and not pd.isna(class_df['Universidade'].iloc[0]) else 'Não especificada'
                course = class_df['Curso'].iloc[0] if 'Curso' in class_df.columns and not pd.isna(class_df['Curso'].iloc[0]) else 'Não especificado'

                classes.append({
                    'name': class_name,
                    'university': university,
                    'course': course,
                    'students': student_count,
                    'implementations': class_impl_count
                })

        # Sort classes by implementation count
        classes = sorted(classes, key=lambda x: x['implementations'], reverse=True)

        return render_template('responsibles/detail.html',
                              responsible_name=responsible_name,
                              responsible_id=responsible_id,
                              role=role,
                              opportunity_count=opportunity_count,
                              implementation_count=implementation_count,
                              finalized_count=finalized_count,
                              active_count=active_count,
                              revenue=format_currency(revenue),
                              mrr=format_currency(mrr),
                              universities=universities,
                              courses=courses,
                              classes=classes)
    except Exception as e:
        logger.error(f"Error in responsible detail page: {e}")
        flash(f'Error loading responsible details: {str(e)}', 'error')
        return redirect(url_for('responsible.index'))
