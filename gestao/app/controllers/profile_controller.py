from flask import Blueprint, render_template, request, redirect, url_for, flash, session
import sqlite3
import hashlib
import re
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

profile_bp = Blueprint('profile', __name__, url_prefix='/profile')

def get_db_connection():
    """Get database connection"""
    try:
        import os
        # Use the correct database path - relative to the gestao directory
        db_path = os.path.join('data', 'business_admin.db')
        logger.info(f"Attempting to connect to database at: {db_path}")

        if not os.path.exists(db_path):
            logger.error(f"Database file not found at: {db_path}")
            return None

        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        logger.info("Database connection successful")
        return conn
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        return None

def hash_password(password):
    """Hash password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def validate_password(password):
    """Validate password strength"""
    if len(password) < 8:
        return False, "A senha deve ter pelo menos 8 caracteres"

    if not re.search(r'[A-Z]', password):
        return False, "A senha deve conter pelo menos uma letra maiúscula"

    if not re.search(r'[a-z]', password):
        return False, "A senha deve conter pelo menos uma letra minúscula"

    if not re.search(r'\d', password):
        return False, "A senha deve conter pelo menos um número"

    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        return False, "A senha deve conter pelo menos um símbolo especial"

    return True, "Senha válida"

@profile_bp.route('/')
def index():
    """Profile page"""
    if 'username' not in session:
        flash('Acesso negado. Faça login para continuar.', 'error')
        return redirect(url_for('auth.login'))

    username = session['username']

    # Get user information
    conn = get_db_connection()
    if not conn:
        flash('Erro de conexão com o banco de dados', 'error')
        return redirect(url_for('dashboard.index'))

    try:
        user = conn.execute('SELECT username, email, created_at FROM users WHERE username = ?', (username,)).fetchone()
        conn.close()

        if not user:
            flash('Usuário não encontrado', 'error')
            return redirect(url_for('auth.login'))

        return render_template('profile/index.html', user=user)

    except Exception as e:
        logger.error(f"Error fetching user profile: {e}")
        flash('Erro ao carregar perfil do usuário', 'error')
        return redirect(url_for('dashboard.index'))

@profile_bp.route('/change-password', methods=['POST'])
def change_password():
    """Change user password"""
    if 'username' not in session:
        flash('Acesso negado. Faça login para continuar.', 'error')
        return redirect(url_for('auth.login'))

    username = session['username']
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')

    # Validate inputs
    if not all([current_password, new_password, confirm_password]):
        flash('Todos os campos são obrigatórios', 'error')
        return redirect(url_for('profile.index'))

    if new_password != confirm_password:
        flash('A nova senha e a confirmação não coincidem', 'error')
        return redirect(url_for('profile.index'))

    # Validate password strength
    is_valid, message = validate_password(new_password)
    if not is_valid:
        flash(message, 'error')
        return redirect(url_for('profile.index'))

    # Check current password and update
    conn = get_db_connection()
    if not conn:
        flash('Erro de conexão com o banco de dados', 'error')
        return redirect(url_for('profile.index'))

    try:
        # Verify current password
        current_password_hash = hash_password(current_password)
        user = conn.execute('SELECT id FROM users WHERE username = ? AND password_hash = ?',
                           (username, current_password_hash)).fetchone()

        if not user:
            flash('Senha atual incorreta', 'error')
            conn.close()
            return redirect(url_for('profile.index'))

        # Update password
        new_password_hash = hash_password(new_password)
        conn.execute('UPDATE users SET password_hash = ? WHERE username = ?',
                    (new_password_hash, username))
        conn.commit()
        conn.close()

        flash('Senha alterada com sucesso!', 'success')
        logger.info(f"Password changed for user: {username}")

        return redirect(url_for('profile.index'))

    except Exception as e:
        logger.error(f"Error changing password for user {username}: {e}")
        flash('Erro ao alterar senha. Tente novamente.', 'error')
        return redirect(url_for('profile.index'))
