"""
Amigo DataHub - Conversion Controller
"""

import json
import logging
from flask import Blueprint, render_template, flash, request
from app.services.data_loader import DataLoader
from app.services.business_rules import BusinessRulesService
from app.services.analytics import AnalyticsService
from app.utils.formatters import format_currency, prepare_chart_data
from typing import Dict, List, Any, Optional, Union, Tuple

logger = logging.getLogger(__name__)

def get_filter_options(df):
    """Get unique values for filter options"""
    try:
        logger.info(f"Getting filter options from DataFrame with shape: {df.shape}")
        logger.info(f"DataFrame columns: {df.columns.tolist()}")

        filter_options = {
            'turmas': [],
            'responsaveis': [],
            'universidades': [],
            'cidades': [],
            'estados': []
        }

        # Get unique values for each filter column
        if 'Turma' in df.columns:
            turmas = df['Turma'].dropna().astype(str).unique().tolist()
            filter_options['turmas'] = sorted([t for t in turmas if t and t != 'nan'])
            logger.info(f"Found {len(filter_options['turmas'])} unique turmas")

        if 'Nome_Responsavel' in df.columns:
            responsaveis = df['Nome_Responsavel'].dropna().astype(str).unique().tolist()
            filter_options['responsaveis'] = sorted([r for r in responsaveis if r and r != 'nan'])
            logger.info(f"Found {len(filter_options['responsaveis'])} unique responsaveis")

        if 'Universidade' in df.columns:
            universidades = df['Universidade'].dropna().astype(str).unique().tolist()
            filter_options['universidades'] = sorted([u for u in universidades if u and u != 'nan'])
            logger.info(f"Found {len(filter_options['universidades'])} unique universidades")

        if 'Cidade' in df.columns:
            cidades = df['Cidade'].dropna().astype(str).unique().tolist()
            filter_options['cidades'] = sorted([c for c in cidades if c and c != 'nan'])
            logger.info(f"Found {len(filter_options['cidades'])} unique cidades")

        if 'Estado' in df.columns:
            estados = df['Estado'].dropna().astype(str).unique().tolist()
            filter_options['estados'] = sorted([e for e in estados if e and e != 'nan'])
            logger.info(f"Found {len(filter_options['estados'])} unique estados")

        logger.info(f"Final filter options: {filter_options}")
        return filter_options

    except Exception as e:
        logger.error(f"Error getting filter options: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            'turmas': [],
            'responsaveis': [],
            'universidades': [],
            'cidades': [],
            'estados': []
        }

def calculate_advanced_funnel_data(business_rules, analytics):
    """Calculate advanced funnel data for D3.js visualization"""
    try:
        # Get basic conversion data
        total_leads, leads_with_opps, lead_to_opp_rate, total_opps, opps_with_impl, opp_to_impl_rate = business_rules.calculate_conversion_rates()

        # Get active users data
        total_students, active_students = business_rules.identify_active_students()

        # Calculate productive users (those who generate invoices)
        df = business_rules.data_loader.get_data()
        productive_users = 0
        if 'Gerou_Nota_Fiscal' in df.columns and 'Status_Implantacao' in df.columns:
            productive_users = len(df[(df['Status_Implantacao'] == 'Finalizado') & (df['Gerou_Nota_Fiscal'] == 'Sim')])

        # Calculate MRR data
        mrr_total, mrr_finalized, mrr_active, mrr_growth = business_rules.calculate_mrr()

        # Calculate time metrics
        avg_lead_to_opp_days, avg_opp_to_impl_days, avg_lead_to_impl_days = business_rules.calculate_time_to_conversion()

        # Calculate conversion rates for each stage
        stages = [
            {
                'name': 'Leads',
                'value': total_leads,
                'percentage': 100,
                'color': '#3B82F6',
                'description': 'Potenciais clientes registrados'
            },
            {
                'name': 'Oportunidades',
                'value': total_opps,
                'percentage': round((total_opps / total_leads * 100) if total_leads > 0 else 0, 1),
                'color': '#6366F1',
                'description': 'Leads qualificados'
            },
            {
                'name': 'Implementações',
                'value': opps_with_impl,
                'percentage': round((opps_with_impl / total_leads * 100) if total_leads > 0 else 0, 1),
                'color': '#8B5CF6',
                'description': 'Oportunidades em implementação'
            },
            {
                'name': 'Usuários Ativos',
                'value': active_students,
                'percentage': round((active_students / total_leads * 100) if total_leads > 0 else 0, 1),
                'color': '#A855F7',
                'description': 'Implementações finalizadas'
            },
            {
                'name': 'Usuários Produtivos',
                'value': productive_users,
                'percentage': round((productive_users / total_leads * 100) if total_leads > 0 else 0, 1),
                'color': '#C084FC',
                'description': 'Usuários gerando receita'
            }
        ]

        # Calculate drop-off rates between stages
        dropoffs = []
        for i in range(len(stages) - 1):
            current_stage = stages[i]
            next_stage = stages[i + 1]

            drop_value = current_stage['value'] - next_stage['value']
            drop_percentage = round((drop_value / current_stage['value'] * 100) if current_stage['value'] > 0 else 0, 1)

            dropoffs.append({
                'from_stage': current_stage['name'],
                'to_stage': next_stage['name'],
                'drop_value': drop_value,
                'drop_percentage': drop_percentage,
                'conversion_rate': round((next_stage['value'] / current_stage['value'] * 100) if current_stage['value'] > 0 else 0, 1)
            })

        # Calculate revenue metrics per stage
        avg_ticket = business_rules.calculate_average_ticket(mrr_total, active_students)

        revenue_metrics = {
            'mrr_total': mrr_total,
            'avg_ticket': avg_ticket,
            'potential_revenue': business_rules.calculate_potential_revenue(),
            'revenue_per_lead': round((mrr_total / total_leads) if total_leads > 0 else 0, 2),
            'revenue_per_opportunity': round((mrr_total / total_opps) if total_opps > 0 else 0, 2)
        }

        # Calculate time metrics for each stage
        time_metrics = {
            'lead_to_opportunity': avg_lead_to_opp_days,
            'opportunity_to_implementation': avg_opp_to_impl_days,
            'implementation_to_active': 19,  # From business rules - average time to activation
            'total_cycle_time': avg_lead_to_impl_days + 19
        }

        return {
            'stages': stages,
            'dropoffs': dropoffs,
            'revenue_metrics': revenue_metrics,
            'time_metrics': time_metrics,
            'summary': {
                'total_conversion_rate': round((active_students / total_leads * 100) if total_leads > 0 else 0, 1),
                'productive_conversion_rate': round((productive_users / total_leads * 100) if total_leads > 0 else 0, 1),
                'biggest_dropoff': max(dropoffs, key=lambda x: x['drop_percentage']) if dropoffs else None,
                'fastest_stage': min(time_metrics.values()) if time_metrics else 0,
                'slowest_stage': max(time_metrics.values()) if time_metrics else 0
            }
        }

    except Exception as e:
        logger.error(f"Error calculating advanced funnel data: {e}")
        return {
            'stages': [],
            'dropoffs': [],
            'revenue_metrics': {},
            'time_metrics': {},
            'summary': {}
        }

# Create blueprint
conversion_bp = Blueprint('conversion', __name__, url_prefix='/conversion')

@conversion_bp.route('/')
def index() -> str:
    """Conversion index page"""
    # Check conversion permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission

    if not check_permission('conversion.view'):
        flash('Acesso negado. Permissão insuficiente para acessar esta página.', 'error')
        return redirect(url_for('auth.login'))

    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        # Get original data for filter options
        df = data_loader.get_data()
        filter_options = get_filter_options(df)
        logger.info(f"Filter options generated: {filter_options}")
        logger.info(f"DataFrame columns: {df.columns.tolist()}")
        logger.info(f"DataFrame shape: {df.shape}")

        # Get filter parameters
        turma_filter = request.args.get('turma')
        responsavel_filter = request.args.get('responsavel')
        universidade_filter = request.args.get('universidade')
        cidade_filter = request.args.get('cidade')
        estado_filter = request.args.get('estado')

        # Apply filters if provided
        if turma_filter:
            df = df[df['Turma'] == turma_filter]
        if responsavel_filter:
            df = df[df['Nome_Responsavel'] == responsavel_filter]
        if universidade_filter:
            df = df[df['Universidade'] == universidade_filter]
        if cidade_filter:
            df = df[df['Cidade'] == cidade_filter]
        if estado_filter:
            df = df[df['Estado'] == estado_filter]

        # Update data loader with filtered data
        data_loader.data = df

        business_rules = BusinessRulesService(data_loader)
        analytics = AnalyticsService(data_loader, business_rules)

        # Calculate conversion rates
        total_leads, leads_with_opps, lead_to_opp_rate, total_opps, opps_with_impl, opp_to_impl_rate = business_rules.calculate_conversion_rates()

        # Calculate overall conversion rate
        conversao_geral = business_rules.calculate_overall_conversion(total_leads, opps_with_impl)

        # Calculate funnel distribution
        funnel_stages = business_rules.calculate_funnel_distribution()

        # Calculate implementation phases
        implementation_phases = business_rules.calculate_implementation_phases()

        # Calculate implementation status
        status_counts, status_positions = business_rules.calculate_implementation_status()

        # Count finalized implementations
        finalized_implementations = 0
        if 'Finalizado' in status_counts:
            finalized_implementations = int(status_counts['Finalizado'])

        # Calculate estimated revenue
        mrr_total, _, _, _ = business_rules.calculate_mrr()
        estimated_revenue = format_currency(mrr_total)

        # Calculate average value
        avg_value = format_currency(business_rules.calculate_average_ticket(mrr_total, opps_with_impl))

        # Calculate time to conversion metrics
        avg_lead_to_opp_days, avg_opp_to_impl_days, avg_lead_to_impl_days = business_rules.calculate_time_to_conversion()

        # Calculate conversion rate by month
        month_labels, lead_to_opp_rates, opp_to_impl_rates, overall_rates = business_rules.calculate_conversion_rate_by_month()

        # Calculate seasonal conversion analysis
        quarter_labels, quarter_rates, month_of_year_labels, month_of_year_rates = business_rules.calculate_seasonal_conversion_analysis()

        # Calculate conversion velocity
        conversion_velocity = business_rules.calculate_conversion_velocity()

        # Prepare chart data
        chart_data = analytics.prepare_chart_data()

        # Get implementation historical phases from analytics service
        implementation_historical_phases = analytics.get_implementation_historical_phases()

        # Add to chart data if available
        if implementation_historical_phases:
            chart_data['implementation_historical_phases'] = implementation_historical_phases
        else:
            # If data is not available, provide an error message
            chart_data['implementation_historical_phases'] = {}

        # Add total leads to chart data
        chart_data['total_leads'] = str(total_leads)

        # Add time-based analysis to chart data
        chart_data['time_to_conversion'] = {
            'lead_to_opp_days': avg_lead_to_opp_days,
            'opp_to_impl_days': avg_opp_to_impl_days,
            'lead_to_impl_days': avg_lead_to_impl_days
        }

        chart_data['conversion_by_month'] = {
            'labels': month_labels,
            'lead_to_opp_rates': lead_to_opp_rates,
            'opp_to_impl_rates': opp_to_impl_rates,
            'overall_rates': overall_rates
        }

        chart_data['seasonal_analysis'] = {
            'quarter_labels': quarter_labels,
            'quarter_rates': quarter_rates,
            'month_labels': month_of_year_labels,
            'month_rates': month_of_year_rates
        }

        chart_data['conversion_velocity'] = conversion_velocity

        # Calculate advanced funnel data for D3.js visualization
        logger.info("Starting advanced funnel data calculation")
        try:
            advanced_funnel_data = calculate_advanced_funnel_data(business_rules, analytics)
            chart_data['advanced_funnel'] = advanced_funnel_data
            logger.info(f"Advanced funnel data calculated successfully: {len(advanced_funnel_data.get('stages', []))} stages")
        except Exception as e:
            logger.error(f"Error calculating advanced funnel data: {e}")
            chart_data['advanced_funnel'] = {
                'stages': [],
                'dropoffs': [],
                'revenue_metrics': {},
                'time_metrics': {},
                'summary': {}
            }

        # Ensure all values are strings
        logger.info("Starting chart data preparation")
        try:
            chart_data = prepare_chart_data(chart_data)
            logger.info("Chart data prepared successfully")
        except Exception as e:
            logger.error(f"Error preparing chart data: {e}")
            # Continue with original data if preparation fails

        return render_template('conversion/index.html',
                              total_leads=total_leads,
                              leads_with_opps=leads_with_opps,
                              lead_to_opp_rate=lead_to_opp_rate,
                              total_opps=total_opps,
                              opps_with_impl=opps_with_impl,
                              opp_to_impl_rate=opp_to_impl_rate,
                              conversao_geral=conversao_geral,
                              estimated_revenue=estimated_revenue,
                              avg_value=avg_value,
                              funnel_stages=funnel_stages,
                              implementation_phases=implementation_phases,
                              implementation_historical_phases=implementation_historical_phases,
                              status_positions=status_counts,
                              finalized_implementations=finalized_implementations,
                              avg_lead_to_opp_days=avg_lead_to_opp_days,
                              avg_opp_to_impl_days=avg_opp_to_impl_days,
                              avg_lead_to_impl_days=avg_lead_to_impl_days,
                              month_labels=month_labels,
                              lead_to_opp_rates=lead_to_opp_rates,
                              opp_to_impl_rates=opp_to_impl_rates,
                              overall_rates=overall_rates,
                              quarter_labels=quarter_labels,
                              quarter_rates=quarter_rates,
                              month_of_year_labels=month_of_year_labels,
                              month_of_year_rates=month_of_year_rates,
                              conversion_velocity=conversion_velocity,
                              chart_data=json.dumps(chart_data),
                              filter_options=filter_options)
    except Exception as e:
        logger.error(f"Error in conversion index: {e}")
        flash(f'Error loading conversion data: {str(e)}', 'error')
        return render_template('conversion/index.html',
                              total_leads=0,
                              leads_with_opps=0,
                              lead_to_opp_rate=0.0,
                              total_opps=0,
                              opps_with_impl=0,
                              opp_to_impl_rate=0.0,
                              conversao_geral=0.0,
                              estimated_revenue="R$ 0,00",
                              avg_value="R$ 0,00",
                              funnel_stages={},
                              implementation_phases={},
                              implementation_historical_phases={},
                              status_positions={},
                              finalized_implementations=0,
                              avg_lead_to_opp_days=0,
                              avg_opp_to_impl_days=0,
                              avg_lead_to_impl_days=0,
                              month_labels=[],
                              lead_to_opp_rates=[],
                              opp_to_impl_rates=[],
                              overall_rates=[],
                              quarter_labels=[],
                              quarter_rates=[],
                              month_of_year_labels=[],
                              month_of_year_rates=[],
                              conversion_velocity={},
                              chart_data=json.dumps({}),
                              filter_options={
                                  'turmas': [],
                                  'responsaveis': [],
                                  'universidades': [],
                                  'cidades': [],
                                  'estados': []
                              })

@conversion_bp.route('/api/filtered-data', methods=['POST'])
def get_filtered_data():
    """Get filtered conversion data via AJAX"""
    try:
        from flask import request, jsonify

        # Get filter parameters from request
        filters = request.get_json()
        logger.info(f"Received filters: {filters}")

        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        # Get original data
        df = data_loader.get_data()

        # Apply filters
        if filters:
            for filter_data in filters:
                filter_type = filter_data.get('type')
                filter_values = filter_data.get('values', [])

                if filter_type == 'turma' and filter_values:
                    df = df[df['Turma'].isin(filter_values)]
                elif filter_type == 'responsavel' and filter_values:
                    df = df[df['Nome_Responsavel'].isin(filter_values)]
                elif filter_type == 'universidade' and filter_values:
                    df = df[df['Universidade'].isin(filter_values)]
                elif filter_type == 'cidade' and filter_values:
                    df = df[df['Cidade'].isin(filter_values)]
                elif filter_type == 'estado' and filter_values:
                    df = df[df['Estado'].isin(filter_values)]

        # Update data loader with filtered data
        data_loader.data = df

        # Calculate filtered funnel data
        business_rules = BusinessRulesService(data_loader)
        analytics = AnalyticsService(data_loader, business_rules)

        # Calculate advanced funnel data
        logger.info("Starting filtered advanced funnel data calculation")
        advanced_funnel_data = calculate_advanced_funnel_data(business_rules, analytics)
        logger.info(f"Filtered advanced funnel data calculated successfully: {len(advanced_funnel_data.get('stages', []))} stages")

        return jsonify({
            'success': True,
            'advanced_funnel': advanced_funnel_data
        })

    except Exception as e:
        logger.error(f"Error getting filtered data: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
