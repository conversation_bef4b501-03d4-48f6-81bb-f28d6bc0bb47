"""
Amigo DataHub - Class Controller
"""

import logging
import pandas as pd
import json
from flask import Blueprint, render_template, flash, jsonify, request, redirect, url_for
from markupsafe import Markup
from app.services.data_loader import DataLoader
from app.services.business_rules import BusinessRulesService
from app.services.analytics import AnalyticsService
from app.utils.formatters import format_currency, prepare_chart_data, format_date
from app.models.class_model import Class
from typing import Dict, List, Any, Optional, Union, Tuple
import pandas as pd
from flask import Response

logger = logging.getLogger(__name__)

# Create blueprint
class_bp = Blueprint('class', __name__, url_prefix='/classes')

@class_bp.route('/')
def index() -> str:
    """Class index page"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        business_rules = BusinessRulesService(data_loader)
        analytics = AnalyticsService(data_loader, business_rules)

        df = data_loader.get_data()

        # Check if Turma column exists
        if 'Turma' not in df.columns:
            logger.error("Column 'Turma' not found in dataframe")
            flash('Estrutura de dados inválida: coluna <PERSON> não encontrada', 'error')
            return render_template('classes/index.html',
                                  classes=[],
                                  total_classes=0,
                                  active_classes=0,
                                  total_students=0,
                                  active_students=0,
                                  avg_students_per_class=0,
                                  top_university_classes=[],
                                  class_status_data=[],
                                  class_by_course_data=[])

        # Filter out rows with empty Turma
        df_classes = df[df['Turma'].notna()]

        # Get unique classes
        unique_classes = df_classes['Turma'].unique()
        total_classes = len(unique_classes)

        # Count active classes (with at least one finalized implementation)
        active_classes = 0
        classes_list = []

        # Prepare data for charts
        class_status_counts = {'Ativo': 0, 'Pendente': 0, 'Inativo': 0}
        class_by_course = {}
        class_by_university = {}
        class_by_month = {}
        class_by_product = {}
        class_by_responsible = {}

        # Process each class
        for class_name in unique_classes:
            class_df = df_classes[df_classes['Turma'] == class_name]

            # Count students in this class
            class_students = class_df['Nome do Lead'].nunique() if 'Nome do Lead' in class_df.columns else 0

            # Determine class status based on implementation status
            class_status = 'Inativo'
            if 'Status_Implantacao' in class_df.columns:
                if (class_df['Status_Implantacao'] == 'Finalizado').any():
                    class_status = 'Ativo'
                    active_classes += 1
                elif (class_df['Status_Implantacao'] == 'Em Andamento').any():
                    class_status = 'Pendente'

            # Update status counts for chart
            class_status_counts[class_status] += 1

            # Get course and university - ensure they are strings
            course_value = class_df['Curso'].iloc[0] if 'Curso' in class_df.columns and not pd.isna(class_df['Curso'].iloc[0]) else 'Não especificado'
            course = str(course_value) if course_value is not None else 'Não especificado'

            university_value = class_df['Universidade'].iloc[0] if 'Universidade' in class_df.columns and not pd.isna(class_df['Universidade'].iloc[0]) else 'Não especificada'
            university = str(university_value) if university_value is not None else 'Não especificada'

            # Update course counts
            if course in class_by_course:
                class_by_course[course] += 1
            else:
                class_by_course[course] = 1

            # Update university counts
            if university in class_by_university:
                class_by_university[university] += 1
            else:
                class_by_university[university] = 1

            # Get product - ensure it's a string
            product_value = class_df['Produto'].iloc[0] if 'Produto' in class_df.columns and not pd.isna(class_df['Produto'].iloc[0]) else 'Não especificado'
            product = str(product_value) if product_value is not None else 'Não especificado'

            # Update product counts
            if product in class_by_product:
                class_by_product[product] += 1
            else:
                class_by_product[product] = 1

            # Get creation date and update monthly trends
            if 'Data_criacao_lead' in class_df.columns and not pd.isna(class_df['Data_criacao_lead'].iloc[0]):
                try:
                    creation_date = pd.to_datetime(class_df['Data_criacao_lead'].iloc[0])
                    month_key = f"{creation_date.year}-{creation_date.month:02d}"
                    if month_key in class_by_month:
                        class_by_month[month_key] += 1
                    else:
                        class_by_month[month_key] = 1
                except Exception as e:
                    logger.error(f"Error processing date: {e}")
                    pass

            # Get responsible and update responsible counts
            if 'Nome_Responsavel' in class_df.columns and not pd.isna(class_df['Nome_Responsavel'].iloc[0]):
                responsible = class_df['Nome_Responsavel'].iloc[0]
                if responsible in class_by_responsible:
                    class_by_responsible[responsible] += 1
                else:
                    class_by_responsible[responsible] = 1

            # Calculate revenue
            class_revenue = 0
            if 'Valor Mensalidade' in class_df.columns and 'Status_Implantacao' in class_df.columns:
                finalized_df = class_df[class_df['Status_Implantacao'] == 'Finalizado']
                for _, row in finalized_df.iterrows():
                    try:
                        value_str = str(row['Valor Mensalidade']).replace('R$', '').replace('.', '').replace(',', '.').strip()
                        class_revenue += float(value_str)
                    except (ValueError, TypeError):
                        pass

            # Create class object
            # Ensure class_name is properly converted to string for ID generation
            class_name_str = str(class_name) if class_name is not None else 'unknown'
            class_obj = {
                'id': f'class-{class_name_str}'.replace(' ', '-').lower(),
                'name': class_name_str,
                'students': class_students,
                'status': class_status,
                'course': course,
                'university': university,
                'product': product,
                'revenue': format_currency(class_revenue),
                'implementation_ids': class_df['Implantacao_id'].dropna().tolist() if 'Implantacao_id' in class_df.columns else [],
                'opportunity_ids': class_df['Oportunidade_id'].dropna().tolist() if 'Oportunidade_id' in class_df.columns else []
            }

            classes_list.append(class_obj)

        # Calculate total students
        total_students = df_classes['Nome do Lead'].nunique() if 'Nome do Lead' in df_classes.columns else 0

        # Calculate active students (with finalized implementations)
        active_students = 0
        if 'Status_Implantacao' in df_classes.columns and 'Nome do Lead' in df_classes.columns:
            active_students = df_classes[df_classes['Status_Implantacao'] == 'Finalizado']['Nome do Lead'].nunique()

        # Calculate average students per class
        avg_students_per_class = total_students / total_classes if total_classes > 0 else 0

        # Prepare chart data
        class_status_data = [
            {'name': 'Ativas', 'value': class_status_counts['Ativo']},
            {'name': 'Pendentes', 'value': class_status_counts['Pendente']},
            {'name': 'Inativas', 'value': class_status_counts['Inativo']}
        ]

        # Prepare course data for chart and filter
        class_by_course_data = [{'name': course, 'value': count} for course, count in class_by_course.items()]
        # Sort by name for better display in filter
        class_by_course_data = sorted(class_by_course_data, key=lambda x: x['name'])

        # Prepare course filter options in a format that's easy to use in the template
        course_options = [{'value': 'all', 'label': 'Todos'}]
        for course in sorted(class_by_course.keys()):
            # Ensure course is a string before applying string methods
            course_str = str(course) if course is not None else 'Não especificado'
            course_options.append({
                'value': course_str.lower().replace(' ', '-'),
                'label': course_str
            })

        # Prepare filters as JSON for the template
        filters_json = json.dumps([
            {
                "id": "status-filter",
                "type": "select",
                "placeholder": "Filtrar por status",
                "options": [
                    {"value": "all", "label": "Todos"},
                    {"value": "active", "label": "Ativo"},
                    {"value": "pending", "label": "Pendente"},
                    {"value": "inactive", "label": "Inativo"}
                ]
            },
            {
                "id": "course-filter",
                "type": "select",
                "placeholder": "Filtrar por curso",
                "options": course_options
            }
        ])

        # Get top universities by class count
        top_university_classes = [{'name': university, 'value': count} for university, count in sorted(class_by_university.items(), key=lambda x: x[1], reverse=True)[:5]]

        # Get top products by class count
        top_product_classes = [{'name': product, 'value': count} for product, count in sorted(class_by_product.items(), key=lambda x: x[1], reverse=True)[:5]]

        # Get top responsibles by class count
        top_responsible_classes = [{'name': responsible, 'value': count} for responsible, count in sorted(class_by_responsible.items(), key=lambda x: x[1], reverse=True)[:5]]

        # Prepare monthly trend data
        # Sort by month
        sorted_months = sorted(class_by_month.items())
        monthly_trend_data = [{'name': month, 'value': count} for month, count in sorted_months]

        # Prepare performance data for classes
        class_performance_data = []

        # Calculate MRR, conversion rate, and student count for each class
        for class_obj in classes_list:
            class_name = class_obj['name']
            # Ensure class_name is a string before applying string methods
            class_name_str = str(class_name) if class_name is not None else ''
            class_df = df[df['Turma'].fillna('').astype(str).str.lower() == class_name_str.lower()]

            # Calculate MRR
            mrr = 0
            if 'Valor Mensalidade' in class_df.columns and 'Status_Implantacao' in class_df.columns:
                finalized_df = class_df[class_df['Status_Implantacao'] == 'Finalizado']
                if 'Oportunidade_id' in finalized_df.columns:
                    finalized_df = finalized_df.drop_duplicates(subset=['Oportunidade_id'])

                for _, row in finalized_df.iterrows():
                    try:
                        value_str = str(row['Valor Mensalidade']).replace('R$', '').replace('.', '').replace(',', '.').strip()
                        if value_str and not value_str.isspace():
                            mrr += float(value_str)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Error converting value '{row.get('Valor Mensalidade', 'N/A')}' to float: {e}")

            # Calculate student count
            student_count = class_df['Nome do Lead'].nunique() if 'Nome do Lead' in class_df.columns else 0

            # Calculate conversion rate
            finalized_count = len(class_df[class_df['Status_Implantacao'] == 'Finalizado'].drop_duplicates(subset=['Oportunidade_id'])) if 'Status_Implantacao' in class_df.columns and 'Oportunidade_id' in class_df.columns else 0
            active_count = len(class_df[(class_df['Status_Implantacao'] != 'Finalizado') & (class_df['Status_Implantacao'] != 'Cancelado') & (class_df['Status_Implantacao'].notna())].drop_duplicates(subset=['Oportunidade_id'])) if 'Status_Implantacao' in class_df.columns and 'Oportunidade_id' in class_df.columns else 0
            opportunity_count = len(class_df[(class_df['Fase_Implantacao'].isna()) & (class_df['Oportunidade_id'].notna())].drop_duplicates(subset=['Oportunidade_id'])) if 'Fase_Implantacao' in class_df.columns and 'Oportunidade_id' in class_df.columns else 0

            conversion_rate = (finalized_count / (finalized_count + active_count + opportunity_count)) * 100 if (finalized_count + active_count + opportunity_count) > 0 else 0

            # Add to performance data if MRR > 0 (to show only classes with revenue)
            if mrr > 0:
                class_performance_data.append({
                    'name': class_name,
                    'mrr': mrr,
                    'students': student_count,
                    'conversion': round(conversion_rate, 1)
                })

        # Sort by MRR (descending) and take top 10
        class_performance_data = sorted(class_performance_data, key=lambda x: x['mrr'], reverse=True)[:10]

        # Prepare chart data
        class_mrr_chart_data = [{'name': item['name'], 'value': item['mrr']} for item in class_performance_data]
        class_students_chart_data = [{'name': item['name'], 'value': item['students']} for item in class_performance_data]
        class_conversion_chart_data = [{'name': item['name'], 'value': item['conversion']} for item in class_performance_data]

        return render_template('classes/index.html',
                              classes=classes_list,
                              total_classes=total_classes,
                              active_classes=active_classes,
                              total_students=total_students,
                              active_students=active_students,
                              avg_students_per_class=round(avg_students_per_class, 1),
                              top_university_classes=top_university_classes,
                              class_status_data=class_status_data,
                              class_by_course_data=class_by_course_data,
                              course_options=course_options,
                              filters_json=Markup(filters_json),  # Use Markup para evitar escape de caracteres
                              top_product_classes=top_product_classes,
                              top_responsible_classes=top_responsible_classes,
                              monthly_trend_data=monthly_trend_data,
                              class_mrr_chart_data=class_mrr_chart_data,
                              class_students_chart_data=class_students_chart_data,
                              class_conversion_chart_data=class_conversion_chart_data,
                              class_performance_data=class_performance_data)
    except Exception as e:
        logger.error(f"Error in class index page: {e}")
        flash(f'Error loading class data: {str(e)}', 'error')
        # Prepare default filters JSON for error case
        default_filters_json = json.dumps([
            {
                "id": "status-filter",
                "type": "select",
                "placeholder": "Filtrar por status",
                "options": [
                    {"value": "all", "label": "Todos"},
                    {"value": "active", "label": "Ativo"},
                    {"value": "pending", "label": "Pendente"},
                    {"value": "inactive", "label": "Inativo"}
                ]
            },
            {
                "id": "course-filter",
                "type": "select",
                "placeholder": "Filtrar por curso",
                "options": [{'value': 'all', 'label': 'Todos'}]
            }
        ])

        return render_template('classes/index.html',
                              classes=[],
                              total_classes=0,
                              active_classes=0,
                              total_students=0,
                              active_students=0,
                              avg_students_per_class=0,
                              top_university_classes=[],
                              class_status_data=[],
                              class_by_course_data=[],
                              course_options=[{'value': 'all', 'label': 'Todos'}],
                              filters_json=Markup(default_filters_json),
                              top_product_classes=[],
                              top_responsible_classes=[],
                              monthly_trend_data=[],
                              class_mrr_chart_data=[],
                              class_students_chart_data=[],
                              class_conversion_chart_data=[],
                              class_performance_data=[])

@class_bp.route('/<class_name>')
def detail(class_name: str) -> str:
    """
    # Check classes permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission
    
    if not check_permission('classes.view'):
        flash('Acesso negado. Permissão insuficiente para acessar esta página.', 'error')
        return redirect(url_for('auth.login'))
Class detail page"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        business_rules = BusinessRulesService(data_loader)
        analytics = AnalyticsService(data_loader, business_rules)

        df = data_loader.get_data()

        # Check if Turma column exists
        if 'Turma' not in df.columns:
            logger.error(f"Column 'Turma' not found in dataframe for class: {class_name}")
            flash('Estrutura de dados inválida: coluna Turma não encontrada', 'error')
            return redirect(url_for('class.index'))

        # Replace hyphens with spaces for matching
        class_name_for_match = class_name.replace('-', ' ')

        # Filter data for this class - usando uma comparação segura para evitar ambiguidade
        mask = df['Turma'].fillna('').astype(str).str.lower() == class_name_for_match.lower()
        class_df = df[mask]

        if len(class_df) == 0:
            logger.error(f"Class not found: {class_name}")
            flash(f'Turma não encontrada: {class_name}', 'error')
            return redirect(url_for('class.index'))

        # Get class details - ensure they are strings
        course_value = class_df['Curso'].iloc[0] if 'Curso' in class_df.columns and not pd.isna(class_df['Curso'].iloc[0]) else 'Não especificado'
        course = str(course_value) if course_value is not None else 'Não especificado'

        university_value = class_df['Universidade'].iloc[0] if 'Universidade' in class_df.columns and not pd.isna(class_df['Universidade'].iloc[0]) else 'Não especificada'
        university = str(university_value) if university_value is not None else 'Não especificada'

        university_city_value = class_df['Cidade'].iloc[0] if 'Cidade' in class_df.columns and not pd.isna(class_df['Cidade'].iloc[0]) else 'N/A'
        university_city = str(university_city_value) if university_city_value is not None else 'N/A'

        university_state_value = class_df['Estado'].iloc[0] if 'Estado' in class_df.columns and not pd.isna(class_df['Estado'].iloc[0]) else 'N/A'
        university_state = str(university_state_value) if university_state_value is not None else 'N/A'

        # Count students
        student_count = class_df['Nome do Lead'].nunique() if 'Nome do Lead' in class_df.columns else 0

        # Determine class status
        class_status = 'Inativo'
        if 'Status_Implantacao' in class_df.columns:
            if (class_df['Status_Implantacao'] == 'Finalizado').any():
                class_status = 'Ativo'
            elif (class_df['Status_Implantacao'] == 'Em Andamento').any():
                class_status = 'Pendente'

        # Get implementations
        finalized_implementations = []
        active_implementations = []
        opportunities = []

        if 'Status_Implantacao' in class_df.columns:
            # Finalized implementations
            finalized_df = class_df[class_df['Status_Implantacao'] == 'Finalizado'].drop_duplicates(subset=['Oportunidade_id'])
            finalized_count = len(finalized_df)

            for _, row in finalized_df.iterrows():
                finalized_implementations.append({
                    'lead_name': row.get('Nome do Lead', 'N/A'),
                    'product': row.get('Produto', 'N/A'),
                    'value': format_currency(row.get('Valor Mensalidade', 0)),
                    'end_date': format_date(row.get('DataPrevistaDeFinalização')),
                    'responsible': row.get('ResponsableOnboarding', 'N/A')
                })

            # Active implementations
            active_df = class_df[(class_df['Status_Implantacao'] != 'Finalizado') &
                               (class_df['Status_Implantacao'] != 'Cancelado') &
                               (class_df['Status_Implantacao'].notna())].drop_duplicates(subset=['Oportunidade_id'])
            active_count = len(active_df)

            for _, row in active_df.iterrows():
                active_implementations.append({
                    'lead_name': row.get('Nome do Lead', 'N/A'),
                    'product': row.get('Produto', 'N/A'),
                    'phase': row.get('Fase_Implantacao', 'N/A'),
                    'expected_end_date': format_date(row.get('DataPrevistaDeFinalização')),
                    'responsible': row.get('ResponsableOnboarding', 'N/A')
                })
        else:
            finalized_count = 0
            active_count = 0

        # Opportunities (no implementation phase)
        opp_df = class_df[(class_df['Fase_Implantacao'].isna()) &
                         (class_df['Oportunidade_id'].notna())].drop_duplicates(subset=['Oportunidade_id'])
        opportunity_count = len(opp_df)

        for _, row in opp_df.iterrows():
            opportunities.append({
                'lead_name': row.get('Nome do Lead', 'N/A'),
                'product': row.get('Produto', 'N/A'),
                'stage': row.get('Etapa do funil Comercial', 'N/A'),
                'value': format_currency(row.get('Valor Mensalidade', 0)),
                'responsible': row.get('Nome_Responsavel', 'N/A')
            })

        # Calculate MRR
        mrr_total = 0
        if 'Valor Mensalidade' in class_df.columns and 'Status_Implantacao' in class_df.columns:
            finalized_df = class_df[class_df['Status_Implantacao'] == 'Finalizado']
            # Deduplicate by Oportunidade_id to avoid counting the same opportunity multiple times
            if 'Oportunidade_id' in finalized_df.columns:
                finalized_df = finalized_df.drop_duplicates(subset=['Oportunidade_id'])

            for _, row in finalized_df.iterrows():
                try:
                    value_str = str(row['Valor Mensalidade']).replace('R$', '').replace('.', '').replace(',', '.').strip()
                    if value_str and not value_str.isspace():
                        mrr_total += float(value_str)
                except (ValueError, TypeError) as e:
                    logger.warning(f"Error converting value '{row.get('Valor Mensalidade', 'N/A')}' to float: {e}")

        # Calculate potential revenue
        potential_revenue = 0
        if 'Valor Mensalidade' in class_df.columns and 'Oportunidade_id' in class_df.columns:
            # Deduplicate by Oportunidade_id to avoid counting the same opportunity multiple times
            unique_df = class_df.drop_duplicates(subset=['Oportunidade_id'])

            for _, row in unique_df.iterrows():
                if pd.notna(row.get('Valor Mensalidade')):
                    try:
                        value_str = str(row['Valor Mensalidade']).replace('R$', '').replace('.', '').replace(',', '.').strip()
                        if value_str and not value_str.isspace():
                            potential_revenue += float(value_str)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Error converting value '{row.get('Valor Mensalidade', 'N/A')}' to float: {e}")

        # Calculate conversion rate
        conversion_rate = (finalized_count / (finalized_count + active_count + opportunity_count)) * 100 if (finalized_count + active_count + opportunity_count) > 0 else 0

        return render_template('classes/detail.html',
                              class_name=class_name_for_match,
                              course=course,
                              university=university,
                              university_city=university_city,
                              university_state=university_state,
                              class_status=class_status,
                              student_count=student_count,
                              finalized_count=finalized_count,
                              active_count=active_count,
                              opportunity_count=opportunity_count,
                              mrr_total=format_currency(mrr_total),
                              potential_revenue=format_currency(potential_revenue),
                              conversion_rate=round(conversion_rate, 1),
                              finalized_implementations=finalized_implementations,
                              active_implementations=active_implementations,
                              opportunities=opportunities)
    except Exception as e:
        logger.error(f"Error in class detail page: {e}")
        flash(f'Error loading class details: {str(e)}', 'error')
        return redirect(url_for('class.index'))
