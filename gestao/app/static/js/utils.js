
// Input validation added for security
function validateInput(input) {
    // Remove potentially dangerous characters
    return input.replace(/[<>"'&]/g, '');
}
/**
 * Amigo DataHub - Utility Functions
 * Utility functions for the frontend
 */

/**
 * Format a value as currency (R$)
 * @param {number} value - The value to format
 * @param {string} currency - The currency symbol (default: R$)
 * @returns {string} - The formatted currency string
 */
function formatCurrency(value, currency = 'R$') {
    if (value === null || value === undefined || isNaN(value)) {
        return `${currency} 0,00`;
    }
    
    return `${currency} ${parseFloat(value).toLocaleString('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })}`;
}

/**
 * Format a value as percentage
 * @param {number} value - The value to format
 * @param {number} decimals - The number of decimal places (default: 1)
 * @returns {string} - The formatted percentage string
 */
function formatPercentage(value, decimals = 1) {
    if (value === null || value === undefined || isNaN(value)) {
        return '0%';
    }
    
    return parseFloat(value).toLocaleString('pt-BR', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }) + '%';
}

/**
 * Format a number with thousand separators and decimal places
 * @param {number} value - The value to format
 * @param {number} decimals - The number of decimal places (default: 2)
 * @returns {string} - The formatted number string
 */
function formatNumber(value, decimals = 2) {
    if (value === null || value === undefined || isNaN(value)) {
        return '0';
    }
    
    return parseFloat(value).toLocaleString('pt-BR', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}

/**
 * Format a date
 * @param {string|Date} date - The date to format
 * @param {string} format - The format to use (default: dd/mm/yyyy)
 * @returns {string} - The formatted date string
 */
function formatDate(date, format = 'dd/mm/yyyy') {
    if (!date) {
        return 'Não informado';
    }
    
    try {
        const d = new Date(date);
        if (isNaN(d.getTime())) {
            return 'Data inválida';
        }
        
        const day = d.getDate().toString().padStart(2, '0');
        const month = (d.getMonth() + 1).toString().padStart(2, '0');
        const year = d.getFullYear();
        
        if (format === 'dd/mm/yyyy') {
            return `${day}/${month}/${year}`;
        } else if (format === 'mm/dd/yyyy') {
            return `${month}/${day}/${year}`;
        } else if (format === 'yyyy-mm-dd') {
            return `${year}-${month}-${day}`;
        }
        
        return `${day}/${month}/${year}`;
    } catch (error) {
        console.error('Error formatting date:', error);
        return 'Erro na data';
    }
}

/**
 * Safely divide two numbers
 * @param {number} numerator - The numerator
 * @param {number} denominator - The denominator
 * @param {number} defaultValue - The default value to return if division fails (default: 0)
 * @returns {number} - The result of the division or the default value
 */
function safeDivision(numerator, denominator, defaultValue = 0) {
    if (denominator === 0 || denominator === null || denominator === undefined || 
        numerator === null || numerator === undefined || 
        isNaN(numerator) || isNaN(denominator)) {
        return defaultValue;
    }
    
    return numerator / denominator;
}

/**
 * Safely calculate a percentage
 * @param {number} part - The part
 * @param {number} total - The total
 * @param {number} defaultValue - The default value to return if calculation fails (default: 0)
 * @returns {number} - The percentage or the default value
 */
function safePercentage(part, total, defaultValue = 0) {
    return safeDivision(part, total, defaultValue) * 100;
}

/**
 * Safely get a value from an object
 * @param {object} obj - The object
 * @param {string} key - The key
 * @param {*} defaultValue - The default value to return if key is not found (default: null)
 * @returns {*} - The value or the default value
 */
function safeGet(obj, key, defaultValue = null) {
    if (obj === null || obj === undefined) {
        return defaultValue;
    }
    
    return obj[key] !== undefined ? obj[key] : defaultValue;
}

/**
 * Safely parse a string to integer
 * @param {string} value - The string to parse
 * @param {number} defaultValue - The default value to return if parsing fails (default: 0)
 * @returns {number} - The parsed integer or the default value
 */
function safeParseInt(value, defaultValue = 0) {
    if (value === undefined || value === null) {
        return defaultValue;
    }
    
    try {
        const stringValue = String(value).trim();
        const parsed = parseInt(stringValue.replace(/[^\d-]/g, ''));
        return isNaN(parsed) ? defaultValue : parsed;
    } catch (error) {
        console.error('Error parsing integer:', error);
        return defaultValue;
    }
}

/**
 * Safely parse a string to float
 * @param {string} value - The string to parse
 * @param {number} defaultValue - The default value to return if parsing fails (default: 0)
 * @returns {number} - The parsed float or the default value
 */
function safeParseFloat(value, defaultValue = 0) {
    if (value === undefined || value === null) {
        return defaultValue;
    }
    
    try {
        const stringValue = String(value).trim();
        const parsed = parseFloat(stringValue.replace(/[^\d.,\-]/g, '').replace(',', '.'));
        return isNaN(parsed) ? defaultValue : parsed;
    } catch (error) {
        console.error('Error parsing float:', error);
        return defaultValue;
    }
}

/**
 * Initialize table filters
 * @param {string} tableId - The ID of the table
 * @param {object} filterSelects - An object mapping filter select IDs to column indices
 * @param {string} searchInputId - The ID of the search input
 * @param {number} searchColumnIndex - The index of the column to search in
 */
function initTableFilters(tableId, filterSelects, searchInputId, searchColumnIndex) {
    const table = document.getElementById(tableId);
    const searchInput = document.getElementById(searchInputId);
    
    if (!table) {
        console.error(`Table with ID ${tableId} not found`);
        return;
    }
    
    function filterTable() {
        const rows = table.querySelectorAll('tbody tr');
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
        
        rows.forEach(row => {
            let showRow = true;
            
            // Apply search filter
            if (searchInput && searchTerm) {
                const cell = row.querySelector(`td:nth-child(${searchColumnIndex + 1})`);
                if (cell) {
                    const text = cell.textContent.toLowerCase();
                    if (!text.includes(searchTerm)) {
                        showRow = false;
                    }
                }
            }
            
            // Apply select filters
            if (filterSelects && showRow) {
                Object.keys(filterSelects).forEach(selectId => {
                    const select = document.getElementById(selectId);
                    if (select && select.value) {
                        const columnIndex = filterSelects[selectId];
                        const cell = row.querySelector(`td:nth-child(${columnIndex + 1})`);
                        if (cell) {
                            const text = cell.textContent.trim();
                            if (text !== select.value) {
                                showRow = false;
                            }
                        }
                    }
                });
            }
            
            row.style.display = showRow ? '' : 'none';
        });
    }
    
    if (searchInput) {
        searchInput.addEventListener('input', filterTable);
    }
    
    if (filterSelects) {
        Object.keys(filterSelects).forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                select.addEventListener('change', filterTable);
            }
        });
    }
}

// Export functions to global scope
window.AmigoDH = {
    formatCurrency,
    formatPercentage,
    formatNumber,
    formatDate,
    safeDivision,
    safePercentage,
    safeGet,
    safeParseInt,
    safeParseFloat,
    initTableFilters
};
