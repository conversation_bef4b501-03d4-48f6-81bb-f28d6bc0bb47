{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table, table_filters, business_tooltip %}

{% block title %}Universidades - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Análise de Universidades",
    subtitle="Acompanhe o desempenho por universidade, visualize métricas de conversão e receita recorrente.",
    stats=[
        {
            "label": "Universidades Ativas",
            "value": active_universities_count,
            "icon": '<path d="M12 14l9-5-9-5-9 5 9 5z" /><path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />'
        },
        {
            "label": "Alunos Ativos",
            "value": active_students_count,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />'
        }
    ],
    bg_class="bg-gray-100"
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- KPI: Total de Universidades -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-primary-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path d="M12 14l9-5-9-5-9 5 9 5z" /><path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Total de Universidades</p>
                        {{ business_tooltip(
                            title="Total de Universidades",
                            description="Número total de universidades cadastradas no sistema, incluindo todas as instituições que possuem pelo menos um lead registrado.",
                            formula="COUNT(DISTINCT Universidade)",
                            columns="Universidade",
                            data_source="base_dados.csv → Contagem de universidades únicas",
                            calculation_method="Contagem simples de valores únicos na coluna Universidade"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ total_universities }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Universidades cadastradas</p>
        </div>

        <!-- KPI: Universidades Ativas -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path d="M12 14l9-5-9-5-9 5 9 5z" /><path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Universidades Ativas</p>
                        {{ business_tooltip(
                            title="Universidades Ativas",
                            description="Universidades que possuem pelo menos uma oportunidade de negócio ativa ou implementação em andamento, indicando engajamento comercial.",
                            formula="COUNT(DISTINCT Universidade WHERE Oportunidade_id IS NOT NULL OR Data_Criacao_Implantacao IS NOT NULL)",
                            columns="Universidade, Oportunidade_id, Data_Criacao_Implantacao",
                            data_source="base_dados.csv → Contagem de universidades com atividade comercial",
                            calculation_method="Contagem de universidades únicas que possuem oportunidades ou implementações"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ active_universities_count }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Com pelo menos uma oportunidade</p>
        </div>

        <!-- KPI: Receita Recorrente -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-primary-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Receita Recorrente</p>
                        {{ business_tooltip(
                            title="Receita Recorrente (MRR Total)",
                            description="Receita mensal recorrente total gerada por todas as universidades com implementações finalizadas, representando a base de receita estável.",
                            formula="SUM(Valor_Mensalidade WHERE Status_Implantacao = 'Finalizado') GROUP BY Universidade",
                            columns="Valor_Mensalidade, Status_Implantacao, Universidade",
                            data_source="base_dados.csv → Soma dos valores de mensalidade por universidade",
                            calculation_method="Soma de todos os valores de mensalidade das implementações finalizadas, agrupados por universidade"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ recurring_revenue }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">MRR total</p>
        </div>

        <!-- KPI: Receita Potencial -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Receita Potencial</p>
                        {{ business_tooltip(
                            title="Receita Potencial",
                            description="Receita mensal estimada que será gerada quando todas as implementações ativas das universidades forem finalizadas.",
                            formula="SUM(Valor_Mensalidade WHERE Status_Implantacao NOT IN ('Finalizado', 'Cancelado')) GROUP BY Universidade",
                            columns="Valor_Mensalidade, Status_Implantacao, Universidade",
                            data_source="base_dados.csv → Soma dos valores de mensalidade das implementações ativas por universidade",
                            calculation_method="Soma de todos os valores de mensalidade das implementações em andamento, agrupados por universidade"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ potential_revenue }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Implantações ativas</p>
        </div>
    </div>

    <!-- Top Insights -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Top University -->
        {{ insight_card(
            title="Universidade com Mais Leads",
            content="<strong>" + top_university_name + "</strong> representa <strong>" + top_university_percentage + "%</strong> do total de leads.",
            tip="Considere estratégias específicas para esta universidade para maximizar conversões."
        )|e }}

        <!-- Top Course -->
        {{ insight_card(
            title="Curso Mais Popular",
            content="<strong>" + top_course_name + "</strong> com <strong>" + top_course_count + "</strong> leads.",
            tip="Adapte sua abordagem comercial para atender às necessidades específicas deste curso."
        )|e }}

        <!-- Top Revenue -->
        {{ insight_card(
            title="Maior Receita por Universidade",
            content="<strong>" + top_revenue_university + "</strong> gera <strong>" + top_revenue_value + "</strong> em receita mensal recorrente.",
            tip="Analise os fatores de sucesso desta universidade para replicar em outras."
        )|e }}
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- MRR by University Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">MRR por Universidade</h3>
                {{ business_tooltip(
                    title="MRR por Universidade",
                    description="Distribuição da receita mensal recorrente por universidade, mostrando quais instituições geram mais receita estável.",
                    formula="SUM(Valor_Mensalidade WHERE Status_Implantacao = 'Finalizado') GROUP BY Universidade ORDER BY SUM DESC",
                    columns="Valor_Mensalidade, Status_Implantacao, Universidade",
                    data_source="base_dados.csv → Agrupamento de receita por universidade",
                    calculation_method="Soma dos valores de mensalidade das implementações finalizadas, agrupados e ordenados por universidade"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Distribuição da receita recorrente por universidade</p>
            <div class="h-64">
                <canvas id="mrrByUniversityChart"></canvas>
            </div>
        </div>

        <!-- Students by University Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Alunos por Universidade</h3>
                {{ business_tooltip(
                    title="Alunos Ativos por Universidade",
                    description="Distribuição do número de alunos ativos (usuários gerando notas fiscais) por universidade, indicando penetração e adoção.",
                    formula="COUNT(Lead_id WHERE Status_Implantacao = 'Finalizado' AND Gerou_Nota_Fiscal = 'Sim') GROUP BY Universidade",
                    columns="Lead_id, Status_Implantacao, Gerou_Nota_Fiscal, Universidade",
                    data_source="base_dados.csv → Contagem de usuários ativos por universidade",
                    calculation_method="Contagem de usuários únicos com implementação finalizada e notas fiscais, agrupados por universidade"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Distribuição de alunos ativos por universidade</p>
            <div class="h-64">
                <canvas id="studentsByUniversityChart"></canvas>
            </div>
        </div>
    </div>

    <!-- MRR por Universidade Table -->
    <div class="mb-8">
        {{ section_header(
            title="MRR por Universidade",
            subtitle="Distribuição da receita mensal recorrente por universidade"
        )|e }}

        <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="mrr-university-table">
                    <thead class="bg-gray-100">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Universidade</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">MRR</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">% do Total</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Ações</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for university, mrr in mrr_by_university.items() %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ university|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ mrr|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ mrr_percentage_by_university[university]|e }}%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div class="flex space-x-3">
                                    <button type="button"
                                            onclick="showUniversityDetails('{{ university|e }}')"
                                            class="text-primary hover:underline inline-flex items-center">
                                        <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                        Resumo
                                    </button>
                                    <a href="{{ url_for('university.detail', university=university) }}"
                                       class="text-blue-600 hover:underline inline-flex items-center">
                                        <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                        Página Detalhada
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Conversão por Estado -->
    <div class="mb-8">
        {{ section_header(
            title="Conversão por Estado",
            subtitle="Taxa de conversão de leads para implantações por estado"
        )|e }}

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Conversão por Estado Chart -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <div class="h-64">
                    <canvas id="conversionByStateChart"></canvas>
                </div>
            </div>

            <!-- Análise de Gargalos -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <h3 class="text-base font-medium text-gray-900 mb-1">Análise de Gargalos</h3>
                <p class="text-sm text-gray-500 mb-4">Identificação de pontos de queda no funil de conversão</p>
                <div class="h-64">
                    <canvas id="bottleneckAnalysisChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Metric: Implantações Ativas -->
        {{ stat_card(
            title="Implantações Ativas",
            value=active_implementations_count,
            subtitle="Em andamento",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="primary"
        )|e }}

        <!-- Metric: Implantações Finalizadas -->
        {{ stat_card(
            title="Implantações Finalizadas",
            value=finalized_implementations_count,
            subtitle="Gerando receita recorrente",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="success"
        )|e }}

        <!-- Metric: Ticket Médio -->
        {{ stat_card(
            title="Ticket Médio",
            value=avg_ticket,
            subtitle="Por implantação finalizada",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="primary"
        )|e }}

        <!-- Metric: Receita Média por Universidade -->
        {{ stat_card(
            title="Receita Média por Universidade",
            value=avg_revenue_per_university,
            subtitle="Universidades ativas",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="success"
        )|e }}

        <!-- Metric: Taxa de Conversão Média -->
        {{ stat_card(
            title="Taxa de Conversão Média",
            value=(avg_conversion_rate|float|round(1))|string + "%",
            subtitle="Oportunidades → Implantações",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />',
            color="success"
        ) }}
    </div>

    <!-- Modal para detalhes da universidade -->
    <div id="universityDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-900" id="universityDetailsTitle">Detalhes da Universidade</h2>
                    <button type="button" onclick="closeUniversityDetails()" class="text-gray-400 hover:text-gray-500">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div id="universityDetailsContent">
                    <!-- Conteúdo será preenchido via JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded for universities page');

        // Parse chart data
        let chartData;
        try {
            chartData = JSON.parse('{{ chart_data|e }}');
            console.log('Chart data parsed successfully:', chartData);
        } catch (error) {
            console.error('Error parsing chart data:', error);
            chartData = {};
        }

        // Create MRR by university chart
        try {
            const mrrByUniversity = chartData.mrr_by_university || {};
            const universityLabels = Object.keys(mrrByUniversity);
            const mrrValues = Object.values(mrrByUniversity).map(v => parseFloat(v.replace(/[^0-9,]/g, '').replace(',', '.')));

            AmigoDH.createBarChart('mrrByUniversityChart', universityLabels, mrrValues, 'MRR (R$)', {
                backgroundColor: AmigoDH.colors.blue[2],
                borderColor: AmigoDH.colors.primary,
                tooltipCallback: function(value) {
                    return 'MRR: ' + AmigoDH.formatCurrency(value);
                },
                yAxisCallback: function(value) {
                    return AmigoDH.formatCurrency(value);
                }
            });
            console.log('MRR by university chart created successfully');
        } catch (error) {
            console.error('Error creating MRR by university chart:', error);
            window.debugChart('mrrByUniversityChart', {}, error);
        }

        // Create students by university chart
        try {
            const studentsByUniversity = chartData.students_by_university || {};

            if (Object.keys(studentsByUniversity).length === 0) {
                document.getElementById('studentsByUniversityChart').parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full">' +
                    '<p class="text-gray-500 text-sm">Dados não disponíveis</p>' +
                    '</div>';
                console.warn('No data available for students by university chart');
                return;
            }

            const universityLabels = Object.keys(studentsByUniversity);
            const studentValues = Object.values(studentsByUniversity).map(v => parseInt(v));

            AmigoDH.createPieChart('studentsByUniversityChart', universityLabels, studentValues, {
                backgroundColor: AmigoDH.colors.blue
            });
            console.log('Students by university chart created successfully');
        } catch (error) {
            console.error('Error creating students by university chart:', error);
            window.debugChart('studentsByUniversityChart', {}, error);
            document.getElementById('studentsByUniversityChart').parentNode.innerHTML =
                '<div class="flex items-center justify-center h-full">' +
                '<p class="text-gray-500 text-sm">Erro ao carregar dados</p>' +
                '</div>';
        }

        // Create conversion by state chart
        try {
            const stateConversion = chartData.state_conversion || {};
            const stateLabels = Object.keys(stateConversion);
            const conversionValues = Object.values(stateConversion).map(v => parseFloat(v.replace(',', '.')));

            AmigoDH.createBarChart('conversionByStateChart', stateLabels, conversionValues, 'Taxa de Conversão (%)', {
                backgroundColor: AmigoDH.colors.blue[4],
                borderColor: AmigoDH.colors.blue[6],
                tooltipCallback: function(value) {
                    return 'Conversão: ' + AmigoDH.formatPercentage(value) + '%';
                },
                yAxisCallback: function(value) {
                    return value + '%';
                }
            });
            console.log('Conversion by state chart created successfully');
        } catch (error) {
            console.error('Error creating conversion by state chart:', error);
            window.debugChart('conversionByStateChart', {}, error);
        }

        // Create bottleneck analysis chart
        try {
            const bottleneckData = chartData.conversion_dropoff || {};

            if (Object.keys(bottleneckData).length === 0) {
                document.getElementById('bottleneckAnalysisChart').parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full">' +
                    '<p class="text-gray-500 text-sm">Dados não disponíveis</p>' +
                    '</div>';
                console.warn('No data available for bottleneck analysis chart');
                return;
            }

            // Prepare data for funnel chart
            const funnelStages = [];
            const funnelValues = [];
            const dropoffPercentages = [];

            // Get funnel stages distribution
            const stagesData = chartData.funnel_stages || {};
            const phasesData = chartData.implementation_phases || {};

            // Combine all stages in order
            if (Object.keys(stagesData).length > 0) {
                funnelStages.push('Leads');
                funnelValues.push(parseInt(chartData.total_leads || 0));
                dropoffPercentages.push(0);

                // Add opportunity stages
                Object.keys(stagesData).forEach(stage => {
                    funnelStages.push(stage);
                    funnelValues.push(parseInt(stagesData[stage]));

                    // Calculate dropoff from previous stage
                    const prevValue = funnelValues[funnelValues.length - 2];
                    const currentValue = funnelValues[funnelValues.length - 1];
                    const dropoff = prevValue > 0 ? Math.round((1 - (currentValue / prevValue)) * 100) : 0;
                    dropoffPercentages.push(dropoff);
                });

                // Add implementation phases if available
                if (Object.keys(phasesData).length > 0) {
                    Object.keys(phasesData).forEach(phase => {
                        funnelStages.push(phase);
                        funnelValues.push(parseInt(phasesData[phase]));

                        // Calculate dropoff from previous stage
                        const prevValue = funnelValues[funnelValues.length - 2];
                        const currentValue = funnelValues[funnelValues.length - 1];
                        const dropoff = prevValue > 0 ? Math.round((1 - (currentValue / prevValue)) * 100) : 0;
                        dropoffPercentages.push(dropoff);
                    });
                }
            }

            // If no data available after processing
            if (funnelStages.length === 0) {
                document.getElementById('bottleneckAnalysisChart').parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full">' +
                    '<p class="text-gray-500 text-sm">Dados insuficientes para análise</p>' +
                    '</div>';
                console.warn('Insufficient data for bottleneck analysis chart');
                return;
            }

            // Generate colors array based on number of stages
            const colors = [];
            for (let i = 0; i < funnelStages.length; i++) {
                const colorIndex = Math.min(i, AmigoDH.colors.blue.length - 1);
                colors.push(AmigoDH.colors.blue[colorIndex]);
            }

            AmigoDH.createFunnelChart('bottleneckAnalysisChart', funnelStages, funnelValues, 'Funil de Conversão', {
                backgroundColor: colors,
                tooltipCallback: function(value, index) {
                    if (index === 0) {
                        return 'Total: ' + value;
                    } else {
                        return 'Total: ' + value + ' (Queda: ' + dropoffPercentages[index] + '%)';
                    }
                }
            });
            console.log('Bottleneck analysis chart created successfully');
        } catch (error) {
            console.error('Error creating bottleneck analysis chart:', error);
            window.debugChart('bottleneckAnalysisChart', {}, error);
            document.getElementById('bottleneckAnalysisChart').parentNode.innerHTML =
                '<div class="flex items-center justify-center h-full">' +
                '<p class="text-gray-500 text-sm">Erro ao carregar dados</p>' +
                '</div>';
        }
    });

    // Função para mostrar detalhes da universidade
    function showUniversityDetails(university) {
        // Atualiza o título do modal
        document.getElementById('universityDetailsTitle').textContent = university;

        // Exibe o modal com mensagem de carregamento
        document.getElementById('universityDetailsContent').innerHTML = `
            <div class="flex items-center justify-center h-32">
                <p class="text-gray-500">Carregando dados...</p>
            </div>
        `;

        document.getElementById('universityDetailsModal').classList.remove('hidden');
        document.getElementById('universityDetailsModal').classList.add('flex');

        // Buscar dados da universidade via fetch
        fetch(`/api/university/${encodeURIComponent(university)}/details`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro ao carregar dados');
                }
                return response.json();
            })
            .then(data => {
                // Verificar se há erro nos dados
                if (data.error) {
                    let errorContent = `
                    <div class="bg-yellow-50 p-4 rounded-lg text-center mb-6">
                        <p class="text-yellow-700">${data.error}</p>
                        <p class="text-gray-500 mt-2">Os dados detalhados podem estar incompletos.</p>
                    </div>`;

                    // Se não houver dados, exibir apenas a mensagem de erro
                    if (data.finalized_count === 0 && data.active_count === 0 && data.opportunity_count === 0) {
                        document.getElementById('universityDetailsContent').innerHTML = errorContent;
                        return;
                    }

                    // Se houver alguns dados, exibir a mensagem de erro e continuar com os dados disponíveis
                    document.getElementById('universityDetailsContent').innerHTML = errorContent;
                }

                // Gerar conteúdo com dados reais
                let content = '';

                // Resumo
                content += `
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Resumo</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-600 mb-1">Implantações Finalizadas</p>
                            <p class="text-xl font-semibold text-gray-900">${data.finalized_count || 0}</p>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-600 mb-1">Implantações em Andamento</p>
                            <p class="text-xl font-semibold text-gray-900">${data.active_count || 0}</p>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-600 mb-1">Oportunidades Ativas</p>
                            <p class="text-xl font-semibold text-gray-900">${data.opportunity_count || 0}</p>
                        </div>
                    </div>
                </div>`;

                // Implantações Finalizadas
                content += `
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Implantações Finalizadas</h3>`;

                if (data.finalized_implementations && data.finalized_implementations.length > 0) {
                    content += `
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Lead</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Produto</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Valor</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Data Finalização</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">`;

                    data.finalized_implementations.forEach(impl => {
                        content += `
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-2 text-sm text-gray-900">${impl.lead_name || 'N/A'}</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">${impl.product || 'N/A'}</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">${impl.value || 'N/A'}</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">${impl.finalized_date || 'N/A'}</td>
                                </tr>`;
                    });

                    content += `
                            </tbody>
                        </table>
                    </div>`;
                } else {
                    content += `
                    <div class="bg-gray-50 p-4 rounded-lg text-center">
                        <p class="text-gray-500">Nenhuma implantação finalizada encontrada</p>
                    </div>`;
                }

                content += `</div>`;

                // Implantações em Andamento
                content += `
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Implantações em Andamento</h3>`;

                if (data.active_implementations && data.active_implementations.length > 0) {
                    content += `
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Lead</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Produto</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Status</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Progresso</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">`;

                    data.active_implementations.forEach(impl => {
                        // Calculate progress based on status position
                        const progress = impl.progress || 50; // Default to 50% if not provided

                        content += `
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-2 text-sm text-gray-900">${impl.lead_name || 'N/A'}</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">${impl.product || 'N/A'}</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">${impl.status || 'N/A'}</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">
                                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                                            <div class="bg-primary h-2.5 rounded-full" style="width: ${progress}%"></div>
                                        </div>
                                    </td>
                                </tr>`;
                    });

                    content += `
                            </tbody>
                        </table>
                    </div>`;
                } else {
                    content += `
                    <div class="bg-gray-50 p-4 rounded-lg text-center">
                        <p class="text-gray-500">Nenhuma implantação em andamento encontrada</p>
                    </div>`;
                }

                content += `</div>`;

                // Oportunidades Ativas
                content += `
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Oportunidades Ativas</h3>`;

                if (data.opportunities && data.opportunities.length > 0) {
                    content += `
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Lead</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Produto</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Etapa do Funil</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Valor</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">`;

                    data.opportunities.forEach(opp => {
                        content += `
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-2 text-sm text-gray-900">${opp.lead_name || 'N/A'}</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">${opp.product || 'N/A'}</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">${opp.stage || 'N/A'}</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">${opp.value || 'N/A'}</td>
                                </tr>`;
                    });

                    content += `
                            </tbody>
                        </table>
                    </div>`;
                } else {
                    content += `
                    <div class="bg-gray-50 p-4 rounded-lg text-center">
                        <p class="text-gray-500">Nenhuma oportunidade ativa encontrada</p>
                    </div>`;
                }

                content += `</div>`;

                // Atualiza o conteúdo do modal
                document.getElementById('universityDetailsContent').innerHTML = content;
            })
            .catch(error => {
                console.error('Error fetching university details:', error);

                // Tentar novamente com a URL alternativa (para compatibilidade)
                console.log('Trying alternative URL...');
                fetch(`/universities/api/university/${encodeURIComponent(university)}/details`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Erro ao carregar dados (URL alternativa)');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Processar os dados da mesma forma que a função principal
                        // Verificar se há erro nos dados
                        if (data.error) {
                            let errorContent = `
                            <div class="bg-yellow-50 p-4 rounded-lg text-center mb-6">
                                <p class="text-yellow-700">${data.error}</p>
                                <p class="text-gray-500 mt-2">Os dados detalhados podem estar incompletos.</p>
                            </div>`;

                            // Se não houver dados, exibir apenas a mensagem de erro
                            if (data.finalized_count === 0 && data.active_count === 0 && data.opportunity_count === 0) {
                                document.getElementById('universityDetailsContent').innerHTML = errorContent;
                                return;
                            }

                            // Se houver alguns dados, exibir a mensagem de erro e continuar com os dados disponíveis
                            document.getElementById('universityDetailsContent').innerHTML = errorContent;
                        }

                        // Continuar com o processamento normal dos dados
                        // Código idêntico ao da função principal
                        let content = '';

                        // Resumo
                        content += `
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Resumo</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <p class="text-sm text-gray-600 mb-1">Implantações Finalizadas</p>
                                    <p class="text-xl font-semibold text-gray-900">${data.finalized_count || 0}</p>
                                </div>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <p class="text-sm text-gray-600 mb-1">Implantações em Andamento</p>
                                    <p class="text-xl font-semibold text-gray-900">${data.active_count || 0}</p>
                                </div>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <p class="text-sm text-gray-600 mb-1">Oportunidades Ativas</p>
                                    <p class="text-xl font-semibold text-gray-900">${data.opportunity_count || 0}</p>
                                </div>
                            </div>
                        </div>`;

                        // Implantações Finalizadas
                        content += `
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Implantações Finalizadas</h3>`;

                        if (data.finalized_implementations && data.finalized_implementations.length > 0) {
                            content += `
                            <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-100">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Lead</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Produto</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Valor</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Data Finalização</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">`;

                            data.finalized_implementations.forEach(impl => {
                                content += `
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-4 py-2 text-sm text-gray-900">${impl.lead_name || 'N/A'}</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">${impl.product || 'N/A'}</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">${impl.value || 'N/A'}</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">${impl.finalized_date || 'N/A'}</td>
                                        </tr>`;
                            });

                            content += `
                                    </tbody>
                                </table>
                            </div>`;
                        } else {
                            content += `
                            <div class="bg-gray-50 p-4 rounded-lg text-center">
                                <p class="text-gray-500">Nenhuma implantação finalizada encontrada</p>
                            </div>`;
                        }

                        content += `</div>`;

                        // Implantações em Andamento
                        content += `
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Implantações em Andamento</h3>`;

                        if (data.active_implementations && data.active_implementations.length > 0) {
                            content += `
                            <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-100">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Lead</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Produto</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Status</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Progresso</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">`;

                            data.active_implementations.forEach(impl => {
                                // Calculate progress based on status position
                                const progress = impl.progress || 50; // Default to 50% if not provided

                                content += `
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-4 py-2 text-sm text-gray-900">${impl.lead_name || 'N/A'}</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">${impl.product || 'N/A'}</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">${impl.status || 'N/A'}</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">
                                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                                    <div class="bg-primary h-2.5 rounded-full" style="width: ${progress}%"></div>
                                                </div>
                                            </td>
                                        </tr>`;
                            });

                            content += `
                                    </tbody>
                                </table>
                            </div>`;
                        } else {
                            content += `
                            <div class="bg-gray-50 p-4 rounded-lg text-center">
                                <p class="text-gray-500">Nenhuma implantação em andamento encontrada</p>
                            </div>`;
                        }

                        content += `</div>`;

                        // Oportunidades Ativas
                        content += `
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Oportunidades Ativas</h3>`;

                        if (data.opportunities && data.opportunities.length > 0) {
                            content += `
                            <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-100">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Lead</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Produto</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Etapa do Funil</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Valor</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">`;

                            data.opportunities.forEach(opp => {
                                content += `
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-4 py-2 text-sm text-gray-900">${opp.lead_name || 'N/A'}</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">${opp.product || 'N/A'}</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">${opp.stage || 'N/A'}</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">${opp.value || 'N/A'}</td>
                                        </tr>`;
                            });

                            content += `
                                    </tbody>
                                </table>
                            </div>`;
                        } else {
                            content += `
                            <div class="bg-gray-50 p-4 rounded-lg text-center">
                                <p class="text-gray-500">Nenhuma oportunidade ativa encontrada</p>
                            </div>`;
                        }

                        content += `</div>`;

                        // Atualiza o conteúdo do modal
                        document.getElementById('universityDetailsContent').innerHTML = content;
                    })
                    .catch(fallbackError => {
                        console.error('Error fetching university details (fallback):', fallbackError);
                        document.getElementById('universityDetailsContent').innerHTML = `
                            <div class="bg-red-50 p-4 rounded-lg text-center">
                                <p class="text-red-500">Erro ao carregar dados: ${error.message}</p>
                                <p class="text-gray-500 mt-2">Os dados detalhados não estão disponíveis no momento. Por favor, tente novamente mais tarde.</p>
                            </div>
                        `;
                    });
            });
    }

    // Função para fechar o modal de detalhes
    function closeUniversityDetails() {
        document.getElementById('universityDetailsModal').classList.remove('flex');
        document.getElementById('universityDetailsModal').classList.add('hidden');
    }
</script>
{% endblock %}
