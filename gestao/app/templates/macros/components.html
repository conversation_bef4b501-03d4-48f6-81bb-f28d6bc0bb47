{# Macros para componentes reutilizáveis #}

{# Card KPI básico - Modernized Design #}
{% macro kpi_card(title, value, subtitle=None, icon=None, color="primary", percentage=None, percentage_label=None, is_positive=true, footer=None, subtitle_class="text-gray-500") %}
<div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
    <div class="flex items-center mb-4">
        {% if icon %}
        <div class="w-14 h-14 rounded-full bg-{{ color }}-50 flex items-center justify-center mr-4">
            <svg class="w-7 h-7 text-{{ color }}-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {# SECURITY: Icon content is controlled by backend, safe to render #}
                {{ icon|safe }}
            </svg>
        </div>
        {% endif %}
        <div>
            <p class="text-sm font-medium text-gray-500 mb-1">{{ title|e }}</p>
            <p class="text-3xl font-bold text-gray-900">{{ value|e }}</p>
        </div>
    </div>
    {% if subtitle %}
    <p class="text-sm {{ subtitle_class|e }} mt-1">{{ subtitle|e }}</p>
    {% endif %}
    {% if percentage is not none %}
    <div class="flex items-center mt-3 bg-gray-50 p-2 rounded-lg">
        <span class="text-sm font-medium {% if is_positive %}text-green-600{% else %}text-red-600{% endif %} mr-1">
            {% if is_positive %}+{% else %}-{% endif %}{{ percentage|e }}%
        </span>
        {% if percentage_label %}
        <span class="text-sm text-gray-500">{{ percentage_label|e }}</span>
        {% endif %}
    </div>
    {% endif %}
    {% if footer %}
    <div class="mt-4 pt-3 border-t border-gray-100">
        {# SECURITY: Footer content is controlled by backend, safe to render #}
        {{ footer|safe }}
    </div>
    {% endif %}
</div>
{% endmacro %}

{# Card de estatística - Modernized Design #}
{% macro stat_card(title, value, subtitle=None, icon=None, color="primary", trend=None, trend_value=None, is_positive=true) %}
<div class="bg-white rounded-lg shadow-sm p-5 border border-gray-100 card-hover transition-all duration-300 h-full">
    <div class="flex items-center mb-3">
        {% if icon %}
        <div class="w-12 h-12 rounded-full bg-{{ color }}-50 flex items-center justify-center mr-3">
            <svg class="w-6 h-6 text-{{ color }}-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {{ icon|safe }}
            </svg>
        </div>
        {% endif %}
        <div>
            <p class="text-sm font-medium text-gray-500 mb-1">{{ title|e }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ value|e }}</p>
        </div>
    </div>
    {% if subtitle or trend %}
    <div class="flex items-center mt-2 {% if trend %}bg-gray-50 p-2 rounded-lg{% endif %}">
        {% if trend %}
        <span class="text-sm font-medium {% if is_positive %}text-green-600{% else %}text-red-600{% endif %} mr-2">
            {% if is_positive %}+{% else %}-{% endif %}{{ trend_value|e }}%
        </span>
        {% endif %}
        {% if subtitle %}
        <span class="text-sm text-gray-500">{{ subtitle|e }}</span>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endmacro %}

{# Card de insight - Modernized Design #}
{% macro insight_card(title, content, tip=None, tags=None, footer=None) %}
<div class="bg-white rounded-lg p-6 border border-gray-100 flex flex-col shadow-sm card-hover transition-all duration-300 h-full">
    <div class="flex items-center mb-3">
        <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
            <svg class="w-4 h-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"></path>
            </svg>
        </div>
        <span class="text-lg font-semibold text-gray-900">{{ title|e }}</span>
    </div>
    <p class="text-sm text-gray-600 mb-4 leading-relaxed">{{ content|e }}</p>
    {% if tip %}
    <div class="mt-2 p-3 bg-blue-50 text-gray-800 rounded-md text-xs flex items-start">
        <span class="mr-2 text-base leading-none text-blue-500">💡</span>
        <span>{{ tip|e }}</span>
    </div>
    {% endif %}
    {% if tags %}
    <div class="flex flex-wrap gap-2 mt-4 mb-2">
        {% for tag in tags %}
        <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">{{ tag|e }}</span>
        {% endfor %}
    </div>
    {% endif %}
    {% if footer %}
    <div class="mt-auto pt-4 border-t border-gray-100">
        {{ footer|safe }}
    </div>
    {% endif %}
</div>
{% endmacro %}

{# Card de insight avançado com ícones e cores variáveis #}
{% macro advanced_insight(title, content, icon_type="info", color="blue", metrics=None, action_text=None, action_url=None) %}
<div class="bg-white rounded-lg p-5 border border-gray-200 flex flex-col shadow-sm hover:shadow-md transition-all duration-300 h-full">
    <div class="flex items-center mb-3">
        {% if icon_type == "info" %}
        <div class="w-10 h-10 rounded-full bg-{{ color }}-50 flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-{{ color }}-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"></path>
            </svg>
        </div>
        {% elif icon_type == "warning" %}
        <div class="w-10 h-10 rounded-full bg-yellow-50 flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
            </svg>
        </div>
        {% elif icon_type == "success" %}
        <div class="w-10 h-10 rounded-full bg-green-50 flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
        </div>
        {% elif icon_type == "trend" %}
        <div class="w-10 h-10 rounded-full bg-{{ color }}-50 flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-{{ color }}-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941" />
            </svg>
        </div>
        {% elif icon_type == "money" %}
        <div class="w-10 h-10 rounded-full bg-green-50 flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
        </div>
        {% endif %}
        <span class="text-base font-semibold text-gray-900">{{ title|e }}</span>
    </div>

    <p class="text-sm text-gray-600 mb-4 leading-relaxed">{{ content|safe }}</p>

    {% if metrics %}
    <div class="grid grid-cols-{{ metrics|length if metrics|length <= 3 else 3 }} gap-3 mb-4">
        {% for metric in metrics %}
        <div class="bg-gray-50 p-3 rounded-lg">
            <p class="text-xs text-gray-500 mb-1">{{ metric.label|e }}</p>
            <p class="text-lg font-semibold text-gray-900">{{ metric.value|e }}</p>
            {% if metric.trend %}
            <p class="text-xs {% if metric.trend_positive %}text-green-600{% else %}text-red-600{% endif %} font-medium">
                {{ metric.trend|e }}
            </p>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    {% if action_text and action_url %}
    <div class="mt-auto pt-4">
        <a href="{{ action_url|e }}" class="inline-flex items-center text-sm font-medium text-{{ color }}-600 hover:text-{{ color }}-700">
            {{ action_text|e }}
            <svg class="ml-1 w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
            </svg>
        </a>
    </div>
    {% endif %}
</div>
{% endmacro %}

{# Gráfico #}
{% macro chart(id, title=None, subtitle=None, height="300px", type=None, data=None, colors=None, horizontal=False) %}
<div class="w-full h-full">
    {% if title %}
    <h3 class="text-base font-medium text-gray-900 mb-1">{{ title|e }}</h3>
    {% endif %}
    {% if subtitle %}
    <p class="text-sm text-gray-500 mb-4">{{ subtitle|e }}</p>
    {% endif %}
    <div class="relative w-full" style="height: {{ height|e }};">
        <canvas id="{{ id|e }}" class="w-full h-full"></canvas>
    </div>
    {% if data %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('{{ id|e }}');
            if (ctx) {
                const chartData = {
                    labels: {{ data|map(attribute='name')|list|tojson }},
                    datasets: [{
                        data: {{ data|map(attribute='value')|list|tojson }},
                        backgroundColor: {{ colors|default(['#0087EB', '#1E96F0', '#3CA5F5', '#5AB4FA', '#78C3FF'])|tojson }},
                        borderWidth: 1
                    }]
                };

                const chartOptions = {
                    responsive: true,
                    maintainAspectRatio: false,
                    layout: {
                        padding: {
                            top: 10,
                            right: 10,
                            bottom: 10,
                            left: 10
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                boxWidth: 10,
                                usePointStyle: true,
                                pointStyle: 'circle',
                                font: {
                                    size: 11
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        },
                        y: {
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        }
                    }
                };

                if (window.AmigoDH) {
                    if ('{{ type|e }}' === 'pie') {
                        window.AmigoDH.createPieChart('{{ id|e }}', chartData.labels, chartData.datasets[0].data, 'Valor', {
                            backgroundColor: chartData.datasets[0].backgroundColor
                        });
                    } else if ('{{ type|e }}' === 'bar') {
                        window.AmigoDH.createBarChart('{{ id|e }}', chartData.labels, chartData.datasets[0].data, 'Valor', {
                            backgroundColor: chartData.datasets[0].backgroundColor,
                            indexAxis: {% if horizontal %}'y'{% else %}'x'{% endif %}
                        });
                    } else if ('{{ type|e }}' === 'line') {
                        window.AmigoDH.createLineChart('{{ id|e }}', chartData.labels, chartData.datasets[0].data, 'Valor', {
                            backgroundColor: chartData.datasets[0].backgroundColor
                        });
                    } else {
                        // Configurar opções para gráficos horizontais
                        {% if horizontal %}
                        chartOptions.indexAxis = 'y';
                        {% endif %}

                        new Chart(ctx, {
                            type: '{{ type|default("bar") }}',
                            data: chartData,
                            options: chartOptions
                        });
                    }
                } else {
                    // Configurar opções para gráficos horizontais
                    {% if horizontal %}
                    chartOptions.indexAxis = 'y';
                    {% endif %}

                    new Chart(ctx, {
                        type: '{{ type|default("bar") }}',
                        data: chartData,
                        options: chartOptions
                    });
                }
            }
        });
    </script>
    {% endif %}
</div>
{% endmacro %}

{# Cabeçalho de seção #}
{% macro section_header(title, subtitle=None, actions=None) %}
<div class="mb-6">
    <h2 class="text-xl font-semibold text-gray-900">{{ title|e }}</h2>
    {% if subtitle %}
    <p class="mt-1 text-sm text-gray-500">{{ subtitle|e }}</p>
    {% endif %}
    {% if actions %}
    <div class="mt-3">
        {{ actions|safe }}
    </div>
    {% endif %}
</div>
{% endmacro %}

{# Resumo executivo #}
{% macro executive_summary(title, metrics) %}
<div class="bg-white rounded-lg p-5 border border-gray-200 shadow-sm mb-8">
    <h2 class="text-lg font-medium text-gray-900 mb-4">{{ title|e }}</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {% for metric in metrics %}
        <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
            <p class="text-sm text-gray-600 mb-1">{{ metric.title|e }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ metric.value|e }}</p>
            <div class="flex items-center mt-1">
                {% if metric.trend %}
                <span class="text-xs {% if metric.trend_color == 'green' %}text-green-600{% elif metric.trend_color == 'red' %}text-red-600{% else %}text-blue-600{% endif %} font-medium">{{ metric.trend|e }}</span>
                {% endif %}
                {% if metric.subtitle %}
                <span class="text-xs text-gray-500 {% if metric.trend %}ml-1{% endif %}">{{ metric.subtitle|e }}</span>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endmacro %}

{# Hero section - Modernized Design #}
{% macro hero_section(title, subtitle, stats=None, actions=None, bg_class="bg-gradient-to-r from-blue-50 to-blue-100") %}
<div class="{{ bg_class|e }} text-gray-800">
    <div class="w-full px-4 sm:px-6 lg:px-8 py-16">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="md:w-2/3">
                <h1 class="text-4xl font-bold mb-3 text-gray-900">{{ title|e }}</h1>
                <div class="h-1 w-24 bg-blue-300 rounded-full mb-6"></div>
                <p class="text-gray-700 max-w-3xl text-lg leading-relaxed">{{ subtitle|e }}</p>
                {% if stats %}
                <div class="mt-6 flex flex-wrap items-center gap-3">
                    {% for stat in stats %}
                    <div class="bg-white bg-opacity-90 text-gray-800 px-4 py-2 rounded-lg text-sm font-medium flex items-center shadow-sm">
                        {% if stat.icon %}
                        <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                {{ stat.icon|safe }}
                            </svg>
                        </div>
                        {% endif %}
                        <div>
                            <div class="text-xs text-gray-500">{{ stat.label|e }}</div>
                            <div class="font-semibold">{{ stat.value|e }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
                {% if actions %}
                <div class="mt-8 flex flex-wrap gap-4">
                    {{ actions|safe }}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endmacro %}

{# Tabela de dados #}
{% macro data_table(id, headers, data, actions=true, pagination=true) %}
<div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200" id="{{ id|e }}">
            <thead class="bg-gray-100">
                <tr>
                    {% for header in headers %}
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">{{ header|e }}</th>
                    {% endfor %}
                    {% if actions %}
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Ações</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for row in data %}
                <tr class="hover:bg-gray-50">
                    {% for cell in row %}
                    <td class="px-6 py-4 whitespace-nowrap text-sm {% if loop.first %}font-medium text-gray-900{% else %}text-gray-500{% endif %}">{{ cell|e }}</td>
                    {% endfor %}
                    {% if actions %}
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {% if row.actions %}
                        {{ row.actions|safe }}
                        {% else %}
                        <a href="#" class="text-primary hover:underline">Ver Detalhes</a>
                        {% endif %}
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% if pagination %}
    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Anterior
            </a>
            <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Próximo
            </a>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    Mostrando <span class="font-medium">1</span> a <span class="font-medium">{{ data|length }}</span> de <span class="font-medium">{{ data|length }}</span> resultados
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Anterior</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    <a href="#" aria-current="page" class="z-10 bg-primary border-primary text-white relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        1
                    </a>
                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Próximo</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                </nav>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endmacro %}

{# Filtros de tabela #}
{% macro table_filters(filters, id=None) %}
<div class="bg-white rounded-lg shadow-sm p-4 mb-6 border border-gray-200" {% if id %}id="{{ id|e }}"{% endif %}>
    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div class="flex flex-col sm:flex-row gap-4">
            {% for filter in filters %}
            {% if filter.type == 'search' %}
            <div class="relative">
                <input type="text" id="{{ filter.id|e }}" placeholder="{{ filter.placeholder|e }}" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>
            {% elif filter.type == 'select' %}
            <select id="{{ filter.id|e }}" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                <option value="">{{ filter.placeholder|e }}</option>
                {% for option in filter.options %}
                <option value="{{ option.value|e }}">{{ option.label|e }}</option>
                {% endfor %}
            </select>
            {% endif %}
            {% endfor %}
        </div>
        <div>
            <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Exportar Dados
            </button>
        </div>
    </div>
</div>
{% endmacro %}

{# Ícones comuns #}
{% macro icons() %}
{% set icons = {
    'user': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />',
    'users': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />',
    'opportunity': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />',
    'implementation': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />',
    'university': '<path d="M12 14l9-5-9-5-9 5 9 5z" /><path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />',
    'conversion': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />',
    'money': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
    'chart': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />',
    'calendar': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />',
    'info': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
    'warning': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />',
    'success': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />',
    'error': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />'
} %}
{{ icons|tojson }}
{% endmacro %}

{# Tooltip Macro for Business Rules and Data Lineage #}
{% macro business_tooltip(title, description, formula=None, columns=None, data_source=None, calculation_method=None) %}
<div class="tooltip-container">
    <svg class="tooltip-trigger w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M9.879 7.519c0-1.105.906-2 2.021-2s2.021.895 2.021 2-.906 2-2.021 2-2.021-.895-2.021-2zM12 13.5v6" />
        <circle cx="12" cy="12" r="9" />
    </svg>
    <div class="tooltip-content">
        <div class="tooltip-section">
            <div class="tooltip-title">{{ title }}</div>
            <div>{{ description }}</div>
        </div>

        {% if formula %}
        <div class="tooltip-section">
            <div class="tooltip-title">Fórmula</div>
            <code class="tooltip-formula">{{ formula }}</code>
        </div>
        {% endif %}

        {% if calculation_method %}
        <div class="tooltip-section">
            <div class="tooltip-title">Método de Cálculo</div>
            <div>{{ calculation_method }}</div>
        </div>
        {% endif %}

        {% if columns %}
        <div class="tooltip-section">
            <div class="tooltip-title">Colunas Utilizadas</div>
            <div class="tooltip-columns">{{ columns }}</div>
        </div>
        {% endif %}

        {% if data_source %}
        <div class="tooltip-section">
            <div class="tooltip-title">Fonte de Dados</div>
            <div class="tooltip-datalineage">{{ data_source }}</div>
        </div>
        {% endif %}
    </div>
</div>
{% endmacro %}
