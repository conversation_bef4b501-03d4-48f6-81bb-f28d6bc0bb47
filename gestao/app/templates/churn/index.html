{% extends 'base.html' %}
{% from 'macros/components.html' import kpi_card, stat_card, chart, section_header, advanced_insight, business_tooltip %}

{% block title %}{{ app_name|e }} - Cancelamentos{% endblock %}

{% block content %}
<div class="p-4 space-y-6">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-6 shadow-sm border border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-3">
                    <span class="flex items-center">
                        <svg class="w-8 h-8 mr-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        Cancelamentos
                    </span>
                </h1>
                <div class="h-1 w-24 bg-red-300 rounded-full mb-4"></div>
                <p class="text-lg text-gray-700 max-w-3xl leading-relaxed">
                    Análise completa de cancelamentos para identificar padrões, riscos e oportunidades de retenção.
                    Monitore métricas críticas e tome decisões baseadas em dados para reduzir a taxa de cancelamento.
                </p>
            </div>
            <div class="hidden lg:block">
                <div class="w-32 h-32 rounded-full bg-red-100 flex items-center justify-center">
                    <svg class="w-16 h-16 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
            </div>
        </div>

        <!-- Business Rules Section -->
        <div class="mt-6 bg-white p-6 rounded-lg border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Status de Cancelamento</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="bg-red-50 p-3 rounded-lg">
                    <h4 class="font-medium text-red-800 mb-2">Cancelamentos Definitivos</h4>
                    <ul class="text-sm text-red-700 space-y-1">
                        <li>• Baixa Concluída</li>
                        <li>• Cancelado</li>
                        <li>• Cancelamento Sem Retorno</li>
                    </ul>
                </div>
                <div class="bg-yellow-50 p-3 rounded-lg">
                    <h4 class="font-medium text-yellow-800 mb-2">Em Processo</h4>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li>• Baixa Iniciada</li>
                        <li>• Cancelamento Solicitado</li>
                    </ul>
                </div>
                <div class="bg-orange-50 p-3 rounded-lg">
                    <h4 class="font-medium text-orange-800 mb-2">Transições</h4>
                    <ul class="text-sm text-orange-700 space-y-1">
                        <li>• Transição de Contador</li>
                        <li>• Saída Concluída</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Main KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Total Cancellations -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-red-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Total de Cancelamentos</p>
                        {{ business_tooltip(
                            title="Total de Cancelamentos",
                            description="Número total de implementações que foram canceladas, incluindo todos os status de cancelamento definitivos e em processo.",
                            formula="COUNT(Status_Implantacao WHERE Status_Implantacao IN ('Baixa Concluída', 'Cancelado', 'Cancelamento Sem Retorno', 'Baixa Iniciada', 'Cancelamento Solicitado'))",
                            columns="Status_Implantacao",
                            data_source="base_dados.csv → Contagem de implementações com status de cancelamento",
                            calculation_method="Contagem de registros onde Status_Implantacao indica cancelamento definitivo ou em processo"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ total_cancellations }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Implementações canceladas</p>
            <div class="text-xs text-gray-500 mt-2">De {{ total_implementations }} implementações totais</div>
        </div>

        <!-- Cancellation Rate -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-yellow-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Taxa de Cancelamento</p>
                        {{ business_tooltip(
                            title="Taxa de Cancelamento",
                            description="Percentual de implementações canceladas em relação ao total de implementações iniciadas, indicando a eficiência de retenção.",
                            formula="(Total de Cancelamentos ÷ Total de Implementações) × 100",
                            columns="Status_Implantacao",
                            data_source="base_dados.csv → Divisão de cancelamentos pelo total de implementações",
                            calculation_method="Divisão do número de cancelamentos pelo total de implementações, multiplicado por 100"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ cancellation_rate|string + "%" }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Percentual de cancelamentos</p>
            <div class="text-xs text-gray-500 mt-2">Meta: < 15%</div>
        </div>

        <!-- Monthly Cancellation Rate -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-red-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Taxa de Cancelamento Mensal</p>
                        {{ business_tooltip(
                            title="Taxa de Cancelamento Mensal",
                            description="Taxa de cancelamento calculada mensalmente baseada em clientes ativos (implementações finalizadas), indicando churn recorrente.",
                            formula="(Cancelamentos do Mês ÷ Clientes Ativos no Início do Mês) × 100",
                            columns="Status_Implantacao, Data_Cancelamento",
                            data_source="base_dados.csv → Cálculo mensal de churn baseado em clientes finalizados",
                            calculation_method="Divisão dos cancelamentos mensais pelos clientes ativos no início do mês, multiplicado por 100"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ monthly_cancellation_rate|string + "%" }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Cancelamentos vs clientes ativos</p>
            <div class="text-xs text-gray-500 mt-2">Baseado em clientes finalizados</div>
        </div>

        <!-- Revenue Impact -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-red-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Impacto na Receita</p>
                        {{ business_tooltip(
                            title="Impacto na Receita (MRR Perdido)",
                            description="Receita mensal recorrente perdida devido aos cancelamentos, representando o impacto financeiro direto do churn.",
                            formula="SUM(Valor_Mensalidade WHERE Status_Implantacao IN (status_cancelamento))",
                            columns="Valor_Mensalidade, Status_Implantacao",
                            data_source="base_dados.csv → Soma dos valores de mensalidade das implementações canceladas",
                            calculation_method="Soma de todos os valores de mensalidade das implementações com status de cancelamento"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ cancelled_revenue }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">MRR perdido por cancelamentos</p>
            <div class="text-xs text-gray-500 mt-2">Receita mensal perdida</div>
        </div>
    </div>

    <!-- Secondary Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Active Implementations -->
        {{ stat_card(
            title="Implementações Ativas",
            value=active_implementations,
            subtitle="Em andamento (risco de cancelamento)",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="primary",
            trend="Monitoramento contínuo",
            trend_value="Crítico"
        ) }}

        <!-- Average Time to Cancellation -->
        {{ stat_card(
            title="Tempo Médio até Cancelamento",
            value=(avg_time_to_cancellation|string + " dias")|e,
            subtitle="Desde criação do lead",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="warning",
            trend="Ciclo de vida do cliente",
            trend_value="Análise"
        ) }}

        <!-- Cancellation with Entry Payment -->
        {{ stat_card(
            title="Cancelamentos c/ Pagamento",
            value=cancellation_with_entry,
            subtitle="Com pagamento de entrada",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />',
            color="warning",
            trend="Vs. sem pagamento",
            trend_value=cancellation_without_entry|string
        ) }}

        <!-- High Risk Universities -->
        {{ stat_card(
            title="Universidades de Alto Risco",
            value=high_risk_universities|length,
            subtitle="Taxa de cancelamento > 20%",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />',
            color="danger",
            trend="Requer atenção",
            trend_value="Urgente"
        ) }}
    </div>

    <!-- Entry Payment Impact Analysis -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Impacto do Pagamento de Entrada nos Cancelamentos</h3>
        <p class="text-gray-600 mb-6">Análise comparativa da taxa de cancelamento entre implementações com e sem pagamento de entrada.</p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
                <h4 class="text-base font-medium text-gray-800 mb-3">Comparação de Taxas</h4>
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h5 class="text-sm font-medium text-green-800 mb-1">Com Pagamento de Entrada</h5>
                        <p class="text-2xl font-bold text-green-700">{{ cancellation_rate_with_entry|e }}%</p>
                        <p class="text-xs text-green-600 mt-1">{{ cancellation_with_entry|e }} cancelamentos</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <h5 class="text-sm font-medium text-red-800 mb-1">Sem Pagamento de Entrada</h5>
                        <p class="text-2xl font-bold text-red-700">{{ cancellation_rate_without_entry|e }}%</p>
                        <p class="text-xs text-red-600 mt-1">{{ cancellation_without_entry|e }} cancelamentos</p>
                    </div>
                </div>

                {% set rate_difference = cancellation_rate_without_entry - cancellation_rate_with_entry %}
                {% if rate_difference > 0 %}
                <div class="mt-4 bg-blue-50 p-4 rounded-lg">
                    <h5 class="text-sm font-medium text-blue-800 mb-2">Insight</h5>
                    <p class="text-sm text-blue-700">
                        Implementações sem pagamento de entrada têm uma taxa de cancelamento
                        <strong>{{ "%.1f"|format(rate_difference) }}%</strong> maior.
                        Considere incentivar o pagamento de entrada para reduzir cancelamentos.
                    </p>
                </div>
                {% endif %}
            </div>

            <div>
                <div class="flex items-center mb-3">
                    <h4 class="text-base font-medium text-gray-800">Gráfico Comparativo</h4>
                    <div class="ml-2 relative group">
                        <svg class="w-4 h-4 text-gray-400 cursor-help" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none w-64 z-10">
                            <div class="text-center">
                                <strong>Escala Logarítmica</strong><br>
                                Este gráfico usa escala logarítmica para permitir comparação mais justa entre valores muito diferentes.
                                Pequenas diferenças percentuais ficam mais visíveis e proporcionais.
                            </div>
                            <div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                        </div>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="entry-payment-rates-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <!-- Cancellation by Status -->
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
            <div class="flex items-center mb-3">
                <h3 class="text-lg font-semibold text-gray-900">Cancelamentos por Status</h3>
                {{ business_tooltip(
                    title="Cancelamentos por Status",
                    description="Distribuição dos cancelamentos por tipo de status, permitindo identificar os principais motivos e fases de cancelamento.",
                    formula="COUNT(Status_Implantacao) GROUP BY Status_Implantacao WHERE Status_Implantacao IN (status_cancelamento)",
                    columns="Status_Implantacao",
                    data_source="base_dados.csv → Agrupamento de cancelamentos por status",
                    calculation_method="Contagem de implementações agrupadas por status de cancelamento: Baixa Concluída, Cancelado, Cancelamento Sem Retorno, etc."
                ) }}
            </div>
            <div class="h-56">
                <canvas id="cancellation-status-chart"></canvas>
            </div>
        </div>

        <!-- Combined Trends: Cancellations vs Finalized -->
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
            <div class="flex items-center mb-3">
                <h3 class="text-lg font-semibold text-gray-900">Tendência: Cancelamentos vs Implementações Finalizadas</h3>
                {{ business_tooltip(
                    title="Tendência Cancelamentos vs Finalizadas",
                    description="Comparação temporal entre cancelamentos e implementações finalizadas, mostrando a evolução da retenção ao longo do tempo.",
                    formula="Duas linhas: COUNT(Cancelamentos) e COUNT(Finalizadas) GROUP BY MONTH",
                    columns="Status_Implantacao, Data_Criacao_Implantacao",
                    data_source="base_dados.csv → Agrupamento mensal de cancelamentos e finalizações",
                    calculation_method="Contagem mensal de implementações canceladas vs finalizadas para análise de tendência"
                ) }}
            </div>
            <div class="h-56">
                <canvas id="combined-trends-chart"></canvas>
            </div>
        </div>
    </div>

    <!-- University and Responsible Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <!-- Top Universities with Cancellations -->
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
            <div class="flex items-center mb-3">
                <h3 class="text-lg font-semibold text-gray-900">Universidades com Mais Cancelamentos</h3>
                {{ business_tooltip(
                    title="Universidades com Mais Cancelamentos",
                    description="Ranking das universidades com maior número absoluto de cancelamentos, permitindo identificar instituições que requerem atenção especial.",
                    formula="COUNT(Status_Implantacao WHERE Status_Implantacao IN (status_cancelamento)) GROUP BY Universidade ORDER BY COUNT DESC",
                    columns="Status_Implantacao, Universidade",
                    data_source="base_dados.csv → Agrupamento de cancelamentos por universidade",
                    calculation_method="Contagem de cancelamentos por universidade, ordenados por quantidade decrescente, exibindo top 10"
                ) }}
            </div>
            <div class="h-56">
                <canvas id="university-cancellation-chart"></canvas>
            </div>
        </div>

        <!-- Top Responsibles with Cancellations -->
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
            <div class="flex items-center mb-3">
                <h3 class="text-lg font-semibold text-gray-900">Responsáveis com Mais Cancelamentos</h3>
                {{ business_tooltip(
                    title="Responsáveis com Mais Cancelamentos",
                    description="Ranking dos responsáveis comerciais com maior número de cancelamentos, identificando necessidades de treinamento ou suporte.",
                    formula="COUNT(Status_Implantacao WHERE Status_Implantacao IN (status_cancelamento)) GROUP BY Nome_Responsavel ORDER BY COUNT DESC",
                    columns="Status_Implantacao, Nome_Responsavel",
                    data_source="base_dados.csv → Agrupamento de cancelamentos por responsável",
                    calculation_method="Contagem de cancelamentos por responsável comercial, ordenados por quantidade decrescente"
                ) }}
            </div>
            <div class="h-56">
                <canvas id="responsible-cancellation-chart"></canvas>
            </div>
        </div>
    </div>

    <!-- State and Class Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <!-- Cancellations by State -->
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
            <div class="flex items-center mb-3">
                <h3 class="text-lg font-semibold text-gray-900">Cancelamentos por Estado</h3>
                {{ business_tooltip(
                    title="Cancelamentos por Estado",
                    description="Distribuição geográfica dos cancelamentos por estado, permitindo identificar regiões com maior taxa de churn e necessidades regionais específicas.",
                    formula="COUNT(Status_Implantacao WHERE Status_Implantacao IN (status_cancelamento)) GROUP BY Estado",
                    columns="Status_Implantacao, Estado",
                    data_source="base_dados.csv → Agrupamento de cancelamentos por estado",
                    calculation_method="Contagem de cancelamentos agrupados por estado, ordenados por quantidade decrescente"
                ) }}
            </div>
            <div class="h-56">
                <canvas id="state-cancellation-chart"></canvas>
            </div>
        </div>

        <!-- Cancellations by Turma (Class) -->
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
            <div class="flex items-center mb-3">
                <h3 class="text-lg font-semibold text-gray-900">Cancelamentos por Turma</h3>
                {{ business_tooltip(
                    title="Cancelamentos por Turma",
                    description="Análise de cancelamentos por turma específica, identificando grupos de alunos com maior propensão ao churn e padrões comportamentais.",
                    formula="COUNT(Status_Implantacao WHERE Status_Implantacao IN (status_cancelamento)) GROUP BY Turma",
                    columns="Status_Implantacao, Turma",
                    data_source="base_dados.csv → Agrupamento de cancelamentos por turma",
                    calculation_method="Contagem de cancelamentos agrupados por turma, permitindo análise de coortes específicas"
                ) }}
            </div>
            <div class="h-56">
                <canvas id="turma-cancellation-chart"></canvas>
            </div>
        </div>
    </div>

    <!-- Risk Insights Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Total High Risk Entities -->
        {{ stat_card(
            title="Entidades de Alto Risco",
            value=risk_insights.total_high_risk_entities|default(0),
            subtitle="Universidades, turmas, estados e responsáveis",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />',
            color="danger",
            trend="Requer monitoramento",
            trend_value="Crítico"
        ) }}

        <!-- Average University Risk -->
        {{ stat_card(
            title="Risco Médio - Universidades",
            value=(risk_insights.avg_university_risk|default(0)|string + "%")|e,
            subtitle="Taxa média de cancelamento",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />',
            color="warning",
            trend="Meta: < 20%",
            trend_value="Controle"
        ) }}

        <!-- Average Turma Risk -->
        {{ stat_card(
            title="Risco Médio - Turmas",
            value=(risk_insights.avg_turma_risk|default(0)|string + "%")|e,
            subtitle="Taxa média por turma",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />',
            color="warning",
            trend="Meta: < 25%",
            trend_value="Monitorar"
        ) }}

        <!-- Highest Risk Exemption -->
        {% if risk_insights.highest_risk_exemption %}
        {{ stat_card(
            title="Maior Risco - Isenção",
            value=(risk_insights.highest_risk_exemption[0]|string + " meses")|e,
            subtitle=(risk_insights.highest_risk_exemption[1].risk_rate|string + "% de cancelamento")|e,
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="danger",
            trend="Período crítico",
            trend_value="Atenção"
        ) }}
        {% else %}
        {{ stat_card(
            title="Maior Risco - Isenção",
            value="N/A",
            subtitle="Dados insuficientes",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="secondary",
            trend="Sem dados",
            trend_value="OK"
        ) }}
        {% endif %}
    </div>

    <!-- Advanced Trend Analysis -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Tendência Geral de Cancelamentos - Análise Avançada</h3>
        <p class="text-gray-600 mb-6">Análise estatística com média móvel, desvio padrão e comparações M/M e Q/Q.</p>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Monthly Trend Chart -->
            <div>
                <h4 class="text-base font-medium text-gray-800 mb-3">Análise Mensal (M/M)</h4>
                <div class="h-80">
                    <canvas id="monthly-trend-chart"></canvas>
                </div>
                {% if monthly_stats and monthly_stats.periods %}
                <div class="mt-4 grid grid-cols-2 gap-4">
                    <div class="bg-blue-50 p-3 rounded-lg">
                        <h5 class="text-sm font-medium text-blue-800 mb-1">Última Variação M/M</h5>
                        {% set last_mom = monthly_stats.mom_change[-1] if monthly_stats.mom_change else 0 %}
                        <p class="text-lg font-bold {{ 'text-green-700' if last_mom < 0 else 'text-red-700' }}">
                            {{ "%.1f"|format(last_mom) }}%
                        </p>
                    </div>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <h5 class="text-sm font-medium text-gray-800 mb-1">Desvio Padrão</h5>
                        {% set avg_std = (monthly_stats.std_dev | sum / monthly_stats.std_dev | length) if monthly_stats.std_dev else 0 %}
                        <p class="text-lg font-bold text-gray-700">{{ "%.1f"|format(avg_std) }}%</p>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Quarterly Trend Chart -->
            <div>
                <h4 class="text-base font-medium text-gray-800 mb-3">Análise Trimestral (Q/Q)</h4>
                <div class="h-80">
                    <canvas id="quarterly-trend-chart"></canvas>
                </div>
                {% if quarterly_stats and quarterly_stats.periods %}
                <div class="mt-4 grid grid-cols-1 gap-4">
                    <div class="bg-purple-50 p-3 rounded-lg">
                        <h5 class="text-sm font-medium text-purple-800 mb-1">Última Variação Q/Q</h5>
                        {% set last_qoq = quarterly_stats.qoq_change[-1] if quarterly_stats.qoq_change else 0 %}
                        <p class="text-lg font-bold {{ 'text-green-700' if last_qoq < 0 else 'text-red-700' }}">
                            {{ "%.1f"|format(last_qoq) }}%
                        </p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Risk Analysis Section - Expanded -->
    <div class="space-y-6">
        <!-- Universities Risk Analysis -->
        {% if high_risk_universities %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Análise de Risco - Universidades</h3>
            <p class="text-gray-600 mb-6">Universidades com taxa de cancelamento superior a 20% requerem atenção especial.</p>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Scatter Plot -->
                <div>
                    <h4 class="text-base font-medium text-gray-800 mb-3">Dispersão: Total vs Taxa de Cancelamento</h4>
                    <div class="h-80">
                        <canvas id="universities-scatter-chart"></canvas>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">Clique nos pontos para ver detalhes da universidade</p>
                </div>

                <!-- Risk Table -->
                <div>
                    <h4 class="text-base font-medium text-gray-800 mb-3">Universidades de Alto Risco</h4>
                    <div class="overflow-y-auto max-h-80">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Universidade</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Cancelamentos</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Taxa</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for university in high_risk_universities %}
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="highlightUniversity('{{ university.name|e }}')">
                                    <td class="px-4 py-2 text-sm font-medium text-gray-900">{{ university.name|e }}</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">{{ university.cancellations|e }}</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">{{ university.total|e }}</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            {{ university.risk_rate|e }}%
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Responsibles Risk Analysis -->
        {% if high_risk_responsibles %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Análise de Risco - Responsáveis</h3>
            <p class="text-gray-600 mb-6">Responsáveis com taxa de cancelamento superior a 15% necessitam treinamento e suporte adicional.</p>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Responsável</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cancelamentos</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Taxa de Risco</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for responsible in high_risk_responsibles %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ responsible.name|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ responsible.cancellations|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ responsible.total|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    {{ responsible.risk_rate|e }}%
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Alto Risco
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Last Actions Before Cancelling Analysis -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Análise das Últimas Ações Antes do Cancelamento</h3>
        <p class="text-gray-600 mb-6">Identificação de padrões nas últimas tarefas, status e fases antes dos cancelamentos.</p>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Last Task Analysis -->
            <div>
                <h4 class="text-base font-medium text-gray-800 mb-3">Últimas Tarefas</h4>
                <div class="h-64">
                    <canvas id="last-task-chart"></canvas>
                </div>
            </div>

            <!-- Last Status Analysis -->
            <div>
                <h4 class="text-base font-medium text-gray-800 mb-3">Últimos Status</h4>
                <div class="h-64">
                    <canvas id="last-status-chart"></canvas>
                </div>
            </div>

            <!-- Last Phase Analysis -->
            <div>
                <h4 class="text-base font-medium text-gray-800 mb-3">Últimas Fases</h4>
                <div class="h-64">
                    <canvas id="last-phase-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Exemption Analysis -->
    {% if exemption_risk_analysis %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Análise de Risco por Isenção em Meses</h3>
        <p class="text-gray-600 mb-6">Impacto dos meses de isenção na taxa de cancelamento.</p>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Exemption Risk Chart -->
            <div>
                <h4 class="text-base font-medium text-gray-800 mb-3">Taxa de Cancelamento por Meses de Isenção</h4>
                <div class="h-64">
                    <canvas id="exemption-risk-chart"></canvas>
                </div>
            </div>

            <!-- Exemption Risk Table -->
            <div>
                <h4 class="text-base font-medium text-gray-800 mb-3">Detalhamento por Período</h4>
                <div class="overflow-y-auto max-h-64">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Meses</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Cancelamentos</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Taxa</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for months, data in exemption_risk_analysis.items() %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 text-sm font-medium text-gray-900">{{ months|e }}</td>
                                <td class="px-4 py-2 text-sm text-gray-500">{{ data.cancellations|e }}</td>
                                <td class="px-4 py-2 text-sm text-gray-500">{{ data.total|e }}</td>
                                <td class="px-4 py-2 text-sm text-gray-500">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {% if data.risk_rate > 30 %}bg-red-100 text-red-800
                                        {% elif data.risk_rate > 20 %}bg-yellow-100 text-yellow-800
                                        {% else %}bg-green-100 text-green-800{% endif %}">
                                        {{ data.risk_rate|e }}%
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Insights -->
                <div class="mt-4 space-y-3">
                    {% if risk_insights.highest_risk_exemption %}
                    <div class="bg-red-50 p-3 rounded-lg">
                        <h5 class="text-sm font-medium text-red-800 mb-1">⚠️ Maior Risco</h5>
                        <p class="text-sm text-red-700">
                            {{ risk_insights.highest_risk_exemption[0] }} meses de isenção apresenta
                            {{ risk_insights.highest_risk_exemption[1].risk_rate }}% de taxa de cancelamento.
                        </p>
                    </div>
                    {% endif %}

                    <div class="bg-blue-50 p-3 rounded-lg">
                        <h5 class="text-sm font-medium text-blue-800 mb-1">💡 Recomendação</h5>
                        <p class="text-sm text-blue-700">
                            Monitore clientes com períodos de isenção de alto risco e implemente estratégias
                            de engajamento específicas para esses períodos.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Advanced Churn Analysis Section -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center mb-6">
            <svg class="w-8 h-8 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            <div>
                <h3 class="text-lg font-semibold text-black">Análise Avançada de Cancelamentos</h3>
                <p class="text-gray-600">Modelos preditivos e estratégias de retenção baseadas em machine learning</p>
            </div>
        </div>

        <!-- Model Performance Stats -->
        {% if churn_analysis and not churn_analysis.get('error') %}
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h4 class="text-sm font-medium text-black mb-1">Clientes Analisados</h4>
                <p class="text-2xl font-bold text-blue-600">{{ churn_analysis.total_active_analyzed|e }}</p>
                <p class="text-xs text-gray-600 mt-1">Implementações ativas</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h4 class="text-sm font-medium text-black mb-1">Precisão do Modelo</h4>
                <p class="text-2xl font-bold text-green-600">{{ churn_analysis.prediction_accuracy|e }}</p>
                <p class="text-xs text-gray-600 mt-1">Taxa de acerto</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h4 class="text-sm font-medium text-black mb-1">Última Atualização</h4>
                <p class="text-2xl font-bold text-blue-600">{{ churn_analysis.model_last_updated|e }}</p>
                <p class="text-xs text-gray-600 mt-1">Modelo atualizado</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h4 class="text-sm font-medium text-black mb-1">Fatores de Risco</h4>
                <p class="text-2xl font-bold text-blue-600">{{ churn_analysis.top_risk_factors|length|e }}</p>
                <p class="text-xs text-gray-600 mt-1">Principais fatores</p>
            </div>
        </div>
        {% endif %}

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Predição de Cancelamentos -->
            <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <div class="flex items-center mb-4">
                    <svg class="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <h4 class="text-lg font-semibold text-black">Predição de Cancelamentos</h4>
                </div>
                <p class="text-gray-600 mb-4">Modelo preditivo para identificar clientes em risco de cancelamento</p>

                {% if churn_analysis and churn_analysis.get('top_risk_factors') %}
                <div class="space-y-2">
                    <h5 class="font-medium text-black mb-2">Principais Fatores de Risco:</h5>
                    {% for factor in churn_analysis.top_risk_factors %}
                    <div class="flex items-center text-sm text-gray-700">
                        <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        {{ factor|e }}
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-gray-600 text-sm">
                    <p>Coletando dados para análise preditiva...</p>
                </div>
                {% endif %}
            </div>

            <!-- Segmentação de Risco -->
            <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <div class="flex items-center mb-4">
                    <svg class="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <h4 class="text-lg font-semibold text-black">Segmentação de Risco</h4>
                </div>
                <p class="text-gray-600 mb-4">Classificação automática de clientes por nível de risco</p>

                {% if risk_segmentation %}
                <div class="space-y-3">
                    <!-- High Risk -->
                    <div class="bg-gray-100 p-3 rounded-lg border border-gray-200">
                        <div class="flex justify-between items-center mb-2">
                            <h5 class="font-medium text-black">Alto Risco</h5>
                            <span class="bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs font-medium">
                                {{ risk_segmentation.high_risk.percentage|e }}%
                            </span>
                        </div>
                        <p class="text-sm text-gray-700">{{ risk_segmentation.high_risk.count|e }} clientes</p>
                    </div>

                    <!-- Medium Risk -->
                    <div class="bg-gray-100 p-3 rounded-lg border border-gray-200">
                        <div class="flex justify-between items-center mb-2">
                            <h5 class="font-medium text-black">Médio Risco</h5>
                            <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs font-medium">
                                {{ risk_segmentation.medium_risk.percentage|e }}%
                            </span>
                        </div>
                        <p class="text-sm text-gray-700">{{ risk_segmentation.medium_risk.count|e }} clientes</p>
                    </div>

                    <!-- Low Risk -->
                    <div class="bg-gray-100 p-3 rounded-lg border border-gray-200">
                        <div class="flex justify-between items-center mb-2">
                            <h5 class="font-medium text-black">Baixo Risco</h5>
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium">
                                {{ risk_segmentation.low_risk.percentage|e }}%
                            </span>
                        </div>
                        <p class="text-sm text-gray-700">{{ risk_segmentation.low_risk.count|e }} clientes</p>
                    </div>
                </div>
                {% else %}
                <div class="text-gray-600 text-sm">
                    <p>Processando segmentação de clientes...</p>
                </div>
                {% endif %}
            </div>

            <!-- Estratégias de Retenção -->
            <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <div class="flex items-center mb-4">
                    <svg class="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                    <h4 class="text-lg font-semibold text-black">Estratégias de Retenção</h4>
                </div>
                <p class="text-gray-600 mb-4">Recomendações personalizadas para cada segmento</p>

                {% if retention_strategies %}
                <div class="space-y-4">
                    <!-- High Risk Strategies -->
                    <div>
                        <h5 class="font-medium text-black mb-2">🚨 Alto Risco:</h5>
                        <ul class="text-sm text-gray-700 space-y-1">
                            {% for strategy in retention_strategies.high_risk[:3] %}
                            <li class="flex items-start">
                                <span class="text-red-500 mr-2">•</span>
                                {{ strategy|e }}
                            </li>
                            {% endfor %}
                        </ul>
                    </div>

                    <!-- Medium Risk Strategies -->
                    <div>
                        <h5 class="font-medium text-black mb-2">⚠️ Médio Risco:</h5>
                        <ul class="text-sm text-gray-700 space-y-1">
                            {% for strategy in retention_strategies.medium_risk[:3] %}
                            <li class="flex items-start">
                                <span class="text-blue-500 mr-2">•</span>
                                {{ strategy|e }}
                            </li>
                            {% endfor %}
                        </ul>
                    </div>

                    <!-- Low Risk Strategies -->
                    <div>
                        <h5 class="font-medium text-black mb-2">✅ Baixo Risco:</h5>
                        <ul class="text-sm text-gray-700 space-y-1">
                            {% for strategy in retention_strategies.low_risk[:3] %}
                            <li class="flex items-start">
                                <span class="text-green-500 mr-2">•</span>
                                {{ strategy|e }}
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% else %}
                <div class="text-gray-600 text-sm">
                    <p>Gerando estratégias personalizadas...</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Detailed Risk Analysis Results -->
        <div class="mt-8 space-y-6">
            <!-- High Risk Clients Table -->
            {% if risk_segmentation and risk_segmentation.high_risk.clients %}
            <div class="bg-red-50 rounded-lg p-6 border border-red-200">
                <div class="flex items-center mb-4">
                    <svg class="w-6 h-6 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <h4 class="text-lg font-semibold text-red-800">🚨 Clientes de Alto Risco - Ação Imediata Necessária</h4>
                </div>
                <p class="text-red-700 mb-4">
                    {{ risk_segmentation.high_risk.count }} clientes identificados com score de risco ≥ 70%.
                    Contate imediatamente para implementar estratégias de retenção.
                </p>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-red-200 bg-white rounded-lg shadow-sm">
                        <thead class="bg-red-100">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-red-800 uppercase tracking-wider">ID Lead</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-red-800 uppercase tracking-wider">Universidade</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-red-800 uppercase tracking-wider">Turma</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-red-800 uppercase tracking-wider">Responsável</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-red-800 uppercase tracking-wider">Estado</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-red-800 uppercase tracking-wider">Score</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-red-800 uppercase tracking-wider">Fatores de Risco</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-red-800 uppercase tracking-wider">Ação Recomendada</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for client in risk_segmentation.high_risk.clients %}
                            <tr class="hover:bg-red-50">
                                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                                    <span class="bg-red-100 text-red-800 px-2 py-1 rounded-md font-mono text-xs">
                                        {{ client.id|e }}
                                    </span>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-700 max-w-xs">
                                    <div class="truncate" title="{{ client.university|e }}">{{ client.university|e }}</div>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-700 max-w-xs">
                                    <div class="truncate" title="{{ client.turma|e }}">{{ client.turma|e }}</div>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-700 max-w-xs">
                                    <div class="truncate" title="{{ client.responsible|e }}">{{ client.responsible|e }}</div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700">{{ client.state|e }}</td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm">
                                    <div class="flex items-center">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-bold bg-red-100 text-red-800">
                                            {{ client.risk_score|e }}%
                                        </span>
                                        {% if client.risk_score >= 90 %}
                                        <span class="ml-2 text-red-600 font-bold">CRÍTICO</span>
                                        {% elif client.risk_score >= 80 %}
                                        <span class="ml-2 text-orange-600 font-bold">URGENTE</span>
                                        {% else %}
                                        <span class="ml-2 text-yellow-600 font-bold">ALTO</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm">
                                    <div class="max-w-sm">
                                        {% for factor in client.risk_factors[:3] %}
                                        <span class="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full mr-1 mb-1">
                                            {{ factor|e }}
                                        </span>
                                        {% endfor %}
                                        {% if client.risk_factors|length > 3 %}
                                        <span class="text-xs text-gray-500">+{{ (client.risk_factors|length - 3)|e }} mais</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm">
                                    {% if client.risk_score >= 90 %}
                                    <div class="text-red-700 font-medium">
                                        <div>• Contato executivo imediato</div>
                                        <div>• Reunião de emergência</div>
                                        <div>• Oferta especial de retenção</div>
                                    </div>
                                    {% elif client.risk_score >= 80 %}
                                    <div class="text-orange-700 font-medium">
                                        <div>• Contato em 24h</div>
                                        <div>• Revisão do plano</div>
                                        <div>• Suporte premium</div>
                                    </div>
                                    {% else %}
                                    <div class="text-yellow-700 font-medium">
                                        <div>• Contato em 48h</div>
                                        <div>• Check-in semanal</div>
                                        <div>• Treinamento adicional</div>
                                    </div>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% endif %}

            <!-- Medium Risk Clients Summary -->
            {% if risk_segmentation and risk_segmentation.medium_risk.clients %}
            <div class="bg-yellow-50 rounded-lg p-6 border border-yellow-200">
                <div class="flex items-center mb-4">
                    <svg class="w-6 h-6 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h4 class="text-lg font-semibold text-yellow-800">⚠️ Clientes de Médio Risco - Monitoramento Ativo</h4>
                </div>
                <p class="text-yellow-700 mb-4">
                    {{ risk_segmentation.medium_risk.count }} clientes com score entre 40-69%.
                    Implementar estratégias preventivas de engajamento.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for client in risk_segmentation.medium_risk.clients %}
                    <div class="bg-white p-4 rounded-lg border border-yellow-200 hover:shadow-md transition-shadow">
                        <div class="flex justify-between items-start mb-2">
                            <span class="text-sm font-medium text-gray-900">{{ client.university|e }}</span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                {{ client.risk_score|e }}%
                            </span>
                        </div>
                        <div class="text-xs text-gray-600 mb-2">
                            <div>ID: {{ client.id|e }}</div>
                            <div>Responsável: {{ client.responsible|e }}</div>
                            <div>Estado: {{ client.state|e }}</div>
                        </div>
                        <div class="text-xs">
                            {% for factor in client.risk_factors[:2] %}
                            <span class="inline-block bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full mr-1 mb-1">
                                {{ factor|e }}
                            </span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Model Performance Summary -->
            <div class="bg-blue-50 rounded-lg p-6 border border-blue-200">
                <div class="flex items-center mb-4">
                    <svg class="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <h4 class="text-lg font-semibold text-blue-800">📊 Resumo da Análise Preditiva</h4>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-blue-700">
                <div class="bg-white p-4 rounded-lg border border-blue-200">
                    <h5 class="font-medium mb-2">Distribuição de Risco</h5>
                    <div class="space-y-1 text-sm">
                        {% if risk_segmentation %}
                        <div class="flex justify-between">
                            <span>Alto Risco:</span>
                            <span class="font-bold text-red-600">{{ risk_segmentation.high_risk.count }} ({{ risk_segmentation.high_risk.percentage }}%)</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Médio Risco:</span>
                            <span class="font-bold text-yellow-600">{{ risk_segmentation.medium_risk.count }} ({{ risk_segmentation.medium_risk.percentage }}%)</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Baixo Risco:</span>
                            <span class="font-bold text-green-600">{{ risk_segmentation.low_risk.count }} ({{ risk_segmentation.low_risk.percentage }}%)</span>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="bg-white p-4 rounded-lg border border-blue-200">
                    <h5 class="font-medium mb-2">Ações Recomendadas</h5>
                    <div class="space-y-1 text-sm">
                        <div class="flex items-center">
                            <span class="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                            <span>{{ risk_segmentation.high_risk.count if risk_segmentation else 0 }} contatos imediatos</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                            <span>{{ risk_segmentation.medium_risk.count if risk_segmentation else 0 }} monitoramentos ativos</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                            <span>{{ risk_segmentation.low_risk.count if risk_segmentation else 0 }} acompanhamentos regulares</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-4 rounded-lg border border-blue-200">
                    <h5 class="font-medium mb-2">Próximos Passos</h5>
                    <div class="space-y-1 text-sm">
                        <div>1. Priorizar clientes críticos (≥90%)</div>
                        <div>2. Implementar estratégias de retenção</div>
                        <div>3. Monitorar evolução dos scores</div>
                        <div>4. Atualizar modelo semanalmente</div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart Scripts -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const chartData = {{ chart_data|safe }};

    // Cancellation by Status Chart
    if (chartData.cancellation_by_status && chartData.cancellation_by_status.length > 0) {
        const statusCtx = document.getElementById('cancellation-status-chart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: chartData.cancellation_by_status.map(item => item.name),
                datasets: [{
                    data: chartData.cancellation_by_status.map(item => item.value),
                    backgroundColor: [
                        '#EF4444', '#F59E0B', '#F97316', '#DC2626', '#B91C1C', '#991B1B'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { usePointStyle: true, padding: 20 }
                    }
                }
            }
        });
    }

    // University Cancellation Chart
    if (chartData.cancellation_by_university && chartData.cancellation_by_university.length > 0) {
        const universityCtx = document.getElementById('university-cancellation-chart').getContext('2d');
        new Chart(universityCtx, {
            type: 'bar',
            data: {
                labels: chartData.cancellation_by_university.map(item => item.name),
                datasets: [{
                    label: 'Cancelamentos',
                    data: chartData.cancellation_by_university.map(item => item.value),
                    backgroundColor: 'rgba(239, 68, 68, 0.7)',
                    borderColor: 'rgb(239, 68, 68)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    y: { beginAtZero: true },
                    x: { ticks: { maxRotation: 45 } }
                }
            }
        });
    }

    // Responsible Cancellation Chart
    if (chartData.cancellation_by_responsible && chartData.cancellation_by_responsible.length > 0) {
        const responsibleCtx = document.getElementById('responsible-cancellation-chart').getContext('2d');
        new Chart(responsibleCtx, {
            type: 'bar',
            data: {
                labels: chartData.cancellation_by_responsible.map(item => item.name),
                datasets: [{
                    label: 'Cancelamentos',
                    data: chartData.cancellation_by_responsible.map(item => item.value),
                    backgroundColor: 'rgba(245, 158, 11, 0.7)',
                    borderColor: 'rgb(245, 158, 11)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    y: { beginAtZero: true },
                    x: { ticks: { maxRotation: 45 } }
                }
            }
        });
    }

    // Combined Trends Chart (Cancellations vs Finalized)
    if (chartData.combined_trends && Object.keys(chartData.combined_trends).length > 0) {
        const trendsCtx = document.getElementById('combined-trends-chart').getContext('2d');
        const months = Object.keys(chartData.combined_trends).sort();
        const cancellationsData = months.map(month => chartData.combined_trends[month].cancellations);
        const finalizedData = months.map(month => chartData.combined_trends[month].finalized);

        new Chart(trendsCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [{
                    label: 'Cancelamentos',
                    data: cancellationsData,
                    borderColor: 'rgb(239, 68, 68)',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1
                }, {
                    label: 'Implementações Finalizadas',
                    data: finalizedData,
                    borderColor: 'rgb(34, 197, 94)',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: { beginAtZero: true },
                    x: { ticks: { maxRotation: 45 } }
                }
            }
        });
    }

    // Entry Payment Rates Chart with Logarithmic Scale
    if (chartData.entry_payment_rates && chartData.entry_payment_rates.length > 0) {
        const entryCtx = document.getElementById('entry-payment-rates-chart').getContext('2d');

        // Ensure we have positive values for logarithmic scale
        const processedData = chartData.entry_payment_rates.map(item => ({
            ...item,
            value: Math.max(item.value, 0.1) // Minimum value for log scale
        }));

        new Chart(entryCtx, {
            type: 'bar',
            data: {
                labels: processedData.map(item => item.name),
                datasets: [{
                    label: 'Taxa de Cancelamento (%)',
                    data: processedData.map(item => item.value),
                    backgroundColor: [
                        'rgba(34, 197, 94, 0.7)',
                        'rgba(239, 68, 68, 0.7)'
                    ],
                    borderColor: [
                        'rgb(34, 197, 94)',
                        'rgb(239, 68, 68)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                return 'Escala logarítmica para comparação proporcional';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        type: 'logarithmic',
                        min: 0.1,
                        title: {
                            display: true,
                            text: 'Taxa de Cancelamento (%) - Escala Log'
                        },
                        ticks: {
                            callback: function(value, index, values) {
                                return value.toFixed(1) + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    // State Cancellation Chart
    if (chartData.cancellation_by_state && chartData.cancellation_by_state.length > 0) {
        const stateCtx = document.getElementById('state-cancellation-chart').getContext('2d');
        new Chart(stateCtx, {
            type: 'bar',
            data: {
                labels: chartData.cancellation_by_state.map(item => item.name),
                datasets: [{
                    label: 'Cancelamentos',
                    data: chartData.cancellation_by_state.map(item => item.value),
                    backgroundColor: 'rgba(168, 85, 247, 0.7)',
                    borderColor: 'rgb(168, 85, 247)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    y: { beginAtZero: true },
                    x: { ticks: { maxRotation: 45 } }
                }
            }
        });
    }

    // Turma Cancellation Chart
    if (chartData.cancellation_by_turma && chartData.cancellation_by_turma.length > 0) {
        const turmaCtx = document.getElementById('turma-cancellation-chart').getContext('2d');
        new Chart(turmaCtx, {
            type: 'bar',
            data: {
                labels: chartData.cancellation_by_turma.map(item => item.name),
                datasets: [{
                    label: 'Cancelamentos',
                    data: chartData.cancellation_by_turma.map(item => item.value),
                    backgroundColor: 'rgba(59, 130, 246, 0.7)',
                    borderColor: 'rgb(59, 130, 246)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    y: { beginAtZero: true },
                    x: { ticks: { maxRotation: 45 } }
                }
            }
        });
    }

    // Monthly Trend Chart with Moving Averages
    if (chartData.monthly_stats && chartData.monthly_stats.periods && chartData.monthly_stats.periods.length > 0) {
        const monthlyCtx = document.getElementById('monthly-trend-chart').getContext('2d');
        new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: chartData.monthly_stats.periods,
                datasets: [{
                    label: 'Taxa de Cancelamento (%)',
                    data: chartData.monthly_stats.cancellation_rates,
                    borderColor: 'rgb(239, 68, 68)',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1
                }, {
                    label: 'Média Móvel 3M',
                    data: chartData.monthly_stats.moving_avg_3,
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1,
                    borderDash: [5, 5]
                }, {
                    label: 'Média Móvel 6M',
                    data: chartData.monthly_stats.moving_avg_6,
                    borderColor: 'rgb(34, 197, 94)',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1,
                    borderDash: [10, 5]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Taxa de Cancelamento (%)'
                        }
                    },
                    x: { ticks: { maxRotation: 45 } }
                }
            }
        });
    }

    // Quarterly Trend Chart
    if (chartData.quarterly_stats && chartData.quarterly_stats.periods && chartData.quarterly_stats.periods.length > 0) {
        const quarterlyCtx = document.getElementById('quarterly-trend-chart').getContext('2d');
        new Chart(quarterlyCtx, {
            type: 'bar',
            data: {
                labels: chartData.quarterly_stats.periods,
                datasets: [{
                    label: 'Taxa de Cancelamento (%)',
                    data: chartData.quarterly_stats.cancellation_rates,
                    backgroundColor: 'rgba(168, 85, 247, 0.7)',
                    borderColor: 'rgb(168, 85, 247)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Taxa de Cancelamento (%)'
                        }
                    },
                    x: { ticks: { maxRotation: 45 } }
                }
            }
        });
    }

    // Last Task Chart
    if (chartData.last_task_analysis && chartData.last_task_analysis.length > 0) {
        const lastTaskCtx = document.getElementById('last-task-chart').getContext('2d');
        new Chart(lastTaskCtx, {
            type: 'doughnut',
            data: {
                labels: chartData.last_task_analysis.map(item => item.name),
                datasets: [{
                    data: chartData.last_task_analysis.map(item => item.value),
                    backgroundColor: [
                        '#EF4444', '#F59E0B', '#10B981', '#3B82F6', '#8B5CF6',
                        '#F97316', '#06B6D4', '#84CC16', '#EC4899', '#6B7280'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { usePointStyle: true, padding: 15 }
                    }
                }
            }
        });
    }

    // Last Status Chart
    if (chartData.last_status_analysis && chartData.last_status_analysis.length > 0) {
        const lastStatusCtx = document.getElementById('last-status-chart').getContext('2d');
        new Chart(lastStatusCtx, {
            type: 'doughnut',
            data: {
                labels: chartData.last_status_analysis.map(item => item.name),
                datasets: [{
                    data: chartData.last_status_analysis.map(item => item.value),
                    backgroundColor: [
                        '#DC2626', '#EA580C', '#059669', '#2563EB', '#7C3AED',
                        '#C2410C', '#0891B2', '#65A30D', '#DB2777', '#4B5563'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { usePointStyle: true, padding: 15 }
                    }
                }
            }
        });
    }

    // Last Phase Chart
    if (chartData.last_phase_analysis && chartData.last_phase_analysis.length > 0) {
        const lastPhaseCtx = document.getElementById('last-phase-chart').getContext('2d');
        new Chart(lastPhaseCtx, {
            type: 'doughnut',
            data: {
                labels: chartData.last_phase_analysis.map(item => item.name),
                datasets: [{
                    data: chartData.last_phase_analysis.map(item => item.value),
                    backgroundColor: [
                        '#B91C1C', '#D97706', '#047857', '#1D4ED8', '#6D28D9',
                        '#9A3412', '#0E7490', '#4D7C0F', '#BE185D', '#374151'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { usePointStyle: true, padding: 15 }
                    }
                }
            }
        });
    }

    // Exemption Risk Chart
    if (chartData.exemption_risk_analysis && Object.keys(chartData.exemption_risk_analysis).length > 0) {
        const exemptionCtx = document.getElementById('exemption-risk-chart').getContext('2d');
        const exemptionData = Object.entries(chartData.exemption_risk_analysis).map(([months, data]) => ({
            x: months,
            y: data.risk_rate
        }));

        new Chart(exemptionCtx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: 'Taxa de Cancelamento por Meses de Isenção',
                    data: exemptionData,
                    backgroundColor: 'rgba(239, 68, 68, 0.7)',
                    borderColor: 'rgb(239, 68, 68)',
                    borderWidth: 2,
                    pointRadius: 8,
                    pointHoverRadius: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const data = chartData.exemption_risk_analysis[context.parsed.x];
                                return `${context.parsed.x} meses: ${context.parsed.y}% (${data.cancellations}/${data.total})`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'linear',
                        title: {
                            display: true,
                            text: 'Meses de Isenção'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Taxa de Cancelamento (%)'
                        }
                    }
                }
            }
        });
    }

    // Universities Scatter Chart
    if (chartData.universities_scatter && chartData.universities_scatter.length > 0) {
        const universitiesScatterCtx = document.getElementById('universities-scatter-chart').getContext('2d');
        new Chart(universitiesScatterCtx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: 'Universidades',
                    data: chartData.universities_scatter.map(item => ({
                        x: item.x,
                        y: item.y,
                        name: item.name,
                        cancellations: item.cancellations,
                        total: item.total
                    })),
                    backgroundColor: function(context) {
                        const value = context.parsed.y;
                        if (value > 20) return 'rgba(239, 68, 68, 0.7)';
                        if (value > 15) return 'rgba(245, 158, 11, 0.7)';
                        return 'rgba(34, 197, 94, 0.7)';
                    },
                    borderColor: function(context) {
                        const value = context.parsed.y;
                        if (value > 20) return 'rgb(239, 68, 68)';
                        if (value > 15) return 'rgb(245, 158, 11)';
                        return 'rgb(34, 197, 94)';
                    },
                    borderWidth: 2,
                    pointRadius: 8,
                    pointHoverRadius: 12
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                return context[0].raw.name;
                            },
                            label: function(context) {
                                const data = context.raw;
                                return [
                                    `Total: ${data.total} implementações`,
                                    `Cancelamentos: ${data.cancellations}`,
                                    `Taxa: ${data.y}%`
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Total de Implementações'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Taxa de Cancelamento (%)'
                        },
                        beginAtZero: true
                    }
                },
                onClick: function(event, elements) {
                    if (elements.length > 0) {
                        const dataIndex = elements[0].index;
                        const university = chartData.universities_scatter[dataIndex];
                        highlightUniversity(university.name);
                    }
                }
            }
        });
    }
});

// Highlight functions for table interaction
function highlightUniversity(name) {
    console.log('Highlighting university:', name);
    // Add visual feedback or additional actions here
}
</script>
{% endblock %}
