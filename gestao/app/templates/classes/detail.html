{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table, table_filters %}

{% block title %}{{ class_name|e }} - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title=class_name,
    subtitle="Análise detalhada da turma, incluindo métricas de desempenho e alunos.",
    stats=[
        {
            "label": "Implantações Finalizadas",
            "value": finalized_count,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />'
        },
        {
            "label": "Implantações Ativas",
            "value": active_count,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />'
        },
        {
            "label": "Alunos",
            "value": student_count,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />'
        }
    ],
    bg_class="bg-gray-100"
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Class Info Card -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">{{ class_name|e }}</h2>
                <p class="text-gray-500 mt-1">{{ course|e }} - {{ university|e }}</p>
                <p class="text-gray-500">{{ university_city|e }}, {{ university_state|e }}</p>
            </div>
            <div class="mt-4 md:mt-0">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    {% if class_status == 'Ativo' %}
                        bg-green-100 text-green-800
                    {% elif class_status == 'Pendente' %}
                        bg-yellow-100 text-yellow-800
                    {% else %}
                        bg-red-100 text-red-800
                    {% endif %}">
                    {{ class_status|e }}
                </span>
            </div>
        </div>
    </div>

    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- KPI: Alunos -->
        {{ kpi_card(
            title="Total de Alunos",
            value=student_count,
            subtitle="Alunos na turma",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />',
            color="primary"
        )|e }}

        <!-- KPI: Receita Recorrente -->
        {{ kpi_card(
            title="Receita Recorrente",
            value=mrr_total,
            subtitle="MRR da turma",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="success"
        )|e }}

        <!-- KPI: Receita Potencial -->
        {{ kpi_card(
            title="Receita Potencial",
            value=potential_revenue,
            subtitle="Incluindo oportunidades",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />',
            color="warning"
        )|e }}

        <!-- KPI: Taxa de Conversão -->
        {{ kpi_card(
            title="Taxa de Conversão",
            value=conversion_rate|string + "%",
            subtitle="Oportunidades finalizadas",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />',
            color="info"
        ) }}
    </div>

    <!-- Implementation and Opportunity Tables -->
    <div class="mb-8">
        <!-- Tabs -->
        <div class="mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button type="button"
                            onclick="showTab('finalized')"
                            id="finalized-tab"
                            class="border-primary text-primary hover:text-primary hover:border-primary px-1 py-4 font-medium text-sm border-b-2">
                        Finalizadas ({{ finalized_implementations|length }})
                    </button>
                    <button type="button"
                            onclick="showTab('active')"
                            id="active-tab"
                            class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 font-medium text-sm border-b-2">
                        Em Andamento ({{ active_implementations|length }})
                    </button>
                    <button type="button"
                            onclick="showTab('opportunities')"
                            id="opportunities-tab"
                            class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 font-medium text-sm border-b-2">
                        Oportunidades ({{ opportunities|length }})
                    </button>
                </nav>
            </div>
        </div>

        <!-- Finalized Implementations Table -->
        <div id="finalized-content" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                <div class="overflow-x-auto">
                    <div class="overflow-y-auto max-h-[500px]">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100 sticky top-0 z-10">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Lead</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Produto</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Valor</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Data de Finalização</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Responsável</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for impl in finalized_implementations %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ impl.lead_name|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.product|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.value|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.end_date|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.responsible|e }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">Nenhuma implantação finalizada encontrada.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Implementations Table -->
        <div id="active-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                <div class="overflow-x-auto">
                    <div class="overflow-y-auto max-h-[500px]">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100 sticky top-0 z-10">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Lead</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Produto</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Fase</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Data Prevista</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Responsável</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for impl in active_implementations %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ impl.lead_name|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.product|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.phase|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.expected_end_date|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.responsible|e }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">Nenhuma implantação ativa encontrada.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Opportunities Table -->
        <div id="opportunities-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                <div class="overflow-x-auto">
                    <div class="overflow-y-auto max-h-[500px]">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100 sticky top-0 z-10">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Lead</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Produto</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Etapa</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Valor</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Responsável</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for opp in opportunities %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ opp.lead_name|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ opp.product|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ opp.stage|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ opp.value|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ opp.responsible|e }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">Nenhuma oportunidade encontrada.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Links Section -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Links Relacionados</h3>
        <div class="flex flex-wrap gap-4">
            <a href="{{ url_for('university.detail', university=university) }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <svg class="mr-2 -ml-1 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path d="M12 14l9-5-9-5-9 5 9 5z" />
                    <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                </svg>
                Ver Universidade
            </a>
            <a href="{{ url_for('class.index') }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <svg class="mr-2 -ml-1 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                Todas as Turmas
            </a>
            <a href="{{ url_for('implementation.index') }}?course={{ course|e }}&university={{ university|e }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <svg class="mr-2 -ml-1 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                Ver Implantações
            </a>
            {% if active_count > 0 or finalized_count > 0 %}
            <a href="{{ url_for('responsible.index') }}?class={{ class_name|lower|replace(' ', '-') }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <svg class="mr-2 -ml-1 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Ver Responsáveis
            </a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function showTab(tabId) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.add('hidden');
        });

        // Show the selected tab content
        document.getElementById(`${tabId}-content`).classList.remove('hidden');

        // Update tab button styles
        document.querySelectorAll('button[id$="-tab"]').forEach(tab => {
            tab.classList.remove('border-primary', 'text-primary');
            tab.classList.add('border-transparent', 'text-gray-500');
        });

        document.getElementById(`${tabId}-tab`).classList.remove('border-transparent', 'text-gray-500');
        document.getElementById(`${tabId}-tab`).classList.add('border-primary', 'text-primary');
    }
</script>
{% endblock %}
