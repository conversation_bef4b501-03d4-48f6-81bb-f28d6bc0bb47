{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table, table_filters, advanced_insight %}

{% block title %}Turmas - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Análise de Turmas",
    subtitle="Acompanhe o desempenho por turma, visualize métricas de conversão e tendências.",
    stats=[
        {
            "label": "Turmas Ativas",
            "value": active_classes,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />'
        },
        {
            "label": "Total de Alunos",
            "value": total_students,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />'
        },
        {
            "label": "Alunos por Turma",
            "value": avg_students_per_class,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />'
        }
    ],
    bg_class="bg-gray-100"
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Insights Section -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-2">Insights de Turmas</h2>
        <p class="text-sm text-gray-500 mb-6">Análises e recomendações baseadas nos dados de turmas.</p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {{ advanced_insight(
                title="Desempenho Global das Turmas",
                content="A análise global mostra que turmas com mais de 30 alunos têm uma taxa de conversão 25% maior que turmas menores.",
                icon_type="trend",
                color="blue",
                metrics=[
                    {"label": "Turmas Grandes", "value": "42.5%", "trend": "+25%", "trend_positive": true},
                    {"label": "Turmas Pequenas", "value": "34.0%"}
                ]
            ) }}

            {{ advanced_insight(
                title="Impacto dos Cupons",
                content="Turmas com distribuição de cupons têm uma taxa de conversão 18% maior que turmas sem cupons.",
                icon_type="success",
                color="green",
                metrics=[
                    {"label": "Com Cupons", "value": "38.2%", "trend": "+18%", "trend_positive": true},
                    {"label": "Sem Cupons", "value": "32.4%"}
                ]
            ) }}

            {{ advanced_insight(
                title="Implementações com Cupons",
                content="Cupons com pagamento de entrada têm 2.5x mais chances de resultar em implementações finalizadas.",
                icon_type="money",
                metrics=[
                    {"label": "Taxa de Finalização", "value": "62.8%", "trend": "+150%", "trend_positive": true}
                ]
            ) }}
        </div>
    </div>

    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- KPI: Total de Turmas -->
        {{ kpi_card(
            title="Total de Turmas",
            value=total_classes,
            subtitle="Turmas cadastradas",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />',
            color="primary"
        )|e }}

        <!-- KPI: Turmas Ativas -->
        {{ kpi_card(
            title="Turmas Ativas",
            value=active_classes,
            subtitle="Com implantações finalizadas",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="success"
        )|e }}

        <!-- KPI: Total de Alunos -->
        {{ kpi_card(
            title="Total de Alunos",
            value=total_students,
            subtitle="Alunos em todas as turmas",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />',
            color="info"
        )|e }}

        <!-- KPI: Alunos Ativos -->
        {{ kpi_card(
            title="Alunos Ativos",
            value=active_students,
            subtitle="Com implantações finalizadas",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />',
            color="warning"
        )|e }}
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Chart: Status das Turmas -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Status das Turmas</h3>
            <div class="h-64">
                {{ chart(
                    id="class-status-chart",
                    type="pie",
                    data=class_status_data,
                    colors=["#10B981", "#F59E0B", "#EF4444"]
                )|e }}
            </div>
        </div>

        <!-- Chart: Turmas por Curso -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Turmas por Curso</h3>
            <div class="h-64">
                {{ chart(
                    id="class-by-course-chart",
                    type="bar",
                    data=class_by_course_data,
                    colors=["#3B82F6"]
                )|e }}
            </div>
        </div>

        <!-- Chart: Tendência Mensal de Turmas -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Tendência Mensal de Turmas</h3>
            <div class="h-64">
                {{ chart(
                    id="monthly-trend-chart",
                    type="line",
                    data=monthly_trend_data,
                    colors=["#8B5CF6"]
                )|e }}
            </div>
        </div>

        <!-- Chart: Turmas por Produto -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Turmas por Produto</h3>
            <div class="h-64">
                {{ chart(
                    id="product-chart",
                    type="pie",
                    data=top_product_classes,
                    colors=["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6"]
                )|e }}
            </div>
        </div>
    </div>

    <!-- Performance Analysis Section -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Análise de Desempenho das Turmas</h3>

        {% if class_performance_data %}
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- MRR Chart -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-2">Top 10 Turmas por MRR</h4>
                <div class="h-80">
                    {{ chart(
                        id="class-mrr-chart",
                        type="bar",
                        data=class_mrr_chart_data,
                        colors=["#3B82F6"],
                        horizontal=true
                    )|e }}
                </div>
            </div>

            <!-- Students Chart -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-2">Top 10 Turmas por Número de Alunos</h4>
                <div class="h-80">
                    {{ chart(
                        id="class-students-chart",
                        type="bar",
                        data=class_students_chart_data,
                        colors=["#10B981"],
                        horizontal=true
                    )|e }}
                </div>
            </div>

            <!-- Conversion Rate Chart -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-2">Top 10 Turmas por Taxa de Conversão (%)</h4>
                <div class="h-80">
                    {{ chart(
                        id="class-conversion-chart",
                        type="bar",
                        data=class_conversion_chart_data,
                        colors=["#F59E0B"],
                        horizontal=true
                    )|e }}
                </div>
            </div>
        </div>
        {% else %}
        <div class="text-center py-8 text-gray-500">
            <p>Não há dados de desempenho disponíveis para as turmas.</p>
        </div>
        {% endif %}
    </div>

    <!-- Top Universities and Responsibles Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Top Universities Section -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Universidades com Mais Turmas</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-100">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Universidade</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Quantidade de Turmas</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for university in top_university_classes %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ university.name|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ university.value|e }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Top Responsibles Section -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Responsáveis com Mais Turmas</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-100">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Responsável</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Quantidade de Turmas</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for responsible in top_responsible_classes %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ responsible.name|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ responsible.value|e }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Classes Table Section -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Lista de Turmas</h3>
        </div>

        <!-- Table Filters -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            {{ table_filters(
                id="classes-table-filters",
                filters=filters_json
            )|e }}
        </div>

        <div class="overflow-x-auto">
            <div class="overflow-y-auto max-h-[500px]">
                <table class="min-w-full divide-y divide-gray-200" id="classes-table">
                    <thead class="bg-gray-100 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Turma</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Curso</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Universidade</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Alunos</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Receita</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Ações</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for class in classes %}
                        <tr class="hover:bg-gray-50 transition-colors duration-150"
                            data-status="{{ (class.status|string)|lower }}"
                            data-course="{{ (class.course|string)|lower|replace(' ', '-') }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ class.name|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ class.course|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ class.university|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ class.students|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if class.status == 'Ativo' %}
                                        bg-green-100 text-green-800
                                    {% elif class.status == 'Pendente' %}
                                        bg-yellow-100 text-yellow-800
                                    {% else %}
                                        bg-red-100 text-red-800
                                    {% endif %}">
                                    {{ class.status|e }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ class.revenue|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <a href="{{ url_for('class.detail', class_name=(class.name|string)|lower|replace(' ', '-')) }}"
                                   class="text-primary hover:underline inline-flex items-center">
                                    <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                    Detalhes
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Table filtering
        const statusFilter = document.getElementById('status-filter');
        const courseFilter = document.getElementById('course-filter');
        const table = document.getElementById('classes-table');
        const rows = table.querySelectorAll('tbody tr');

        function filterTable() {
            const statusValue = statusFilter.value;
            const courseValue = courseFilter.value;

            rows.forEach(row => {
                const statusMatch = statusValue === 'all' || row.dataset.status === statusValue;
                const courseMatch = courseValue === 'all' || row.dataset.course === courseValue;

                if (statusMatch && courseMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        statusFilter.addEventListener('change', filterTable);
        courseFilter.addEventListener('change', filterTable);
    });
</script>
{% endblock %}
