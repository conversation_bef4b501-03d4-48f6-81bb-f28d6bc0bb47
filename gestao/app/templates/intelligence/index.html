{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table %}

{% block title %}Intelligence Index - Amigo DataHub{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Enhanced Hero Section with Full Width -->
    <div class="bg-gradient-to-br from-blue-50 via-blue-100 to-indigo-100 text-gray-900 relative overflow-hidden border-b border-blue-200">
        <!-- Geometric Background Elements -->
        <div class="absolute inset-0 z-0">
            <div class="absolute top-20 right-20 w-96 h-96 rounded-full bg-blue-200 opacity-20"></div>
            <div class="absolute bottom-10 left-10 w-0 h-0 border-l-[150px] border-l-transparent border-b-[200px] border-b-blue-200 border-r-[150px] border-r-transparent opacity-20"></div>
            <div class="absolute top-1/3 left-1/3 w-48 h-48 bg-indigo-200 opacity-20 rotate-45"></div>
            <div class="absolute bottom-1/4 right-1/4 w-32 h-32 bg-blue-300 opacity-25 rounded-full"></div>
        </div>

        <div class="relative z-10 px-6 py-16">
            <div class="max-w-7xl mx-auto">
                <div class="text-center mb-12">
                    <h1 class="text-5xl font-bold mb-4 text-gray-900">
                        Intelligence Index
                        <span class="block text-3xl font-normal text-gray-700 mt-2">Índice Composto de Performance Comercial</span>
                    </h1>
                    <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
                        Análise econométrica avançada que combina múltiplas dimensões de performance para identificar
                        padrões comportamentais e oportunidades de crescimento no pipeline comercial.
                    </p>
                </div>

                <!-- Mathematical Formula Section -->
                <div class="bg-white rounded-2xl p-10 mb-8 border border-gray-200 shadow-sm">
                    <h2 class="text-3xl font-bold mb-8 text-center text-gray-900">Modelo Econométrico do Intelligence Index</h2>

                    <!-- Main Formula -->
                    <div class="text-center mb-8">
                        <div class="bg-gray-50 rounded-lg p-8 border-2 border-gray-200 inline-block">
                            <div class="text-3xl font-mono text-gray-900 leading-relaxed">
                                <span class="text-blue-600 font-bold">I</span><sub class="text-lg text-gray-600">composite</sub> =
                                <span class="text-blue-600">∑</span><sub class="text-sm text-gray-600">i=1</sub><sup class="text-sm text-gray-600">5</sup>
                                <span class="text-blue-600">w</span><sub class="text-sm text-gray-600">i</sub> ×
                                <span class="text-blue-600">C</span><sub class="text-sm text-gray-600">i</sub><sup class="text-sm text-gray-600">*</sup>
                            </div>
                            <div class="text-sm text-gray-600 mt-4 font-medium">
                                Onde <span class="text-blue-600">C<sub>i</sub><sup>*</sup></span> representa os componentes ajustados econometricamente
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Components -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        <!-- Left Column: Component Formulas -->
                        <div class="space-y-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Componentes Detalhados:</h3>

                            <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                                <div class="font-mono text-lg text-gray-900 mb-2">
                                    <span class="text-blue-600">C₁*</span> = <span class="text-blue-600">C₁</span> × <span class="text-blue-600">φ</span> + <span class="text-blue-600">μ</span><sub class="text-sm">global</sub> × (1 - <span class="text-blue-600">φ</span>)
                                </div>
                                <div class="text-sm text-gray-700">Taxa de Conversão (w₁ = 0.25)</div>
                            </div>

                            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                <div class="font-mono text-lg text-gray-900 mb-2">
                                    <span class="text-blue-600">C₂*</span> = min(max((log₁₀(<span class="text-blue-600">R</span>) - 6) × 20, 0), 100)
                                </div>
                                <div class="text-sm text-gray-700">Score Faturamento (w₂ = 0.25)</div>
                            </div>

                            <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                                <div class="font-mono text-lg text-gray-900 mb-2">
                                    <span class="text-blue-600">C₃*</span> = min((<span class="text-blue-600">A</span> ÷ <span class="text-blue-600">T</span>) × 1000 × 2, 100)
                                </div>
                                <div class="text-sm text-gray-700">Densidade de Ativos (w₃ = 0.15)</div>
                            </div>

                            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                <div class="font-mono text-lg text-gray-900 mb-2">
                                    <span class="text-blue-600">C₄*</span> = <span class="text-blue-600">S₁</span> × <span class="text-blue-600">φ</span> + <span class="text-blue-600">σ</span><sub class="text-sm">global</sub> × (1 - <span class="text-blue-600">φ</span>)
                                </div>
                                <div class="text-sm text-gray-700">Taxa de Sucesso (w₄ = 0.20)</div>
                            </div>

                            <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                                <div class="font-mono text-lg text-gray-900 mb-2">
                                    <span class="text-blue-600">C₅*</span> = min(log₁₀(<span class="text-blue-600">O</span> + <span class="text-blue-600">I</span> + 1) × 20, 100)
                                </div>
                                <div class="text-sm text-gray-700">Volume de Negócios (w₅ = 0.15)</div>
                            </div>
                        </div>

                        <!-- Right Column: Econometric Adjustments -->
                        <div class="space-y-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Ajustes Econométricos:</h3>

                            <div class="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                                <div class="font-bold text-gray-900 mb-2">Fator de Confiança (Wilson Score):</div>
                                <div class="font-mono text-lg text-gray-900 mb-2">
                                    <span class="text-blue-600">φ</span> = <span class="text-blue-600">n</span> ÷ (<span class="text-blue-600">n</span> + <span class="text-blue-600">z</span><sub class="text-sm">α/2</sub>²)
                                </div>
                                <div class="text-sm text-gray-700">Onde z<sub>α/2</sub> = 1.96 (95% confiança)</div>
                            </div>

                            <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                                <div class="font-bold text-gray-900 mb-2">Shrinkage Bayesiano:</div>
                                <div class="font-mono text-sm text-gray-900 mb-2">
                                    <span class="text-blue-600">θ̂</span><sub class="text-sm">shrunk</sub> = <span class="text-blue-600">θ̂</span><sub class="text-sm">MLE</sub> × <span class="text-blue-600">φ</span> + <span class="text-blue-600">θ̂</span><sub class="text-sm">prior</sub> × (1 - <span class="text-blue-600">φ</span>)
                                </div>
                                <div class="text-sm text-gray-700">Reduz viés em amostras pequenas</div>
                            </div>

                            <div class="bg-red-50 rounded-lg p-4 border border-red-200">
                                <div class="font-bold text-gray-900 mb-2">Normalização Logarítmica:</div>
                                <div class="font-mono text-sm text-gray-900 mb-2">
                                    <span class="text-blue-600">f(x)</span> = min(max((log₁₀(<span class="text-blue-600">x</span>) - <span class="text-blue-600">β</span>) × <span class="text-blue-600">α</span>, 0), 100)
                                </div>
                                <div class="text-sm text-gray-700">Estabiliza variância e outliers</div>
                            </div>

                            <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
                                <div class="font-bold text-gray-900 mb-2">Variáveis do Modelo:</div>
                                <div class="text-sm text-gray-700 space-y-1">
                                    <div><span class="font-mono text-blue-600">O</span> = Oportunidades totais</div>
                                    <div><span class="font-mono text-blue-600">I</span> = Implementações totais</div>
                                    <div><span class="font-mono text-blue-600">R</span> = Receita total</div>
                                    <div><span class="font-mono text-blue-600">A</span> = Usuários ativos</div>
                                    <div><span class="font-mono text-blue-600">T</span> = Total de registros</div>
                                    <div><span class="font-mono text-blue-600">n</span> = Tamanho da amostra</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Final Formula Expansion -->
                    <div class="bg-gray-900 rounded-lg p-6 text-white">
                        <div class="text-center">
                            <div class="text-lg font-bold mb-4">Expansão Completa do Modelo:</div>
                            <div class="font-mono text-sm leading-relaxed">
                                <span class="text-yellow-300">I</span><sub class="text-xs">composite</sub> =
                                0.25 × [<span class="text-blue-300">C₁</span> × <span class="text-green-300">φ</span> + <span class="text-red-300">μ</span> × (1-<span class="text-green-300">φ</span>)] +
                                0.25 × [min(max((log₁₀(<span class="text-blue-300">R</span>)-6)×20,0),100)] +<br>
                                0.15 × [min((<span class="text-blue-300">A</span>÷<span class="text-blue-300">T</span>)×2000,100)] +
                                0.20 × [<span class="text-blue-300">S₁</span> × <span class="text-green-300">φ</span> + <span class="text-red-300">σ</span> × (1-<span class="text-green-300">φ</span>)] +
                                0.15 × [min(log₁₀(<span class="text-blue-300">O</span>+<span class="text-blue-300">I</span>+1)×20,100)]
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Key Benefits -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2 text-gray-900">Análise Multidimensional</h3>
                        <p class="text-gray-600 text-sm">Avalia performance por turma, estado e responsável simultaneamente</p>
                    </div>
                    <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2 text-gray-900">Benchmarking Inteligente</h3>
                        <p class="text-gray-600 text-sm">Identifica outliers e padrões de alta performance</p>
                    </div>
                    <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2 text-gray-900">Insights Acionáveis</h3>
                        <p class="text-gray-600 text-sm">Gera recomendações automáticas baseadas em dados</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content with Full Width -->
    <div class="px-6 py-8 space-y-8">

        <!-- Overall Intelligence Summary -->
        {% if intelligence_summary and 'error' not in intelligence_summary %}
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <div class="flex items-center justify-between mb-8">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">Resumo Geral do Índice</h2>
                    <p class="text-gray-600 mt-1">Performance consolidada de todas as dimensões analisadas</p>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span class="text-sm text-gray-600">Dados atualizados</span>
                </div>
            </div>

            <!-- Overall Index KPI - Enhanced -->
            {% if intelligence_summary.overall_index and 'error' not in intelligence_summary.overall_index %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
                <!-- Main Index -->
                <div class="lg:col-span-1 text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                    <div class="text-4xl font-bold text-blue-600 mb-2">{{ "{:.1f}".format(intelligence_summary.overall_index.composite_index) }}</div>
                    <div class="text-sm font-medium text-gray-700">Índice Composto</div>
                    <div class="text-xs text-gray-500 mt-1">Escala 0-100</div>
                </div>

                <!-- Components -->
                <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-100">
                    <div class="text-2xl font-bold text-blue-600 mb-1">{{ "{:.1f}%".format(intelligence_summary.overall_index.components.conversion_rate) }}</div>
                    <div class="text-xs font-medium text-gray-700">Taxa Conversão</div>
                    <div class="text-xs text-gray-500">Peso: 25%</div>
                </div>

                <div class="text-center p-4 bg-gray-50 rounded-lg border border-gray-100">
                    <div class="text-2xl font-bold text-gray-600 mb-1">{{ "{:.1f}".format(intelligence_summary.overall_index.components.revenue_score) }}</div>
                    <div class="text-xs font-medium text-gray-700">Score Faturamento</div>
                    <div class="text-xs text-gray-500">Peso: 25%</div>
                </div>

                <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-100">
                    <div class="text-2xl font-bold text-blue-600 mb-1">{{ "{:.1f}".format(intelligence_summary.overall_index.components.user_density) }}</div>
                    <div class="text-xs font-medium text-gray-700">Densidade Ativos</div>
                    <div class="text-xs text-gray-500">Peso: 15%</div>
                </div>

                <div class="text-center p-4 bg-gray-50 rounded-lg border border-gray-100">
                    <div class="text-2xl font-bold text-gray-600 mb-1">{{ "{:.1f}%".format(intelligence_summary.overall_index.components.success_rate) }}</div>
                    <div class="text-xs font-medium text-gray-700">Taxa Sucesso</div>
                    <div class="text-xs text-gray-500">Peso: 20%</div>
                </div>

                <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-100">
                    <div class="text-2xl font-bold text-blue-600 mb-1">{{ "{:.1f}".format(intelligence_summary.overall_index.components.volume_score) }}</div>
                    <div class="text-xs font-medium text-gray-700">Volume Score</div>
                    <div class="text-xs text-gray-500">Peso: 15%</div>
                </div>
            </div>
            {% endif %}

            <!-- Enhanced Insights -->
            {% if intelligence_summary.insights %}
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                <div class="flex items-center mb-4">
                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-blue-900">Insights Principais</h3>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {% for insight in intelligence_summary.insights %}
                    <div class="bg-white/70 rounded-lg p-4 border border-blue-200">
                        <div class="text-sm text-blue-800 font-medium">{{ insight }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Usuários Ativos Card -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900">Usuários Ativos</h3>
                        <p class="text-gray-600 mt-1">Implementações finalizadas gerando receita recorrente</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                        </svg>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Total de Usuários Ativos -->
                    <div class="text-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-100">
                        <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </div>
                        <div class="text-3xl font-bold text-green-600 mb-1">1.776</div>
                        <div class="text-sm font-medium text-gray-700">Implementações Finalizadas</div>
                        <div class="text-xs text-gray-500 mt-1">Gerando receita recorrente</div>
                    </div>

                    <!-- Status Ativo -->
                    <div class="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                        <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <div class="text-3xl font-bold text-blue-600 mb-1">100%</div>
                        <div class="text-sm font-medium text-gray-700">Status Ativo</div>
                        <div class="text-xs text-gray-500 mt-1">Todos gerando receita</div>
                    </div>

                    <!-- Tempo Médio -->
                    <div class="text-center p-6 bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl border border-gray-100">
                        <div class="w-12 h-12 bg-gray-500 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="text-3xl font-bold text-gray-600 mb-1">19</div>
                        <div class="text-sm font-medium text-gray-700">Tempo Médio</div>
                        <div class="text-xs text-gray-500 mt-1">Dias para ativação</div>
                    </div>
                </div>

                <!-- Detalhes Adicionais -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Critérios de Usuário Ativo</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Implementação finalizada com sucesso</li>
                                <li>• Gerando receita recorrente mensal</li>
                                <li>• Status de pagamento em dia</li>
                                <li>• Sistema em uso regular</li>
                            </ul>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Métricas de Performance</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Taxa de Ativação:</span>
                                    <span class="font-medium text-blue-600">31.5%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Receita Mensal:</span>
                                    <span class="font-medium text-green-600">R$ 2.1M</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Ticket Médio:</span>
                                    <span class="font-medium text-gray-600">R$ 1.182</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Index Behavior Analysis Charts -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Index Distribution Chart -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Distribuição do Índice</h3>
                        <p class="text-sm text-gray-600">Análise da distribuição de performance</p>
                    </div>
                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                </div>
                <div class="h-80">
                    <canvas id="indexDistributionChart"></canvas>
                </div>
            </div>

            <!-- Components Correlation Chart -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Correlação dos Componentes</h3>
                        <p class="text-sm text-gray-600">Relação entre os componentes do índice</p>
                    </div>
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
                <div class="h-80">
                    <canvas id="componentsCorrelationChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Performance Trends -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900">Análise de Tendências por Dimensão</h3>
                    <p class="text-gray-600">Comportamento do índice ao longo das diferentes dimensões</p>
                </div>
                <div class="flex space-x-2">
                    <button class="dimension-btn active px-4 py-2 bg-blue-500 text-white rounded-lg text-sm" data-dimension="turma">Turmas</button>
                    <button class="dimension-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg text-sm" data-dimension="estado">Estados</button>
                    <button class="dimension-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg text-sm" data-dimension="responsavel">Responsáveis</button>
                </div>
            </div>
            <div class="h-96">
                <canvas id="dimensionTrendsChart"></canvas>
            </div>
        </div>

        <!-- Enhanced Performance Distribution -->
        {% if intelligence_summary and intelligence_summary.performance_distribution %}
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <div class="flex items-center justify-between mb-8">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900">Distribuição de Performance</h3>
                    <p class="text-gray-600 mt-1">Categorização das entidades por nível de performance</p>
                </div>
                <div class="text-sm text-gray-500">
                    Total: {{ (intelligence_summary.performance_distribution.excellent or 0) + (intelligence_summary.performance_distribution.good or 0) + (intelligence_summary.performance_distribution.average or 0) + (intelligence_summary.performance_distribution.poor or 0) }} entidades
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                    <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                    </div>
                    <div class="text-3xl font-bold text-blue-600 mb-1">{{ intelligence_summary.performance_distribution.excellent or 0 }}</div>
                    <div class="text-sm font-medium text-gray-700">Excelente</div>
                    <div class="text-xs text-gray-500 mt-1">Índice ≥ 70</div>
                </div>

                <div class="text-center p-6 bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl border border-gray-100">
                    <div class="w-12 h-12 bg-gray-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                        </svg>
                    </div>
                    <div class="text-3xl font-bold text-gray-600 mb-1">{{ intelligence_summary.performance_distribution.good or 0 }}</div>
                    <div class="text-sm font-medium text-gray-700">Bom</div>
                    <div class="text-xs text-gray-500 mt-1">Índice 50-69</div>
                </div>

                <div class="text-center p-6 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200">
                    <div class="w-12 h-12 bg-gray-400 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                        </svg>
                    </div>
                    <div class="text-3xl font-bold text-gray-600 mb-1">{{ intelligence_summary.performance_distribution.average or 0 }}</div>
                    <div class="text-sm font-medium text-gray-700">Médio</div>
                    <div class="text-xs text-gray-500 mt-1">Índice 30-49</div>
                </div>

                <div class="text-center p-6 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl border border-gray-300">
                    <div class="w-12 h-12 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </div>
                    <div class="text-3xl font-bold text-gray-600 mb-1">{{ intelligence_summary.performance_distribution.poor or 0 }}</div>
                    <div class="text-sm font-medium text-gray-700">Baixo</div>
                    <div class="text-xs text-gray-500 mt-1">Índice < 30</div>
                </div>
            </div>

            <!-- Performance Distribution Chart -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <h4 class="text-lg font-medium text-gray-900 mb-4">Visualização da Distribuição</h4>
                <div class="h-64">
                    <canvas id="performanceDistributionChart"></canvas>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Enhanced Analysis by Dimensions -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

            <!-- Turma Analysis -->
            {% if turma_analysis and 'error' not in turma_analysis %}
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Top Turmas</h3>
                        <p class="text-sm text-gray-600">Performance por turma</p>
                    </div>
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                    </div>
                </div>

                <div class="space-y-4">
                    {% for turma in turma_analysis.top_performers[:5] %}
                    <div class="p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-gray-100">
                        <div class="flex items-center justify-between mb-2">
                            <div class="font-semibold text-gray-900 text-sm">{{ turma.dimension_value }}</div>
                            <div class="flex items-center space-x-2">
                                <div class="text-xl font-bold text-blue-600">{{ "{:.1f}".format(turma.composite_index) }}</div>
                                {% if turma_analysis.statistics %}
                                    {% set mean_value = turma_analysis.statistics.mean %}
                                    {% set diff = turma.composite_index - mean_value %}
                                    <div class="text-xs {% if diff > 0 %}text-green-600{% else %}text-red-600{% endif %} font-medium">
                                        {% if diff > 0 %}↗ +{% else %}↘ {% endif %}{{ "{:.1f}".format((diff if diff > 0 else -diff) / 10) }}%
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="grid grid-cols-3 gap-2 text-xs">
                            <div class="text-gray-600">
                                <span class="font-medium">Conv:</span> {{ "{:.1f}%".format(turma.components.conversion_rate) }}
                            </div>
                            <div class="text-gray-600">
                                <span class="font-medium">Sucesso:</span> {{ "{:.1f}%".format(turma.components.success_rate) }}
                            </div>
                            <div class="text-gray-600">
                                <span class="font-medium">Volume:</span> {{ "{:.1f}".format(turma.components.volume_score) }}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                {% if turma_analysis.statistics %}
                <div class="mt-6 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ "{:.1f}".format(turma_analysis.statistics.mean) }}</div>
                            <div class="text-xs text-gray-600">Média</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ "{:.1f}".format(turma_analysis.statistics.median) }}</div>
                            <div class="text-xs text-gray-600">Mediana</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ turma_analysis.statistics.count }}</div>
                            <div class="text-xs text-gray-600">Total</div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- Estado Analysis -->
            {% if estado_analysis and 'error' not in estado_analysis %}
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Top Estados</h3>
                        <p class="text-sm text-gray-600">Performance por estado</p>
                    </div>
                    <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                    </div>
                </div>

                <div class="space-y-4">
                    {% for estado in estado_analysis.top_performers[:5] %}
                    <div class="p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-100">
                        <div class="flex items-center justify-between mb-2">
                            <div class="font-semibold text-gray-900 text-sm">{{ estado.dimension_value }}</div>
                            <div class="flex items-center space-x-2">
                                <div class="text-xl font-bold text-gray-600">{{ "{:.1f}".format(estado.composite_index) }}</div>
                                {% if estado_analysis.statistics %}
                                    {% set mean_value = estado_analysis.statistics.mean %}
                                    {% set diff = estado.composite_index - mean_value %}
                                    <div class="text-xs {% if diff > 0 %}text-green-600{% else %}text-red-600{% endif %} font-medium">
                                        {% if diff > 0 %}↗ +{% else %}↘ {% endif %}{{ "{:.1f}".format((diff if diff > 0 else -diff) / 10) }}%
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="grid grid-cols-3 gap-2 text-xs">
                            <div class="text-gray-600">
                                <span class="font-medium">Conv:</span> {{ "{:.1f}%".format(estado.components.conversion_rate) }}
                            </div>
                            <div class="text-gray-600">
                                <span class="font-medium">Sucesso:</span> {{ "{:.1f}%".format(estado.components.success_rate) }}
                            </div>
                            <div class="text-gray-600">
                                <span class="font-medium">Volume:</span> {{ "{:.1f}".format(estado.components.volume_score) }}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                {% if estado_analysis.statistics %}
                <div class="mt-6 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ "{:.1f}".format(estado_analysis.statistics.mean) }}</div>
                            <div class="text-xs text-gray-600">Média</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ "{:.1f}".format(estado_analysis.statistics.median) }}</div>
                            <div class="text-xs text-gray-600">Mediana</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ estado_analysis.statistics.count }}</div>
                            <div class="text-xs text-gray-600">Total</div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- Responsável Analysis -->
            {% if responsavel_analysis and 'error' not in responsavel_analysis %}
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Top Responsáveis</h3>
                        <p class="text-sm text-gray-600">Performance por responsável</p>
                    </div>
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                    </div>
                </div>

                <div class="space-y-4">
                    {% for resp in responsavel_analysis.top_performers[:5] %}
                    <div class="p-4 bg-gradient-to-r from-blue-50 to-gray-50 rounded-lg border border-gray-100">
                        <div class="flex items-center justify-between mb-2">
                            <div class="font-semibold text-gray-900 text-sm">{{ resp.dimension_value }}</div>
                            <div class="flex items-center space-x-2">
                                <div class="text-xl font-bold text-blue-600">{{ "{:.1f}".format(resp.composite_index) }}</div>
                                {% if responsavel_analysis.statistics %}
                                    {% set mean_value = responsavel_analysis.statistics.mean %}
                                    {% set diff = resp.composite_index - mean_value %}
                                    <div class="text-xs {% if diff > 0 %}text-green-600{% else %}text-red-600{% endif %} font-medium">
                                        {% if diff > 0 %}↗ +{% else %}↘ {% endif %}{{ "{:.1f}".format((diff if diff > 0 else -diff) / 10) }}%
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="grid grid-cols-3 gap-2 text-xs">
                            <div class="text-gray-600">
                                <span class="font-medium">Conv:</span> {{ "{:.1f}%".format(resp.components.conversion_rate) }}
                            </div>
                            <div class="text-gray-600">
                                <span class="font-medium">Sucesso:</span> {{ "{:.1f}%".format(resp.components.success_rate) }}
                            </div>
                            <div class="text-gray-600">
                                <span class="font-medium">Volume:</span> {{ "{:.1f}".format(resp.components.volume_score) }}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                {% if responsavel_analysis.statistics %}
                <div class="mt-6 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ "{:.1f}".format(responsavel_analysis.statistics.mean) }}</div>
                            <div class="text-xs text-gray-600">Média</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ "{:.1f}".format(responsavel_analysis.statistics.median) }}</div>
                            <div class="text-xs text-gray-600">Mediana</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ responsavel_analysis.statistics.count }}</div>
                            <div class="text-xs text-gray-600">Total</div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <!-- Complete Analysis Tables -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold text-gray-900 mb-2">Análise Completa por Dimensões</h3>
                <p class="text-gray-600">Visualização detalhada de todas as entidades com rolagem otimizada</p>
            </div>

            <!-- Tab Navigation -->
            <div class="flex justify-center mb-8">
                <div class="bg-gray-100 rounded-lg p-1 flex space-x-1">
                    <button class="table-tab-btn active px-6 py-2 bg-blue-500 text-white rounded-md text-sm font-medium transition-all" data-table="turmas">
                        Todas as Turmas
                    </button>
                    <button class="table-tab-btn px-6 py-2 text-gray-600 rounded-md text-sm font-medium transition-all hover:bg-gray-200" data-table="estados">
                        Todos os Estados
                    </button>
                    <button class="table-tab-btn px-6 py-2 text-gray-600 rounded-md text-sm font-medium transition-all hover:bg-gray-200" data-table="responsaveis">
                        Todos os Responsáveis
                    </button>
                </div>
            </div>

            <!-- Turmas Table -->
            {% if turma_analysis and 'error' not in turma_analysis %}
            <div id="turmas-table" class="table-content">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-lg font-semibold text-gray-900">Ranking Completo de Turmas</h4>
                    <div class="text-sm text-gray-500">
                        Total: {{ turma_analysis.statistics.count if turma_analysis.statistics else 0 }} turmas
                    </div>
                </div>

                <div class="overflow-hidden rounded-lg border border-gray-200">
                    <div class="max-h-96 overflow-y-auto">
                        <div class="grid gap-2 p-4">
                            {% for turma in turma_analysis.results %}
                            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-gray-100 hover:shadow-sm transition-shadow">
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="font-semibold text-gray-900 text-sm">{{ turma.dimension_value }}</div>
                                        <div class="flex items-center space-x-3">
                                            <div class="text-xl font-bold text-blue-600">{{ "{:.1f}".format(turma.composite_index) }}</div>
                                            {% if turma_analysis.statistics %}
                                                {% set mean_value = turma_analysis.statistics.mean %}
                                                {% set diff = turma.composite_index - mean_value %}
                                                <div class="text-xs {% if diff > 0 %}text-green-600{% else %}text-red-600{% endif %} font-medium">
                                                    {% if diff > 0 %}↗ +{% else %}↘ {% endif %}{{ "{:.1f}".format((diff if diff > 0 else -diff) / 10) }}%
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-5 gap-3 text-xs">
                                        <div class="text-gray-600">
                                            <span class="font-medium">Conv:</span> {{ "{:.1f}%".format(turma.components.conversion_rate) }}
                                        </div>
                                        <div class="text-gray-600">
                                            <span class="font-medium">Fat:</span> {{ "{:.1f}".format(turma.components.revenue_score) }}
                                        </div>
                                        <div class="text-gray-600">
                                            <span class="font-medium">Dens:</span> {{ "{:.1f}".format(turma.components.user_density) }}
                                        </div>
                                        <div class="text-gray-600">
                                            <span class="font-medium">Suc:</span> {{ "{:.1f}%".format(turma.components.success_rate) }}
                                        </div>
                                        <div class="text-gray-600">
                                            <span class="font-medium">Vol:</span> {{ "{:.1f}".format(turma.components.volume_score) }}
                                        </div>
                                    </div>
                                    <div class="mt-2 text-xs text-gray-500">
                                        Oportunidades: {{ turma.raw_metrics.total_opportunities }} |
                                        Implementações: {{ turma.raw_metrics.total_implementations }} |
                                        Ativos: {{ turma.raw_metrics.active_users }}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Estados Table -->
            {% if estado_analysis and 'error' not in estado_analysis %}
            <div id="estados-table" class="table-content hidden">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-lg font-semibold text-gray-900">Ranking Completo de Estados</h4>
                    <div class="text-sm text-gray-500">
                        Total: {{ estado_analysis.statistics.count if estado_analysis.statistics else 0 }} estados
                    </div>
                </div>

                <div class="overflow-hidden rounded-lg border border-gray-200">
                    <div class="max-h-96 overflow-y-auto">
                        <div class="grid gap-2 p-4">
                            {% for estado in estado_analysis.results %}
                            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-100 hover:shadow-sm transition-shadow">
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="font-semibold text-gray-900 text-sm">{{ estado.dimension_value }}</div>
                                        <div class="flex items-center space-x-3">
                                            <div class="text-xl font-bold text-gray-600">{{ "{:.1f}".format(estado.composite_index) }}</div>
                                            {% set mean_value = estado_analysis.statistics.mean if estado_analysis.statistics else 50 %}
                                            {% set diff = estado.composite_index - mean_value %}
                                            <div class="text-xs {% if diff > 0 %}text-green-600{% else %}text-red-600{% endif %} font-medium">
                                                {% if diff > 0 %}↗ +{% else %}↘ {% endif %}{{ "{:.1f}".format((diff if diff > 0 else -diff) / 10) }}%
                                            </div>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-5 gap-3 text-xs">
                                        <div class="text-gray-600">
                                            <span class="font-medium">Conv:</span> {{ "{:.1f}%".format(estado.components.conversion_rate) }}
                                        </div>
                                        <div class="text-gray-600">
                                            <span class="font-medium">Fat:</span> {{ "{:.1f}".format(estado.components.revenue_score) }}
                                        </div>
                                        <div class="text-gray-600">
                                            <span class="font-medium">Dens:</span> {{ "{:.1f}".format(estado.components.user_density) }}
                                        </div>
                                        <div class="text-gray-600">
                                            <span class="font-medium">Suc:</span> {{ "{:.1f}%".format(estado.components.success_rate) }}
                                        </div>
                                        <div class="text-gray-600">
                                            <span class="font-medium">Vol:</span> {{ "{:.1f}".format(estado.components.volume_score) }}
                                        </div>
                                    </div>
                                    <div class="mt-2 text-xs text-gray-500">
                                        Oportunidades: {{ estado.raw_metrics.total_opportunities }} |
                                        Implementações: {{ estado.raw_metrics.total_implementations }} |
                                        Ativos: {{ estado.raw_metrics.active_users }}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Responsáveis Table -->
            {% if responsavel_analysis and 'error' not in responsavel_analysis %}
            <div id="responsaveis-table" class="table-content hidden">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-lg font-semibold text-gray-900">Ranking Completo de Responsáveis</h4>
                    <div class="text-sm text-gray-500">
                        Total: {{ responsavel_analysis.statistics.count if responsavel_analysis.statistics else 0 }} responsáveis
                    </div>
                </div>

                <div class="overflow-hidden rounded-lg border border-gray-200">
                    <div class="max-h-96 overflow-y-auto">
                        <div class="grid gap-2 p-4">
                            {% for resp in responsavel_analysis.results %}
                            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-gray-50 rounded-lg border border-gray-100 hover:shadow-sm transition-shadow">
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="font-semibold text-gray-900 text-sm">{{ resp.dimension_value }}</div>
                                        <div class="flex items-center space-x-3">
                                            <div class="text-xl font-bold text-blue-600">{{ "{:.1f}".format(resp.composite_index) }}</div>
                                            {% set mean_value = responsavel_analysis.statistics.mean if responsavel_analysis.statistics else 50 %}
                                            {% set diff = resp.composite_index - mean_value %}
                                            <div class="text-xs {% if diff > 0 %}text-green-600{% else %}text-red-600{% endif %} font-medium">
                                                {% if diff > 0 %}↗ +{% else %}↘ {% endif %}{{ "{:.1f}".format((diff if diff > 0 else -diff) / 10) }}%
                                            </div>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-5 gap-3 text-xs">
                                        <div class="text-gray-600">
                                            <span class="font-medium">Conv:</span> {{ "{:.1f}%".format(resp.components.conversion_rate) }}
                                        </div>
                                        <div class="text-gray-600">
                                            <span class="font-medium">Fat:</span> {{ "{:.1f}".format(resp.components.revenue_score) }}
                                        </div>
                                        <div class="text-gray-600">
                                            <span class="font-medium">Dens:</span> {{ "{:.1f}".format(resp.components.user_density) }}
                                        </div>
                                        <div class="text-gray-600">
                                            <span class="font-medium">Suc:</span> {{ "{:.1f}%".format(resp.components.success_rate) }}
                                        </div>
                                        <div class="text-gray-600">
                                            <span class="font-medium">Vol:</span> {{ "{:.1f}".format(resp.components.volume_score) }}
                                        </div>
                                    </div>
                                    <div class="mt-2 text-xs text-gray-500">
                                        Oportunidades: {{ resp.raw_metrics.total_opportunities }} |
                                        Implementações: {{ resp.raw_metrics.total_implementations }} |
                                        Ativos: {{ resp.raw_metrics.active_users }}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Enhanced Methodology Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold text-gray-900 mb-2">Metodologia do Intelligence Index</h3>
                <p class="text-gray-600">Fundamentos econométricos e aplicação prática do índice composto</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Left Column: Components -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        Componentes do Índice
                    </h4>
                    <div class="space-y-4">
                        <div class="p-4 bg-blue-50 rounded-lg border border-blue-100">
                            <div class="font-semibold text-blue-800 mb-1">Taxa de Conversão (25%)</div>
                            <div class="text-sm text-gray-600 mb-2">Oportunidades que se tornam implementações (ajustada por confiança)</div>
                            <div class="text-xs text-gray-500">Fórmula: Taxa_bruta × Fator_confiança + Taxa_global × (1 - Fator_confiança)</div>
                        </div>

                        <div class="p-4 bg-gray-50 rounded-lg border border-gray-100">
                            <div class="font-semibold text-gray-800 mb-1">Score Faturamento (25%)</div>
                            <div class="text-sm text-gray-600 mb-2">Receita normalizada em escala logarítmica</div>
                            <div class="text-xs text-gray-500">Fórmula: min(max((log₁₀(receita) - 6) × 20, 0), 100)</div>
                        </div>

                        <div class="p-4 bg-blue-50 rounded-lg border border-blue-100">
                            <div class="font-semibold text-blue-800 mb-1">Densidade de Ativos (15%)</div>
                            <div class="text-sm text-gray-600 mb-2">Usuários ativos por 1000 registros</div>
                            <div class="text-xs text-gray-500">Fórmula: min((ativos ÷ total_registros) × 1000 × 2, 100)</div>
                        </div>

                        <div class="p-4 bg-gray-50 rounded-lg border border-gray-100">
                            <div class="font-semibold text-gray-800 mb-1">Taxa de Sucesso (20%)</div>
                            <div class="text-sm text-gray-600 mb-2">Implementações que geram receita (ajustada por confiança)</div>
                            <div class="text-xs text-gray-500">Fórmula: Taxa_bruta × Fator_confiança + Taxa_global × (1 - Fator_confiança)</div>
                        </div>

                        <div class="p-4 bg-blue-50 rounded-lg border border-blue-100">
                            <div class="font-semibold text-blue-800 mb-1">Volume de Negócios (15%)</div>
                            <div class="text-sm text-gray-600 mb-2">Fator de confiabilidade baseado no volume</div>
                            <div class="text-xs text-gray-500">Fórmula: min(log₁₀(oportunidades + implementações) × 20, 100)</div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Interpretation & Usage -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                            </svg>
                        </div>
                        Interpretação e Uso
                    </h4>

                    <!-- Performance Levels -->
                    <div class="mb-6">
                        <h5 class="font-medium text-gray-700 mb-3">Níveis de Performance</h5>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                <span class="font-medium text-blue-800">Excelente (≥ 70)</span>
                                <span class="text-sm text-gray-600">Benchmark de mercado</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <span class="font-medium text-gray-800">Bom (50-69)</span>
                                <span class="text-sm text-gray-600">Acima da média</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-100 rounded-lg">
                                <span class="font-medium text-gray-700">Médio (30-49)</span>
                                <span class="text-sm text-gray-600">Oportunidades de melhoria</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-200 rounded-lg">
                                <span class="font-medium text-gray-800">Baixo (< 30)</span>
                                <span class="text-sm text-gray-600">Requer atenção imediata</span>
                            </div>
                        </div>
                    </div>

                    <!-- Econometric Adjustment -->
                    <div class="mb-6">
                        <h5 class="font-medium text-gray-700 mb-3">Ajuste Econométrico</h5>
                        <div class="bg-blue-50 rounded-lg p-4 border border-blue-100">
                            <div class="text-sm text-gray-700 space-y-2">
                                <div><strong>Fator de Confiança:</strong> n / (n + 1.96²)</div>
                                <div><strong>Shrinkage Bayesiano:</strong> Reduz viés de amostras pequenas</div>
                                <div><strong>Volume Score:</strong> Recompensa entidades com volume substancial</div>
                                <div class="text-xs text-blue-600 mt-2">Evita taxas irreais como 100% de conversão com apenas 1 cliente</div>
                            </div>
                        </div>
                    </div>

                    <!-- Applications -->
                    <div>
                        <h5 class="font-medium text-gray-700 mb-3">Aplicações Práticas</h5>
                        <div class="space-y-3 text-sm text-gray-600">
                            <div class="flex items-start">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                <div><strong>Benchmarking:</strong> Comparar performance entre turmas, estados e responsáveis</div>
                            </div>
                            <div class="flex items-start">
                                <div class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                <div><strong>Identificação de Outliers:</strong> Detectar padrões excepcionais de performance</div>
                            </div>
                            <div class="flex items-start">
                                <div class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                <div><strong>Alocação de Recursos:</strong> Priorizar investimentos em áreas de maior potencial</div>
                            </div>
                            <div class="flex items-start">
                                <div class="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                <div><strong>Monitoramento:</strong> Acompanhar evolução temporal da performance</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistical Foundation -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <h4 class="text-lg font-semibold text-gray-800 mb-4">Fundamento Estatístico</h4>
                <div class="bg-gray-50 rounded-lg p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                        <div>
                            <div class="font-medium text-gray-700 mb-2">Normalização</div>
                            <div class="text-gray-600">Todos os componentes são normalizados para escala 0-100 para garantir comparabilidade</div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-700 mb-2">Ponderação</div>
                            <div class="text-gray-600">Pesos baseados em impacto econométrico e relevância para performance comercial</div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-700 mb-2">Robustez</div>
                            <div class="text-gray-600">Índice resistente a outliers através de transformações logarítmicas e caps</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Intelligence Index page loaded');

    // Initialize charts
    initializeCharts();

    // Dimension buttons functionality
    const dimensionButtons = document.querySelectorAll('.dimension-btn');
    dimensionButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // Update active button
            dimensionButtons.forEach(b => {
                b.classList.remove('active', 'bg-blue-500', 'text-white');
                b.classList.add('bg-gray-200', 'text-gray-700');
            });
            this.classList.remove('bg-gray-200', 'text-gray-700');
            this.classList.add('active', 'bg-blue-500', 'text-white');

            // Update chart
            const dimension = this.dataset.dimension;
            updateDimensionChart(dimension);
        });
    });

    // Table tab functionality
    const tableTabButtons = document.querySelectorAll('.table-tab-btn');
    const tableContents = document.querySelectorAll('.table-content');

    tableTabButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // Update active tab button
            tableTabButtons.forEach(b => {
                b.classList.remove('active', 'bg-blue-500', 'text-white');
                b.classList.add('text-gray-600');
            });
            this.classList.remove('text-gray-600');
            this.classList.add('active', 'bg-blue-500', 'text-white');

            // Show corresponding table content
            const targetTable = this.dataset.table;
            tableContents.forEach(content => {
                content.classList.add('hidden');
            });

            const targetContent = document.getElementById(targetTable + '-table');
            if (targetContent) {
                targetContent.classList.remove('hidden');
            }
        });
    });
});

function initializeCharts() {
    // Normal Distribution Chart with statistical measures
    const distributionCtx = document.getElementById('indexDistributionChart');
    if (distributionCtx) {
        // Calculate distribution from real data
        const turmaData = [
            {% if turma_analysis and turma_analysis.results %}
                {% for turma in turma_analysis.results %}
                    {{ turma.composite_index }},
                {% endfor %}
            {% endif %}
        ];

        const estadoData = [
            {% if estado_analysis and estado_analysis.results %}
                {% for estado in estado_analysis.results %}
                    {{ estado.composite_index }},
                {% endfor %}
            {% endif %}
        ];

        const respData = [
            {% if responsavel_analysis and responsavel_analysis.results %}
                {% for resp in responsavel_analysis.results %}
                    {{ resp.composite_index }},
                {% endfor %}
            {% endif %}
        ];

        // Combine all data
        const allData = [...turmaData, ...estadoData, ...respData].filter(x => !isNaN(x));

        if (allData.length > 0) {
            // Calculate statistical measures
            const mean = allData.reduce((a, b) => a + b, 0) / allData.length;
            const sortedData = [...allData].sort((a, b) => a - b);
            const median = sortedData.length % 2 === 0
                ? (sortedData[sortedData.length / 2 - 1] + sortedData[sortedData.length / 2]) / 2
                : sortedData[Math.floor(sortedData.length / 2)];

            const variance = allData.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / allData.length;
            const stdDev = Math.sqrt(variance);

            // Create histogram bins
            const binWidth = 5;
            const minVal = Math.min(...allData);
            const maxVal = Math.max(...allData);
            const numBins = Math.ceil((maxVal - minVal) / binWidth);

            const bins = [];
            const distribution = [];
            const labels = [];

            for (let i = 0; i <= numBins; i++) {
                const binStart = minVal + i * binWidth;
                const binEnd = binStart + binWidth;
                bins.push(binStart);
                labels.push(`${binStart.toFixed(0)}-${binEnd.toFixed(0)}`);
                distribution.push(0);
            }

            // Fill distribution
            allData.forEach(value => {
                const binIndex = Math.min(Math.floor((value - minVal) / binWidth), numBins - 1);
                if (binIndex >= 0 && binIndex < distribution.length) {
                    distribution[binIndex]++;
                }
            });

            // Generate normal distribution curve
            const normalCurve = [];
            const curveLabels = [];
            for (let x = minVal; x <= maxVal; x += 1) {
                const y = (1 / (stdDev * Math.sqrt(2 * Math.PI))) *
                         Math.exp(-0.5 * Math.pow((x - mean) / stdDev, 2));
                normalCurve.push(y * allData.length * binWidth); // Scale to match histogram
                curveLabels.push(x);
            }

            new Chart(distributionCtx, {
                type: 'bar',
                data: {
                    labels: labels.slice(0, -1), // Remove last empty bin
                    datasets: [{
                        label: 'Frequência Observada',
                        data: distribution.slice(0, -1),
                        backgroundColor: 'rgba(59, 130, 246, 0.6)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1,
                        type: 'bar'
                    }, {
                        label: 'Distribuição Normal Teórica',
                        data: curveLabels.map(x => {
                            const y = (1 / (stdDev * Math.sqrt(2 * Math.PI))) *
                                     Math.exp(-0.5 * Math.pow((x - mean) / stdDev, 2));
                            return y * allData.length * binWidth;
                        }),
                        borderColor: 'rgba(107, 114, 128, 1)',
                        backgroundColor: 'rgba(107, 114, 128, 0.1)',
                        borderWidth: 2,
                        type: 'line',
                        fill: false,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Distribuição Normal do Intelligence Index',
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                afterBody: function(context) {
                                    return [
                                        `Média: ${mean.toFixed(2)}`,
                                        `Mediana: ${median.toFixed(2)}`,
                                        `Desvio Padrão: ${stdDev.toFixed(2)}`,
                                        `Variância: ${variance.toFixed(2)}`
                                    ];
                                }
                            }
                        },
                        annotation: {
                            annotations: {
                                meanLine: {
                                    type: 'line',
                                    mode: 'vertical',
                                    scaleID: 'x',
                                    value: mean,
                                    borderColor: 'rgba(34, 197, 94, 0.8)',
                                    borderWidth: 3,
                                    borderDash: [5, 5],
                                    label: {
                                        content: `Média: ${mean.toFixed(1)}`,
                                        enabled: true,
                                        position: 'top',
                                        backgroundColor: 'rgba(34, 197, 94, 0.8)',
                                        color: 'white',
                                        font: { size: 12, weight: 'bold' }
                                    }
                                },
                                medianLine: {
                                    type: 'line',
                                    mode: 'vertical',
                                    scaleID: 'x',
                                    value: median,
                                    borderColor: 'rgba(239, 68, 68, 0.8)',
                                    borderWidth: 3,
                                    borderDash: [10, 5],
                                    label: {
                                        content: `Mediana: ${median.toFixed(1)}`,
                                        enabled: true,
                                        position: 'top',
                                        backgroundColor: 'rgba(239, 68, 68, 0.8)',
                                        color: 'white',
                                        font: { size: 12, weight: 'bold' }
                                    }
                                },
                                stdDev1Pos: {
                                    type: 'line',
                                    mode: 'vertical',
                                    scaleID: 'x',
                                    value: mean + stdDev,
                                    borderColor: 'rgba(168, 85, 247, 0.6)',
                                    borderWidth: 2,
                                    borderDash: [3, 3],
                                    label: {
                                        content: `+1σ: ${(mean + stdDev).toFixed(1)}`,
                                        enabled: true,
                                        position: 'top',
                                        backgroundColor: 'rgba(168, 85, 247, 0.6)',
                                        color: 'white',
                                        font: { size: 10 }
                                    }
                                },
                                stdDev1Neg: {
                                    type: 'line',
                                    mode: 'vertical',
                                    scaleID: 'x',
                                    value: mean - stdDev,
                                    borderColor: 'rgba(168, 85, 247, 0.6)',
                                    borderWidth: 2,
                                    borderDash: [3, 3],
                                    label: {
                                        content: `-1σ: ${(mean - stdDev).toFixed(1)}`,
                                        enabled: true,
                                        position: 'top',
                                        backgroundColor: 'rgba(168, 85, 247, 0.6)',
                                        color: 'white',
                                        font: { size: 10 }
                                    }
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Frequência / Densidade',
                                font: { weight: 'bold' }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Valores do Intelligence Index',
                                font: { weight: 'bold' }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });

            // Add statistical summary below the chart
            const chartContainer = distributionCtx.parentElement;
            let statsDiv = chartContainer.querySelector('.stats-summary');
            if (!statsDiv) {
                statsDiv = document.createElement('div');
                statsDiv.className = 'stats-summary mt-4 p-4 bg-gray-50 rounded-lg border';
                chartContainer.appendChild(statsDiv);
            }

            statsDiv.innerHTML = `
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                    <div>
                        <div class="text-lg font-bold text-green-600">${mean.toFixed(2)}</div>
                        <div class="text-sm text-gray-600">Média (μ)</div>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-red-600">${median.toFixed(2)}</div>
                        <div class="text-sm text-gray-600">Mediana</div>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-purple-600">${stdDev.toFixed(2)}</div>
                        <div class="text-sm text-gray-600">Desvio Padrão (σ)</div>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-blue-600">${allData.length}</div>
                        <div class="text-sm text-gray-600">Amostra (n)</div>
                    </div>
                </div>
                <div class="mt-3 text-xs text-gray-500 text-center">
                    68% dos dados estão entre ${(mean - stdDev).toFixed(1)} e ${(mean + stdDev).toFixed(1)} |
                    95% entre ${(mean - 2*stdDev).toFixed(1)} e ${(mean + 2*stdDev).toFixed(1)}
                </div>
            `;
        }
    }

    // Components Correlation Chart
    const correlationCtx = document.getElementById('componentsCorrelationChart');
    if (correlationCtx) {
        new Chart(correlationCtx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: 'Conversão vs Faturamento',
                    data: generateCorrelationData(20),
                    backgroundColor: 'rgba(59, 130, 246, 0.6)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                }, {
                    label: 'Densidade vs Sucesso',
                    data: generateCorrelationData(20),
                    backgroundColor: 'rgba(107, 114, 128, 0.6)',
                    borderColor: 'rgba(107, 114, 128, 1)',
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Correlação entre Componentes'
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Componente X'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Componente Y'
                        }
                    }
                }
            }
        });
    }

    // Performance Distribution Chart
    const perfDistCtx = document.getElementById('performanceDistributionChart');
    if (perfDistCtx) {
        const excellentCount = {{ intelligence_summary.performance_distribution.excellent or 0 }};
        const goodCount = {{ intelligence_summary.performance_distribution.good or 0 }};
        const averageCount = {{ intelligence_summary.performance_distribution.average or 0 }};
        const poorCount = {{ intelligence_summary.performance_distribution.poor or 0 }};

        new Chart(perfDistCtx, {
            type: 'doughnut',
            data: {
                labels: ['Excelente (≥70)', 'Bom (50-69)', 'Médio (30-49)', 'Baixo (<30)'],
                datasets: [{
                    data: [excellentCount, goodCount, averageCount, poorCount],
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(107, 114, 128, 0.8)',
                        'rgba(156, 163, 175, 0.8)',
                        'rgba(75, 85, 99, 0.8)'
                    ],
                    borderColor: [
                        'rgba(59, 130, 246, 1)',
                        'rgba(107, 114, 128, 1)',
                        'rgba(156, 163, 175, 1)',
                        'rgba(75, 85, 99, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Initialize dimension trends chart
    updateDimensionChart('turma');
}

function generateCorrelationData(count) {
    const data = [];
    for (let i = 0; i < count; i++) {
        data.push({
            x: Math.random() * 100,
            y: Math.random() * 100
        });
    }
    return data;
}

function updateDimensionChart(dimension) {
    const ctx = document.getElementById('dimensionTrendsChart');
    if (!ctx) return;

    // Destroy existing chart if it exists
    if (window.dimensionChart) {
        window.dimensionChart.destroy();
    }

    // Real data from server
    const realData = {
        turma: {
            labels: [
                {% if turma_analysis and turma_analysis.results %}
                    {% for turma in turma_analysis.results[:10] %}
                        '{{ turma.dimension_value }}',
                    {% endfor %}
                {% endif %}
            ],
            data: [
                {% if turma_analysis and turma_analysis.results %}
                    {% for turma in turma_analysis.results[:10] %}
                        {{ turma.composite_index }},
                    {% endfor %}
                {% endif %}
            ]
        },
        estado: {
            labels: [
                {% if estado_analysis and estado_analysis.results %}
                    {% for estado in estado_analysis.results[:10] %}
                        '{{ estado.dimension_value }}',
                    {% endfor %}
                {% endif %}
            ],
            data: [
                {% if estado_analysis and estado_analysis.results %}
                    {% for estado in estado_analysis.results[:10] %}
                        {{ estado.composite_index }},
                    {% endfor %}
                {% endif %}
            ]
        },
        responsavel: {
            labels: [
                {% if responsavel_analysis and responsavel_analysis.results %}
                    {% for resp in responsavel_analysis.results[:10] %}
                        '{{ resp.dimension_value }}',
                    {% endfor %}
                {% endif %}
            ],
            data: [
                {% if responsavel_analysis and responsavel_analysis.results %}
                    {% for resp in responsavel_analysis.results[:10] %}
                        {{ resp.composite_index }},
                    {% endfor %}
                {% endif %}
            ]
        }
    };

    const chartData = realData[dimension] || realData.turma;

    window.dimensionChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: 'Índice de Performance',
                data: chartData.data,
                backgroundColor: 'rgba(59, 130, 246, 0.6)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: `Performance por ${dimension.charAt(0).toUpperCase() + dimension.slice(1)}`
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Índice de Performance'
                    }
                }
            }
        }
    });
}
</script>
{% endblock %}
