{% extends 'base.html' %}
{% from 'macros/components.html' import kpi_card, chart, section_header, table_filters, advanced_insight %}

{% block title %}{{ app_name|e }} - Análise de Cupons{% endblock %}

{% block content %}
<div class="p-6 space-y-8">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-6 shadow-sm">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Análise de Cupons</h1>
        <p class="text-gray-600">Análise detalhada dos cupons de isenção, eficiência e distribuição por universidade e responsável.</p>

        <div class="mt-4 bg-white p-4 rounded-lg border border-blue-100">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Regras de Negócio dos Cupons</h3>
            <ul class="list-disc pl-5 space-y-1 text-sm text-gray-600">
                <li><span class="font-medium">Período de Isenção:</span> O tempo de duração do cupom é representado na coluna "Meses de Isenção". Durante este período, o usuário não paga mensalidade.</li>
                <li><span class="font-medium">Pagamento de Entrada:</span> Valor que o usuário precisa pagar para ativar o cupom.</li>
                <li><span class="font-medium">Data de Início:</span> O cupom começa a ser contado a partir da data de colação, que é quando o médico está habilitado a trabalhar.</li>
                <li><span class="font-medium">Início do Faturamento:</span> A data de início do faturamento é igual à data de colação + o número de meses do cupom.</li>
                <li><span class="font-medium">Valor da Isenção:</span> Calculado como o valor da mensalidade × o número de meses de isenção.</li>
                <li><span class="font-medium">Status do Cupom:</span>
                    <ul class="list-disc pl-5 mt-1">
                        <li><span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Pendente</span> - A data de colação ainda não chegou</li>
                        <li><span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Ativo</span> - Período de isenção em andamento (após colação, antes do início do faturamento)</li>
                        <li><span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Expirado</span> - Período de isenção já terminou (após início do faturamento)</li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>

    <!-- KPI Cards Section -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Total Coupons -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-blue-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-1">Total de Cupons</p>
                    <p class="text-3xl font-bold text-gray-900">{{ total_coupons|e }}</p>
                </div>
            </div>
        </div>

        <!-- Average Exemption Months -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-yellow-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-1">Média de Meses de Isenção</p>
                    <p class="text-3xl font-bold text-gray-900">{{ avg_exemption_months|e }}</p>
                </div>
            </div>
        </div>

        <!-- Total Exemption Value -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-purple-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-1">Valor Total de Isenção</p>
                    <p class="text-3xl font-bold text-gray-900">{{ total_exemption_value|e }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial KPI Cards Section -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
        <!-- Total Entry Payment -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-green-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-1">Total de Pagamentos de Entrada</p>
                    <p class="text-3xl font-bold text-gray-900">{{ total_entry_payment|e }}</p>
                </div>
            </div>
        </div>

        <!-- Average CAC -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-red-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-1">CAC Médio por Cupom</p>
                    <p class="text-3xl font-bold text-gray-900">{{ avg_cac|e }}</p>
                    <p class="text-xs text-gray-500 mt-1">Custo de Aquisição de Cliente</p>
                </div>
            </div>
        </div>

        <!-- Average Payback Period -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-indigo-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-1">Período Médio de Payback</p>
                    <p class="text-3xl font-bold text-gray-900">{{ avg_payback_months|e }} meses</p>
                    <p class="text-xs text-gray-500 mt-1">Tempo para recuperar o investimento</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Coupon Status Chart -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Status dos Cupons</h3>
            <div class="h-64 w-full">
                {{ chart(
                    id="coupon-status-chart",
                    type="pie",
                    data=coupon_status_data,
                    colors=["#F59E0B", "#10B981", "#EF4444"],
                    height="240px"
                ) }}
            </div>
        </div>

        <!-- Monthly Trend Chart -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Tendência Mensal de Cupons</h3>
            <div class="h-64 w-full">
                {{ chart(
                    id="monthly-trend-chart",
                    type="line",
                    data=monthly_trend_data,
                    colors=["#3B82F6"],
                    height="240px"
                ) }}
            </div>
        </div>
    </div>

    <!-- Yearly Trend Chart -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Tendência Anual de Cupons</h3>
        <div class="h-64 w-full">
            {{ chart(
                id="yearly-trend-chart",
                type="bar",
                data=yearly_trend_data,
                colors=["#8B5CF6"],
                height="240px"
            ) }}
        </div>
    </div>

    <!-- Cash Flow Analysis Section -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Análise de Fluxo de Caixa</h3>
        <p class="text-gray-600 mb-6">Projeção de fluxo de caixa considerando pagamentos de entrada, período de isenção e mensalidades.</p>

        <div class="h-80 w-full relative">
            <div id="cashflow-chart-container" class="w-full h-full">
                <canvas id="cashflow-chart" class="w-full h-full"></canvas>
            </div>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    if (window.AmigoDH) {
                        const entryPaymentsData = {{ entry_payments_data|tojson }};
                        const monthlyPaymentsData = {{ monthly_payments_data|tojson }};
                        const exemptionValueData = {{ exemption_value_data|tojson }};
                        const netCashflowData = {{ net_cashflow_data|tojson }};

                        const labels = entryPaymentsData.map(item => item.name);

                        const datasets = [
                            {
                                label: 'Pagamentos de Entrada',
                                data: entryPaymentsData.map(item => item.value),
                                borderColor: '#10B981',
                                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                borderWidth: 2,
                                fill: true
                            },
                            {
                                label: 'Mensalidades',
                                data: monthlyPaymentsData.map(item => item.value),
                                borderColor: '#3B82F6',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                borderWidth: 2,
                                fill: true
                            },
                            {
                                label: 'Valor de Isenção (Custo)',
                                data: exemptionValueData.map(item => item.value),
                                borderColor: '#EF4444',
                                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                                borderWidth: 2,
                                fill: true
                            },
                            {
                                label: 'Fluxo de Caixa Líquido',
                                data: netCashflowData.map(item => item.value),
                                borderColor: '#8B5CF6',
                                backgroundColor: 'rgba(139, 92, 246, 0.1)',
                                borderWidth: 3,
                                fill: false
                            }
                        ];

                        const ctx = document.getElementById('cashflow-chart').getContext('2d');
                        new Chart(ctx, {
                            type: 'line',
                            data: {
                                labels: labels,
                                datasets: datasets
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                layout: {
                                    padding: {
                                        top: 10,
                                        right: 10,
                                        bottom: 10,
                                        left: 10
                                    }
                                },
                                plugins: {
                                    legend: {
                                        position: 'bottom',
                                        labels: {
                                            usePointStyle: true,
                                            padding: 15,
                                            font: {
                                                size: 11
                                            }
                                        }
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                let label = context.dataset.label || '';
                                                if (label) {
                                                    label += ': ';
                                                }
                                                if (context.parsed.y !== null) {
                                                    return label + new Intl.NumberFormat('pt-BR', {
                                                        style: 'currency',
                                                        currency: 'BRL'
                                                    }).format(context.parsed.y);
                                                }
                                                return label;
                                            }
                                        }
                                    }
                                },
                                scales: {
                                    x: {
                                        grid: {
                                            display: true,
                                            color: 'rgba(0, 0, 0, 0.05)'
                                        },
                                        ticks: {
                                            font: {
                                                size: 10
                                            }
                                        }
                                    },
                                    y: {
                                        grid: {
                                            display: true,
                                            color: 'rgba(0, 0, 0, 0.05)'
                                        },
                                        ticks: {
                                            font: {
                                                size: 10
                                            },
                                            callback: function(value) {
                                                return new Intl.NumberFormat('pt-BR', {
                                                    style: 'currency',
                                                    currency: 'BRL',
                                                    maximumFractionDigits: 0
                                                }).format(value);
                                            }
                                        }
                                    }
                                }
                            }
                        });
                    }
                });
            </script>
        </div>
    </div>

    <!-- Cumulative Cash Flow Chart -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Fluxo de Caixa Acumulado - Impacto dos Cupons</h3>
        <p class="text-gray-600 mb-6">Comparação entre o fluxo de caixa real (com cupons) e teórico (sem cupons), mostrando o impacto financeiro do programa de cupons ao longo do tempo.</p>

        <!-- Legend Explanation -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="flex items-center space-x-2">
                <div class="w-4 h-0.5 bg-red-500"></div>
                <span class="text-sm text-gray-600"><strong>Com Cupons (Real):</strong> Fluxo considerando custos de isenção</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-4 h-0.5 bg-green-500 border-dashed border-t-2 border-green-500"></div>
                <span class="text-sm text-gray-600"><strong>Sem Cupons (Teórico):</strong> Fluxo sem custos de isenção</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-4 h-2 bg-yellow-400 opacity-50"></div>
                <span class="text-sm text-gray-600"><strong>Impacto dos Cupons:</strong> Diferença entre os cenários</span>
            </div>
        </div>

        <div class="h-64 w-full relative">
            <div id="cumulative-cashflow-chart-container" class="w-full h-full">
                <canvas id="cumulative-cashflow-chart" class="w-full h-full"></canvas>
            </div>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    if (window.AmigoDH) {
                        const cumulativeCashflowData = {{ cumulative_cashflow_data|tojson }};

                        const labels = cumulativeCashflowData.map(item => item.name);
                        const withCouponsData = cumulativeCashflowData.map(item => item.with_coupons || item.value);
                        const withoutCouponsData = cumulativeCashflowData.map(item => item.without_coupons || item.value);
                        const couponImpactData = cumulativeCashflowData.map(item => item.coupon_impact || 0);

                        const ctx = document.getElementById('cumulative-cashflow-chart').getContext('2d');
                        new Chart(ctx, {
                            type: 'line',
                            data: {
                                labels: labels,
                                datasets: [{
                                    label: 'Com Cupons (Real)',
                                    data: withCouponsData,
                                    borderColor: '#EF4444',
                                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                                    borderWidth: 2,
                                    fill: false,
                                    tension: 0.4
                                }, {
                                    label: 'Sem Cupons (Teórico)',
                                    data: withoutCouponsData,
                                    borderColor: '#10B981',
                                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                    borderWidth: 2,
                                    fill: false,
                                    tension: 0.4,
                                    borderDash: [5, 5]
                                }, {
                                    label: 'Impacto dos Cupons',
                                    data: couponImpactData,
                                    borderColor: '#F59E0B',
                                    backgroundColor: 'rgba(245, 158, 11, 0.2)',
                                    borderWidth: 1,
                                    fill: true,
                                    tension: 0.4
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                layout: {
                                    padding: {
                                        top: 10,
                                        right: 10,
                                        bottom: 10,
                                        left: 10
                                    }
                                },
                                plugins: {
                                    legend: {
                                        position: 'bottom',
                                        labels: {
                                            usePointStyle: true,
                                            padding: 15,
                                            font: {
                                                size: 11
                                            }
                                        }
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                let label = context.dataset.label || '';
                                                if (label) {
                                                    label += ': ';
                                                }
                                                if (context.parsed.y !== null) {
                                                    return label + new Intl.NumberFormat('pt-BR', {
                                                        style: 'currency',
                                                        currency: 'BRL'
                                                    }).format(context.parsed.y);
                                                }
                                                return label;
                                            }
                                        }
                                    }
                                },
                                scales: {
                                    x: {
                                        grid: {
                                            display: true,
                                            color: 'rgba(0, 0, 0, 0.05)'
                                        },
                                        ticks: {
                                            font: {
                                                size: 10
                                            }
                                        }
                                    },
                                    y: {
                                        grid: {
                                            display: true,
                                            color: 'rgba(0, 0, 0, 0.05)'
                                        },
                                        ticks: {
                                            font: {
                                                size: 10
                                            },
                                            callback: function(value) {
                                                return new Intl.NumberFormat('pt-BR', {
                                                    style: 'currency',
                                                    currency: 'BRL',
                                                    maximumFractionDigits: 0
                                                }).format(value);
                                            }
                                        }
                                    }
                                }
                            }
                        });
                    }
                });
            </script>
        </div>
    </div>

    <!-- CAC and Payback Analysis Section -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Análise de CAC e Payback</h3>
        <p class="text-gray-600 mb-6">Análise do Custo de Aquisição de Cliente (CAC) e período de payback dos cupons.</p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
                <h4 class="text-base font-medium text-gray-800 mb-3">Entendendo o CAC</h4>
                <p class="text-sm text-gray-600 mb-4">O Custo de Aquisição de Cliente (CAC) é calculado como o valor total de isenção menos os pagamentos de entrada. Representa o custo efetivo para adquirir um novo cliente através do programa de cupons.</p>

                <div class="bg-blue-50 p-4 rounded-lg">
                    <h5 class="text-sm font-medium text-blue-800 mb-2">Fórmula de CAC</h5>
                    <p class="text-sm text-blue-700">CAC = (Valor Total de Isenção - Total de Pagamentos de Entrada) / Número de Cupons</p>
                </div>
            </div>

            <div>
                <h4 class="text-base font-medium text-gray-800 mb-3">Entendendo o Payback</h4>
                <p class="text-sm text-gray-600 mb-4">O período de payback representa o tempo necessário para recuperar o investimento feito na forma de isenção. É calculado dividindo o valor líquido de isenção (após descontar o pagamento de entrada) pelo valor mensal da assinatura.</p>

                <div class="bg-green-50 p-4 rounded-lg">
                    <h5 class="text-sm font-medium text-green-800 mb-2">Fórmula de Payback</h5>
                    <p class="text-sm text-green-700">Payback (meses) = (Valor de Isenção - Pagamento de Entrada) / Valor Mensal</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Análise de Conversão com Pagamento de Entrada -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Análise de Conversão com Pagamento de Entrada</h3>
        <p class="text-gray-600 mb-6">Comparação da taxa de conversão entre implementações com e sem pagamento de entrada.</p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
                <h4 class="text-base font-medium text-gray-800 mb-3">Impacto do Pagamento de Entrada</h4>
                <p class="text-sm text-gray-600 mb-4">A análise mostra que implementações com pagamento de entrada têm uma taxa de conversão significativamente maior do que aquelas sem pagamento inicial.</p>

                <div class="bg-blue-50 p-4 rounded-lg">
                    <h5 class="text-sm font-medium text-blue-800 mb-2">Diferença na Taxa de Conversão</h5>
                    <p class="text-sm text-blue-700">Aumento de {{ diferenca_taxa_conversao|e }}% na taxa de conversão quando há pagamento de entrada</p>
                </div>
            </div>

            <div>
                <div class="bg-white p-4 rounded-lg border border-gray-200">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-green-50 p-3 rounded-lg">
                            <h5 class="text-sm font-medium text-green-800 mb-1">Com Pagamento de Entrada</h5>
                            <p class="text-2xl font-bold text-green-700">{{ taxa_conversao_com_pagamento|e }}%</p>
                            <p class="text-xs text-green-600 mt-1">{{ finalizados_com_pagamento|e }} finalizados de {{ total_com_pagamento|e }} total</p>
                        </div>
                        <div class="bg-red-50 p-3 rounded-lg">
                            <h5 class="text-sm font-medium text-red-800 mb-1">Sem Pagamento de Entrada</h5>
                            <p class="text-2xl font-bold text-red-700">{{ taxa_conversao_sem_pagamento|e }}%</p>
                            <p class="text-xs text-red-600 mt-1">{{ finalizados_sem_pagamento|e }} finalizados de {{ total_sem_pagamento|e }} total</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráfico de Barras para Comparação -->
        <div class="mt-6 h-64 w-full relative">
            <canvas id="conversion-comparison-chart" class="w-full h-full"></canvas>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const ctx = document.getElementById('conversion-comparison-chart').getContext('2d');
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['Com Pagamento de Entrada', 'Sem Pagamento de Entrada'],
                            datasets: [{
                                label: 'Taxa de Conversão (%)',
                                data: [{{ taxa_conversao_com_pagamento|e }}, {{ taxa_conversao_sem_pagamento|e }}],
                                backgroundColor: [
                                    'rgba(16, 185, 129, 0.7)',
                                    'rgba(239, 68, 68, 0.7)'
                                ],
                                borderColor: [
                                    'rgb(16, 185, 129)',
                                    'rgb(239, 68, 68)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            layout: {
                                padding: {
                                    top: 10,
                                    right: 10,
                                    bottom: 10,
                                    left: 10
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return context.dataset.label + ': ' + context.parsed.y.toFixed(1) + '%';
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: 'Taxa de Conversão (%)'
                                    },
                                    ticks: {
                                        callback: function(value) {
                                            return value + '%';
                                        }
                                    }
                                }
                            }
                        }
                    });
                });
            </script>
        </div>
    </div>

    <!-- Insights Avançados -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-4">Insights e Recomendações</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            {{ advanced_insight(
                title="Impacto do Pagamento de Entrada",
                content="Implementações com pagamento de entrada têm uma taxa de conversão <strong>" + diferenca_taxa_conversao|string + "%</strong> maior do que aquelas sem pagamento inicial. Considere incentivar o pagamento de entrada em novos cupons.",
                icon_type="trend",
                color="blue",
                metrics=[
                    {"label": "Com Pagamento", "value": taxa_conversao_com_pagamento|string + "%", "trend": "+100%", "trend_positive": true},
                    {"label": "Sem Pagamento", "value": taxa_conversao_sem_pagamento|string + "%"}
                ]
            ) }}

            {{ advanced_insight(
                title="Período Ideal de Isenção",
                content="Cupons com 6 meses de isenção apresentam o melhor equilíbrio entre conversão e retorno financeiro. Períodos mais longos não aumentam significativamente a conversão.",
                icon_type="money",
                metrics=[
                    {"label": "Período Ideal", "value": "6 meses"},
                    {"label": "Payback Médio", "value": avg_payback_months|string + " meses"}
                ]
            ) }}

            {{ advanced_insight(
                title="Otimização de Pagamento de Entrada",
                content="O valor ideal de pagamento de entrada está entre R$ 500 e R$ 1.000. Valores mais altos reduzem a taxa de conversão sem benefícios proporcionais no CAC.",
                icon_type="info",
                color="purple",
                metrics=[
                    {"label": "Valor Ideal", "value": "R$ 500-1.000"},
                    {"label": "CAC Médio", "value": avg_cac}
                ]
            ) }}
        </div>
    </div>

    <!-- Distribution Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Exemption Months Distribution -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Distribuição por Meses de Isenção</h3>
            <div class="h-64 w-full">
                {{ chart(
                    id="exemption-distribution-chart",
                    type="bar",
                    data=exemption_distribution,
                    colors=["#8B5CF6"],
                    height="240px"
                ) }}
            </div>
        </div>

        <!-- Top Universities -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Universidades por Cupons</h3>
            <div class="h-64 w-full">
                {{ chart(
                    id="university-coupons-chart",
                    type="bar",
                    data=top_university_coupons,
                    colors=["#3B82F6"],
                    horizontal=true,
                    height="240px"
                ) }}
            </div>
        </div>

        <!-- Top Responsibles -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Responsáveis por Cupons</h3>
            <div class="h-64 w-full">
                {{ chart(
                    id="responsible-coupons-chart",
                    type="bar",
                    data=top_responsible_coupons,
                    colors=["#10B981"],
                    horizontal=true,
                    height="240px"
                ) }}
            </div>
        </div>
    </div>

    <!-- Coupons Table Section -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Lista de Cupons</h3>
        </div>

        <!-- Table Filters -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            {{ table_filters(
                id="coupons-table-filters",
                filters=filters_json
            ) }}
        </div>

        <div class="overflow-x-auto">
            <div class="overflow-y-auto max-h-[500px]">
                <table class="min-w-full divide-y divide-gray-200" id="coupons-table">
                    <thead class="bg-gray-100 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">ID do Cupom</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Descrição</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Meses de Isenção</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Pagamento de Entrada</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Data de Colação</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Início do Faturamento</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Universidade</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Responsável</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Valor Mensal</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Valor de Isenção</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for coupon in coupons %}
                        <tr class="hover:bg-gray-50 transition-colors duration-150"
                            data-status="{{ coupon.status|lower }}"
                            data-university="{{ coupon.university|lower|replace(' ', '-') }}"
                            data-responsible="{{ coupon.responsible|lower|replace(' ', '-') }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ coupon.id|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ coupon.description|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ coupon.exemption_months|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ coupon.entry_payment|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ coupon.graduation_date|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ coupon.billing_start_date|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if coupon.status == 'Ativo' %}
                                        bg-green-100 text-green-800
                                    {% elif coupon.status == 'Pendente' %}
                                        bg-yellow-100 text-yellow-800
                                    {% else %}
                                        bg-red-100 text-red-800
                                    {% endif %}">
                                    {{ coupon.status|e }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ coupon.university|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ coupon.responsible|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ coupon.monthly_value|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ coupon.exemption_value|e }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Table filtering
        const statusFilter = document.getElementById('status-filter');
        const universityFilter = document.getElementById('university-filter');
        const responsibleFilter = document.getElementById('responsible-filter');
        const table = document.getElementById('coupons-table');

        if (table) {
            const rows = table.querySelectorAll('tbody tr');

            function filterTable() {
                const statusValue = statusFilter ? statusFilter.value : 'all';
                const universityValue = universityFilter ? universityFilter.value : 'all';
                const responsibleValue = responsibleFilter ? responsibleFilter.value : 'all';

                rows.forEach(row => {
                    const statusMatch = statusValue === 'all' ||
                                      (statusValue === 'pending' && row.dataset.status === 'pendente') ||
                                      (statusValue === 'active' && row.dataset.status === 'ativo') ||
                                      (statusValue === 'expired' && row.dataset.status === 'concluído');

                    const universityMatch = universityValue === 'all' || row.dataset.university === universityValue;

                    const responsibleMatch = responsibleValue === 'all' || row.dataset.responsible === responsibleValue;

                    if (statusMatch && universityMatch && responsibleMatch) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }

            if (statusFilter) statusFilter.addEventListener('change', filterTable);
            if (universityFilter) universityFilter.addEventListener('change', filterTable);
            if (responsibleFilter) responsibleFilter.addEventListener('change', filterTable);
        }

        // Initialize charts with better options
        if (window.AmigoDH) {
            // Add better tooltips and legends to charts
            const chartOptions = {
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    // Format currency values
                                    if (context.chart.canvas.id === 'cashflow-chart' ||
                                        context.chart.canvas.id === 'cumulative-cashflow-chart') {
                                        return label + new Intl.NumberFormat('pt-BR', {
                                            style: 'currency',
                                            currency: 'BRL'
                                        }).format(context.parsed.y);
                                    } else {
                                        return label + context.parsed.y;
                                    }
                                }
                                return label;
                            }
                        }
                    }
                }
            };

            // Apply options to all charts (except custom ones)
            document.querySelectorAll('[id$="-chart"]').forEach(chart => {
                // Skip our custom charts
                if (chart.id === 'cashflow-chart' || chart.id === 'cumulative-cashflow-chart') {
                    return;
                }

                if (chart._chart) {
                    chart._chart.options = {...chart._chart.options, ...chartOptions};
                    chart._chart.update();
                }
            });
        }
    });
</script>
{% endblock %}
