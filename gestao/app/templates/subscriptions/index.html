{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section %}

{% block title %}Assinaturas (MRR) - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Análise de Assinaturas (MRR)",
    subtitle="Acompanhe a receita mensal recorrente, visualize a distribuição por responsável, produto e universidade.",
    stats=[
        {
            "label": "MRR Total",
            "value": mrr_total,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />'
        },
        {
            "label": "Implantações Finalizadas",
            "value": finalized_count,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />'
        }
    ],
    bg_class="bg-gray-100"
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- MRR Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- KPI: MRR Total -->
        {{ kpi_card(
            title="MRR Total",
            value=mrr_total,
            subtitle="Receita mensal recorrente",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="primary"
        )|e }}

        <!-- KPI: Implantações Finalizadas -->
        {{ kpi_card(
            title="Implantações Finalizadas",
            value=finalized_count,
            subtitle="Gerando receita recorrente",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />',
            color="success"
        )|e }}

        <!-- KPI: Ticket Médio -->
        {{ kpi_card(
            title="Ticket Médio",
            value=ticket_medio,
            subtitle="Por implantação finalizada",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="primary"
        )|e }}

        <!-- KPI: Previsão de MRR (12 meses) -->
        {{ kpi_card(
            title="Previsão de MRR (12 meses)",
            value=mrr_forecast['12']|default(mrr_total),
            subtitle="Crescimento projetado",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />',
            color="success",
            percentage=79.5,
            percentage_label="de crescimento",
            is_positive=true
        ) }}
    </div>

    <!-- MRR Forecast Chart -->
    <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Previsão de MRR</h2>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="h-80">
                <canvas id="mrrForecastChart"></canvas>
            </div>
        </div>
    </div>

    <!-- MRR Distribution -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- MRR by Responsible Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <h3 class="text-base font-medium text-gray-900 mb-1">MRR por Responsável</h3>
            <p class="text-sm text-gray-500 mb-4">Distribuição da receita recorrente por responsável</p>
            <div class="h-64">
                <canvas id="mrrByResponsibleChart"></canvas>
            </div>
        </div>

        <!-- MRR by Product Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <h3 class="text-base font-medium text-gray-900 mb-1">MRR por Produto</h3>
            <p class="text-sm text-gray-500 mb-4">Distribuição da receita recorrente por produto</p>
            <div class="h-64">
                <canvas id="mrrByProductChart"></canvas>
            </div>
        </div>
    </div>

    <!-- MRR by University -->
    <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">MRR por Universidade</h2>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-100">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Universidade</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">MRR</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">% do Total</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for university, mrr in mrr_by_university.items() %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ university|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ mrr|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% set percentage = (mrr|replace('R$ ', '')|replace('.', '')|replace(',', '.')|float / mrr_total|replace('R$ ', '')|replace('.', '')|replace(',', '.')|float * 100)|round(1) %}
                                {{ percentage|e }}%
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded for subscriptions page');

        // Parse chart data
        let chartData;
        try {
            chartData = JSON.parse('{{ chart_data|e }}');
            console.log('Chart data parsed successfully:', chartData);
        } catch (error) {
            console.error('Error parsing chart data:', error);
            chartData = {};
        }

        // Create MRR forecast chart
        try {
            const mrrForecastData = chartData.mrr_forecast || {};
            const forecastLabels = Object.keys(mrrForecastData).map(month => 'Mês ' + month);
            const forecastValues = Object.values(mrrForecastData).map(v => parseFloat(v));

            AmigoDH.createLineChart('mrrForecastChart', forecastLabels, forecastValues, 'MRR Projetado (R$)', {
                backgroundColor: 'rgba(152, 207, 255, 0.2)',
                borderColor: AmigoDH.colors.primary,
                fill: true,
                tooltipCallback: function(value) {
                    return 'MRR: ' + AmigoDH.formatCurrency(value);
                },
                yAxisCallback: function(value) {
                    return AmigoDH.formatCurrency(value);
                }
            });
            console.log('MRR forecast chart created successfully');
        } catch (error) {
            console.error('Error creating MRR forecast chart:', error);
            window.debugChart('mrrForecastChart', {}, error);
        }

        // Create MRR by responsible chart
        try {
            const mrrByResponsibleData = chartData.mrr_by_responsible || {};
            const responsibleLabels = Object.keys(mrrByResponsibleData);
            const responsibleValues = Object.values(mrrByResponsibleData).map(v =>
                parseFloat(v.replace(/[^0-9,]/g, '').replace(',', '.'))
            );

            AmigoDH.createPieChart('mrrByResponsibleChart', responsibleLabels, responsibleValues, {
                backgroundColor: AmigoDH.colors.blue,
                tooltipCallback: function(value, label) {
                    return label + ': ' + AmigoDH.formatCurrency(value);
                }
            });
            console.log('MRR by responsible chart created successfully');
        } catch (error) {
            console.error('Error creating MRR by responsible chart:', error);
            window.debugChart('mrrByResponsibleChart', {}, error);
        }

        // Create MRR by product chart
        try {
            const mrrByProductData = chartData.mrr_by_product || {};
            const productLabels = Object.keys(mrrByProductData);
            const productValues = Object.values(mrrByProductData).map(v =>
                parseFloat(v.replace(/[^0-9,]/g, '').replace(',', '.'))
            );

            AmigoDH.createDoughnutChart('mrrByProductChart', productLabels, productValues, {
                backgroundColor: AmigoDH.colors.blue,
                tooltipCallback: function(value, label) {
                    return label + ': ' + AmigoDH.formatCurrency(value);
                }
            });
            console.log('MRR by product chart created successfully');
        } catch (error) {
            console.error('Error creating MRR by product chart:', error);
            window.debugChart('mrrByProductChart', {}, error);
        }
    });
</script>
{% endblock %}
