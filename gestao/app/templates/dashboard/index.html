{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, advanced_insight, business_tooltip %}
{% from "components/error_message.html" import chart_error %}

{% block title %}{{ app_name|e }} - Visão Geral{% endblock %}

{% block content %}
<!-- Hero Section with Blue Gradient and Geometric Elements -->
<div class="bg-gradient-to-r from-blue-50 to-blue-100 text-gray-800 relative overflow-hidden">
    <!-- Geometric Elements -->
    <div class="absolute inset-0 z-0">
        <!-- Circle -->
        <div class="absolute top-20 right-20 w-64 h-64 rounded-full bg-blue-200 opacity-20"></div>
        <!-- Triangle -->
        <div class="absolute bottom-10 left-10 w-0 h-0 border-l-[100px] border-l-transparent border-b-[150px] border-b-blue-300 border-r-[100px] border-r-transparent opacity-10"></div>
        <!-- Square -->
        <div class="absolute top-40 left-1/4 w-32 h-32 bg-blue-400 opacity-10 rotate-45"></div>
    </div>

    <div class="w-full px-4 sm:px-6 lg:px-8 py-16 relative z-10">
        <div class="flex flex-col md:flex-row items-center relative z-10">
            <div class="md:w-full">
                <h1 class="text-4xl font-bold text-gray-800 mb-3">
                    <span class="flex items-center">
                        Olá, Bruno Abreu
                        <span class="animate-wave inline-block ml-2">👋</span>
                    </span>
                </h1>
                <div class="h-1 w-24 bg-blue-300 rounded-full mb-4"></div>
                <h2 class="text-xl text-gray-600 mb-6 font-medium">Bem vindo ao Datahub do Amigo One - Negócios</h2>
                <p class="text-lg text-gray-700 mb-8 max-w-2xl leading-relaxed">Acompanhe métricas, análises e forecasts do seu negócio em um único lugar. Visualize dados em tempo real e tome decisões baseadas em informações precisas.</p>
                <div class="flex flex-wrap gap-4">
                    <a href="/universities/" class="bg-primary hover:bg-blue-700 text-white px-6 py-3 rounded-lg shadow-md transition duration-150 ease-in-out flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 14l9-5-9-5-9 5 9 5z" />
                            <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998a12.078 12.078 0 01.665-6.479L12 14z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                        </svg>
                        Universidades
                    </a>
                    <a href="/conversion/" class="bg-white border border-gray-300 hover:bg-gray-50 text-gray-800 px-6 py-3 rounded-lg shadow-md transition duration-150 ease-in-out flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        Análise de Conversão
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add animation for the waving hand emoji -->
<style>
    @keyframes wave {
        0% { transform: rotate(0deg); }
        10% { transform: rotate(14deg); }
        20% { transform: rotate(-8deg); }
        30% { transform: rotate(14deg); }
        40% { transform: rotate(-4deg); }
        50% { transform: rotate(10deg); }
        60% { transform: rotate(0deg); }
        100% { transform: rotate(0deg); }
    }
    .animate-wave {
        animation: wave 2.5s infinite;
        transform-origin: 70% 70%;
    }
</style>

<!-- Main Content Container -->
    <!-- KPI Cards - Principais Métricas Financeiras -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- KPI: MRR Total -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">MRR Total</p>
                        {{ business_tooltip(
                            title="MRR Total (Monthly Recurring Revenue)",
                            description="Receita mensal recorrente total gerada por todas as implementações finalizadas que estão ativas.",
                            formula="SUM(Valor_Mensalidade) WHERE Status_Implantacao = 'Finalizado' AND Gerou_Nota_Fiscal = 'Sim'",
                            columns="Valor_Mensalidade, Status_Implantacao, Gerou_Nota_Fiscal",
                            data_source="base_dados.csv → Soma dos valores de mensalidade para implementações ativas",
                            calculation_method="Soma de todos os valores de mensalidade das implementações finalizadas que geram notas fiscais"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ mrr_total|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Receita mensal recorrente atual</p>
            <div class="flex items-center mt-3 bg-gray-50 p-2 rounded-lg">
                <span class="text-sm font-medium text-green-600 mr-1">+{{ mrr_growth_rate|string }}%</span>
                <span class="text-sm text-gray-500">crescimento mensal</span>
            </div>
        </div>

        <!-- KPI: ARR Total -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">ARR Total</p>
                        {{ business_tooltip(
                            title="ARR Total (Annual Recurring Revenue)",
                            description="Receita anual recorrente projetada baseada no MRR atual multiplicado por 12 meses.",
                            formula="MRR_Total × 12",
                            columns="Derivado do MRR Total",
                            data_source="Calculado a partir do MRR Total → MRR × 12 meses",
                            calculation_method="Multiplicação simples do MRR atual por 12 para projeção anual"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ arr_total|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Receita anual recorrente</p>
            <div class="mt-4 pt-3 border-t border-gray-100">
                <div class="text-xs text-gray-500">Baseado no MRR atual x 12 meses</div>
            </div>
        </div>

        <!-- KPI: Receita Potencial -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-primary-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Receita Potencial</p>
                        {{ business_tooltip(
                            title="Receita Potencial",
                            description="Receita mensal estimada que será gerada quando todas as implementações em andamento forem finalizadas.",
                            formula="SUM(Valor_Mensalidade) WHERE Status_Implantacao != 'Finalizado'",
                            columns="Valor_Mensalidade, Status_Implantacao",
                            data_source="base_dados.csv → Soma dos valores de mensalidade para implementações não finalizadas",
                            calculation_method="Soma de todos os valores de mensalidade das implementações que ainda não foram finalizadas"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ potential_revenue|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">De implementações em andamento</p>
            <div class="mt-4 pt-3 border-t border-gray-100">
                <div class="text-xs text-gray-500">Valor mensal após finalização</div>
            </div>
        </div>

        <!-- KPI: Lifetime Value -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">LTV Médio</p>
                        {{ business_tooltip(
                            title="LTV Médio (Lifetime Value)",
                            description="Valor médio que um cliente gera ao longo de todo o relacionamento com a empresa, baseado em 24 meses de retenção.",
                            formula="(Valor_Mensalidade × 24) × Taxa_Retenção",
                            columns="Valor_Mensalidade, dados históricos de retenção",
                            data_source="base_dados.csv → Cálculo baseado em valor médio de mensalidade e taxa de retenção histórica",
                            calculation_method="Multiplicação do valor médio de mensalidade por 24 meses, ajustado pela taxa de retenção observada"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ lifetime_value|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Valor do cliente ao longo do tempo</p>
            <div class="mt-4 pt-3 border-t border-gray-100">
                <div class="text-xs text-gray-500">Baseado em 24 meses de retenção</div>
            </div>
        </div>
    </div>

    <!-- Métricas de Conversão e ROI -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
        <!-- Metric: Conversão com Pagamento de Entrada -->
        {{ stat_card(
            title="Impacto do Pagamento de Entrada",
            value=diferenca_taxa_conversao,
            subtitle="Aumento na taxa de conversão",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />',
            color="success",
            trend=("Com pagamento: " + taxa_conversao_com_pagamento)|e,
            trend_value=("Sem pagamento: " + taxa_conversao_sem_pagamento)|e,
            is_positive=true
        ) }}

        <!-- Metric: Tempo Médio de Conversão -->
        {{ stat_card(
            title="Tempo de Conversão",
            value=(avg_lead_to_impl_days|string + " dias")|e,
            subtitle="Lead → Implantação finalizada",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="primary",
            trend="Tempo médio do ciclo de vendas",
            trend_value="Otimizar"
        ) }}

        <!-- Metric: Impacto da Conversão -->
        {{ stat_card(
            title="Impacto +10% Conversão",
            value=conversion_impact,
            subtitle="MRR adicional potencial",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />',
            color="success",
            trend="Aumento de 10% na taxa",
            trend_value="Positivo",
            is_positive=true
        ) }}

        <!-- Metric: Ticket Médio -->
        {{ stat_card(
            title="Ticket Médio",
            value=ticket_medio,
            subtitle="Por implantação finalizada",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="primary",
            trend="Valor médio mensal",
            trend_value="Estável"
        ) }}
    </div>

    <!-- Métricas de Funil e Operacionais -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
        <!-- KPI: Total de Leads -->
        {% set footer_potencial = '<div class="text-xs text-gray-500 mt-2">Valor potencial: ' ~ (total_leads * 500)|currency ~ '</div>' %}
        {{ kpi_card(
            title="Total de Leads",
            value=total_leads,
            subtitle="Potenciais clientes cadastrados",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />',
            color="primary",
            footer=footer_potencial
        ) }}

        <!-- KPI: Oportunidades -->
        {{ kpi_card(
            title="Oportunidades",
            value=total_opportunities,
            subtitle="Negociações em andamento",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="primary",
            footer='<div class="text-xs text-gray-500 mt-2">Taxa de conversão: ' + (lead_to_opp_rate|float|round(1))|string + '%</div>'
        ) }}

        <!-- KPI: Implantações Finalizadas -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Implantações Finalizadas</p>
                        {{ business_tooltip(
                            title="Implantações Finalizadas",
                            description="Total de projetos de implementação que foram concluídos, independente de estarem gerando receita.",
                            formula="COUNT(Status_Implantacao = 'Finalizado')",
                            columns="Status_Implantacao",
                            data_source="base_dados.csv → Filtro: Status_Implantacao = 'Finalizado'",
                            calculation_method="Contagem simples de registros onde o status da implementação está marcado como 'Finalizado'"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ finalized_implementations|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Projetos concluídos</p>
            <div class="mt-4 pt-3 border-t border-gray-100">
                <div class="text-xs text-gray-500">Tempo médio: {{ avg_lead_to_impl_days|string }} dias</div>
            </div>
        </div>

        <!-- Metric: Taxa de Conversão -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-100 card-hover transition-all duration-300 h-full">
            <div class="flex items-center mb-3">
                <div class="w-12 h-12 rounded-full bg-success-50 flex items-center justify-center mr-3">
                    <svg class="w-6 h-6 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Taxa de Conversão</p>
                        {{ business_tooltip(
                            title="Taxa de Conversão Geral",
                            description="Percentual de leads que se tornaram implementações finalizadas, representando a eficiência do funil comercial completo.",
                            formula="(Implementações Finalizadas ÷ Total de Leads) × 100",
                            columns="Status_Implantacao, Lead_id",
                            data_source="base_dados.csv → Contagem de Status_Implantacao = 'Finalizado' dividido pelo total de Lead_id únicos",
                            calculation_method="Divisão do número de implementações finalizadas pelo total de leads únicos, multiplicado por 100"
                        ) }}
                    </div>
                    <p class="text-2xl font-semibold text-gray-900">{{ (conversao_geral|float|round(1))|string + "%" }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mb-2">Lead → Implantação</p>
            <div class="flex items-center text-sm">
                <span class="text-green-600 font-medium mr-1">Impacto financeiro</span>
                <span class="text-gray-500">{{ conversion_impact }}</span>
            </div>
        </div>
    </div>

    <!-- Nova seção: Usuários Ativos -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 mt-6">
        <!-- KPI: Usuários Ativos -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-primary-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Usuários Ativos</p>
                        {{ business_tooltip(
                            title="Usuários Ativos",
                            description="Implementações finalizadas que estão gerando receita recorrente através de notas fiscais.",
                            formula="COUNT(Status_Implantacao = 'Finalizado' AND Gerou_Nota_Fiscal = 'Sim')",
                            columns="Status_Implantacao, Gerou_Nota_Fiscal",
                            data_source="base_dados.csv → Filtro: Status_Implantacao = 'Finalizado' AND Gerou_Nota_Fiscal = 'Sim'",
                            calculation_method="Contagem de registros onde a implementação está finalizada E está gerando notas fiscais"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ active_users|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Gerando receita recorrente</p>
            <div class="mt-4 pt-3 border-t border-gray-100">
                <div class="text-xs text-gray-500">Taxa de ativação: {{ "%.1f"|format((active_users / finalized_implementations * 100) if finalized_implementations > 0 else 0) }}%</div>
            </div>
        </div>

        <!-- KPI: Usuários Alta Atividade -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Usuários Alta Atividade</p>
                        {{ business_tooltip(
                            title="Usuários Alta Atividade",
                            description="Usuários ativos com alta regularidade, definidos como aqueles com 3 ou mais meses de atividade contínua.",
                            formula="COUNT(Gerou_Nota_Fiscal = 'Sim' AND Meses_atividade >= 3)",
                            columns="Gerou_Nota_Fiscal, Meses_atividade",
                            data_source="base_dados.csv → Filtro: Gerou_Nota_Fiscal = 'Sim' AND Meses_atividade >= 3",
                            calculation_method="Contagem de usuários que geram notas fiscais E têm 3 ou mais meses de atividade"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ high_activity_users|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Com regularidade (3+ meses)</p>
            <div class="mt-4 pt-3 border-t border-gray-100">
                <div class="text-xs text-gray-500">Taxa de consistência: {{ "%.1f"|format((high_activity_users / active_users * 100) if active_users > 0 else 0) }}%</div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Funnel Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Funil de Conversão</h3>
                {{ business_tooltip(
                    title="Funil de Conversão",
                    description="Visualização do processo completo de conversão desde leads até clientes ativos, mostrando a quantidade em cada etapa.",
                    formula="Leads → Oportunidades → Implementações → Clientes Ativos",
                    columns="Lead_id, Oportunidade_id, Status_Implantacao, Gerou_Nota_Fiscal",
                    data_source="base_dados.csv → Contagem progressiva através das etapas do funil comercial",
                    calculation_method="Contagem de registros únicos em cada etapa: total de leads, leads com oportunidades, implementações finalizadas e usuários ativos"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Visão geral do processo de conversão</p>
            <div class="h-64">
                <canvas id="funnelChart"></canvas>
            </div>
        </div>

        <!-- Implementation Timeline Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Evolução de Implantações</h3>
                {{ business_tooltip(
                    title="Evolução de Implantações",
                    description="Gráfico temporal mostrando o número de implementações iniciadas por mês, permitindo identificar tendências e sazonalidade.",
                    formula="COUNT(Data_Criacao_Implantacao) GROUP BY MONTH(Data_Criacao_Implantacao)",
                    columns="Data_Criacao_Implantacao",
                    data_source="base_dados.csv → Agrupamento por mês da data de criação das implementações",
                    calculation_method="Contagem de implementações agrupadas por mês/ano da data de criação, ordenadas cronologicamente"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Implantações iniciadas por mês</p>
            <div class="h-64">
                <canvas id="timelineChart"></canvas>
            </div>
        </div>
    </div>

    <!-- MRR by Creation Date Chart -->
    <div class="mt-6">
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Evolução do MRR Cumulativo</h3>
                {{ business_tooltip(
                    title="Evolução do MRR Cumulativo",
                    description="Gráfico mostrando o crescimento cumulativo da receita mensal recorrente ao longo do tempo, baseado na data de criação das implementações finalizadas.",
                    formula="SUM(Valor_Mensalidade) CUMULATIVE WHERE Status_Implantacao = 'Finalizado' ORDER BY Data_Criacao_Implantacao",
                    columns="Valor_Mensalidade, Status_Implantacao, Data_Criacao_Implantacao",
                    data_source="base_dados.csv → Soma cumulativa dos valores de mensalidade por data de criação (a partir de 2024)",
                    calculation_method="Soma cumulativa dos valores de mensalidade das implementações finalizadas, agrupadas por mês de criação e acumuladas ao longo do tempo"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Receita mensal recorrente cumulativa baseada na data de criação para implantações finalizadas (a partir de 2024)</p>
            <div class="h-72">
                <canvas id="mrrCreationChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Advanced Insights Section -->
    <div class="mt-10">
        <h2 class="text-xl font-semibold text-gray-900 mb-2">Insights Avançados</h2>
        <p class="text-sm text-gray-500 mb-6">Análises e recomendações baseadas nos dados mais recentes.</p>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {{ advanced_insight(
                title="Impacto do Pagamento de Entrada",
                content="Implementações com pagamento de entrada têm uma taxa de conversão <strong>" + diferenca_taxa_conversao + "</strong> maior do que aquelas sem pagamento inicial.",
                icon_type="trend",
                color="blue",
                metrics=[
                    {"label": "Com Pagamento", "value": taxa_conversao_com_pagamento, "trend": "+100%", "trend_positive": true},
                    {"label": "Sem Pagamento", "value": taxa_conversao_sem_pagamento}
                ],
                action_text="Ver análise completa",
                action_url="/coupons/"
            ) }}

            {{ advanced_insight(
                title="Oportunidades de Crescimento",
                content="As universidades com maior taxa de conversão são aquelas onde temos parcerias com professores. Considere expandir essas parcerias.",
                icon_type="success",
                color="green",
                metrics=[
                    {"label": "Conversão com Parceria", "value": "42.5%"},
                    {"label": "Conversão sem Parceria", "value": "18.3%"}
                ]
            ) }}

            {{ advanced_insight(
                title="Alerta de Retenção",
                content="Identificamos um aumento nos cancelamentos após o período de isenção. Recomendamos reforçar o onboarding e o acompanhamento.",
                icon_type="warning",
                color="yellow",
                metrics=[
                    {"label": "Taxa de Cancelamento", "value": "15.2%", "trend": "+3.4%", "trend_positive": false}
                ],
                action_text="Ver detalhes",
                action_url="#"
            ) }}
        </div>
    </div>

    <!-- Insights Cards - Improved Design with More Cards -->
    <div class="mt-10">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Insights e Ações Rápidas</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Card: Leads -->
            <div class="bg-white rounded-lg p-6 border border-gray-100 flex flex-col shadow-sm card-hover transition-all duration-300 h-full">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"></path>
                        </svg>
                    </div>
                    <span class="text-lg font-semibold text-gray-900">Leads</span>
                </div>
                <p class="text-sm text-gray-600 mb-4 leading-relaxed">Gerencie potenciais clientes, acompanhe interações e monitore o progresso no funil de vendas.</p>
                <div class="flex flex-wrap gap-2 mt-4 mb-2">
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Leads</span>
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Funil de Vendas</span>
                </div>
                <div class="mt-auto pt-4 border-t border-gray-100">
                    <div class="flex flex-col space-y-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2">
                                    <svg class="w-4 h-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">Funil de Vendas</span>
                                    <p class="text-sm font-medium">{{ total_leads|int }} leads cadastrados</p>
                                </div>
                            </div>
                            <a href="/leads/" class="text-primary hover:text-blue-800 text-sm font-medium flex items-center">
                                Ver detalhes
                                <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card: Oportunidades -->
            <div class="bg-white rounded-lg p-6 border border-gray-100 flex flex-col shadow-sm card-hover transition-all duration-300 h-full">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"></path>
                        </svg>
                    </div>
                    <span class="text-lg font-semibold text-gray-900">Oportunidades</span>
                </div>
                <p class="text-sm text-gray-600 mb-4 leading-relaxed">Acompanhe negociações em andamento, gerencie propostas e monitore a conversão de leads em clientes.</p>
                <div class="flex flex-wrap gap-2 mt-4 mb-2">
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Oportunidades</span>
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Negociações</span>
                </div>
                <div class="mt-auto pt-4 border-t border-gray-100">
                    <div class="flex flex-col space-y-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-green-50 flex items-center justify-center mr-2">
                                    <svg class="w-4 h-4 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">Negociações</span>
                                    <p class="text-sm font-medium">{{ total_opportunities|int }} oportunidades</p>
                                </div>
                            </div>
                            <a href="/opportunities/" class="text-primary hover:text-blue-800 text-sm font-medium flex items-center">
                                Ver detalhes
                                <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card: Implantações -->
            <div class="bg-white rounded-lg p-6 border border-gray-100 flex flex-col shadow-sm card-hover transition-all duration-300 h-full">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"></path>
                        </svg>
                    </div>
                    <span class="text-lg font-semibold text-gray-900">Implantações</span>
                </div>
                <p class="text-sm text-gray-600 mb-4 leading-relaxed">Gerencie projetos em implementação, acompanhe o progresso e garanta a entrega de valor ao cliente.</p>
                <div class="flex flex-wrap gap-2 mt-4 mb-2">
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Implantações</span>
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Projetos</span>
                </div>
                <div class="mt-auto pt-4 border-t border-gray-100">
                    <div class="flex flex-col space-y-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-indigo-50 flex items-center justify-center mr-2">
                                    <svg class="w-4 h-4 text-indigo-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">Projetos</span>
                                    <p class="text-sm font-medium">{{ total_implementations|int }} implantações</p>
                                </div>
                            </div>
                            <a href="/implementations/" class="text-primary hover:text-blue-800 text-sm font-medium flex items-center">
                                Ver detalhes
                                <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card: Universidades -->
            <div class="bg-white rounded-lg p-6 border border-gray-100 flex flex-col shadow-sm card-hover transition-all duration-300 h-full">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"></path>
                        </svg>
                    </div>
                    <span class="text-lg font-semibold text-gray-900">Universidades</span>
                </div>
                <p class="text-sm text-gray-600 mb-4 leading-relaxed">Acompanhe o desempenho por universidade, visualize métricas de conversão e receita recorrente.</p>
                <div class="flex flex-wrap gap-2 mt-4 mb-2">
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Universidades</span>
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Análise</span>
                </div>
                <div class="mt-auto pt-4 border-t border-gray-100">
                    <div class="flex flex-col space-y-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-2">
                                    <svg class="w-4 h-4 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path d="M12 14l9-5-9-5-9 5 9 5z" />
                                        <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                                    </svg>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">Análise</span>
                                    <p class="text-sm font-medium">{{ total_universities|int }} universidades</p>
                                </div>
                            </div>
                            <a href="/universities/" class="text-primary hover:text-blue-800 text-sm font-medium flex items-center">
                                Ver detalhes
                                <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card: Assinaturas (MRR) -->
            <div class="bg-white rounded-lg p-6 border border-gray-100 flex flex-col shadow-sm card-hover transition-all duration-300 h-full">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l .813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"></path>
                        </svg>
                    </div>
                    <span class="text-lg font-semibold text-gray-900">Assinaturas (MRR)</span>
                </div>
                <p class="text-sm text-gray-600 mb-4 leading-relaxed">Acompanhe a receita mensal recorrente, visualize a distribuição por responsável, produto e universidade.</p>
                <div class="flex flex-wrap gap-2 mt-4 mb-2">
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">MRR</span>
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Receita</span>
                </div>
                <div class="mt-auto pt-4 border-t border-gray-100">
                    <div class="flex flex-col space-y-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-green-50 flex items-center justify-center mr-2">
                                    <svg class="w-4 h-4 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">Receita</span>
                                    <p class="text-sm font-medium">{{ mrr_total|e }}</p>
                                </div>
                            </div>
                            <a href="/subscriptions/" class="text-primary hover:text-blue-800 text-sm font-medium flex items-center">
                                Ver detalhes
                                <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card: Conversão -->
            <div class="bg-white rounded-lg p-6 border border-gray-100 flex flex-col shadow-sm card-hover transition-all duration-300 h-full">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"></path>
                        </svg>
                    </div>
                    <span class="text-lg font-semibold text-gray-900">Análise de Conversão</span>
                </div>
                <p class="text-sm text-gray-600 mb-4 leading-relaxed">Visualize o funil de vendas, analise taxas de conversão entre etapas e identifique oportunidades de melhoria.</p>
                <div class="flex flex-wrap gap-2 mt-4 mb-2">
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Conversão</span>
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Funil</span>
                </div>
                <div class="mt-auto pt-4 border-t border-gray-100">
                    <div class="flex flex-col space-y-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2">
                                    <svg class="w-4 h-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                    </svg>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">Funil</span>
                                    <p class="text-sm font-medium">{{ "%.1f"|format(conversao_geral|float|default(0)) }}% conversão geral</p>
                                </div>
                            </div>
                            <a href="/conversion/" class="text-primary hover:text-blue-800 text-sm font-medium flex items-center">
                                Ver detalhes
                                <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card: Cancelamentos e Churn -->
            <div class="bg-white rounded-lg p-6 border border-gray-100 flex flex-col shadow-sm card-hover transition-all duration-300 h-full">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"></path>
                        </svg>
                    </div>
                    <span class="text-lg font-semibold text-gray-900">Cancelamentos e Churn</span>
                </div>
                <p class="text-sm text-gray-600 mb-4 leading-relaxed">Monitore cancelamentos, analise padrões de churn e identifique oportunidades de retenção de clientes.</p>
                <div class="flex flex-wrap gap-2 mt-4 mb-2">
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Churn</span>
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Retenção</span>
                </div>
                <div class="mt-auto pt-4 border-t border-gray-100">
                    <div class="flex flex-col space-y-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-red-50 flex items-center justify-center mr-2">
                                    <svg class="w-4 h-4 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                    </svg>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">Retenção</span>
                                    <p class="text-sm font-medium">Análise de cancelamentos</p>
                                </div>
                            </div>
                            <a href="/churn/" class="text-primary hover:text-blue-800 text-sm font-medium flex items-center">
                                Ver detalhes
                                <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Leads Table -->
    <div class="mt-8">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium text-gray-900">Leads Recentes</h2>
            <a href="/leads/" class="text-primary hover:text-blue-800 text-sm font-medium flex items-center">
                Ver todos
                <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </a>
        </div>

        <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-100">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Nome</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Data de Criação</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Formação</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Universidade</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Ações</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for lead in recent_leads %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ lead['Nome do Lead']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ lead['Data_criacao_lead']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ lead['Formação Academica']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ lead['Universidade']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <a href="/leads/{{ lead['Lead_id']|e }}" class="text-primary hover:underline">Ver Detalhes</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
<!-- Garantir que o Chart.js esteja carregado com SRI -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"
        integrity="sha384-+8jvZ3BSCIty6l/5oCPE2Es/P4jXwKupZhUzDaehcNUtR8DJZ4wv4nfhPpJHUvyF"
        crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"
        integrity="sha384-JPsChhUGAoY4nduYC7VcNHqnQlvMdqYFjCh+OJlv2N1Zq8cTa9LZvpNyKzV8k8pF"
        crossorigin="anonymous"></script>

<script>
    // Definir o objeto AmigoDH se não existir
    window.AmigoDH = window.AmigoDH || {};

    // Definir cores padrão se não existirem
    AmigoDH.colors = AmigoDH.colors || {
        primary: '#0087EB',
        secondary: '#4fd1c5',
        success: '#48bb78',
        danger: '#f56565',
        warning: '#ed8936',
        info: '#4299e1',
        blue: ['#0087EB', '#1E96F0', '#3CA5F5', '#5AB4FA', '#78C3FF', '#96D2FF', '#B8DFFF', '#D6EDFF']
    };

    // Definir função de formatação de moeda se não existir
    AmigoDH.formatCurrency = AmigoDH.formatCurrency || function(value) {
        return 'R$ ' + parseFloat(value).toLocaleString('pt-BR', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    };

    // Definir função de criação de gráfico de linha se não existir
    AmigoDH.createLineChart = AmigoDH.createLineChart || function(canvasId, labels, data, label, options) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error('Canvas element not found:', canvasId);
            return;
        }

        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error('Could not get 2D context for canvas:', canvasId);
            return;
        }

        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (options && options.tooltipCallback) {
                                return label + options.tooltipCallback(context.parsed.y);
                            }
                            return label + context.parsed.y;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            if (options && options.yAxisCallback) {
                                return options.yAxisCallback(value);
                            }
                            return value;
                        }
                    }
                }
            }
        };

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: label,
                    data: data,
                    backgroundColor: options && options.backgroundColor ? options.backgroundColor : 'rgba(0, 135, 235, 0.2)',
                    borderColor: options && options.borderColor ? options.borderColor : '#0087EB',
                    borderWidth: 2,
                    fill: options && options.fill !== undefined ? options.fill : false,
                    tension: options && options.tension !== undefined ? options.tension : 0.1
                }]
            },
            options: chartOptions
        });
    };

    // Definir função de criação de gráfico de funil se não existir
    AmigoDH.createFunnelChart = AmigoDH.createFunnelChart || function(canvasId, labels, data, options) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error('Canvas element not found:', canvasId);
            return;
        }

        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error('Could not get 2D context for canvas:', canvasId);
            return;
        }

        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true
                }
            }
        };

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: options && options.backgroundColor ? options.backgroundColor : [
                        '#0087EB', '#1E96F0', '#3CA5F5', '#5AB4FA'
                    ],
                    borderWidth: 0
                }]
            },
            options: chartOptions
        });
    };

    document.addEventListener('DOMContentLoaded', function() {
        if (typeof console !== 'undefined' && console.log) {
            console.log('DOM loaded for dashboard page');
            console.log('Chart.js loaded:', typeof Chart !== 'undefined');
            console.log('AmigoDH object:', window.AmigoDH);
        }

        // Verificar se as funções de criação de gráficos estão disponíveis
        if (window.AmigoDH && typeof console !== 'undefined' && console.log) {
            console.log('createFunnelChart exists:', typeof window.AmigoDH.createFunnelChart === 'function');
            console.log('createLineChart exists:', typeof window.AmigoDH.createLineChart === 'function');
        }

        // Debug chart data - SECURITY: Sanitize JSON data
        let chartData;
        try {
            // Validate and sanitize chart data before parsing
            const rawData = '{{ chart_data|tojson }}';
            chartData = JSON.parse(rawData);
            if (typeof console !== 'undefined' && console.log) {
                console.log('Chart data parsed successfully:', chartData);
            }
        } catch (error) {
            if (typeof console !== 'undefined' && console.error) {
                console.error('Error parsing chart data:', error);
            }
            chartData = {};
        }

        // Check if canvas elements exist
        if (typeof console !== 'undefined' && console.log) {
            console.log('funnelChart canvas exists:', !!document.getElementById('funnelChart'));
            console.log('timelineChart canvas exists:', !!document.getElementById('timelineChart'));
            console.log('mrrCreationChart canvas exists:', !!document.getElementById('mrrCreationChart'));
        }

        try {
            // Dados para o gráfico de funil
            const funnelLabels = ['Leads', 'Oportunidades', 'Implantações', 'Clientes Ativos'];

            // Converter strings para números, com fallback para 0 se a conversão falhar
            const totalLeads = parseInt({{ total_leads|e }}) || 0;
            const totalOpportunities = parseInt({{ total_opportunities|e }}) || 0;
            const totalImplementations = parseInt({{ total_implementations|e }}) || 0;
            const finalizedCount = parseInt({{ finalized_count|e }}) || 0;

            const funnelData = [totalLeads, totalOpportunities, totalImplementations, finalizedCount];

            if (typeof console !== 'undefined' && console.log) {
                console.log('Funnel data:', funnelLabels, funnelData);
            }

            // Verificar se temos dados válidos
            // Check if we have error information from the backend
            if (chartData.errors && chartData.errors.funnel_stages) {
                const error = chartData.errors.funnel_stages;
                document.getElementById('funnelChart').parentNode.innerHTML = `
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-yellow-800">Dados não disponíveis</h3>
                                <div class="mt-2 text-sm text-yellow-700">
                                    <p>${error.message}</p>
                                    <p class="mt-1">Colunas necessárias:
                                        ${error.required_columns.map(col => `<span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">${col}</span>`).join(' ')}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            // Fallback to client-side check
            else if (funnelLabels.length > 0 && funnelData.length > 0 && !funnelData.every(d => d === 0)) {
                try {
                    // Verificar se a função existe
                    if (typeof AmigoDH === 'undefined' || typeof AmigoDH.createFunnelChart !== 'function') {
                        if (typeof console !== 'undefined' && console.error) {
                            console.error('AmigoDH.createFunnelChart is not a function');
                        }
                        document.getElementById('funnelChart').parentNode.innerHTML = `
                            <div class="bg-red-50 border-l-4 border-red-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">Erro ao criar gráfico</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            <p>A função de criação de gráfico não está disponível.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        return;
                    }

                    // Criar gráfico de funil usando a função compartilhada
                    const colors = AmigoDH.colors ? AmigoDH.colors.blue : ['#0087EB', '#1E96F0', '#3CA5F5', '#5AB4FA'];
                    AmigoDH.createFunnelChart('funnelChart', funnelLabels, funnelData, {
                        backgroundColor: [
                            colors[2] || '#3CA5F5',
                            colors[0] || '#0087EB',
                            colors[4] || '#78C3FF',
                            colors[6] || '#B8DFFF'
                        ]
                    });
                    if (typeof console !== 'undefined' && console.log) {
                        console.log('Funnel chart created successfully');
                    }
                } catch (error) {
                    if (typeof console !== 'undefined' && console.error) {
                        console.error('Error creating funnel chart:', error);
                    }
                    document.getElementById('funnelChart').parentNode.innerHTML = `
                        <div class="bg-red-50 border-l-4 border-red-400 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">Erro ao criar gráfico</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <p>${error.message}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }
            } else {
                if (typeof console !== 'undefined' && console.error) {
                    console.error('Invalid funnel data');
                }
                document.getElementById('funnelChart').parentNode.innerHTML = `
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-yellow-800">Dados não disponíveis</h3>
                                <div class="mt-2 text-sm text-yellow-700">
                                    <p>Não foi possível carregar os dados para o gráfico de Funil de Conversão.</p>
                                    <p class="mt-1">Colunas necessárias:
                                        <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">Lead_id</span>
                                        <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">Oportunidade_id</span>
                                        <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">Fase_Implantacao</span>
                                        <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">Status_Implantacao</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        } catch (error) {
            if (typeof console !== 'undefined' && console.error) {
                console.error('Error creating funnel chart:', error);
            }
            document.getElementById('funnelChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico: ' + error.message + '</div>';
        }

        try {
            // Dados para o gráfico de MRR
            let mrrLabels = [];
            let mrrData = [];

            // Obter dados para o gráfico de evolução de implantações
            // Tentar obter dados diretamente do timeline_data ou do implementation_data.timeline
            const timelineData = chartData.timeline_data ||
                               (chartData.implementation_data && chartData.implementation_data.timeline) ||
                               {};

            if (typeof console !== 'undefined' && console.log) {
                console.log('Timeline data:', timelineData);
            }

            try {
                if (timelineData && (timelineData.months || timelineData.counts)) {
                    let timelineLabels = [];
                    let timelineValues = [];

                    // Verificar se os dados são strings (formato JSON) ou arrays
                    if (typeof timelineData.months === 'string' && timelineData.months.startsWith('[')) {
                        try {
                            timelineLabels = JSON.parse(timelineData.months);
                            timelineValues = JSON.parse(timelineData.counts).map(v => parseInt(v) || 0);
                            if (typeof console !== 'undefined' && console.log) {
                                console.log('Parsed timeline data from JSON strings:', timelineLabels, timelineValues);
                            }
                        } catch (e) {
                            if (typeof console !== 'undefined' && console.error) {
                                console.error('Error parsing timeline data:', e);
                            }
                        }
                    } else if (Array.isArray(timelineData.months)) {
                        timelineLabels = timelineData.months;
                        timelineValues = Array.isArray(timelineData.counts)
                            ? timelineData.counts.map(v => parseInt(v) || 0)
                            : [];
                        if (typeof console !== 'undefined' && console.log) {
                            console.log('Using timeline data from arrays:', timelineLabels, timelineValues);
                        }
                    } else if (typeof timelineData.months === 'string' && !timelineData.months.startsWith('[')) {
                        // Handle case where months might be a comma-separated string
                        try {
                            timelineLabels = timelineData.months.split(',').map(m => m.trim());

                            if (typeof timelineData.counts === 'string') {
                                timelineValues = timelineData.counts.split(',').map(v => parseInt(v.trim()) || 0);
                            } else {
                                timelineValues = [];
                            }

                            if (typeof console !== 'undefined' && console.log) {
                                console.log('Parsed timeline data from comma-separated strings:', timelineLabels, timelineValues);
                            }
                        } catch (e) {
                            if (typeof console !== 'undefined' && console.error) {
                                console.error('Error parsing timeline data from comma-separated strings:', e);
                            }
                        }
                    }

                    // Check if we have error information from the backend
                    if (chartData.errors && chartData.errors.implementation_phases) {
                        const error = chartData.errors.implementation_phases;
                        document.getElementById('timelineChart').parentNode.innerHTML = `
                            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-yellow-800">Dados não disponíveis</h3>
                                        <div class="mt-2 text-sm text-yellow-700">
                                            <p>${error.message}</p>
                                            <p class="mt-1">Colunas necessárias:
                                                ${error.required_columns.map(col => `<span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">${col}</span>`).join(' ')}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                    // Fallback to client-side check
                    else if (timelineLabels.length > 0 && timelineValues.length > 0 && !timelineValues.every(v => v === 0)) {
                        try {
                            // Verificar se a função existe
                            if (typeof AmigoDH === 'undefined' || typeof AmigoDH.createLineChart !== 'function') {
                                if (typeof console !== 'undefined' && console.error) {
                                    console.error('AmigoDH.createLineChart is not a function');
                                }
                                document.getElementById('timelineChart').parentNode.innerHTML = `
                                    <div class="bg-red-50 border-l-4 border-red-400 p-4">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <h3 class="text-sm font-medium text-red-800">Erro ao criar gráfico</h3>
                                                <div class="mt-2 text-sm text-red-700">
                                                    <p>A função de criação de gráfico não está disponível.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `;
                                return;
                            }

                            // Criar gráfico de evolução de implantações
                            const primaryColor = AmigoDH.colors ? AmigoDH.colors.primary : '#0087EB';
                            AmigoDH.createLineChart('timelineChart', timelineLabels, timelineValues, 'Implantações', {
                                backgroundColor: 'rgba(152, 207, 255, 0.2)',
                                borderColor: primaryColor,
                                fill: true
                            });
                            if (typeof console !== 'undefined' && console.log) {
                                console.log('Timeline chart created successfully');
                            }
                        } catch (error) {
                            if (typeof console !== 'undefined' && console.error) {
                                console.error('Error creating timeline chart:', error);
                            }
                            document.getElementById('timelineChart').parentNode.innerHTML = `
                                <div class="bg-red-50 border-l-4 border-red-400 p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">Erro ao criar gráfico</h3>
                                            <div class="mt-2 text-sm text-red-700">
                                                <p>${error.message}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }
                    } else {
                        console.error('Timeline data arrays are empty or all zeros after processing');
                        document.getElementById('timelineChart').parentNode.innerHTML = `
                            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-yellow-800">Dados não disponíveis</h3>
                                        <div class="mt-2 text-sm text-yellow-700">
                                            <p>Não foi possível carregar os dados para o gráfico de Evolução de Implantações.</p>
                                            <p class="mt-1">Colunas necessárias:
                                                <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">Data_Criacao_Implantacao</span>
                                                <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">Fase_Implantacao</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    console.error('Implementation timeline data not available');
                    document.getElementById('timelineChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de evolução de implantações não disponíveis</div>';
                }
            } catch (error) {
                console.error('Error processing timeline data:', error);
                document.getElementById('timelineChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao processar dados de evolução: ' + error.message + '</div>';
            }

            // Dados para o gráfico de MRR por data de criação
            let mrrCreationLabels = [];
            let mrrCreationData = [];

            // Use data from backend if available
            if (chartData && chartData.mrr_creation_labels && chartData.mrr_creation_values) {
                try {
                    // Verificar se os dados são strings (formato JSON) ou arrays
                    if (typeof chartData.mrr_creation_labels === 'string' && chartData.mrr_creation_labels.startsWith('[')) {
                        try {
                            mrrCreationLabels = JSON.parse(chartData.mrr_creation_labels);
                            mrrCreationData = JSON.parse(chartData.mrr_creation_values).map(value => {
                                const parsed = parseFloat(value);
                                return isNaN(parsed) ? 0 : parsed;
                            });
                            console.log('Parsed MRR creation data from JSON strings');
                        } catch (e) {
                            console.error('Error parsing MRR creation data:', e);
                            mrrCreationLabels = [];
                            mrrCreationData = [];
                        }
                    } else if (Array.isArray(chartData.mrr_creation_labels)) {
                        mrrCreationLabels = chartData.mrr_creation_labels;

                        // Verificar se mrr_creation_values é um array antes de usar map
                        if (Array.isArray(chartData.mrr_creation_values)) {
                            mrrCreationData = chartData.mrr_creation_values.map(value => {
                                // Garantir que o valor é um número
                                const parsed = parseFloat(value);
                                return isNaN(parsed) ? 0 : parsed;
                            });
                        } else {
                            // Se não for um array, criar um array vazio
                            console.error('mrr_creation_values is not an array:', chartData.mrr_creation_values);
                            mrrCreationData = [];
                        }
                    } else if (typeof chartData.mrr_creation_labels === 'string' && !chartData.mrr_creation_labels.startsWith('[')) {
                        // Handle case where labels might be a comma-separated string
                        try {
                            mrrCreationLabels = chartData.mrr_creation_labels.split(',').map(m => m.trim());
                            mrrCreationData = chartData.mrr_creation_values.split(',').map(v => {
                                const parsed = parseFloat(v.trim());
                                return isNaN(parsed) ? 0 : parsed;
                            });
                            console.log('Parsed MRR creation data from comma-separated strings');
                        } catch (e) {
                            console.error('Error parsing MRR creation data from comma-separated strings:', e);
                            mrrCreationLabels = [];
                            mrrCreationData = [];
                        }
                    }

                    console.log('Using MRR Creation data from backend:', mrrCreationLabels, mrrCreationData);

                    // If we have no data, show a message
                    // Check if we have error information from the backend
                    if (chartData.errors && chartData.errors.mrr_creation) {
                        const error = chartData.errors.mrr_creation;
                        document.getElementById('mrrCreationChart').parentNode.innerHTML = `
                            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-yellow-800">Dados não disponíveis</h3>
                                        <div class="mt-2 text-sm text-yellow-700">
                                            <p>${error.message}</p>
                                            <p class="mt-1">Colunas necessárias:
                                                ${error.required_columns.map(col => `<span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">${col}</span>`).join(' ')}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                    // Fallback to client-side check
                    else if (mrrCreationLabels.length === 0 || mrrCreationData.length === 0 || mrrCreationData.every(d => d === 0)) {
                        console.error('MRR Creation data arrays are empty or all zeros');
                        document.getElementById('mrrCreationChart').parentNode.innerHTML = `
                            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-yellow-800">Dados não disponíveis</h3>
                                        <div class="mt-2 text-sm text-yellow-700">
                                            <p>Não foi possível carregar os dados para o gráfico de MRR Cumulativo.</p>
                                            <p class="mt-1">Colunas necessárias:
                                                <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">Status_Implantacao</span>
                                                <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">Valor Mensalidade</span>
                                                <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">Data_Criacao_Implantacao</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    } else {
                        try {
                            // Verificar se a função existe
                            if (typeof AmigoDH === 'undefined' || typeof AmigoDH.createLineChart !== 'function') {
                                console.error('AmigoDH.createLineChart is not a function');
                                document.getElementById('mrrCreationChart').parentNode.innerHTML = `
                                    <div class="bg-red-50 border-l-4 border-red-400 p-4">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <h3 class="text-sm font-medium text-red-800">Erro ao criar gráfico</h3>
                                                <div class="mt-2 text-sm text-red-700">
                                                    <p>A função de criação de gráfico não está disponível.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `;
                                return;
                            }

                            // Criar gráfico de MRR por data de criação
                            AmigoDH.createLineChart('mrrCreationChart', mrrCreationLabels, mrrCreationData, 'MRR Cumulativo (R$)', {
                                backgroundColor: 'rgba(79, 209, 197, 0.2)',
                                borderColor: '#4fd1c5', // Teal color
                                fill: true,
                                tooltipCallback: function(value) {
                                    return 'MRR Cumulativo: ' + AmigoDH.formatCurrency(value);
                                },
                                yAxisCallback: function(value) {
                                    return AmigoDH.formatCurrency(value);
                                }
                            });
                            console.log('MRR Creation chart created successfully');
                        } catch (innerError) {
                            console.error('Error creating MRR Creation chart:', innerError);
                            document.getElementById('mrrCreationChart').parentNode.innerHTML = `
                                <div class="bg-red-50 border-l-4 border-red-400 p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">Erro ao criar gráfico</h3>
                                            <div class="mt-2 text-sm text-red-700">
                                                <p>${innerError.message}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }
                    }
                } catch (error) {
                    console.error('Error processing MRR creation data:', error);
                    document.getElementById('mrrCreationChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao processar dados de MRR: ' + error.message + '</div>';
                }
            } else {
                // Fallback message if data is not available
                console.error('MRR Creation data not available from backend');
                document.getElementById('mrrCreationChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de MRR por data de criação não disponíveis</div>';
            }
        } catch (error) {
            console.error('Error creating MRR charts:', error);
            document.getElementById('mrrChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de MRR</div>';
            document.getElementById('mrrCreationChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de MRR por data de criação</div>';
        }
    });
</script>
{% endblock %}
