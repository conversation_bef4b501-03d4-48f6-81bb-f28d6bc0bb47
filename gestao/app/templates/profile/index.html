{% extends "base.html" %}

{% block title %}DataHub Amigo One - Perfil do Usuário{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Hero Section -->
    <div class="bg-white rounded-lg shadow-sm p-8 mb-8 border border-gray-200">
        <div class="flex items-center space-x-6">
            <!-- Avatar -->
            <div class="flex-shrink-0">
                <div class="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center">
                    <span class="text-2xl font-bold text-white">
                        {{ user.username[0].upper() if user.username else 'U' }}
                    </span>
                </div>
            </div>

            <!-- User Info -->
            <div>
                <h1 class="text-3xl font-bold text-black">Perfil do Usuário</h1>
                <p class="text-gray-600 mt-2">Gerencie suas informações pessoais e configurações de segurança</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- User Information -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 class="text-lg font-semibold text-black mb-4">Informações Pessoais</h2>

            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-black mb-1">Nome de Usuário</label>
                    <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                        <span class="text-gray-700">{{ user.username|e }}</span>
                    </div>
                </div>

                {% if user.email %}
                <div>
                    <label class="block text-sm font-medium text-black mb-1">Email</label>
                    <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                        <span class="text-gray-700">{{ user.email|e }}</span>
                    </div>
                </div>
                {% endif %}

                {% if user.created_at %}
                <div>
                    <label class="block text-sm font-medium text-black mb-1">Membro desde</label>
                    <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                        <span class="text-gray-700">{{ user.created_at|e }}</span>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Change Password -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 class="text-lg font-semibold text-black mb-4">Alterar Senha</h2>

            <form method="POST" action="{{ url_for('profile.change_password') }}" class="space-y-4">
                <div>
                    <label for="current_password" class="block text-sm font-medium text-black mb-1">Senha Atual</label>
                    <input type="password" id="current_password" name="current_password" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <div>
                    <label for="new_password" class="block text-sm font-medium text-black mb-1">Nova Senha</label>
                    <input type="password" id="new_password" name="new_password" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <p class="text-xs text-gray-600 mt-1">
                        A senha deve ter pelo menos 8 caracteres, incluindo maiúscula, minúscula, número e símbolo especial.
                    </p>
                </div>

                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-black mb-1">Confirmar Nova Senha</label>
                    <input type="password" id="confirm_password" name="confirm_password" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <button type="submit"
                        class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                    Alterar Senha
                </button>
            </form>
        </div>
    </div>

    <!-- Security Guidelines -->
    <div class="bg-blue-50 rounded-lg p-6 mt-8 border border-blue-200">
        <h3 class="text-lg font-semibold text-blue-800 mb-3">Dicas de Segurança</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
            <div class="flex items-start space-x-2">
                <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Use uma senha única para esta conta</span>
            </div>
            <div class="flex items-start space-x-2">
                <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Combine letras, números e símbolos</span>
            </div>
            <div class="flex items-start space-x-2">
                <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Evite informações pessoais óbvias</span>
            </div>
            <div class="flex items-start space-x-2">
                <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Altere sua senha regularmente</span>
            </div>
        </div>
    </div>
</div>

<script>
// Password strength indicator
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const requirements = [
        { regex: /.{8,}/, text: 'Pelo menos 8 caracteres' },
        { regex: /[A-Z]/, text: 'Uma letra maiúscula' },
        { regex: /[a-z]/, text: 'Uma letra minúscula' },
        { regex: /\d/, text: 'Um número' },
        { regex: /[!@#$%^&*(),.?":{}|<>]/, text: 'Um símbolo especial' }
    ];

    // You can add visual feedback here if needed
});

// Confirm password validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;

    if (confirmPassword && newPassword !== confirmPassword) {
        this.setCustomValidity('As senhas não coincidem');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
