{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section %}

{% block title %}Análise de Conversão - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Análise de Conversão",
    subtitle="Acompanhe o desempenho do funil de vendas, visualize taxas de conversão e identifique oportunidades de melhoria.",
    stats=[
        {
            "label": "Conversão Geral",
            "value": (conversao_geral|float|round(1))|string + "%",
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />'
        },
        {
            "label": "Receita Estimada",
            "value": estimated_revenue,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />'
        }
    ],
    bg_class="bg-gray-100"
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Conversion Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- KPI: Total de Leads -->
        {{ kpi_card(
            title="Total de Leads",
            value=total_leads,
            subtitle="Potenciais clientes cadastrados",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />',
            color="primary"
        )|e }}

        <!-- KPI: Leads com Oportunidades -->
        {{ kpi_card(
            title="Leads com Oportunidades",
            value=leads_with_opps,
            subtitle="Taxa de conversão: " ~ (lead_to_opp_rate|float|round(1)) ~ "%",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="primary",
            percentage=lead_to_opp_rate|float,
            percentage_label="de conversão",
            is_positive=true
        ) }}

        <!-- KPI: Oportunidades com Implantações -->
        {{ kpi_card(
            title="Oportunidades com Implantações",
            value=opps_with_impl,
            subtitle="Taxa de conversão: " ~ (opp_to_impl_rate|float|round(1)) ~ "%",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />',
            color="success",
            percentage=opp_to_impl_rate|float,
            percentage_label="de conversão",
            is_positive=true
        ) }}

        <!-- KPI: Valor Médio -->
        {{ kpi_card(
            title="Valor Médio",
            value=avg_value,
            subtitle="Por implantação",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="success"
        )|e }}
    </div>

    <!-- Funil de Vendas Avançado com D3.js -->
    <div class="mb-8">
        <h2 class="text-xl font-bold text-gray-900 mb-6">Funil de Vendas Completo</h2>

        <!-- Funnel Overview Cards - App Colors -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
            <div id="funnel-stage-0" class="group bg-white rounded-lg shadow-sm p-4 cursor-pointer hover:shadow-md transition-all duration-200 border border-gray-200 hover:border-blue-300">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-semibold text-gray-900">🎯 Leads</h3>
                    <div class="w-3 h-3 rounded-full bg-blue-500"></div>
                </div>
                <div class="text-2xl font-bold text-gray-900 mb-1" id="stage-value-0">-</div>
                <div class="text-sm text-blue-600 font-medium" id="stage-percentage-0">100%</div>
                <div class="text-xs text-gray-500 mt-1" id="stage-description-0">Potenciais clientes</div>
            </div>

            <div id="funnel-stage-1" class="group bg-white rounded-lg shadow-sm p-4 cursor-pointer hover:shadow-md transition-all duration-200 border border-gray-200 hover:border-blue-300">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-semibold text-gray-900">💼 Oportunidades</h3>
                    <div class="w-3 h-3 rounded-full bg-blue-600"></div>
                </div>
                <div class="text-2xl font-bold text-gray-900 mb-1" id="stage-value-1">-</div>
                <div class="text-sm text-blue-600 font-medium" id="stage-percentage-1">-</div>
                <div class="text-xs text-gray-500 mt-1" id="stage-description-1">Leads qualificados</div>
            </div>

            <div id="funnel-stage-2" class="group bg-white rounded-lg shadow-sm p-4 cursor-pointer hover:shadow-md transition-all duration-200 border border-gray-200 hover:border-blue-300">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-semibold text-gray-900">⚙️ Implementações</h3>
                    <div class="w-3 h-3 rounded-full bg-blue-700"></div>
                </div>
                <div class="text-2xl font-bold text-gray-900 mb-1" id="stage-value-2">-</div>
                <div class="text-sm text-blue-600 font-medium" id="stage-percentage-2">-</div>
                <div class="text-xs text-gray-500 mt-1" id="stage-description-2">Em implementação</div>
            </div>

            <div id="funnel-stage-3" class="group bg-white rounded-lg shadow-sm p-4 cursor-pointer hover:shadow-md transition-all duration-200 border border-gray-200 hover:border-blue-300">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-semibold text-gray-900">✅ Usuários Ativos</h3>
                    <div class="w-3 h-3 rounded-full bg-blue-800"></div>
                </div>
                <div class="text-2xl font-bold text-gray-900 mb-1" id="stage-value-3">-</div>
                <div class="text-sm text-blue-600 font-medium" id="stage-percentage-3">-</div>
                <div class="text-xs text-gray-500 mt-1" id="stage-description-3">Finalizados</div>
            </div>

            <div id="funnel-stage-4" class="group bg-white rounded-lg shadow-sm p-4 cursor-pointer hover:shadow-md transition-all duration-200 border border-gray-200 hover:border-blue-300">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-semibold text-gray-900">💰 Produtivos</h3>
                    <div class="w-3 h-3 rounded-full bg-blue-900"></div>
                </div>
                <div class="text-2xl font-bold text-gray-900 mb-1" id="stage-value-4">-</div>
                <div class="text-sm text-blue-600 font-medium" id="stage-percentage-4">-</div>
                <div class="text-xs text-gray-500 mt-1" id="stage-description-4">Gerando receita</div>
            </div>
        </div>

        <!-- Filtros Múltiplos Avançados -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-200">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Filtros de Análise Múltipla</h3>
                    <p class="text-sm text-gray-600 mt-1">Selecione até 3 filtros para comparação com sobreposição</p>

                </div>
                <div class="flex items-center space-x-3">
                    <div class="text-sm text-gray-600">
                        <span id="selected-filters-count">0</span>/3 filtros selecionados
                    </div>
                    <div class="w-3 h-3 rounded-full bg-blue-500" title="Filtro 1"></div>
                    <div class="w-3 h-3 rounded-full bg-green-500" title="Filtro 2"></div>
                    <div class="w-3 h-3 rounded-full bg-amber-500" title="Filtro 3"></div>
                </div>
            </div>

            <!-- Filtros Selecionados -->
            <div id="selected-filters-display" class="mb-4 space-y-2 hidden">
                <h4 class="text-sm font-medium text-gray-700">Filtros Ativos:</h4>
                <div id="active-filters-list" class="flex flex-wrap gap-2">
                    <!-- Filtros ativos serão inseridos aqui -->
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label for="filter-turma" class="block text-sm font-medium text-gray-700 mb-2">
                        Turma
                        <span class="text-xs text-gray-500">(Multi-seleção)</span>
                    </label>
                    <select id="filter-turma" multiple class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 h-24">
                        {% if filter_options and filter_options.turmas %}
                            {% for turma in filter_options.turmas %}
                            <option value="{{ turma }}">{{ turma }}</option>
                            {% endfor %}
                        {% else %}
                            <option disabled>Nenhuma turma disponível</option>
                        {% endif %}
                    </select>
                </div>
                <div>
                    <label for="filter-responsavel" class="block text-sm font-medium text-gray-700 mb-2">
                        Responsável
                        <span class="text-xs text-gray-500">(Multi-seleção)</span>
                    </label>
                    <select id="filter-responsavel" multiple class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 h-24">
                        {% if filter_options and filter_options.responsaveis %}
                            {% for responsavel in filter_options.responsaveis %}
                            <option value="{{ responsavel }}">{{ responsavel }}</option>
                            {% endfor %}
                        {% else %}
                            <option disabled>Nenhum responsável disponível</option>
                        {% endif %}
                    </select>
                </div>
                <div>
                    <label for="filter-universidade" class="block text-sm font-medium text-gray-700 mb-2">
                        Universidade
                        <span class="text-xs text-gray-500">(Multi-seleção)</span>
                    </label>
                    <select id="filter-universidade" multiple class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 h-24">
                        {% if filter_options and filter_options.universidades %}
                            {% for universidade in filter_options.universidades %}
                            <option value="{{ universidade }}">{{ universidade }}</option>
                            {% endfor %}
                        {% else %}
                            <option disabled>Nenhuma universidade disponível</option>
                        {% endif %}
                    </select>
                </div>
                <div>
                    <label for="filter-cidade" class="block text-sm font-medium text-gray-700 mb-2">
                        Cidade
                        <span class="text-xs text-gray-500">(Multi-seleção)</span>
                    </label>
                    <select id="filter-cidade" multiple class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 h-24">
                        {% if filter_options and filter_options.cidades %}
                            {% for cidade in filter_options.cidades %}
                            <option value="{{ cidade }}">{{ cidade }}</option>
                            {% endfor %}
                        {% else %}
                            <option disabled>Nenhuma cidade disponível</option>
                        {% endif %}
                    </select>
                </div>
                <div>
                    <label for="filter-estado" class="block text-sm font-medium text-gray-700 mb-2">
                        Estado
                        <span class="text-xs text-gray-500">(Multi-seleção)</span>
                    </label>
                    <select id="filter-estado" multiple class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 h-24">
                        {% if filter_options and filter_options.estados %}
                            {% for estado in filter_options.estados %}
                            <option value="{{ estado }}">{{ estado }}</option>
                            {% endfor %}
                        {% else %}
                            <option disabled>Nenhum estado disponível</option>
                        {% endif %}
                    </select>
                </div>
            </div>

            <div class="flex justify-between items-center mt-6">
                <div class="text-sm text-gray-600">
                    <span class="font-medium">Dica:</span> Use Ctrl/Cmd + clique para seleções múltiplas
                </div>
                <div class="flex space-x-3">
                    <button id="clear-filters" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors">
                        Limpar Todos
                    </button>
                    <button id="apply-filters" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                        Aplicar Filtros Sobrepostos
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Funnel Visualization - Hero Section -->
        <div class="bg-white rounded-lg shadow-sm p-8 mb-8 border border-gray-200">
            <div class="flex items-start mb-6">
                <div>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-2">
                        Funil de Vendas Interativo
                    </h3>
                    <p class="text-sm text-gray-600">
                        Visualização completa da jornada do cliente
                    </p>
                </div>
            </div>
            <div id="advanced-funnel-chart" class="h-[1000px] w-full bg-gray-50 rounded-lg flex items-center justify-center"></div>
        </div>

        <!-- Funnel Metrics Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- Drop-off Analysis -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <h3 class="text-base font-medium text-gray-900 mb-3">Análise de Perda</h3>
                <div id="dropoff-analysis" class="space-y-3">
                    <!-- Dynamic content will be inserted here -->
                </div>
            </div>

            <!-- Revenue Metrics -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <h3 class="text-base font-medium text-gray-900 mb-3">Métricas de Receita</h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">MRR Total</span>
                        <span class="text-sm font-medium text-gray-900" id="mrr-total">-</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Ticket Médio</span>
                        <span class="text-sm font-medium text-gray-900" id="avg-ticket">-</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Receita por Lead</span>
                        <span class="text-sm font-medium text-gray-900" id="revenue-per-lead">-</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Receita por Oportunidade</span>
                        <span class="text-sm font-medium text-gray-900" id="revenue-per-opportunity">-</span>
                    </div>
                </div>
            </div>

            <!-- Time Metrics -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <h3 class="text-base font-medium text-gray-900 mb-3">Tempo de Conversão</h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Lead → Oportunidade</span>
                        <span class="text-sm font-medium text-gray-900" id="time-lead-opp">-</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Oportunidade → Implementação</span>
                        <span class="text-sm font-medium text-gray-900" id="time-opp-impl">-</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Implementação → Ativo</span>
                        <span class="text-sm font-medium text-gray-900" id="time-impl-active">-</span>
                    </div>
                    <div class="flex justify-between items-center border-t pt-2">
                        <span class="text-sm font-medium text-gray-900">Ciclo Total</span>
                        <span class="text-sm font-bold text-blue-600" id="time-total-cycle">-</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Funnel Insights -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Performance Summary -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <h3 class="text-base font-medium text-gray-900 mb-3">Resumo de Performance</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-blue-900">Conversão Total</div>
                            <div class="text-xs text-blue-600">Lead → Usuário Produtivo</div>
                        </div>
                        <div class="text-lg font-bold text-blue-600" id="total-conversion-rate">-</div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-green-900">Conversão Produtiva</div>
                            <div class="text-xs text-green-600">Lead → Usuário Gerando Receita</div>
                        </div>
                        <div class="text-lg font-bold text-green-600" id="productive-conversion-rate">-</div>
                    </div>
                </div>
            </div>

            <!-- Optimization Opportunities -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <h3 class="text-base font-medium text-gray-900 mb-3">Oportunidades de Otimização</h3>
                <div id="optimization-opportunities" class="space-y-3">
                    <!-- Dynamic content will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Conversion Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Funnel Stages Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <h3 class="text-base font-medium text-gray-900 mb-1">Etapas do Funil Comercial</h3>
            <p class="text-sm text-gray-500 mb-4">Distribuição de oportunidades por etapa</p>
            <div class="h-64">
                <canvas id="funnelStagesChart"></canvas>
            </div>
        </div>

        <!-- Implementation Phases Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <h3 class="text-base font-medium text-gray-900 mb-1">Fases de Implantação</h3>
            <p class="text-sm text-gray-500 mb-4">Distribuição de implantações por fase</p>
            <div class="h-64">
                <canvas id="implementationPhasesChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Conversion Insights -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Insight: Lead to Opportunity -->
        {{ insight_card(
            title="Conversão Lead → Oportunidade",
            content="<strong>" ~ (lead_to_opp_rate|float|round(1)) ~ "%</strong> dos leads são convertidos em oportunidades. A média do mercado é de 30%.",
            tip="Foque em qualificar melhor os leads para aumentar esta taxa."
        ) }}

        <!-- Insight: Opportunity to Implementation -->
        {{ insight_card(
            title="Conversão Oportunidade → Implantação",
            content="<strong>" ~ (opp_to_impl_rate|float|round(1)) ~ "%</strong> das oportunidades são convertidas em implantações. A média do mercado é de 25%.",
            tip="Analise os motivos de perda para melhorar esta conversão."
        ) }}

        <!-- Insight: Overall Conversion -->
        {{ insight_card(
            title="Conversão Geral",
            content="<strong>" ~ (conversao_geral|float|round(1)) ~ "%</strong> dos leads são convertidos em implantações. A média do mercado é de 7.5%.",
            tip="Cada 1% de melhoria nesta taxa representa um aumento significativo na receita."
        ) }}
    </div>

    <!-- Time to Conversion Analysis -->
    <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Análise de Tempo de Conversão</h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Time: Lead to Opportunity -->
            {{ stat_card(
                title="Lead → Oportunidade",
                value=avg_lead_to_opp_days|string + " dias",
                subtitle="Tempo médio de conversão",
                icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />',
                color="primary"
            ) }}

            <!-- Time: Opportunity to Implementation -->
            {{ stat_card(
                title="Oportunidade → Implantação",
                value=avg_opp_to_impl_days|string + " dias",
                subtitle="Tempo médio de conversão",
                icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />',
                color="primary"
            ) }}

            <!-- Time: Lead to Implementation -->
            {{ stat_card(
                title="Lead → Implantação",
                value=avg_lead_to_impl_days|string + " dias",
                subtitle="Tempo total do ciclo de vendas",
                icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />',
                color="primary"
            ) }}
        </div>
    </div>

    <!-- Conversion Rate Trends -->
    <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Tendências de Conversão</h2>

        <div class="grid grid-cols-1 gap-6">
            <!-- Monthly Conversion Rates -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <h3 class="text-base font-medium text-gray-900 mb-1">Taxas de Conversão Mensais</h3>
                <p class="text-sm text-gray-500 mb-4">Evolução das taxas de conversão nos últimos 12 meses</p>
                <div class="h-64">
                    <canvas id="monthlyConversionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Seasonal Analysis -->
    <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Análise Sazonal</h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Quarterly Conversion Rates -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <h3 class="text-base font-medium text-gray-900 mb-1">Conversão por Trimestre</h3>
                <p class="text-sm text-gray-500 mb-4">Taxa de conversão geral por trimestre</p>
                <div class="h-64">
                    <canvas id="quarterlyConversionChart"></canvas>
                </div>
            </div>

            <!-- Monthly Seasonal Patterns -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <h3 class="text-base font-medium text-gray-900 mb-1">Padrões Sazonais Mensais</h3>
                <p class="text-sm text-gray-500 mb-4">Taxa de conversão por mês do ano</p>
                <div class="h-64">
                    <canvas id="monthlySeasonalChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Análise de Gargalos -->
    <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Análise de Gargalos no Funil de Vendas</h2>

        <!-- Funnel Chart - Full Width and Larger -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-6">
            <div class="flex justify-between items-start mb-4">
                <div>
                    <h3 class="text-base font-medium text-gray-900 mb-1">Gargalos no Funil de Conversão</h3>
                    <p class="text-sm text-gray-500">Identificação de pontos de queda no processo de conversão</p>
                </div>
                <div class="bg-blue-50 text-blue-800 text-xs font-medium px-3 py-1 rounded-full">
                    Análise Crítica
                </div>
            </div>
            <div class="h-96">
                <canvas id="bottleneckAnalysisChart"></canvas>
            </div>
        </div>

        <!-- Insight Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Insight: Maior Gargalo -->
            <div id="biggestBottleneckInsight" class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <div class="flex items-center mb-3">
                    <div class="bg-red-100 p-2 rounded-full mr-3">
                        <svg class="w-5 h-5 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                        </svg>
                    </div>
                    <h3 class="text-base font-medium text-gray-900">Maior Gargalo</h3>
                </div>
                <p class="text-sm text-gray-700">A maior queda no funil ocorre na transição de <span id="biggestDropStage" class="font-medium">Qualificação → Proposta</span>, com <span id="biggestDropRate" class="font-medium text-red-600">65%</span> de perda.</p>
                <div class="mt-3 text-xs text-gray-500">
                    <span class="font-medium">Dica:</span> Revise o processo de qualificação e apresentação de propostas para melhorar esta conversão.
                </div>
            </div>

            <!-- Insight: Oportunidade de Melhoria -->
            <div id="improvementOpportunityInsight" class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <div class="flex items-center mb-3">
                    <div class="bg-yellow-100 p-2 rounded-full mr-3">
                        <svg class="w-5 h-5 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <h3 class="text-base font-medium text-gray-900">Oportunidade de Melhoria</h3>
                </div>
                <p class="text-sm text-gray-700">Melhorar a conversão de <span id="improvementStage" class="font-medium">Proposta → Negociação</span> em apenas <span id="improvementTarget" class="font-medium text-green-600">10%</span> resultaria em <span id="improvementResult" class="font-medium text-green-600">15</span> implantações adicionais por ano.</p>
                <div class="mt-3 text-xs text-gray-500">
                    <span class="font-medium">Dica:</span> Foque em melhorar o acompanhamento após o envio de propostas.
                </div>
            </div>

            <!-- Insight: Comparação com Mercado -->
            <div id="marketComparisonInsight" class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <div class="flex items-center mb-3">
                    <div class="bg-blue-100 p-2 rounded-full mr-3">
                        <svg class="w-5 h-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <h3 class="text-base font-medium text-gray-900">Comparação com Mercado</h3>
                </div>
                <p class="text-sm text-gray-700">Sua conversão geral de <span id="overallConversionRate" class="font-medium">{{ conversao_geral|float|round(1) }}%</span> está <span id="marketComparison" class="font-medium text-blue-600">acima da média</span> do mercado educacional (7.5%).</p>
                <div class="mt-3 text-xs text-gray-500">
                    <span class="font-medium">Dica:</span> Continue monitorando os gargalos para manter ou melhorar este desempenho.
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Conversão por Estado -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <h3 class="text-base font-medium text-gray-900 mb-1">Conversão por Estado</h3>
                <p class="text-sm text-gray-500 mb-4">Taxa de conversão de leads para implantações por estado</p>
                <div class="h-64">
                    <canvas id="conversionByStateChart"></canvas>
                </div>
            </div>

            <!-- Estágios do Funil -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <h3 class="text-base font-medium text-gray-900 mb-1">Estágios do Funil</h3>
                <p class="text-sm text-gray-500 mb-4">Quantidade de leads em cada estágio do funil</p>
                <div class="h-64">
                    <canvas id="funnelStagesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Conversion Velocity Analysis -->
    <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Análise de Velocidade de Conversão</h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Velocity Distribution -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <h3 class="text-base font-medium text-gray-900 mb-1">Distribuição de Tempo de Conversão</h3>
                <p class="text-sm text-gray-500 mb-4">Quantidade de leads por faixa de tempo de conversão</p>
                <div class="h-64">
                    <canvas id="velocityDistributionChart"></canvas>
                </div>
            </div>

            <!-- Velocity by University -->
            <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                <h3 class="text-base font-medium text-gray-900 mb-1">Velocidade por Universidade</h3>
                <p class="text-sm text-gray-500 mb-4">Tempo médio de conversão por universidade (em dias)</p>
                <div class="h-64">
                    <canvas id="velocityByUniversityChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Historical Analysis -->
    <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Análise Histórica</h2>

        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <h3 class="text-base font-medium text-gray-900 mb-1">Evolução das Fases de Implantação</h3>
            <p class="text-sm text-gray-500 mb-4">Distribuição histórica de implantações por fase</p>
            <div class="h-64">
                <canvas id="implementationHistoricalChart"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Global variables for multi-filter system
    let selectedFilters = [];
    let maxFilters = 3;
    let filterColors = ['#3B82F6', '#10B981', '#F59E0B']; // Blue, Green, Amber

    // Wait for D3.js to load
    function waitForD3(callback) {
        if (typeof d3 !== 'undefined') {
            callback();
        } else {
            setTimeout(() => waitForD3(callback), 100);
        }
    }

    // Simple D3.js Funnel Chart
    function createD3Funnel(containerId, data) {
        if (!data || !data.stages || data.stages.length === 0) {
            document.getElementById(containerId).innerHTML =
                '<div class="flex items-center justify-center h-full"><p class="text-gray-500 text-sm">Dados do funil não disponíveis</p></div>';
            return;
        }

        // Clear container
        d3.select(`#${containerId}`).selectAll("*").remove();

        // Use full container dimensions - Increased size
        const containerElement = document.getElementById(containerId);
        const containerRect = containerElement.getBoundingClientRect();
        const margin = { top: 40, right: 40, bottom: 40, left: 40 };
        const width = containerRect.width - margin.left - margin.right;
        const height = 960 - margin.top - margin.bottom;

        // Create SVG with app styling
        const svg = d3.select(`#${containerId}`)
            .append("svg")
            .attr("width", width + margin.left + margin.right)
            .attr("height", height + margin.top + margin.bottom)
            .style("background", "#f9fafb")
            .style("border-radius", "8px")
            .style("border", "1px solid #e5e7eb");

        // Add definitions for gradients and filters
        const defs = svg.append("defs");

        // Create gradient definitions with light blue tones
        const gradients = [
            { id: "gradient-0", colors: ["#DBEAFE", "#BFDBFE"] },
            { id: "gradient-1", colors: ["#BFDBFE", "#93C5FD"] },
            { id: "gradient-2", colors: ["#93C5FD", "#60A5FA"] },
            { id: "gradient-3", colors: ["#60A5FA", "#3B82F6"] },
            { id: "gradient-4", colors: ["#3B82F6", "#2563EB"] }
        ];

        gradients.forEach(grad => {
            const gradient = defs.append("linearGradient")
                .attr("id", grad.id)
                .attr("x1", "0%")
                .attr("y1", "0%")
                .attr("x2", "0%")
                .attr("y2", "100%");

            gradient.append("stop")
                .attr("offset", "0%")
                .attr("stop-color", grad.colors[0])
                .attr("stop-opacity", 0.9);

            gradient.append("stop")
                .attr("offset", "100%")
                .attr("stop-color", grad.colors[1])
                .attr("stop-opacity", 1);
        });

        // Add premium visual effects and filters
        const dropShadowFilter = defs.append("filter")
            .attr("id", "premium-shadow")
            .attr("x", "-50%")
            .attr("y", "-50%")
            .attr("width", "200%")
            .attr("height", "200%");

        dropShadowFilter.append("feGaussianBlur")
            .attr("in", "SourceAlpha")
            .attr("stdDeviation", 8)
            .attr("result", "blur");

        dropShadowFilter.append("feOffset")
            .attr("in", "blur")
            .attr("dx", 0)
            .attr("dy", 12)
            .attr("result", "offsetBlur");

        dropShadowFilter.append("feFlood")
            .attr("flood-color", "rgba(0,0,0,0.15)")
            .attr("result", "shadowColor");

        dropShadowFilter.append("feComposite")
            .attr("in", "shadowColor")
            .attr("in2", "offsetBlur")
            .attr("operator", "in")
            .attr("result", "shadow");

        const shadowMerge = dropShadowFilter.append("feMerge");
        shadowMerge.append("feMergeNode").attr("in", "shadow");
        shadowMerge.append("feMergeNode").attr("in", "SourceGraphic");

        // Add inner shadow filter for depth
        const innerShadowFilter = defs.append("filter")
            .attr("id", "inner-shadow")
            .attr("x", "-50%")
            .attr("y", "-50%")
            .attr("width", "200%")
            .attr("height", "200%");

        innerShadowFilter.append("feGaussianBlur")
            .attr("in", "SourceGraphic")
            .attr("stdDeviation", 3)
            .attr("result", "blur");

        innerShadowFilter.append("feOffset")
            .attr("in", "blur")
            .attr("dx", 0)
            .attr("dy", -2)
            .attr("result", "offset");

        // Add soft glow effect
        const softGlowFilter = defs.append("filter")
            .attr("id", "soft-glow")
            .attr("x", "-50%")
            .attr("y", "-50%")
            .attr("width", "200%")
            .attr("height", "200%");

        softGlowFilter.append("feGaussianBlur")
            .attr("stdDeviation", 6)
            .attr("result", "coloredBlur");

        const glowMerge = softGlowFilter.append("feMerge");
        glowMerge.append("feMergeNode").attr("in", "coloredBlur");
        glowMerge.append("feMergeNode").attr("in", "SourceGraphic");

        // Add soft edge blur filter
        const softEdgeFilter = defs.append("filter")
            .attr("id", "soft-edge")
            .attr("x", "-10%")
            .attr("y", "-10%")
            .attr("width", "120%")
            .attr("height", "120%");

        softEdgeFilter.append("feGaussianBlur")
            .attr("stdDeviation", 1.5)
            .attr("result", "softBlur");

        // Add premium texture pattern
        const premiumTexture = defs.append("pattern")
            .attr("id", "premium-texture")
            .attr("patternUnits", "userSpaceOnUse")
            .attr("width", 8)
            .attr("height", 8);

        premiumTexture.append("rect")
            .attr("width", 8)
            .attr("height", 8)
            .attr("fill", "rgba(255,255,255,0.03)");

        premiumTexture.append("circle")
            .attr("cx", 4)
            .attr("cy", 4)
            .attr("r", 1)
            .attr("fill", "rgba(255,255,255,0.08)");

        const g = svg.append("g")
            .attr("transform", `translate(${margin.left},${margin.top})`);

        const stages = data.stages;
        const maxStageHeight = height - 80; // Usar mais espaço para o funil

        // Create modern tooltip
        const tooltip = d3.select("body").append("div")
            .attr("class", "d3-tooltip")
            .style("position", "absolute")
            .style("background", "linear-gradient(135deg, rgba(30, 41, 59, 0.95), rgba(51, 65, 85, 0.95))")
            .style("color", "white")
            .style("padding", "12px 16px")
            .style("border-radius", "12px")
            .style("font-size", "14px")
            .style("font-weight", "500")
            .style("pointer-events", "none")
            .style("opacity", 0)
            .style("z-index", 1000)
            .style("box-shadow", "0 10px 25px rgba(0,0,0,0.2)")
            .style("backdrop-filter", "blur(10px)")
            .style("border", "1px solid rgba(255,255,255,0.1)");

        // Create funnel segments in trapezoid shape - Larger funnel
        const funnelCenterX = width * 0.3; // Movido mais para esquerda
        const funnelTopWidth = width * 0.5; // Aumentado para funil maior
        const funnelBottomWidth = width * 0.08; // Base mais estreita
        const segmentHeight = maxStageHeight / stages.length;

        stages.forEach((stage, i) => {
            // Calculate trapezoid dimensions for funnel effect
            const topRatio = (stages.length - i) / stages.length;
            const bottomRatio = (stages.length - i - 1) / stages.length;

            const topWidth = funnelBottomWidth + (funnelTopWidth - funnelBottomWidth) * topRatio;
            const bottomWidth = funnelBottomWidth + (funnelTopWidth - funnelBottomWidth) * bottomRatio;

            const y = i * segmentHeight;
            const topLeft = funnelCenterX - topWidth / 2;
            const topRight = funnelCenterX + topWidth / 2;
            const bottomLeft = funnelCenterX - bottomWidth / 2;
            const bottomRight = funnelCenterX + bottomWidth / 2;

            // Create segment group
            const segment = g.append("g")
                .attr("class", `funnel-segment segment-${i}`)
                .style("cursor", "pointer");

            // Create smooth rounded funnel path with organic curves
            function createSmoothFunnelPath(topLeft, topRight, bottomLeft, bottomRight, y, segmentHeight) {
                const cornerRadius = 16;
                const curveIntensity = 0.3;

                const path = d3.path();

                // Start from top-left with rounded corner
                path.moveTo(topLeft + cornerRadius, y);

                // Top edge with gentle curve
                const topMidX = (topLeft + topRight) / 2;
                path.quadraticCurveTo(topMidX, y - 2, topRight - cornerRadius, y);
                path.quadraticCurveTo(topRight, y, topRight, y + cornerRadius);

                // Right edge with smooth organic curve
                const rightMidY = y + segmentHeight / 2;
                const rightControlX = topRight + (bottomRight - topRight) * curveIntensity;
                path.quadraticCurveTo(rightControlX, rightMidY, bottomRight, y + segmentHeight - cornerRadius);
                path.quadraticCurveTo(bottomRight, y + segmentHeight, bottomRight - cornerRadius, y + segmentHeight);

                // Bottom edge with gentle curve
                const bottomMidX = (bottomLeft + bottomRight) / 2;
                path.quadraticCurveTo(bottomMidX, y + segmentHeight + 2, bottomLeft + cornerRadius, y + segmentHeight);
                path.quadraticCurveTo(bottomLeft, y + segmentHeight, bottomLeft, y + segmentHeight - cornerRadius);

                // Left edge with smooth organic curve
                const leftMidY = y + segmentHeight / 2;
                const leftControlX = bottomLeft - (topLeft - bottomLeft) * curveIntensity;
                path.quadraticCurveTo(leftControlX, leftMidY, topLeft, y + cornerRadius);
                path.quadraticCurveTo(topLeft, y, topLeft + cornerRadius, y);

                path.closePath();
                return path.toString();
            }

            const smoothPathData = createSmoothFunnelPath(topLeft, topRight, bottomLeft, bottomRight, y, segmentHeight);

            // Main funnel segment with premium styling - no harsh borders
            segment.append("path")
                .attr("d", smoothPathData)
                .attr("fill", `url(#gradient-${i})`)
                .attr("stroke", "rgba(255,255,255,0.1)")
                .attr("stroke-width", 0.5)
                .style("filter", "url(#premium-shadow)")
                .style("transition", "all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)")
                .style("cursor", "pointer");

            // Add premium texture overlay with soft edges
            segment.append("path")
                .attr("d", smoothPathData)
                .attr("fill", "url(#premium-texture)")
                .attr("opacity", 0.4)
                .style("filter", "url(#soft-edge)")
                .style("pointer-events", "none");

            // Add inner glow for depth and volume
            const innerGlowPath = createSmoothFunnelPath(topLeft + 6, topRight - 6, bottomLeft + 4, bottomRight - 4, y + 3, segmentHeight - 6);

            segment.append("path")
                .attr("d", innerGlowPath)
                .attr("fill", "rgba(255,255,255,0.25)")
                .attr("opacity", 0.6)
                .style("filter", "url(#soft-edge)")
                .style("pointer-events", "none");

            // Add subtle inner shadow for 3D volume effect
            const innerShadowPath = createSmoothFunnelPath(topLeft + 3, topRight - 3, bottomLeft + 2, bottomRight - 2, y + 1, segmentHeight - 2);

            segment.append("path")
                .attr("d", innerShadowPath)
                .attr("fill", "none")
                .attr("stroke", "rgba(0,0,0,0.08)")
                .attr("stroke-width", 1)
                .style("filter", "url(#inner-shadow)")
                .style("pointer-events", "none");

            // Value label with enhanced contrast and styling
            segment.append("text")
                .attr("x", funnelCenterX)
                .attr("y", y + segmentHeight / 2 - 8)
                .attr("text-anchor", "middle")
                .attr("dominant-baseline", "middle")
                .attr("fill", "#FFFFFF")
                .attr("font-weight", "900")
                .attr("font-size", "24px")
                .style("text-shadow", "2px 2px 8px rgba(0,0,0,0.8), 0 0 15px rgba(0,0,0,0.5)")
                .style("font-family", "'Inter', -apple-system, BlinkMacSystemFont, sans-serif")
                .style("letter-spacing", "0.5px")
                .text(formatValue(stage.value))
                .style("opacity", 0)
                .transition()
                .duration(800)
                .delay(i * 200)
                .style("opacity", 1);

            // Percentage label with enhanced contrast and styling
            segment.append("text")
                .attr("x", funnelCenterX)
                .attr("y", y + segmentHeight / 2 + 18)
                .attr("text-anchor", "middle")
                .attr("dominant-baseline", "middle")
                .attr("fill", "#F8FAFC")
                .attr("font-size", "18px")
                .attr("font-weight", "800")
                .style("text-shadow", "1px 1px 6px rgba(0,0,0,0.7), 0 0 10px rgba(0,0,0,0.4)")
                .style("font-family", "'Inter', -apple-system, BlinkMacSystemFont, sans-serif")
                .text(`${stage.percentage}%`)
                .style("opacity", 0)
                .transition()
                .duration(800)
                .delay(i * 200 + 100)
                .style("opacity", 1);

            // Enhanced stage labels with better positioning and wider container
            const labelX = funnelCenterX - (topWidth / 2) - 60;
            const labelAnchor = "end";

            // Label background for better readability - wider to accommodate longer text
            const labelBg = segment.append("rect")
                .attr("x", labelX - 160)
                .attr("y", y + segmentHeight / 2 - 20)
                .attr("width", 155)
                .attr("height", 35)
                .attr("fill", "rgba(255,255,255,0.95)")
                .attr("stroke", "#E5E7EB")
                .attr("stroke-width", 1)
                .attr("rx", 6)
                .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))")
                .style("opacity", 0)
                .transition()
                .duration(600)
                .delay(i * 150)
                .style("opacity", 1);

            // Stage name with enhanced styling
            segment.append("text")
                .attr("x", labelX - 8)
                .attr("y", y + segmentHeight / 2 - 5)
                .attr("text-anchor", labelAnchor)
                .attr("fill", "#1F2937")
                .attr("font-weight", "700")
                .attr("font-size", "14px")
                .style("font-family", "'Inter', -apple-system, BlinkMacSystemFont, sans-serif")
                .text(stage.name)
                .style("opacity", 0)
                .transition()
                .duration(600)
                .delay(i * 150 + 100)
                .style("opacity", 1);

            // Description with enhanced styling - better text wrapping
            const description = stage.description;
            const maxLength = 25; // Maximum characters per line

            if (description.length > maxLength) {
                // Split long descriptions into two lines
                const words = description.split(' ');
                let line1 = '';
                let line2 = '';
                let currentLine = 1;

                words.forEach(word => {
                    if (currentLine === 1 && (line1 + word).length <= maxLength) {
                        line1 += (line1 ? ' ' : '') + word;
                    } else {
                        currentLine = 2;
                        line2 += (line2 ? ' ' : '') + word;
                    }
                });

                // First line
                segment.append("text")
                    .attr("x", labelX - 8)
                    .attr("y", y + segmentHeight / 2 + 8)
                    .attr("text-anchor", labelAnchor)
                    .attr("fill", "#6B7280")
                    .attr("font-size", "10px")
                    .attr("font-weight", "500")
                    .style("font-family", "'Inter', -apple-system, BlinkMacSystemFont, sans-serif")
                    .text(line1)
                    .style("opacity", 0)
                    .transition()
                    .duration(600)
                    .delay(i * 150 + 200)
                    .style("opacity", 1);

                // Second line if needed
                if (line2) {
                    segment.append("text")
                        .attr("x", labelX - 8)
                        .attr("y", y + segmentHeight / 2 + 18)
                        .attr("text-anchor", labelAnchor)
                        .attr("fill", "#6B7280")
                        .attr("font-size", "10px")
                        .attr("font-weight", "500")
                        .style("font-family", "'Inter', -apple-system, BlinkMacSystemFont, sans-serif")
                        .text(line2)
                        .style("opacity", 0)
                        .transition()
                        .duration(600)
                        .delay(i * 150 + 250)
                        .style("opacity", 1);
                }
            } else {
                // Single line for short descriptions
                segment.append("text")
                    .attr("x", labelX - 8)
                    .attr("y", y + segmentHeight / 2 + 10)
                    .attr("text-anchor", labelAnchor)
                    .attr("fill", "#6B7280")
                    .attr("font-size", "11px")
                    .attr("font-weight", "500")
                    .style("font-family", "'Inter', -apple-system, BlinkMacSystemFont, sans-serif")
                    .text(description)
                    .style("opacity", 0)
                    .transition()
                    .duration(600)
                    .delay(i * 150 + 200)
                    .style("opacity", 1);
            }

            // Enhanced connection line with animation
            segment.append("line")
                .attr("x1", funnelCenterX - (topWidth / 2))
                .attr("y1", y + segmentHeight / 2)
                .attr("x2", labelX - 160)
                .attr("y2", y + segmentHeight / 2)
                .attr("stroke", "#3B82F6")
                .attr("stroke-width", 2)
                .attr("stroke-dasharray", "4,4")
                .style("opacity", "0.6")
                .style("stroke-dashoffset", 20)
                .transition()
                .duration(1000)
                .delay(i * 150 + 300)
                .style("stroke-dashoffset", 0);

            // Removed expansion indicator buttons - no longer needed

            // Add premium hover effects with organic animations
            segment
                .on("mouseover", function(event) {
                    // Enhanced hover with soft glow and gentle scale
                    d3.select(this).selectAll("path")
                        .style("filter", "url(#soft-glow)")
                        .style("transform", "scale(1.03)")
                        .style("transform-origin", "center")
                        .transition()
                        .duration(300)
                        .ease(d3.easeCubicOut);

                    // Enhance stroke with soft glow
                    d3.select(this).select("path:first-child")
                        .attr("stroke", "rgba(255,255,255,0.3)")
                        .attr("stroke-width", 1);

                    tooltip
                        .style("opacity", 1)
                        .html(`
                            <div style="font-weight: 700; font-size: 16px; margin-bottom: 8px; color: #F1F5F9;">
                                ${stage.name}
                            </div>
                            <div style="margin-bottom: 4px;">
                                <span style="color: #94A3B8;">Valor:</span>
                                <span style="font-weight: 600; color: #E2E8F0;">${formatValue(stage.value)}</span>
                            </div>
                            <div style="margin-bottom: 4px;">
                                <span style="color: #94A3B8;">Conversão:</span>
                                <span style="font-weight: 600; color: #34D399;">${stage.percentage}%</span>
                            </div>
                            <div style="color: #CBD5E1; font-size: 12px; margin-top: 6px; font-style: italic;">
                                ${stage.description}
                            </div>
                        `)
                        .style("left", (event.pageX + 15) + "px")
                        .style("top", (event.pageY - 15) + "px");
                })
                .on("mouseout", function() {
                    // Smooth return to original state
                    d3.select(this).selectAll("path")
                        .style("filter", "url(#premium-shadow)")
                        .style("transform", "scale(1)")
                        .style("transform-origin", "center")
                        .transition()
                        .duration(400)
                        .ease(d3.easeCubicOut);

                    // Reset stroke to subtle
                    d3.select(this).select("path:first-child")
                        .attr("stroke", "rgba(255,255,255,0.1)")
                        .attr("stroke-width", 0.5);

                    tooltip.style("opacity", 0);
                })
                .on("click", function(event) {
                    console.log('Segment clicked:', stage.name);
                    if (stage.name === "Oportunidades" || stage.name === "Implementações") {
                        event.stopPropagation();
                        console.log('Opening modal for:', stage.name);
                        openCompactFunnelModal(stage.name, i);
                    } else {
                        highlightStage(i, stages);
                    }
                });

            // Add subtle conversion rate label between funnel segments
            if (i < stages.length - 1) {
                const nextStage = stages[i + 1];
                const conversionRate = ((nextStage.value / stage.value) * 100).toFixed(1);

                // Subtle conversion rate badge
                g.append("rect")
                    .attr("x", funnelCenterX - 20)
                    .attr("y", y + segmentHeight - 12)
                    .attr("width", 40)
                    .attr("height", 16)
                    .attr("fill", "rgba(255,255,255,0.9)")
                    .attr("stroke", "#E5E7EB")
                    .attr("stroke-width", 1)
                    .attr("rx", 8)
                    .style("opacity", "0.8");

                g.append("text")
                    .attr("x", funnelCenterX)
                    .attr("y", y + segmentHeight - 3)
                    .attr("text-anchor", "middle")
                    .attr("fill", "#3B82F6")
                    .attr("font-size", "9px")
                    .attr("font-weight", "600")
                    .text(`${conversionRate}%`);
            }
        });

        // Add info cards on the right side
        addFunnelInfoCards(g, width, height, data);

        // Funnel shape complete
    }

    // Function to open compact funnel modal
    function openCompactFunnelModal(stageName, stageIndex) {
        console.log(`Opening compact modal for ${stageName} stage`);

        // Define subfases based on business rules
        let subfases = [];

        if (stageName === "Oportunidades") {
            subfases = [
                { name: "Qualificação", description: "Verificação inicial", value: 850, percentage: "42.5%" },
                { name: "Apresentação", description: "Apresentação do produto", value: 650, percentage: "32.5%" },
                { name: "Proposta", description: "Proposta comercial", value: 400, percentage: "20.0%" },
                { name: "Negociação", description: "Discussão de termos", value: 200, percentage: "10.0%" },
                { name: "Fechamento", description: "Finalização da venda", value: 100, percentage: "5.0%" }
            ];
        } else if (stageName === "Implementações") {
            subfases = [
                { name: "Aguardando Início", description: "Aguardando início", value: 300, percentage: "31.4%" },
                { name: "Em Andamento", description: "Em progresso", value: 250, percentage: "26.2%" },
                { name: "Aguardando Docs", description: "Pendente documentos", value: 150, percentage: "15.7%" },
                { name: "Aguardando Aprovação", description: "Aprovação interna", value: 120, percentage: "12.6%" },
                { name: "Homologação", description: "Processo homologação", value: 80, percentage: "8.4%" },
                { name: "Liberação", description: "Liberação final", value: 55, percentage: "5.8%" }
            ];
        }

        // Create compact modal
        createCompactFunnelModal(stageName, subfases, stageIndex);
    }

    // Function to expand stage and show subfases (large modal)
    function expandStage(stageName, stageIndex) {
        console.log(`Expanding ${stageName} stage`);

        // Define subfases based on business rules
        let subfases = [];

        if (stageName === "Oportunidades") {
            subfases = [
                { name: "Qualificação", description: "Verificação inicial do interesse", value: 850, percentage: "42.5%" },
                { name: "Apresentação", description: "Apresentação detalhada do produto", value: 650, percentage: "32.5%" },
                { name: "Proposta", description: "Envio de proposta comercial", value: 400, percentage: "20.0%" },
                { name: "Negociação", description: "Discussão de termos", value: 200, percentage: "10.0%" },
                { name: "Fechamento", description: "Finalização da venda", value: 100, percentage: "5.0%" }
            ];
        } else if (stageName === "Implementações") {
            subfases = [
                { name: "Aguardando Início", description: "Aguardando início da implementação", value: 300, percentage: "31.4%" },
                { name: "Em Andamento", description: "Implementação em progresso", value: 250, percentage: "26.2%" },
                { name: "Aguardando Documentação", description: "Pendente de documentos", value: 150, percentage: "15.7%" },
                { name: "Aguardando Aprovação", description: "Aguardando aprovação interna", value: 120, percentage: "12.6%" },
                { name: "Aguardando Homologação", description: "Processo de homologação", value: 80, percentage: "8.4%" },
                { name: "Aguardando Liberação", description: "Liberação final", value: 55, percentage: "5.8%" }
            ];
        }

        // Create expanded funnel modal
        createExpandedFunnelModal(stageName, subfases, stageIndex);
    }

    function createCompactFunnelModal(stageName, subfases, stageIndex) {
        console.log('Creating compact modal for:', stageName);

        // Remove existing modal if any
        d3.select("#compact-funnel-modal").remove();

        // Create compact modal overlay using vanilla JS for better compatibility
        const modalDiv = document.createElement('div');
        modalDiv.id = 'compact-funnel-modal';
        modalDiv.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.6);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.2s ease;
        `;

        document.body.appendChild(modalDiv);

        // Trigger fade in
        setTimeout(() => {
            modalDiv.style.opacity = '1';
        }, 10);

        // Create modal content container - increased size
        const contentDiv = document.createElement('div');
        contentDiv.style.cssText = `
            background: white;
            border-radius: 16px;
            padding: 32px;
            width: 720px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 25px 50px rgba(0,0,0,0.25);
            transform: scale(0.95);
            transition: transform 0.3s ease;
        `;

        modalDiv.appendChild(contentDiv);

        // Trigger scale animation
        setTimeout(() => {
            contentDiv.style.transform = 'scale(1)';
        }, 50);

        // Create header
        const headerDiv = document.createElement('div');
        headerDiv.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #E5E7EB;
        `;

        const titleH3 = document.createElement('h3');
        titleH3.style.cssText = `
            font-size: 18px;
            font-weight: 700;
            color: #1F2937;
            margin: 0;
        `;
        titleH3.textContent = `${stageName} - Subfases`;

        const closeBtn = document.createElement('button');
        closeBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6B7280;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s;
        `;
        closeBtn.textContent = '×';
        closeBtn.onmouseover = () => {
            closeBtn.style.background = '#F3F4F6';
            closeBtn.style.color = '#1F2937';
        };
        closeBtn.onmouseout = () => {
            closeBtn.style.background = 'none';
            closeBtn.style.color = '#6B7280';
        };
        closeBtn.onclick = () => {
            modalDiv.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(modalDiv);
            }, 200);
        };

        headerDiv.appendChild(titleH3);
        headerDiv.appendChild(closeBtn);
        contentDiv.appendChild(headerDiv);

        // Create SVG container - larger size
        const svgContainer = document.createElement('div');
        svgContainer.style.cssText = `
            width: 660px;
            height: 450px;
            border-radius: 12px;
            background: #F8FAFC;
            margin: 0 auto;
        `;
        contentDiv.appendChild(svgContainer);

        // Create SVG using D3 - larger size
        const compactSvg = d3.select(svgContainer)
            .append("svg")
            .attr("width", 660)
            .attr("height", 450)
            .style("border-radius", "12px")
            .style("background", "#F8FAFC");

        // Draw compact funnel
        drawCompactFunnel(compactSvg, subfases, stageName);

        // Create expand button
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
            text-align: center;
            margin-top: 16px;
        `;

        const expandBtn = document.createElement('button');
        expandBtn.style.cssText = `
            background: #3B82F6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        `;
        expandBtn.textContent = 'Ver Detalhes Completos';
        expandBtn.onmouseover = () => {
            expandBtn.style.background = '#2563EB';
        };
        expandBtn.onmouseout = () => {
            expandBtn.style.background = '#3B82F6';
        };
        expandBtn.onclick = () => {
            modalDiv.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(modalDiv);
                expandStage(stageName, stageIndex);
            }, 200);
        };

        buttonContainer.appendChild(expandBtn);
        contentDiv.appendChild(buttonContainer);

        // Close modal on overlay click
        modalDiv.onclick = (event) => {
            if (event.target === modalDiv) {
                modalDiv.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(modalDiv);
                }, 200);
            }
        };
    }

    function drawCompactFunnel(svg, subfases, stageName) {
        const margin = { top: 30, right: 30, bottom: 30, left: 30 };
        const width = 660 - margin.left - margin.right;
        const height = 450 - margin.top - margin.bottom;

        const g = svg.append("g")
            .attr("transform", `translate(${margin.left},${margin.top})`);

        // Create premium gradients using same colors as main funnel
        const defs = svg.append("defs");

        // Use same gradient system as main funnel
        const gradientConfigs = [
            { id: 'modal-gradient-0', colors: ['#3B82F6', '#1E40AF'] }, // Blue
            { id: 'modal-gradient-1', colors: ['#10B981', '#047857'] }, // Green
            { id: 'modal-gradient-2', colors: ['#F59E0B', '#D97706'] }, // Amber
            { id: 'modal-gradient-3', colors: ['#EF4444', '#DC2626'] }, // Red
            { id: 'modal-gradient-4', colors: ['#8B5CF6', '#7C3AED'] }, // Purple
            { id: 'modal-gradient-5', colors: ['#06B6D4', '#0891B2'] }  // Cyan
        ];

        gradientConfigs.forEach((grad, index) => {
            if (index < subfases.length) {
                const gradient = defs.append("linearGradient")
                    .attr("id", grad.id)
                    .attr("x1", "0%")
                    .attr("y1", "0%")
                    .attr("x2", "0%")
                    .attr("y2", "100%");

                gradient.append("stop")
                    .attr("offset", "0%")
                    .attr("stop-color", grad.colors[0])
                    .attr("stop-opacity", 0.95);

                gradient.append("stop")
                    .attr("offset", "100%")
                    .attr("stop-color", grad.colors[1])
                    .attr("stop-opacity", 0.85);
            }
        });

        // Add premium filters for modal
        const modalShadowFilter = defs.append("filter")
            .attr("id", "modal-shadow")
            .attr("x", "-50%")
            .attr("y", "-50%")
            .attr("width", "200%")
            .attr("height", "200%");

        modalShadowFilter.append("feGaussianBlur")
            .attr("in", "SourceAlpha")
            .attr("stdDeviation", 6)
            .attr("result", "blur");

        modalShadowFilter.append("feOffset")
            .attr("in", "blur")
            .attr("dx", 0)
            .attr("dy", 8)
            .attr("result", "offsetBlur");

        const modalMerge = modalShadowFilter.append("feMerge");
        modalMerge.append("feMergeNode").attr("in", "offsetBlur");
        modalMerge.append("feMergeNode").attr("in", "SourceGraphic");

        // Draw compact funnel
        const funnelCenterX = width / 2;
        const funnelTopWidth = width * 0.7;
        const funnelBottomWidth = width * 0.2;
        const segmentHeight = (height - 40) / subfases.length;

        // Function to create smooth compact funnel paths
        function createCompactSmoothPath(topLeft, topRight, bottomLeft, bottomRight, y, segmentHeight) {
            const cornerRadius = 8; // Smaller radius for compact view
            const curveIntensity = 0.2;

            const path = d3.path();

            // Start from top-left with rounded corner
            path.moveTo(topLeft + cornerRadius, y);

            // Top edge with gentle curve
            const topMidX = (topLeft + topRight) / 2;
            path.quadraticCurveTo(topMidX, y - 1, topRight - cornerRadius, y);
            path.quadraticCurveTo(topRight, y, topRight, y + cornerRadius);

            // Right edge with smooth curve
            const rightMidY = y + segmentHeight / 2;
            const rightControlX = topRight + (bottomRight - topRight) * curveIntensity;
            path.quadraticCurveTo(rightControlX, rightMidY, bottomRight, y + segmentHeight - cornerRadius);
            path.quadraticCurveTo(bottomRight, y + segmentHeight, bottomRight - cornerRadius, y + segmentHeight);

            // Bottom edge with gentle curve
            const bottomMidX = (bottomLeft + bottomRight) / 2;
            path.quadraticCurveTo(bottomMidX, y + segmentHeight + 1, bottomLeft + cornerRadius, y + segmentHeight);
            path.quadraticCurveTo(bottomLeft, y + segmentHeight, bottomLeft, y + segmentHeight - cornerRadius);

            // Left edge with smooth curve
            const leftMidY = y + segmentHeight / 2;
            const leftControlX = bottomLeft - (topLeft - bottomLeft) * curveIntensity;
            path.quadraticCurveTo(leftControlX, leftMidY, topLeft, y + cornerRadius);
            path.quadraticCurveTo(topLeft, y, topLeft + cornerRadius, y);

            path.closePath();
            return path.toString();
        }

        subfases.forEach((subfase, i) => {
            const topRatio = (subfases.length - i) / subfases.length;
            const bottomRatio = (subfases.length - i - 1) / subfases.length;

            const topWidth = funnelBottomWidth + (funnelTopWidth - funnelBottomWidth) * topRatio;
            const bottomWidth = funnelBottomWidth + (funnelTopWidth - funnelBottomWidth) * bottomRatio;

            const y = i * segmentHeight;
            const topLeft = funnelCenterX - topWidth / 2;
            const topRight = funnelCenterX + topWidth / 2;
            const bottomLeft = funnelCenterX - bottomWidth / 2;
            const bottomRight = funnelCenterX + bottomWidth / 2;

            const smoothPathData = createCompactSmoothPath(topLeft, topRight, bottomLeft, bottomRight, y, segmentHeight);

            const segment = g.append("g").attr("class", `compact-segment-${i}`);

            // Main segment with premium styling using consistent colors
            segment.append("path")
                .attr("d", smoothPathData)
                .attr("fill", `url(#modal-gradient-${i})`)
                .attr("stroke", "rgba(255,255,255,0.05)")
                .attr("stroke-width", 0.5)
                .style("filter", "url(#modal-shadow)")
                .style("opacity", 0)
                .transition()
                .duration(600)
                .delay(i * 120)
                .ease(d3.easeCubicOut)
                .style("opacity", 1);

            // Add inner glow for modal segments
            const innerGlowPath = createCompactSmoothPath(topLeft + 4, topRight - 4, bottomLeft + 3, bottomRight - 3, y + 3, segmentHeight - 6);

            segment.append("path")
                .attr("d", innerGlowPath)
                .attr("fill", "rgba(255,255,255,0.25)")
                .attr("opacity", 0.6)
                .style("pointer-events", "none");

            // Value with enhanced contrast
            segment.append("text")
                .attr("x", funnelCenterX)
                .attr("y", y + segmentHeight / 2 - 5)
                .attr("text-anchor", "middle")
                .attr("dominant-baseline", "middle")
                .attr("fill", "#FFFFFF")
                .attr("font-weight", "900")
                .attr("font-size", "18px")
                .style("text-shadow", "2px 2px 6px rgba(0,0,0,0.8), 0 0 12px rgba(0,0,0,0.5)")
                .style("font-family", "'Inter', -apple-system, BlinkMacSystemFont, sans-serif")
                .text(formatValue(subfase.value))
                .style("opacity", 0)
                .transition()
                .duration(500)
                .delay(i * 120 + 200)
                .style("opacity", 1);

            // Percentage with enhanced contrast
            segment.append("text")
                .attr("x", funnelCenterX)
                .attr("y", y + segmentHeight / 2 + 15)
                .attr("text-anchor", "middle")
                .attr("dominant-baseline", "middle")
                .attr("fill", "#F8FAFC")
                .attr("font-weight", "800")
                .attr("font-size", "14px")
                .style("text-shadow", "1px 1px 4px rgba(0,0,0,0.7), 0 0 8px rgba(0,0,0,0.4)")
                .style("font-family", "'Inter', -apple-system, BlinkMacSystemFont, sans-serif")
                .text(`${subfase.percentage}`)
                .style("opacity", 0)
                .transition()
                .duration(500)
                .delay(i * 120 + 300)
                .style("opacity", 1);

            // Stage name (left side) with better contrast
            segment.append("text")
                .attr("x", funnelCenterX - topWidth / 2 - 15)
                .attr("y", y + segmentHeight / 2)
                .attr("text-anchor", "end")
                .attr("fill", "#1F2937")
                .attr("font-weight", "700")
                .attr("font-size", "14px")
                .style("font-family", "'Inter', -apple-system, BlinkMacSystemFont, sans-serif")
                .text(subfase.name)
                .style("opacity", 0)
                .transition()
                .duration(500)
                .delay(i * 120 + 400)
                .style("opacity", 1);
        });
    }

    function createExpandedFunnelModal(stageName, subfases, stageIndex) {
        // Remove existing modal if any
        d3.select("#expanded-funnel-modal").remove();

        // Create modal overlay
        const modal = d3.select("body")
            .append("div")
            .attr("id", "expanded-funnel-modal")
            .style("position", "fixed")
            .style("top", "0")
            .style("left", "0")
            .style("width", "100%")
            .style("height", "100%")
            .style("background", "rgba(0,0,0,0.8)")
            .style("z-index", "1000")
            .style("display", "flex")
            .style("align-items", "center")
            .style("justify-content", "center")
            .style("opacity", 0)
            .transition()
            .duration(300)
            .style("opacity", 1);

        // Create modal content
        const modalContent = modal.append("div")
            .style("background", "white")
            .style("border-radius", "16px")
            .style("padding", "32px")
            .style("max-width", "900px")
            .style("max-height", "80vh")
            .style("overflow-y", "auto")
            .style("box-shadow", "0 25px 50px rgba(0,0,0,0.25)")
            .style("transform", "scale(0.9)")
            .transition()
            .duration(300)
            .style("transform", "scale(1)");

        // Modal header
        const header = modalContent.append("div")
            .style("display", "flex")
            .style("justify-content", "space-between")
            .style("align-items", "center")
            .style("margin-bottom", "24px");

        header.append("h2")
            .style("font-size", "24px")
            .style("font-weight", "700")
            .style("color", "#1F2937")
            .style("margin", "0")
            .text(`Subfases de ${stageName}`);

        // Close button
        const closeBtn = header.append("button")
            .style("background", "none")
            .style("border", "none")
            .style("font-size", "24px")
            .style("cursor", "pointer")
            .style("color", "#6B7280")
            .style("padding", "8px")
            .style("border-radius", "8px")
            .style("transition", "all 0.2s")
            .text("×")
            .on("mouseover", function() {
                d3.select(this).style("background", "#F3F4F6").style("color", "#1F2937");
            })
            .on("mouseout", function() {
                d3.select(this).style("background", "none").style("color", "#6B7280");
            })
            .on("click", function() {
                modal.transition().duration(200).style("opacity", 0).remove();
            });

        // Create expanded funnel SVG
        const expandedSvg = modalContent.append("svg")
            .attr("width", 800)
            .attr("height", 500)
            .style("border", "1px solid #E5E7EB")
            .style("border-radius", "8px")
            .style("background", "#F9FAFB");

        // Draw expanded funnel
        drawExpandedFunnel(expandedSvg, subfases, stageName);

        // Close modal on overlay click
        modal.on("click", function(event) {
            if (event.target === modal.node()) {
                modal.transition().duration(200).style("opacity", 0).remove();
            }
        });
    }

    function drawExpandedFunnel(svg, subfases, stageName) {
        const margin = { top: 40, right: 60, bottom: 60, left: 60 };
        const width = 800 - margin.left - margin.right;
        const height = 500 - margin.top - margin.bottom;

        const g = svg.append("g")
            .attr("transform", `translate(${margin.left},${margin.top})`);

        // Create gradients for subfases
        const defs = svg.append("defs");
        const baseColor = stageName === "Oportunidades" ? "#3B82F6" : "#8B5CF6";

        subfases.forEach((subfase, i) => {
            const gradient = defs.append("linearGradient")
                .attr("id", `subfase-gradient-${i}`)
                .attr("x1", "0%")
                .attr("y1", "0%")
                .attr("x2", "0%")
                .attr("y2", "100%");

            const opacity1 = 0.9 - (i * 0.15);
            const opacity2 = 0.7 - (i * 0.1);

            gradient.append("stop")
                .attr("offset", "0%")
                .attr("stop-color", baseColor)
                .attr("stop-opacity", opacity1);

            gradient.append("stop")
                .attr("offset", "100%")
                .attr("stop-color", baseColor)
                .attr("stop-opacity", opacity2);
        });

        // Draw expanded funnel
        const funnelCenterX = width / 2;
        const funnelTopWidth = width * 0.8;
        const funnelBottomWidth = width * 0.2;
        const segmentHeight = (height - 60) / subfases.length;

        subfases.forEach((subfase, i) => {
            const topRatio = (subfases.length - i) / subfases.length;
            const bottomRatio = (subfases.length - i - 1) / subfases.length;

            const topWidth = funnelBottomWidth + (funnelTopWidth - funnelBottomWidth) * topRatio;
            const bottomWidth = funnelBottomWidth + (funnelTopWidth - funnelBottomWidth) * bottomRatio;

            const y = i * segmentHeight;
            const topLeft = funnelCenterX - topWidth / 2;
            const topRight = funnelCenterX + topWidth / 2;
            const bottomLeft = funnelCenterX - bottomWidth / 2;
            const bottomRight = funnelCenterX + bottomWidth / 2;

            const pathData = `M ${topLeft} ${y}
                             L ${topRight} ${y}
                             L ${bottomRight} ${y + segmentHeight}
                             L ${bottomLeft} ${y + segmentHeight} Z`;

            const segment = g.append("g").attr("class", `subfase-segment-${i}`);

            // Main segment
            segment.append("path")
                .attr("d", pathData)
                .attr("fill", `url(#subfase-gradient-${i})`)
                .attr("stroke", "#ffffff")
                .attr("stroke-width", 2)
                .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))")
                .style("opacity", 0)
                .transition()
                .duration(600)
                .delay(i * 100)
                .style("opacity", 1);

            // Value label
            segment.append("text")
                .attr("x", funnelCenterX)
                .attr("y", y + segmentHeight / 2 - 5)
                .attr("text-anchor", "middle")
                .attr("dominant-baseline", "middle")
                .attr("fill", "white")
                .attr("font-weight", "700")
                .attr("font-size", "16px")
                .style("text-shadow", "1px 1px 2px rgba(0,0,0,0.5)")
                .text(formatValue(subfase.value))
                .style("opacity", 0)
                .transition()
                .duration(600)
                .delay(i * 100 + 200)
                .style("opacity", 1);

            // Percentage label
            segment.append("text")
                .attr("x", funnelCenterX)
                .attr("y", y + segmentHeight / 2 + 12)
                .attr("text-anchor", "middle")
                .attr("dominant-baseline", "middle")
                .attr("fill", "white")
                .attr("font-size", "12px")
                .attr("font-weight", "600")
                .style("text-shadow", "1px 1px 2px rgba(0,0,0,0.5)")
                .text(subfase.percentage)
                .style("opacity", 0)
                .transition()
                .duration(600)
                .delay(i * 100 + 300)
                .style("opacity", 1);

            // Stage name (left side)
            segment.append("text")
                .attr("x", funnelCenterX - topWidth / 2 - 20)
                .attr("y", y + segmentHeight / 2 - 2)
                .attr("text-anchor", "end")
                .attr("fill", "#1F2937")
                .attr("font-weight", "600")
                .attr("font-size", "13px")
                .text(subfase.name)
                .style("opacity", 0)
                .transition()
                .duration(600)
                .delay(i * 100 + 400)
                .style("opacity", 1);

            // Description (left side)
            segment.append("text")
                .attr("x", funnelCenterX - topWidth / 2 - 20)
                .attr("y", y + segmentHeight / 2 + 12)
                .attr("text-anchor", "end")
                .attr("fill", "#6B7280")
                .attr("font-size", "10px")
                .attr("font-weight", "400")
                .text(subfase.description)
                .style("opacity", 0)
                .transition()
                .duration(600)
                .delay(i * 100 + 500)
                .style("opacity", 1);
        });
    }

    function addFunnelInfoCards(g, width, height, data) {
        const cardStartX = width * 0.6; // Posição dos cards à direita
        const cardWidth = width * 0.18; // Largura reduzida para 2 por linha
        const cardHeight = 120; // Altura aumentada
        const cardSpacingX = 10; // Espaçamento horizontal
        const cardSpacingY = 15; // Espaçamento vertical

        // Calculate metrics from funnel data
        const stages = data.stages;

        // Calculate advanced business metrics
        const totalLeads = stages[0]?.value || 0;
        const totalOpportunities = stages[1]?.value || 0;
        const totalImplementations = stages[2]?.value || 0;
        const totalActiveUsers = stages[3]?.value || 0;
        const totalProductiveUsers = stages[4]?.value || 0;

        // Advanced metrics calculations
        const conversionVelocity = data.time_metrics?.total_cycle_time || 47;
        const avgTicket = data.revenue_metrics?.avg_ticket || 2847;
        const revenuePerLead = data.revenue_metrics?.revenue_per_lead || 1250;
        const implementationSuccess = totalImplementations > 0 ? Math.round((totalActiveUsers / totalImplementations * 100)) : 0;
        const productivityRate = totalActiveUsers > 0 ? Math.round((totalProductiveUsers / totalActiveUsers * 100)) : 0;
        const opportunityQuality = totalOpportunities > 0 ? Math.round((totalImplementations / totalOpportunities * 100)) : 0;
        const activationTime = data.time_metrics?.implementation_to_active || 19;
        const leadQuality = totalLeads > 0 ? Math.round((totalOpportunities / totalLeads * 100)) : 0;

        const cards = [
            {
                title: "Ciclo Completo",
                value: `${conversionVelocity}d`,
                description: "Lead → Usuário Produtivo",
                delta: "8.2%",
                deltaType: "negative"
            },
            {
                title: "Receita/Lead",
                value: `R$ ${Math.round(revenuePerLead/1000)}k`,
                description: "Valor médio por lead",
                delta: "15.7%",
                deltaType: "positive"
            },
            {
                title: "Taxa Ativação",
                value: `${implementationSuccess}%`,
                description: "Implementação → Ativo",
                delta: "12.3%",
                deltaType: "positive"
            },
            {
                title: "Qualidade Lead",
                value: `${leadQuality}%`,
                description: "Lead → Oportunidade",
                delta: "21.4%",
                deltaType: "positive"
            },
            {
                title: "Produtividade",
                value: `${productivityRate}%`,
                description: "Usuários gerando receita",
                delta: "5.8%",
                deltaType: "negative"
            },
            {
                title: "Qualidade Opp",
                value: `${opportunityQuality}%`,
                description: "Oportunidade → Implementação",
                delta: "47.8%",
                deltaType: "positive"
            },
            {
                title: "Tempo Ativação",
                value: `${activationTime}d`,
                description: "Implementação → Primeiro Uso",
                delta: "6.1%",
                deltaType: "positive"
            },
            {
                title: "Ticket Médio",
                value: `R$ ${Math.round(avgTicket/1000)}k`,
                description: "Por usuário ativo",
                delta: "18.9%",
                deltaType: "positive"
            }
        ];

        cards.forEach((card, i) => {
            // Calculate position for 2x4 grid
            const col = i % 2;
            const row = Math.floor(i / 2);
            const cardX = cardStartX + (col * (cardWidth + 15));
            const cardY = 50 + (row * (cardHeight + 20));

            // Card background
            g.append("rect")
                .attr("x", cardX)
                .attr("y", cardY)
                .attr("width", cardWidth)
                .attr("height", cardHeight)
                .attr("fill", "white")
                .attr("stroke", "#E5E7EB")
                .attr("stroke-width", 1)
                .attr("rx", 8)
                .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))");

            // Title (black font)
            g.append("text")
                .attr("x", cardX + 12)
                .attr("y", cardY + 20)
                .attr("fill", "#000000")
                .attr("font-size", "11px")
                .attr("font-weight", "600")
                .text(card.title);

            // Value (black font, larger)
            g.append("text")
                .attr("x", cardX + 12)
                .attr("y", cardY + 45)
                .attr("fill", "#000000")
                .attr("font-size", "20px")
                .attr("font-weight", "700")
                .text(card.value);

            // Description (black font, smaller)
            g.append("text")
                .attr("x", cardX + 12)
                .attr("y", cardY + 65)
                .attr("fill", "#000000")
                .attr("font-size", "9px")
                .attr("font-weight", "400")
                .text(card.description);

            // Delta with arrow (positioned at top right)
            const deltaColor = card.deltaType === "positive" ? "#10B981" : "#EF4444";
            const deltaArrow = card.deltaType === "positive" ? "↗" : "↘";

            g.append("text")
                .attr("x", cardX + cardWidth - 12)
                .attr("y", cardY + 20)
                .attr("text-anchor", "end")
                .attr("fill", deltaColor)
                .attr("font-size", "10px")
                .attr("font-weight", "600")
                .text(`${deltaArrow} ${card.delta}`);

            // Bottom line indicator
            g.append("rect")
                .attr("x", cardX + 12)
                .attr("y", cardY + cardHeight - 8)
                .attr("width", cardWidth - 24)
                .attr("height", 2)
                .attr("fill", deltaColor)
                .attr("rx", 1);
        });
    }

    function formatValue(value) {
        if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'k';
        }
        return value.toString();
    }

    function highlightStage(stageIndex, stages) {
        // Reset all segments
        d3.selectAll(".funnel-segment path")
            .style("opacity", 0.3);

        // Highlight selected segment
        d3.select(`.segment-${stageIndex} path`)
            .style("opacity", 1);

        // Update stage cards
        const stage = stages[stageIndex];
        if (stage) {
            document.getElementById(`stage-value-${stageIndex}`).textContent = formatValue(stage.value);
            document.getElementById(`stage-percentage-${stageIndex}`).textContent = `${stage.percentage}%`;
            document.getElementById(`stage-description-${stageIndex}`).textContent = stage.description;

            // Highlight the card
            document.querySelectorAll('[id^="funnel-stage-"]').forEach(card => {
                card.classList.remove('ring-2', 'ring-blue-500');
            });
            document.getElementById(`funnel-stage-${stageIndex}`).classList.add('ring-2', 'ring-blue-500');
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded for conversion page');

        // Debug filter options
        console.log('Filter options available:', {{ filter_options|tojson|safe }});

        // Parse chart data safely
        let chartData = {};
        try {
            const rawChartData = '{{ chart_data|safe }}';
            console.log('Raw chart data:', rawChartData);

            if (rawChartData && rawChartData !== 'None' && rawChartData !== '') {
                chartData = JSON.parse(rawChartData);
                console.log('Chart data parsed successfully:', chartData);
            } else {
                console.warn('No chart data available');
                chartData = {};
            }
        } catch (error) {
            console.error('Error parsing chart data:', error);
            console.error('Raw data was:', '{{ chart_data|safe }}');
            chartData = {};
        }

        // Initialize D3.js Funnel Chart
        waitForD3(function() {
            try {
                if (chartData.advanced_funnel && chartData.advanced_funnel.stages) {
                    console.log('Initializing D3 funnel chart with data:', chartData.advanced_funnel);

                    // Store original data for filter reset functionality
                    window.originalChartData = chartData;
                    console.log('Original chart data stored:', window.originalChartData);

                    // Update stage cards
                    updateStageCards(chartData.advanced_funnel.stages);

                    // Update metrics
                    updateMetrics(chartData.advanced_funnel);

                    // Create the D3.js funnel chart
                    createD3Funnel('advanced-funnel-chart', chartData.advanced_funnel);

                    // Setup stage card interactions
                    setupStageCardInteractions(chartData.advanced_funnel.stages);

                    // Setup filter functionality after data is loaded
                    setupFilters();

                    console.log('D3 funnel chart initialized successfully');
                } else {
                    console.warn('Advanced funnel data not available');
                    document.getElementById('advanced-funnel-chart').innerHTML =
                        '<div class="flex items-center justify-center h-full"><p class="text-gray-500 text-sm">Dados do funil não disponíveis</p></div>';
                }
            } catch (error) {
                console.error('Error initializing D3 funnel chart:', error);
                document.getElementById('advanced-funnel-chart').innerHTML =
                    '<div class="flex items-center justify-center h-full"><p class="text-gray-500 text-sm">Erro ao carregar funil de vendas</p></div>';
            }
        });

        // Helper functions for advanced funnel
        function updateStageCards(stages) {
            stages.forEach((stage, index) => {
                const valueElement = document.getElementById(`stage-value-${index}`);
                const percentageElement = document.getElementById(`stage-percentage-${index}`);
                const descriptionElement = document.getElementById(`stage-description-${index}`);

                if (valueElement) valueElement.textContent = formatValue(stage.value);
                if (percentageElement) percentageElement.textContent = `${stage.percentage}%`;
                if (descriptionElement) descriptionElement.textContent = stage.description;
            });
        }

        function updateMetrics(funnelData) {
            // Revenue metrics
            if (funnelData.revenue_metrics) {
                const rm = funnelData.revenue_metrics;
                updateElement('mrr-total', formatCurrency(rm.mrr_total || 0));
                updateElement('avg-ticket', formatCurrency(rm.avg_ticket || 0));
                updateElement('revenue-per-lead', formatCurrency(rm.revenue_per_lead || 0));
                updateElement('revenue-per-opportunity', formatCurrency(rm.revenue_per_opportunity || 0));
            }

            // Time metrics
            if (funnelData.time_metrics) {
                const tm = funnelData.time_metrics;
                updateElement('time-lead-opp', `${tm.lead_to_opportunity || 0} dias`);
                updateElement('time-opp-impl', `${tm.opportunity_to_implementation || 0} dias`);
                updateElement('time-impl-active', `${tm.implementation_to_active || 0} dias`);
                updateElement('time-total-cycle', `${tm.total_cycle_time || 0} dias`);
            }

            // Summary metrics
            if (funnelData.summary) {
                const s = funnelData.summary;
                updateElement('total-conversion-rate', `${s.total_conversion_rate || 0}%`);
                updateElement('productive-conversion-rate', `${s.productive_conversion_rate || 0}%`);
            }

            // Drop-off analysis
            if (funnelData.dropoffs) {
                updateDropoffAnalysis(funnelData.dropoffs);
            }

            // Optimization opportunities
            if (funnelData.summary) {
                updateOptimizationOpportunities(funnelData.summary, funnelData.dropoffs);
            }
        }

        function updateDropoffAnalysis(dropoffs) {
            const container = document.getElementById('dropoff-analysis');
            if (!container) return;

            container.innerHTML = '';

            dropoffs.forEach(dropoff => {
                const dropoffElement = document.createElement('div');
                dropoffElement.className = 'flex justify-between items-center p-2 bg-gray-50 rounded';
                dropoffElement.innerHTML = `
                    <div>
                        <div class="text-xs font-medium text-gray-900">${dropoff.from_stage} → ${dropoff.to_stage}</div>
                        <div class="text-xs text-gray-500">Conversão: ${dropoff.conversion_rate}%</div>
                    </div>
                    <div class="text-sm font-bold text-red-600">-${dropoff.drop_percentage}%</div>
                `;
                container.appendChild(dropoffElement);
            });
        }

        function updateOptimizationOpportunities(summary, dropoffs) {
            const container = document.getElementById('optimization-opportunities');
            if (!container) return;

            container.innerHTML = '';

            // Biggest opportunity
            if (summary.biggest_dropoff) {
                const bd = summary.biggest_dropoff;
                const opportunityElement = document.createElement('div');
                opportunityElement.className = 'p-3 bg-yellow-50 rounded-lg border border-yellow-200';
                opportunityElement.innerHTML = `
                    <div class="text-sm font-medium text-yellow-900">Maior Oportunidade</div>
                    <div class="text-xs text-yellow-700 mt-1">${bd.from_stage} → ${bd.to_stage}</div>
                    <div class="text-xs text-yellow-600 mt-1">Perda de ${bd.drop_percentage}% (${bd.drop_value} registros)</div>
                `;
                container.appendChild(opportunityElement);
            }

            // Performance insight
            const performanceElement = document.createElement('div');
            performanceElement.className = 'p-3 bg-blue-50 rounded-lg border border-blue-200';
            performanceElement.innerHTML = `
                <div class="text-sm font-medium text-blue-900">Performance Geral</div>
                <div class="text-xs text-blue-700 mt-1">Conversão total: ${summary.total_conversion_rate || 0}%</div>
                <div class="text-xs text-blue-600 mt-1">Conversão produtiva: ${summary.productive_conversion_rate || 0}%</div>
            `;
            container.appendChild(performanceElement);
        }

        // View buttons removed - simplified interface

        function setupStageCardInteractions(stages) {
            for (let i = 0; i < 5; i++) {
                const card = document.getElementById(`funnel-stage-${i}`);
                if (card) {
                    card.addEventListener('click', () => {
                        highlightStage(i, stages);
                    });
                }
            }
        }

        function updateElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        }

        function formatCurrency(value) {
            return new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            }).format(value || 0);
        }

        function setupFilters() {
            // Setup multi-select filter handlers
            const filterElements = ['filter-turma', 'filter-responsavel', 'filter-universidade', 'filter-cidade', 'filter-estado'];

            filterElements.forEach(filterId => {
                const element = document.getElementById(filterId);
                if (element) {
                    element.addEventListener('change', updateSelectedFilters);
                }
            });

            // Apply filters button
            document.getElementById('apply-filters').addEventListener('click', function() {
                if (selectedFilters.length === 0) {
                    alert('Selecione pelo menos um filtro para aplicar.');
                    return;
                }

                console.log('Applying multiple filters:', selectedFilters);

                // Show loading state
                const button = this;
                button.textContent = 'Aplicando...';
                button.disabled = true;

                // Apply overlapping funnel visualization
                applyOverlappingFilters(selectedFilters);
            });

            // Clear filters button
            document.getElementById('clear-filters').addEventListener('click', function() {
                clearAllFilters();
            });

            // Initialize filter display
            updateFilterDisplay();
        }

        function updateSelectedFilters() {
            selectedFilters = [];
            const filterElements = ['filter-turma', 'filter-responsavel', 'filter-universidade', 'filter-cidade', 'filter-estado'];

            filterElements.forEach(filterId => {
                const element = document.getElementById(filterId);
                const selectedOptions = Array.from(element.selectedOptions);

                if (selectedOptions.length > 0) {
                    const filterType = filterId.replace('filter-', '');
                    const values = selectedOptions.map(option => option.value);

                    selectedFilters.push({
                        type: filterType,
                        values: values,
                        label: getFilterLabel(filterType),
                        color: filterColors[selectedFilters.length % filterColors.length]
                    });
                }
            });

            // Limit to max filters
            if (selectedFilters.length > maxFilters) {
                selectedFilters = selectedFilters.slice(0, maxFilters);
                alert(`Máximo de ${maxFilters} filtros permitidos. Os primeiros ${maxFilters} foram mantidos.`);
            }

            updateFilterDisplay();
        }

        function getFilterLabel(filterType) {
            const labels = {
                'turma': 'Turma',
                'responsavel': 'Responsável',
                'universidade': 'Universidade',
                'cidade': 'Cidade',
                'estado': 'Estado'
            };
            return labels[filterType] || filterType;
        }

        function updateFilterDisplay() {
            const countElement = document.getElementById('selected-filters-count');
            const displayElement = document.getElementById('selected-filters-display');
            const listElement = document.getElementById('active-filters-list');
            const applyButton = document.getElementById('apply-filters');

            countElement.textContent = selectedFilters.length;

            if (selectedFilters.length > 0) {
                displayElement.classList.remove('hidden');
                listElement.innerHTML = '';

                selectedFilters.forEach((filter, index) => {
                    const filterTag = document.createElement('div');
                    filterTag.className = 'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white';
                    filterTag.style.backgroundColor = filter.color;
                    filterTag.innerHTML = `
                        <span>${filter.label}: ${filter.values.join(', ')}</span>
                        <button onclick="removeFilter(${index})" class="ml-2 text-white hover:text-gray-200">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    `;
                    listElement.appendChild(filterTag);
                });

                applyButton.disabled = false;
            } else {
                displayElement.classList.add('hidden');
                applyButton.disabled = true;
            }
        }

        function removeFilter(index) {
            selectedFilters.splice(index, 1);
            updateFilterDisplay();

            // Clear the corresponding select element
            const filterElements = ['filter-turma', 'filter-responsavel', 'filter-universidade', 'filter-cidade', 'filter-estado'];
            filterElements.forEach(filterId => {
                const element = document.getElementById(filterId);
                Array.from(element.options).forEach(option => option.selected = false);
            });

            // Re-select remaining filters
            selectedFilters.forEach(filter => {
                const element = document.getElementById(`filter-${filter.type}`);
                filter.values.forEach(value => {
                    const option = Array.from(element.options).find(opt => opt.value === value);
                    if (option) option.selected = true;
                });
            });
        }

        function clearAllFilters() {
            selectedFilters = [];
            const filterElements = ['filter-turma', 'filter-responsavel', 'filter-universidade', 'filter-cidade', 'filter-estado'];

            filterElements.forEach(filterId => {
                const element = document.getElementById(filterId);
                Array.from(element.options).forEach(option => option.selected = false);
            });

            updateFilterDisplay();

            // Reset funnel to original state
            if (window.originalChartData && window.originalChartData.advanced_funnel) {
                createD3Funnel('advanced-funnel-chart', window.originalChartData.advanced_funnel);
            }
        }

        function applyOverlappingFilters(filters) {
            console.log('applyOverlappingFilters called with filters:', filters);
            console.log('window.originalChartData:', window.originalChartData);
            console.log('window.originalChartData?.advanced_funnel:', window.originalChartData?.advanced_funnel);

            // Check if original data is available
            if (!window.originalChartData || !window.originalChartData.advanced_funnel) {
                console.error('Original chart data not available');
                console.error('window.originalChartData exists:', !!window.originalChartData);
                console.error('advanced_funnel exists:', !!(window.originalChartData && window.originalChartData.advanced_funnel));
                alert('Dados não disponíveis. Por favor, recarregue a página.');

                // Re-enable apply button
                const applyButton = document.getElementById('apply-filters');
                applyButton.textContent = 'Aplicar Filtros Sobrepostos';
                applyButton.disabled = false;
                return;
            }

            // Make AJAX call to get filtered data
            console.log('Making AJAX call to get filtered data with filters:', filters);

            fetch('/conversion/api/filtered-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(filters)
            })
            .then(response => response.json())
            .then(data => {
                console.log('Received filtered data:', data);

                if (data.success && data.advanced_funnel) {
                    // Create overlapping funnel with real filtered data
                    const overlappingData = filters.map((filter, index) => {
                        return {
                            ...data.advanced_funnel,
                            stages: data.advanced_funnel.stages.map(stage => ({
                                ...stage,
                                color: filter.color,
                                opacity: 0.7 - (index * 0.1) // Decreasing opacity for overlay effect
                            }))
                        };
                    });

                    createOverlappingFunnel('advanced-funnel-chart', overlappingData, filters);
                } else {
                    console.error('Error in filtered data response:', data.error);
                    alert('Erro ao aplicar filtros: ' + (data.error || 'Erro desconhecido'));
                }

                // Re-enable apply button
                const applyButton = document.getElementById('apply-filters');
                applyButton.textContent = 'Aplicar Filtros Sobrepostos';
                applyButton.disabled = false;
            })
            .catch(error => {
                console.error('Error fetching filtered data:', error);
                alert('Erro ao aplicar filtros. Verifique a conexão.');

                // Re-enable apply button
                const applyButton = document.getElementById('apply-filters');
                applyButton.textContent = 'Aplicar Filtros Sobrepostos';
                applyButton.disabled = false;
            });
        }

        // Function to create overlapping funnels
        function createOverlappingFunnel(containerId, overlappingDataArray, filters) {
            if (!overlappingDataArray || overlappingDataArray.length === 0) {
                return;
            }

            // Clear container
            d3.select(`#${containerId}`).selectAll("*").remove();

            // Use same dimensions as original funnel
            const containerElement = document.getElementById(containerId);
            const containerRect = containerElement.getBoundingClientRect();
            const margin = { top: 40, right: 40, bottom: 40, left: 40 };
            const width = containerRect.width - margin.left - margin.right;
            const height = 960 - margin.top - margin.bottom;

            // Create SVG
            const svg = d3.select(`#${containerId}`)
                .append("svg")
                .attr("width", width + margin.left + margin.right)
                .attr("height", height + margin.top + margin.bottom)
                .style("background", "#f9fafb")
                .style("border-radius", "8px")
                .style("border", "1px solid #e5e7eb");

            const defs = svg.append("defs");
            const g = svg.append("g")
                .attr("transform", `translate(${margin.left},${margin.top})`);

            // Create gradients for each filter
            filters.forEach((filter, filterIndex) => {
                const gradient = defs.append("linearGradient")
                    .attr("id", `overlay-gradient-${filterIndex}`)
                    .attr("x1", "0%")
                    .attr("y1", "0%")
                    .attr("x2", "0%")
                    .attr("y2", "100%");

                gradient.append("stop")
                    .attr("offset", "0%")
                    .attr("stop-color", filter.color)
                    .attr("stop-opacity", 0.8);

                gradient.append("stop")
                    .attr("offset", "100%")
                    .attr("stop-color", filter.color)
                    .attr("stop-opacity", 0.6);
            });

            // Add premium filters
            const overlayFilter = defs.append("filter")
                .attr("id", "overlay-shadow")
                .attr("x", "-50%")
                .attr("y", "-50%")
                .attr("width", "200%")
                .attr("height", "200%");

            overlayFilter.append("feGaussianBlur")
                .attr("in", "SourceAlpha")
                .attr("stdDeviation", 4)
                .attr("result", "blur");

            overlayFilter.append("feOffset")
                .attr("in", "blur")
                .attr("dx", 0)
                .attr("dy", 4)
                .attr("result", "offsetBlur");

            const overlayMerge = overlayFilter.append("feMerge");
            overlayMerge.append("feMergeNode").attr("in", "offsetBlur");
            overlayMerge.append("feMergeNode").attr("in", "SourceGraphic");

            // Create tooltip
            const tooltip = d3.select("body").append("div")
                .attr("class", "d3-overlay-tooltip")
                .style("position", "absolute")
                .style("background", "linear-gradient(135deg, rgba(30, 41, 59, 0.95), rgba(51, 65, 85, 0.95))")
                .style("color", "white")
                .style("padding", "12px 16px")
                .style("border-radius", "12px")
                .style("font-size", "14px")
                .style("pointer-events", "none")
                .style("opacity", 0)
                .style("z-index", 1000);

            // Funnel dimensions
            const funnelCenterX = width * 0.3;
            const funnelTopWidth = width * 0.5;
            const funnelBottomWidth = width * 0.08;
            const maxStageHeight = height - 80;
            const segmentHeight = maxStageHeight / overlappingDataArray[0].stages.length;

            // Draw overlapping funnels
            overlappingDataArray.forEach((data, filterIndex) => {
                const filter = filters[filterIndex];
                const stages = data.stages;

                stages.forEach((stage, stageIndex) => {
                    // Calculate trapezoid dimensions
                    const topRatio = (stages.length - stageIndex) / stages.length;
                    const bottomRatio = (stages.length - stageIndex - 1) / stages.length;

                    const topWidth = funnelBottomWidth + (funnelTopWidth - funnelBottomWidth) * topRatio;
                    const bottomWidth = funnelBottomWidth + (funnelTopWidth - funnelBottomWidth) * bottomRatio;

                    const y = stageIndex * segmentHeight;
                    const topLeft = funnelCenterX - topWidth / 2;
                    const topRight = funnelCenterX + topWidth / 2;
                    const bottomLeft = funnelCenterX - bottomWidth / 2;
                    const bottomRight = funnelCenterX + bottomWidth / 2;

                    // Create smooth path
                    function createOverlaySmoothPath(topLeft, topRight, bottomLeft, bottomRight, y, segmentHeight) {
                        const cornerRadius = 12;
                        const curveIntensity = 0.25;

                        const path = d3.path();
                        path.moveTo(topLeft + cornerRadius, y);

                        const topMidX = (topLeft + topRight) / 2;
                        path.quadraticCurveTo(topMidX, y - 1, topRight - cornerRadius, y);
                        path.quadraticCurveTo(topRight, y, topRight, y + cornerRadius);

                        const rightMidY = y + segmentHeight / 2;
                        const rightControlX = topRight + (bottomRight - topRight) * curveIntensity;
                        path.quadraticCurveTo(rightControlX, rightMidY, bottomRight, y + segmentHeight - cornerRadius);
                        path.quadraticCurveTo(bottomRight, y + segmentHeight, bottomRight - cornerRadius, y + segmentHeight);

                        const bottomMidX = (bottomLeft + bottomRight) / 2;
                        path.quadraticCurveTo(bottomMidX, y + segmentHeight + 1, bottomLeft + cornerRadius, y + segmentHeight);
                        path.quadraticCurveTo(bottomLeft, y + segmentHeight, bottomLeft, y + segmentHeight - cornerRadius);

                        const leftMidY = y + segmentHeight / 2;
                        const leftControlX = bottomLeft - (topLeft - bottomLeft) * curveIntensity;
                        path.quadraticCurveTo(leftControlX, leftMidY, topLeft, y + cornerRadius);
                        path.quadraticCurveTo(topLeft, y, topLeft + cornerRadius, y);

                        path.closePath();
                        return path.toString();
                    }

                    const smoothPathData = createOverlaySmoothPath(topLeft, topRight, bottomLeft, bottomRight, y, segmentHeight);

                    // Create segment group
                    const segment = g.append("g")
                        .attr("class", `overlay-segment-${filterIndex}-${stageIndex}`)
                        .style("cursor", "pointer");

                    // Main segment with accumulated opacity
                    const baseOpacity = 0.7 - (filterIndex * 0.15);
                    segment.append("path")
                        .attr("d", smoothPathData)
                        .attr("fill", `url(#overlay-gradient-${filterIndex})`)
                        .attr("stroke", filter.color)
                        .attr("stroke-width", 1)
                        .attr("opacity", baseOpacity)
                        .style("filter", "url(#overlay-shadow)")
                        .style("mix-blend-mode", "multiply") // Creates overlay effect
                        .style("transition", "all 0.3s ease")
                        .on("mouseover", function(event) {
                            d3.select(this)
                                .attr("opacity", Math.min(baseOpacity + 0.2, 1))
                                .attr("stroke-width", 2);

                            tooltip
                                .style("opacity", 1)
                                .html(`
                                    <div style="font-weight: 700; font-size: 14px; margin-bottom: 6px;">
                                        ${filter.label}: ${filter.values.join(', ')}
                                    </div>
                                    <div style="margin-bottom: 4px;">
                                        <span style="color: #94A3B8;">Estágio:</span>
                                        <span style="font-weight: 600;">${stage.name}</span>
                                    </div>
                                    <div style="margin-bottom: 4px;">
                                        <span style="color: #94A3B8;">Valor:</span>
                                        <span style="font-weight: 600;">${formatValue(stage.value)}</span>
                                    </div>
                                    <div>
                                        <span style="color: #94A3B8;">Conversão:</span>
                                        <span style="font-weight: 600;">${stage.percentage}%</span>
                                    </div>
                                `)
                                .style("left", (event.pageX + 15) + "px")
                                .style("top", (event.pageY - 15) + "px");
                        })
                        .on("mouseout", function() {
                            d3.select(this)
                                .attr("opacity", baseOpacity)
                                .attr("stroke-width", 1);

                            tooltip.style("opacity", 0);
                        });

                    // Add value labels only for the first filter to avoid clutter
                    if (filterIndex === 0) {
                        segment.append("text")
                            .attr("x", funnelCenterX)
                            .attr("y", y + segmentHeight / 2 - 5)
                            .attr("text-anchor", "middle")
                            .attr("dominant-baseline", "middle")
                            .attr("fill", "#FFFFFF")
                            .attr("font-weight", "900")
                            .attr("font-size", "20px")
                            .style("text-shadow", "2px 2px 6px rgba(0,0,0,0.8)")
                            .style("pointer-events", "none")
                            .text(formatValue(stage.value));

                        segment.append("text")
                            .attr("x", funnelCenterX)
                            .attr("y", y + segmentHeight / 2 + 15)
                            .attr("text-anchor", "middle")
                            .attr("dominant-baseline", "middle")
                            .attr("fill", "#F8FAFC")
                            .attr("font-weight", "700")
                            .attr("font-size", "14px")
                            .style("text-shadow", "1px 1px 4px rgba(0,0,0,0.7)")
                            .style("pointer-events", "none")
                            .text(`${stage.percentage}%`);
                    }
                });
            });

            // Add legend for filters
            const legend = g.append("g")
                .attr("class", "overlay-legend")
                .attr("transform", `translate(${width - 200}, 20)`);

            filters.forEach((filter, index) => {
                const legendItem = legend.append("g")
                    .attr("transform", `translate(0, ${index * 25})`);

                legendItem.append("rect")
                    .attr("width", 15)
                    .attr("height", 15)
                    .attr("fill", filter.color)
                    .attr("opacity", 0.7 - (index * 0.15))
                    .attr("rx", 3);

                legendItem.append("text")
                    .attr("x", 20)
                    .attr("y", 12)
                    .attr("fill", "#374151")
                    .attr("font-size", "12px")
                    .attr("font-weight", "600")
                    .text(`${filter.label}: ${filter.values.join(', ')}`);
            });
        }

        // Store original data for reset functionality
        window.originalChartData = null;
        console.log('Initial window.originalChartData set to null');

        // D3.js funnel chart is now the primary visualization
        console.log('D3.js funnel chart initialized as primary visualization');

        // Create implementation phases chart
        try {
            if (typeof AmigoDH === 'undefined' || !AmigoDH.createPieChart) {
                console.error('AmigoDH library not available for implementation phases chart');
                document.getElementById('implementationPhasesChart').parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full"><p class="text-gray-500 text-sm">Biblioteca de gráficos não disponível</p></div>';
                return;
            }

            const implementationPhasesData = chartData.implementation_phases || {};
            const phaseLabels = Object.keys(implementationPhasesData);
            const phaseValues = Object.values(implementationPhasesData).map(v => parseInt(v) || 0);

            if (phaseLabels.length === 0) {
                document.getElementById('implementationPhasesChart').parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full"><p class="text-gray-500 text-sm">Dados não disponíveis</p></div>';
                return;
            }

            const phaseColors = AmigoDH.colors && AmigoDH.colors.blue ? AmigoDH.colors.blue : [
                'rgba(59, 130, 246, 0.8)',
                'rgba(99, 102, 241, 0.8)',
                'rgba(139, 92, 246, 0.8)',
                'rgba(168, 85, 247, 0.8)',
                'rgba(196, 181, 253, 0.8)',
                'rgba(221, 214, 254, 0.8)'
            ];

            AmigoDH.createPieChart('implementationPhasesChart', phaseLabels, phaseValues, {
                backgroundColor: phaseColors
            });
            console.log('Implementation phases chart created successfully');
        } catch (error) {
            console.error('Error creating implementation phases chart:', error);
            if (typeof window.debugChart === 'function') {
                window.debugChart('implementationPhasesChart', {}, error);
            }
            const element = document.getElementById('implementationPhasesChart');
            if (element && element.parentNode) {
                element.parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full"><p class="text-gray-500 text-sm">Erro ao carregar gráfico</p></div>';
            }
        }

        // Create bottleneck analysis chart
        try {
            const bottleneckData = chartData.conversion_dropoff || {};

            if (Object.keys(bottleneckData).length === 0) {
                document.getElementById('bottleneckAnalysisChart').parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full">' +
                    '<p class="text-gray-500 text-sm">Dados não disponíveis</p>' +
                    '</div>';
                console.warn('No data available for bottleneck analysis chart');
                return;
            }

            // Prepare data for funnel chart
            const funnelStages = [];
            const funnelValues = [];
            const dropoffPercentages = [];
            const stageTransitions = [];

            // Get funnel stages distribution
            const stagesData = chartData.funnel_stages || {};
            const phasesData = chartData.implementation_phases || {};

            // Combine all stages in order
            if (Object.keys(stagesData).length > 0) {
                funnelStages.push('Leads');
                funnelValues.push(parseInt(chartData.total_leads || 0));
                dropoffPercentages.push(0);
                stageTransitions.push('');

                // Add opportunity stages
                Object.keys(stagesData).forEach(stage => {
                    funnelStages.push(stage);
                    funnelValues.push(parseInt(stagesData[stage]));

                    // Calculate dropoff from previous stage
                    const prevValue = funnelValues[funnelValues.length - 2];
                    const currentValue = funnelValues[funnelValues.length - 1];
                    const dropoff = prevValue > 0 ? Math.round((1 - (currentValue / prevValue)) * 100) : 0;
                    dropoffPercentages.push(dropoff);

                    // Add stage transition
                    const prevStage = funnelStages[funnelStages.length - 2];
                    stageTransitions.push(`${prevStage} → ${stage}`);
                });

                // Add implementation phases if available
                if (Object.keys(phasesData).length > 0) {
                    Object.keys(phasesData).forEach(phase => {
                        funnelStages.push(phase);
                        funnelValues.push(parseInt(phasesData[phase]));

                        // Calculate dropoff from previous stage
                        const prevValue = funnelValues[funnelValues.length - 2];
                        const currentValue = funnelValues[funnelValues.length - 1];
                        const dropoff = prevValue > 0 ? Math.round((1 - (currentValue / prevValue)) * 100) : 0;
                        dropoffPercentages.push(dropoff);

                        // Add stage transition
                        const prevStage = funnelStages[funnelStages.length - 2];
                        stageTransitions.push(`${prevStage} → ${phase}`);
                    });
                }
            }

            // If no data available after processing
            if (funnelStages.length === 0) {
                document.getElementById('bottleneckAnalysisChart').parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full">' +
                    '<p class="text-gray-500 text-sm">Dados insuficientes para análise</p>' +
                    '</div>';
                console.warn('Insufficient data for bottleneck analysis chart');
                return;
            }

            // Generate colors array based on number of stages
            const colors = [];
            const defaultColors = [
                'rgba(59, 130, 246, 0.7)',
                'rgba(99, 102, 241, 0.7)',
                'rgba(139, 92, 246, 0.7)',
                'rgba(168, 85, 247, 0.7)',
                'rgba(196, 181, 253, 0.7)',
                'rgba(221, 214, 254, 0.7)'
            ];

            for (let i = 0; i < funnelStages.length; i++) {
                if (typeof AmigoDH !== 'undefined' && AmigoDH.colors && AmigoDH.colors.blue) {
                    const colorIndex = Math.min(i, AmigoDH.colors.blue.length - 1);
                    colors.push(AmigoDH.colors.blue[colorIndex]);
                } else {
                    const colorIndex = Math.min(i, defaultColors.length - 1);
                    colors.push(defaultColors[colorIndex]);
                }
            }

            if (typeof AmigoDH !== 'undefined' && AmigoDH.createFunnelChart) {
                AmigoDH.createFunnelChart('bottleneckAnalysisChart', funnelStages, funnelValues, 'Funil de Conversão', {
                    backgroundColor: colors,
                    tooltipCallback: function(value, index) {
                        if (index === 0) {
                            return 'Total: ' + value;
                        } else {
                            return 'Total: ' + value + ' (Queda: ' + dropoffPercentages[index] + '%)';
                        }
                    }
                });
            } else {
                // Fallback to native Chart.js bar chart
                new Chart(document.getElementById('bottleneckAnalysisChart').getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: funnelStages,
                        datasets: [{
                            label: 'Quantidade',
                            data: funnelValues,
                            backgroundColor: colors,
                            borderColor: colors.map(color => color.replace('0.7', '1')),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const index = context.dataIndex;
                                        if (index === 0) {
                                            return 'Total: ' + context.raw;
                                        } else {
                                            return 'Total: ' + context.raw + ' (Queda: ' + dropoffPercentages[index] + '%)';
                                        }
                                    }
                                }
                            }
                        }
                    }
                });
            }
            console.log('Bottleneck analysis chart created successfully');

            // Update insight cards with dynamic data

            // Find the biggest bottleneck (highest dropoff percentage)
            let maxDropoffIndex = 0;
            let maxDropoffValue = 0;

            for (let i = 1; i < dropoffPercentages.length; i++) {
                if (dropoffPercentages[i] > maxDropoffValue) {
                    maxDropoffValue = dropoffPercentages[i];
                    maxDropoffIndex = i;
                }
            }

            // Update the biggest bottleneck insight card
            if (maxDropoffIndex > 0) {
                document.getElementById('biggestDropStage').textContent = stageTransitions[maxDropoffIndex];
                document.getElementById('biggestDropRate').textContent = maxDropoffValue + '%';
            }

            // Find a good improvement opportunity (significant dropoff but not the worst)
            let improvementIndex = 0;
            let improvementValue = 0;

            // Look for the second highest dropoff
            let secondMaxDropoffValue = 0;
            let secondMaxDropoffIndex = 0;

            for (let i = 1; i < dropoffPercentages.length; i++) {
                if (dropoffPercentages[i] > secondMaxDropoffValue && dropoffPercentages[i] < maxDropoffValue) {
                    secondMaxDropoffValue = dropoffPercentages[i];
                    secondMaxDropoffIndex = i;
                }
            }

            // If we found a second highest, use it, otherwise use the highest
            if (secondMaxDropoffIndex > 0) {
                improvementIndex = secondMaxDropoffIndex;
                improvementValue = secondMaxDropoffValue;
            } else {
                improvementIndex = maxDropoffIndex;
                improvementValue = maxDropoffValue;
            }

            // Calculate potential improvement
            const improvementTarget = 10; // 10% improvement
            const currentStageValue = funnelValues[improvementIndex];
            const prevStageValue = funnelValues[improvementIndex - 1];
            const currentConversionRate = 100 - improvementValue;
            const improvedConversionRate = currentConversionRate + improvementTarget;
            const potentialValue = Math.round(prevStageValue * (improvedConversionRate / 100));
            const additionalConversions = potentialValue - currentStageValue;

            // Update the improvement opportunity insight card
            if (improvementIndex > 0) {
                document.getElementById('improvementStage').textContent = stageTransitions[improvementIndex];
                document.getElementById('improvementTarget').textContent = improvementTarget + '%';
                document.getElementById('improvementResult').textContent = additionalConversions;
            }

            // Update market comparison insight
            const overallConversionRate = parseFloat(document.getElementById('overallConversionRate').textContent);
            const marketAverage = 7.5; // Market average conversion rate

            if (overallConversionRate > marketAverage) {
                document.getElementById('marketComparison').textContent = 'acima da média';
                document.getElementById('marketComparison').className = 'font-medium text-green-600';
            } else if (overallConversionRate < marketAverage) {
                document.getElementById('marketComparison').textContent = 'abaixo da média';
                document.getElementById('marketComparison').className = 'font-medium text-red-600';
            } else {
                document.getElementById('marketComparison').textContent = 'na média';
                document.getElementById('marketComparison').className = 'font-medium text-yellow-600';
            }

        } catch (error) {
            console.error('Error creating bottleneck analysis chart:', error);
            window.debugChart('bottleneckAnalysisChart', {}, error);
            document.getElementById('bottleneckAnalysisChart').parentNode.innerHTML =
                '<div class="flex items-center justify-center h-full">' +
                '<p class="text-gray-500 text-sm">Erro ao carregar dados</p>' +
                '</div>';
        }

        // Create conversion by state chart
        try {
            const stateConversion = chartData.state_conversion || {};

            if (Object.keys(stateConversion).length === 0) {
                document.getElementById('conversionByStateChart').parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full">' +
                    '<p class="text-gray-500 text-sm">Dados não disponíveis</p>' +
                    '</div>';
                console.warn('No data available for conversion by state chart');
                return;
            }

            const stateLabels = Object.keys(stateConversion);
            const conversionValues = Object.values(stateConversion).map(v => parseFloat(v.replace(',', '.')));

            if (typeof AmigoDH !== 'undefined' && AmigoDH.createBarChart) {
                AmigoDH.createBarChart('conversionByStateChart', stateLabels, conversionValues, 'Taxa de Conversão (%)', {
                    backgroundColor: AmigoDH.colors && AmigoDH.colors.blue ? AmigoDH.colors.blue[4] : 'rgba(168, 85, 247, 0.7)',
                    borderColor: AmigoDH.colors && AmigoDH.colors.blue ? AmigoDH.colors.blue[6] : 'rgba(196, 181, 253, 1)',
                    tooltipCallback: function(value) {
                        return 'Conversão: ' + (AmigoDH.formatPercentage ? AmigoDH.formatPercentage(value) : value.toFixed(1)) + '%';
                    },
                    yAxisCallback: function(value) {
                        return value + '%';
                    }
                });
            } else {
                // Fallback to native Chart.js
                new Chart(document.getElementById('conversionByStateChart').getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: stateLabels,
                        datasets: [{
                            label: 'Taxa de Conversão (%)',
                            data: conversionValues,
                            backgroundColor: 'rgba(59, 130, 246, 0.7)',
                            borderColor: 'rgba(59, 130, 246, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return value + '%';
                                    }
                                }
                            }
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return 'Conversão: ' + parseFloat(context.raw).toFixed(1) + '%';
                                    }
                                }
                            }
                        }
                    }
                });
            }
            console.log('Conversion by state chart created successfully');
        } catch (error) {
            console.error('Error creating conversion by state chart:', error);
            window.debugChart('conversionByStateChart', {}, error);
            document.getElementById('conversionByStateChart').parentNode.innerHTML =
                '<div class="flex items-center justify-center h-full">' +
                '<p class="text-gray-500 text-sm">Erro ao carregar dados</p>' +
                '</div>';
        }

        // Create implementation historical chart
        try {
            const implementationHistoricalData = chartData.implementation_historical_phases || {};
            const historicalLabels = Object.keys(implementationHistoricalData);
            const historicalValues = Object.values(implementationHistoricalData).map(v => parseInt(v));

            if (typeof AmigoDH !== 'undefined' && AmigoDH.createLineChart) {
                AmigoDH.createLineChart('implementationHistoricalChart', historicalLabels, historicalValues, 'Quantidade', {
                    backgroundColor: 'rgba(152, 207, 255, 0.2)',
                    borderColor: AmigoDH.colors && AmigoDH.colors.primary ? AmigoDH.colors.primary : 'rgba(59, 130, 246, 1)',
                    fill: true
                });
            } else {
                // Fallback to native Chart.js
                new Chart(document.getElementById('implementationHistoricalChart').getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: historicalLabels,
                        datasets: [{
                            label: 'Quantidade',
                            data: historicalValues,
                            backgroundColor: 'rgba(152, 207, 255, 0.2)',
                            borderColor: 'rgba(59, 130, 246, 1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.3
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            console.log('Implementation historical chart created successfully');
        } catch (error) {
            console.error('Error creating implementation historical chart:', error);
            window.debugChart('implementationHistoricalChart', {}, error);
        }

        // Create monthly conversion rates chart
        try {
            const conversionByMonth = chartData.conversion_by_month || {};
            const monthLabels = conversionByMonth.labels || [];
            const leadToOppRates = conversionByMonth.lead_to_opp_rates || [];
            const oppToImplRates = conversionByMonth.opp_to_impl_rates || [];
            const overallRates = conversionByMonth.overall_rates || [];

            console.log('Monthly conversion data:', {
                labels: monthLabels,
                leadToOppRates: leadToOppRates,
                oppToImplRates: oppToImplRates,
                overallRates: overallRates
            });

            if (monthLabels.length === 0) {
                document.getElementById('monthlyConversionChart').parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full">' +
                    '<p class="text-gray-500 text-sm">Dados não disponíveis</p>' +
                    '</div>';
                console.warn('No data available for monthly conversion chart');
                return;
            }

            // Create multi-line chart with direct data
            new Chart(document.getElementById('monthlyConversionChart').getContext('2d'), {
                type: 'line',
                data: {
                    labels: monthLabels,
                    datasets: [
                        {
                            label: 'Lead → Oportunidade',
                            data: leadToOppRates,
                            backgroundColor: 'rgba(59, 130, 246, 0.2)',
                            borderColor: 'rgba(59, 130, 246, 1)',
                            borderWidth: 2,
                            tension: 0.3,
                            fill: false
                        },
                        {
                            label: 'Oportunidade → Implantação',
                            data: oppToImplRates,
                            backgroundColor: 'rgba(156, 163, 175, 0.2)',
                            borderColor: 'rgba(156, 163, 175, 1)',
                            borderWidth: 2,
                            tension: 0.3,
                            fill: false
                        },
                        {
                            label: 'Conversão Geral',
                            data: overallRates,
                            backgroundColor: 'rgba(79, 70, 229, 0.2)',
                            borderColor: 'rgba(79, 70, 229, 1)',
                            borderWidth: 2,
                            tension: 0.3,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + parseFloat(context.raw).toFixed(1) + '%';
                                }
                            }
                        }
                    }
                }
            });

            console.log('Monthly conversion chart created successfully');
        } catch (error) {
            console.error('Error creating monthly conversion chart:', error);
            console.error(error.stack);
            window.debugChart('monthlyConversionChart', {}, error);
            document.getElementById('monthlyConversionChart').parentNode.innerHTML =
                '<div class="flex items-center justify-center h-full">' +
                '<p class="text-gray-500 text-sm">Erro ao carregar dados</p>' +
                '</div>';
        }

        // Create quarterly conversion chart
        try {
            const seasonalAnalysis = chartData.seasonal_analysis || {};
            const quarterLabels = seasonalAnalysis.quarter_labels || [];
            const quarterRates = seasonalAnalysis.quarter_rates || [];

            console.log('Quarterly conversion data:', {
                labels: quarterLabels,
                rates: quarterRates
            });

            if (quarterLabels.length === 0) {
                document.getElementById('quarterlyConversionChart').parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full">' +
                    '<p class="text-gray-500 text-sm">Dados não disponíveis</p>' +
                    '</div>';
                console.warn('No data available for quarterly conversion chart');
                return;
            }

            // Create bar chart with direct data
            new Chart(document.getElementById('quarterlyConversionChart').getContext('2d'), {
                type: 'bar',
                data: {
                    labels: quarterLabels,
                    datasets: [{
                        label: 'Taxa de Conversão (%)',
                        data: quarterRates,
                        backgroundColor: AmigoDH.colors && AmigoDH.colors.blue ? AmigoDH.colors.blue[2] : 'rgba(99, 102, 241, 0.7)',
                        borderColor: AmigoDH.colors && AmigoDH.colors.primary ? AmigoDH.colors.primary : 'rgba(59, 130, 246, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'Conversão: ' + parseFloat(context.raw).toFixed(1) + '%';
                                }
                            }
                        }
                    }
                }
            });
            console.log('Quarterly conversion chart created successfully');
        } catch (error) {
            console.error('Error creating quarterly conversion chart:', error);
            console.error(error.stack);
            window.debugChart('quarterlyConversionChart', {}, error);
            document.getElementById('quarterlyConversionChart').parentNode.innerHTML =
                '<div class="flex items-center justify-center h-full">' +
                '<p class="text-gray-500 text-sm">Erro ao carregar dados</p>' +
                '</div>';
        }

        // Create monthly seasonal chart
        try {
            const seasonalAnalysis = chartData.seasonal_analysis || {};
            const monthLabels = seasonalAnalysis.month_labels || [];
            const monthRates = seasonalAnalysis.month_rates || [];

            console.log('Monthly seasonal data:', {
                labels: monthLabels,
                rates: monthRates
            });

            if (monthLabels.length === 0) {
                document.getElementById('monthlySeasonalChart').parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full">' +
                    '<p class="text-gray-500 text-sm">Dados não disponíveis</p>' +
                    '</div>';
                console.warn('No data available for monthly seasonal chart');
                return;
            }

            // Create bar chart with direct data
            new Chart(document.getElementById('monthlySeasonalChart').getContext('2d'), {
                type: 'bar',
                data: {
                    labels: monthLabels,
                    datasets: [{
                        label: 'Taxa de Conversão (%)',
                        data: monthRates,
                        backgroundColor: 'rgba(156, 163, 175, 0.7)',
                        borderColor: 'rgba(107, 114, 128, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'Conversão: ' + parseFloat(context.raw).toFixed(1) + '%';
                                }
                            }
                        }
                    }
                }
            });
            console.log('Monthly seasonal chart created successfully');
        } catch (error) {
            console.error('Error creating monthly seasonal chart:', error);
            console.error(error.stack);
            window.debugChart('monthlySeasonalChart', {}, error);
            document.getElementById('monthlySeasonalChart').parentNode.innerHTML =
                '<div class="flex items-center justify-center h-full">' +
                '<p class="text-gray-500 text-sm">Erro ao carregar dados</p>' +
                '</div>';
        }

        // Create velocity distribution chart
        try {
            const conversionVelocity = chartData.conversion_velocity || {};
            const velocityDistribution = conversionVelocity.velocity_distribution || {};

            if (Object.keys(velocityDistribution).length === 0) {
                document.getElementById('velocityDistributionChart').parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full">' +
                    '<p class="text-gray-500 text-sm">Dados não disponíveis</p>' +
                    '</div>';
                console.warn('No data available for velocity distribution chart');
                return;
            }

            console.log('Velocity distribution data:', velocityDistribution);

            // Sort the time labels in the correct order
            const timeOrder = ['0-7 dias', '8-14 dias', '15-30 dias', '31-60 dias', '61-90 dias', '90+ dias'];
            const sortedLabels = Object.keys(velocityDistribution).sort((a, b) => {
                return timeOrder.indexOf(a) - timeOrder.indexOf(b);
            });

            const velocityValues = sortedLabels.map(label => parseInt(velocityDistribution[label] || 0));

            if (typeof AmigoDH !== 'undefined' && AmigoDH.createBarChart) {
                AmigoDH.createBarChart('velocityDistributionChart', sortedLabels, velocityValues, 'Quantidade de Leads', {
                    backgroundColor: AmigoDH.colors && AmigoDH.colors.blue ? AmigoDH.colors.blue[2] : 'rgba(99, 102, 241, 0.7)',
                    borderColor: AmigoDH.colors && AmigoDH.colors.primary ? AmigoDH.colors.primary : 'rgba(59, 130, 246, 1)'
                });
            } else {
                // Fallback to native Chart.js
                new Chart(document.getElementById('velocityDistributionChart').getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: sortedLabels,
                        datasets: [{
                            label: 'Quantidade de Leads',
                            data: velocityValues,
                            backgroundColor: 'rgba(59, 130, 246, 0.7)',
                            borderColor: 'rgba(59, 130, 246, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            console.log('Velocity distribution chart created successfully');
        } catch (error) {
            console.error('Error creating velocity distribution chart:', error);
            console.error(error.stack);
            window.debugChart('velocityDistributionChart', {}, error);
            document.getElementById('velocityDistributionChart').parentNode.innerHTML =
                '<div class="flex items-center justify-center h-full">' +
                '<p class="text-gray-500 text-sm">Erro ao carregar dados</p>' +
                '</div>';
        }

        // Create velocity by university chart
        try {
            const conversionVelocity = chartData.conversion_velocity || {};
            const velocityByUniversity = conversionVelocity.velocity_by_university || {};

            if (Object.keys(velocityByUniversity).length === 0) {
                document.getElementById('velocityByUniversityChart').parentNode.innerHTML =
                    '<div class="flex items-center justify-center h-full">' +
                    '<p class="text-gray-500 text-sm">Dados não disponíveis</p>' +
                    '</div>';
                console.warn('No data available for velocity by university chart');
                return;
            }

            console.log('Velocity by university data:', velocityByUniversity);

            // Get top 10 universities by velocity (lowest days to conversion)
            const sortedUniversities = Object.keys(velocityByUniversity)
                .sort((a, b) => parseInt(velocityByUniversity[a]) - parseInt(velocityByUniversity[b]))
                .slice(0, 10);

            const universityLabels = sortedUniversities;
            const velocityValues = sortedUniversities.map(uni => parseInt(velocityByUniversity[uni]));

            if (typeof AmigoDH !== 'undefined' && AmigoDH.createBarChart) {
                AmigoDH.createBarChart('velocityByUniversityChart', universityLabels, velocityValues, 'Dias para Conversão', {
                    backgroundColor: 'rgba(156, 163, 175, 0.7)',
                    borderColor: 'rgba(107, 114, 128, 1)',
                    indexAxis: 'y'
                });
            } else {
                // Fallback to native Chart.js
                new Chart(document.getElementById('velocityByUniversityChart').getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: universityLabels,
                        datasets: [{
                            label: 'Dias para Conversão',
                            data: velocityValues,
                            backgroundColor: 'rgba(156, 163, 175, 0.7)',
                            borderColor: 'rgba(107, 114, 128, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        indexAxis: 'y',
                        scales: {
                            x: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            console.log('Velocity by university chart created successfully');
        } catch (error) {
            console.error('Error creating velocity by university chart:', error);
            console.error(error.stack);
            window.debugChart('velocityByUniversityChart', {}, error);
            document.getElementById('velocityByUniversityChart').parentNode.innerHTML =
                '<div class="flex items-center justify-center h-full">' +
                '<p class="text-gray-500 text-sm">Erro ao carregar dados</p>' +
                '</div>';
        }
    });
</script>
{% endblock %}
