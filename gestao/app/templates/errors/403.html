{% extends "error_base.html" %}

{% block title %}Acesso Negado - {{ app_name|e }}{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <!-- Icon -->
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
            <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
        </div>

        <!-- Title -->
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Acesso Negado</h1>
        
        <!-- Message -->
        <p class="text-gray-600 mb-6">
            Você não tem permissão para acessar esta página. Entre em contato com o administrador se precisar de acesso.
        </p>

        <!-- User Info -->
        {% if session.username %}
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <p class="text-sm text-gray-700">
                <span class="font-medium">Usuário:</span> {{ session.username }}
            </p>
            <p class="text-sm text-gray-700">
                <span class="font-medium">Perfil:</span> 
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ 'bg-purple-100 text-purple-800' if session.role == 'admin' else 'bg-gray-100 text-gray-800' }}">
                    {{ 'Administrador' if session.role == 'admin' else 'Usuário' }}
                </span>
            </p>
        </div>
        {% endif %}

        <!-- Actions -->
        <div class="space-y-3">
            <a href="{{ url_for('dashboard.index') }}" 
               class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                Voltar ao Dashboard
            </a>
            
            <button onclick="history.back()" 
                    class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Voltar à Página Anterior
            </button>
        </div>

        <!-- Help Text -->
        <div class="mt-6 pt-6 border-t border-gray-200">
            <p class="text-xs text-gray-500">
                Se você acredita que deveria ter acesso a esta página, entre em contato com o administrador do sistema.
            </p>
        </div>
    </div>
</div>
{% endblock %}
