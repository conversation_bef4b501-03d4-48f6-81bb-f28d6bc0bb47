{% macro error_message(title, message) %}
<div class="alert alert-warning" role="alert">
    <h4 class="alert-heading">{{ title|e }}</h4>
    <p>{{ message|e }}</p>
</div>
{% endmacro %}

{% macro chart_error(chart_name) %}
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{{ chart_name|e }}</h6>
    </div>
    <div class="card-body">
        <div class="alert alert-warning" role="alert">
            <h4 class="alert-heading">Dados não disponíveis</h4>
            <p>Não foi possível carregar os dados para este gráfico. Verifique se os dados necessários estão disponíveis no arquivo CSV.</p>
            <hr>
            <p class="mb-0">Colunas necessárias: 
                {% if chart_name == 'MRR Mensal' or chart_name == 'MRR Cumulativo' %}
                    <code>Status_Implantacao</code>, <code>Valor Mensalidade</code>, <code>Data_Criacao_Implantacao</code>
                {% elif chart_name == 'Funil Comercial' %}
                    <code>Lead_id</code>, <code>Oportunidade_id</code>, <code>Fase_Implantacao</code>, <code>Status_Implantacao</code>
                {% elif chart_name == 'Tipos de Oportunidades' %}
                    <code>Produto</code>
                {% elif chart_name == 'Timeline de Implantações' %}
                    <code>Data_Criacao_Implantacao</code>, <code>Fase_Implantacao</code>
                {% else %}
                    Verifique a documentação para mais detalhes.
                {% endif %}
            </p>
        </div>
    </div>
</div>
{% endmacro %}
