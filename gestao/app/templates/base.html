<!DOCTYPE html>
<html lang="pt-br">
<head>
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data:; font-src 'self' data:;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}DataHub Amigo One - Visão 360°{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                primary: '#60a5fa',
                secondary: '#93c5fd',
                tertiary: '#dbeafe',
                success: '#34C759',
                warning: '#FF9500',
                danger: '#FF3B30',
                gray: {
                  DEFAULT: '#8E8E93',
                  light: '#AEAEB2',
                  lighter: '#C7C7CC',
                  lightest: '#D1D1D6',
                  extralight: '#E5E5EA',
                  ultralight: '#F2F2F7'
                },
                label: {
                    DEFAULT: '#000000',
                    secondary: 'rgba(60, 60, 67, 0.6)',
                    tertiary: 'rgba(60, 60, 67, 0.3)',
                    quaternary: 'rgba(60, 60, 67, 0.18)'
                }
              },
              borderRadius: {
                  'view': '10px',
                  'control': '7px'
              }
            }
          }
        }
    </script>
    <style>
        /* Custom styles */
        .hero-gradient {
            background: linear-gradient(135deg, #f0f7ff 0%, #e1effe 100%);
        }

        /* Enhanced card hover effect */
        .card-hover {
            transition: all 0.3s ease-in-out;
            border-bottom: 3px solid transparent;
        }

        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-bottom: 3px solid #3b82f6;
        }

        /* Scrollbar styles */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Button styles */
        .btn-primary {
            transition: all 0.2s ease;
            box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2), 0 2px 4px -1px rgba(59, 130, 246, 0.1);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.3), 0 4px 6px -2px rgba(59, 130, 246, 0.2);
        }

        /* Table styles */
        table {
            border-collapse: separate;
            border-spacing: 0;
        }

        table th {
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
        }

        table tr:hover td {
            background-color: #f9fafb;
        }

        /* Simplified layout without scaling conflicts */
        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Standard viewport */
        html {
            margin: 0;
            padding: 0;
        }

        /* Remove extra space at bottom of page */

        footer {
            margin-bottom: 0 !important;
            padding-bottom: 0.25rem !important;
        }

        html, body {
            height: 100%;
            min-height: 100vh;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Sidebar styling */
        .sidebar-section {
            margin-bottom: 2rem; /* 32px between sections - more spacing */
        }

        /* Override sidebar link styles - more minimal */
        nav a {
            padding: 0.5rem 0.75rem !important; /* py-2 px-3 */
            font-size: 0.875rem !important; /* text-sm */
            line-height: 1.25rem !important;
        }

        /* Sidebar icons - smaller */
        nav a svg {
            width: 1rem !important; /* h-4 w-4 */
            height: 1rem !important;
        }

        /* Layout adjustments */
        @media (min-width: 768px) {
            main {
                margin-left: 17rem; /* 240px sidebar + 32px breathing space */
                margin-right: 1.5rem; /* 24px right margin */
                width: auto; /* Let it flow naturally */
                max-width: none; /* Remove restrictions */
                min-width: 0; /* Allow flexibility */
            }
        }

        /* Mobile adjustments */
        @media (max-width: 767px) {
            main {
                margin-left: 0;
                margin-right: 0;
                width: 100%;
                max-width: 100%;
            }
        }

        /* Ensure sidebar doesn't interfere */
        aside {
            height: calc(100vh - 5rem); /* Full height minus header */
        }

        /* Breathing space divider styling */
        .breathing-divider {
            background: linear-gradient(90deg,
                rgba(249, 250, 251, 0.9) 0%,
                rgba(249, 250, 251, 0.6) 30%,
                rgba(249, 250, 251, 0.3) 70%,
                transparent 100%);
            backdrop-filter: blur(2px);
            transition: all 0.3s ease;
            border-right: 1px solid rgba(229, 231, 235, 0.3);
        }

        /* Simplified content container */
        main > div {
            width: 100%;
            box-sizing: border-box;
        }

        /* Basic grid styling */
        .grid {
            display: grid;
            gap: 1.5rem;
        }

        .bg-white {
            box-sizing: border-box;
        }

        /* Chart containers optimization */
        .chart-container, canvas {
            width: 100% !important;
            max-width: 100% !important;
        }

        /* Tooltip Styles */
        .tooltip-container {
            position: relative;
            display: inline-block;
        }

        .tooltip-trigger {
            cursor: help;
            color: #6b7280;
            transition: color 0.2s;
            margin-left: 4px;
        }

        .tooltip-trigger:hover {
            color: #3b82f6;
        }

        .tooltip-content {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #1f2937;
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            font-size: 13px;
            line-height: 1.5;
            max-width: 450px;
            white-space: normal;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
            margin-bottom: 8px;
        }

        .tooltip-content::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: #1f2937;
        }

        .tooltip-container:hover .tooltip-content {
            opacity: 1;
            visibility: visible;
        }

        .tooltip-section {
            margin-bottom: 12px;
        }

        .tooltip-section:last-child {
            margin-bottom: 0;
        }

        .tooltip-title {
            font-weight: 600;
            color: #60a5fa;
            margin-bottom: 6px;
            font-size: 14px;
        }

        .tooltip-formula {
            font-family: 'Courier New', monospace;
            background: rgba(255, 255, 255, 0.1);
            padding: 4px 6px;
            border-radius: 4px;
            font-size: 12px;
            display: block;
            margin: 4px 0;
        }

        .tooltip-columns {
            color: #d1d5db;
            font-size: 12px;
            margin-top: 4px;
        }

        .tooltip-datalineage {
            color: #fbbf24;
            font-size: 12px;
            font-style: italic;
            margin-top: 6px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
    <!-- HYBRID STRUCTURED: Static assets loaded server-side -->
    <script src="{{ url_for('static', filename='js/utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/charts.js') }}"></script>
    <!-- HYBRID STRUCTURED: Local hybrid management -->
    <script src="{{ url_for('static', filename='js/hybrid-manager.js') }}"></script>

    <!-- HYBRID STRUCTURED: Global configuration for dynamic content -->
    <script>
        // Global app configuration - STRUCTURED HYBRID PATTERN
        window.AmigoDH = {
            // Static configuration (SSR)
            APP_URLS: {
                business: "https://5mmmbhl1-5000.brs.devtunnels.ms/",
                product: "https://5mmmbhl1-5001.brs.devtunnels.ms/"
            },
            // API endpoints for dynamic content (CSR)
            API_ENDPOINTS: {
                realtime: '/api/business/realtime',
                analytics: '/api/business/analytics',
                charts: '/api/business/charts'
            },
            // Debug utilities
            debugChart: function(elementId, chartData, error) {
                if (typeof console !== 'undefined' && console.group) {
                    console.group('Chart Debug: ' + elementId);
                    console.log('Element exists:', !!document.getElementById(elementId));
                    console.log('Chart data:', chartData);
                    if (error) {
                        console.error('Error:', error);
                    }
                    console.groupEnd();
                }
            }
        };
    </script>
    {% block head %}{% endblock %}
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- Header - Fixed at top -->
    <header class="w-full bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
        <div class="w-full px-4 sm:px-6 lg:px-8 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <a href="{{ url_for('dashboard.index') }}" class="flex items-center">
                    <img src="{{ url_for('static', filename='img/image.png') }}" alt="Logo" class="h-16 mr-2">
                    <span class="text-blue-500 mx-2 font-medium">|</span>
                    <span class="text-2xl font-bold text-gray-900">DataHub</span>
                </a>
                <span class="ml-2 text-sm text-label-secondary">DataHub Amigo One - Negócios</span>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Ir para Produto Button - Minimal -->
                <button id="goto-product-btn" class="text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors flex items-center">
                    <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    Ir para Produto
                </button>

                <!-- User Avatar -->
                <a href="{{ url_for('dashboard.index') }}" class="flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <span class="text-xs font-bold text-white">
                            {{ session.username[0].upper() if session.username else 'U' }}
                        </span>
                    </div>
                    <span class="text-sm text-gray-700 hidden sm:block">{{ session.username|e if session.username else 'Usuário' }}</span>
                </a>
            </div>
        </div>
    </header>

    <!-- Spacer to account for fixed header -->
    <div class="h-20"></div>

    <!-- Layout Container -->
    <div class="min-h-screen">
        <!-- Sidebar - Colapsável em dispositivos móveis -->
        <aside class="w-60 bg-white shadow-sm hidden md:block border-r border-gray-200 fixed left-0 top-20 h-full z-10">
            <nav class="mt-12 px-4 pb-4 h-full flex flex-col">
                <!-- ANALYTICS Section -->
                <div class="sidebar-section">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Analytics</h3>

                    <a href="{{ url_for('dashboard.index') }}" class="group flex items-center px-3 py-3 text-base font-medium rounded-md transition-all duration-200 {% if request.endpoint == 'dashboard.index' %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-600 hover:bg-gray-50 hover:text-blue-600{% endif %}">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint == 'dashboard.index' %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                        Dashboard
                    </a>

                    <a href="{{ url_for('lead.index') }}" class="group flex items-center px-3 py-3 text-base font-medium rounded-md transition-all duration-200 {% if request.endpoint.startswith('lead.') %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-600 hover:bg-gray-50 hover:text-blue-600{% endif %}">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('lead.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                        Leads
                    </a>

                    <a href="{{ url_for('opportunity.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if request.endpoint.startswith('opportunity.') %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-600 hover:bg-gray-50 hover:text-blue-600{% endif %}">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('opportunity.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Oportunidades
                    </a>

                    <a href="{{ url_for('implementation.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if request.endpoint.startswith('implementation.') %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-600 hover:bg-gray-50 hover:text-blue-600{% endif %}">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('implementation.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Implantações
                    </a>

                    <a href="{{ url_for('university.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if request.endpoint.startswith('university.') %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-600 hover:bg-gray-50 hover:text-blue-600{% endif %}">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('university.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path d="M12 14l9-5-9-5-9 5 9 5z" />
                            <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                        </svg>
                        Universidades
                    </a>

                    <a href="{{ url_for('class.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if request.endpoint.startswith('class.') %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-600 hover:bg-gray-50 hover:text-blue-600{% endif %}">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('class.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                        Turmas
                    </a>

                    <a href="{{ url_for('responsible.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if request.endpoint.startswith('responsible.') %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-600 hover:bg-gray-50 hover:text-blue-600{% endif %}">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('responsible.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Responsáveis
                    </a>

                    <a href="#" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-400 cursor-not-allowed" title="Funcionalidade removida">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('conversion.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        Conversão
                    </a>

                    <a href="#" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-400 cursor-not-allowed" title="Funcionalidade removida">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('subscription.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Assinaturas (MRR)
                    </a>

                    <a href="#" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-400 cursor-not-allowed" title="Funcionalidade removida">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('coupon.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                        </svg>
                        Cupons
                    </a>

                    <a href="#" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-400 cursor-not-allowed" title="Funcionalidade removida">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('cancellation.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        Cancelamentos
                    </a>

                    <a href="#" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-400 cursor-not-allowed" title="Funcionalidade removida">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint and request.endpoint.startswith('active_users.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                        Usuários Ativos
                    </a>


                </div>

                <!-- INTELLIGENCE Section -->
                <div class="sidebar-section border-t border-gray-100 pt-2 mt-2">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Intelligence</h3>

                    <a href="#" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-400 cursor-not-allowed" title="Funcionalidade removida">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint and request.endpoint.startswith('intelligence.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                        Intelligence Index
                    </a>

                    <a href="#" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-400 cursor-not-allowed" title="Funcionalidade removida">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint and request.endpoint.startswith('clustering.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        Clusterizacao
                    </a>

                    <a href="#" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-400 cursor-not-allowed" title="Funcionalidade removida">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint and request.endpoint.startswith('go_to_market.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                        Go-to-Market
                    </a>


                </div>

                <!-- KNOWLEDGE Section -->
                <div class="sidebar-section border-t border-gray-100 pt-4 mt-12">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Knowledge</h3>

                    <a href="#" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-400 cursor-not-allowed" title="Funcionalidade removida">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('database_quality.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5l8 4m0 0l-8 4m8-4H4" />
                        </svg>
                        Base de Dados
                    </a>

                    <a href="#" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-400 cursor-not-allowed" title="Funcionalidade removida">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('business_rules.') %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Regras de Negócio
                    </a>
                </div>

                <!-- ADMIN Section (only for admin users) -->
                {% if session.username in ['admin', 'TTK', 'bruno@abreu', 'bruno@bruno'] %}
                <div class="sidebar-section border-t border-gray-100 pt-2 mt-2">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Admin</h3>

                    <a href="#" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 text-gray-400 cursor-not-allowed" title="Funcionalidade removida">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('admin.users') %}text-red-600{% else %}text-gray-400 group-hover:text-red-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                        Gerenciar Usuários
                    </a>

                    <a href="#" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 text-gray-400 cursor-not-allowed" title="Funcionalidade removida">
                        <svg class="mr-3 h-4 w-4 {% if request.endpoint.startswith('admin.monitoring') %}text-red-600{% else %}text-gray-400 group-hover:text-red-600{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        Monitoramento
                    </a>
                </div>
                {% endif %}

                <!-- Logout Button -->
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="w-full" title="Logout removido">
                        <div class="w-full group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                            <svg class="mr-3 h-4 w-4 text-red-500 group-hover:text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                            Sair (Desabilitado)
                        </div>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Breathing Space Divider -->
        <div class="breathing-divider hidden md:block fixed left-60 top-20 h-full w-8" style="z-index: 1;"></div>

        <!-- Main Content -->
        <main class="min-h-screen bg-gray-50" style="position: relative; z-index: 2;">
            <div class="px-6 py-6">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="fixed top-20 right-4 z-50 space-y-2 max-w-md flash-messages">
                            {% for category, message in messages %}
                                <div class="bg-white border-l-4 {% if category == 'error' %}border-red-500{% elif category == 'success' %}border-green-500{% elif category == 'warning' %}border-yellow-500{% else %}border-blue-500{% endif %} p-4 shadow-lg rounded-md">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            {% if category == 'error' %}
                                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                                </svg>
                                            {% elif category == 'success' %}
                                                <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                </svg>
                                            {% else %}
                                                <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                                </svg>
                                            {% endif %}
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <p class="text-sm {% if category == 'error' %}text-red-800{% elif category == 'success' %}text-green-800{% else %}text-blue-800{% endif %}">
                                                {{ message|e }}
                                            </p>
                                        </div>
                                        <div class="ml-4 flex-shrink-0">
                                            <button type="button" class="inline-flex {% if category == 'error' %}text-red-400 hover:text-red-600{% elif category == 'success' %}text-green-400 hover:text-green-600{% else %}text-blue-400 hover:text-blue-600{% endif %}" onclick="this.parentElement.parentElement.parentElement.remove()">
                                                <span class="sr-only">Fechar</span>
                                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        <!-- Auto-hide flash messages after 5 seconds -->
                        <script>
                            setTimeout(function() {
                                const flashMessages = document.querySelectorAll('.fixed.top-20 > div');
                                flashMessages.forEach(function(message) {
                                    message.style.transition = 'opacity 0.5s ease-out';
                                    message.style.opacity = '0';
                                    setTimeout(function() {
                                        message.remove();
                                    }, 500);
                                });
                            }, 5000);
                        </script>
                    {% endif %}
                {% endwith %}

                <!-- Page Content -->
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 py-1">
        <div class="w-full px-4 sm:px-6 lg:px-8">
            <p class="text-center text-xs text-label-secondary">
                &copy; 2023 DataHub Amigo One - Visão 360° | Versão 1.0.0
            </p>
        </div>
    </footer>

    <!-- Common Scripts -->
    <script src="{{ url_for('static', filename='js/shared.js') }}"></script>
    <script src="{{ url_for('static', filename='js/redirect.js') }}"></script>

    <!-- Modal de Redirecionamento para o App de Produto -->
    <div id="redirect-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6 transform transition-all">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Redirecionamento</h3>
                <button id="close-modal" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="mb-5">
                <p class="text-gray-600 mb-3">Você está sendo redirecionado para o aplicativo de Produto.</p>
                <div class="bg-blue-50 p-3 rounded-md">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p class="text-sm text-blue-700">Você está saindo do aplicativo de <strong>Negócios</strong> e indo para o aplicativo de <strong>Produto</strong>.</p>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3">
                <button id="cancel-redirect" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    Cancelar
                </button>
                <a id="continue-redirect" href="https://5mmmbhl1-5001.brs.devtunnels.ms/" class="px-4 py-2 bg-blue-500 text-white text-sm font-medium rounded-md hover:bg-blue-600">
                    Continuar
                </a>
            </div>
        </div>
    </div>

    <!-- Page-specific Scripts -->
    {% block scripts %}{% endblock %}

    <script>
        // Script para o modal de redirecionamento
        document.addEventListener('DOMContentLoaded', function() {
            const gotoProductBtn = document.getElementById('goto-product-btn');
            const redirectModal = document.getElementById('redirect-modal');
            const closeModal = document.getElementById('close-modal');
            const cancelRedirect = document.getElementById('cancel-redirect');
            const continueRedirect = document.getElementById('continue-redirect');

            if (!gotoProductBtn || !redirectModal || !closeModal || !cancelRedirect) {
                return;
            }

            // Atualizar o link de redirecionamento com a URL correta
            if (continueRedirect && window.AmigoDH && window.AmigoDH.APP_URLS) {
                continueRedirect.href = window.AmigoDH.APP_URLS.product;
            }

            gotoProductBtn.addEventListener('click', function() {
                redirectModal.classList.remove('hidden');
            });

            closeModal.addEventListener('click', function() {
                redirectModal.classList.add('hidden');
            });

            cancelRedirect.addEventListener('click', function() {
                redirectModal.classList.add('hidden');
            });

            // Fechar o modal ao clicar fora dele
            redirectModal.addEventListener('click', function(e) {
                if (e.target === redirectModal) {
                    redirectModal.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>
