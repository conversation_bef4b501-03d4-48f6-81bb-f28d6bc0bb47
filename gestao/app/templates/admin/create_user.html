<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data:; font-src 'self' data:;">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Criar <PERSON> - Admin DataHub</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="min-h-screen bg-gradient-to-br from-purple-50 to-blue-100 flex items-center justify-center p-4">
    <div class="w-full max-w-lg">
        <div class="bg-white rounded-2xl shadow-xl p-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-user-plus text-white text-2xl"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-900">Criar Novo Usuário</h2>
                <p class="text-gray-600 mt-2">Adicionar usuário ao sistema</p>
            </div>

            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="mb-6 p-4 rounded-lg {% if category == 'error' %}bg-red-50 border border-red-200 text-red-700{% else %}bg-green-50 border border-green-200 text-green-700{% endif %}">
                            <div class="flex items-center">
                                <i class="fas {% if category == 'error' %}fa-exclamation-triangle{% else %}fa-check-circle{% endif %} mr-2"></i>
                                {{ message|e }}
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Form -->
            <form method="POST" class="space-y-6" id="createUserForm">

                <!-- Username -->
                <div>
                    <label for="username" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-user mr-2 text-blue-600"></i>Nome de Usuário
                    </label>
                    <input
                        type="text"
                        id="username"
                        name="username"
                        required
                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300 bg-gray-50 focus:bg-white"
                        placeholder="Digite o nome de usuário"
                    >
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-envelope mr-2 text-green-600"></i>Email
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        required
                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300 bg-gray-50 focus:bg-white"
                        placeholder="Digite o email do usuário"
                    >
                </div>

                <!-- Password -->
                <div>
                    <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-key mr-2 text-red-600"></i>Senha
                    </label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        required
                        minlength="6"
                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300 bg-gray-50 focus:bg-white"
                        placeholder="Digite a senha (mín. 6 caracteres)"
                    >
                    <div class="mt-2">
                        <div class="text-xs text-gray-500">
                            <div id="length-check" class="flex items-center">
                                <i class="fas fa-times text-red-500 mr-1"></i>
                                <span>Pelo menos 6 caracteres</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Role -->
                <div>
                    <label for="role" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-user-tag mr-2 text-purple-600"></i>Função
                    </label>
                    <select
                        id="role"
                        name="role"
                        required
                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300 bg-gray-50 focus:bg-white"
                    >
                        <option value="">Selecione uma função</option>
                        <option value="user">Usuário</option>
                        <option value="admin">Administrador</option>
                    </select>
                </div>

                <!-- Role Description -->
                <div id="role-description" class="hidden">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-blue-600 mr-2 mt-0.5"></i>
                            <div class="text-sm text-blue-800">
                                <p class="font-semibold mb-1">Permissões da Função:</p>
                                <div id="role-permissions" class="text-xs space-y-1"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Info -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <i class="fas fa-shield-alt text-green-600 mr-2 mt-0.5"></i>
                        <div class="text-sm text-green-800">
                            <p class="font-semibold mb-1">Segurança:</p>
                            <ul class="text-xs space-y-1">
                                <li>• Senha será criptografada com bcrypt</li>
                                <li>• Dados armazenados em SQLite local</li>
                                <li>• Logs de auditoria automáticos</li>
                                <li>• Validação de sessão ativa</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex space-x-4">
                    <button
                        type="button"
                        onclick="window.history.back()"
                        class="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-3 px-4 rounded-xl transition-all duration-300"
                    >
                        <i class="fas fa-arrow-left mr-2"></i>Voltar
                    </button>
                    <button
                        type="submit"
                        id="submitBtn"
                        class="flex-1 bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-[1.02] focus:ring-4 focus:ring-purple-300"
                    >
                        <i class="fas fa-user-plus mr-2"></i>Criar Usuário
                    </button>
                </div>
            </form>

            <!-- Security Footer -->
            <div class="mt-8 pt-6 border-t border-gray-100 text-center">
                <p class="text-xs text-gray-500 mb-2">
                    <i class="fas fa-crown mr-1 text-yellow-600"></i>
                    Painel de Administração - Acesso Restrito
                </p>
                <div class="flex items-center justify-center space-x-3 text-xs text-gray-400">
                    <span><i class="fas fa-database mr-1 text-blue-500"></i>SQLite</span>
                    <span><i class="fas fa-key mr-1 text-green-500"></i>bcrypt</span>
                    <span><i class="fas fa-user-shield mr-1 text-purple-500"></i>Admin</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        const password = document.getElementById('password');
        const role = document.getElementById('role');
        const roleDescription = document.getElementById('role-description');
        const rolePermissions = document.getElementById('role-permissions');
        const lengthCheck = document.getElementById('length-check');

        // Role permissions mapping
        const permissions = {
            'user': [
                'Visualizar dashboard',
                'Visualizar leads e oportunidades',
                'Visualizar implementações',
                'Visualizar universidades e responsáveis',
                'Visualizar turmas e cupons',
                'Visualizar conversões e assinaturas'
            ],
            'admin': [
                'Todas as permissões de usuário',
                'Editar leads e oportunidades',
                'Editar implementações',
                'Editar universidades e responsáveis',
                'Editar turmas e cupons',
                'Gerenciar usuários',
                'Visualizar regras de negócio',
                'Acesso ao painel administrativo'
            ]
        };

        function validatePassword() {
            const pass = password.value;
            const lengthValid = pass.length >= 6;
            const lengthIcon = lengthCheck.querySelector('i');
            const lengthText = lengthCheck.querySelector('span');

            if (lengthValid) {
                lengthIcon.className = 'fas fa-check text-green-500 mr-1';
                lengthText.className = 'text-green-600';
            } else {
                lengthIcon.className = 'fas fa-times text-red-500 mr-1';
                lengthText.className = 'text-red-600';
            }
        }

        function updateRoleDescription() {
            const selectedRole = role.value;

            if (selectedRole && permissions[selectedRole]) {
                roleDescription.classList.remove('hidden');
                rolePermissions.innerHTML = permissions[selectedRole]
                    .map(perm => `<li>• ${perm}</li>`)
                    .join('');
            } else {
                roleDescription.classList.add('hidden');
            }
        }

        password.addEventListener('input', validatePassword);
        role.addEventListener('change', updateRoleDescription);

        // Form submission validation
        document.getElementById('createUserForm').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const pass = password.value;
            const selectedRole = role.value;

            if (!username || !email || !pass || !selectedRole) {
                e.preventDefault();
                alert('Por favor, preencha todos os campos.');
                return;
            }

            if (pass.length < 6) {
                e.preventDefault();
                alert('A senha deve ter pelo menos 6 caracteres.');
                return;
            }

            // Email validation
            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('Por favor, digite um email válido.');
                return;
            }
        });

        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.bg-red-50, .bg-green-50');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            });
        }, 5000);
    </script>
</body>
</html>
