{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section %}

{% block title %}Detalhes da Implantação - {{ app_name|e }}{% endblock %}

{% block content %}
<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Back Button -->
    <div class="mb-6">
        <a href="{{ url_for('implementation.index') }}" class="inline-flex items-center text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Voltar para a lista de implantações
        </a>
    </div>

    <!-- Implementation Header -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Implantação: {{ implementation['Nome do Lead']|e }}</h1>
                <p class="text-gray-500 mt-1">Criada em {{ implementation['Data_Criacao_Implantacao']|e }}</p>
            </div>
            <div class="mt-4 md:mt-0">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                    {% if implementation['Status_Implantacao'] == 'Finalizado' %}
                        bg-green-100 text-green-800
                    {% elif implementation['Status_Implantacao'] == 'Cancelado' %}
                        bg-red-100 text-red-800
                    {% elif implementation['Status_Implantacao'] == 'Em Andamento' %}
                        bg-blue-100 text-blue-800
                    {% else %}
                        bg-gray-100 text-gray-800
                    {% endif %}
                ">
                    {{ implementation['Status_Implantacao']|e }}
                </span>
            </div>
        </div>
    </div>

    <!-- Implementation Information -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Implementation Details -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 md:col-span-2">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Informações da Implantação</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p class="text-sm font-medium text-gray-500">ID da Oportunidade</p>
                    <p class="text-base text-gray-900">{{ implementation['Oportunidade_id']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Data de Criação</p>
                    <p class="text-base text-gray-900">{{ implementation['Data_Criacao_Implantacao']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Lead</p>
                    <p class="text-base text-gray-900">{{ implementation['Nome do Lead']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Fase</p>
                    <p class="text-base text-gray-900">{{ implementation['Fase_Implantacao']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Status</p>
                    <p class="text-base text-gray-900">{{ implementation['Status_Implantacao']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Responsável</p>
                    <p class="text-base text-gray-900">{{ implementation['ResponsableOnboarding']|e }}</p>
                </div>
                
                {% if implementation['ActualStartDate'] != 'Não informado' %}
                <div>
                    <p class="text-sm font-medium text-gray-500">Data de Início</p>
                    <p class="text-base text-gray-900">{{ implementation['ActualStartDate']|e }}</p>
                </div>
                {% endif %}
                
                {% if implementation['DataPrevistaDeFinalização'] != 'Não informado' %}
                <div>
                    <p class="text-sm font-medium text-gray-500">Data Prevista de Finalização</p>
                    <p class="text-base text-gray-900">{{ implementation['DataPrevistaDeFinalização']|e }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Implementation Progress -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Progresso da Implantação</h2>
            
            <div class="mb-4">
                <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700">Progresso</span>
                    <span class="text-sm font-medium text-gray-700">{{ progress_percentage|e }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-primary h-2.5 rounded-full" style="width: {{ progress_percentage|e }}%"></div>
                </div>
            </div>
            
            <div class="mt-6">
                <a href="{{ url_for('opportunity.detail', opp_id=implementation['Oportunidade_id']) }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    Ver Oportunidade
                </a>
            </div>
        </div>
    </div>

    <!-- Milestones -->
    <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Marcos da Implantação</h2>
        
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="relative">
                <!-- Progress Line -->
                <div class="absolute left-5 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                
                <!-- Milestones -->
                <div class="space-y-8 relative">
                    {% for milestone in milestones %}
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center 
                            {% if milestone.date != 'Não informado' %}
                                bg-primary text-white
                            {% else %}
                                bg-gray-200 text-gray-500
                            {% endif %}
                            relative z-10">
                            {% if milestone.date != 'Não informado' %}
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            {% else %}
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {% endif %}
                        </div>
                        <div class="ml-4">
                            <h3 class="text-base font-medium text-gray-900">{{ milestone.label|e }}</h3>
                            <p class="mt-1 text-sm text-gray-500">
                                {% if milestone.date != 'Não informado' %}
                                Concluído em {{ milestone.date|e }}
                                {% else %}
                                Pendente
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
