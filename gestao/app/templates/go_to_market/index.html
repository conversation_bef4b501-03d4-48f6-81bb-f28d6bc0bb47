{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table %}

{% block title %}Estratégia Go-to-Market - {{ app_name|e }}{% endblock %}

{% block content %}

<!-- Hero Section Minimalista -->
<div class="bg-white border-b border-gray-100">
    <div class="w-full px-4 sm:px-6 lg:px-8 py-12">
        <div class="max-w-6xl mx-auto">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-light text-gray-900 mb-3">Go-to-Market Strategy</h1>
                <p class="text-lg text-gray-600 font-light">Plano orquestrado para crescimento de 300% em 90 dias</p>
            </div>

            <!-- KPIs Baseados no Intelligence Index -->
            <div class="grid grid-cols-2 md:grid-cols-6 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-light text-blue-600 mb-1">{{ intelligence_summary.overall_index.composite_index|round(1) if intelligence_summary and intelligence_summary.overall_index else '65.2' }}</div>
                    <div class="text-xs text-gray-500 uppercase tracking-wide">Índice Composto</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-light text-gray-900 mb-1">{{ intelligence_summary.overall_index.components.conversion_rate|round(1) if intelligence_summary and intelligence_summary.overall_index else '18.5' }}%</div>
                    <div class="text-xs text-gray-500 uppercase tracking-wide">Taxa Conversão</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-light text-gray-900 mb-1">{{ gtm_insights.get('total_turmas', 220) if gtm_insights else 220 }}</div>
                    <div class="text-xs text-gray-500 uppercase tracking-wide">Turmas Base</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-light text-gray-900 mb-1">{{ gtm_insights.get('high_potential_segments', [])|length if gtm_insights else 4 }}</div>
                    <div class="text-xs text-gray-500 uppercase tracking-wide">Clusters Premium</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-light text-blue-600 mb-1">300%</div>
                    <div class="text-xs text-gray-500 uppercase tracking-wide">Meta Crescimento</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-light text-gray-900 mb-1">90</div>
                    <div class="text-xs text-gray-500 uppercase tracking-wide">Dias Execução</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Conteúdo Principal -->
<div class="bg-gray-50 min-h-screen">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

        <!-- 1. ORGANOGRAMA DE TIMES -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-12">
            <h2 class="text-xl font-light text-gray-900 mb-8 text-center">Estrutura Organizacional - Growth Team</h2>

            <!-- Organograma Mermaid -->
            <div class="mb-8">
                <div id="orgChart" class="flex justify-center"></div>
            </div>

            <!-- Legenda dos Times -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                <div class="space-y-3">
                    <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Comercial</h3>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                            <span>BDR - Business Development</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                            <span>Sales Reps - Vendas Diretas</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                            <span>Key Accounts - Grandes Contas</span>
                        </div>
                    </div>
                </div>
                <div class="space-y-3">
                    <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Operações</h3>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
                            <span>Onboarding - Implementação</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
                            <span>Ongoing - Customer Success</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
                            <span>Especialistas - Suporte Técnico</span>
                        </div>
                    </div>
                </div>
                <div class="space-y-3">
                    <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Gestão & Suporte</h3>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-blue-600 rounded-full mr-2"></div>
                            <span>Gestor Comercial</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-gray-600 rounded-full mr-2"></div>
                            <span>Gestor de Operações</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                            <span>Time de Comunicação</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 2. RESUMO EXECUTIVO -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-12">
            <h2 class="text-xl font-light text-gray-900 mb-8">Resumo Executivo</h2>

            <!-- Métricas Atuais vs Metas -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
                <div>
                    <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide mb-6">Situação Atual</h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center py-2 border-b border-gray-100">
                            <span class="text-gray-600 font-light">Turmas Ativas</span>
                            <span class="font-light text-gray-900">{{ gtm_insights.get('total_turmas', 220) if gtm_insights else 220 }}</span>
                        </div>
                        <div class="flex justify-between items-center py-2 border-b border-gray-100">
                            <span class="text-gray-600 font-light">Taxa Conversão</span>
                            <span class="font-light text-gray-900">18.5%</span>
                        </div>
                        <div class="flex justify-between items-center py-2 border-b border-gray-100">
                            <span class="text-gray-600 font-light">Receita por Lead</span>
                            <span class="font-light text-gray-900">R$ 850</span>
                        </div>
                        <div class="flex justify-between items-center py-2 border-b border-gray-100">
                            <span class="text-gray-600 font-light">Clusters Premium</span>
                            <span class="font-light text-blue-600">{{ gtm_insights.get('high_potential_segments', [])|length if gtm_insights else 4 }}</span>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide mb-6">Metas 90 Dias</h3>
                    <div class="text-center mb-6">
                        <div class="text-3xl font-light text-blue-600 mb-2">300%</div>
                        <div class="text-sm text-gray-500 font-light">Crescimento Total</div>
                    </div>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center py-2 border-b border-gray-100">
                            <span class="text-gray-600 font-light">Receita Atual</span>
                            <span class="font-light text-gray-900">R$ 2.5M</span>
                        </div>
                        <div class="flex justify-between items-center py-2 border-b border-gray-100">
                            <span class="text-gray-600 font-light">Meta Final</span>
                            <span class="font-light text-blue-600">R$ 7.5M</span>
                        </div>
                        <div class="flex justify-between items-center py-2 border-b border-gray-100">
                            <span class="text-gray-600 font-light">Incremento</span>
                            <span class="font-light text-blue-600">+R$ 5M</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Análise Qualitativa dos Clusters -->
            <div class="border-t border-gray-100 pt-8">
                <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide mb-6">Análise Qualitativa dos Clusters</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">

                    <!-- Cluster Premium (Alto Valor) -->
                    <button onclick="openClusterModal('premium')" class="bg-blue-50 rounded-lg p-6 border border-blue-100 hover:bg-blue-100 transition-colors w-full text-left">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-blue-600 rounded-full mr-3"></div>
                            <h4 class="font-medium text-blue-900">Cluster Premium - Alto Valor</h4>
                            <span class="ml-auto text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">25% das turmas</span>
                        </div>
                        <div class="space-y-3 text-sm text-blue-800">
                            <div><strong>Perfil:</strong> Universidades privadas de grande porte, regiões metropolitanas</div>
                            <div><strong>Características:</strong> Alta conversão (25%+), receita/lead R$ 1.200+, baixo churn</div>
                            <div><strong>Comportamento:</strong> Decisão rápida, pagamento à vista, alta implementação</div>
                            <div><strong>Potencial:</strong> Expansão para produtos premium, advocacy program</div>
                        </div>
                        <div class="mt-4 text-xs text-blue-600 font-medium">
                            👆 Clique para ver turmas deste cluster
                        </div>
                    </button>

                    <!-- Cluster Crescimento (Médio-Alto) -->
                    <button onclick="openClusterModal('crescimento')" class="bg-gray-50 rounded-lg p-6 border border-gray-200 hover:bg-gray-100 transition-colors w-full text-left">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-gray-600 rounded-full mr-3"></div>
                            <h4 class="font-medium text-gray-900">Cluster Crescimento - Médio-Alto</h4>
                            <span class="ml-auto text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">35% das turmas</span>
                        </div>
                        <div class="space-y-3 text-sm text-gray-700">
                            <div><strong>Perfil:</strong> Universidades regionais, cidades médias, crescimento acelerado</div>
                            <div><strong>Características:</strong> Conversão moderada (18%), receita/lead R$ 850, potencial alto</div>
                            <div><strong>Comportamento:</strong> Processo estruturado, negociação de prazo, implementação gradual</div>
                            <div><strong>Potencial:</strong> Upselling, cross-selling, programas de fidelidade</div>
                        </div>
                        <div class="mt-4 text-xs text-gray-600 font-medium">
                            👆 Clique para ver turmas deste cluster
                        </div>
                    </button>

                    <!-- Cluster Desenvolvimento (Médio) -->
                    <button onclick="openClusterModal('desenvolvimento')" class="bg-gray-50 rounded-lg p-6 border border-gray-200 hover:bg-gray-100 transition-colors w-full text-left">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-gray-500 rounded-full mr-3"></div>
                            <h4 class="font-medium text-gray-900">Cluster Desenvolvimento - Médio</h4>
                            <span class="ml-auto text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">30% das turmas</span>
                        </div>
                        <div class="space-y-3 text-sm text-gray-700">
                            <div><strong>Perfil:</strong> Universidades em desenvolvimento, interior, orçamento limitado</div>
                            <div><strong>Características:</strong> Conversão baixa (12%), receita/lead R$ 600, sensível a preço</div>
                            <div><strong>Comportamento:</strong> Decisão lenta, negociação intensa, implementação parcial</div>
                            <div><strong>Potencial:</strong> Programas de incentivo, parcelamento, suporte intensivo</div>
                        </div>
                        <div class="mt-4 text-xs text-gray-600 font-medium">
                            👆 Clique para ver turmas deste cluster
                        </div>
                    </button>

                    <!-- Cluster Oportunidade (Baixo-Médio) -->
                    <button onclick="openClusterModal('oportunidade')" class="bg-blue-50 rounded-lg p-6 border border-blue-100 hover:bg-blue-100 transition-colors w-full text-left">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-blue-400 rounded-full mr-3"></div>
                            <h4 class="font-medium text-blue-900">Cluster Oportunidade - Baixo-Médio</h4>
                            <span class="ml-auto text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">10% das turmas</span>
                        </div>
                        <div class="space-y-3 text-sm text-blue-800">
                            <div><strong>Perfil:</strong> Universidades emergentes, novos mercados, alta digitalização</div>
                            <div><strong>Características:</strong> Conversão variável (8-20%), receita/lead R$ 400-900</div>
                            <div><strong>Comportamento:</strong> Inovadores, early adopters, experimentação</div>
                            <div><strong>Potencial:</strong> Crescimento exponencial, parcerias estratégicas, co-criação</div>
                        </div>
                        <div class="mt-4 text-xs text-blue-600 font-medium">
                            👆 Clique para ver turmas deste cluster
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- 3. SEGMENTAÇÃO E CADÊNCIA SALESFORCE -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-12">
            <h2 class="text-xl font-light text-gray-900 mb-8">Segmentação e Cadência Salesforce v1</h2>

            <!-- Configuração de Segmentos -->
            <div class="mb-8">
                <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide mb-6">Configuração de Segmentos</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

                    <!-- Lead Scoring -->
                    <div class="space-y-6">
                        <h4 class="text-sm font-medium text-gray-700">Lead Scoring Automático</h4>
                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Universidade Privada Grande:</span>
                                    <span class="font-medium text-gray-900">+25 pontos</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Região Metropolitana:</span>
                                    <span class="font-medium text-gray-900">+20 pontos</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Orçamento > R$ 50k:</span>
                                    <span class="font-medium text-gray-900">+15 pontos</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Decisor Identificado:</span>
                                    <span class="font-medium text-gray-900">+10 pontos</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Urgência Alta:</span>
                                    <span class="font-medium text-gray-900">+10 pontos</span>
                                </div>
                                <div class="border-t border-gray-200 pt-2 mt-3">
                                    <div class="flex justify-between font-medium">
                                        <span class="text-gray-900">Score Total:</span>
                                        <span class="text-blue-600">0-100 pontos</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Classificação de Segmentos -->
                    <div class="space-y-6">
                        <h4 class="text-sm font-medium text-gray-700">Classificação de Segmentos</h4>
                        <div class="space-y-3">
                            <div class="bg-blue-50 rounded-lg p-3 border border-blue-200">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="font-medium text-blue-900">Hot Leads (80-100 pts)</span>
                                    <span class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">Key Accounts</span>
                                </div>
                                <div class="text-xs text-blue-700">Contato imediato, proposta personalizada, follow-up diário</div>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="font-medium text-gray-900">Warm Leads (60-79 pts)</span>
                                    <span class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">Sales Reps</span>
                                </div>
                                <div class="text-xs text-gray-700">Contato em 24h, demo agendada, follow-up 3x/semana</div>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="font-medium text-gray-900">Cold Leads (40-59 pts)</span>
                                    <span class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">BDR</span>
                                </div>
                                <div class="text-xs text-gray-700">Nutrição por email, conteúdo educativo, follow-up semanal</div>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="font-medium text-gray-900">Low Priority (0-39 pts)</span>
                                    <span class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">Marketing</span>
                                </div>
                                <div class="text-xs text-gray-700">Automação de marketing, newsletter, follow-up mensal</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cadências de Follow-up -->
            <div class="border-t border-gray-100 pt-8">
                <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide mb-6">Cadências de Follow-up por Segmento</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

                    <!-- Hot Leads - Key Accounts -->
                    <div class="bg-blue-50 rounded-lg p-6 border border-blue-100">
                        <h4 class="font-medium text-blue-900 mb-4">Hot Leads - Cadência Intensiva</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex items-start">
                                <span class="text-blue-600 font-medium mr-2">D0:</span>
                                <span class="text-blue-800">Contato telefônico imediato + email personalizado</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-blue-600 font-medium mr-2">D1:</span>
                                <span class="text-blue-800">Follow-up telefônico + proposta inicial</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-blue-600 font-medium mr-2">D3:</span>
                                <span class="text-blue-800">Demo personalizada + case study relevante</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-blue-600 font-medium mr-2">D5:</span>
                                <span class="text-blue-800">Proposta comercial detalhada</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-blue-600 font-medium mr-2">D7:</span>
                                <span class="text-blue-800">Negociação e fechamento</span>
                            </div>
                        </div>
                    </div>

                    <!-- Warm Leads - Sales Reps -->
                    <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
                        <h4 class="font-medium text-gray-900 mb-4">Warm Leads - Cadência Estruturada</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D0:</span>
                                <span class="text-gray-700">Email de boas-vindas + agendamento de demo</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D2:</span>
                                <span class="text-gray-700">Contato telefônico + qualificação</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D5:</span>
                                <span class="text-gray-700">Demo do produto + discovery</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D8:</span>
                                <span class="text-gray-700">Follow-up pós-demo + proposta</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D12:</span>
                                <span class="text-gray-700">Negociação e fechamento</span>
                            </div>
                        </div>
                    </div>

                    <!-- Cold Leads - BDR -->
                    <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
                        <h4 class="font-medium text-gray-900 mb-4">Cold Leads - Cadência de Nutrição</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D0:</span>
                                <span class="text-gray-700">Email de boas-vindas + conteúdo educativo</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D3:</span>
                                <span class="text-gray-700">Conteúdo sobre ROI + case studies</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D7:</span>
                                <span class="text-gray-700">Webinar educativo + convite para demo</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D14:</span>
                                <span class="text-gray-700">Follow-up telefônico + qualificação</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D21:</span>
                                <span class="text-gray-700">Reavaliação e transferência para Sales</span>
                            </div>
                        </div>
                    </div>

                    <!-- Low Priority - Marketing -->
                    <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
                        <h4 class="font-medium text-gray-900 mb-4">Low Priority - Automação de Marketing</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D0:</span>
                                <span class="text-gray-700">Inscrição em newsletter + conteúdo básico</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D7:</span>
                                <span class="text-gray-700">Email semanal com dicas e insights</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D30:</span>
                                <span class="text-gray-700">Pesquisa de necessidades + re-scoring</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D60:</span>
                                <span class="text-gray-700">Convite para evento + reavaliação</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-gray-600 font-medium mr-2">D90:</span>
                                <span class="text-gray-700">Campanha de reativação + nova qualificação</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Configuração Técnica Salesforce -->
            <div class="border-t border-gray-100 pt-8">
                <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide mb-6">Configuração Técnica Salesforce</h3>
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">

                    <!-- Custom Fields -->
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <h4 class="font-medium text-gray-900 mb-3">Custom Fields</h4>
                        <div class="space-y-2 text-sm text-gray-700">
                            <div>• Lead_Score__c (Number)</div>
                            <div>• Cluster_Type__c (Picklist)</div>
                            <div>• University_Size__c (Picklist)</div>
                            <div>• Budget_Range__c (Currency)</div>
                            <div>• Decision_Urgency__c (Picklist)</div>
                            <div>• Geographic_Region__c (Text)</div>
                        </div>
                    </div>

                    <!-- Process Builder -->
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <h4 class="font-medium text-gray-900 mb-3">Process Builder</h4>
                        <div class="space-y-2 text-sm text-gray-700">
                            <div>• Auto Lead Scoring</div>
                            <div>• Segment Assignment</div>
                            <div>• Owner Assignment Rules</div>
                            <div>• Cadence Trigger</div>
                            <div>• Notification Alerts</div>
                            <div>• Task Creation</div>
                        </div>
                    </div>

                    <!-- Reports & Dashboards -->
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <h4 class="font-medium text-gray-900 mb-3">Reports & Dashboards</h4>
                        <div class="space-y-2 text-sm text-gray-700">
                            <div>• Lead Score Distribution</div>
                            <div>• Conversion by Segment</div>
                            <div>• Cadence Performance</div>
                            <div>• Pipeline by Cluster</div>
                            <div>• Team Performance</div>
                            <div>• ROI by Channel</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 4. PLANO DE DISTRIBUIÇÃO DE RECURSOS -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-12">
            <h2 class="text-xl font-light text-gray-900 mb-8">Plano de Distribuição de Recursos - 90 Dias</h2>

            <!-- Orçamento Total -->
            <div class="bg-blue-50 rounded-lg p-6 border border-blue-100 mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-blue-900">Orçamento Total Disponível</h3>
                    <div class="text-3xl font-bold text-blue-600">R$ 300.000</div>
                </div>
                <div class="text-sm text-blue-700">
                    <strong>Período:</strong> 90 dias | <strong>Equipe:</strong> Já remunerada (não inclusa no orçamento) | <strong>Foco:</strong> Tecnologia, Marketing e Operações
                </div>
            </div>

            <!-- Distribuição por Categoria -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">

                <!-- Tecnologia e Infraestrutura -->
                <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
                    <h4 class="font-medium text-gray-900 mb-4">🔧 Tecnologia & Infraestrutura</h4>
                    <div class="text-2xl font-bold text-blue-600 mb-4">R$ 95.000 (32%)</div>
                    <div class="space-y-3 text-sm text-gray-700">
                        <div class="flex justify-between">
                            <span>DataHub Development:</span>
                            <span class="font-medium">R$ 50.000</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Salesforce Sales Cloud:</span>
                            <span class="font-medium">R$ 35.000</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Salesforce Marketing Cloud:</span>
                            <span class="font-medium text-green-600">R$ 0 (já pago)</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Integrações e APIs:</span>
                            <span class="font-medium">R$ 10.000</span>
                        </div>
                    </div>
                </div>

                <!-- Marketing e Aquisição -->
                <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
                    <h4 class="font-medium text-gray-900 mb-4">📈 Marketing & Aquisição</h4>
                    <div class="text-2xl font-bold text-blue-600 mb-4">R$ 145.000 (48%)</div>
                    <div class="space-y-3 text-sm text-gray-700">
                        <div class="flex justify-between">
                            <span>Campanhas Digitais:</span>
                            <span class="font-medium">R$ 85.000</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Conteúdo e Creative:</span>
                            <span class="font-medium">R$ 25.000</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Eventos e Webinars:</span>
                            <span class="font-medium">R$ 20.000</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Ferramentas de Marketing:</span>
                            <span class="font-medium">R$ 15.000</span>
                        </div>
                    </div>
                </div>

                <!-- Operações e Suporte -->
                <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
                    <h4 class="font-medium text-gray-900 mb-4">⚙️ Operações & Suporte</h4>
                    <div class="text-2xl font-bold text-blue-600 mb-4">R$ 60.000 (20%)</div>
                    <div class="space-y-3 text-sm text-gray-700">
                        <div class="flex justify-between">
                            <span>Treinamentos e Capacitação:</span>
                            <span class="font-medium">R$ 25.000</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Ferramentas de Produtividade:</span>
                            <span class="font-medium">R$ 15.000</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Consultoria Especializada:</span>
                            <span class="font-medium">R$ 12.000</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Contingência:</span>
                            <span class="font-medium">R$ 8.000</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timeline de Investimentos -->
            <div class="border-t border-gray-100 pt-8">
                <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide mb-6">Timeline de Investimentos</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">

                    <!-- Mês 1 -->
                    <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                        <h4 class="font-medium text-blue-900 mb-3">Mês 1 - Setup (R$ 155k)</h4>
                        <div class="space-y-2 text-sm text-blue-800">
                            <div>• DataHub MVP: R$ 30k</div>
                            <div>• Salesforce Sales Cloud: R$ 35k</div>
                            <div>• Campanhas Iniciais: R$ 60k</div>
                            <div>• Treinamentos: R$ 20k</div>
                            <div>• Ferramentas: R$ 10k</div>
                        </div>
                    </div>

                    <!-- Mês 2 -->
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <h4 class="font-medium text-gray-900 mb-3">Mês 2 - Execução (R$ 95k)</h4>
                        <div class="space-y-2 text-sm text-gray-700">
                            <div>• DataHub Expansão: R$ 20k</div>
                            <div>• Campanhas Escaladas: R$ 50k</div>
                            <div>• Conteúdo e Eventos: R$ 20k</div>
                            <div>• Consultoria: R$ 5k</div>
                        </div>
                    </div>

                    <!-- Mês 3 -->
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <h4 class="font-medium text-gray-900 mb-3">Mês 3 - Otimização (R$ 50k)</h4>
                        <div class="space-y-2 text-sm text-gray-700">
                            <div>• Campanhas Refinadas: R$ 30k</div>
                            <div>• Eventos Finais: R$ 10k</div>
                            <div>• Consultoria Avançada: R$ 7k</div>
                            <div>• Contingência: R$ 3k</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 5. PLANO ORQUESTRADO POR TIMES -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-12">
            <h2 class="text-xl font-light text-gray-900 mb-8">Plano Orquestrado por Times</h2>

            <!-- Timeline de 90 Dias -->
            <div class="space-y-12">

                <!-- MÊS 1: FUNDAÇÃO -->
                <div class="relative">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium mr-4">1</div>
                        <h3 class="text-lg font-light text-gray-900">Mês 1 - Fundação e Estruturação</h3>
                        <div class="ml-auto text-xs text-gray-500 uppercase tracking-wide">Dias 1-30</div>
                    </div>

                    <!-- Times e Responsabilidades -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ml-12">

                        <!-- Comercial -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide">Time Comercial</h4>
                            <div class="space-y-3 text-sm">
                                <div>
                                    <span class="font-medium text-blue-600">BDR:</span>
                                    <p class="text-gray-600 font-light mt-1">Implementar segmentação ML nos 4 clusters premium. Criar cadências personalizadas por perfil.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-blue-600">Sales Reps:</span>
                                    <p class="text-gray-600 font-light mt-1">Treinar abordagem por cluster. Otimizar scripts de vendas baseados em dados.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-blue-600">Key Accounts:</span>
                                    <p class="text-gray-600 font-light mt-1">Mapear contas estratégicas. Desenvolver propostas de valor personalizadas.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Operações -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide">Time Operações</h4>
                            <div class="space-y-3 text-sm">
                                <div>
                                    <span class="font-medium text-gray-600">Onboarding:</span>
                                    <p class="text-gray-600 font-light mt-1">Criar jornadas de implementação por cluster. Reduzir time-to-value em 40%.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-600">Ongoing:</span>
                                    <p class="text-gray-600 font-light mt-1">Implementar sistema de alertas de churn. Criar playbooks de retenção.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-600">Especialistas:</span>
                                    <p class="text-gray-600 font-light mt-1">Desenvolver base de conhecimento. Otimizar tempo de resolução.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Gestão -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide">Gestão & Suporte</h4>
                            <div class="space-y-3 text-sm">
                                <div>
                                    <span class="font-medium text-blue-600">Gestor Comercial:</span>
                                    <p class="text-gray-600 font-light mt-1">Definir metas por time. Implementar dashboard de performance.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-600">Gestor Operações:</span>
                                    <p class="text-gray-600 font-light mt-1">Otimizar processos internos. Implementar automações.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-400">Comunicação:</span>
                                    <p class="text-gray-600 font-light mt-1">Criar materiais por cluster. Desenvolver case studies.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Meta do Mês -->
                    <div class="ml-12 mt-6 bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-900">Meta Mês 1:</span>
                            <span class="text-sm font-light text-blue-600">+40% conversão nos clusters premium</span>
                        </div>
                    </div>
                </div>

                <!-- MÊS 2: EXPANSÃO -->
                <div class="relative">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium mr-4">2</div>
                        <h3 class="text-lg font-light text-gray-900">Mês 2 - Expansão e Crescimento</h3>
                        <div class="ml-auto text-xs text-gray-500 uppercase tracking-wide">Dias 31-60</div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ml-12">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide">Time Comercial</h4>
                            <div class="space-y-3 text-sm">
                                <div>
                                    <span class="font-medium text-blue-600">BDR:</span>
                                    <p class="text-gray-600 font-light mt-1">Expandir prospecção para 6 novos estados. Implementar automação de cadências.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-blue-600">Sales Reps:</span>
                                    <p class="text-gray-600 font-light mt-1">Executar programa de upselling. Focar em cross-selling de produtos complementares.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-blue-600">Key Accounts:</span>
                                    <p class="text-gray-600 font-light mt-1">Negociar contratos enterprise. Desenvolver parcerias estratégicas.</p>
                                </div>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide">Time Operações</h4>
                            <div class="space-y-3 text-sm">
                                <div>
                                    <span class="font-medium text-gray-600">Onboarding:</span>
                                    <p class="text-gray-600 font-light mt-1">Escalar processos de implementação. Treinar equipe para volume 2x maior.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-600">Ongoing:</span>
                                    <p class="text-gray-600 font-light mt-1">Executar campanhas de retenção proativas. Implementar programa de fidelidade.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-600">Especialistas:</span>
                                    <p class="text-gray-600 font-light mt-1">Criar centro de excelência. Desenvolver treinamentos avançados.</p>
                                </div>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide">Gestão & Suporte</h4>
                            <div class="space-y-3 text-sm">
                                <div>
                                    <span class="font-medium text-blue-600">Gestor Comercial:</span>
                                    <p class="text-gray-600 font-light mt-1">Monitorar performance regional. Ajustar estratégias por mercado.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-600">Gestor Operações:</span>
                                    <p class="text-gray-600 font-light mt-1">Otimizar capacidade operacional. Implementar métricas de qualidade.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-400">Comunicação:</span>
                                    <p class="text-gray-600 font-light mt-1">Lançar campanhas regionais. Criar conteúdo para novos mercados.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="ml-12 mt-6 bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-900">Meta Mês 2:</span>
                            <span class="text-sm font-light text-blue-600">+100% receita através de expansão e upselling</span>
                        </div>
                    </div>
                </div>

                <!-- MÊS 3: CONSOLIDAÇÃO -->
                <div class="relative">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium mr-4">3</div>
                        <h3 class="text-lg font-light text-gray-900">Mês 3 - Consolidação e Escala</h3>
                        <div class="ml-auto text-xs text-gray-500 uppercase tracking-wide">Dias 61-90</div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ml-12">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide">Time Comercial</h4>
                            <div class="space-y-3 text-sm">
                                <div>
                                    <span class="font-medium text-blue-600">BDR:</span>
                                    <p class="text-gray-600 font-light mt-1">Otimizar performance baseada em dados. Implementar programa de referência.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-blue-600">Sales Reps:</span>
                                    <p class="text-gray-600 font-light mt-1">Consolidar processos de vendas. Preparar escala para próximo ciclo.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-blue-600">Key Accounts:</span>
                                    <p class="text-gray-600 font-light mt-1">Expandir contratos existentes. Desenvolver advocacy program.</p>
                                </div>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide">Time Operações</h4>
                            <div class="space-y-3 text-sm">
                                <div>
                                    <span class="font-medium text-gray-600">Onboarding:</span>
                                    <p class="text-gray-600 font-light mt-1">Consolidar processos otimizados. Documentar best practices.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-600">Ongoing:</span>
                                    <p class="text-gray-600 font-light mt-1">Medir impacto das iniciativas de retenção. Refinar estratégias.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-600">Especialistas:</span>
                                    <p class="text-gray-600 font-light mt-1">Implementar programa de certificação. Criar comunidade de usuários.</p>
                                </div>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide">Gestão & Suporte</h4>
                            <div class="space-y-3 text-sm">
                                <div>
                                    <span class="font-medium text-blue-600">Gestor Comercial:</span>
                                    <p class="text-gray-600 font-light mt-1">Análise completa de performance. Planejamento do próximo ciclo.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-600">Gestor Operações:</span>
                                    <p class="text-gray-600 font-light mt-1">Consolidar melhorias operacionais. Preparar infraestrutura para escala.</p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-400">Comunicação:</span>
                                    <p class="text-gray-600 font-light mt-1">Documentar cases de sucesso. Criar materiais de advocacy.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="ml-12 mt-6 bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-900">Meta Mês 3:</span>
                            <span class="text-sm font-light text-blue-600">300% crescimento total consolidado</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 4. MÉTRICAS DE SUCESSO -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <h2 class="text-xl font-light text-gray-900 mb-8">Métricas de Sucesso</h2>

            <!-- KPIs Principais -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                <div class="text-center p-4 border border-gray-100 rounded-lg">
                    <div class="text-2xl font-light text-blue-600 mb-2">300%</div>
                    <div class="text-xs text-gray-500 uppercase tracking-wide">Meta Crescimento</div>
                </div>
                <div class="text-center p-4 border border-gray-100 rounded-lg">
                    <div class="text-2xl font-light text-gray-900 mb-2">R$ 5M</div>
                    <div class="text-xs text-gray-500 uppercase tracking-wide">Receita Adicional</div>
                </div>
                <div class="text-center p-4 border border-gray-100 rounded-lg">
                    <div class="text-2xl font-light text-gray-900 mb-2">4</div>
                    <div class="text-xs text-gray-500 uppercase tracking-wide">Clusters Premium</div>
                </div>
                <div class="text-center p-4 border border-gray-100 rounded-lg">
                    <div class="text-2xl font-light text-gray-900 mb-2">80%</div>
                    <div class="text-xs text-gray-500 uppercase tracking-wide">Redução Churn</div>
                </div>
            </div>

            <!-- Monitoramento -->
            <div class="border-t border-gray-100 pt-8">
                <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide mb-6">Monitoramento Semanal</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="space-y-4">
                        <h4 class="text-sm font-medium text-gray-700">Crescimento</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between items-center py-1">
                                <span class="text-gray-600 font-light">MRR</span>
                                <span class="font-light text-blue-600">+200%</span>
                            </div>
                            <div class="flex justify-between items-center py-1">
                                <span class="text-gray-600 font-light">Conversão por Cluster</span>
                                <span class="font-light text-blue-600">+40%</span>
                            </div>
                            <div class="flex justify-between items-center py-1">
                                <span class="text-gray-600 font-light">Leads Qualificados</span>
                                <span class="font-light text-blue-600">+150%</span>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <h4 class="text-sm font-medium text-gray-700">Retenção</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between items-center py-1">
                                <span class="text-gray-600 font-light">Churn Mensal</span>
                                <span class="font-light text-gray-900">-80%</span>
                            </div>
                            <div class="flex justify-between items-center py-1">
                                <span class="text-gray-600 font-light">Customer LTV</span>
                                <span class="font-light text-gray-900">+200%</span>
                            </div>
                            <div class="flex justify-between items-center py-1">
                                <span class="text-gray-600 font-light">Net Promoter Score</span>
                                <span class="font-light text-gray-900">>70</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- JavaScript para Organograma e Interatividade -->
<script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configurar Mermaid
    mermaid.initialize({
        startOnLoad: true,
        theme: 'base',
        themeVariables: {
            primaryColor: '#3B82F6',
            primaryTextColor: '#1F2937',
            primaryBorderColor: '#E5E7EB',
            lineColor: '#9CA3AF',
            secondaryColor: '#F3F4F6',
            tertiaryColor: '#FFFFFF'
        }
    });

    // Criar organograma
    const orgChartDefinition = `
        graph TD
            CEO[CEO/Diretor Geral]

            CEO --> GC[Gestor Comercial]
            CEO --> GO[Gestor de Operações]
            CEO --> COM[Time de Comunicação]

            GC --> BDR[BDR Team]
            GC --> SR[Sales Reps]
            GC --> KA[Key Accounts]

            GO --> ON[Onboarding]
            GO --> OG[Ongoing/CS]
            GO --> ESP[Especialistas]

            classDef commercial fill:#DBEAFE,stroke:#3B82F6,stroke-width:2px,color:#1E40AF
            classDef operations fill:#F3F4F6,stroke:#6B7280,stroke-width:2px,color:#374151
            classDef management fill:#EFF6FF,stroke:#2563EB,stroke-width:2px,color:#1D4ED8
            classDef support fill:#F9FAFB,stroke:#9CA3AF,stroke-width:2px,color:#6B7280

            class CEO management
            class GC,BDR,SR,KA commercial
            class GO,ON,OG,ESP operations
            class COM support
    `;

    // Renderizar organograma
    const orgChartElement = document.getElementById('orgChart');
    if (orgChartElement) {
        mermaid.render('orgChartSvg', orgChartDefinition).then(result => {
            orgChartElement.innerHTML = result.svg;
        });
    }

    console.log('Go-to-Market Strategy Dashboard carregado');
});
</script>

<!-- Modais dos Clusters -->
<!-- Modal Premium -->
<div id="clusterModalPremium" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-blue-600 rounded-full mr-3"></div>
                        <h3 class="text-xl font-medium text-blue-900">Cluster Premium - Alto Valor</h3>
                    </div>
                    <button onclick="closeClusterModal('premium')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="mb-6">
                    <h4 class="font-medium text-gray-900 mb-3">Turmas do Cluster Premium</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="premiumTurmasList">
                        <!-- Turmas serão carregadas dinamicamente via JavaScript -->
                        <div class="col-span-full text-center text-gray-500 py-8">
                            <div class="animate-spin w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2"></div>
                            Carregando turmas do cluster...
                        </div>
                    </div>
                </div>
                <div class="bg-blue-50 rounded-lg p-4 border border-blue-100">
                    <h5 class="font-medium text-blue-900 mb-2">Estratégia Recomendada</h5>
                    <ul class="text-sm text-blue-800 space-y-1">
                        <li>• Foco em produtos premium e soluções enterprise</li>
                        <li>• Programa de advocacy e referências</li>
                        <li>• Atendimento VIP com Key Account Manager dedicado</li>
                        <li>• Propostas personalizadas com ROI detalhado</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Crescimento -->
<div id="clusterModalCrescimento" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-gray-600 rounded-full mr-3"></div>
                        <h3 class="text-xl font-medium text-gray-900">Cluster Crescimento - Médio-Alto</h3>
                    </div>
                    <button onclick="closeClusterModal('crescimento')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="mb-6">
                    <h4 class="font-medium text-gray-900 mb-3">Turmas do Cluster Crescimento</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="crescimentoTurmasList">
                        <!-- Turmas serão carregadas dinamicamente via JavaScript -->
                        <div class="col-span-full text-center text-gray-500 py-8">
                            <div class="animate-spin w-6 h-6 border-2 border-gray-600 border-t-transparent rounded-full mx-auto mb-2"></div>
                            Carregando turmas do cluster...
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <h5 class="font-medium text-gray-900 mb-2">Estratégia Recomendada</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• Programas de upselling e cross-selling estruturados</li>
                        <li>• Parcelamento flexível e condições especiais</li>
                        <li>• Treinamentos e capacitação contínua</li>
                        <li>• Programa de fidelidade com benefícios progressivos</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Desenvolvimento -->
<div id="clusterModalDesenvolvimento" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-gray-500 rounded-full mr-3"></div>
                        <h3 class="text-xl font-medium text-gray-900">Cluster Desenvolvimento - Médio</h3>
                    </div>
                    <button onclick="closeClusterModal('desenvolvimento')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="mb-6">
                    <h4 class="font-medium text-gray-900 mb-3">Turmas do Cluster Desenvolvimento</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="desenvolvimentoTurmasList">
                        <!-- Turmas serão carregadas dinamicamente via JavaScript -->
                        <div class="col-span-full text-center text-gray-500 py-8">
                            <div class="animate-spin w-6 h-6 border-2 border-gray-600 border-t-transparent rounded-full mx-auto mb-2"></div>
                            Carregando turmas do cluster...
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <h5 class="font-medium text-gray-900 mb-2">Estratégia Recomendada</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• Programas de incentivo e descontos especiais</li>
                        <li>• Parcelamento estendido e condições facilitadas</li>
                        <li>• Suporte intensivo durante implementação</li>
                        <li>• Conteúdo educativo sobre ROI e benefícios</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Oportunidade -->
<div id="clusterModalOportunidade" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-blue-400 rounded-full mr-3"></div>
                        <h3 class="text-xl font-medium text-blue-900">Cluster Oportunidade - Baixo-Médio</h3>
                    </div>
                    <button onclick="closeClusterModal('oportunidade')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="mb-6">
                    <h4 class="font-medium text-gray-900 mb-3">Turmas do Cluster Oportunidade</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="oportunidadeTurmasList">
                        <!-- Turmas serão carregadas dinamicamente via JavaScript -->
                        <div class="col-span-full text-center text-gray-500 py-8">
                            <div class="animate-spin w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2"></div>
                            Carregando turmas do cluster...
                        </div>
                    </div>
                </div>
                <div class="bg-blue-50 rounded-lg p-4 border border-blue-100">
                    <h5 class="font-medium text-blue-900 mb-2">Estratégia Recomendada</h5>
                    <ul class="text-sm text-blue-800 space-y-1">
                        <li>• Parcerias estratégicas e co-criação de soluções</li>
                        <li>• Programas piloto com condições especiais</li>
                        <li>• Foco em inovação e early adoption</li>
                        <li>• Crescimento exponencial através de referências</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Global variable to store cluster data
let clusterData = null;

// Load cluster data from API
async function loadClusterData() {
    try {
        // Primeiro tentar o endpoint de teste
        try {
            const testResponse = await fetch('/clustering/api/test-clustering-data');
            const testData = await testResponse.json();
            console.log('Test endpoint response:', testData);

            // Se o teste funcionar, carregar dados completos
            if (testData.status === 'success') {
                const response = await fetch('/clustering/api/clustering-data');
                const data = await response.json();
                clusterData = data;
                console.log('Cluster data loaded from API:', clusterData);
                return data;
            }
        } catch (apiError) {
            console.warn('API endpoint failed, trying static file:', apiError);
        }

        // Fallback: usar arquivo estático
        console.log('Loading cluster data from static file...');
        const response = await fetch('/static/data/clustering_sample.json');
        const data = await response.json();
        clusterData = data;
        console.log('Cluster data loaded from static file:', clusterData);
        return data;

    } catch (error) {
        console.error('Erro ao carregar dados dos clusters:', error);
        return null;
    }
}

// Load turma mapping
async function loadTurmaMapping() {
    try {
        const response = await fetch('/clustering/api/turma-mapping');
        const csvText = await response.text();
        const lines = csvText.split('\n');
        const mapping = {};

        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line) {
                const [turma_id, turma_nome] = line.split(',');
                if (turma_id && turma_nome) {
                    mapping[parseInt(turma_id)] = turma_nome;
                }
            }
        }

        return mapping;
    } catch (error) {
        console.error('Erro ao carregar mapeamento de turmas:', error);
        return {};
    }
}

// Populate modal with real cluster data
async function populateClusterModal(clusterType) {
    const listElementId = clusterType + 'TurmasList';
    const listElement = document.getElementById(listElementId);

    if (!listElement) {
        console.error('Elemento não encontrado:', listElementId);
        return;
    }

    // Show loading
    listElement.innerHTML = `
        <div class="col-span-full text-center text-gray-500 py-8">
            <div class="animate-spin w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2"></div>
            Carregando turmas do cluster...
        </div>
    `;

    try {
        // Load data if not already loaded
        if (!clusterData) {
            clusterData = await loadClusterData();
        }

        if (!clusterData || !clusterData.clusters_data) {
            throw new Error('Dados dos clusters não disponíveis');
        }

        // Find cluster by classification instead of hardcoded mapping
        let cluster = null;

        // Search for cluster with matching classification
        for (const [id, clusterInfo] of Object.entries(clusterData.clusters_data)) {
            const classification = clusterData.cluster_classifications[id];
            if (classification === clusterType) {
                cluster = clusterInfo;
                break;
            }
        }

        if (!cluster || !cluster.turmas) {
            throw new Error(`Cluster ${clusterType} não encontrado`);
        }

        // Generate HTML for turmas
        let turmasHTML = '';
        const colorClass = clusterType === 'premium' || clusterType === 'oportunidade' ? 'blue' : 'gray';

        cluster.turmas.forEach(turma => {
            const turmaName = turma.turma_nome || `Turma ${turma.turma_id}`;
            const conversao = turma.taxa_conversao ? turma.taxa_conversao.toFixed(1) : 'N/A';
            const receita = turma.receita_por_lead ? turma.receita_por_lead.toLocaleString('pt-BR', {minimumFractionDigits: 0}) : 'N/A';
            const leads = turma.total_leads ? turma.total_leads.toLocaleString('pt-BR', {minimumFractionDigits: 0}) : 'N/A';

            turmasHTML += `
                <div class="bg-${colorClass}-50 rounded-lg p-4 border border-${colorClass}-200 hover:bg-${colorClass}-100 transition-colors">
                    <div class="font-medium text-${colorClass}-900 mb-2">${turmaName}</div>
                    <div class="text-xs text-${colorClass}-700 space-y-1">
                        <div><strong>Leads:</strong> ${leads}</div>
                        <div><strong>Conversão:</strong> ${conversao}%</div>
                        <div><strong>Receita/Lead:</strong> R$ ${receita}</div>
                    </div>
                </div>
            `;
        });

        listElement.innerHTML = turmasHTML;

    } catch (error) {
        console.error('Erro ao carregar turmas do cluster:', error);
        listElement.innerHTML = `
            <div class="col-span-full text-center text-red-500 py-8">
                <div class="text-red-600 mb-2">⚠️</div>
                Erro ao carregar turmas do cluster.<br>
                <span class="text-sm">Dados não disponíveis no momento.</span>
            </div>
        `;
    }
}

function openClusterModal(clusterType) {
    const modalId = 'clusterModal' + clusterType.charAt(0).toUpperCase() + clusterType.slice(1);
    document.getElementById(modalId).classList.remove('hidden');

    // Load cluster data when modal opens
    populateClusterModal(clusterType);
}

function closeClusterModal(clusterType) {
    document.getElementById('clusterModal' + clusterType.charAt(0).toUpperCase() + clusterType.slice(1)).classList.add('hidden');
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modals = ['Premium', 'Crescimento', 'Desenvolvimento', 'Oportunidade'];
    modals.forEach(modal => {
        const modalElement = document.getElementById('clusterModal' + modal);
        if (event.target === modalElement) {
            modalElement.classList.add('hidden');
        }
    });
});

// Load cluster data on page load
document.addEventListener('DOMContentLoaded', function() {
    loadClusterData();
});
</script>

{% endblock %}