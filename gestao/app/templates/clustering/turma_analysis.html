{% extends "base.html" %}

{% block title %}Análise de Clusterização de Turmas{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-chart-scatter"></i>
                    Análise de Clusterização de Turmas
                </h1>
                <p class="page-subtitle">Segmentação inteligente baseada em métricas de performance</p>
            </div>
        </div>
    </div>

    {% if summary and summary.model_info %}
    <!-- Resumo do Modelo -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle"></i>
                        Informações do Modelo
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="metric-card text-center">
                                <div class="metric-value">{{ summary.model_info.n_clusters }}</div>
                                <div class="metric-label">Clusters Identificados</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card text-center">
                                <div class="metric-value">{{ summary.model_info.total_turmas }}</div>
                                <div class="metric-label">Total de Turmas</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card text-center">
                                <div class="metric-value">{{ "%.3f"|format(summary.model_info.silhouette_score) }}</div>
                                <div class="metric-label">Silhouette Score</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card text-center">
                                {% if summary.model_info.silhouette_score >= 0.5 %}
                                    <div class="metric-value text-success">Boa</div>
                                {% elif summary.model_info.silhouette_score >= 0.3 %}
                                    <div class="metric-value text-warning">Moderada</div>
                                {% else %}
                                    <div class="metric-value text-danger">Baixa</div>
                                {% endif %}
                                <div class="metric-label">Qualidade</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Análise dos Clusters -->
    <div class="row mb-4">
        {% for cluster_key, cluster_data in summary.clusters.items() %}
        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-header">
                    <h4 class="card-title">
                        {% if cluster_key == 'cluster_0' %}
                            <i class="fas fa-star text-warning"></i>
                            Cluster 0 - Alto Desempenho
                        {% else %}
                            <i class="fas fa-chart-line text-info"></i>
                            Cluster {{ cluster_key.split('_')[1] }} - Performance Padrão
                        {% endif %}
                    </h4>
                    <div class="card-subtitle">
                        {{ cluster_data.size }} turmas ({{ cluster_data.percentage }}%)
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="metric-small">
                                <div class="metric-value">{{ "%.1f"|format(cluster_data.characteristics.total_leads) }}</div>
                                <div class="metric-label">Leads Médios</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-small">
                                <div class="metric-value">{{ "%.1f"|format(cluster_data.characteristics.taxa_conversao) }}%</div>
                                <div class="metric-label">Taxa Conversão</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-small">
                                <div class="metric-value">{{ "%.1f"|format(cluster_data.characteristics.volume_implementacoes) }}</div>
                                <div class="metric-label">Implementações</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-small">
                                <div class="metric-value">R$ {{ "%.0f"|format(cluster_data.characteristics.receita_por_lead) }}</div>
                                <div class="metric-label">Receita/Lead</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Primeiras turmas do cluster -->
                    <div class="mt-3">
                        <h6>Exemplos de Turmas:</h6>
                        <div class="turma-list">
                            {% for turma in cluster_data.turmas[:5] %}
                                <span class="badge badge-secondary mr-1 mb-1">{{ turma }}</span>
                            {% endfor %}
                            {% if cluster_data.total_turmas > 5 %}
                                <span class="text-muted">... e mais {{ cluster_data.total_turmas - 5 }} turmas</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Insights e Recomendações -->
    {% if insights and insights.business_insights %}
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-lightbulb"></i>
                        Insights de Negócio
                    </h4>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        {% for insight in insights.business_insights %}
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success mr-2"></i>
                            {{ insight }}
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-tasks"></i>
                        Recomendações
                    </h4>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        {% for recommendation in insights.recommendations %}
                        <li class="mb-2">
                            <i class="fas fa-arrow-right text-primary mr-2"></i>
                            {{ recommendation }}
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Ferramentas de Análise -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-tools"></i>
                        Ferramentas de Análise
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="turmaSearch">Buscar Turma:</label>
                                <input type="text" class="form-control" id="turmaSearch" 
                                       placeholder="Digite o nome da turma">
                                <button class="btn btn-primary btn-sm mt-2" onclick="searchTurma()">
                                    <i class="fas fa-search"></i> Buscar
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="clusterSelect">Explorar Cluster:</label>
                                <select class="form-control" id="clusterSelect">
                                    <option value="">Selecione um cluster</option>
                                    {% if summary and summary.clusters %}
                                        {% for cluster_key in summary.clusters.keys() %}
                                        <option value="{{ cluster_key.split('_')[1] }}">
                                            Cluster {{ cluster_key.split('_')[1] }}
                                        </option>
                                        {% endfor %}
                                    {% endif %}
                                </select>
                                <button class="btn btn-info btn-sm mt-2" onclick="exploreCluster()">
                                    <i class="fas fa-eye"></i> Explorar
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Ações:</label><br>
                                <button class="btn btn-success btn-sm" onclick="exportResults()">
                                    <i class="fas fa-download"></i> Exportar Resultados
                                </button>
                                <button class="btn btn-warning btn-sm ml-2" onclick="refreshAnalysis()">
                                    <i class="fas fa-sync"></i> Atualizar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Resultados da Busca -->
    <div class="row" id="searchResults" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Resultado da Busca</h4>
                </div>
                <div class="card-body" id="searchResultsContent">
                    <!-- Conteúdo será preenchido via JavaScript -->
                </div>
            </div>
        </div>
    </div>

    {% else %}
    <!-- Mensagem quando não há dados -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h4>Dados de Clusterização Não Disponíveis</h4>
                    <p class="text-muted">
                        Execute a clusterização primeiro para visualizar os resultados.
                    </p>
                    <a href="{{ url_for('clustering.index') }}" class="btn btn-primary">
                        <i class="fas fa-play"></i> Executar Clusterização
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.metric-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin: 10px 0;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #495057;
}

.metric-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}

.metric-small .metric-value {
    font-size: 1.5rem;
}

.metric-small .metric-label {
    font-size: 0.8rem;
}

.turma-list {
    max-height: 100px;
    overflow-y: auto;
}

.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.page-title {
    color: #495057;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}
</style>

<script>
function searchTurma() {
    const turmaName = document.getElementById('turmaSearch').value.trim();
    if (!turmaName) {
        alert('Digite o nome da turma');
        return;
    }
    
    fetch(`/clustering/api/turma-clustering/turma/${encodeURIComponent(turmaName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Turma não encontrada: ' + data.error);
                return;
            }
            
            displaySearchResult(data);
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao buscar turma');
        });
}

function exploreCluster() {
    const clusterId = document.getElementById('clusterSelect').value;
    if (!clusterId) {
        alert('Selecione um cluster');
        return;
    }
    
    fetch(`/clustering/api/turma-clustering/cluster/${clusterId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Erro ao carregar cluster: ' + data.error);
                return;
            }
            
            displayClusterResult(data);
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao explorar cluster');
        });
}

function displaySearchResult(data) {
    const content = `
        <h5>Turma: ${data.turma_nome}</h5>
        <p><strong>Cluster:</strong> ${data.cluster}</p>
        <div class="row">
            <div class="col-md-6">
                <h6>Métricas da Turma:</h6>
                <ul>
                    <li>Total de Leads: ${data.metrics.total_leads}</li>
                    <li>Taxa de Conversão: ${data.metrics.taxa_conversao.toFixed(1)}%</li>
                    <li>Volume de Implementações: ${data.metrics.volume_implementacoes}</li>
                    <li>Receita por Lead: R$ ${data.metrics.receita_por_lead.toFixed(2)}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Características do Cluster:</h6>
                <ul>
                    <li>Leads Médios: ${data.cluster_characteristics.total_leads.toFixed(1)}</li>
                    <li>Taxa Conversão Média: ${data.cluster_characteristics.taxa_conversao.toFixed(1)}%</li>
                    <li>Implementações Médias: ${data.cluster_characteristics.volume_implementacoes.toFixed(1)}</li>
                    <li>Receita Média por Lead: R$ ${data.cluster_characteristics.receita_por_lead.toFixed(2)}</li>
                </ul>
            </div>
        </div>
    `;
    
    document.getElementById('searchResultsContent').innerHTML = content;
    document.getElementById('searchResults').style.display = 'block';
}

function displayClusterResult(data) {
    const turmasList = data.turmas.map(t => `<span class="badge badge-secondary mr-1 mb-1">${t.turma_nome}</span>`).join('');
    
    const content = `
        <h5>Cluster ${data.cluster_id}</h5>
        <p><strong>Total de Turmas:</strong> ${data.total_turmas}</p>
        <div class="turma-list mb-3">
            ${turmasList}
        </div>
        <h6>Características do Cluster:</h6>
        <div class="row">
            <div class="col-md-3">
                <div class="metric-small text-center">
                    <div class="metric-value">${data.characteristics.stats.total_leads.toFixed(1)}</div>
                    <div class="metric-label">Leads Médios</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-small text-center">
                    <div class="metric-value">${data.characteristics.stats.taxa_conversao.toFixed(1)}%</div>
                    <div class="metric-label">Taxa Conversão</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-small text-center">
                    <div class="metric-value">${data.characteristics.stats.volume_implementacoes.toFixed(1)}</div>
                    <div class="metric-label">Implementações</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-small text-center">
                    <div class="metric-value">R$ ${data.characteristics.stats.receita_por_lead.toFixed(0)}</div>
                    <div class="metric-label">Receita/Lead</div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('searchResultsContent').innerHTML = content;
    document.getElementById('searchResults').style.display = 'block';
}

function exportResults() {
    fetch('/clustering/api/turma-clustering/summary')
        .then(response => response.json())
        .then(data => {
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'clustering_results.json';
            link.click();
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao exportar resultados');
        });
}

function refreshAnalysis() {
    location.reload();
}
</script>
{% endblock %}
