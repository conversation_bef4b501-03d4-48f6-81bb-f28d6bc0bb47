{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table %}

{% block title %}Detalhes do Cluster - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Detalhes do Cluster",
    subtitle="Análise detalhada do cluster " + (cluster.name if cluster else "não encontrado"),
    stats=[
        {
            "label": "Turmas no Cluster",
            "value": cluster.size if cluster else 0,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />'
        },
        {
            "label": "Total de Registros",
            "value": cluster_metrics.get('total_records', 0),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />'
        },
        {
            "label": "Leads Únicos",
            "value": cluster_metrics.get('unique_leads', 0),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />'
        }
    ]
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Back Button -->
    <div class="mb-6">
        <a href="{{ url_for('clustering.index') }}" class="inline-flex items-center text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Voltar para Clusterização
        </a>
    </div>

    {% if cluster %}
    <!-- Cluster Information -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">📊 Informações do Cluster</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-gray-900">{{ cluster.size|e }}</div>
                <div class="text-sm text-gray-600 mt-1">Turmas</div>
            </div>
            
            {% if cluster.get('total_revenue') %}
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-gray-900">R$ {{ "{:,.2f}".format(cluster.total_revenue) }}</div>
                <div class="text-sm text-gray-600 mt-1">Receita Total</div>
            </div>
            {% endif %}
            
            {% if cluster.get('total_leads') %}
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-gray-900">{{ cluster.total_leads|e }}</div>
                <div class="text-sm text-gray-600 mt-1">Total de Leads</div>
            </div>
            {% endif %}
            
            {% if cluster.get('avg_performance_score') %}
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-gray-900">{{ "{:.1%}".format(cluster.avg_performance_score) }}</div>
                <div class="text-sm text-gray-600 mt-1">Performance Média</div>
            </div>
            {% endif %}
        </div>
        
        <!-- Additional Cluster Metrics -->
        {% if cluster_metrics %}
        <div class="mt-6 pt-6 border-t border-gray-200">
            <h4 class="font-medium text-gray-900 mb-4">Métricas Detalhadas</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% if cluster_metrics.get('avg_mensalidade') %}
                <div class="flex justify-between py-2">
                    <span class="text-gray-600">Mensalidade Média:</span>
                    <span class="font-medium">R$ {{ "{:,.2f}".format(cluster_metrics.avg_mensalidade) }}</span>
                </div>
                {% endif %}
                
                {% if cluster_metrics.get('total_receita') %}
                <div class="flex justify-between py-2">
                    <span class="text-gray-600">Receita Total:</span>
                    <span class="font-medium">R$ {{ "{:,.2f}".format(cluster_metrics.total_receita) }}</span>
                </div>
                {% endif %}
                
                <div class="flex justify-between py-2">
                    <span class="text-gray-600">Registros Únicos:</span>
                    <span class="font-medium">{{ cluster_metrics.get('unique_leads', 0)|e }}</span>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Status Distribution -->
    {% if cluster_metrics.get('status_distribution') %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">📈 Distribuição por Status</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for status, count in cluster_metrics.status_distribution.items() %}
            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span class="text-gray-700">{{ status|e }}</span>
                <span class="font-semibold text-gray-900">{{ count|e }}</span>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Turmas List -->
    {% if cluster.get('turmas') %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">🎓 Turmas no Cluster</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {% for turma in cluster.turmas %}
            <div class="p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div class="font-medium text-gray-900">{{ turma|e }}</div>
                <div class="text-sm text-gray-600 mt-1">
                    <a href="{{ url_for('class.detail', class_name=turma) }}" 
                       class="text-blue-600 hover:text-blue-800">
                        Ver detalhes →
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Sample Data -->
    {% if cluster_data %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">📋 Amostra de Dados (Primeiros 100 registros)</h3>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        {% if cluster_data[0] %}
                        {% for key in cluster_data[0].keys() %}
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ key|e }}
                        </th>
                        {% endfor %}
                        {% endif %}
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for row in cluster_data[:20] %}
                    <tr class="hover:bg-gray-50">
                        {% for value in row.values() %}
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ value if value is not none else '-'|e }}
                        </td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        {% if cluster_data|length > 20 %}
        <div class="mt-4 text-center text-sm text-gray-600">
            Mostrando 20 de {{ cluster_data|length }} registros
        </div>
        {% endif %}
    </div>
    {% endif %}
    
    {% else %}
    <!-- No Cluster Found -->
    <div class="bg-white rounded-lg shadow-sm p-8 border border-gray-200 text-center">
        <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Cluster Não Encontrado</h3>
        <p class="text-gray-600 mb-6">O cluster solicitado não foi encontrado ou não pôde ser carregado.</p>
        <a href="{{ url_for('clustering.index') }}" 
           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
            Voltar à Clusterização
        </a>
    </div>
    {% endif %}

    <!-- Actions -->
    <div class="mt-8 flex justify-center space-x-4">
        <a href="{{ url_for('clustering.index') }}" 
           class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Nova Clusterização
        </a>
        
        {% if cluster %}
        <button onclick="window.print()" 
                class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
            </svg>
            Imprimir Detalhes
        </button>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Cluster detail page loaded');
    });
</script>
{% endblock %}
