{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table %}

{% block title %}Resultados da Clusterização - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Resultados da Clusterização",
    subtitle="Análise detalhada dos clusters identificados para " + clustering_options.get(current_type, {}).get('name', current_type.title()),
    stats=[
        {
            "label": "Clusters Identificados",
            "value": clustering_result.clusters|length if clustering_result.clusters else 0,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />'
        },
        {
            "label": "Total de Turmas",
            "value": clustering_result.summary.get('total_turmas', 0) if clustering_result.summary else 0,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />'
        },
        {
            "label": "Tipo de Segmentação",
            "value": clustering_options.get(current_type, {}).get('name', current_type.title()),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />'
        }
    ]
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    {% if clustering_result.error %}
    <!-- Error State -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
        <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
        </div>
        <h3 class="text-lg font-medium text-red-900 mb-2">Erro na Clusterização</h3>
        <p class="text-red-700 mb-6">{{ clustering_result.error|e }}</p>
        <a href="{{ url_for('clustering.index') }}" 
           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            Tentar Novamente
        </a>
    </div>
    
    {% else %}
    <!-- Results Content -->
    
    <!-- Cluster Overview -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">📊 Visão Geral dos Clusters</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {% for cluster in clustering_result.clusters %}
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div class="flex justify-between items-start mb-3">
                    <h4 class="font-semibold text-gray-900">{{ cluster.name|e }}</h4>
                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {{ cluster.size|e }} turmas
                    </span>
                </div>
                
                <div class="space-y-2 text-sm">
                    {% if cluster.get('total_revenue') %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Receita Total:</span>
                        <span class="font-medium">R$ {{ "{:,.2f}".format(cluster.total_revenue) }}</span>
                    </div>
                    {% endif %}
                    {% if cluster.get('total_leads') %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total Leads:</span>
                        <span class="font-medium">{{ cluster.total_leads|e }}</span>
                    </div>
                    {% endif %}
                    {% if cluster.get('avg_value') %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Valor Médio:</span>
                        <span class="font-medium">R$ {{ "{:,.2f}".format(cluster.avg_value) }}</span>
                    </div>
                    {% endif %}
                    {% if cluster.get('avg_performance_score') %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Performance:</span>
                        <span class="font-medium">{{ "{:.1%}".format(cluster.avg_performance_score) }}</span>
                    </div>
                    {% endif %}
                </div>
                
                <div class="mt-4">
                    <a href="{{ url_for('clustering.cluster_detail', cluster_id=cluster.cluster_id, type=current_type, clusters=current_clusters) }}" 
                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        Ver Detalhes →
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Analysis and Insights -->
    {% if analysis %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">💡 Análise e Insights</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% if analysis.insights %}
            <div>
                <h4 class="font-medium text-gray-900 mb-3">📈 Principais Insights</h4>
                <ul class="space-y-2">
                    {% for insight in analysis.insights %}
                    <li class="flex items-start text-sm text-gray-700">
                        <span class="text-blue-600 mr-2">•</span>
                        <span>{{ insight|e }}</span>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
            
            {% if analysis.recommendations %}
            <div>
                <h4 class="font-medium text-gray-900 mb-3">🎯 Recomendações</h4>
                <ul class="space-y-2">
                    {% for recommendation in analysis.recommendations %}
                    <li class="flex items-start text-sm text-gray-700">
                        <span class="text-green-600 mr-2">✓</span>
                        <span>{{ recommendation|e }}</span>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Summary Statistics -->
    {% if clustering_result.summary %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">📈 Estatísticas Resumidas</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {% for key, value in clustering_result.summary.items() %}
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">
                    {% if key in ['total_receita', 'avg_value', 'min_value', 'max_value'] %}
                        R$ {{ "{:,.2f}".format(value) }}
                    {% elif key in ['avg_performance_score', 'avg_taxa_conversao'] %}
                        {{ "{:.1%}".format(value) }}
                    {% else %}
                        {{ value|e }}
                    {% endif %}
                </div>
                <div class="text-sm text-gray-600 mt-1">
                    {% if key == 'total_turmas' %}Total de Turmas
                    {% elif key == 'total_receita' %}Receita Total
                    {% elif key == 'avg_value' %}Valor Médio
                    {% elif key == 'min_value' %}Valor Mínimo
                    {% elif key == 'max_value' %}Valor Máximo
                    {% elif key == 'avg_performance_score' %}Performance Média
                    {% elif key == 'avg_taxa_conversao' %}Taxa de Conversão Média
                    {% else %}{{ key.title()|e }}
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% endif %}

    <!-- Actions -->
    <div class="flex justify-center space-x-4">
        <a href="{{ url_for('clustering.index') }}" 
           class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Nova Clusterização
        </a>
        
        <a href="{{ url_for('clustering.compare') }}" 
           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            Comparar Tipos
        </a>
        
        {% if not clustering_result.error %}
        <a href="{{ url_for('clustering.export_clustering', clustering_type=current_type, clusters=current_clusters) }}" 
           class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Exportar Dados
        </a>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Results page loaded');
    });
</script>
{% endblock %}
