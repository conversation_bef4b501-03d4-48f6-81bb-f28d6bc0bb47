{% extends "base.html" %}

{% block title %}Base de Dados - Qualidade dos Dados{% endblock %}

{% block content %}
<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
    <!-- Page Header -->
    <div class="sm:flex sm:justify-between sm:items-center mb-8">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-2xl md:text-3xl text-gray-800 font-bold">Base de Dados</h1>
            <p class="text-sm text-gray-500 mt-1">Análise completa da qualidade dos dados</p>
        </div>
    </div>

    <!-- Visão Geral da Qualidade dos Dados -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
        <div class="px-6 py-5 border-b border-gray-200">
            <h2 class="font-semibold text-xl text-gray-800">V<PERSON><PERSON>eral da Qualidade dos Dados</h2>
            <p class="text-sm text-gray-500 mt-1">Métricas gerais sobre a qualidade da base de dados</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Score de Qualidade -->
                <div class="bg-white rounded-lg border border-gray-200 p-5">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Score de Qualidade</p>
                            <h3 class="text-3xl font-bold text-gray-900 mt-1">{{ quality_metrics.quality_score|default('N/A') }}%</h3>
                        </div>
                        <div class="h-12 w-12 rounded-full flex items-center justify-center
                            {% if quality_metrics.quality_score >= 90 %}bg-green-100 text-green-600
                            {% elif quality_metrics.quality_score >= 80 %}bg-blue-100 text-blue-600
                            {% elif quality_metrics.quality_score >= 70 %}bg-yellow-100 text-yellow-600
                            {% elif quality_metrics.quality_score >= 60 %}bg-orange-100 text-orange-600
                            {% else %}bg-red-100 text-red-600{% endif %}">
                            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <p class="text-sm font-medium mt-2
                        {% if quality_metrics.quality_score >= 90 %}text-green-600
                        {% elif quality_metrics.quality_score >= 80 %}text-blue-600
                        {% elif quality_metrics.quality_score >= 70 %}text-yellow-600
                        {% elif quality_metrics.quality_score >= 60 %}text-orange-600
                        {% else %}text-red-600{% endif %}">
                        {{ quality_metrics.quality_level|default('N/A') }}
                    </p>
                </div>

                <!-- Dados Ausentes -->
                <div class="bg-white rounded-lg border border-gray-200 p-5">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Dados Ausentes</p>
                            <h3 class="text-3xl font-bold text-gray-900 mt-1">{{ quality_metrics.missing_percentage|default('N/A') }}%</h3>
                        </div>
                        <div class="h-12 w-12 rounded-full flex items-center justify-center bg-yellow-100 text-yellow-600">
                            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <p class="text-sm font-medium mt-2 text-gray-600">
                        {{ quality_metrics.missing_values|default('0') }} valores ausentes
                    </p>
                </div>

                <!-- Linhas Duplicadas -->
                <div class="bg-white rounded-lg border border-gray-200 p-5">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Linhas Duplicadas</p>
                            <h3 class="text-3xl font-bold text-gray-900 mt-1">{{ quality_metrics.duplicate_percentage|default('N/A') }}%</h3>
                        </div>
                        <div class="h-12 w-12 rounded-full flex items-center justify-center bg-orange-100 text-orange-600">
                            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                            </svg>
                        </div>
                    </div>
                    <p class="text-sm font-medium mt-2 text-gray-600">
                        {{ quality_metrics.duplicate_rows|default('0') }} linhas duplicadas
                    </p>
                </div>

                <!-- Tamanho da Base -->
                <div class="bg-white rounded-lg border border-gray-200 p-5">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Tamanho da Base</p>
                            <h3 class="text-3xl font-bold text-gray-900 mt-1">{{ quality_metrics.total_rows|default('N/A') }}</h3>
                        </div>
                        <div class="h-12 w-12 rounded-full flex items-center justify-center bg-blue-100 text-blue-600">
                            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5l8 4m0 0l-8 4m8-4H4" />
                            </svg>
                        </div>
                    </div>
                    <p class="text-sm font-medium mt-2 text-gray-600">
                        {{ quality_metrics.total_columns|default('0') }} colunas
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Completude por Entidade -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
        <div class="px-6 py-5 border-b border-gray-200">
            <h2 class="font-semibold text-xl text-gray-800">Completude por Entidade</h2>
            <p class="text-sm text-gray-500 mt-1">Análise da completude dos dados por entidade de negócio</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for entity, data in completeness_by_entity.items() %}
                <div class="bg-white rounded-lg border border-gray-200 p-5">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500">{{ entity|e }}</p>
                            <h3 class="text-3xl font-bold text-gray-900 mt-1">{{ data.percentage|default('N/A') }}%</h3>
                        </div>
                        <div class="h-12 w-12 rounded-full flex items-center justify-center
                            {% if data.percentage >= 95 %}bg-green-100 text-green-600
                            {% elif data.percentage >= 85 %}bg-blue-100 text-blue-600
                            {% elif data.percentage >= 70 %}bg-yellow-100 text-yellow-600
                            {% elif data.percentage >= 50 %}bg-orange-100 text-orange-600
                            {% else %}bg-red-100 text-red-600{% endif %}">
                            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <p class="text-sm font-medium mt-2
                        {% if data.percentage >= 95 %}text-green-600
                        {% elif data.percentage >= 85 %}text-blue-600
                        {% elif data.percentage >= 70 %}text-yellow-600
                        {% elif data.percentage >= 50 %}text-orange-600
                        {% else %}text-red-600{% endif %}">
                        {{ data.level|e }} ({{ data.columns_count|e }} campos)
                    </p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Colunas com Mais Dados Ausentes -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
        <div class="px-6 py-5 border-b border-gray-200">
            <h2 class="font-semibold text-xl text-gray-800">Colunas com Mais Dados Ausentes</h2>
            <p class="text-sm text-gray-500 mt-1">Top 10 colunas com maior percentual de dados ausentes</p>
        </div>
        <div class="p-6">
            {% if missing_data.top_missing_columns %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Coluna</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valores Ausentes</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentual</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for column, data in missing_data.top_missing_columns.items() %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ column|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ data.count|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ data.percentage|e }}%</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    {% if data.percentage < 10 %}bg-green-100 text-green-800
                                    {% elif data.percentage < 30 %}bg-yellow-100 text-yellow-800
                                    {% elif data.percentage < 50 %}bg-orange-100 text-orange-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                                    {% if data.percentage < 10 %}Bom
                                    {% elif data.percentage < 30 %}Atenção
                                    {% elif data.percentage < 50 %}Preocupante
                                    {% else %}Crítico{% endif %}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="mt-4 text-sm text-gray-500">
                <p>Total de colunas com dados ausentes: {{ missing_data.total_columns_with_missing|e }}</p>
                <p>Colunas em estado crítico (>50% ausente): {{ missing_data.critical_columns|e }}</p>
            </div>
            {% else %}
            <p class="text-sm text-gray-500">Não foram encontradas colunas com dados ausentes.</p>
            {% endif %}
        </div>
    </div>

    <!-- Problemas de Consistência -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
        <div class="px-6 py-5 border-b border-gray-200">
            <h2 class="font-semibold text-xl text-gray-800">Problemas de Consistência</h2>
            <p class="text-sm text-gray-500 mt-1">Inconsistências identificadas nos dados</p>
        </div>
        <div class="p-6">
            {% if consistency_issues %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Coluna</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Problema</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for column, issue in consistency_issues.items() %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ column|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ issue|e }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p class="text-sm text-gray-500">Não foram encontrados problemas de consistência nos dados.</p>
            {% endif %}
        </div>
    </div>

    <!-- Qualidade de Dados por Responsável -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
        <div class="px-6 py-5 border-b border-gray-200">
            <h2 class="font-semibold text-xl text-gray-800">Qualidade de Dados por Responsável</h2>
            <p class="text-sm text-gray-500 mt-1">Análise da qualidade de preenchimento de dados por responsável</p>
        </div>
        <div class="p-6">
            {% if responsible_quality %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Responsável</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Implantações</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Finalizadas</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score de Qualidade</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nível</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campos Problemáticos</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for responsible, data in responsible_quality.items() %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ responsible|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ data.total_implementations|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ data.finalized_implementations|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ data.overall_score|e }}%</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    {% if data.level == 'Excelente' %}bg-green-100 text-green-800
                                    {% elif data.level == 'Bom' %}bg-blue-100 text-blue-800
                                    {% elif data.level == 'Razoável' %}bg-yellow-100 text-yellow-800
                                    {% elif data.level == 'Precisa de Atenção' %}bg-orange-100 text-orange-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                                    {{ data.level|e }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                {% if data.problem_fields %}
                                <ul class="list-disc pl-5">
                                    {% for field in data.problem_fields %}
                                    <li>
                                        {{ field.field|e }}
                                        <span class="text-xs
                                            {% if field.percentage < 50 %}text-red-600
                                            {% elif field.percentage < 70 %}text-orange-600
                                            {% elif field.percentage < 90 %}text-yellow-600
                                            {% else %}text-blue-600{% endif %}">
                                            ({{ field.percentage|e }}%)
                                        </span>
                                        {% if field.required %}
                                        <span class="text-xs bg-red-100 text-red-800 px-1 rounded">Obrigatório</span>
                                        {% endif %}
                                    </li>
                                    {% endfor %}
                                </ul>
                                {% else %}
                                <span class="text-green-600">Sem problemas identificados</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p class="text-sm text-gray-500">Não foi possível analisar a qualidade de dados por responsável.</p>
            {% endif %}
        </div>
    </div>

    <!-- Recomendações -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
        <div class="px-6 py-5 border-b border-gray-200">
            <h2 class="font-semibold text-xl text-gray-800">Recomendações</h2>
            <p class="text-sm text-gray-500 mt-1">Sugestões para melhorar a qualidade dos dados</p>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                {% if missing_data.required_fields_with_missing > 0 %}
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-gray-900">Preencher campos obrigatórios ausentes</h3>
                        <p class="mt-1 text-sm text-gray-500">Existem {{ missing_data.required_fields_with_missing|e }} campos obrigatórios com dados ausentes. Priorize o preenchimento desses campos para garantir a integridade dos dados.</p>
                    </div>
                </div>
                {% endif %}

                {% if missing_data.critical_columns > 0 %}
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-orange-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-gray-900">Preencher dados críticos ausentes</h3>
                        <p class="mt-1 text-sm text-gray-500">Existem {{ missing_data.critical_columns|e }} colunas com mais de 50% de dados ausentes. Priorize o preenchimento dessas colunas para melhorar a qualidade geral dos dados.</p>
                    </div>
                </div>
                {% endif %}

                {% if consistency_issues %}
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-gray-900">Corrigir inconsistências de formato</h3>
                        <p class="mt-1 text-sm text-gray-500">Foram identificadas inconsistências de formato em {{ consistency_issues|length }} colunas. Padronize os formatos de data, valores numéricos e status para melhorar a consistência dos dados.</p>
                    </div>
                </div>
                {% endif %}

                {% if quality_metrics.duplicate_percentage > 0 %}
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-orange-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-gray-900">Remover duplicações</h3>
                        <p class="mt-1 text-sm text-gray-500">Existem {{ quality_metrics.duplicate_rows|e }} linhas duplicadas ({{ quality_metrics.duplicate_percentage|e }}%). Remova as duplicações para melhorar a qualidade e reduzir o tamanho da base de dados.</p>
                    </div>
                </div>
                {% endif %}

                {% if responsible_quality %}
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-gray-900">Melhorar preenchimento por responsável</h3>
                        <p class="mt-1 text-sm text-gray-500">Alguns responsáveis apresentam baixa qualidade no preenchimento de dados. Realize treinamentos e estabeleça diretrizes claras para garantir o preenchimento correto de todos os campos.</p>
                    </div>
                </div>
                {% endif %}

                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-gray-900">Implementar validações de entrada</h3>
                        <p class="mt-1 text-sm text-gray-500">Implemente validações de entrada para garantir que novos dados sejam inseridos com o formato correto e campos obrigatórios preenchidos.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-gray-900">Monitorar qualidade regularmente</h3>
                        <p class="mt-1 text-sm text-gray-500">Estabeleça um processo de monitoramento regular da qualidade dos dados para identificar e corrigir problemas rapidamente.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
