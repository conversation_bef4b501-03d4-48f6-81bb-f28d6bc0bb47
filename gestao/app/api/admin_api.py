"""
Admin API routes for Business Domain
Provides REST API endpoints for admin functionality
"""

from flask import Blueprint, request, jsonify, session, Response
import sys
import os
from pathlib import Path
import logging
from typing import Dict, List, Any, Optional

# Add shared core to path
CORE_PATH = Path(__file__).parent.parent.parent.parent / 'shared' / 'datamesh-core'
sys.path.insert(0, str(CORE_PATH))

try:
    from admin.admin_service import AdminService
    from admin.monitoring_service import MonitoringService
    ADMIN_AVAILABLE = True
    print("✅ Admin service loaded successfully for Business domain")
except ImportError as e:
    ADMIN_AVAILABLE = False
    print(f"❌ Admin service not available: {e}")

logger = logging.getLogger(__name__)

admin_api = Blueprint('admin_api', __name__, url_prefix='/api/admin')

def require_admin() -> bool:
    """Check if user is admin"""
    # Check if user is logged in
    if 'user_id' not in session and 'username' not in session:
        return False

    # Check username-based admin access
    username = session.get('username', '')
    if username in ['admin', 'TTK', 'bruno@abreu', 'bruno@bruno']:
        return True

    # Check user_id-based admin access (for SecureAuthService)
    user_id = session.get('user_id')
    if user_id:
        try:
            from app.services.secure_auth_service import SecureAuthService
            auth_service = SecureAuthService()
            user = auth_service.get_user_by_id(user_id)
            if user and user.get('role') == 'admin':
                return True
        except Exception as e:
            logger.error(f"Error checking admin status: {e}")

    return False

@admin_api.route('/stats', methods=['GET'])
def get_admin_stats() -> Optional[Dict[str, Any]]:
    """Get admin dashboard statistics"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    try:
        # Use SecureAuthService to get stats
        from app.services.secure_auth_service import SecureAuthService
        auth_service = SecureAuthService()

        import sqlite3
        from datetime import datetime, timedelta

        stats = {}
        with sqlite3.connect(auth_service.db_path) as conn:
            cursor = conn.cursor()

            # Total users
            cursor.execute("SELECT COUNT(*) FROM users")
            stats['total_users'] = cursor.fetchone()[0]

            # Active users
            cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = 1")
            stats['active_users'] = cursor.fetchone()[0]

            # Admin users
            cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin' AND is_active = 1")
            stats['admin_users'] = cursor.fetchone()[0]

            # Recent logins (last 7 days)
            seven_days_ago = (datetime.now() - timedelta(days=7)).isoformat()
            cursor.execute("SELECT COUNT(*) FROM users WHERE last_login > ? AND is_active = 1", (seven_days_ago,))
            stats['recent_logins'] = cursor.fetchone()[0]

        return jsonify(stats)
    except Exception as e:
        logger.error(f"Error getting admin stats: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/users', methods=['GET'])
def get_users() -> Optional[Dict[str, Any]]:
    """Get all users"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    try:
        # Use SecureAuthService to get users
        from app.services.secure_auth_service import SecureAuthService
        auth_service = SecureAuthService()

        import sqlite3
        import json

        users = []
        with sqlite3.connect(auth_service.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, username, email, role, permissions, is_active,
                       created_at, last_login, metadata
                FROM users
                ORDER BY created_at DESC
            """)

            rows = cursor.fetchall()

            for row in rows:
                user_dict = dict(row)
                # Parse JSON fields
                user_dict['permissions'] = json.loads(user_dict.get('permissions', '[]'))
                user_dict['metadata'] = json.loads(user_dict.get('metadata', '{}'))
                # Convert boolean
                user_dict['is_active'] = bool(user_dict['is_active'])
                users.append(user_dict)

        return jsonify({'users': users})
    except Exception as e:
        logger.error(f"Error getting users: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/users/test', methods=['GET'])
def test_get_users() -> Response:
    """Test endpoint to get all users without auth (temporary)"""
    if not ADMIN_AVAILABLE:
        return jsonify({'error': 'Admin service not available'}), 503

    try:
        admin_service = AdminService('business')
        users = admin_service.get_all_users()
        return jsonify({
            'users': users,
            'count': len(users),
            'session_user': session.get('username', 'Not logged in'),
            'is_admin': require_admin()
        })
    except Exception as e:
        logger.error(f"Error getting users: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/users', methods=['POST'])
def create_user() -> Dict[str, Any]:
    """Create a new user"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    if not ADMIN_AVAILABLE:
        return jsonify({'error': 'Admin service not available'}), 503

    try:
        data = request.get_json()

        if not data or not all(k in data for k in ['username', 'email', 'password']):
            return jsonify({'error': 'Missing required fields'}), 400

        admin_service = AdminService('business')
        user = admin_service.create_user(
            username=data['username'],
            email=data['email'],
            password=data['password'],
            role=data.get('role', 'user'),
            permissions=data.get('permissions', [])
        )

        # Remove sensitive data
        if 'metadata' in user and 'password_hash' in user['metadata']:
            del user['metadata']['password_hash']

        return jsonify({'user': user}), 201
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/users/<username>/permissions', methods=['PUT'])
def update_user_permissions(username: str) -> bool:
    """Update user permissions"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    try:
        data = request.get_json()

        if not data or 'permissions' not in data:
            return jsonify({'error': 'Missing permissions field'}), 400

        # Use SecureAuthService to update permissions
        from app.services.secure_auth_service import SecureAuthService
        import sqlite3
        import json

        auth_service = SecureAuthService()

        with sqlite3.connect(auth_service.db_path) as conn:
            cursor = conn.cursor()

            # Update user permissions
            cursor.execute('''
                UPDATE users
                SET permissions = ?
                WHERE username = ? AND is_active = 1
            ''', (json.dumps(data['permissions']), username))

            if cursor.rowcount == 0:
                return jsonify({'error': 'User not found'}), 404

            conn.commit()

            # Log the permission update
            auth_service.log_activity(
                user_id=session.get('user_id', 'admin'),
                action='permissions_updated',
                description=f'Permissions updated for user {username}',
                metadata={
                    'target_user': username,
                    'new_permissions': data['permissions'],
                    'updated_by': session.get('username', 'admin')
                }
            )

        return jsonify({'message': 'Permissions updated successfully'})
    except Exception as e:
        logger.error(f"Error updating permissions: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/users/<username>/role', methods=['PUT'])
def update_user_role(username: str) -> bool:
    """Update user role"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    # Protect native admin
    if username == 'bruno@abreu':
        return jsonify({'error': 'Cannot modify native admin role'}), 403

    try:
        data = request.get_json()

        if not data or 'role' not in data:
            return jsonify({'error': 'Missing role field'}), 400

        role = data['role']
        if role not in ['admin', 'user']:
            return jsonify({'error': 'Invalid role. Must be admin or user'}), 400

        # Use SecureAuthService to update role
        from app.services.secure_auth_service import SecureAuthService
        import sqlite3
        import json

        auth_service = SecureAuthService()

        with sqlite3.connect(auth_service.db_path) as conn:
            cursor = conn.cursor()

            # Update user role and permissions based on role
            if role == 'admin':
                permissions = ['dashboard.view', 'admin.view', 'clustering.view', 'all']
            else:
                permissions = ['dashboard.view', 'clustering.view']

            cursor.execute('''
                UPDATE users
                SET role = ?, permissions = ?
                WHERE username = ? AND is_active = 1
            ''', (role, json.dumps(permissions), username))

            if cursor.rowcount == 0:
                return jsonify({'error': 'User not found'}), 404

            conn.commit()

            # Log the role update
            auth_service.log_activity(
                user_id=session.get('user_id', 'admin'),
                action='role_updated',
                description=f'Role updated for user {username} to {role}',
                metadata={
                    'target_user': username,
                    'new_role': role,
                    'updated_by': session.get('username', 'admin')
                }
            )

        return jsonify({'message': f'User role updated to {role} successfully'})
    except Exception as e:
        logger.error(f"Error updating user role: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/users/<username>/status', methods=['PUT'])
def update_user_status(username: str) -> bool:
    """Update user active status"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    # Protect native admin
    if username == 'bruno@abreu':
        return jsonify({'error': 'Cannot deactivate native admin'}), 403

    try:
        data = request.get_json()

        if not data or 'is_active' not in data:
            return jsonify({'error': 'Missing is_active field'}), 400

        is_active = bool(data['is_active'])

        # Use SecureAuthService to update status
        from app.services.secure_auth_service import SecureAuthService
        import sqlite3

        auth_service = SecureAuthService()

        with sqlite3.connect(auth_service.db_path) as conn:
            cursor = conn.cursor()

            # Update user status
            cursor.execute('''
                UPDATE users
                SET is_active = ?
                WHERE username = ?
            ''', (1 if is_active else 0, username))

            if cursor.rowcount == 0:
                return jsonify({'error': 'User not found'}), 404

            conn.commit()

            # Log the status update
            auth_service.log_activity(
                user_id=session.get('user_id', 'admin'),
                action='status_updated',
                description=f'User {username} {"activated" if is_active else "deactivated"}',
                metadata={
                    'target_user': username,
                    'new_status': 'active' if is_active else 'inactive',
                    'updated_by': session.get('username', 'admin')
                }
            )

        status_text = 'activated' if is_active else 'deactivated'
        return jsonify({'message': f'User {status_text} successfully'})
    except Exception as e:
        logger.error(f"Error updating user status: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/users/<username>/deactivate', methods=['POST'])
def deactivate_user(username: str) -> Response:
    """Deactivate a user"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    if not ADMIN_AVAILABLE:
        return jsonify({'error': 'Admin service not available'}), 503

    try:
        admin_service = AdminService('business')
        success = admin_service.deactivate_user(username)

        if not success:
            return jsonify({'error': 'User not found'}), 404

        return jsonify({'message': 'User deactivated successfully'})
    except Exception as e:
        logger.error(f"Error deactivating user: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/permissions', methods=['GET'])
def get_page_permissions() -> Optional[Dict[str, Any]]:
    """Get available page permissions"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    try:
        from app.services.permission_service import PermissionService

        permissions = PermissionService.PERMISSIONS
        return jsonify({'permissions': permissions})
    except Exception as e:
        logger.error(f"Error getting permissions: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/activity', methods=['GET'])
def get_activity_logs() -> Optional[Dict[str, Any]]:
    """Get activity logs"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    try:
        # Import secure auth service for logs
        from app.services.secure_auth_service import SecureAuthService

        limit = request.args.get('limit', 100, type=int)
        action_filter = request.args.get('action', None)

        auth_service = SecureAuthService()
        logs = auth_service.get_activity_logs(limit=limit, action_filter=action_filter)

        return jsonify({'logs': logs})
    except Exception as e:
        logger.error(f"Error getting activity logs: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/monitoring/dashboard', methods=['GET'])
def get_monitoring_dashboard() -> Optional[Dict[str, Any]]:
    """Get monitoring dashboard data"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    if not ADMIN_AVAILABLE:
        return jsonify({'error': 'Monitoring service not available'}), 503

    try:
        monitoring_service = MonitoringService('business')
        metrics = monitoring_service.get_dashboard_metrics()
        user_analytics = monitoring_service.get_user_analytics()
        page_analytics = monitoring_service.get_page_analytics()

        return jsonify({
            'metrics': metrics,
            'user_analytics': user_analytics,
            'page_analytics': page_analytics
        })
    except Exception as e:
        logger.error(f"Error getting monitoring data: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/monitoring/log', methods=['POST'])
def log_user_activity() -> Response:
    """Log user activity"""
    try:
        data = request.get_json()

        if not data or not all(k in data for k in ['user_id', 'action', 'page']):
            return jsonify({'error': 'Missing required fields'}), 400

        # Use secure auth service for logging
        from app.services.secure_auth_service import SecureAuthService

        auth_service = SecureAuthService()
        auth_service.log_activity(
            user_id=data['user_id'],
            action=data['action'],
            description=data.get('description', f"Ação: {data['action']}"),
            page=data['page'],
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            metadata=data.get('details', {})
        )

        return jsonify({'message': 'Activity logged successfully'})
    except Exception as e:
        logger.error(f"Error logging activity: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/invite', methods=['POST'])
def invite_user() -> Any:
    """Invite a new user"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    try:
        data = request.get_json()

        if not data or not all(k in data for k in ['email', 'role']):
            return jsonify({'error': 'Missing required fields'}), 400

        # Generate temporary password
        import secrets
        import string

        temp_password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))
        username = data['email'].split('@')[0]

        # Use SecureAuthService to create user
        from app.services.secure_auth_service import SecureAuthService
        auth_service = SecureAuthService()

        # Create user with temporary password
        success = auth_service.create_user(
            username=username,
            email=data['email'],
            password=temp_password,
            role=data['role'],
            permissions=data.get('permissions', []),
            domain='business'
        )

        if success:
            # Log the invitation
            auth_service.log_activity(
                user_id=session.get('user_id', 'admin'),
                action='user_invited',
                description=f'User {username} invited with role {data["role"]}',
                metadata={
                    'invited_user': username,
                    'invited_email': data['email'],
                    'role': data['role'],
                    'permissions': data.get('permissions', [])
                }
            )

            return jsonify({
                'message': 'User invited successfully',
                'username': username,
                'temporary_password': temp_password,
                'email': data['email'],
                'role': data['role'],
                'note': 'User created with temporary password. User should change password on first login.'
            }), 201
        else:
            return jsonify({'error': 'Failed to create user. Username or email may already exist.'}), 400

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error inviting user: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'error': 'Internal server error'}), 500
