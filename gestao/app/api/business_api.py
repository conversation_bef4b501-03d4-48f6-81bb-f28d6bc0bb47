"""
Business Domain API Routes - Hybrid Structured Pattern
Provides structured APIs for dynamic data updates following SSR+CSR pattern
"""

from flask import Blueprint, jsonify, session, request
from datetime import datetime
import logging
from typing import Dict, List, Any, Optional, Union

# Import controllers and services
from app.controllers.dashboard import <PERSON>board<PERSON>ontroller
from app.controllers.lead import LeadController
from app.controllers.implementation import ImplementationController
from app.controllers.intelligence_controller import IntelligenceController
from app.services.analytics import AnalyticsService
from app.services.data_processing import DataProcessingService

logger = logging.getLogger(__name__)

# Create API Blueprint with structured prefix
business_api = Blueprint('business_api', __name__, url_prefix='/api/business')

def require_auth() -> Optional[tuple]:
    """Check if user is authenticated for API calls"""
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401
    return None

def create_api_response(data: Any, status: str = 'success', message: str = None) -> Dict[str, Any]:
    """Create standardized API response"""
    response = {
        'status': status,
        'timestamp': datetime.now().isoformat(),
        'user': session.get('username', 'anonymous')
    }
    
    if status == 'success':
        response['data'] = data
    else:
        response['error'] = message or 'Unknown error'
        
    return response

# ============================================================================
# DASHBOARD APIs - Real-time metrics and analytics
# ============================================================================

@business_api.route('/dashboard/realtime')
def dashboard_realtime():
    """
    HYBRID STRUCTURED: Real-time dashboard metrics (CSR)
    Used by: HybridManager for dynamic updates
    Pattern: Client-side polling every 60s
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = DashboardController()
        
        # Get real-time metrics
        realtime_data = {
            'total_leads': controller.get_total_leads(),
            'active_implementations': controller.get_active_implementations(),
            'monthly_revenue': controller.get_monthly_revenue(),
            'conversion_rate': controller.get_conversion_rate(),
            'growth_metrics': {
                'leads_growth': controller.get_leads_growth(),
                'revenue_growth': controller.get_revenue_growth(),
                'implementation_growth': controller.get_implementation_growth()
            },
            'last_updated': datetime.now().isoformat()
        }
        
        return jsonify(create_api_response(realtime_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter métricas em tempo real: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

@business_api.route('/dashboard/metrics')
def dashboard_metrics():
    """
    HYBRID STRUCTURED: Dashboard metrics for charts (CSR)
    Used by: Chart components for dynamic data loading
    Pattern: On-demand loading for chart rendering
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = DashboardController()
        analytics_service = AnalyticsService()
        
        # Get chart data
        metrics_data = {
            'funnel_data': analytics_service.get_funnel_data(),
            'revenue_trend': analytics_service.get_revenue_trend(),
            'implementation_timeline': analytics_service.get_implementation_timeline(),
            'conversion_analytics': analytics_service.get_conversion_analytics(),
            'geographic_distribution': analytics_service.get_geographic_data()
        }
        
        return jsonify(create_api_response(metrics_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter métricas do dashboard: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

# ============================================================================
# LEADS APIs - Lead management and analytics
# ============================================================================

@business_api.route('/leads/analytics')
def leads_analytics():
    """
    HYBRID STRUCTURED: Lead analytics data (CSR)
    Used by: Lead dashboard for dynamic charts and metrics
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = LeadController()
        
        analytics_data = {
            'total_leads': controller.get_total_leads(),
            'leads_by_source': controller.get_leads_by_source(),
            'leads_by_status': controller.get_leads_by_status(),
            'conversion_funnel': controller.get_conversion_funnel(),
            'lead_quality_score': controller.get_lead_quality_metrics(),
            'monthly_trends': controller.get_monthly_trends()
        }
        
        return jsonify(create_api_response(analytics_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter analytics de leads: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

@business_api.route('/leads/search', methods=['POST'])
def leads_search():
    """
    HYBRID STRUCTURED: Dynamic lead search (CSR)
    Used by: Search components for real-time filtering
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        search_params = request.get_json() or {}
        controller = LeadController()
        
        search_results = controller.search_leads(
            query=search_params.get('query', ''),
            filters=search_params.get('filters', {}),
            sort_by=search_params.get('sort_by', 'created_at'),
            limit=search_params.get('limit', 50)
        )
        
        return jsonify(create_api_response(search_results))
        
    except Exception as e:
        logger.error(f"Erro na busca de leads: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

# ============================================================================
# IMPLEMENTATIONS APIs - Implementation tracking and progress
# ============================================================================

@business_api.route('/implementations/status')
def implementations_status():
    """
    HYBRID STRUCTURED: Implementation status overview (CSR)
    Used by: Implementation dashboard for real-time status updates
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = ImplementationController()
        
        status_data = {
            'active_implementations': controller.get_active_implementations(),
            'completed_implementations': controller.get_completed_implementations(),
            'pending_implementations': controller.get_pending_implementations(),
            'implementation_health': controller.get_implementation_health(),
            'timeline_data': controller.get_timeline_data(),
            'bottlenecks': controller.get_bottlenecks()
        }
        
        return jsonify(create_api_response(status_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter status de implementações: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

@business_api.route('/implementations/progress')
def implementations_progress():
    """
    HYBRID STRUCTURED: Real-time implementation progress (CSR)
    Used by: Progress tracking components
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = ImplementationController()
        
        progress_data = {
            'overall_progress': controller.get_overall_progress(),
            'individual_progress': controller.get_individual_progress(),
            'milestone_completion': controller.get_milestone_completion(),
            'estimated_completion': controller.get_estimated_completion(),
            'resource_utilization': controller.get_resource_utilization()
        }
        
        return jsonify(create_api_response(progress_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter progresso de implementações: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

# ============================================================================
# INTELLIGENCE APIs - AI insights and analytics
# ============================================================================

@business_api.route('/intelligence/insights')
def intelligence_insights():
    """
    HYBRID STRUCTURED: AI-generated business insights (CSR)
    Used by: Intelligence dashboard for dynamic insights
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = IntelligenceController()
        
        insights_data = {
            'business_insights': controller.get_business_insights(),
            'predictive_analytics': controller.get_predictive_analytics(),
            'anomaly_detection': controller.get_anomaly_detection(),
            'recommendations': controller.get_recommendations(),
            'market_trends': controller.get_market_trends()
        }
        
        return jsonify(create_api_response(insights_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter insights de intelligence: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

@business_api.route('/intelligence/clustering')
def intelligence_clustering():
    """
    HYBRID STRUCTURED: Clustering analysis data (CSR)
    Used by: Clustering visualization components
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = IntelligenceController()
        
        clustering_data = {
            'cluster_analysis': controller.get_cluster_analysis(),
            'cluster_characteristics': controller.get_cluster_characteristics(),
            'cluster_performance': controller.get_cluster_performance(),
            'cluster_recommendations': controller.get_cluster_recommendations()
        }
        
        return jsonify(create_api_response(clustering_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter dados de clustering: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

# ============================================================================
# HEALTH AND STATUS APIs
# ============================================================================

@business_api.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'business-domain-api',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@business_api.route('/status')
def status():
    """Detailed status endpoint"""
    try:
        return jsonify({
            'status': 'operational',
            'service': 'business-domain-api',
            'authenticated_user': session.get('username', 'anonymous'),
            'timestamp': datetime.now().isoformat(),
            'endpoints': {
                'dashboard': 'operational',
                'leads': 'operational',
                'implementations': 'operational',
                'intelligence': 'operational'
            },
            'version': '1.0.0'
        })
    except Exception as e:
        logger.error(f"Erro ao obter status: {e}")
        return jsonify({
            'status': 'error',
            'service': 'business-domain-api',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500
