from typing import Dict, List, Any, Optional, Union, Tuple
"""
Amigo DataHub - Course Model
"""

class Course:
    """Course Model"""
    
    def __init__(self: List[Any], data: Dict[str, Any]) -> None:
        """
        Initialize a course
        
        Args:
            data (dict): The course data
        """
        self.id = data.get('id')
        self.name = data.get('name')
        self.university = data.get('university')
        self.students = data.get('students', 0)
        self.implementations = data.get('implementations', 0)
        self.revenue = data.get('revenue', 0)
        self.classes = data.get('classes', [])
        self.data = data
    
    def to_dict(self: List[Any]) -> Dict[str, Any]:
        """
        Convert the course to a dictionary
        
        Returns:
            dict: The course as a dictionary
        """
        return {
            'id': self.id,
            'name': self.name,
            'university': self.university,
            'students': self.students,
            'implementations': self.implementations,
            'revenue': self.revenue,
            'classes': [cls.to_dict() if hasattr(cls, 'to_dict') else cls for cls in self.classes]
        }
    
    @classmethod
    def from_dict(cls: List[Any], data: Dict[str, Any]) -> Any:
        """
        Create a course from a dictionary
        
        Args:
            data (dict): The course data
            
        Returns:
            Course: The created course
        """
        return cls(data)
