from typing import Dict, List, Any, Optional, Union, Tuple
"""
Amigo DataHub - Dimension Model
"""

class Dimension:
    """Dimension Model"""
    
    def __init__(self: List[Any], name: str, data: Dict[str, Any]) -> None:
        """
        Initialize a dimension
        
        Args:
            name (str): The dimension name
            data (DataFrame): The dimension data
        """
        self.name = name
        self.data = data
    
    def to_dict(self: List[Any]) -> Dict[str, Any]:
        """
        Convert the dimension to a dictionary
        
        Returns:
            dict: The dimension as a dictionary
        """
        return {
            'name': self.name,
            'data': self.data.to_dict('records') if self.data is not None else []
        }
    
    def get_value(self: List[Any], key: List[Any], value_column: Any, key_column: List[Any] = 'id') -> Optional[Dict[str, Any]]:
        """
        Get a value from the dimension
        
        Args:
            key: The key to look for
            value_column (str): The column to get the value from
            key_column (str): The column to look for the key in
            
        Returns:
            The value or None if not found
        """
        if self.data is None:
            return None
        
        if key_column not in self.data.columns or value_column not in self.data.columns:
            return None
        
        row = self.data[self.data[key_column] == key]
        
        if row.empty:
            return None
        
        return row[value_column].iloc[0]
