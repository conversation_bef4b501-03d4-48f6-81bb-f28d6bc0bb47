from typing import Dict, List, Any, Optional, Union, Tuple
"""
Amigo DataHub - University Model
"""

class University:
    """University Model"""
    
    def __init__(self: Any, data: Dict[str, Any]) -> None:
        """
        Initialize a university
        
        Args:
            data (dict): The university data
        """
        self.id = data.get('Universidade_id')
        self.name = data.get('Universidade')
        self.city = data.get('Cidade')
        self.state = data.get('Estado')
        self.active = data.get('active', False)
        self.data = data
    
    def to_dict(self: Any) -> Dict[str, Any]:
        """
        Convert the university to a dictionary
        
        Returns:
            dict: The university as a dictionary
        """
        return {
            'id': self.id,
            'name': self.name,
            'city': self.city,
            'state': self.state,
            'active': self.active
        }
    
    @classmethod
    def from_dict(cls: Any, data: Dict[str, Any]) -> Any:
        """
        Create a university from a dictionary
        
        Args:
            data (dict): The university data
            
        Returns:
            University: The created university
        """
        return cls(data)
