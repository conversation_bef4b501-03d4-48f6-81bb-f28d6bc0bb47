"""
Admin routes for Business Domain
"""

from flask import Blueprint, render_template, session, redirect, url_for, flash
import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from app.services.permission_service import require_admin, require_permission

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')
logger = logging.getLogger(__name__)

@admin_bp.route('/user-management')
@require_admin("Apenas administradores podem gerenciar usuários.")
def user_management() -> str:
    """Admin users management page"""
    logger.info(f"Usuário {session.get('username')} acessando painel admin de usuários")
    return render_template('admin/user_management_complete.html')

@admin_bp.route('/users')
@require_admin("Apenas administradores podem gerenciar usuários.")
def users() -> str:
    """Admin users management page (legacy route)"""
    return redirect(url_for('admin.user_management'))

@admin_bp.route('/monitoring')
@require_permission('admin.view', "Você precisa de permissão de administrador para acessar o monitoramento.")
def monitoring() -> str:
    """Admin monitoring page"""
    logger.info(f"Usuário {session.get('username')} acessando painel de monitoramento")
    return render_template('admin/monitoring_complete.html')
