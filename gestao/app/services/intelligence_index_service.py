"""
Amigo DataHub - Intelligence Index Service
Advanced behavioral analysis using composite performance index
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class IntelligenceIndexService:
    """Service for calculating and analyzing composite intelligence index"""

    def __init__(self, data_loader):
        """Initialize with data loader"""
        self.data_loader = data_loader
        self.df = data_loader.df if data_loader else None

        if self.df is not None:
            logger.info(f"Intelligence index data loaded: {len(self.df)} records")
        else:
            logger.warning("No data available for intelligence index analysis")

    def calculate_composite_index(self, dimension_col: str = None, dimension_value: str = None) -> Dict[str, Any]:
        """Calculate composite intelligence index with confidence factor based on business volume"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Filter data if dimension specified
            if dimension_col and dimension_value:
                filtered_df = self.df[self.df[dimension_col] == dimension_value].copy()
                if filtered_df.empty:
                    return {"error": f"No data for {dimension_col}={dimension_value}"}
            else:
                filtered_df = self.df.copy()

            # Component 1: Conversion Rate (Opportunity to Implementation)
            total_opportunities = filtered_df['Oportunidade_id'].nunique()
            opportunities_with_impl = filtered_df[filtered_df['Data_Criacao_Implantacao'].notna()]['Oportunidade_id'].nunique()

            raw_conversion_rate = (opportunities_with_impl / total_opportunities * 100) if total_opportunities > 0 else 0

            # Component 2: Revenue from authorized invoices
            invoice_df = filtered_df[filtered_df['Gerou_Nota_Fiscal'] == 'Sim'].copy()
            invoice_df['Faturamento_total'] = pd.to_numeric(invoice_df['Faturamento_total'], errors='coerce')
            total_revenue = invoice_df['Faturamento_total'].sum()

            # Component 3: Active users with at least 1 invoice
            active_users = len(invoice_df)

            # Component 4: Implementation success rate
            total_implementations = len(filtered_df[filtered_df['Data_Criacao_Implantacao'].notna()])
            successful_implementations = len(filtered_df[
                (filtered_df['Data_Criacao_Implantacao'].notna()) &
                (filtered_df['Gerou_Nota_Fiscal'] == 'Sim')
            ])

            raw_implementation_success_rate = (successful_implementations / total_implementations * 100) if total_implementations > 0 else 0

            # ECONOMETRIC ADJUSTMENT: Confidence Factor based on business volume
            # Wilson Score Interval approach for small sample bias correction
            confidence_factor = self._calculate_confidence_factor(total_opportunities, total_implementations)

            # Apply confidence adjustment to rates (Bayesian shrinkage towards global mean)
            global_conversion_rate = self._get_global_conversion_rate()
            global_success_rate = self._get_global_success_rate()

            # Adjusted rates using confidence factor
            conversion_rate = (raw_conversion_rate * confidence_factor +
                             global_conversion_rate * (1 - confidence_factor))

            implementation_success_rate = (raw_implementation_success_rate * confidence_factor +
                                         global_success_rate * (1 - confidence_factor))

            # Normalize components (0-100 scale)
            comp1 = conversion_rate  # Already in percentage (adjusted)

            # Revenue component (logarithmic scale)
            if total_revenue > 0:
                revenue_log = np.log10(total_revenue)
                comp2 = min(max((revenue_log - 6) * 20, 0), 100)
            else:
                comp2 = 0

            # Active users density
            total_records = len(filtered_df)
            user_density = (active_users / total_records * 1000) if total_records > 0 else 0
            comp3 = min(user_density * 2, 100)

            # Implementation success rate (adjusted)
            comp4 = implementation_success_rate

            # Volume factor (new component)
            volume_score = self._calculate_volume_score(total_opportunities, total_implementations)

            # Updated composite index with volume consideration
            weights = {
                'conversion': 0.25,    # Reduced from 0.30
                'revenue': 0.25,       # Maintained
                'users': 0.15,         # Reduced from 0.20
                'success': 0.20,       # Reduced from 0.25
                'volume': 0.15         # New component
            }

            composite_index = (
                comp1 * weights['conversion'] +
                comp2 * weights['revenue'] +
                comp3 * weights['users'] +
                comp4 * weights['success'] +
                volume_score * weights['volume']
            )

            return {
                'composite_index': round(composite_index, 2),
                'components': {
                    'conversion_rate': round(conversion_rate, 2),
                    'revenue_score': round(comp2, 2),
                    'user_density': round(comp3, 2),
                    'success_rate': round(implementation_success_rate, 2),
                    'volume_score': round(volume_score, 2)
                },
                'raw_metrics': {
                    'total_opportunities': total_opportunities,
                    'opportunities_with_impl': opportunities_with_impl,
                    'total_revenue': total_revenue,
                    'active_users': active_users,
                    'total_implementations': total_implementations,
                    'successful_implementations': successful_implementations,
                    'total_records': total_records,
                    'raw_conversion_rate': round(raw_conversion_rate, 2),
                    'raw_success_rate': round(raw_implementation_success_rate, 2),
                    'confidence_factor': round(confidence_factor, 3)
                },
                'weights': weights,
                'success': True
            }

        except Exception as e:
            logger.error(f"Error calculating composite index: {e}")
            return {"error": str(e)}

    def analyze_by_dimension(self, dimension_col: str) -> Dict[str, Any]:
        """Analyze index by specific dimension (turma, estado, responsavel)"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            if dimension_col not in self.df.columns:
                return {"error": f"Column {dimension_col} not found"}

            # Get unique values for the dimension
            unique_values = self.df[dimension_col].dropna().unique()

            results = []
            for value in unique_values:
                index_result = self.calculate_composite_index(dimension_col, value)
                if 'error' not in index_result:
                    results.append({
                        'dimension_value': value,
                        'composite_index': index_result['composite_index'],
                        'components': index_result['components'],
                        'raw_metrics': index_result['raw_metrics']
                    })

            # Sort by composite index
            results.sort(key=lambda x: x['composite_index'], reverse=True)

            # Calculate statistics
            indices = [r['composite_index'] for r in results]
            stats = {
                'mean': np.mean(indices),
                'median': np.median(indices),
                'std': np.std(indices),
                'min': np.min(indices),
                'max': np.max(indices),
                'count': len(indices)
            }

            return {
                'dimension': dimension_col,
                'results': results,
                'statistics': stats,
                'top_performers': results[:10],
                'bottom_performers': results[-5:] if len(results) > 5 else [],
                'success': True
            }

        except Exception as e:
            logger.error(f"Error analyzing by dimension {dimension_col}: {e}")
            return {"error": str(e)}

    def get_overall_intelligence_summary(self) -> Dict[str, Any]:
        """Get overall intelligence summary with key insights"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Overall index
            overall_index = self.calculate_composite_index()

            # Analysis by key dimensions
            dimensions_analysis = {}
            key_dimensions = ['Turma', 'Estado', 'Nome_Responsavel']

            for dim in key_dimensions:
                if dim in self.df.columns:
                    dim_analysis = self.analyze_by_dimension(dim)
                    if 'error' not in dim_analysis:
                        dimensions_analysis[dim] = {
                            'top_performer': dim_analysis['top_performers'][0] if dim_analysis['top_performers'] else None,
                            'statistics': dim_analysis['statistics'],
                            'total_analyzed': len(dim_analysis['results'])
                        }

            # Performance distribution
            all_indices = []
            for dim, analysis in dimensions_analysis.items():
                if 'top_performer' in analysis and analysis['top_performer']:
                    all_indices.extend([r['composite_index'] for r in self.analyze_by_dimension(dim)['results']])

            performance_distribution = {
                'excellent': len([i for i in all_indices if i >= 70]),
                'good': len([i for i in all_indices if 50 <= i < 70]),
                'average': len([i for i in all_indices if 30 <= i < 50]),
                'poor': len([i for i in all_indices if i < 30])
            } if all_indices else {}

            return {
                'overall_index': overall_index,
                'dimensions_analysis': dimensions_analysis,
                'performance_distribution': performance_distribution,
                'insights': self._generate_insights(overall_index, dimensions_analysis),
                'success': True
            }

        except Exception as e:
            logger.error(f"Error getting intelligence summary: {e}")
            return {"error": str(e)}

    def _generate_insights(self, overall_index: Dict, dimensions_analysis: Dict) -> List[str]:
        """Generate actionable insights from the analysis"""
        insights = []

        if 'error' not in overall_index:
            index_value = overall_index['composite_index']

            if index_value >= 70:
                insights.append("🎯 Performance excepcional: Índice acima de 70 pontos")
            elif index_value >= 50:
                insights.append("📈 Performance boa: Há oportunidades de melhoria")
            else:
                insights.append("⚠️ Performance abaixo da média: Requer atenção imediata")

            # Conversion rate insights
            conv_rate = overall_index['components']['conversion_rate']
            if conv_rate < 15:
                insights.append("🔄 Taxa de conversão baixa: Revisar processo de vendas")
            elif conv_rate > 25:
                insights.append("✅ Excelente taxa de conversão oportunidade-implementação")

            # Success rate insights
            success_rate = overall_index['components']['success_rate']
            if success_rate < 10:
                insights.append("⚠️ Taxa de sucesso de implementações crítica")
            elif success_rate > 15:
                insights.append("🚀 Boa taxa de sucesso nas implementações")

        # Dimension-specific insights
        for dim, analysis in dimensions_analysis.items():
            if analysis and 'top_performer' in analysis and analysis['top_performer']:
                top = analysis['top_performer']
                insights.append(f"🏆 Melhor {dim}: {top['dimension_value']} (Índice: {top['composite_index']:.1f})")

        return insights

    def _calculate_confidence_factor(self, total_opportunities: int, total_implementations: int) -> float:
        """
        Calculate confidence factor based on business volume using Wilson Score approach
        Higher volume = higher confidence in the rates
        """
        # Use the minimum of opportunities and implementations as the effective sample size
        effective_sample_size = min(total_opportunities, total_implementations)

        # Wilson Score confidence factor (95% confidence level)
        # Formula: confidence = n / (n + z²) where z = 1.96 for 95% confidence
        z_score = 1.96  # 95% confidence level
        confidence_factor = effective_sample_size / (effective_sample_size + z_score**2)

        # Apply minimum threshold to avoid over-penalizing small samples
        min_confidence = 0.1  # Minimum 10% confidence
        return max(confidence_factor, min_confidence)

    def _get_global_conversion_rate(self) -> float:
        """Get global conversion rate as baseline for Bayesian adjustment"""
        if self.df is None or self.df.empty:
            return 21.35  # Default based on our analysis

        total_opps = self.df['Oportunidade_id'].nunique()
        opps_with_impl = self.df[self.df['Data_Criacao_Implantacao'].notna()]['Oportunidade_id'].nunique()

        return (opps_with_impl / total_opps * 100) if total_opps > 0 else 21.35

    def _get_global_success_rate(self) -> float:
        """Get global implementation success rate as baseline for Bayesian adjustment"""
        if self.df is None or self.df.empty:
            return 31.5  # Default based on our analysis (1776/5630 * 100)

        total_impl = len(self.df[self.df['Data_Criacao_Implantacao'].notna()])
        successful_impl = len(self.df[
            (self.df['Data_Criacao_Implantacao'].notna()) &
            (self.df['Gerou_Nota_Fiscal'] == 'Sim')
        ])

        return (successful_impl / total_impl * 100) if total_impl > 0 else 31.5

    def _calculate_volume_score(self, total_opportunities: int, total_implementations: int) -> float:
        """
        Calculate volume score based on business volume
        Rewards entities with substantial business volume
        """
        # Combined volume metric
        combined_volume = total_opportunities + total_implementations

        # Logarithmic scaling to prevent large entities from dominating
        if combined_volume > 0:
            # Use log base 10 and scale to 0-100
            log_volume = np.log10(combined_volume + 1)  # +1 to handle log(0)

            # Scale: log10(10) = 1 → 20 points, log10(100) = 2 → 40 points, etc.
            volume_score = min(log_volume * 20, 100)

            # Apply minimum threshold for very small volumes
            if combined_volume < 5:
                volume_score *= 0.5  # Penalize very small samples

            return volume_score

        return 0
