"""
Amigo DataHub - Analytics Service
Service for analytics and insights
"""

import pandas as pd
import numpy as np
import logging
from app.utils.formatters import safe_convert_to_float, format_currency
from app.utils.safe_operations import safe_division, safe_percentage
from typing import Dict, List, Any, Optional, Union, Tuple
import pandas as pd

logger = logging.getLogger(__name__)

class AnalyticsService:
    """Analytics Service"""

    def __init__(self: List[Any], data_loader: Dict[str, Any], business_rules: List[Any]) -> None:
        """
        Initialize the analytics service

        Args:
            data_loader: The data loader service
            business_rules: The business rules service
        """
        self.data_loader = data_loader
        self.business_rules = business_rules

    def get_top_university_data(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the top university data

        Returns:
            tuple: (top_university_name, top_university_percentage)
        """
        try:
            df = self.data_loader.get_data()

            if 'Universidade' not in df.columns:
                return "Não disponível", "0"

            # Count leads by university
            university_counts = df['Universidade'].value_counts()

            if university_counts.empty:
                return "Não disponível", "0"

            # Get the top university
            top_university = university_counts.index[0]
            top_count = university_counts.iloc[0]

            # Calculate the percentage
            total_leads = len(df)
            percentage = safe_percentage(top_count, total_leads)

            return top_university, f"{percentage:.1f}"
        except Exception as e:
            logger.error(f"Error getting top university data: {e}")
            return "Não disponível", "0"

    def get_top_course_data(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the top course data

        Returns:
            tuple: (top_course_name, top_course_count)
        """
        try:
            df = self.data_loader.get_data()

            if 'Curso' not in df.columns:
                return "Não disponível", "0"

            # Count leads by course
            course_counts = df['Curso'].value_counts()

            if course_counts.empty:
                return "Não disponível", "0"

            # Get the top course
            top_course = course_counts.index[0]
            top_count = course_counts.iloc[0]

            return top_course, str(top_count)
        except Exception as e:
            logger.error(f"Error getting top course data: {e}")
            return "Não disponível", "0"

    def get_top_revenue_data(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the top revenue data

        Returns:
            tuple: (top_revenue_university, top_revenue_value)
        """
        try:
            df = self.data_loader.get_data()

            if 'Universidade' not in df.columns or 'Valor Mensalidade' not in df.columns or 'Status_Implantacao' not in df.columns:
                return "Não disponível", "R$ 0,00"

            # Filter finalized implementations
            finalized_df = df[df['Status_Implantacao'] == 'Finalizado'].copy()

            if finalized_df.empty:
                return "Não disponível", "R$ 0,00"

            # Convert currency values to float
            finalized_df['Valor_Float'] = finalized_df['Valor Mensalidade'].apply(safe_convert_to_float)

            # Group by university and sum the values
            university_revenue = finalized_df.groupby('Universidade')['Valor_Float'].sum()

            if university_revenue.empty:
                return "Não disponível", "R$ 0,00"

            # Get the top university by revenue
            top_university = university_revenue.idxmax()
            top_value = university_revenue.max()

            return top_university, format_currency(top_value)
        except Exception as e:
            logger.error(f"Error getting top revenue data: {e}")
            return "Não disponível", "R$ 0,00"

    def get_conversion_by_product(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the conversion rate by product

        Returns:
            dict: The conversion rate by product
        """
        try:
            df = self.data_loader.get_data()
            opp_id_col = self.data_loader.get_opportunity_id_column()

            if 'Produto' not in df.columns or not opp_id_col or 'Fase_Implantacao' not in df.columns:
                return {}

            # Group by product
            product_conversion = {}

            # Get unique products
            products = df['Produto'].dropna().unique()

            for product in products:
                # Filter opportunities by product
                product_opps = df[df['Produto'] == product][opp_id_col].dropna().nunique()

                # Filter implementations by product
                product_impls = df[(df['Produto'] == product) & df['Fase_Implantacao'].notna()][opp_id_col].nunique()

                # Calculate conversion rate
                conversion_rate = safe_percentage(product_impls, product_opps)

                product_conversion[product] = f"{conversion_rate:.1f}"

            return product_conversion
        except Exception as e:
            logger.error(f"Error getting conversion by product: {e}")
            return {}

    def get_conversion_by_state(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the conversion rate by state

        Returns:
            dict: The conversion rate by state
        """
        try:
            df = self.data_loader.get_data()
            lead_id_col = self.data_loader.get_lead_id_column()

            if 'Estado' not in df.columns or not lead_id_col or 'Fase_Implantacao' not in df.columns:
                return {}

            # Group by state
            state_conversion = {}

            # Get unique states
            states = df['Estado'].dropna().unique()

            for state in states:
                # Filter leads by state
                state_leads = df[df['Estado'] == state][lead_id_col].dropna().nunique()

                # Filter implementations by state
                state_impls = df[(df['Estado'] == state) & df['Fase_Implantacao'].notna()][lead_id_col].nunique()

                # Calculate conversion rate
                conversion_rate = safe_percentage(state_impls, state_leads)

                state_conversion[state] = f"{conversion_rate:.1f}"

            return state_conversion
        except Exception as e:
            logger.error(f"Error getting conversion by state: {e}")
            return {}

    def get_conversion_dropoff(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the conversion dropoff between funnel stages

        Returns:
            dict: The conversion dropoff
        """
        try:
            df = self.data_loader.get_data()

            if 'Etapa do funil Comercial' not in df.columns:
                return {}

            # Get funnel stages
            funnel_stages = sorted(df['Etapa do funil Comercial'].dropna().unique())

            # Calculate dropoff between stages
            dropoff = {}

            for i in range(len(funnel_stages) - 1):
                current_stage = funnel_stages[i]
                next_stage = funnel_stages[i + 1]

                # Count leads in current stage
                current_count = len(df[df['Etapa do funil Comercial'] == current_stage])

                # Count leads in next stage
                next_count = len(df[df['Etapa do funil Comercial'] == next_stage])

                # Calculate conversion rate
                conversion_rate = safe_percentage(next_count, current_count)

                dropoff[f"{current_stage}→{next_stage}"] = f"{conversion_rate:.1f}"

            return dropoff
        except Exception as e:
            logger.error(f"Error getting conversion dropoff: {e}")
            return {}

    def get_time_by_stage(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the average time spent in each funnel stage

        Returns:
            dict: The average time by stage
        """
        try:
            df = self.data_loader.get_data()

            if 'Etapa do funil Comercial' not in df.columns or 'Data_criacao_Oportunidade' not in df.columns:
                logger.warning("Required columns for time by stage calculation not found")
                return {'error': 'Dados não disponíveis'}

            # Get funnel stages
            funnel_stages = sorted(df['Etapa do funil Comercial'].dropna().unique())

            # Check if we have the necessary data for calculation
            if 'Data_Mudanca_Estagio' not in df.columns:
                logger.warning("Column 'Data_Mudanca_Estagio' required for time by stage calculation not found")
                # Use Data_criacao_Oportunidade as a fallback
                if 'Data_criacao_Oportunidade' in df.columns:
                    logger.info("Using Data_criacao_Oportunidade as fallback for Data_Mudanca_Estagio")
                    df['Data_Mudanca_Estagio'] = df['Data_criacao_Oportunidade']
                else:
                    return {'error': 'Dados históricos não disponíveis para este cálculo'}

            # Initialize result dictionary
            time_by_stage = {}

            # For each funnel stage, calculate the average time spent
            for stage in funnel_stages:
                # In a real implementation, we would calculate the average time spent in each stage
                # based on the difference between stage change dates
                # Since we don't have this data, we return an error message
                time_by_stage[str(stage)] = 'N/A'

            return {'error': 'Dados históricos não disponíveis para este cálculo'}
        except Exception as e:
            logger.error(f"Error getting time by stage: {e}")
            return {'error': 'Erro ao calcular tempo por estágio'}

    def get_mrr_by_responsible(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the MRR by responsible

        Returns:
            dict: The MRR by responsible
        """
        try:
            _, _, responsible_mrr, _ = self.business_rules.calculate_mrr()

            # Format the values
            formatted_mrr = {k: format_currency(v) for k, v in responsible_mrr.items()}

            return formatted_mrr
        except Exception as e:
            logger.error(f"Error getting MRR by responsible: {e}")
            return {}

    def get_mrr_by_product(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the MRR by product

        Returns:
            dict: The MRR by product
        """
        try:
            _, _, _, product_mrr = self.business_rules.calculate_mrr()

            # Format the values
            formatted_mrr = {k: format_currency(v) for k, v in product_mrr.items()}

            return formatted_mrr
        except Exception as e:
            logger.error(f"Error getting MRR by product: {e}")
            return {}

    def get_mrr_by_university(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the MRR by university

        Returns:
            tuple: (mrr_by_university, mrr_percentage_by_university, total_mrr)
        """
        try:
            df = self.data_loader.get_data()

            if 'Universidade' not in df.columns or 'Valor Mensalidade' not in df.columns or 'Status_Implantacao' not in df.columns:
                return {}, {}, 0

            # Filter finalized implementations
            finalized_df = df[df['Status_Implantacao'] == 'Finalizado'].copy()

            if finalized_df.empty:
                return {}, {}, 0

            # Convert currency values to float
            finalized_df['Valor_Float'] = finalized_df['Valor Mensalidade'].apply(safe_convert_to_float)

            # Group by university and sum the values
            university_mrr = finalized_df.groupby('Universidade')['Valor_Float'].sum().to_dict()

            # Calculate total MRR
            total_mrr = sum(university_mrr.values())

            # Calculate percentage for each university
            university_percentage = {}
            for uni, mrr in university_mrr.items():
                percentage = safe_percentage(mrr, total_mrr)
                university_percentage[uni] = f"{percentage:.1f}"

            # Format the values
            formatted_mrr = {k: format_currency(v) for k, v in university_mrr.items()}

            return formatted_mrr, university_percentage, total_mrr
        except Exception as e:
            logger.error(f"Error getting MRR by university: {e}")
            return {}, {}, 0

    def get_mrr_by_state(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the MRR by state

        Returns:
            tuple: (mrr_by_state, mrr_percentage_by_state, total_mrr)
        """
        try:
            df = self.data_loader.get_data()

            if 'Estado' not in df.columns or 'Valor Mensalidade' not in df.columns or 'Status_Implantacao' not in df.columns:
                logger.warning("Required columns for MRR by state calculation not found")
                return {}, {}, 0

            # Filter finalized implementations
            finalized_df = df[df['Status_Implantacao'] == 'Finalizado'].copy()

            if finalized_df.empty:
                return {}, {}, 0

            # Convert currency values to float
            finalized_df['Valor_Float'] = finalized_df['Valor Mensalidade'].apply(safe_convert_to_float)

            # Group by state and sum the values
            state_mrr = finalized_df.groupby('Estado')['Valor_Float'].sum().to_dict()

            # Calculate total MRR
            total_mrr = sum(state_mrr.values())

            # Calculate percentage for each state
            state_percentage = {}
            for state, mrr in state_mrr.items():
                percentage = safe_percentage(mrr, total_mrr)
                state_percentage[state] = f"{percentage:.1f}"

            # Format the values
            formatted_mrr = {k: format_currency(v) for k, v in state_mrr.items()}

            return formatted_mrr, state_percentage, total_mrr
        except Exception as e:
            logger.error(f"Error getting MRR by state: {e}")
            return {}, {}, 0

    def get_monthly_opportunities(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the number of opportunities by month

        Returns:
            dict: The number of opportunities by month
        """
        try:
            df = self.data_loader.get_data()
            opp_id_col = self.data_loader.get_opportunity_id_column()

            # Check if we have Data_criacao_Oportunidade column
            if 'Data_criacao_Oportunidade' not in df.columns:
                logger.warning("Column 'Data_criacao_Oportunidade' required for monthly opportunities calculation not found")
                # Try to use Data_criacao_lead as fallback
                if 'Data_criacao_lead' in df.columns:
                    logger.info("Using Data_criacao_lead as fallback for Data_criacao_Oportunidade")
                    df['Data_criacao_Oportunidade'] = df['Data_criacao_lead']
                # If not, try to use Data_Criacao_Implantacao as fallback
                elif 'Data_Criacao_Implantacao' in df.columns:
                    logger.info("Using Data_Criacao_Implantacao as fallback for Data_criacao_Oportunidade")
                    df['Data_criacao_Oportunidade'] = df['Data_Criacao_Implantacao']
                else:
                    # Create a default date column with today's date
                    logger.info("Creating default Data_criacao_Oportunidade with today's date")
                    df['Data_criacao_Oportunidade'] = pd.Timestamp.now().strftime('%Y-%m-%d')
                    df['Data_criacao_Oportunidade'] = pd.to_datetime(df['Data_criacao_Oportunidade'], format='%Y-%m-%d')

            if not opp_id_col:
                return {}

            # Make sure the date column is datetime
            if not pd.api.types.is_datetime64_any_dtype(df['Data_criacao_Oportunidade']):
                df['Data_criacao_Oportunidade'] = pd.to_datetime(df['Data_criacao_Oportunidade'], format='%Y-%m-%d', errors='coerce')

            # Group by month
            df['Month'] = df['Data_criacao_Oportunidade'].dt.strftime('%Y-%m')

            # Count opportunities by month
            monthly_opps = df.groupby('Month')[opp_id_col].nunique().to_dict()

            # If we have no data, log a warning
            if not monthly_opps:
                logger.warning("No monthly opportunities data found")

            # Convert to strings
            monthly_opps = {k: str(v) for k, v in monthly_opps.items()}

            return monthly_opps
        except Exception as e:
            logger.error(f"Error getting monthly opportunities: {e}")
            return {}

    def get_opportunity_types(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the distribution of opportunity types

        Returns:
            dict: The distribution of opportunity types
        """
        try:
            df = self.data_loader.get_data()

            if 'Produto' not in df.columns:
                return {}

            # Count opportunities by product
            opp_types = df['Produto'].value_counts().to_dict()

            # Convert to strings
            opp_types = {k: str(v) for k, v in opp_types.items()}

            return opp_types
        except Exception as e:
            logger.error(f"Error getting opportunity types: {e}")
            return {}

    def get_monthly_mrr_data(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the monthly MRR data for the chart

        Returns:
            tuple: (labels, values) for the MRR chart
        """
        try:
            df = self.data_loader.get_data()

            if 'Status_Implantacao' not in df.columns or 'Valor Mensalidade' not in df.columns:
                logger.warning("Required columns for monthly MRR calculation not found")
                return [], []

            # Check if we have date information - use Data_Criacao_Implantacao directly
            if 'Data_Criacao_Implantacao' not in df.columns:
                logger.warning("Column 'Data_Criacao_Implantacao' required for monthly MRR calculation not found")
                # Try to use Data_criacao_Oportunidade as fallback
                if 'Data_criacao_Oportunidade' in df.columns:
                    logger.info("Using Data_criacao_Oportunidade as fallback for Data_Criacao_Implantacao")
                    df['Data_Criacao_Implantacao'] = df['Data_criacao_Oportunidade']
                # If not, try to use Data_criacao_lead as fallback
                elif 'Data_criacao_lead' in df.columns:
                    logger.info("Using Data_criacao_lead as fallback for Data_Criacao_Implantacao")
                    df['Data_Criacao_Implantacao'] = df['Data_criacao_lead']
                else:
                    # Create a default date column with today's date
                    logger.info("Creating default Data_Criacao_Implantacao with today's date")
                    df['Data_Criacao_Implantacao'] = pd.Timestamp.now().strftime('%Y-%m-%d')
                    df['Data_Criacao_Implantacao'] = pd.to_datetime(df['Data_Criacao_Implantacao'], format='%Y-%m-%d')

            # Filter finalized implementations
            finalized_df = df[df['Status_Implantacao'] == 'Finalizado'].copy()

            # If we have no finalized implementations, return empty data with error message
            if finalized_df.empty:
                logger.warning("No finalized implementations found for MRR calculation")
                return [], []

            # Convert currency values to float
            finalized_df['Valor_Float'] = finalized_df['Valor Mensalidade'].apply(safe_convert_to_float)

            # Make sure the date column is datetime
            if not pd.api.types.is_datetime64_any_dtype(finalized_df['Data_Criacao_Implantacao']):
                finalized_df['Data_Criacao_Implantacao'] = pd.to_datetime(finalized_df['Data_Criacao_Implantacao'], format='%Y-%m-%d', errors='coerce')

            # Group by month and calculate cumulative MRR
            finalized_df['Month'] = finalized_df['Data_Criacao_Implantacao'].dt.strftime('%Y-%m')
            monthly_mrr = finalized_df.groupby('Month')['Valor_Float'].sum()

            # Sort by month
            monthly_mrr = monthly_mrr.sort_index()

            # Calculate cumulative MRR
            cumulative_mrr = monthly_mrr.cumsum()

            # If we have less than 12 months of data, fill in the gaps
            if len(cumulative_mrr) < 12:
                # Get the current date for reference
                _ = pd.Timestamp.now()

                # Generate a range of 12 months ending with the current month
                months = pd.date_range(end=pd.Timestamp.now(), periods=12, freq='M')
                months = [month.strftime('%Y-%m') for month in months]

                # Create a new series with all months
                full_mrr = pd.Series(index=months, dtype=float)

                # Fill with the cumulative MRR where available
                for month, value in cumulative_mrr.items():
                    if month in full_mrr.index:
                        full_mrr[month] = value

                # Forward fill missing values
                full_mrr = full_mrr.ffill()

                # If we still have NaN values at the beginning, fill with 0
                full_mrr = full_mrr.fillna(0)

                # Update cumulative_mrr
                cumulative_mrr = full_mrr

            # Filter for 2024 and later
            filtered_months = [m for m in cumulative_mrr.index if m.startswith('2024') or m > '2024']

            # If no data from 2024, use all data
            if not filtered_months:
                filtered_months = cumulative_mrr.index

            # Format month labels for display (e.g., 'Jan', 'Fev', etc.)
            month_labels = []
            mrr_values = []

            for month in filtered_months:
                try:
                    month_dt = pd.to_datetime(month + '-01')
                    month_labels.append(month_dt.strftime('%b/%y'))
                    mrr_values.append(cumulative_mrr[month])
                except:
                    month_labels.append(month)
                    mrr_values.append(cumulative_mrr[month])

            return month_labels, mrr_values
        except Exception as e:
            logger.error(f"Error getting monthly MRR data: {e}")
            return [], []

    def get_cumulative_mrr_by_creation_date(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the cumulative MRR data based on creation date for finalized implementations

        Returns:
            tuple: (labels, values) for the cumulative MRR chart
        """
        try:
            df = self.data_loader.get_data()

            if 'Status_Implantacao' not in df.columns or 'Valor Mensalidade' not in df.columns:
                logger.warning("Required columns for cumulative MRR calculation not found")
                return [], []

            # Check if we have creation date information - use Data_Criacao_Implantacao
            if 'Data_Criacao_Implantacao' not in df.columns:
                logger.warning("Column 'Data_Criacao_Implantacao' required for cumulative MRR calculation not found")
                # Try to use Data_criacao_Oportunidade as fallback
                if 'Data_criacao_Oportunidade' in df.columns:
                    logger.info("Using Data_criacao_Oportunidade as fallback for Data_Criacao_Implantacao")
                    df['Data_Criacao_Implantacao'] = df['Data_criacao_Oportunidade']
                else:
                    return [], []

            # Filter finalized implementations
            finalized_df = df[df['Status_Implantacao'] == 'Finalizado'].copy()

            # If we have no finalized implementations, return empty data with error message
            if finalized_df.empty:
                logger.warning("No finalized implementations found for cumulative MRR calculation")
                return [], []

            # Convert currency values to float
            finalized_df['Valor_Float'] = finalized_df['Valor Mensalidade'].apply(safe_convert_to_float)

            # Make sure the date column is datetime
            if not pd.api.types.is_datetime64_any_dtype(finalized_df['Data_Criacao_Implantacao']):
                finalized_df['Data_Criacao_Implantacao'] = pd.to_datetime(finalized_df['Data_Criacao_Implantacao'], errors='coerce')

            # Group by month of creation date and calculate MRR
            finalized_df['Month'] = finalized_df['Data_Criacao_Implantacao'].dt.strftime('%Y-%m')
            monthly_mrr = finalized_df.groupby('Month')['Valor_Float'].sum()

            # Sort by month
            monthly_mrr = monthly_mrr.sort_index()

            # Calculate cumulative MRR
            cumulative_mrr = monthly_mrr.cumsum()

            # If we have less than 12 months of data, fill in the gaps
            if len(cumulative_mrr) < 12:
                # Get the current date for reference
                _ = pd.Timestamp.now()

                # Generate a range of 12 months ending with the current month
                months = pd.date_range(end=pd.Timestamp.now(), periods=12, freq='M')
                months = [month.strftime('%Y-%m') for month in months]

                # Create a new series with all months
                full_mrr = pd.Series(index=months, dtype=float)

                # Fill with the cumulative MRR where available
                for month, value in cumulative_mrr.items():
                    if month in full_mrr.index:
                        full_mrr[month] = value

                # Forward fill missing values
                full_mrr = full_mrr.ffill()

                # If we still have NaN values at the beginning, fill with 0
                full_mrr = full_mrr.fillna(0)

                # Update cumulative_mrr
                cumulative_mrr = full_mrr

            # Filter for 2024 and later
            filtered_months = [m for m in cumulative_mrr.index if m.startswith('2024') or m > '2024']

            # If no data from 2024, use all data
            if not filtered_months:
                filtered_months = cumulative_mrr.index

            # Format month labels for display (e.g., 'Jan/22', 'Fev/22', etc.)
            month_labels = []
            mrr_values = []

            for month in filtered_months:
                try:
                    month_dt = pd.to_datetime(month + '-01')
                    month_labels.append(month_dt.strftime('%b/%y'))
                    mrr_values.append(cumulative_mrr[month])
                except:
                    month_labels.append(month)
                    mrr_values.append(cumulative_mrr[month])

            return month_labels, mrr_values
        except Exception as e:
            logger.error(f"Error getting cumulative MRR data by creation date: {e}")
            return [], []

    def get_implementation_data(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get implementation data for charts and metrics

        Returns:
            dict: Implementation data for charts and metrics
        """
        try:
            df = self.data_loader.get_data()
            opp_id_col = self.data_loader.get_opportunity_id_column()

            # Initialize result dictionary
            result = {
                'phases': {},
                'status': {},
                'responsible': {},
                'timeline': {},
                'monthly_implementations': {},
                'avg_implementation_time': 0,
                'completion_rate': 0,
                'university_distribution': {},
                'product_distribution': {},
                'implementation_by_month': {},
                'implementation_by_week': {},
                'implementation_time_by_product': {},
                'implementation_time_by_responsible': {},
                'implementation_time_by_university': {},
                'implementation_time_distribution': {},
                'recent_implementations': [],
                'upcoming_finalizations': []
            }

            # Check if we have implementation data
            if 'Fase_Implantacao' not in df.columns or not opp_id_col:
                logger.warning("Required columns for implementation data not found")
                return result

            # Get implementations (opportunities with implementation phase)
            impl_df = df[df['Fase_Implantacao'].notna()].copy()

            if impl_df.empty:
                return result

            # Calculate implementation phases distribution
            if 'Fase_Implantacao' in impl_df.columns:
                phases_raw = impl_df['Fase_Implantacao'].value_counts().to_dict()
                result['phases'] = {k: str(v) for k, v in phases_raw.items()}

                # If we have no phases data, log a warning
                if not result['phases']:
                    logger.warning("No implementation phases data found")

            # Calculate implementation status distribution
            if 'Status_Implantacao' in impl_df.columns:
                status_raw = impl_df['Status_Implantacao'].value_counts().to_dict()
                result['status'] = {k: str(v) for k, v in status_raw.items()}

                # If we have no status data, log a warning
                if not result['status']:
                    logger.warning("No implementation status data found")

            # Calculate implementation by responsible
            if 'ResponsableOnboarding' in impl_df.columns:
                responsible_raw = impl_df['ResponsableOnboarding'].value_counts().to_dict()
                result['responsible'] = {k: str(v) for k, v in responsible_raw.items()}

            # Calculate implementation by university
            if 'Universidade' in impl_df.columns:
                university_raw = impl_df['Universidade'].value_counts().to_dict()
                result['university_distribution'] = {k: str(v) for k, v in university_raw.items()}

            # Calculate implementation by product
            if 'Produto' in impl_df.columns:
                product_raw = impl_df['Produto'].value_counts().to_dict()
                result['product_distribution'] = {k: str(v) for k, v in product_raw.items()}

            # Calculate monthly implementations
            # Check if we have Data_Criacao_Implantacao column
            if 'Data_Criacao_Implantacao' not in impl_df.columns:
                # Try to use Data_criacao_Oportunidade as fallback
                if 'Data_criacao_Oportunidade' in impl_df.columns:
                    logger.info("Using Data_criacao_Oportunidade as fallback for Data_Criacao_Implantacao")
                    impl_df['Data_Criacao_Implantacao'] = impl_df['Data_criacao_Oportunidade']
                # If not, try to use Data_criacao_lead as fallback
                elif 'Data_criacao_lead' in impl_df.columns:
                    logger.info("Using Data_criacao_lead as fallback for Data_Criacao_Implantacao")
                    impl_df['Data_Criacao_Implantacao'] = impl_df['Data_criacao_lead']

            if 'Data_Criacao_Implantacao' in impl_df.columns:
                # Make sure the date column is datetime
                if not pd.api.types.is_datetime64_any_dtype(impl_df['Data_Criacao_Implantacao']):
                    impl_df['Data_Criacao_Implantacao'] = pd.to_datetime(impl_df['Data_Criacao_Implantacao'], errors='coerce')

                # Group by month
                impl_df['Month'] = impl_df['Data_Criacao_Implantacao'].dt.strftime('%Y-%m')
                monthly_impl = impl_df.groupby('Month')[opp_id_col].nunique().to_dict()

                # Convert to strings
                result['monthly_implementations'] = {k: str(v) for k, v in monthly_impl.items()}

                # Format for chart display
                months = []
                counts = []

                # Filter for 2024 and later
                filtered_months = [m for m in sorted(monthly_impl.keys()) if m.startswith('2024') or m > '2024']

                # If no data from 2024, use all data
                if not filtered_months:
                    filtered_months = sorted(monthly_impl.keys())

                # Sort by month
                for month in filtered_months:
                    try:
                        month_dt = pd.to_datetime(month + '-01')
                        months.append(month_dt.strftime('%b/%Y'))
                        counts.append(monthly_impl[month])
                    except:
                        continue

                # If we have no timeline data, log a warning
                if not months or not counts:
                    logger.warning("No implementation timeline data found")

                result['timeline'] = {
                    'months': months,
                    'counts': counts
                }

                # Group by month for the last 12 months
                current_date = pd.Timestamp.now()
                start_date = current_date - pd.DateOffset(months=11)

                # Create a range of months
                month_range = pd.date_range(start=start_date, end=current_date, freq='M')
                month_labels = [d.strftime('%Y-%m') for d in month_range]

                # Filter implementations for the last 12 months
                recent_impl_df = impl_df[impl_df['Data_Criacao_Implantacao'] >= start_date]

                # Group by month
                month_counts = recent_impl_df.groupby('Month')[opp_id_col].nunique().reindex(month_labels, fill_value=0).to_dict()

                # Format for chart display
                month_labels_formatted = [pd.to_datetime(m + '-01').strftime('%b/%Y') for m in month_labels]
                month_values = [month_counts.get(m, 0) for m in month_labels]

                # If we have no implementation_by_month data, log a warning
                if not month_labels_formatted or not any(month_values):
                    logger.warning("No implementation_by_month data found")

                result['implementation_by_month'] = {
                    'labels': month_labels_formatted,
                    'values': month_values
                }

                # Group by week for the last 12 weeks
                impl_df['Week'] = impl_df['Data_Criacao_Implantacao'].dt.strftime('%Y-%U')

                # Get the last 12 weeks
                weeks = []
                current_date = pd.Timestamp.now()

                for i in range(12):
                    week_date = current_date - pd.DateOffset(weeks=i)
                    weeks.insert(0, week_date.strftime('%Y-%U'))

                # Group by week
                week_counts = impl_df.groupby('Week')[opp_id_col].nunique().reindex(weeks, fill_value=0).to_dict()

                # Format for chart display
                week_labels = [f"Semana {w.split('-')[1]}" for w in weeks]
                week_values = [week_counts.get(w, 0) for w in weeks]

                # If we have no implementation_by_week data, log a warning
                if not week_labels or not any(week_values):
                    logger.warning("No implementation_by_week data found")

                result['implementation_by_week'] = {
                    'labels': week_labels,
                    'values': week_values
                }

            # Calculate average implementation time
            if 'Data_Criacao_Implantacao' in impl_df.columns:
                # Check if we have Data_Finalizacao column
                if 'Data_Finalizacao' not in impl_df.columns:
                    # Use Data_Criacao_Implantacao as a fallback
                    logger.info("Using Data_Criacao_Implantacao as fallback for Data_Finalizacao")
                    impl_df['Data_Finalizacao'] = impl_df['Data_Criacao_Implantacao']

                # Filter finalized implementations
                finalized_df = impl_df[impl_df['Status_Implantacao'] == 'Finalizado'].copy()

                if not finalized_df.empty:
                    # Make sure date columns are datetime
                    for col in ['Data_Criacao_Implantacao', 'Data_Finalizacao']:
                        if not pd.api.types.is_datetime64_any_dtype(finalized_df[col]):
                            finalized_df[col] = pd.to_datetime(finalized_df[col], errors='coerce')

                    # Calculate implementation time in days
                    finalized_df['implementation_time'] = (finalized_df['Data_Finalizacao'] - finalized_df['Data_Criacao_Implantacao']).dt.days

                    # Calculate average implementation time
                    avg_time = finalized_df['implementation_time'].mean()
                    result['avg_implementation_time'] = round(avg_time) if not pd.isna(avg_time) else 0

                    # If avg_implementation_time is 0, log a warning
                    if result['avg_implementation_time'] == 0:
                        logger.warning("No average implementation time data found")

                    # Calculate implementation time by product
                    if 'Produto' in finalized_df.columns:
                        time_by_product = finalized_df.groupby('Produto')['implementation_time'].mean().to_dict()
                        result['implementation_time_by_product'] = {k: str(round(v)) for k, v in time_by_product.items()}

                    # Calculate implementation time by responsible
                    if 'ResponsableOnboarding' in finalized_df.columns:
                        time_by_responsible = finalized_df.groupby('ResponsableOnboarding')['implementation_time'].mean().to_dict()
                        result['implementation_time_by_responsible'] = {k: str(round(v)) for k, v in time_by_responsible.items()}

                    # Calculate implementation time by university
                    if 'Universidade' in finalized_df.columns:
                        time_by_university = finalized_df.groupby('Universidade')['implementation_time'].mean().to_dict()
                        result['implementation_time_by_university'] = {k: str(round(v)) for k, v in time_by_university.items()}

                    # Calculate implementation time distribution
                    time_bins = [0, 15, 30, 60, 90, float('inf')]
                    time_labels = ['0-15 dias', '16-30 dias', '31-60 dias', '61-90 dias', '90+ dias']

                    time_distribution = pd.cut(finalized_df['implementation_time'], bins=time_bins, labels=time_labels).value_counts().to_dict()
                    result['implementation_time_distribution'] = {k: str(v) for k, v in time_distribution.items()}

            # Calculate completion rate
            if 'Status_Implantacao' in impl_df.columns:
                total_impl = len(impl_df[opp_id_col].unique())
                finalized_impl = len(impl_df[impl_df['Status_Implantacao'] == 'Finalizado'][opp_id_col].unique())

                if total_impl > 0:
                    result['completion_rate'] = round((finalized_impl / total_impl) * 100, 1)
                else:
                    # If we have no completion rate data, log a warning
                    logger.warning("No completion rate data found")

            # Get recent implementations (last 5)
            if 'Data_Criacao_Implantacao' in impl_df.columns:
                # Sort by creation date
                recent_df = impl_df.sort_values('Data_Criacao_Implantacao', ascending=False).head(5)

                # Format dates
                recent_df['Data_Criacao_Implantacao'] = recent_df['Data_Criacao_Implantacao'].apply(
                    lambda x: x.strftime('%d/%m/%Y') if pd.notna(x) else 'N/A'
                )

                # Select relevant columns
                select_cols = [opp_id_col, 'Nome do Lead', 'Produto', 'Status_Implantacao', 'ResponsableOnboarding', 'Data_Criacao_Implantacao']
                select_cols = [col for col in select_cols if col in recent_df.columns]

                if select_cols:
                    result['recent_implementations'] = recent_df[select_cols].to_dict('records')

                # If we have no recent implementations data, log a warning
                if not result['recent_implementations']:
                    logger.warning("No recent implementations data found")

            # Get upcoming finalizations (implementations close to completion)
            if 'Status_Implantacao' in impl_df.columns and 'DataPrevistaDeFinalização' in impl_df.columns:
                # Filter active implementations with a planned finalization date
                active_df = impl_df[(impl_df['Status_Implantacao'] != 'Finalizado') &
                                   (impl_df['Status_Implantacao'] != 'Cancelado') &
                                   (impl_df['DataPrevistaDeFinalização'].notna())].copy()

                if not active_df.empty:
                    # Make sure date column is datetime
                    if not pd.api.types.is_datetime64_any_dtype(active_df['DataPrevistaDeFinalização']):
                        active_df['DataPrevistaDeFinalização'] = pd.to_datetime(active_df['DataPrevistaDeFinalização'], errors='coerce')

                    # Sort by planned finalization date
                    upcoming_df = active_df.sort_values('DataPrevistaDeFinalização').head(5)

                    # Format dates
                    upcoming_df['DataPrevistaDeFinalização'] = upcoming_df['DataPrevistaDeFinalização'].apply(
                        lambda x: x.strftime('%d/%m/%Y') if pd.notna(x) else 'N/A'
                    )

                    # Select relevant columns
                    select_cols = [opp_id_col, 'Nome do Lead', 'Produto', 'Status_Implantacao', 'ResponsableOnboarding', 'DataPrevistaDeFinalização']
                    select_cols = [col for col in select_cols if col in upcoming_df.columns]

                    if select_cols:
                        result['upcoming_finalizations'] = upcoming_df[select_cols].to_dict('records')

                # If we have no upcoming finalizations data, log a warning
                if not result['upcoming_finalizations']:
                    logger.warning("No upcoming finalizations data found")

            return result
        except Exception as e:
            logger.error(f"Error getting implementation data: {e}")
            return {
                'phases': {},
                'status': {},
                'responsible': {},
                'timeline': {},
                'monthly_implementations': {},
                'avg_implementation_time': 0,
                'completion_rate': 0,
                'university_distribution': {},
                'product_distribution': {},
                'implementation_by_month': {},
                'implementation_by_week': {},
                'implementation_time_by_product': {},
                'implementation_time_by_responsible': {},
                'implementation_time_by_university': {},
                'implementation_time_distribution': {},
                'recent_implementations': [],
                'upcoming_finalizations': []
            }

    def get_students_by_university(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the number of students by university

        Returns:
            dict: The number of students by university
        """
        try:
            df = self.data_loader.get_data()

            if 'Universidade' not in df.columns:
                logger.warning("Column 'Universidade' not found for students by university calculation")
                return {}

            # Count students by university
            students_by_university = df['Universidade'].value_counts().to_dict()

            # Convert to strings
            students_by_university = {k: str(v) for k, v in students_by_university.items()}

            return students_by_university
        except Exception as e:
            logger.error(f"Error getting students by university: {e}")
            return {}

    def get_implementation_historical_phases(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get historical data for implementation phases

        Returns:
            dict: Historical data for implementation phases
        """
        try:
            df = self.data_loader.get_data()

            if 'Fase_Implantacao' not in df.columns:
                logger.warning("Column 'Fase_Implantacao' required for implementation historical phases not found")
                return {}

            # Check if we have Data_Criacao_Implantacao column
            if 'Data_Criacao_Implantacao' not in df.columns:
                logger.warning("Column 'Data_Criacao_Implantacao' required for implementation historical phases not found")
                # Try to use Data_criacao_Oportunidade as fallback
                if 'Data_criacao_Oportunidade' in df.columns:
                    logger.info("Using Data_criacao_Oportunidade as fallback for Data_Criacao_Implantacao")
                    df['Data_Criacao_Implantacao'] = df['Data_criacao_Oportunidade']
                # If not, try to use Data_criacao_lead as fallback
                elif 'Data_criacao_lead' in df.columns:
                    logger.info("Using Data_criacao_lead as fallback for Data_Criacao_Implantacao")
                    df['Data_Criacao_Implantacao'] = df['Data_criacao_lead']
                else:
                    return {}

            # Make sure the date column is datetime
            if not pd.api.types.is_datetime64_any_dtype(df['Data_Criacao_Implantacao']):
                df['Data_Criacao_Implantacao'] = pd.to_datetime(df['Data_Criacao_Implantacao'], errors='coerce')

            # Filter implementations
            impl_df = df[df['Fase_Implantacao'].notna()].copy()

            if impl_df.empty:
                return {}

            # Group by phase and count
            phases = impl_df['Fase_Implantacao'].value_counts().to_dict()

            # Convert to strings
            phases = {k: str(v) for k, v in phases.items()}

            return phases
        except Exception as e:
            logger.error(f"Error getting implementation historical phases: {e}")
            return {}

    def prepare_chart_data(self: List[Any]) -> Dict[str, Any]:
        """
        Prepare data for charts

        Returns:
            dict: The chart data
        """
        try:
            # Get data for charts
            funnel_stages = self.business_rules.calculate_funnel_distribution()
            implementation_phases = self.business_rules.calculate_implementation_phases()
            status_counts, status_positions = self.business_rules.calculate_implementation_status()
            product_conversion = self.get_conversion_by_product()
            state_conversion = self.get_conversion_by_state()
            conversion_dropoff = self.get_conversion_dropoff()
            time_by_stage = self.get_time_by_stage()
            mrr_by_responsible = self.get_mrr_by_responsible()
            mrr_by_product = self.get_mrr_by_product()
            mrr_by_university, mrr_percentage_by_university, _ = self.get_mrr_by_university()
            monthly_opps = self.get_monthly_opportunities()
            opp_types = self.get_opportunity_types()

            # Get monthly MRR data for the chart
            mrr_labels, mrr_values = self.get_monthly_mrr_data()

            # Get cumulative MRR data by creation date
            mrr_creation_labels, mrr_creation_values = self.get_cumulative_mrr_by_creation_date()

            # Get students by university
            students_by_university = self.get_students_by_university()

            # Add error messages for missing data
            errors = {}

            # Check if MRR data is missing
            if not mrr_labels or not mrr_values:
                errors['mrr_monthly'] = {
                    'message': 'Não foi possível carregar os dados para o gráfico de MRR Mensal.',
                    'required_columns': ['Status_Implantacao', 'Valor Mensalidade', 'Data_Criacao_Implantacao']
                }

            # Check if MRR by creation date data is missing
            if not mrr_creation_labels or not mrr_creation_values:
                errors['mrr_creation'] = {
                    'message': 'Não foi possível carregar os dados para o gráfico de MRR Cumulativo.',
                    'required_columns': ['Status_Implantacao', 'Valor Mensalidade', 'Data_Criacao_Implantacao']
                }

            # Check if funnel stages data is missing
            if not funnel_stages:
                errors['funnel_stages'] = {
                    'message': 'Não foi possível carregar os dados para o gráfico de Funil de Conversão.',
                    'required_columns': ['Etapa do funil Comercial']
                }

            # Check if implementation phases data is missing
            if not implementation_phases:
                errors['implementation_phases'] = {
                    'message': 'Não foi possível carregar os dados para o gráfico de Fases de Implantação.',
                    'required_columns': ['Fase_Implantacao']
                }

            # Check if opportunity types data is missing
            if not opp_types:
                errors['opp_types'] = {
                    'message': 'Não foi possível carregar os dados para o gráfico de Tipos de Oportunidades.',
                    'required_columns': ['Produto']
                }

            # Prepare chart data
            chart_data = {
                'funnel_stages': funnel_stages,
                'implementation_phases': implementation_phases,
                'status_counts': status_counts,
                'status_positions': status_positions,
                'product_conversion': product_conversion,
                'state_conversion': state_conversion,
                'conversion_dropoff': conversion_dropoff,
                'time_by_stage': time_by_stage,
                'mrr_by_responsible': mrr_by_responsible,
                'mrr_by_product': mrr_by_product,
                'mrr_by_university': mrr_by_university,
                'mrr_percentage_by_university': mrr_percentage_by_university,
                'monthly_opps': monthly_opps,
                'opp_types': opp_types,
                'mrr_monthly_labels': mrr_labels,
                'mrr_monthly_values': mrr_values,
                'mrr_creation_labels': mrr_creation_labels,
                'mrr_creation_values': mrr_creation_values,
                'students_by_university': students_by_university,
                'errors': errors
            }

            return chart_data
        except Exception as e:
            logger.error(f"Error preparing chart data: {e}")
            return {}
