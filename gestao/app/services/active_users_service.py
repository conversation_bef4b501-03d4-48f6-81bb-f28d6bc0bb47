"""
Amigo DataHub - Active Users Service
Advanced analysis for active users and invoice production
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class ActiveUsersService:
    """Service for active users analysis"""

    def __init__(self, data_loader):
        """Initialize the active users service"""
        self.data_loader = data_loader
        self.df = None
        self._load_data()

    def _load_data(self) -> None:
        """Load and prepare data for analysis"""
        try:
            self.df = self.data_loader.get_data()
            if self.df is None or self.df.empty:
                raise ValueError("No data available")

            # Ensure required columns exist
            required_columns = [
                'Gerou_Nota_Fiscal', 'Data_primeira_nota', 'Data_ultima_nota',
                'Meses_atividade', 'Total_notas', 'Media_nota_mes',
                'Ticket_medio_notas', 'Faturamento_total', 'Turma', 'Universidade'
            ]

            missing_columns = [col for col in required_columns if col not in self.df.columns]
            if missing_columns:
                logger.warning(f"Missing columns: {missing_columns}")

            # Convert date columns
            date_columns = ['Data_primeira_nota', 'Data_ultima_nota']
            for col in date_columns:
                if col in self.df.columns:
                    self.df[col] = pd.to_datetime(self.df[col], errors='coerce')

            # Convert numeric columns
            numeric_columns = [
                'Meses_atividade', 'Total_notas', 'Media_nota_mes',
                'Ticket_medio_notas', 'Faturamento_total'
            ]
            for col in numeric_columns:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col], errors='coerce')

            logger.info(f"Active users data loaded: {len(self.df)} records")

        except Exception as e:
            logger.error(f"Error loading active users data: {e}")
            raise

    def calculate_active_users_kpis(self) -> Dict[str, Any]:
        """Calculate key performance indicators for active users"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Filter records with invoice generation
            invoice_df = self.df[self.df['Gerou_Nota_Fiscal'] == 'Sim'].copy()
            # Produtores inativos são todos os que NÃO geraram nota fiscal
            inactive_df = self.df[self.df['Gerou_Nota_Fiscal'] != 'Sim'].copy()

            if invoice_df.empty:
                return {"error": "No invoice data available"}

            # Calculate KPIs
            total_producers = len(invoice_df)
            inactive_producers = len(inactive_df)
            total_invoices = invoice_df['Total_notas'].sum()
            total_revenue = invoice_df['Faturamento_total'].sum()
            avg_ticket = invoice_df['Ticket_medio_notas'].mean()
            avg_monthly_production = invoice_df['Media_nota_mes'].mean()

            # Debug logs
            logger.info(f"DEBUG KPIs: total_producers={total_producers}, inactive_producers={inactive_producers}, total_invoices={total_invoices}")
            # Calcular média de notas por produtor corretamente
            if total_producers > 0 and not invoice_df['Total_notas'].isna().all():
                avg_notes_per_producer = float(invoice_df['Total_notas'].sum()) / total_producers
            else:
                avg_notes_per_producer = 0

            # Ticket distribution analysis
            ticket_ranges = [
                ('R$ 0 - R$ 1.000', int((invoice_df['Ticket_medio_notas'] <= 1000).sum())),
                ('R$ 1.001 - R$ 3.000', int(((invoice_df['Ticket_medio_notas'] > 1000) & (invoice_df['Ticket_medio_notas'] <= 3000)).sum())),
                ('R$ 3.001 - R$ 5.000', int(((invoice_df['Ticket_medio_notas'] > 3000) & (invoice_df['Ticket_medio_notas'] <= 5000)).sum())),
                ('R$ 5.001 - R$ 10.000', int(((invoice_df['Ticket_medio_notas'] > 5000) & (invoice_df['Ticket_medio_notas'] <= 10000)).sum())),
                ('Acima de R$ 10.000', int((invoice_df['Ticket_medio_notas'] > 10000).sum()))
            ]

            # Revenue distribution analysis
            revenue_ranges = [
                ('R$ 0 - R$ 10.000', int((invoice_df['Faturamento_total'] <= 10000).sum())),
                ('R$ 10.001 - R$ 50.000', int(((invoice_df['Faturamento_total'] > 10000) & (invoice_df['Faturamento_total'] <= 50000)).sum())),
                ('R$ 50.001 - R$ 100.000', int(((invoice_df['Faturamento_total'] > 50000) & (invoice_df['Faturamento_total'] <= 100000)).sum())),
                ('R$ 100.001 - R$ 500.000', int(((invoice_df['Faturamento_total'] > 100000) & (invoice_df['Faturamento_total'] <= 500000)).sum())),
                ('Acima de R$ 500.000', int((invoice_df['Faturamento_total'] > 500000).sum()))
            ]

            # Calculate growth metrics (comparing last 3 months vs previous 3 months)
            current_date = datetime.now()
            three_months_ago = current_date - timedelta(days=90)
            six_months_ago = current_date - timedelta(days=180)

            recent_producers = invoice_df[
                invoice_df['Data_ultima_nota'] >= three_months_ago
            ]

            previous_producers = invoice_df[
                (invoice_df['Data_ultima_nota'] >= six_months_ago) &
                (invoice_df['Data_ultima_nota'] < three_months_ago)
            ]

            # Growth calculations
            revenue_growth = 0
            if len(previous_producers) > 0:
                recent_revenue = recent_producers['Faturamento_total'].sum()
                previous_revenue = previous_producers['Faturamento_total'].sum()
                if previous_revenue > 0:
                    revenue_growth = ((recent_revenue - previous_revenue) / previous_revenue) * 100

            producer_growth = 0
            if len(previous_producers) > 0:
                producer_growth = ((len(recent_producers) - len(previous_producers)) / len(previous_producers)) * 100

            ticket_growth = 0
            if len(previous_producers) > 0:
                recent_avg_ticket = recent_producers['Ticket_medio_notas'].mean()
                previous_avg_ticket = previous_producers['Ticket_medio_notas'].mean()
                if previous_avg_ticket > 0:
                    ticket_growth = ((recent_avg_ticket - previous_avg_ticket) / previous_avg_ticket) * 100

            # Activity distribution
            activity_distribution = {
                'Ativos (últimos 30 dias)': len(invoice_df[
                    invoice_df['Data_ultima_nota'] >= (current_date - timedelta(days=30))
                ]),
                'Ativos (últimos 90 dias)': len(recent_producers),
                'Inativos (mais de 90 dias)': len(invoice_df[
                    invoice_df['Data_ultima_nota'] < three_months_ago
                ])
            }

            # Calculate high activity users (3+ months of activity)
            high_activity_users = len(invoice_df[invoice_df['Meses_atividade'] >= 3])
            high_activity_rate = (high_activity_users / total_producers * 100) if total_producers > 0 else 0

            return {
                'total_producers': int(total_producers),
                'inactive_producers': int(inactive_producers),
                'high_activity_users': int(high_activity_users),
                'high_activity_rate': float(high_activity_rate),
                'total_invoices': int(total_invoices) if not pd.isna(total_invoices) else 0,
                'total_revenue': float(total_revenue) if not pd.isna(total_revenue) else 0,
                'avg_ticket': float(avg_ticket) if not pd.isna(avg_ticket) else 0,
                'avg_notes_per_producer': float(avg_notes_per_producer) if not pd.isna(avg_notes_per_producer) else 0,
                'avg_monthly_production': float(avg_monthly_production) if not pd.isna(avg_monthly_production) else 0,
                'revenue_growth': float(revenue_growth),
                'producer_growth': float(producer_growth),
                'ticket_growth': float(ticket_growth),
                'activity_distribution': activity_distribution,
                'ticket_distribution': [{'range': r[0], 'count': r[1]} for r in ticket_ranges],
                'revenue_distribution': [{'range': r[0], 'count': r[1]} for r in revenue_ranges],
                'deltas': {
                    'producer_delta': round(producer_growth, 1),
                    'revenue_delta': round(revenue_growth, 1),
                    'ticket_delta': round(ticket_growth, 1),
                    'high_activity_delta': 3.7  # Placeholder for growth
                },
                'success': True
            }

        except Exception as e:
            logger.error(f"Error calculating active users KPIs: {e}")
            return {"error": str(e)}

    def analyze_production_by_turma(self) -> Dict[str, Any]:
        """Analyze active users production by turma (class)"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Filter records with invoice generation
            invoice_df = self.df[self.df['Gerou_Nota_Fiscal'] == 'Sim'].copy()

            if invoice_df.empty:
                return {"error": "No invoice data available"}

            # Group by turma
            turma_stats = invoice_df.groupby('Turma').agg({
                'Total_notas': 'sum',
                'Faturamento_total': 'sum',
                'Ticket_medio_notas': 'mean',
                'Media_nota_mes': 'mean',
                'Meses_atividade': 'mean',
                'Gerou_Nota_Fiscal': 'count'  # Count of producers
            }).round(2)

            turma_stats.columns = [
                'total_notas', 'faturamento_total', 'ticket_medio',
                'media_mensal', 'meses_atividade_media', 'num_produtores'
            ]

            # Calculate productivity score
            turma_stats['produtividade_score'] = (
                turma_stats['faturamento_total'] * 0.4 +
                turma_stats['total_notas'] * 0.3 +
                turma_stats['ticket_medio'] * 0.2 +
                turma_stats['media_mensal'] * 0.1
            ).round(2)

            # Sort by productivity score
            turma_stats = turma_stats.sort_values('produtividade_score', ascending=False)

            # Convert to list of dictionaries
            turma_list = []
            for turma, row in turma_stats.iterrows():
                turma_list.append({
                    'turma': turma,
                    'total_notas': int(row['total_notas']) if not pd.isna(row['total_notas']) else 0,
                    'faturamento_total': float(row['faturamento_total']) if not pd.isna(row['faturamento_total']) else 0,
                    'ticket_medio': float(row['ticket_medio']) if not pd.isna(row['ticket_medio']) else 0,
                    'media_mensal': float(row['media_mensal']) if not pd.isna(row['media_mensal']) else 0,
                    'meses_atividade_media': float(row['meses_atividade_media']) if not pd.isna(row['meses_atividade_media']) else 0,
                    'num_produtores': int(row['num_produtores']),
                    'produtividade_score': float(row['produtividade_score'])
                })

            # Top and bottom performers
            top_turmas = turma_list[:10]
            bottom_turmas = turma_list[-5:] if len(turma_list) > 5 else []

            # Summary statistics
            summary = {
                'total_turmas': len(turma_list),
                'total_faturamento': sum([t['faturamento_total'] for t in turma_list]),
                'media_produtores_por_turma': np.mean([t['num_produtores'] for t in turma_list]),
                'turma_mais_produtiva': turma_list[0]['turma'] if turma_list else None
            }

            return {
                'turma_ranking': turma_list,
                'top_turmas': top_turmas,
                'bottom_turmas': bottom_turmas,
                'summary': summary,
                'success': True
            }

        except Exception as e:
            logger.error(f"Error analyzing production by turma: {e}")
            return {"error": str(e)}

    def analyze_production_by_university(self) -> Dict[str, Any]:
        """Analyze active users production by university"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Filter records with invoice generation
            invoice_df = self.df[self.df['Gerou_Nota_Fiscal'] == 'Sim'].copy()

            if invoice_df.empty:
                return {"error": "No invoice data available"}

            # Group by university
            university_stats = invoice_df.groupby('Universidade').agg({
                'Total_notas': 'sum',
                'Faturamento_total': 'sum',
                'Ticket_medio_notas': 'mean',
                'Media_nota_mes': 'mean',
                'Turma': 'nunique',  # Number of unique turmas
                'Gerou_Nota_Fiscal': 'count'  # Count of producers
            }).round(2)

            university_stats.columns = [
                'total_notas', 'faturamento_total', 'ticket_medio',
                'media_mensal', 'num_turmas', 'num_produtores'
            ]

            # Calculate efficiency metrics
            university_stats['faturamento_por_produtor'] = (
                university_stats['faturamento_total'] / university_stats['num_produtores']
            ).round(2)

            university_stats['notas_por_produtor'] = (
                university_stats['total_notas'] / university_stats['num_produtores']
            ).round(2)

            # Sort by total revenue
            university_stats = university_stats.sort_values('faturamento_total', ascending=False)

            # Convert to list of dictionaries
            university_list = []
            for university, row in university_stats.iterrows():
                university_list.append({
                    'universidade': university,
                    'total_notas': int(row['total_notas']) if not pd.isna(row['total_notas']) else 0,
                    'faturamento_total': float(row['faturamento_total']) if not pd.isna(row['faturamento_total']) else 0,
                    'ticket_medio': float(row['ticket_medio']) if not pd.isna(row['ticket_medio']) else 0,
                    'media_mensal': float(row['media_mensal']) if not pd.isna(row['media_mensal']) else 0,
                    'num_turmas': int(row['num_turmas']),
                    'num_produtores': int(row['num_produtores']),
                    'faturamento_por_produtor': float(row['faturamento_por_produtor']) if not pd.isna(row['faturamento_por_produtor']) else 0,
                    'notas_por_produtor': float(row['notas_por_produtor']) if not pd.isna(row['notas_por_produtor']) else 0
                })

            # Top performers
            top_universities = university_list[:10]

            # Summary statistics
            summary = {
                'total_universidades': len(university_list),
                'total_faturamento': sum([u['faturamento_total'] for u in university_list]),
                'media_produtores_por_universidade': np.mean([u['num_produtores'] for u in university_list]),
                'universidade_mais_produtiva': university_list[0]['universidade'] if university_list else None
            }

            return {
                'university_ranking': university_list,
                'top_universities': top_universities,
                'summary': summary,
                'success': True
            }

        except Exception as e:
            logger.error(f"Error analyzing production by university: {e}")
            return {"error": str(e)}

    def get_top_producers(self) -> Dict[str, Any]:
        """Get ranking of top active users producers"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Filter records with invoice generation
            invoice_df = self.df[self.df['Gerou_Nota_Fiscal'] == 'Sim'].copy()

            if invoice_df.empty:
                return {"error": "No invoice data available"}

            # Create producer ranking
            producers = []
            for idx, row in invoice_df.iterrows():
                producer = {
                    'id': f"PROD_{idx}",
                    'nome': row.get('Nome do Lead', f'Produtor {idx}'),
                    'turma': row.get('Turma', 'N/A'),
                    'universidade': row.get('Universidade', 'N/A'),
                    'total_notas': int(row['Total_notas']) if not pd.isna(row['Total_notas']) else 0,
                    'faturamento_total': float(row['Faturamento_total']) if not pd.isna(row['Faturamento_total']) else 0,
                    'ticket_medio': float(row['Ticket_medio_notas']) if not pd.isna(row['Ticket_medio_notas']) else 0,
                    'media_mensal': float(row['Media_nota_mes']) if not pd.isna(row['Media_nota_mes']) else 0,
                    'meses_atividade': float(row['Meses_atividade']) if not pd.isna(row['Meses_atividade']) else 0,
                    'data_primeira_nota': row['Data_primeira_nota'].strftime('%Y-%m-%d') if pd.notna(row['Data_primeira_nota']) else None,
                    'data_ultima_nota': row['Data_ultima_nota'].strftime('%Y-%m-%d') if pd.notna(row['Data_ultima_nota']) else None
                }
                producers.append(producer)

            # Sort by total revenue
            producers.sort(key=lambda x: x['faturamento_total'], reverse=True)

            # Top 20 producers
            top_producers = producers[:20]

            # Calculate percentiles
            revenues = [p['faturamento_total'] for p in producers if p['faturamento_total'] > 0]
            if revenues:
                percentiles = {
                    'p25': np.percentile(revenues, 25),
                    'p50': np.percentile(revenues, 50),
                    'p75': np.percentile(revenues, 75),
                    'p90': np.percentile(revenues, 90)
                }
            else:
                percentiles = {'p25': 0, 'p50': 0, 'p75': 0, 'p90': 0}

            return {
                'top_producers': top_producers,
                'total_producers': len(producers),
                'percentiles': percentiles,
                'success': True
            }

        except Exception as e:
            logger.error(f"Error getting top producers: {e}")
            return {"error": str(e)}

    def analyze_temporal_trends(self) -> Dict[str, Any]:
        """Analyze temporal trends in active users production"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Filter records with invoice generation and valid dates
            invoice_df = self.df[
                (self.df['Gerou_Nota_Fiscal'] == 'Sim') &
                (pd.notna(self.df['Data_primeira_nota'])) &
                (pd.notna(self.df['Data_ultima_nota']))
            ].copy()

            if invoice_df.empty:
                return {"error": "No temporal data available"}

            # Monthly trends (based on last note date)
            invoice_df['year_month'] = invoice_df['Data_ultima_nota'].dt.to_period('M')

            monthly_trends = invoice_df.groupby('year_month').agg({
                'Total_notas': 'sum',
                'Faturamento_total': 'sum',
                'Gerou_Nota_Fiscal': 'count'  # Active producers
            }).reset_index()

            monthly_trends['year_month_str'] = monthly_trends['year_month'].astype(str)

            # Convert to list for JSON serialization
            monthly_data = []
            for _, row in monthly_trends.iterrows():
                monthly_data.append({
                    'month': row['year_month_str'],
                    'total_notas': int(row['Total_notas']) if not pd.isna(row['Total_notas']) else 0,
                    'faturamento': float(row['Faturamento_total']) if not pd.isna(row['Faturamento_total']) else 0,
                    'produtores_ativos': int(row['Gerou_Nota_Fiscal'])
                })

            # Activity duration analysis
            activity_duration = invoice_df['Meses_atividade'].dropna()
            duration_stats = {
                'media': float(activity_duration.mean()) if len(activity_duration) > 0 else 0,
                'mediana': float(activity_duration.median()) if len(activity_duration) > 0 else 0,
                'min': float(activity_duration.min()) if len(activity_duration) > 0 else 0,
                'max': float(activity_duration.max()) if len(activity_duration) > 0 else 0
            }

            # Seasonality analysis (by month)
            invoice_df['month'] = invoice_df['Data_ultima_nota'].dt.month
            seasonality = invoice_df.groupby('month').agg({
                'Faturamento_total': 'mean',
                'Total_notas': 'mean'
            }).round(2)

            seasonality_data = []
            for month, row in seasonality.iterrows():
                seasonality_data.append({
                    'month': month,
                    'avg_faturamento': float(row['Faturamento_total']) if not pd.isna(row['Faturamento_total']) else 0,
                    'avg_notas': float(row['Total_notas']) if not pd.isna(row['Total_notas']) else 0
                })

            return {
                'monthly_trends': monthly_data,
                'duration_stats': duration_stats,
                'seasonality': seasonality_data,
                'success': True
            }

        except Exception as e:
            logger.error(f"Error analyzing temporal trends: {e}")
            return {"error": str(e)}

    def calculate_quality_metrics(self) -> Dict[str, Any]:
        """Calculate quality and performance metrics"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Filter records with invoice generation
            invoice_df = self.df[self.df['Gerou_Nota_Fiscal'] == 'Sim'].copy()

            if invoice_df.empty:
                return {"error": "No invoice data available"}

            # Consistency metrics
            consistency_metrics = {
                'produtores_consistentes': len(invoice_df[invoice_df['Meses_atividade'] >= 3]),
                'produtores_esporadicos': len(invoice_df[invoice_df['Meses_atividade'] < 3]),
                'taxa_consistencia': 0
            }

            if len(invoice_df) > 0:
                consistency_metrics['taxa_consistencia'] = (
                    consistency_metrics['produtores_consistentes'] / len(invoice_df) * 100
                )

            # Performance distribution
            ticket_ranges = [
                {'range': '< R$ 1.000', 'count': len(invoice_df[invoice_df['Ticket_medio_notas'] < 1000])},
                {'range': 'R$ 1.000 - R$ 5.000', 'count': len(invoice_df[(invoice_df['Ticket_medio_notas'] >= 1000) & (invoice_df['Ticket_medio_notas'] < 5000)])},
                {'range': 'R$ 5.000 - R$ 10.000', 'count': len(invoice_df[(invoice_df['Ticket_medio_notas'] >= 5000) & (invoice_df['Ticket_medio_notas'] < 10000)])},
                {'range': '> R$ 10.000', 'count': len(invoice_df[invoice_df['Ticket_medio_notas'] >= 10000])}
            ]

            # Productivity metrics
            productivity_metrics = {
                'alta_produtividade': len(invoice_df[invoice_df['Media_nota_mes'] >= 2]),
                'media_produtividade': len(invoice_df[(invoice_df['Media_nota_mes'] >= 1) & (invoice_df['Media_nota_mes'] < 2)]),
                'baixa_produtividade': len(invoice_df[invoice_df['Media_nota_mes'] < 1])
            }

            return {
                'consistency_metrics': consistency_metrics,
                'ticket_distribution': ticket_ranges,
                'productivity_metrics': productivity_metrics,
                'success': True
            }

        except Exception as e:
            logger.error(f"Error calculating quality metrics: {e}")
            return {"error": str(e)}

    def get_producer_details(self, producer_id: str) -> Dict[str, Any]:
        """Get detailed information for a specific producer"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Extract index from producer_id (format: PROD_123)
            try:
                idx = int(producer_id.split('_')[1])
                if idx >= len(self.df):
                    return {"error": "Producer not found"}

                row = self.df.iloc[idx]
            except (ValueError, IndexError):
                return {"error": "Invalid producer ID"}

            # Build detailed producer information
            producer_details = {
                'id': producer_id,
                'nome': row.get('Nome do Lead', 'N/A'),
                'turma': row.get('Turma', 'N/A'),
                'universidade': row.get('Universidade', 'N/A'),
                'cidade': row.get('Cidade', 'N/A'),
                'estado': row.get('Estado', 'N/A'),
                'gerou_nota_fiscal': row.get('Gerou_Nota_Fiscal', 'Não'),
                'total_notas': int(row['Total_notas']) if not pd.isna(row['Total_notas']) else 0,
                'faturamento_total': float(row['Faturamento_total']) if not pd.isna(row['Faturamento_total']) else 0,
                'ticket_medio': float(row['Ticket_medio_notas']) if not pd.isna(row['Ticket_medio_notas']) else 0,
                'media_mensal': float(row['Media_nota_mes']) if not pd.isna(row['Media_nota_mes']) else 0,
                'meses_atividade': float(row['Meses_atividade']) if not pd.isna(row['Meses_atividade']) else 0,
                'data_primeira_nota': row['Data_primeira_nota'].strftime('%Y-%m-%d') if pd.notna(row['Data_primeira_nota']) else None,
                'data_ultima_nota': row['Data_ultima_nota'].strftime('%Y-%m-%d') if pd.notna(row['Data_ultima_nota']) else None
            }

            return {
                'producer': producer_details,
                'success': True
            }

        except Exception as e:
            logger.error(f"Error getting producer details: {e}")
            return {"error": str(e)}

    def analyze_unmapped_users(self) -> Dict[str, Any]:
        """Analyze users with incomplete mapping information"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Analyze mapping completeness
            mapping_analysis = {
                'total_records': len(self.df),
                'user_id_only': 0,
                'lead_id_only': 0,
                'name_only': 0,
                'complete_mapping': 0,
                'no_mapping': 0
            }

            # Check mapping patterns
            has_user_id = self.df['user_id'].notna()
            has_lead_id = self.df['Lead_id'].notna() if 'Lead_id' in self.df.columns else False
            has_name = self.df['Nome do Lead'].notna() if 'Nome do Lead' in self.df.columns else False

            # Count mapping patterns
            mapping_analysis['user_id_only'] = len(self.df[has_user_id & ~has_lead_id & ~has_name])
            mapping_analysis['lead_id_only'] = len(self.df[~has_user_id & has_lead_id & ~has_name])
            mapping_analysis['name_only'] = len(self.df[~has_user_id & ~has_lead_id & has_name])
            mapping_analysis['complete_mapping'] = len(self.df[has_user_id & has_lead_id & has_name])
            mapping_analysis['no_mapping'] = len(self.df[~has_user_id & ~has_lead_id & ~has_name])

            # Medical production among unmapped users
            unmapped_medical = self.df[
                (self.df['Gerou_Nota_Fiscal'] == 'Sim') &
                (has_user_id & ~has_name)
            ]

            unmapped_stats = {
                'total_unmapped_producers': len(unmapped_medical),
                'unmapped_revenue': float(unmapped_medical['Faturamento_total'].sum()) if len(unmapped_medical) > 0 else 0,
                'unmapped_invoices': int(unmapped_medical['Total_notas'].sum()) if len(unmapped_medical) > 0 else 0
            }

            return {
                'mapping_analysis': mapping_analysis,
                'unmapped_medical_stats': unmapped_stats,
                'success': True
            }

        except Exception as e:
            logger.error(f"Error analyzing unmapped users: {e}")
            return {"error": str(e)}

    def analyze_time_to_first_invoice(self) -> Dict[str, Any]:
        """Analyze time from implementation creation to first invoice"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Filter records with both dates available and invoice generation
            analysis_df = self.df[
                (self.df['Gerou_Nota_Fiscal'] == 'Sim') &
                (pd.notna(self.df['Data_Criacao_Implantacao'])) &
                (pd.notna(self.df['Data_primeira_nota']))
            ].copy()

            if analysis_df.empty:
                return {"error": "No data available with both implementation and first invoice dates"}

            # Calculate time difference in days
            analysis_df['dias_para_primeira_nota'] = (
                analysis_df['Data_primeira_nota'] - analysis_df['Data_Criacao_Implantacao']
            ).dt.days

            # Remove negative values (data inconsistencies)
            analysis_df = analysis_df[analysis_df['dias_para_primeira_nota'] >= 0]

            if analysis_df.empty:
                return {"error": "No valid time data available"}

            # Calculate statistics
            time_stats = {
                'media_dias': float(analysis_df['dias_para_primeira_nota'].mean()),
                'mediana_dias': float(analysis_df['dias_para_primeira_nota'].median()),
                'min_dias': int(analysis_df['dias_para_primeira_nota'].min()),
                'max_dias': int(analysis_df['dias_para_primeira_nota'].max()),
                'desvio_padrao': float(analysis_df['dias_para_primeira_nota'].std()),
                'total_registros': len(analysis_df)
            }

            # Time distribution by ranges
            time_distribution = [
                {'range': '0-30 dias', 'count': len(analysis_df[analysis_df['dias_para_primeira_nota'] <= 30])},
                {'range': '31-60 dias', 'count': len(analysis_df[(analysis_df['dias_para_primeira_nota'] > 30) & (analysis_df['dias_para_primeira_nota'] <= 60)])},
                {'range': '61-90 dias', 'count': len(analysis_df[(analysis_df['dias_para_primeira_nota'] > 60) & (analysis_df['dias_para_primeira_nota'] <= 90)])},
                {'range': '91-180 dias', 'count': len(analysis_df[(analysis_df['dias_para_primeira_nota'] > 90) & (analysis_df['dias_para_primeira_nota'] <= 180)])},
                {'range': 'Mais de 180 dias', 'count': len(analysis_df[analysis_df['dias_para_primeira_nota'] > 180])}
            ]

            # Monthly trend analysis
            analysis_df['mes_implementacao'] = analysis_df['Data_Criacao_Implantacao'].dt.to_period('M')
            monthly_trend = analysis_df.groupby('mes_implementacao').agg({
                'dias_para_primeira_nota': 'mean',
                'Data_Criacao_Implantacao': 'count'
            }).reset_index()

            monthly_trend['mes_str'] = monthly_trend['mes_implementacao'].astype(str)
            monthly_data = []
            for _, row in monthly_trend.iterrows():
                monthly_data.append({
                    'month': row['mes_str'],
                    'avg_days': float(row['dias_para_primeira_nota']),
                    'count': int(row['Data_Criacao_Implantacao'])
                })

            # Analysis by turma (fastest and slowest)
            turma_col = None
            for col in ['Turma', 'turma', 'TURMA']:
                if col in analysis_df.columns:
                    turma_col = col
                    break

            if turma_col:
                turma_analysis = analysis_df.groupby(turma_col).agg({
                    'dias_para_primeira_nota': 'mean'
                }).reset_index()

                turma_analysis['total_registros'] = analysis_df.groupby(turma_col).size().values
                turma_analysis.columns = ['Turma', 'media_dias', 'total_registros']
                turma_analysis = turma_analysis[turma_analysis['total_registros'] >= 3]  # At least 3 records

                if not turma_analysis.empty:
                    fastest_turmas = turma_analysis.nsmallest(5, 'media_dias').to_dict('records')
                    slowest_turmas = turma_analysis.nlargest(5, 'media_dias').to_dict('records')
                else:
                    fastest_turmas = []
                    slowest_turmas = []
            else:
                fastest_turmas = []
                slowest_turmas = []

            return {
                'time_stats': time_stats,
                'time_distribution': time_distribution,
                'monthly_trend': monthly_data,
                'fastest_turmas': fastest_turmas,
                'slowest_turmas': slowest_turmas,
                'success': True
            }

        except Exception as e:
            logger.error(f"Error analyzing time to first invoice: {e}")
            return {"error": str(e)}

    def analyze_geographic_distribution(self) -> Dict[str, Any]:
        """Analyze medical production by geographic distribution"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Filter records with invoice generation
            invoice_df = self.df[self.df['Gerou_Nota_Fiscal'] == 'Sim'].copy()

            if invoice_df.empty:
                return {"error": "No invoice data available"}

            # State analysis
            state_stats = invoice_df.groupby('Estado').agg({
                'Total_notas': 'sum',
                'Faturamento_total': 'sum',
                'Gerou_Nota_Fiscal': 'count',
                'Cidade': 'nunique'
            }).round(2)

            state_stats.columns = ['total_notas', 'faturamento_total', 'num_produtores', 'num_cidades']
            state_stats = state_stats.sort_values('faturamento_total', ascending=False)

            # City analysis
            city_stats = invoice_df.groupby(['Estado', 'Cidade']).agg({
                'Total_notas': 'sum',
                'Faturamento_total': 'sum',
                'Gerou_Nota_Fiscal': 'count'
            }).round(2)

            city_stats.columns = ['total_notas', 'faturamento_total', 'num_produtores']
            city_stats = city_stats.sort_values('faturamento_total', ascending=False)

            # Convert to lists
            state_list = []
            for state, row in state_stats.iterrows():
                if pd.notna(state):
                    state_list.append({
                        'estado': state,
                        'total_notas': int(row['total_notas']) if not pd.isna(row['total_notas']) else 0,
                        'faturamento_total': float(row['faturamento_total']) if not pd.isna(row['faturamento_total']) else 0,
                        'num_produtores': int(row['num_produtores']),
                        'num_cidades': int(row['num_cidades'])
                    })

            city_list = []
            for (state, city), row in city_stats.head(20).iterrows():
                if pd.notna(state) and pd.notna(city):
                    city_list.append({
                        'estado': state,
                        'cidade': city,
                        'total_notas': int(row['total_notas']) if not pd.isna(row['total_notas']) else 0,
                        'faturamento_total': float(row['faturamento_total']) if not pd.isna(row['faturamento_total']) else 0,
                        'num_produtores': int(row['num_produtores'])
                    })

            return {
                'state_ranking': state_list,
                'city_ranking': city_list,
                'summary': {
                    'total_states': len(state_list),
                    'total_cities': len(city_list),
                    'top_state': state_list[0]['estado'] if state_list else None,
                    'top_city': f"{city_list[0]['cidade']}, {city_list[0]['estado']}" if city_list else None
                },
                'success': True
            }

        except Exception as e:
            logger.error(f"Error analyzing geographic distribution: {e}")
            return {"error": str(e)}

    def analyze_course_performance(self) -> Dict[str, Any]:
        """Analyze medical production by course and academic formation"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Filter records with invoice generation
            invoice_df = self.df[self.df['Gerou_Nota_Fiscal'] == 'Sim'].copy()

            if invoice_df.empty:
                return {"error": "No invoice data available"}

            # Course analysis
            course_stats = invoice_df.groupby('Curso').agg({
                'Total_notas': 'sum',
                'Faturamento_total': 'sum',
                'Ticket_medio_notas': 'mean',
                'Gerou_Nota_Fiscal': 'count'
            }).round(2)

            course_stats.columns = ['total_notas', 'faturamento_total', 'ticket_medio', 'num_produtores']
            course_stats = course_stats.sort_values('faturamento_total', ascending=False)

            # Academic formation analysis
            formation_stats = invoice_df.groupby('Formação Academica').agg({
                'Total_notas': 'sum',
                'Faturamento_total': 'sum',
                'Ticket_medio_notas': 'mean',
                'Gerou_Nota_Fiscal': 'count'
            }).round(2)

            formation_stats.columns = ['total_notas', 'faturamento_total', 'ticket_medio', 'num_produtores']
            formation_stats = formation_stats.sort_values('faturamento_total', ascending=False)

            # Convert to lists
            course_list = []
            for course, row in course_stats.iterrows():
                if pd.notna(course):
                    course_list.append({
                        'curso': course,
                        'total_notas': int(row['total_notas']) if not pd.isna(row['total_notas']) else 0,
                        'faturamento_total': float(row['faturamento_total']) if not pd.isna(row['faturamento_total']) else 0,
                        'ticket_medio': float(row['ticket_medio']) if not pd.isna(row['ticket_medio']) else 0,
                        'num_produtores': int(row['num_produtores'])
                    })

            formation_list = []
            for formation, row in formation_stats.iterrows():
                if pd.notna(formation):
                    formation_list.append({
                        'formacao': formation,
                        'total_notas': int(row['total_notas']) if not pd.isna(row['total_notas']) else 0,
                        'faturamento_total': float(row['faturamento_total']) if not pd.isna(row['faturamento_total']) else 0,
                        'ticket_medio': float(row['ticket_medio']) if not pd.isna(row['ticket_medio']) else 0,
                        'num_produtores': int(row['num_produtores'])
                    })

            return {
                'course_ranking': course_list,
                'formation_ranking': formation_list,
                'summary': {
                    'total_courses': len(course_list),
                    'total_formations': len(formation_list),
                    'top_course': course_list[0]['curso'] if course_list else None,
                    'top_formation': formation_list[0]['formacao'] if formation_list else None
                },
                'success': True
            }

        except Exception as e:
            logger.error(f"Error analyzing course performance: {e}")
            return {"error": str(e)}

    def analyze_implementation_status(self) -> Dict[str, Any]:
        """Analyze medical production by implementation status"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Filter records with invoice generation
            invoice_df = self.df[self.df['Gerou_Nota_Fiscal'] == 'Sim'].copy()

            if invoice_df.empty:
                return {"error": "No invoice data available"}

            # Implementation status analysis
            status_stats = invoice_df.groupby('Status_Implantacao').agg({
                'Total_notas': 'sum',
                'Faturamento_total': 'sum',
                'Ticket_medio_notas': 'mean',
                'Gerou_Nota_Fiscal': 'count'
            }).round(2)

            status_stats.columns = ['total_notas', 'faturamento_total', 'ticket_medio', 'num_produtores']
            status_stats = status_stats.sort_values('faturamento_total', ascending=False)

            # Implementation phase analysis
            phase_stats = invoice_df.groupby('Fase_Implantacao').agg({
                'Total_notas': 'sum',
                'Faturamento_total': 'sum',
                'Ticket_medio_notas': 'mean',
                'Gerou_Nota_Fiscal': 'count'
            }).round(2)

            phase_stats.columns = ['total_notas', 'faturamento_total', 'ticket_medio', 'num_produtores']
            phase_stats = phase_stats.sort_values('faturamento_total', ascending=False)

            # Convert to lists
            status_list = []
            for status, row in status_stats.iterrows():
                if pd.notna(status):
                    status_list.append({
                        'status': status,
                        'total_notas': int(row['total_notas']) if not pd.isna(row['total_notas']) else 0,
                        'faturamento_total': float(row['faturamento_total']) if not pd.isna(row['faturamento_total']) else 0,
                        'ticket_medio': float(row['ticket_medio']) if not pd.isna(row['ticket_medio']) else 0,
                        'num_produtores': int(row['num_produtores'])
                    })

            phase_list = []
            for phase, row in phase_stats.iterrows():
                if pd.notna(phase):
                    phase_list.append({
                        'fase': phase,
                        'total_notas': int(row['total_notas']) if not pd.isna(row['total_notas']) else 0,
                        'faturamento_total': float(row['faturamento_total']) if not pd.isna(row['faturamento_total']) else 0,
                        'ticket_medio': float(row['ticket_medio']) if not pd.isna(row['ticket_medio']) else 0,
                        'num_produtores': int(row['num_produtores'])
                    })

            return {
                'status_ranking': status_list,
                'phase_ranking': phase_list,
                'summary': {
                    'total_statuses': len(status_list),
                    'total_phases': len(phase_list),
                    'most_productive_status': status_list[0]['status'] if status_list else None,
                    'most_productive_phase': phase_list[0]['fase'] if phase_list else None
                },
                'success': True
            }

        except Exception as e:
            logger.error(f"Error analyzing implementation status: {e}")
            return {"error": str(e)}

    def analyze_time_to_first_invoice(self) -> Dict[str, Any]:
        """Analyze time from implementation creation to first invoice"""
        try:
            if self.data_loader.df is None or self.data_loader.df.empty:
                return {"error": "No data available"}

            df = self.data_loader.df.copy()

            # Filter records with both dates
            df_filtered = df[
                (df['Data_Criacao_Implantacao'].notna()) &
                (df['Data_primeira_nota'].notna()) &
                (df['Gerou_Nota_Fiscal'] == 'Sim')
            ].copy()

            if df_filtered.empty:
                return {"error": "No records with both implementation and first invoice dates"}

            # Convert to datetime
            df_filtered['Data_Criacao_Implantacao'] = pd.to_datetime(df_filtered['Data_Criacao_Implantacao'], errors='coerce')
            df_filtered['Data_primeira_nota'] = pd.to_datetime(df_filtered['Data_primeira_nota'], errors='coerce')

            # Calculate days difference
            df_filtered['dias_para_primeira_nota'] = (
                df_filtered['Data_primeira_nota'] - df_filtered['Data_Criacao_Implantacao']
            ).dt.days

            # Remove negative values (data inconsistencies)
            df_filtered = df_filtered[df_filtered['dias_para_primeira_nota'] >= 0]

            if df_filtered.empty:
                return {"error": "No valid time calculations possible"}

            # Overall statistics
            time_stats = {
                'media_dias': float(df_filtered['dias_para_primeira_nota'].mean()),
                'mediana_dias': float(df_filtered['dias_para_primeira_nota'].median()),
                'min_dias': int(df_filtered['dias_para_primeira_nota'].min()),
                'max_dias': int(df_filtered['dias_para_primeira_nota'].max()),
                'std_dias': float(df_filtered['dias_para_primeira_nota'].std()),
                'total_records': len(df_filtered)
            }

            # Analysis by turma
            turma_time_analysis = df_filtered.groupby('Turma').agg({
                'dias_para_primeira_nota': ['mean', 'median', 'count'],
                'Valor_Mensalidade': 'mean'
            }).round(2)

            turma_time_analysis.columns = ['media_dias', 'mediana_dias', 'total_registros', 'valor_medio_mensalidade']
            turma_time_analysis = turma_time_analysis.reset_index()
            turma_time_analysis = turma_time_analysis.sort_values('media_dias')

            # Analysis by university
            university_time_analysis = df_filtered.groupby('Universidade').agg({
                'dias_para_primeira_nota': ['mean', 'median', 'count'],
                'Valor_Mensalidade': 'mean'
            }).round(2)

            university_time_analysis.columns = ['media_dias', 'mediana_dias', 'total_registros', 'valor_medio_mensalidade']
            university_time_analysis = university_time_analysis.reset_index()
            university_time_analysis = university_time_analysis.sort_values('media_dias')

            # Time distribution ranges
            time_ranges = [
                ('0-30 dias', (df_filtered['dias_para_primeira_nota'] <= 30).sum()),
                ('31-60 dias', ((df_filtered['dias_para_primeira_nota'] > 30) & (df_filtered['dias_para_primeira_nota'] <= 60)).sum()),
                ('61-90 dias', ((df_filtered['dias_para_primeira_nota'] > 60) & (df_filtered['dias_para_primeira_nota'] <= 90)).sum()),
                ('91-180 dias', ((df_filtered['dias_para_primeira_nota'] > 90) & (df_filtered['dias_para_primeira_nota'] <= 180)).sum()),
                ('Mais de 180 dias', (df_filtered['dias_para_primeira_nota'] > 180).sum())
            ]

            # Monthly trend
            df_filtered['mes_implementacao'] = df_filtered['Data_Criacao_Implantacao'].dt.to_period('M')
            monthly_trend = df_filtered.groupby('mes_implementacao').agg({
                'dias_para_primeira_nota': 'mean',
                'Nome': 'count'
            }).round(2)
            monthly_trend.columns = ['media_dias', 'total_implementacoes']
            monthly_trend = monthly_trend.reset_index()
            monthly_trend['mes_implementacao'] = monthly_trend['mes_implementacao'].astype(str)

            return {
                'time_stats': time_stats,
                'turma_analysis': turma_time_analysis.to_dict('records'),
                'university_analysis': university_time_analysis.to_dict('records'),
                'time_distribution': [{'range': r[0], 'count': r[1]} for r in time_ranges],
                'monthly_trend': monthly_trend.to_dict('records'),
                'fastest_turmas': turma_time_analysis.head(10).to_dict('records'),
                'slowest_turmas': turma_time_analysis.tail(10).to_dict('records')
            }

        except Exception as e:
            logger.error(f"Error analyzing time to first invoice: {e}")
            return {"error": str(e)}

    def analyze_monthly_trends(self) -> Dict[str, Any]:
        """Analyze Month over Month trends for medical production"""
        try:
            if self.df is None or self.df.empty:
                return {"error": "No data available"}

            # Filter records with invoice generation and valid dates
            invoice_df = self.df[
                (self.df['Gerou_Nota_Fiscal'] == 'Sim') &
                (pd.notna(self.df['Data_ultima_nota']))
            ].copy()

            if invoice_df.empty:
                return {"error": "No invoice data available for monthly trends"}

            # Create month-year column for grouping
            invoice_df['year_month'] = invoice_df['Data_ultima_nota'].dt.to_period('M')

            # Get last 12 months of data
            current_date = datetime.now()
            twelve_months_ago = current_date - timedelta(days=365)

            # Filter for last 12 months
            recent_df = invoice_df[
                invoice_df['Data_ultima_nota'] >= twelve_months_ago
            ].copy()

            if recent_df.empty:
                return {"error": "No recent data available for monthly trends"}

            # Group by month and calculate metrics
            monthly_stats = recent_df.groupby('year_month').agg({
                'Total_notas': ['sum', 'count'],  # sum = total notes, count = number of users
                'Faturamento_total': 'sum',
                'Ticket_medio_notas': 'mean'
            }).round(2)

            # Flatten column names
            monthly_stats.columns = [
                'total_notes', 'active_users', 'total_revenue', 'avg_note_value'
            ]

            # Calculate average notes per user
            monthly_stats['avg_notes_per_user'] = (
                monthly_stats['total_notes'] / monthly_stats['active_users']
            ).round(1)

            # Reset index to get month as column
            monthly_stats = monthly_stats.reset_index()
            monthly_stats['month_name'] = monthly_stats['year_month'].dt.strftime('%b %Y')
            monthly_stats['month_sort'] = monthly_stats['year_month'].dt.to_timestamp()

            # Sort by date
            monthly_stats = monthly_stats.sort_values('month_sort')

            # Calculate growth rates (month over month)
            growth_rates = {}

            if len(monthly_stats) >= 2:
                # Get last two months for comparison
                last_month = monthly_stats.iloc[-1]
                prev_month = monthly_stats.iloc[-2]

                # Calculate growth rates
                if prev_month['avg_notes_per_user'] > 0:
                    growth_rates['avg_notes_growth'] = (
                        (last_month['avg_notes_per_user'] - prev_month['avg_notes_per_user']) /
                        prev_month['avg_notes_per_user'] * 100
                    )
                else:
                    growth_rates['avg_notes_growth'] = 0

                if prev_month['total_notes'] > 0:
                    growth_rates['total_notes_growth'] = (
                        (last_month['total_notes'] - prev_month['total_notes']) /
                        prev_month['total_notes'] * 100
                    )
                else:
                    growth_rates['total_notes_growth'] = 0

                if prev_month['avg_note_value'] > 0:
                    growth_rates['avg_value_growth'] = (
                        (last_month['avg_note_value'] - prev_month['avg_note_value']) /
                        prev_month['avg_note_value'] * 100
                    )
                else:
                    growth_rates['avg_value_growth'] = 0
            else:
                growth_rates = {
                    'avg_notes_growth': 0,
                    'total_notes_growth': 0,
                    'avg_value_growth': 0
                }

            # Convert to list for JSON serialization
            monthly_data = []
            for _, row in monthly_stats.iterrows():
                monthly_data.append({
                    'month': row['month_name'],
                    'month_sort': row['month_sort'].strftime('%Y-%m'),
                    'avg_notes_per_user': float(row['avg_notes_per_user']) if not pd.isna(row['avg_notes_per_user']) else 0,
                    'total_notes': int(row['total_notes']) if not pd.isna(row['total_notes']) else 0,
                    'avg_note_value': float(row['avg_note_value']) if not pd.isna(row['avg_note_value']) else 0,
                    'active_users': int(row['active_users']) if not pd.isna(row['active_users']) else 0,
                    'total_revenue': float(row['total_revenue']) if not pd.isna(row['total_revenue']) else 0
                })

            # Calculate overall trends
            if len(monthly_data) >= 3:
                # Calculate 3-month average growth
                recent_3_months = monthly_data[-3:]
                avg_notes_trend = np.mean([m['avg_notes_per_user'] for m in recent_3_months])
                total_notes_trend = np.mean([m['total_notes'] for m in recent_3_months])
                avg_value_trend = np.mean([m['avg_note_value'] for m in recent_3_months])
            else:
                avg_notes_trend = monthly_data[-1]['avg_notes_per_user'] if monthly_data else 0
                total_notes_trend = monthly_data[-1]['total_notes'] if monthly_data else 0
                avg_value_trend = monthly_data[-1]['avg_note_value'] if monthly_data else 0

            return {
                'monthly_data': monthly_data,
                'growth_rates': growth_rates,
                'trends': {
                    'avg_notes_trend': float(avg_notes_trend),
                    'total_notes_trend': float(total_notes_trend),
                    'avg_value_trend': float(avg_value_trend)
                },
                'summary': {
                    'total_months_analyzed': len(monthly_data),
                    'latest_month': monthly_data[-1]['month'] if monthly_data else None,
                    'latest_avg_notes_per_user': monthly_data[-1]['avg_notes_per_user'] if monthly_data else 0,
                    'latest_total_notes': monthly_data[-1]['total_notes'] if monthly_data else 0
                },
                'success': True
            }

        except Exception as e:
            logger.error(f"Error analyzing monthly trends: {e}")
            return {"error": str(e)}
