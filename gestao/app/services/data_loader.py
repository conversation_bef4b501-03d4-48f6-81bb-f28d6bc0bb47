"""
Amigo DataHub - Data Loader Service
Service for loading and processing data
"""

import os
import pandas as pd
import logging
from flask import current_app
from typing import Dict, List, Any, Optional, Union, Tuple
import pandas as pd

logger = logging.getLogger(__name__)

# Mapping of responsible IDs to names
responsaveis_mapping = {
    "8f10537d-ce1d-4180-a168-d1403cba67e4": "<PERSON> Calasans",  # ResponsableOnboarding
    "86e11e54-26f5-4415-bf1e-3f18efc16291": "<PERSON> Calasans",  # ResponsableOngoing
    "RHoTKEJPQ-6yOIijySnuJw": "Victor Calasans",                # ResponsableContabil
    "9PKkbeysS1GYpr6-SXnwZA": "Victor Calasans"                 # ResponsableSocietário
}

class DataLoader:
    """Data Loader Service"""

    def __init__(self: List[Any]) -> None:
        """Initialize the data loader"""
        self.df = None
        self.lead_id_col = None
        self.opp_id_col = None
        self.dimension_tables = {}

    def load_data(self: List[Any]) -> pd.DataFrame:
        """
        Load data from the data file (CSV or XLSX) and process it

        Returns:
            DataFrame: The processed data
        """
        try:
            # Get the data file path from the configuration
            file_path = current_app.config['DATA_FILE']

            # Check if the file exists
            if not os.path.exists(file_path):
                logger.error(f"File {file_path} not found")
                return pd.DataFrame()

            # Determine file type based on extension
            file_extension = os.path.splitext(file_path)[1].lower()

            # Load the data based on file type
            if file_extension == '.csv':
                logger.info(f"Loading CSV file: {file_path}")
                self.df = pd.read_csv(file_path, encoding='utf-8', low_memory=False)
            elif file_extension in ['.xlsx', '.xls']:
                logger.info(f"Loading Excel file: {file_path}")
                self.df = pd.read_excel(file_path, engine='openpyxl')
            else:
                logger.error(f"Unsupported file type: {file_extension}")
                return pd.DataFrame()

            # Process the data
            self._process_data()

            # Identify ID columns
            self._identify_id_columns()

            # Load dimension tables
            self._load_dimension_tables()

            return self.df
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            return pd.DataFrame()

    def _process_data(self: List[Any]) -> None:
        """Process the data"""
        try:
            # Convert date columns to datetime
            self._convert_date_columns()

            # Replace responsible IDs with names
            self._replace_responsible_ids()

            # Add status position column
            self._add_status_position()
        except Exception as e:
            logger.error(f"Error processing data: {e}")

    def _convert_date_columns(self: List[Any]) -> Any:
        """Convert date columns to datetime"""
        try:
            # Different date formats in the file
            date_columns = {
                # Format YYYY-MM-DD
                'Data_criacao_Oportunidade': {'format': '%Y-%m-%d'},
                'Data_criacao': {'format': '%Y-%m-%d'},
                'Data_criacao_lead': {'format': '%Y-%m-%d'},
                'Data_Criacao_Implantacao': {'format': '%Y-%m-%d'},

                # Other date columns
                'Data_Finalizacao': {'format': None},
                'Data_Mudanca_Estagio': {'format': None},
                'DataMarco1 - Co. Hero': {'format': None},
                'DateMarco2 - Constituído': {'format': None},
                'Marco 3 - Homologado': {'format': None},
                'Marco 4 - Liberação': {'format': None},
                'ActualStartDate': {'format': None},
                'DataPrevistaDeFinalização': {'format': None}
            }

            # Process existing date columns
            for col, options in date_columns.items():
                if col in self.df.columns:
                    try:
                        # Pre-process the column to handle problematic strings
                        if self.df[col].dtype == object:  # Only for string columns
                            # Replace problematic strings with NaN
                            def clean_date_string(x: List[Any]) -> Any:
                                if not isinstance(x, str):
                                    return x
                                # Check for problematic keywords
                                lower_x = str(x).lower()
                                if any(keyword in lower_x for keyword in ['for', 'nan', 'none', 'null', 'undefined', 'data', 'result']):
                                    return pd.NA
                                # Check if it's a valid date format (basic check)
                                if not any(c.isdigit() for c in x):
                                    return pd.NA
                                return x

                            self.df[col] = self.df[col].apply(clean_date_string)

                        # Convert to datetime with specific options for each column
                        self.df[col] = pd.to_datetime(self.df[col], errors='coerce', **options)

                        # Remove timezone if the column has datetime values
                        if not self.df[col].empty and pd.api.types.is_datetime64_dtype(self.df[col]):
                            try:
                                # Ensure all datetime values are timezone-naive
                                # First convert all to UTC, then remove timezone info
                                if self.df[col].dt.tz is not None:
                                    self.df[col] = self.df[col].dt.tz_convert('UTC').dt.tz_localize(None)
                                # Handle mixed timezone-aware and timezone-naive values
                                else:
                                    # Check if any values have timezone info
                                    has_tz = False
                                    for val in self.df[col].dropna():
                                        if hasattr(val, 'tzinfo') and val.tzinfo is not None:
                                            has_tz = True
                                            break

                                    if has_tz:
                                        # Convert each value individually
                                        self.df[col] = self.df[col].apply(
                                            lambda x: x.tz_convert('UTC').replace(tzinfo=None)
                                            if pd.notna(x) and hasattr(x, 'tzinfo') and x.tzinfo is not None
                                            else x
                                        )
                            except Exception as e:
                                logger.warning(f"Could not process timezone for column {col}: {e}")
                    except Exception as e:
                        logger.warning(f"Could not convert column {col} to datetime: {e}")
                        # Ensure the column is still usable even if conversion failed
                        if col in self.df.columns and self.df[col].dtype == object:
                            try:
                                # Set problematic values to NaN
                                self.df[col] = pd.to_datetime(self.df[col], errors='coerce')
                            except Exception as inner_e:
                                logger.warning(f"Second attempt to convert column {col} failed: {inner_e}")

            # Ensure we have all required date columns
            # If Data_criacao_Oportunidade is missing, try to create it from other date columns
            if 'Data_criacao_Oportunidade' not in self.df.columns:
                if 'Data_criacao' in self.df.columns:
                    logger.info("Using Data_criacao for Data_criacao_Oportunidade")
                    self.df['Data_criacao_Oportunidade'] = pd.to_datetime(self.df['Data_criacao'], format='%Y-%m-%d', errors='coerce')
                elif 'Data_criacao_lead' in self.df.columns:
                    logger.info("Using Data_criacao_lead for Data_criacao_Oportunidade")
                    self.df['Data_criacao_Oportunidade'] = pd.to_datetime(self.df['Data_criacao_lead'], format='%Y-%m-%d', errors='coerce')
                elif 'Data_Criacao_Implantacao' in self.df.columns:
                    logger.info("Using Data_Criacao_Implantacao for Data_criacao_Oportunidade")
                    self.df['Data_criacao_Oportunidade'] = pd.to_datetime(self.df['Data_Criacao_Implantacao'], format='%Y-%m-%d', errors='coerce')
                else:
                    # Create a default date column with today's date
                    logger.info("Creating default Data_criacao_Oportunidade with today's date")
                    self.df['Data_criacao_Oportunidade'] = pd.Timestamp.now().strftime('%Y-%m-%d')
                    self.df['Data_criacao_Oportunidade'] = pd.to_datetime(self.df['Data_criacao_Oportunidade'], format='%Y-%m-%d')

            # If Data_criacao_lead is missing, try to create it from other date columns
            if 'Data_criacao_lead' not in self.df.columns:
                if 'Data_criacao' in self.df.columns:
                    logger.info("Using Data_criacao for Data_criacao_lead")
                    self.df['Data_criacao_lead'] = pd.to_datetime(self.df['Data_criacao'], format='%Y-%m-%d', errors='coerce')
                elif 'Data_criacao_Oportunidade' in self.df.columns:
                    logger.info("Using Data_criacao_Oportunidade for Data_criacao_lead")
                    self.df['Data_criacao_lead'] = pd.to_datetime(self.df['Data_criacao_Oportunidade'], format='%Y-%m-%d', errors='coerce')
                elif 'Data_Criacao_Implantacao' in self.df.columns:
                    logger.info("Using Data_Criacao_Implantacao for Data_criacao_lead")
                    self.df['Data_criacao_lead'] = pd.to_datetime(self.df['Data_Criacao_Implantacao'], format='%Y-%m-%d', errors='coerce')
                else:
                    # Create a default date column with today's date
                    logger.info("Creating default Data_criacao_lead with today's date")
                    self.df['Data_criacao_lead'] = pd.Timestamp.now().strftime('%Y-%m-%d')
                    self.df['Data_criacao_lead'] = pd.to_datetime(self.df['Data_criacao_lead'], format='%Y-%m-%d')

            # If Data_Criacao_Implantacao is missing, try to create it from other date columns
            if 'Data_Criacao_Implantacao' not in self.df.columns:
                if 'Data_criacao' in self.df.columns:
                    logger.info("Using Data_criacao for Data_Criacao_Implantacao")
                    self.df['Data_Criacao_Implantacao'] = pd.to_datetime(self.df['Data_criacao'], format='%Y-%m-%d', errors='coerce')
                elif 'Data_criacao_Oportunidade' in self.df.columns:
                    logger.info("Using Data_criacao_Oportunidade for Data_Criacao_Implantacao")
                    self.df['Data_Criacao_Implantacao'] = pd.to_datetime(self.df['Data_criacao_Oportunidade'], format='%Y-%m-%d', errors='coerce')
                elif 'Data_criacao_lead' in self.df.columns:
                    logger.info("Using Data_criacao_lead for Data_Criacao_Implantacao")
                    self.df['Data_Criacao_Implantacao'] = pd.to_datetime(self.df['Data_criacao_lead'], format='%Y-%m-%d', errors='coerce')
                else:
                    # Create a default date column with today's date
                    logger.info("Creating default Data_Criacao_Implantacao with today's date")
                    self.df['Data_Criacao_Implantacao'] = pd.Timestamp.now().strftime('%Y-%m-%d')
                    self.df['Data_Criacao_Implantacao'] = pd.to_datetime(self.df['Data_Criacao_Implantacao'], format='%Y-%m-%d')

            # If Data_Finalizacao is missing, use Data_Criacao_Implantacao as fallback
            if 'Data_Finalizacao' not in self.df.columns and 'Data_Criacao_Implantacao' in self.df.columns:
                logger.info("Using Data_Criacao_Implantacao as fallback for Data_Finalizacao")
                self.df['Data_Finalizacao'] = self.df['Data_Criacao_Implantacao']

            # If Data_Mudanca_Estagio is missing, use Data_criacao_Oportunidade as fallback
            if 'Data_Mudanca_Estagio' not in self.df.columns and 'Data_criacao_Oportunidade' in self.df.columns:
                logger.info("Using Data_criacao_Oportunidade as fallback for Data_Mudanca_Estagio")
                self.df['Data_Mudanca_Estagio'] = self.df['Data_criacao_Oportunidade']

            # Log the date columns that are available
            date_cols = [col for col in self.df.columns if 'data' in col.lower() or 'date' in col.lower()]
            logger.info(f"Available date columns: {date_cols}")

        except Exception as e:
            logger.error(f"Error converting date columns: {e}")

    def _replace_responsible_ids(self: List[Any]) -> None:
        """Replace responsible IDs with names"""
        try:
            # Responsible columns
            responsible_columns = ['ResponsableOnboarding', 'ResponsableOngoing', 'ResponsableContabil', 'ResponsableSocietário']

            # For each responsible column
            for column in responsible_columns:
                if column in self.df.columns:
                    # Replace IDs with names
                    self.df[column] = self.df[column].apply(
                        lambda x: responsaveis_mapping.get(str(x), x) if pd.notna(x) else x
                    )
        except Exception as e:
            logger.error(f"Error replacing responsible IDs: {e}")

    def _add_status_position(self: List[Any]) -> None:
        """Add status position column"""
        try:
            # Status position mapping
            status_positions = {
                'Aguardando Início': 1,
                'Em Andamento': 2,
                'Aguardando Documentação': 3,
                'Aguardando Aprovação': 4,
                'Aguardando Pagamento': 5,
                'Aguardando Assinatura': 6,
                'Aguardando Homologação': 7,
                'Aguardando Liberação': 8,
                'Aguardando Feedback': 9,
                'Finalizado': 10,
                'Cancelado': 0
            }

            # Add status position column
            if 'Status_Implantacao' in self.df.columns:
                self.df['StatusPosition'] = self.df['Status_Implantacao'].map(status_positions)
        except Exception as e:
            logger.error(f"Error adding status position: {e}")

    def _identify_id_columns(self: List[Any]) -> None:
        """Identify ID columns with improved fallback logic"""
        try:
            # Look for lead ID column (case insensitive) with multiple patterns
            self.lead_id_col = None
            lead_patterns = ['lead_id', 'leadid', 'lead id']

            for pattern in lead_patterns:
                for col in self.df.columns:
                    if pattern.replace('_', '').replace(' ', '').lower() in col.replace('_', '').replace(' ', '').lower():
                        self.lead_id_col = col
                        logger.info(f"Found lead ID column: {col}")
                        break
                if self.lead_id_col:
                    break

            # Look for opportunity ID column (case insensitive) with multiple patterns
            self.opp_id_col = None
            opp_patterns = ['oportunidade_id', 'oportunidadeid', 'oportunidade id', 'opportunity_id', 'opportunityid']

            for pattern in opp_patterns:
                for col in self.df.columns:
                    if pattern.replace('_', '').replace(' ', '').lower() in col.replace('_', '').replace(' ', '').lower():
                        self.opp_id_col = col
                        logger.info(f"Found opportunity ID column: {col}")
                        break
                if self.opp_id_col:
                    break

            # Log all available columns for debugging
            logger.info(f"Available columns: {list(self.df.columns)}")
            logger.info(f"Identified ID columns: lead_id_col={self.lead_id_col}, opp_id_col={self.opp_id_col}")

            # Verify the columns have data
            if self.lead_id_col and self.lead_id_col in self.df.columns:
                lead_count = self.df[self.lead_id_col].notna().sum()
                logger.info(f"Lead ID column '{self.lead_id_col}' has {lead_count} non-null values")

            if self.opp_id_col and self.opp_id_col in self.df.columns:
                opp_count = self.df[self.opp_id_col].notna().sum()
                unique_opps = self.df[self.opp_id_col].nunique()
                logger.info(f"Opportunity ID column '{self.opp_id_col}' has {opp_count} non-null values, {unique_opps} unique")

        except Exception as e:
            logger.error(f"Error identifying ID columns: {e}")

    def _load_dimension_tables(self: List[Any]) -> None:
        """Load dimension tables"""
        try:
            # Get the dimension tables directory from the configuration
            dimension_dir = current_app.config['DATA_DIR']

            # Check if the directory exists
            if not os.path.exists(dimension_dir):
                logger.warning(f"Dimension directory {dimension_dir} not found")
                return

            # Load each data file in the directory
            for file_name in os.listdir(dimension_dir):
                file_extension = os.path.splitext(file_name)[1].lower()

                # Check if the file is a supported data file
                if file_extension in ['.csv', '.xlsx', '.xls']:
                    try:
                        file_path = os.path.join(dimension_dir, file_name)
                        table_name = os.path.splitext(file_name)[0]

                        # Load the dimension table based on file type
                        if file_extension == '.csv':
                            logger.info(f"Loading CSV dimension table: {file_name}")
                            self.dimension_tables[table_name] = pd.read_csv(file_path, encoding='utf-8', low_memory=False)
                        elif file_extension in ['.xlsx', '.xls']:
                            logger.info(f"Loading Excel dimension table: {file_name}")
                            self.dimension_tables[table_name] = pd.read_excel(file_path, engine='openpyxl')

                        logger.info(f"Loaded dimension table {table_name} with {len(self.dimension_tables[table_name])} rows")
                    except Exception as e:
                        logger.error(f"Error loading dimension table {file_name}: {e}")
        except Exception as e:
            logger.error(f"Error loading dimension tables: {e}")

    def get_data(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the loaded data

        Returns:
            DataFrame: The loaded data
        """
        if self.df is None:
            return self.load_data()
        return self.df

    def get_lead_id_column(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the lead ID column

        Returns:
            str: The lead ID column
        """
        return self.lead_id_col

    def get_opportunity_id_column(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the opportunity ID column

        Returns:
            str: The opportunity ID column
        """
        return self.opp_id_col

    def get_dimension_table(self: List[Any], table_name: str) -> Optional[Dict[str, Any]]:
        """
        Get a dimension table

        Args:
            table_name (str): The name of the dimension table

        Returns:
            DataFrame: The dimension table
        """
        return self.dimension_tables.get(table_name, pd.DataFrame())

    def get_dimension_tables(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get all dimension tables

        Returns:
            dict: The dimension tables
        """
        return self.dimension_tables
