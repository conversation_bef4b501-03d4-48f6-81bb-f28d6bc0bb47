"""
ML Data Processor - Specialized data preparation for Machine Learning
Converts text to numbers and prepares clean datasets for ML models
"""

import pandas as pd
import numpy as np
import logging
import os
import re
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from sklearn.preprocessing import LabelEncoder, StandardScaler
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class MLDataProcessor:
    """Professional ML data processor for converting text to numbers and preparing clean datasets"""
    
    def __init__(self: List[Any]) -> None:
        """Initialize the ML data processor"""
        self.label_encoders = {}
        self.feature_mappings = {}
        self.processed_data = None
        self.ml_ready_data = None
        self.processing_log = []
        
    def log_step(self: List[Any], message: str) -> None:
        """Log processing step"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.processing_log.append(log_message)
        logger.info(log_message)
        
    def load_and_process_data(self, file_path: str) -> pd.DataFrame:
        """
        Load data from file and process it for ML
        
        Args:
            file_path: Path to the data file (CSV or Excel)
            
        Returns:
            Processed dataframe ready for ML
        """
        try:
            self.log_step(f"Carregando dados de: {file_path}")
            
            # Load data based on file extension
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8', low_memory=False)
            elif file_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(file_path, engine='openpyxl')
            else:
                raise ValueError(f"Formato de arquivo não suportado: {file_path}")
            
            self.log_step(f"Dados carregados: {len(df)} registros, {len(df.columns)} colunas")
            
            # Process the data
            processed_df = self.process_for_ml(df)
            
            return processed_df
            
        except Exception as e:
            self.log_step(f"Erro ao carregar dados: {e}")
            raise
    
    def process_for_ml(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process dataframe for ML by converting all text to numbers
        
        Args:
            df: Input dataframe
            
        Returns:
            ML-ready dataframe with all numeric features
        """
        try:
            self.log_step("Iniciando processamento para ML")
            
            # Create a copy
            ml_df = df.copy()
            
            # 1. Clean column names
            ml_df = self._clean_column_names(ml_df)
            
            # 2. Handle missing values first
            ml_df = self._handle_missing_values_ml(ml_df)
            
            # 3. Convert text columns to numbers
            ml_df = self._convert_text_to_numbers(ml_df)
            
            # 4. Handle date columns
            ml_df = self._convert_dates_to_numbers(ml_df)
            
            # 5. Create derived numerical features
            ml_df = self._create_numerical_features(ml_df)
            
            # 6. Final cleanup and validation
            ml_df = self._final_cleanup(ml_df)
            
            self.ml_ready_data = ml_df
            self.log_step(f"Processamento concluído: {len(ml_df)} registros, {len(ml_df.columns)} features numéricas")
            
            return ml_df
            
        except Exception as e:
            self.log_step(f"Erro no processamento ML: {e}")
            raise
    
    def _clean_column_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize column names"""
        self.log_step("Limpando nomes das colunas")
        
        # Create mapping of old to new column names
        new_columns = {}
        for col in df.columns:
            # Remove special characters and spaces
            clean_col = re.sub(r'[^\w\s]', '', str(col))
            clean_col = re.sub(r'\s+', '_', clean_col.strip())
            clean_col = clean_col.lower()
            new_columns[col] = clean_col
        
        df = df.rename(columns=new_columns)
        self.feature_mappings['column_mapping'] = new_columns
        
        return df
    
    def _handle_missing_values_ml(self, df: pd.DataFrame) -> pd.DataFrame:
        """Handle missing values specifically for ML"""
        self.log_step("Tratando valores ausentes para ML")
        
        for col in df.columns:
            if df[col].dtype in ['object', 'string']:
                # For text columns, fill with 'unknown'
                df[col] = df[col].fillna('unknown')
            elif df[col].dtype in ['float64', 'int64']:
                # For numeric columns, fill with median
                df[col] = df[col].fillna(df[col].median())
            else:
                # For other types, fill with 'unknown'
                df[col] = df[col].fillna('unknown')
        
        return df
    
    def _convert_text_to_numbers(self, df: pd.DataFrame) -> pd.DataFrame:
        """Convert all text columns to numerical representations"""
        self.log_step("Convertendo texto para números")
        
        for col in df.columns:
            if df[col].dtype in ['object', 'string']:
                try:
                    # First, try to convert to numeric directly
                    numeric_series = pd.to_numeric(df[col], errors='coerce')
                    
                    if not numeric_series.isna().all():
                        # If some values are numeric, use them
                        df[col] = numeric_series.fillna(0)
                        self.log_step(f"Coluna {col}: convertida para numérico direto")
                    else:
                        # Use label encoding for categorical text
                        le = LabelEncoder()
                        df[col] = le.fit_transform(df[col].astype(str))
                        self.label_encoders[col] = le
                        self.log_step(f"Coluna {col}: codificada com {len(le.classes_)} categorias")
                        
                except Exception as e:
                    self.log_step(f"Erro ao converter {col}: {e}")
                    # Fallback: simple hash-based encoding
                    df[col] = df[col].astype(str).apply(lambda x: hash(x) % 10000)
        
        return df
    
    def _convert_dates_to_numbers(self, df: pd.DataFrame) -> pd.DataFrame:
        """Convert date columns to numerical features"""
        self.log_step("Convertendo datas para números")
        
        date_patterns = ['data', 'date', 'marco', 'prazo']
        
        for col in df.columns:
            if any(pattern in col.lower() for pattern in date_patterns):
                try:
                    # Convert to datetime
                    date_series = pd.to_datetime(df[col], errors='coerce')
                    
                    if not date_series.isna().all():
                        # Extract numerical features from dates
                        base_date = pd.Timestamp('2020-01-01')
                        
                        # Days since base date
                        df[f'{col}_days_since_base'] = (date_series - base_date).dt.days.fillna(0)
                        
                        # Year, month, day
                        df[f'{col}_year'] = date_series.dt.year.fillna(2020)
                        df[f'{col}_month'] = date_series.dt.month.fillna(1)
                        df[f'{col}_day'] = date_series.dt.day.fillna(1)
                        df[f'{col}_weekday'] = date_series.dt.weekday.fillna(0)
                        
                        # Remove original date column
                        df = df.drop(columns=[col])
                        
                        self.log_step(f"Data {col}: convertida para 5 features numéricas")
                    
                except Exception as e:
                    self.log_step(f"Erro ao converter data {col}: {e}")
                    # Fallback: remove the column
                    df = df.drop(columns=[col])
        
        return df
    
    def _create_numerical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create additional numerical features"""
        self.log_step("Criando features numéricas derivadas")
        
        # Find potential value columns
        value_cols = [col for col in df.columns if any(word in col.lower() for word in ['valor', 'preco', 'price', 'amount'])]
        
        for col in value_cols:
            if df[col].dtype in ['float64', 'int64']:
                # Create value ranges
                df[f'{col}_log'] = np.log1p(df[col].abs())
                df[f'{col}_sqrt'] = np.sqrt(df[col].abs())
                
                # Create bins
                try:
                    df[f'{col}_bin'] = pd.cut(df[col], bins=5, labels=False).fillna(0)
                except:
                    df[f'{col}_bin'] = 0
        
        # Create interaction features for important columns
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        
        if len(numeric_cols) >= 2:
            # Create some interaction features (limit to avoid explosion)
            for i, col1 in enumerate(numeric_cols[:3]):
                for col2 in numeric_cols[i+1:4]:
                    try:
                        df[f'{col1}_x_{col2}'] = df[col1] * df[col2]
                    except:
                        pass
        
        return df
    
    def _final_cleanup(self, df: pd.DataFrame) -> pd.DataFrame:
        """Final cleanup and validation"""
        self.log_step("Limpeza final dos dados")
        
        # Ensure all columns are numeric
        for col in df.columns:
            if df[col].dtype not in ['float64', 'int64']:
                try:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
                except:
                    df[col] = 0
        
        # Handle infinite values
        df = df.replace([np.inf, -np.inf], 0)
        
        # Fill any remaining NaN values
        df = df.fillna(0)
        
        # Remove columns with zero variance
        zero_var_cols = []
        for col in df.columns:
            if df[col].std() == 0:
                zero_var_cols.append(col)
        
        if zero_var_cols:
            df = df.drop(columns=zero_var_cols)
            self.log_step(f"Removidas {len(zero_var_cols)} colunas com variância zero")
        
        return df
    
    def prepare_for_clustering(self, df: pd.DataFrame, target_col: str = None) -> Tuple[pd.DataFrame, List[str]]:
        """
        Prepare data specifically for clustering
        
        Args:
            df: Input dataframe
            target_col: Target column name (will be excluded from features)
            
        Returns:
            Tuple of (features_df, feature_column_names)
        """
        try:
            self.log_step(f"Preparando dados para clusterização (target: {target_col})")
            
            # Process data for ML
            ml_df = self.process_for_ml(df)
            
            # Identify target column in processed data
            target_col_processed = None
            if target_col:
                # Find the processed version of target column
                original_mapping = self.feature_mappings.get('column_mapping', {})
                for orig, processed in original_mapping.items():
                    if orig == target_col:
                        target_col_processed = processed
                        break
            
            # Separate features and target
            feature_cols = [col for col in ml_df.columns if col != target_col_processed]
            features_df = ml_df[feature_cols].copy()
            
            # Scale features for clustering
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features_df)
            features_df = pd.DataFrame(features_scaled, columns=feature_cols, index=features_df.index)
            
            self.log_step(f"Dados preparados para clusterização: {len(features_df)} registros, {len(feature_cols)} features")
            
            return features_df, feature_cols
            
        except Exception as e:
            self.log_step(f"Erro na preparação para clusterização: {e}")
            raise
    
    def save_ml_dataset(self: List[Any], output_path: str) -> bool:
        """Save the ML-ready dataset to CSV"""
        if self.ml_ready_data is not None:
            self.ml_ready_data.to_csv(output_path, index=False)
            self.log_step(f"Dataset ML salvo em: {output_path}")
        else:
            raise ValueError("Nenhum dataset ML processado disponível")
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """Get summary of processing steps"""
        return {
            'steps_executed': len(self.processing_log),
            'processing_log': self.processing_log,
            'label_encoders_count': len(self.label_encoders),
            'feature_mappings': self.feature_mappings,
            'data_shape': self.ml_ready_data.shape if self.ml_ready_data is not None else None
        }
