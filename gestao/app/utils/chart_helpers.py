"""
Amigo DataHub - Chart Helpers
Utility functions for chart generation
"""

import logging
import json
from app.utils.formatters import format_currency, format_percentage
from typing import Dict, List, Any, Optional, Union, Tuple

logger = logging.getLogger(__name__)

def prepare_funnel_data(stages: List[Any], values: List[Any]) -> Dict[str, Any]:
    """
    Prepare data for a funnel chart
    
    Args:
        stages (list): The funnel stages
        values (list): The values for each stage
        
    Returns:
        dict: The prepared funnel data
    """
    try:
        if len(stages) != len(values):
            logger.error(f"Stages and values must have the same length: {len(stages)} != {len(values)}")
            return {}
        
        return {
            'labels': stages,
            'data': values
        }
    except Exception as e:
        logger.error(f"Error preparing funnel data: {e}")
        return {}

def prepare_pie_data(labels: List[Any], values: List[Any]) -> Dict[str, Any]:
    """
    Prepare data for a pie chart
    
    Args:
        labels (list): The pie chart labels
        values (list): The values for each label
        
    Returns:
        dict: The prepared pie data
    """
    try:
        if len(labels) != len(values):
            logger.error(f"Labels and values must have the same length: {len(labels)} != {len(values)}")
            return {}
        
        return {
            'labels': labels,
            'data': values
        }
    except Exception as e:
        logger.error(f"Error preparing pie data: {e}")
        return {}

def prepare_bar_data(labels: List[Any], values: List[Any], horizontal: List[Any] = False) -> Dict[str, Any]:
    """
    Prepare data for a bar chart
    
    Args:
        labels (list): The bar chart labels
        values (list): The values for each label
        horizontal (bool): Whether the bar chart is horizontal
        
    Returns:
        dict: The prepared bar data
    """
    try:
        if len(labels) != len(values):
            logger.error(f"Labels and values must have the same length: {len(labels)} != {len(values)}")
            return {}
        
        return {
            'labels': labels,
            'data': values,
            'horizontal': horizontal
        }
    except Exception as e:
        logger.error(f"Error preparing bar data: {e}")
        return {}

def prepare_line_data(labels: List[Any], values: List[Any], fill: List[Any] = True) -> Dict[str, Any]:
    """
    Prepare data for a line chart
    
    Args:
        labels (list): The line chart labels
        values (list): The values for each label
        fill (bool): Whether to fill the area under the line
        
    Returns:
        dict: The prepared line data
    """
    try:
        if len(labels) != len(values):
            logger.error(f"Labels and values must have the same length: {len(labels)} != {len(values)}")
            return {}
        
        return {
            'labels': labels,
            'data': values,
            'fill': fill
        }
    except Exception as e:
        logger.error(f"Error preparing line data: {e}")
        return {}

def prepare_multi_line_data(labels: List[Any], datasets: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare data for a multi-line chart
    
    Args:
        labels (list): The line chart labels
        datasets (list): A list of dictionaries with 'label', 'data', and 'color' keys
        
    Returns:
        dict: The prepared multi-line data
    """
    try:
        for dataset in datasets:
            if len(labels) != len(dataset.get('data', [])):
                logger.error(f"Labels and dataset data must have the same length: {len(labels)} != {len(dataset.get('data', []))}")
                return {}
        
        return {
            'labels': labels,
            'datasets': datasets
        }
    except Exception as e:
        logger.error(f"Error preparing multi-line data: {e}")
        return {}

def format_chart_data_for_js(chart_data: Dict[str, Any]) -> str:
    """
    Format chart data for JavaScript
    
    Args:
        chart_data (dict): The chart data
        
    Returns:
        str: The formatted chart data as a JSON string
    """
    try:
        # Ensure all values are strings
        for key, value in chart_data.items():
            if isinstance(value, dict):
                for k, v in value.items():
                    if isinstance(v, (int, float)):
                        value[k] = str(v)
            elif isinstance(value, (int, float)):
                chart_data[key] = str(value)
        
        return json.dumps(chart_data)
    except Exception as e:
        logger.error(f"Error formatting chart data for JS: {e}")
        return "{}"

def format_currency_for_chart(value: Any) -> str:
    """
    Format a currency value for a chart
    
    Args:
        value: The value to format
        
    Returns:
        str: The formatted currency value
    """
    try:
        return format_currency(value).replace('R$ ', '')
    except Exception as e:
        logger.error(f"Error formatting currency for chart: {e}")
        return "0"

def format_percentage_for_chart(value: Any) -> str:
    """
    Format a percentage value for a chart
    
    Args:
        value: The value to format
        
    Returns:
        str: The formatted percentage value
    """
    try:
        return format_percentage(value).replace('%', '')
    except Exception as e:
        logger.error(f"Error formatting percentage for chart: {e}")
        return "0"
