"""
Amigo DataHub - Extensions
"""
from typing import Any, Dict

def init_extensions(app: Any) -> None:
    """
    Initialize Flask extensions

    Args:
        app (Flask): The Flask application
    """
    # Register Jinja2 filters
    register_jinja_filters(app)

    # Register context processors
    register_context_processors(app)

def register_jinja_filters(app: Any) -> None:
    """
    Register custom Jinja2 filters

    Args:
        app (Flask): The Flask application
    """
    from app.utils.formatters import format_currency, format_date, format_percentage

    app.jinja_env.filters['currency'] = format_currency
    app.jinja_env.filters['date'] = format_date
    app.jinja_env.filters['percentage'] = format_percentage

def register_context_processors(app: Any) -> Dict[str, Any]:
    """
    Register context processors

    Args:
        app (Flask): The Flask application
    """
    @app.context_processor
    def inject_app_info() -> Dict[str, Any]:
        """Inject application information into templates"""
        return {
            'app_name': app.config.get('APP_NAME', 'DataHub Amigo One - Negócios'),
            'app_version': app.config.get('APP_VERSION', '1.0.0'),
            'colors': app.config.get('COLORS', {})
        }
