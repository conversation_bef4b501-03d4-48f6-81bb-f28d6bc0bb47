#!/usr/bin/env python3
"""
Script para executar clusterização na base de dados e gerar JSON para os modais
"""

import pandas as pd
import numpy as np
import json
import os
import sys

# Adicionar o diretório raiz ao path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def load_data():
    """Carrega os dados de clustering e mapeamento de turmas"""
    
    # Caminhos dos arquivos
    clustering_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'ML', 'datasets', 'clustering_dataset_ml_ready.csv')
    mapping_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'ML', 'datasets', 'turma_id_mapping.csv')
    
    # Carregar dados de clustering
    clustering_data = pd.read_csv(clustering_file)
    print(f"Dados de clustering carregados: {len(clustering_data)} turmas")
    
    # Carregar mapeamento de turmas
    turma_mapping = {}
    if os.path.exists(mapping_file):
        mapping_df = pd.read_csv(mapping_file)
        turma_mapping = dict(zip(mapping_df['turma_id'], mapping_df['turma_nome']))
        print(f"Mapeamento de turmas carregado: {len(turma_mapping)} turmas")
    else:
        print("Arquivo de mapeamento não encontrado, usando IDs como nomes")
    
    return clustering_data, turma_mapping

def perform_clustering(data, n_clusters=4):
    """Executa a clusterização K-Means"""
    
    # Preparar features para clustering
    features = ['total_leads', 'taxa_conversao', 'volume_implementacoes', 'receita_por_lead']
    X = data[features].copy()
    
    # Tratar valores NaN
    X = X.fillna(0)
    
    # Normalizar os dados
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Executar K-Means
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
    clusters = kmeans.fit_predict(X_scaled)
    
    # Calcular silhouette score
    silhouette_avg = silhouette_score(X_scaled, clusters)
    print(f"Silhouette Score para {n_clusters} clusters: {silhouette_avg:.3f}")
    
    # Adicionar clusters aos dados
    data_with_clusters = data.copy()
    data_with_clusters['cluster'] = clusters
    
    return data_with_clusters, kmeans, scaler

def analyze_clusters(data_with_clusters, turma_mapping):
    """Analisa os clusters e cria estrutura de dados para os modais"""
    
    clusters_data = {}
    
    for cluster_id in sorted(data_with_clusters['cluster'].unique()):
        cluster_data = data_with_clusters[data_with_clusters['cluster'] == cluster_id]
        
        # Estatísticas do cluster
        stats = {
            'total_turmas': len(cluster_data),
            'avg_leads': float(cluster_data['total_leads'].mean()),
            'avg_conversao': float(cluster_data['taxa_conversao'].mean()),
            'avg_implementacoes': float(cluster_data['volume_implementacoes'].mean()),
            'avg_receita_per_lead': float(cluster_data['receita_por_lead'].mean()),
            'std_leads': float(cluster_data['total_leads'].std()),
            'std_conversao': float(cluster_data['taxa_conversao'].std()),
            'std_receita_per_lead': float(cluster_data['receita_por_lead'].std())
        }
        
        # Lista de turmas do cluster
        turmas = []
        for _, row in cluster_data.iterrows():
            turma_id = int(row['turma_id'])
            turma_nome = turma_mapping.get(turma_id, f"Turma {turma_id}")
            
            turma_info = {
                'turma_id': turma_id,
                'turma_nome': turma_nome,
                'total_leads': float(row['total_leads']),
                'taxa_conversao': float(row['taxa_conversao']),
                'volume_implementacoes': float(row['volume_implementacoes']),
                'receita_por_lead': float(row['receita_por_lead'])
            }
            turmas.append(turma_info)
        
        # Ordenar turmas por receita por lead (decrescente)
        turmas.sort(key=lambda x: x['receita_por_lead'], reverse=True)
        
        clusters_data[str(cluster_id)] = {
            'cluster_id': cluster_id,
            'stats': stats,
            'turmas': turmas
        }
    
    return clusters_data

def classify_clusters(clusters_data):
    """Classifica os clusters em categorias qualitativas"""
    
    # Calcular métricas médias para classificação
    cluster_metrics = []
    for cluster_id, data in clusters_data.items():
        metrics = {
            'cluster_id': cluster_id,
            'avg_conversao': data['stats']['avg_conversao'],
            'avg_receita_per_lead': data['stats']['avg_receita_per_lead'],
            'total_turmas': data['stats']['total_turmas']
        }
        cluster_metrics.append(metrics)
    
    # Ordenar por receita per lead e conversão
    cluster_metrics.sort(key=lambda x: (x['avg_receita_per_lead'], x['avg_conversao']), reverse=True)
    
    # Classificar clusters
    classifications = {}
    for i, cluster in enumerate(cluster_metrics):
        if i == 0:
            classifications[cluster['cluster_id']] = 'premium'
        elif i == 1:
            classifications[cluster['cluster_id']] = 'crescimento'
        elif i == 2:
            classifications[cluster['cluster_id']] = 'desenvolvimento'
        else:
            classifications[cluster['cluster_id']] = 'oportunidade'
    
    return classifications

def generate_clustering_json():
    """Função principal para gerar o JSON de clustering"""
    
    print("=== Executando Clusterização ===")
    
    # Carregar dados
    clustering_data, turma_mapping = load_data()
    
    # Executar clusterização
    data_with_clusters, kmeans, scaler = perform_clustering(clustering_data, n_clusters=4)
    
    # Analisar clusters
    clusters_data = analyze_clusters(data_with_clusters, turma_mapping)
    
    # Classificar clusters
    cluster_classifications = classify_clusters(clusters_data)
    
    # Criar estrutura final
    result = {
        'metadata': {
            'total_turmas': len(clustering_data),
            'n_clusters': 4,
            'algorithm': 'K-Means',
            'features_used': ['total_leads', 'taxa_conversao', 'volume_implementacoes', 'receita_por_lead'],
            'generated_at': pd.Timestamp.now().isoformat()
        },
        'cluster_classifications': cluster_classifications,
        'clusters_data': clusters_data,
        'summary': {
            'cluster_distribution': {cluster_id: data['stats']['total_turmas'] 
                                   for cluster_id, data in clusters_data.items()},
            'best_performing_cluster': max(clusters_data.keys(), 
                                         key=lambda x: clusters_data[x]['stats']['avg_receita_per_lead']),
            'largest_cluster': max(clusters_data.keys(), 
                                 key=lambda x: clusters_data[x]['stats']['total_turmas'])
        }
    }
    
    # Salvar JSON
    output_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'ML', 'results', 'clustering_results.json')
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n=== Resultados Salvos ===")
    print(f"Arquivo: {output_file}")
    print(f"Total de turmas: {result['metadata']['total_turmas']}")
    print(f"Número de clusters: {result['metadata']['n_clusters']}")
    
    # Imprimir resumo dos clusters
    print(f"\n=== Resumo dos Clusters ===")
    for cluster_id, data in clusters_data.items():
        classification = cluster_classifications[cluster_id]
        stats = data['stats']
        print(f"Cluster {cluster_id} ({classification.title()}):")
        print(f"  - Turmas: {stats['total_turmas']}")
        print(f"  - Conversão média: {stats['avg_conversao']:.1f}%")
        print(f"  - Receita/Lead média: R$ {stats['avg_receita_per_lead']:.2f}")
        print(f"  - Leads médios: {stats['avg_leads']:.0f}")
        print()
    
    return result

if __name__ == "__main__":
    generate_clustering_json()
