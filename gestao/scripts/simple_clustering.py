#!/usr/bin/env python3
"""
Script simples para gerar dados de clustering para os modais
"""

import json
import os
import csv
from datetime import datetime
import math

def load_data():
    """Carrega os dados de clustering e mapeamento de turmas"""
    
    # Caminhos dos arquivos
    base_path = '/Users/<USER>/Desktop/Amigo_dataapp/one-interno/gestao/data/ML'
    clustering_file = os.path.join(base_path, 'datasets', 'clustering_dataset_ml_ready.csv')
    mapping_file = os.path.join(base_path, 'datasets', 'turma_id_mapping.csv')
    
    # Carregar dados de clustering
    clustering_data = []
    with open(clustering_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            clustering_data.append({
                'turma_id': int(row['turma_id']),
                'total_leads': float(row['total_leads']),
                'taxa_conversao': float(row['taxa_conversao']),
                'volume_implementacoes': float(row['volume_implementacoes']),
                'receita_por_lead': float(row['receita_por_lead'])
            })
    
    print(f"Dados de clustering carregados: {len(clustering_data)} turmas")
    
    # Carregar mapeamento de turmas
    turma_mapping = {}
    if os.path.exists(mapping_file):
        with open(mapping_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                turma_mapping[int(row['turma_id'])] = row['turma_nome']
        print(f"Mapeamento de turmas carregado: {len(turma_mapping)} turmas")
    else:
        print("Arquivo de mapeamento não encontrado, usando IDs como nomes")
    
    return clustering_data, turma_mapping

def simple_clustering(data):
    """Executa uma clusterização simples baseada em quartis"""
    
    # Calcular score composto para cada turma
    for turma in data:
        # Score baseado em receita por lead e taxa de conversão
        score = (turma['receita_por_lead'] * 0.6) + (turma['taxa_conversao'] * 10 * 0.4)
        turma['score'] = score
    
    # Ordenar por score
    data.sort(key=lambda x: x['score'], reverse=True)
    
    # Dividir em 4 clusters baseado em quartis
    total = len(data)
    q1 = total // 4
    q2 = total // 2
    q3 = 3 * total // 4
    
    for i, turma in enumerate(data):
        if i < q1:
            turma['cluster'] = 0  # Premium
        elif i < q2:
            turma['cluster'] = 1  # Crescimento
        elif i < q3:
            turma['cluster'] = 2  # Desenvolvimento
        else:
            turma['cluster'] = 3  # Oportunidade
    
    return data

def analyze_clusters(data_with_clusters, turma_mapping):
    """Analisa os clusters e cria estrutura de dados para os modais"""
    
    clusters_data = {}
    
    for cluster_id in range(4):
        cluster_data = [t for t in data_with_clusters if t['cluster'] == cluster_id]
        
        if not cluster_data:
            continue
        
        # Estatísticas do cluster
        total_turmas = len(cluster_data)
        avg_leads = sum(t['total_leads'] for t in cluster_data) / total_turmas
        avg_conversao = sum(t['taxa_conversao'] for t in cluster_data) / total_turmas
        avg_implementacoes = sum(t['volume_implementacoes'] for t in cluster_data) / total_turmas
        avg_receita_per_lead = sum(t['receita_por_lead'] for t in cluster_data) / total_turmas
        
        stats = {
            'total_turmas': total_turmas,
            'avg_leads': avg_leads,
            'avg_conversao': avg_conversao,
            'avg_implementacoes': avg_implementacoes,
            'avg_receita_per_lead': avg_receita_per_lead
        }
        
        # Lista de turmas do cluster
        turmas = []
        for turma in cluster_data:
            turma_id = turma['turma_id']
            turma_nome = turma_mapping.get(turma_id, f"Turma {turma_id}")
            
            turma_info = {
                'turma_id': turma_id,
                'turma_nome': turma_nome,
                'total_leads': turma['total_leads'],
                'taxa_conversao': turma['taxa_conversao'],
                'volume_implementacoes': turma['volume_implementacoes'],
                'receita_por_lead': turma['receita_por_lead']
            }
            turmas.append(turma_info)
        
        # Ordenar turmas por receita por lead (decrescente)
        turmas.sort(key=lambda x: x['receita_por_lead'], reverse=True)
        
        clusters_data[str(cluster_id)] = {
            'cluster_id': cluster_id,
            'stats': stats,
            'turmas': turmas
        }
    
    return clusters_data

def generate_clustering_json():
    """Função principal para gerar o JSON de clustering"""
    
    print("=== Executando Clusterização Simples ===")
    
    # Carregar dados
    clustering_data, turma_mapping = load_data()
    
    # Executar clusterização simples
    data_with_clusters = simple_clustering(clustering_data)
    
    # Analisar clusters
    clusters_data = analyze_clusters(data_with_clusters, turma_mapping)
    
    # Classificações dos clusters
    cluster_classifications = {
        '0': 'premium',
        '1': 'crescimento', 
        '2': 'desenvolvimento',
        '3': 'oportunidade'
    }
    
    # Criar estrutura final
    result = {
        'metadata': {
            'total_turmas': len(clustering_data),
            'n_clusters': 4,
            'algorithm': 'Simple Quartile-based',
            'features_used': ['receita_por_lead', 'taxa_conversao'],
            'generated_at': datetime.now().isoformat()
        },
        'cluster_classifications': cluster_classifications,
        'clusters_data': clusters_data,
        'summary': {
            'cluster_distribution': {cluster_id: data['stats']['total_turmas'] 
                                   for cluster_id, data in clusters_data.items()},
            'best_performing_cluster': max(clusters_data.keys(), 
                                         key=lambda x: clusters_data[x]['stats']['avg_receita_per_lead']),
            'largest_cluster': max(clusters_data.keys(), 
                                 key=lambda x: clusters_data[x]['stats']['total_turmas'])
        }
    }
    
    # Salvar JSON
    output_file = '/Users/<USER>/Desktop/Amigo_dataapp/one-interno/gestao/data/ML/results/clustering_results.json'
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n=== Resultados Salvos ===")
    print(f"Arquivo: {output_file}")
    print(f"Total de turmas: {result['metadata']['total_turmas']}")
    print(f"Número de clusters: {result['metadata']['n_clusters']}")
    
    # Imprimir resumo dos clusters
    print(f"\n=== Resumo dos Clusters ===")
    for cluster_id, data in clusters_data.items():
        classification = cluster_classifications[cluster_id]
        stats = data['stats']
        print(f"Cluster {cluster_id} ({classification.title()}):")
        print(f"  - Turmas: {stats['total_turmas']}")
        print(f"  - Conversão média: {stats['avg_conversao']:.1f}%")
        print(f"  - Receita/Lead média: R$ {stats['avg_receita_per_lead']:.2f}")
        print(f"  - Leads médios: {stats['avg_leads']:.0f}")
        print()
    
    return result

if __name__ == "__main__":
    generate_clustering_json()
