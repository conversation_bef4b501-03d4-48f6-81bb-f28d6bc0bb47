#!/usr/bin/env python

import os
import sys
import logging
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def check_data_files() -> bool:
    """
    Check if the required data files exist
    
    Returns:
        bool: True if all required files exist, False otherwise
    """
    # Get the project root directory
    project_root = Path(__file__).parent.absolute()
    
    # Get the parent directory (where the data files should be)
    data_parent_dir = project_root.parent
    
    # Check if the main data file exists
    main_data_file = os.path.join(data_parent_dir, 'base_dados.csv')
    if not os.path.exists(main_data_file):
        logger.error(f"Main data file not found: {main_data_file}")
        return False
    
    # Check if the dimension directory exists
    dimension_dir = os.path.join(data_parent_dir, 'dimensoes')
    if not os.path.exists(dimension_dir):
        logger.error(f"Dimension directory not found: {dimension_dir}")
        return False
    
    # Check if there are any dimension files
    dimension_files = [f for f in os.listdir(dimension_dir) if f.endswith('.csv')]
    if not dimension_files:
        logger.warning(f"No dimension files found in {dimension_dir}")
    else:
        logger.info(f"Found {len(dimension_files)} dimension files in {dimension_dir}")
    
    return True

if __name__ == "__main__":
    main()
