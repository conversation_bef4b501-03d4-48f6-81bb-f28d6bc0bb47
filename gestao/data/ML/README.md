# 📊 Diretório de Machine Learning - DataHub Amigo One

Este diretório contém todos os arquivos relacionados aos modelos de Machine Learning e datasets processados para análise de clustering e inteligência comercial.

## 📁 Estrutura de Diretórios

```
data/ML/
├── datasets/           # Datasets processados para ML
├── models/            # Modelos treinados e salvos
├── results/           # Resultados de clustering e análises
├── stats/             # Estatísticas e metadados dos modelos
└── README.md          # Este arquivo
```

## 📋 Datasets Disponíveis

### 🎯 **Datasets de Clustering**

#### `clustering_dataset_ml_ready.csv`
- **Descrição**: Dataset principal para modelos de clustering
- **Registros**: 220 turmas
- **Features**:
  - `turma_id`: ID numérico da turma
  - `total_leads`: Volume de leads únicos por turma
  - `taxa_conversao`: Taxa de conversão de lead para implementação finalizada (%)
  - `volume_implementacoes`: Número total de implementações por turma
  - `total_registros`: Volume total de registros por turma
  - `receita_por_lead`: Receita total dividida pelo número de leads únicos (R$)

#### `turma_id_mapping.csv`
- **Descrição**: Mapeamento entre IDs numéricos e nomes das turmas
- **Registros**: 220 mapeamentos
- **Colunas**:
  - `turma_id`: ID numérico único
  - `turma_nome`: Nome original da turma

## 📊 Estatísticas e Metadados

### `clustering_scaler_stats.json`
- **Descrição**: Parâmetros do StandardScaler para normalização
- **Método**: Standard Scaling
- **Features**: 5 features numéricas
- **Uso**: Normalização de novos dados para predição

### `clustering_stats_basic.json`
- **Descrição**: Estatísticas básicas do modelo de clustering
- **Informações**: Métricas de qualidade, número de clusters, etc.

## 🤖 Modelos de ML

### Algoritmos Utilizados
1. **K-means Clustering**
   - Implementação própria otimizada
   - Número ótimo de clusters: 2-6 (determinado automaticamente)
   - Features normalizadas com StandardScaler

2. **Segmentação Avançada**
   - Múltiplos tipos de segmentação disponíveis
   - Análise de silhueta para qualidade dos clusters

## 🔧 Serviços que Utilizam os Datasets

### Principais Serviços:
- `FinalClusteringService`: Usa dataset principal para clustering
- `TurmaClusteringService`: Aplica modelo pré-treinado
- `DynamicClusteringService`: Orquestra análises em tempo real
- `ClusteringDatasetService`: Cria e valida datasets

### APIs Relacionadas:
- `/api/cluster`: Clustering básico
- `/api/professional-cluster`: Análise profissional
- `/api/turma-clustering/*`: Clustering específico por turma

## 📈 Qualidade dos Dados

### ✅ **Pontos Fortes:**
- 220 turmas processadas
- Dados numéricos limpos e validados
- Mapeamento ID ↔ Nome funcional
- Normalização adequada para ML

### ⚠️ **Pontos de Atenção:**
- Taxa de conversão zerada em algumas turmas (verificar dados fonte)
- Variação alta na receita por lead
- Algumas turmas com volume baixo de dados

## 🚀 Como Usar

### 1. Carregar Dataset Principal:
```python
import pandas as pd
dataset = pd.read_csv('data/ML/datasets/clustering_dataset_ml_ready.csv')
```

### 2. Aplicar Normalização:
```python
import json
from sklearn.preprocessing import StandardScaler

# Carregar parâmetros do scaler
with open('data/ML/stats/clustering_scaler_stats.json', 'r') as f:
    scaler_params = json.load(f)

# Aplicar normalização
scaler = StandardScaler()
scaler.mean_ = scaler_params['scaler_params']['mean_']
scaler.scale_ = scaler_params['scaler_params']['scale_']
```

### 3. Obter Mapeamento de Turmas:
```python
mapping = pd.read_csv('data/ML/datasets/turma_id_mapping.csv')
turma_names = dict(zip(mapping['turma_id'], mapping['turma_nome']))
```

## 🔄 Atualização dos Datasets

### Processo Recomendado:
1. Executar `ClusteringDatasetService.create_clustering_dataset()`
2. Validar qualidade dos dados
3. Salvar novo dataset com timestamp
4. Atualizar estatísticas do scaler
5. Re-treinar modelos se necessário

### Frequência Sugerida:
- **Semanal**: Para dados em produção
- **Mensal**: Para análises estratégicas
- **Sob demanda**: Para novos insights

## 📞 Suporte

Para questões sobre os datasets ou modelos de ML:
- Verificar logs em `app/services/`
- Executar scripts de debug em `/debug_*.py`
- Consultar documentação dos serviços

---

**Última atualização**: Dezembro 2024  
**Versão**: 1.0  
**Responsável**: Sistema de ML DataHub Amigo One
