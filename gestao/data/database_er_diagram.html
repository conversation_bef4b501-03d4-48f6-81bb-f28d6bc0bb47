<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagrama ER - DataHub Amigo One - Business Admin Database</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
            color: #374151;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1f2937;
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
            color: #1f2937;
        }

        .header p {
            margin: 0.5rem 0 0 0;
            font-size: 1.1rem;
            color: #6b7280;
        }

        .content {
            padding: 2rem;
            background: #f9fafb;
        }

        .info-section {
            background: white;
            border-left: 4px solid #93c5fd;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-radius: 0 8px 8px 0;
            border: 1px solid #e5e7eb;
        }

        .info-section h2 {
            margin: 0 0 1rem 0;
            color: #374151;
            font-size: 1.25rem;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .info-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1.5rem;
        }

        .info-card h3 {
            margin: 0 0 1rem 0;
            color: #374151;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .info-card ul {
            margin: 0;
            padding-left: 1.5rem;
        }

        .info-card li {
            margin-bottom: 0.5rem;
            color: #6b7280;
        }

        .diagram-container {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 2rem;
            margin-top: 2rem;
            overflow-x: auto;
        }

        .mermaid {
            text-align: center;
        }

        .footer {
            background: #f3f4f6;
            color: #6b7280;
            padding: 1.5rem;
            text-align: center;
            font-size: 0.9rem;
            border-top: 1px solid #e5e7eb;
        }

        .badge {
            display: inline-block;
            background: #93c5fd;
            color: #1f2937;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-right: 0.5rem;
        }

        .badge.admin { background: #d1d5db; color: #374151; }
        .badge.user { background: #bfdbfe; color: #1f2937; }
        .badge.system { background: #e5e7eb; color: #374151; }

        @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .content { padding: 1rem; }
            .info-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Diagrama ER - Business Admin Database</h1>
            <p>DataHub Amigo One - Estrutura do Banco de Dados de Administração</p>
        </div>

        <div class="content">
            <div class="info-section">
                <h2>🗄️ Visão Geral do Banco de Dados</h2>
                <p>O banco <strong>business_admin.db</strong> é responsável por gerenciar toda a infraestrutura de autenticação, autorização, auditoria e monitoramento do sistema DataHub Amigo One. Este banco SQLite contém 5 tabelas principais que trabalham em conjunto para garantir a segurança e rastreabilidade do sistema.</p>
            </div>

            <div class="info-grid">
                <div class="info-card">
                    <h3><span class="badge user">CORE</span>Tabela: users</h3>
                    <ul>
                        <li><strong>Propósito:</strong> Gerenciamento de usuários do sistema</li>
                        <li><strong>Registros:</strong> 6 usuários ativos</li>
                        <li><strong>Chave Primária:</strong> id (INTEGER)</li>
                        <li><strong>Campos Únicos:</strong> username, email</li>
                        <li><strong>Segurança:</strong> Senhas com hash SHA-256</li>
                    </ul>
                </div>

                <div class="info-card">
                    <h3><span class="badge admin">SECURITY</span>Tabela: permissions</h3>
                    <ul>
                        <li><strong>Propósito:</strong> Controle de permissões granular</li>
                        <li><strong>Estrutura:</strong> Baseada em chaves de permissão</li>
                        <li><strong>Categorias:</strong> Por domínio e funcionalidade</li>
                        <li><strong>Admin Only:</strong> Permissões exclusivas para admins</li>
                    </ul>
                </div>

                <div class="info-card">
                    <h3><span class="badge system">AUDIT</span>Tabela: activity_logs</h3>
                    <ul>
                        <li><strong>Propósito:</strong> Auditoria de ações dos usuários</li>
                        <li><strong>Rastreamento:</strong> IP, User-Agent, Timestamp</li>
                        <li><strong>Metadados:</strong> JSON com informações adicionais</li>
                        <li><strong>Domínio:</strong> Separação por contexto de negócio</li>
                    </ul>
                </div>

                <div class="info-card">
                    <h3><span class="badge user">SESSION</span>Tabela: user_sessions</h3>
                    <ul>
                        <li><strong>Propósito:</strong> Gerenciamento de sessões ativas</li>
                        <li><strong>Segurança:</strong> Session IDs únicos</li>
                        <li><strong>Monitoramento:</strong> Última atividade</li>
                        <li><strong>Controle:</strong> Ativação/desativação de sessões</li>
                    </ul>
                </div>

                <div class="info-card">
                    <h3><span class="badge system">MONITOR</span>Tabela: system_logs</h3>
                    <ul>
                        <li><strong>Propósito:</strong> Logs gerais do sistema</li>
                        <li><strong>Escopo:</strong> Eventos de sistema e aplicação</li>
                        <li><strong>Flexibilidade:</strong> User_id opcional</li>
                        <li><strong>Metadados:</strong> Informações contextuais em JSON</li>
                    </ul>
                </div>
            </div>

            <div class="diagram-container">
                <div class="mermaid">
erDiagram
    users {
        INTEGER id PK "Primary Key, Auto Increment"
        TEXT username UK "Unique, Not Null"
        TEXT email UK "Unique, Not Null"
        TEXT password_hash "SHA-256 Hash, Not Null"
        TEXT role "Default: user"
        TEXT permissions "JSON Array, Default: []"
        TEXT domain "Business Domain, Not Null"
        BOOLEAN is_active "Default: 1"
        TIMESTAMP created_at "Default: CURRENT_TIMESTAMP"
        TIMESTAMP last_login "Nullable"
        TEXT metadata "JSON Object, Default: {}"
    }

    permissions {
        INTEGER id PK "Primary Key, Auto Increment"
        TEXT permission_key UK "Unique Permission Identifier"
        TEXT permission_name "Human Readable Name"
        TEXT description "Permission Description"
        TEXT domain "Business Domain"
        TEXT category "Permission Category"
        BOOLEAN is_admin_only "Default: 0"
    }

    activity_logs {
        INTEGER id PK "Primary Key, Auto Increment"
        TEXT user_id "Foreign Key Reference"
        TEXT action "Action Performed"
        TEXT description "Action Description"
        TEXT page "Page/Route Accessed"
        TEXT ip_address "Client IP Address"
        TEXT user_agent "Browser User Agent"
        TEXT domain "Business Domain"
        TEXT metadata "JSON Metadata"
        TIMESTAMP timestamp "Default: CURRENT_TIMESTAMP"
    }

    user_sessions {
        INTEGER id PK "Primary Key, Auto Increment"
        TEXT user_id "Foreign Key Reference"
        TEXT session_id UK "Unique Session Identifier"
        TEXT ip_address "Session IP Address"
        TEXT user_agent "Session User Agent"
        TIMESTAMP created_at "Default: CURRENT_TIMESTAMP"
        TIMESTAMP last_activity "Default: CURRENT_TIMESTAMP"
        BOOLEAN is_active "Default: 1"
    }

    system_logs {
        INTEGER id PK "Primary Key, Auto Increment"
        TEXT user_id "Optional User Reference"
        TEXT action "System Action"
        TEXT description "Action Description"
        TEXT page "Page/Route"
        TEXT ip_address "Client IP"
        TEXT user_agent "Browser Info"
        TEXT metadata "JSON Metadata"
        TIMESTAMP timestamp "Default: CURRENT_TIMESTAMP"
    }

    %% Relationships
    users ||--o{ activity_logs : "generates"
    users ||--o{ user_sessions : "has"
    users ||--o{ system_logs : "may_generate"
    users }o--o{ permissions : "has_many_through_json"
                </div>
            </div>
        </div>

            <div class="info-section">
                <h2>🔗 Relacionamentos e Regras de Negócio</h2>
                <div class="info-grid">
                    <div class="info-card">
                        <h3>🔐 Autenticação e Autorização</h3>
                        <ul>
                            <li><strong>users → activity_logs:</strong> Um usuário pode gerar múltiplos logs de atividade</li>
                            <li><strong>users → user_sessions:</strong> Um usuário pode ter múltiplas sessões ativas</li>
                            <li><strong>users ↔ permissions:</strong> Relacionamento many-to-many via JSON array</li>
                            <li><strong>Segurança:</strong> Senhas nunca armazenadas em texto plano</li>
                        </ul>
                    </div>

                    <div class="info-card">
                        <h3>📊 Auditoria e Monitoramento</h3>
                        <ul>
                            <li><strong>Rastreabilidade:</strong> Todas as ações são logadas com timestamp</li>
                            <li><strong>Metadados:</strong> Informações contextuais em formato JSON</li>
                            <li><strong>IP Tracking:</strong> Endereços IP registrados para segurança</li>
                            <li><strong>User Agent:</strong> Informações do navegador para análise</li>
                        </ul>
                    </div>

                    <div class="info-card">
                        <h3>⚙️ Configurações Técnicas</h3>
                        <ul>
                            <li><strong>Banco:</strong> SQLite 3.x</li>
                            <li><strong>Encoding:</strong> UTF-8</li>
                            <li><strong>Timezone:</strong> UTC para timestamps</li>
                            <li><strong>Backup:</strong> Recomendado backup diário</li>
                        </ul>
                    </div>

                    <div class="info-card">
                        <h3>👥 Usuários Atuais</h3>
                        <ul>
                            <li><strong>admin:</strong> Administrador principal</li>
                            <li><strong>TTK:</strong> Administrador técnico</li>
                            <li><strong>bruno@abreu:</strong> Administrador de negócio</li>
                            <li><strong>bruno@bruno:</strong> Administrador de desenvolvimento</li>
                            <li><strong>bruno.abreu:</strong> Usuário padrão</li>
                            <li><strong>bruno:</strong> Usuário de teste</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="info-section">
                <h2>📈 Estatísticas do Banco de Dados</h2>
                <div class="info-grid">
                    <div class="info-card">
                        <h3>📊 Contadores de Registros</h3>
                        <ul>
                            <li><strong>Usuários:</strong> 6 registros ativos</li>
                            <li><strong>Roles:</strong> 2 tipos (admin, user)</li>
                            <li><strong>Domínios:</strong> 1 domínio (business)</li>
                            <li><strong>Sessões Ativas:</strong> Variável por uso</li>
                        </ul>
                    </div>

                    <div class="info-card">
                        <h3>🔍 Consultas SQL Úteis</h3>
                        <ul>
                            <li><code>SELECT COUNT(*) FROM users WHERE is_active = 1;</code></li>
                            <li><code>SELECT username, last_login FROM users ORDER BY last_login DESC;</code></li>
                            <li><code>SELECT action, COUNT(*) FROM activity_logs GROUP BY action;</code></li>
                            <li><code>SELECT COUNT(*) FROM user_sessions WHERE is_active = 1;</code></li>
                        </ul>
                    </div>

                    <div class="info-card">
                        <h3>🛡️ Políticas de Segurança</h3>
                        <ul>
                            <li><strong>Senhas:</strong> Hash SHA-256 obrigatório</li>
                            <li><strong>Sessões:</strong> Timeout automático</li>
                            <li><strong>Logs:</strong> Retenção de 90 dias</li>
                            <li><strong>Permissões:</strong> Princípio do menor privilégio</li>
                        </ul>
                    </div>

                    <div class="info-card">
                        <h3>🔧 Manutenção Recomendada</h3>
                        <ul>
                            <li><strong>Backup:</strong> Diário às 02:00 UTC</li>
                            <li><strong>Limpeza de Logs:</strong> Mensal</li>
                            <li><strong>Análise de Sessões:</strong> Semanal</li>
                            <li><strong>Auditoria de Permissões:</strong> Trimestral</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p><strong>DataHub Amigo One</strong> | Diagrama ER gerado automaticamente | Banco: business_admin.db</p>
            <p>Última atualização: <span id="current-date"></span> | Versão: 1.0.0</p>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            theme: 'default',
            themeVariables: {
                primaryColor: '#93c5fd',
                primaryTextColor: '#374151',
                primaryBorderColor: '#93c5fd',
                lineColor: '#9ca3af',
                secondaryColor: '#f9fafb',
                tertiaryColor: '#f3f4f6',
                background: '#ffffff',
                mainBkg: '#ffffff',
                secondBkg: '#f9fafb',
                tertiaryBkg: '#f3f4f6'
            },
            er: {
                diagramPadding: 20,
                layoutDirection: 'TB',
                minEntityWidth: 100,
                minEntityHeight: 75,
                entityPadding: 15,
                stroke: '#93c5fd',
                fill: '#f9fafb',
                fontSize: 12
            }
        });

        // Set current date
        document.getElementById('current-date').textContent = new Date().toLocaleDateString('pt-BR');
    </script>
</body>
</html>
