{"admin": {"user_id": "admin", "username": "admin", "email": "<EMAIL>", "role": "admin", "permissions": ["dashboard.view", "leads.view", "leads.edit", "opportunities.view", "opportunities.edit", "implementations.view", "implementations.edit", "universities.view", "universities.edit", "responsibles.view", "responsibles.edit", "classes.view", "classes.edit", "coupons.view", "coupons.edit", "conversion.view", "subscriptions.view", "data_quality.view", "business_rules.view", "users.manage"], "domain": "business", "created_at": "2025-05-24T21:12:56.824311", "last_login": null, "is_active": true, "metadata": {"is_default": true, "password_hash": "$2b$12$r6a3jUlNG3lucQin3quXfuQ5vd.fhBT69BKImoKrAesP0y5lT.gMi"}}, "TTK": {"user_id": "TTK", "username": "TTK", "email": "<EMAIL>", "role": "admin", "permissions": ["dashboard.view", "leads.view", "leads.edit", "opportunities.view", "opportunities.edit", "implementations.view", "implementations.edit", "universities.view", "universities.edit", "responsibles.view", "responsibles.edit", "classes.view", "classes.edit", "coupons.view", "coupons.edit", "conversion.view", "subscriptions.view", "data_quality.view", "business_rules.view", "users.manage"], "domain": "business", "created_at": "2025-05-24T21:15:42.306726", "last_login": null, "is_active": true, "metadata": {"is_default": true, "description": "TTK Administrator Account", "password_hash": "$2b$12$VP7brh/HE..SFoQflwuHVeqaOlyQTq8w7kfzFIlPbjXbE.oUvU/jm"}}, "bruno@abreu": {"user_id": "bruno@abreu", "username": "bruno@abreu", "email": "<EMAIL>", "role": "admin", "permissions": ["dashboard.view", "leads.view", "leads.edit", "opportunities.view", "opportunities.edit", "implementations.view", "implementations.edit", "universities.view", "universities.edit", "responsibles.view", "responsibles.edit", "classes.view", "classes.edit", "coupons.view", "coupons.edit", "conversion.view", "subscriptions.view", "data_quality.view", "business_rules.view", "users.manage"], "domain": "business", "created_at": "2025-01-01T12:00:00.000000", "last_login": "2025-05-25T02:01:29.290479", "is_active": true, "metadata": {"is_default": true, "description": "<PERSON> - Global Administrator Account", "password_hash": "$2b$12$lYvNE/E9dusu711TYLn2buh59023AhSlXoLoIsPbE/Folm4L0mHhS"}}, "sergio": {"user_id": "sergio", "username": "sergio", "email": "<EMAIL>", "role": "user", "permissions": ["dashboard.view", "leads.view", "opportunities.view", "implementations.view", "universities.view", "data_quality.view"], "domain": "business", "created_at": "2024-01-15T10:00:00.000000", "last_login": "2024-12-19T08:45:00.000000", "is_active": true, "metadata": {"description": "Sergio <PERSON> <PERSON><PERSON><PERSON><PERSON>", "department": "Data Engineering", "password_hash": "$2b$12$example_hash_sergio"}}, "bruno": {"user_id": "bruno", "username": "bruno", "email": "<EMAIL>", "role": "user", "permissions": ["dashboard.view", "leads.view", "opportunities.view", "implementations.view", "universities.view", "responsibles.view", "classes.view", "coupons.view", "conversion.view", "subscriptions.view", "business_rules.view"], "domain": "business", "created_at": "2024-01-15T10:00:00.000000", "last_login": "2024-12-19T09:00:00.000000", "is_active": true, "metadata": {"description": "Bruno - Ana<PERSON><PERSON> de Negócios", "department": "Business Analysis", "password_hash": "$2b$12$example_hash_bruno"}}, "ana.silva": {"user_id": "ana.silva", "username": "ana.silva", "email": "<EMAIL>", "role": "user", "permissions": ["dashboard.view", "leads.view", "opportunities.view", "universities.view"], "domain": "business", "created_at": "2024-02-01T14:30:00.000000", "last_login": "2024-12-18T16:20:00.000000", "is_active": true, "metadata": {"description": "<PERSON> - <PERSON><PERSON><PERSON>", "department": "Sales", "password_hash": "$2b$12$example_hash_ana"}}, "carlos.santos": {"user_id": "carlos.santos", "username": "carlos.santos", "email": "<EMAIL>", "role": "user", "permissions": ["dashboard.view", "implementations.view", "universities.view", "responsibles.view", "classes.view"], "domain": "business", "created_at": "2024-03-10T09:15:00.000000", "last_login": "2024-12-19T07:30:00.000000", "is_active": true, "metadata": {"description": "<PERSON> Gerente de Implementação", "department": "Implementation", "password_hash": "$2b$12$example_hash_carlos"}}, "maria.costa": {"user_id": "maria.costa", "username": "maria.costa", "email": "<EMAIL>", "role": "user", "permissions": ["dashboard.view", "data_quality.view", "business_rules.view"], "domain": "business", "created_at": "2024-04-05T11:45:00.000000", "last_login": "2024-12-17T15:10:00.000000", "is_active": false, "metadata": {"description": "Maria Costa - Analista de Qualidade (Inativa)", "department": "Quality Assurance", "password_hash": "$2b$12$example_hash_maria", "deactivated_reason": "Transferida para outro projeto"}}, "test_user_business": {"user_id": "test_user_business", "username": "test_user_business", "email": "<EMAIL>", "role": "user", "permissions": ["dashboard.view", "leads.view"], "domain": "business", "created_at": "2025-05-25T01:41:23.888792", "last_login": null, "is_active": true, "metadata": {"password_hash": "$2b$12$.3IrNevqeok9oEK9qWfIZuQKygpsh4U7aIG6Pq9orr1aZjbrgaJbO", "created_by": "admin"}}}