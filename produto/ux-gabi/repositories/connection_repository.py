"""
Connection Repository
Data access layer for connection/network operations
"""
from typing import List, Dict, Any, Optional, Set
from datetime import date, datetime
import logging

from .base_repository import BaseRepository
from models.connection_models import Connection, NetworkMetrics, ConnectionAnalytics

logger = logging.getLogger(__name__)

class ConnectionRepository(BaseRepository[Connection]):
    """Repository for connection data"""
    
    def __init__(self):
        super().__init__()
        self.csv_filename = 'base_dados_conexoes.csv'
    
    def find_all(self) -> List[Connection]:
        """Find all connection records"""
        try:
            raw_data = self._load_csv_data(self.csv_filename)
            return [self._dict_to_model(record) for record in raw_data]
        except Exception as e:
            logger.error(f"Error finding all connection records: {e}")
            return []
    
    def find_by_id(self, record_id: str) -> Optional[Connection]:
        """Find connection record by ID (not applicable for connections)"""
        # Connections don't have a single ID, they're identified by source+target
        return None
    
    def find_by_user(self, user_id: int) -> List[Connection]:
        """Find all connections for a user"""
        try:
            all_connections = self.find_all()
            user_connections = []
            
            for connection in all_connections:
                if (connection.source_profile_id == user_id or 
                    connection.target_profile_id == user_id):
                    user_connections.append(connection)
            
            return user_connections
        except Exception as e:
            logger.error(f"Error finding connections for user {user_id}: {e}")
            return []
    
    def find_by_status(self, status: str) -> List[Connection]:
        """Find connections by status"""
        try:
            all_connections = self.find_all()
            return [conn for conn in all_connections if conn.status == status]
        except Exception as e:
            logger.error(f"Error finding connections by status {status}: {e}")
            return []
    
    def get_accepted_connections(self) -> List[Connection]:
        """Get all accepted connections"""
        return self.find_by_status('ACCEPTED')
    
    def get_pending_connections(self) -> List[Connection]:
        """Get all pending connections"""
        return self.find_by_status('PENDING')
    
    def get_refused_connections(self) -> List[Connection]:
        """Get all refused connections"""
        return self.find_by_status('REFUSED')
    
    def get_unique_users(self) -> Set[int]:
        """Get all unique user IDs"""
        try:
            all_connections = self.find_all()
            users = set()
            
            for connection in all_connections:
                if connection.source_profile_id:
                    users.add(connection.source_profile_id)
                if connection.target_profile_id:
                    users.add(connection.target_profile_id)
            
            return users
        except Exception as e:
            logger.error(f"Error getting unique users: {e}")
            return set()
    
    def get_active_users(self) -> Set[int]:
        """Get users with at least one accepted connection"""
        try:
            accepted_connections = self.get_accepted_connections()
            active_users = set()
            
            for connection in accepted_connections:
                if connection.source_profile_id:
                    active_users.add(connection.source_profile_id)
                if connection.target_profile_id:
                    active_users.add(connection.target_profile_id)
            
            return active_users
        except Exception as e:
            logger.error(f"Error getting active users: {e}")
            return set()
    
    def get_user_connection_count(self, user_id: int, status: str = None) -> int:
        """Get connection count for a user"""
        try:
            user_connections = self.find_by_user(user_id)
            
            if status:
                user_connections = [conn for conn in user_connections if conn.status == status]
            
            return len(user_connections)
        except Exception as e:
            logger.error(f"Error getting connection count for user {user_id}: {e}")
            return 0
    
    def get_top_connectors(self, limit: int = 10, status: str = 'ACCEPTED') -> List[Dict[str, Any]]:
        """Get top connectors by connection count"""
        try:
            user_counts = {}
            connections = self.find_by_status(status) if status else self.find_all()
            
            for connection in connections:
                if connection.source_profile_id:
                    user_counts[connection.source_profile_id] = user_counts.get(connection.source_profile_id, 0) + 1
                if connection.target_profile_id:
                    user_counts[connection.target_profile_id] = user_counts.get(connection.target_profile_id, 0) + 1
            
            # Sort by connection count
            sorted_users = sorted(user_counts.items(), key=lambda x: x[1], reverse=True)
            
            return [
                {'user_id': user_id, 'connection_count': count}
                for user_id, count in sorted_users[:limit]
            ]
        except Exception as e:
            logger.error(f"Error getting top connectors: {e}")
            return []
    
    def calculate_network_density(self) -> float:
        """Calculate network density"""
        try:
            unique_users = self.get_unique_users()
            accepted_connections = self.get_accepted_connections()
            
            total_users = len(unique_users)
            if total_users <= 1:
                return 0.0
            
            max_possible_connections = total_users * (total_users - 1) / 2
            actual_connections = len(accepted_connections)
            
            return (actual_connections / max_possible_connections) * 100
        except Exception as e:
            logger.error(f"Error calculating network density: {e}")
            return 0.0
    
    def get_network_metrics(self) -> NetworkMetrics:
        """Get comprehensive network metrics"""
        try:
            all_connections = self.find_all()
            accepted_connections = self.get_accepted_connections()
            pending_connections = self.get_pending_connections()
            refused_connections = self.get_refused_connections()
            
            unique_users = self.get_unique_users()
            active_users = self.get_active_users()
            
            metrics = NetworkMetrics(
                total_connections=len(all_connections),
                accepted_connections=len(accepted_connections),
                pending_connections=len(pending_connections),
                refused_connections=len(refused_connections),
                total_users=len(unique_users),
                active_users=len(active_users),
                pending_only_users=len(unique_users) - len(active_users)
            )
            
            metrics.calculate_acceptance_rate()
            metrics.calculate_density()
            
            if metrics.total_users > 0:
                metrics.avg_connections_per_user = metrics.accepted_connections / metrics.total_users
            
            return metrics
        except Exception as e:
            logger.error(f"Error getting network metrics: {e}")
            return NetworkMetrics()
    
    def _dict_to_model(self, data: Dict[str, Any]) -> Connection:
        """Convert dictionary to Connection model"""
        try:
            # Handle date conversion
            created_date = None
            if 'created_date' in data and data['created_date']:
                try:
                    created_date = datetime.strptime(data['created_date'], '%Y-%m-%d').date()
                except ValueError:
                    try:
                        created_date = datetime.strptime(data['created_date'], '%d/%m/%Y').date()
                    except ValueError:
                        logger.warning(f"Could not parse date: {data['created_date']}")
            
            return Connection(
                source_profile_id=int(data['source_profile_id']) if data.get('source_profile_id') else None,
                target_profile_id=int(data['target_profile_id']) if data.get('target_profile_id') else None,
                status=data.get('status'),
                created_date=created_date
            )
        except Exception as e:
            logger.error(f"Error converting dict to Connection: {e}")
            return Connection()
    
    def get_raw_data(self) -> List[Dict[str, Any]]:
        """Get raw data for backward compatibility"""
        return self._load_csv_data(self.csv_filename)
