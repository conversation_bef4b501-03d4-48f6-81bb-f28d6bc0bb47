"""
User Repository
Data access layer for user operations
"""
from typing import List, Dict, Any, Optional
from datetime import date, datetime
import logging

from .base_repository import BaseRepository
from models.user_models import User, UserStats, UserAnalytics

logger = logging.getLogger(__name__)

class UserRepository(BaseRepository[User]):
    """Repository for user data"""
    
    def __init__(self):
        super().__init__()
        self.csv_filename = 'base_produto.csv'
    
    def find_all(self) -> List[User]:
        """Find all user records"""
        try:
            raw_data = self._load_csv_data(self.csv_filename)
            return [self._dict_to_model(record) for record in raw_data]
        except Exception as e:
            logger.error(f"Error finding all user records: {e}")
            return []
    
    def find_by_id(self, user_id: str) -> Optional[User]:
        """Find user by ID"""
        try:
            raw_data = self._load_csv_data(self.csv_filename)
            for record in raw_data:
                if record.get('user_id') == user_id:
                    return self._dict_to_model(record)
            return None
        except Exception as e:
            logger.error(f"Error finding user by ID {user_id}: {e}")
            return None
    
    def find_by_email(self, email: str) -> Optional[User]:
        """Find user by email"""
        try:
            all_users = self.find_all()
            for user in all_users:
                if user.email == email:
                    return user
            return None
        except Exception as e:
            logger.error(f"Error finding user by email {email}: {e}")
            return None
    
    def find_by_specialty(self, specialty: str) -> List[User]:
        """Find users by specialty"""
        try:
            all_users = self.find_all()
            return [user for user in all_users if user.specialty == specialty]
        except Exception as e:
            logger.error(f"Error finding users by specialty {specialty}: {e}")
            return []
    
    def find_by_state(self, state: str) -> List[User]:
        """Find users by state"""
        try:
            all_users = self.find_all()
            return [user for user in all_users if user.state == state]
        except Exception as e:
            logger.error(f"Error finding users by state {state}: {e}")
            return []
    
    def find_active_users(self) -> List[User]:
        """Find all active users"""
        try:
            all_users = self.find_all()
            return [user for user in all_users if user.is_active()]
        except Exception as e:
            logger.error(f"Error finding active users: {e}")
            return []
    
    def find_premium_users(self) -> List[User]:
        """Find all premium users"""
        try:
            all_users = self.find_all()
            return [user for user in all_users if user.is_premium()]
        except Exception as e:
            logger.error(f"Error finding premium users: {e}")
            return []
    
    def get_specialties(self) -> List[str]:
        """Get all unique specialties"""
        try:
            all_users = self.find_all()
            specialties = set()
            for user in all_users:
                if user.specialty:
                    specialties.add(user.specialty)
            return sorted(list(specialties))
        except Exception as e:
            logger.error(f"Error getting specialties: {e}")
            return []
    
    def get_states(self) -> List[str]:
        """Get all unique states"""
        try:
            all_users = self.find_all()
            states = set()
            for user in all_users:
                if user.state:
                    states.add(user.state)
            return sorted(list(states))
        except Exception as e:
            logger.error(f"Error getting states: {e}")
            return []
    
    def get_user_analytics(self) -> UserAnalytics:
        """Get comprehensive user analytics"""
        try:
            all_users = self.find_all()
            active_users = self.find_active_users()
            premium_users = self.find_premium_users()
            
            # Count by device
            device_counts = {}
            for user in all_users:
                device = user.device_type or 'Unknown'
                device_counts[device] = device_counts.get(device, 0) + 1
            
            # Count by specialty
            specialty_counts = {}
            for user in all_users:
                specialty = user.specialty or 'Unknown'
                specialty_counts[specialty] = specialty_counts.get(specialty, 0) + 1
            
            # Count by state
            state_counts = {}
            for user in all_users:
                state = user.state or 'Unknown'
                state_counts[state] = state_counts.get(state, 0) + 1
            
            # Count by age group
            age_counts = {}
            for user in all_users:
                age_group = user.get_age_group()
                age_counts[age_group] = age_counts.get(age_group, 0) + 1
            
            return UserAnalytics(
                total_users=len(all_users),
                active_users=len(active_users),
                premium_users=len(premium_users),
                free_users=len(all_users) - len(premium_users),
                users_by_device=device_counts,
                users_by_specialty=specialty_counts,
                users_by_state=state_counts,
                age_distribution=age_counts
            )
        except Exception as e:
            logger.error(f"Error getting user analytics: {e}")
            return UserAnalytics()
    
    def _dict_to_model(self, data: Dict[str, Any]) -> User:
        """Convert dictionary to User model"""
        try:
            # Handle date conversions
            registration_date = None
            if 'data_cadastro' in data and data['data_cadastro']:
                try:
                    registration_date = datetime.strptime(data['data_cadastro'], '%Y-%m-%d').date()
                except ValueError:
                    try:
                        registration_date = datetime.strptime(data['data_cadastro'], '%d/%m/%Y').date()
                    except ValueError:
                        logger.warning(f"Could not parse registration date: {data['data_cadastro']}")
            
            last_access_date = None
            if 'data_ultimo_acesso' in data and data['data_ultimo_acesso']:
                try:
                    last_access_date = datetime.strptime(data['data_ultimo_acesso'], '%Y-%m-%d').date()
                except ValueError:
                    try:
                        last_access_date = datetime.strptime(data['data_ultimo_acesso'], '%d/%m/%Y').date()
                    except ValueError:
                        logger.warning(f"Could not parse last access date: {data['data_ultimo_acesso']}")
            
            # Handle age conversion
            age = None
            if 'idade' in data and data['idade']:
                try:
                    age = int(data['idade'])
                except (ValueError, TypeError):
                    logger.warning(f"Could not parse age: {data['idade']}")
            
            return User(
                user_id=data.get('user_id'),
                username=data.get('nome_usuario'),
                email=data.get('email'),
                full_name=data.get('nome_usuario'),  # Using nome_usuario as full_name
                specialty=data.get('especialidade_medica'),
                city=data.get('endereco_cidade'),
                state=data.get('endereco_estado'),
                subscription_type=data.get('tipo_plano'),
                status=data.get('status_usuario'),
                device_type=data.get('dispositivo_principal'),
                registration_date=registration_date,
                last_access_date=last_access_date,
                age=age,
                gender=data.get('genero')
            )
        except Exception as e:
            logger.error(f"Error converting dict to User: {e}")
            return User()
    
    def get_raw_data(self) -> List[Dict[str, Any]]:
        """Get raw data for backward compatibility"""
        return self._load_csv_data(self.csv_filename)
