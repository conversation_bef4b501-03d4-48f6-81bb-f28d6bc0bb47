"""
Accounting Repository
Data access layer for accounting operations
"""
from typing import List, Dict, Any, Optional
from datetime import date, datetime
from decimal import Decimal
import logging

from .base_repository import BaseRepository
from models.accounting_models import AccountingRecord, FinancialSummary, AccountingAnalytics

logger = logging.getLogger(__name__)

class AccountingRepository(BaseRepository[AccountingRecord]):
    """Repository for accounting data"""
    
    def __init__(self):
        super().__init__()
        self.csv_filename = 'base_dados_contabilidade.csv'
    
    def find_all(self) -> List[AccountingRecord]:
        """Find all accounting records"""
        try:
            raw_data = self._load_csv_data(self.csv_filename)
            return [self._dict_to_model(record) for record in raw_data]
        except Exception as e:
            logger.error(f"Error finding all accounting records: {e}")
            return []
    
    def find_by_id(self, record_id: str) -> Optional[AccountingRecord]:
        """Find accounting record by ID"""
        try:
            raw_data = self._load_csv_data(self.csv_filename)
            for record in raw_data:
                if record.get('record_id') == record_id:
                    return self._dict_to_model(record)
            return None
        except Exception as e:
            logger.error(f"Error finding accounting record by ID {record_id}: {e}")
            return None
    
    def find_by_date_range(self, start_date: date, end_date: date) -> List[AccountingRecord]:
        """Find records within date range"""
        try:
            all_records = self.find_all()
            filtered_records = []
            
            for record in all_records:
                if record.date and start_date <= record.date <= end_date:
                    filtered_records.append(record)
            
            return filtered_records
        except Exception as e:
            logger.error(f"Error finding records by date range: {e}")
            return []
    
    def find_by_category(self, category: str) -> List[AccountingRecord]:
        """Find records by category"""
        try:
            all_records = self.find_all()
            return [record for record in all_records if record.category == category]
        except Exception as e:
            logger.error(f"Error finding records by category {category}: {e}")
            return []
    
    def find_by_type(self, record_type: str) -> List[AccountingRecord]:
        """Find records by type (revenue/expense)"""
        try:
            all_records = self.find_all()
            return [record for record in all_records if record.type == record_type]
        except Exception as e:
            logger.error(f"Error finding records by type {record_type}: {e}")
            return []
    
    def get_revenue_records(self) -> List[AccountingRecord]:
        """Get all revenue records"""
        return self.find_by_type('revenue')
    
    def get_expense_records(self) -> List[AccountingRecord]:
        """Get all expense records"""
        return self.find_by_type('expense')
    
    def get_categories(self) -> List[str]:
        """Get all unique categories"""
        try:
            all_records = self.find_all()
            categories = set()
            for record in all_records:
                if record.category:
                    categories.add(record.category)
            return sorted(list(categories))
        except Exception as e:
            logger.error(f"Error getting categories: {e}")
            return []
    
    def calculate_total_revenue(self) -> Decimal:
        """Calculate total revenue"""
        try:
            revenue_records = self.get_revenue_records()
            total = Decimal('0')
            for record in revenue_records:
                if record.amount:
                    total += record.amount
            return total
        except Exception as e:
            logger.error(f"Error calculating total revenue: {e}")
            return Decimal('0')
    
    def calculate_total_expenses(self) -> Decimal:
        """Calculate total expenses"""
        try:
            expense_records = self.get_expense_records()
            total = Decimal('0')
            for record in expense_records:
                if record.amount:
                    total += record.amount
            return total
        except Exception as e:
            logger.error(f"Error calculating total expenses: {e}")
            return Decimal('0')
    
    def get_financial_summary(self) -> FinancialSummary:
        """Get financial summary"""
        try:
            total_revenue = self.calculate_total_revenue()
            total_expenses = self.calculate_total_expenses()
            
            return FinancialSummary(
                total_revenue=total_revenue,
                total_expenses=total_expenses
            )
        except Exception as e:
            logger.error(f"Error getting financial summary: {e}")
            return FinancialSummary()
    
    def _dict_to_model(self, data: Dict[str, Any]) -> AccountingRecord:
        """Convert dictionary to AccountingRecord model"""
        try:
            # Handle date conversion
            record_date = None
            if 'date' in data and data['date']:
                try:
                    record_date = datetime.strptime(data['date'], '%Y-%m-%d').date()
                except ValueError:
                    try:
                        record_date = datetime.strptime(data['date'], '%d/%m/%Y').date()
                    except ValueError:
                        logger.warning(f"Could not parse date: {data['date']}")
            
            # Handle amount conversion
            amount = None
            if 'amount' in data and data['amount']:
                try:
                    amount = Decimal(str(data['amount']))
                except (ValueError, TypeError):
                    logger.warning(f"Could not parse amount: {data['amount']}")
            
            return AccountingRecord(
                record_id=data.get('record_id'),
                date=record_date,
                description=data.get('description'),
                category=data.get('category'),
                amount=amount,
                type=data.get('type'),
                status=data.get('status')
            )
        except Exception as e:
            logger.error(f"Error converting dict to AccountingRecord: {e}")
            return AccountingRecord()
    
    def get_raw_data(self) -> List[Dict[str, Any]]:
        """Get raw data for backward compatibility"""
        return self._load_csv_data(self.csv_filename)
