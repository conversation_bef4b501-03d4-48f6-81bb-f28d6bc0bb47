"""
Base Repository
Provides common data access functionality
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, TypeVar, Generic
import logging
import os
import csv
from pathlib import Path

logger = logging.getLogger(__name__)

T = TypeVar('T')

class BaseRepository(ABC, Generic[T]):
    """Base repository with common functionality"""
    
    def __init__(self):
        self.data_path = Path(__file__).parent.parent / 'data' / 'sources'
        
    def _load_csv_data(self, filename: str) -> List[Dict[str, Any]]:
        """Load data from CSV file"""
        try:
            csv_path = self.data_path / filename
            
            if not csv_path.exists():
                logger.warning(f"CSV file not found: {csv_path}")
                return []
            
            data = []
            with open(csv_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    data.append(dict(row))
            
            logger.info(f"Loaded {len(data)} records from {filename}")
            return data
            
        except Exception as e:
            logger.error(f"Error loading CSV data from {filename}: {e}")
            return []
    
    def _save_csv_data(self, filename: str, data: List[Dict[str, Any]]) -> bool:
        """Save data to CSV file"""
        try:
            csv_path = self.data_path / filename
            
            # Ensure directory exists
            csv_path.parent.mkdir(parents=True, exist_ok=True)
            
            if not data:
                logger.warning(f"No data to save to {filename}")
                return False
            
            # Get fieldnames from first record
            fieldnames = list(data[0].keys())
            
            with open(csv_path, 'w', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
            
            logger.info(f"Saved {len(data)} records to {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving CSV data to {filename}: {e}")
            return False
    
    def _filter_data(self, data: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Filter data based on criteria"""
        if not filters:
            return data
        
        filtered_data = []
        for record in data:
            match = True
            for key, value in filters.items():
                if key not in record or record[key] != value:
                    match = False
                    break
            if match:
                filtered_data.append(record)
        
        return filtered_data
    
    def _sort_data(self, data: List[Dict[str, Any]], sort_by: str, reverse: bool = False) -> List[Dict[str, Any]]:
        """Sort data by field"""
        try:
            return sorted(data, key=lambda x: x.get(sort_by, ''), reverse=reverse)
        except Exception as e:
            logger.warning(f"Error sorting data by {sort_by}: {e}")
            return data
    
    def _paginate_data(self, data: List[Dict[str, Any]], page: int = 1, per_page: int = 100) -> List[Dict[str, Any]]:
        """Paginate data"""
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        return data[start_idx:end_idx]
    
    @abstractmethod
    def find_all(self) -> List[T]:
        """Find all records"""
        pass
    
    @abstractmethod
    def find_by_id(self, record_id: str) -> Optional[T]:
        """Find record by ID"""
        pass
    
    def count(self) -> int:
        """Count total records"""
        return len(self.find_all())
    
    def exists(self, record_id: str) -> bool:
        """Check if record exists"""
        return self.find_by_id(record_id) is not None
