"""
Simple Database Service for Product Domain
Basic SQLite/PostgreSQL connection without complex adapters
"""

import logging
import sqlite3
import pandas as pd
from typing import Dict, List, Optional, Any
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class SimpleDatabaseService:
    """Simple database service for basic operations"""

    def __init__(self, db_path: Optional[str] = None):
        """Initialize database service"""
        if db_path is None:
            # Default to SQLite in data directory
            app_root = Path(__file__).parent
            db_path = os.path.join(app_root, 'data', 'product.db')
            
        self.db_path = db_path
        self.connection = None
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        try:
            self.connection = sqlite3.connect(db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row  # Enable dict-like access
            logger.info(f"Connected to SQLite database: {db_path}")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            self.connection = None

    def is_available(self) -> bool:
        """Check if database is available"""
        return self.connection is not None

    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """Execute a SELECT query and return results"""
        if not self.is_available():
            return []
            
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            return []

    def execute_update(self, query: str, params: tuple = ()) -> bool:
        """Execute an INSERT/UPDATE/DELETE query"""
        if not self.is_available():
            return False
            
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            self.connection.commit()
            return True
        except Exception as e:
            logger.error(f"Error executing update: {e}")
            return False

    def get_dataframe(self, query: str, params: tuple = ()) -> pd.DataFrame:
        """Execute query and return as pandas DataFrame"""
        if not self.is_available():
            return pd.DataFrame()
            
        try:
            return pd.read_sql_query(query, self.connection, params=params)
        except Exception as e:
            logger.error(f"Error getting DataFrame: {e}")
            return pd.DataFrame()

    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None

    def __del__(self):
        """Cleanup on destruction"""
        self.close()

class ProductDataService:
    """Simple data service for product domain"""
    
    def __init__(self):
        self.db = SimpleDatabaseService()
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get basic dashboard data"""
        if not self.db.is_available():
            return self._get_fallback_data()
            
        try:
            # Simple queries for basic metrics
            total_users = self.db.execute_query("SELECT COUNT(*) as count FROM users")
            total_sessions = self.db.execute_query("SELECT COUNT(*) as count FROM user_sessions")
            
            return {
                'kpis': {
                    'total_users': total_users[0]['count'] if total_users else 0,
                    'total_sessions': total_sessions[0]['count'] if total_sessions else 0,
                    'active_features': 0,
                    'total_revenue': 0.0
                },
                'users_by_status': {},
                'sessions_by_type': {},
                'feature_usage': {}
            }
        except Exception as e:
            logger.error(f"Error getting dashboard data: {e}")
            return self._get_fallback_data()
    
    def _get_fallback_data(self) -> Dict[str, Any]:
        """Fallback data when database is not available"""
        return {
            'kpis': {
                'total_users': 0,
                'total_sessions': 0,
                'active_features': 0,
                'total_revenue': 0.0
            },
            'users_by_status': {},
            'sessions_by_type': {},
            'feature_usage': {}
        }
