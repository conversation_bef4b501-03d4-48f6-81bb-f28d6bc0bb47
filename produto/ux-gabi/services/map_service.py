"""
Map Service for Product Domain
Handles map generation and visualization
"""
import folium
from folium import plugins
import branca.colormap as cm
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Union, Tuple

logger = logging.getLogger(__name__)

# Coordenadas aproximadas dos estados brasileiros
STATE_COORDINATES = {
    'SP': [-23.5505, -46.6333],  # São Paulo
    'RJ': [-22.9068, -43.1729],  # Rio de Janeiro
    'MG': [-19.9167, -43.9345],  # Belo Horizonte
    'RS': [-30.0346, -51.2177],  # Porto Alegre
    'PR': [-25.4284, -49.2733],  # Curitiba
    'BA': [-12.9714, -38.5014],  # Salvador
    'SC': [-27.5969, -48.5495],  # Florianópolis
    'PE': [-8.0476, -34.8770],   # Recife
    'CE': [-3.7319, -38.5267],   # Fortaleza
    'GO': [-16.6799, -49.2550],  # Goiânia
    'PA': [-1.4558, -48.4902],   # Belém
    'ES': [-20.3222, -40.3381],  # <PERSON>it<PERSON><PERSON>
    'PB': [-7.1195, -34.8450],   # <PERSON> Pessoa
    'AM': [-3.1190, -60.0217],   # Manaus
    'MA': [-2.5307, -44.3068],   # São Lu<PERSON>
    'RN': [-5.7945, -35.2120],   # Natal
    'MT': [-15.6014, -56.0979],  # Cuiab<PERSON>
    'MS': [-20.4428, -54.6464],  # Campo Grande
    'DF': [-15.7801, -47.9292],  # Brasília
    'AL': [-9.6498, -35.7089],   # Maceió
    'PI': [-5.0920, -42.8038],   # Teresina
    'SE': [-10.9472, -37.0731],  # Aracaju
    'RO': [-8.7619, -63.9039],   # Porto Velho
    'TO': [-10.2491, -48.3243],  # Palmas
    'AC': [-9.9754, -67.8249],   # Rio Branco
    'AP': [0.0356, -51.0705],    # Macapá
    'RR': [2.8200, -60.6714]     # Boa Vista
}

def generate_brazil_map(users_by_state: Dict[str, Any]) -> str:
    """Generate Brazil map with user distribution"""
    logger.info("Iniciando geração do mapa do Brasil")

    try:
        # Criar um mapa centralizado no Brasil
        m = folium.Map(
            location=[-15.7801, -47.9292],
            zoom_start=4,
            tiles='CartoDB positron',
            control_scale=True,
            prefer_canvas=True
        )

        # Encontrar o valor máximo para normalizar os dados
        max_value = max(users_by_state.values())

        # Criar uma escala de cores
        colormap = cm.LinearColormap(
            colors=['#E6F2FF', '#CCE5FF', '#99CCFF', '#66B2FF', '#3399FF', '#0080FF', '#0066CC'],
            index=[0, 0.1*max_value, 0.2*max_value, 0.4*max_value, 0.6*max_value, 0.8*max_value, max_value],
            vmin=0,
            vmax=max_value
        )

        # Adicionar a legenda
        colormap.caption = 'Número de Usuários'
        colormap.add_to(m)

        # Adicionar marcadores para cada estado
        for state, users in users_by_state.items():
            if state in STATE_COORDINATES and state != 'Outros':
                # Calcular o tamanho do círculo com base no número de usuários
                radius = 5 + (users / max_value) * 25

                # Calcular a cor com base no número de usuários
                color = colormap(users)

                # Adicionar um círculo para o estado
                folium.CircleMarker(
                    location=STATE_COORDINATES[state],
                    radius=radius,
                    color=None,
                    fill=True,
                    fill_color=color,
                    fill_opacity=0.7,
                    tooltip=f"<div style='font-size: 12px; font-weight: bold;'>{state}</div><div style='font-size: 14px;'>{users} usuários</div>"
                ).add_to(m)

                # Adicionar o nome do estado
                folium.Marker(
                    location=STATE_COORDINATES[state],
                    icon=folium.DivIcon(
                        icon_size=(0, 0),
                        html=f'<div style="font-size: 10pt; font-weight: bold; text-align: center;">{state}</div>'
                    )
                ).add_to(m)

        # Salvar o mapa como HTML usando get_root().render()
        map_html = m.get_root().render()

        # Limpar o HTML para remover mensagens de segurança do Jupyter
        if "Make this Notebook Trusted" in map_html:
            # Extrair apenas o iframe do mapa
            import re
            iframe_match = re.search(r'<iframe[^>]*>.*?</iframe>', map_html, re.DOTALL)
            if iframe_match:
                iframe_html = iframe_match.group(0)
                # Criar um HTML limpo apenas com o iframe
                map_html = f'''
                <div style="width:100%; height:100%;">
                    {iframe_html}
                </div>
                '''
            else:
                # Fallback: criar um mapa simples
                map_html = '''
                <div style="width:100%; height:100%; display:flex; align-items:center; justify-content:center; background:#f8f9fa; border:1px solid #dee2e6; border-radius:8px;">
                    <div style="text-align:center; color:#6c757d;">
                        <div style="font-size:48px; margin-bottom:16px;">🗺️</div>
                        <div style="font-size:16px; font-weight:500;">Mapa do Brasil</div>
                        <div style="font-size:14px; margin-top:8px;">Distribuição de usuários por estado</div>
                    </div>
                </div>
                '''

        logger.info(f"HTML do mapa gerado com sucesso: {len(map_html)} caracteres")
        return map_html
    except Exception as e:
        logger.error(f"Erro ao gerar mapa do Brasil: {str(e)}", exc_info=True)
        return f'<div class="alert alert-danger">Erro ao gerar mapa: {str(e)}</div>'

def generate_features_map(features_by_state: List[Any]) -> str:
    """Generate features usage map"""
    logger.info("Iniciando geração do mapa de features")

    try:
        # Criar um mapa centralizado no Brasil
        m = folium.Map(
            location=[-15.7801, -47.9292],
            zoom_start=4,
            tiles='CartoDB positron',
            control_scale=True,
            prefer_canvas=True
        )

        # Cores para cada feature
        feature_colors = {
            'Prontuário': '#FF6B6B',
            'Agenda': '#4ECDC4',
            'Amigo Intelligence': '#45B7D1',
            'Amigo Pay': '#96CEB4',
            'Rede Amigo': '#FFEAA7'
        }

        # Deslocamentos para evitar sobreposição
        offsets = {
            'Prontuário': [0, 0],
            'Agenda': [0.15, 0.15],
            'Amigo Intelligence': [-0.15, 0.15],
            'Amigo Pay': [0.15, -0.15],
            'Rede Amigo': [-0.15, -0.15]
        }

        # Criar um grupo de features para cada feature
        for feature_name, feature_color in feature_colors.items():
            logger.info(f"Processando feature: {feature_name}")

            # Encontrar o valor máximo para esta feature
            max_value = max([state_data[feature_name] for state_data in features_by_state.values()])
            logger.info(f"Valor máximo para {feature_name}: {max_value}")

            # Adicionar marcadores para cada estado para esta feature
            feature_group = folium.FeatureGroup(name=feature_name)

            for state, features in features_by_state.items():
                if state in STATE_COORDINATES:
                    # Obter o valor da feature para este estado
                    feature_value = features[feature_name]

                    # Calcular o tamanho do círculo com base no valor da feature
                    radius = 5 + (feature_value / max_value) * 15

                    # Calcular a opacidade com base no valor da feature
                    opacity = 0.4 + (feature_value / max_value) * 0.6

                    # Aplicar deslocamento para evitar sobreposição
                    offset = offsets[feature_name]
                    location = [
                        STATE_COORDINATES[state][0] + offset[0],
                        STATE_COORDINATES[state][1] + offset[1]
                    ]

                    # Adicionar um círculo para o estado
                    folium.CircleMarker(
                        location=location,
                        radius=radius,
                        color=feature_color,
                        fill=True,
                        fill_color=feature_color,
                        fill_opacity=opacity,
                        weight=1,
                        tooltip=f"<div style='font-size: 12px; font-weight: bold;'>{state}: {feature_name}</div><div style='font-size: 14px;'>{feature_value}% de uso</div>"
                    ).add_to(feature_group)

                    # Adicionar uma linha conectando o círculo ao estado real (apenas para valores altos)
                    if feature_value > 70:
                        folium.PolyLine(
                            locations=[location, STATE_COORDINATES[state]],
                            color=feature_color,
                            weight=1,
                            opacity=0.5,
                            dash_array='5,5'
                        ).add_to(feature_group)

            # Adicionar o grupo de features ao mapa
            feature_group.add_to(m)

        # Adicionar marcadores para os estados
        states_group = folium.FeatureGroup(name="Estados")
        for state, coord in STATE_COORDINATES.items():
            if state in features_by_state:
                # Adicionar um pequeno marcador para o estado
                folium.CircleMarker(
                    location=coord,
                    radius=2,
                    color='#333',
                    fill=True,
                    fill_color='#333',
                    fill_opacity=0.7,
                    weight=1,
                    tooltip=f"{state}"
                ).add_to(states_group)

                # Adicionar o nome do estado
                folium.Marker(
                    location=coord,
                    icon=folium.DivIcon(
                        icon_size=(0, 0),
                        html=f'<div style="font-size: 8pt; color: #333; text-align: center;">{state}</div>'
                    )
                ).add_to(states_group)

        # Adicionar o grupo de estados ao mapa
        states_group.add_to(m)

        # Adicionar controle de camadas para mostrar/ocultar features
        folium.LayerControl(collapsed=False).add_to(m)

        # Adicionar legenda para as features
        legend_html = f"""
        <div style="position: fixed; bottom: 50px; left: 50px; z-index: 1000; background-color: white; padding: 15px; border-radius: 8px; border: 1px solid #ccc; font-family: Arial, sans-serif; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
            <h4 style="margin: 0 0 10px 0; font-size: 16px; color: #333; border-bottom: 1px solid #eee; padding-bottom: 8px;">Taxa de Uso por Feature</h4>
            {''.join([f'<div style="margin: 8px 0; display: flex; align-items: center;"><span style="display: inline-block; width: 15px; height: 15px; background-color: {color}; border-radius: 50%; margin-right: 8px;"></span> <span style="font-size: 13px;">{feature}</span></div>' for feature, color in feature_colors.items()])}
            <div style="margin-top: 10px; font-size: 11px; color: #666; background-color: #f8f9fa; padding: 8px; border-radius: 4px;">
                <div style="margin-bottom: 5px;"><strong>Guia de Leitura:</strong></div>
                <div>• O tamanho do círculo indica o percentual de uso</div>
                <div>• A intensidade da cor indica a relevância</div>
                <div>• Linhas pontilhadas mostram valores acima de 70%</div>
            </div>
        </div>
        """

        # Adicionar a legenda personalizada ao mapa
        m.get_root().html.add_child(folium.Element(legend_html))
        logger.info("Legenda de features adicionada ao mapa")

        # Salvar o mapa como HTML
        logger.info("Gerando HTML do mapa de features")
        map_html = m.get_root().render()

        # Limpar o HTML para remover mensagens de segurança do Jupyter
        if "Make this Notebook Trusted" in map_html:
            # Fallback: criar um mapa simples
            map_html = '''
            <div style="width:100%; height:100%; display:flex; align-items:center; justify-content:center; background:#f8f9fa; border:1px solid #dee2e6; border-radius:8px;">
                <div style="text-align:center; color:#6c757d;">
                    <div style="font-size:48px; margin-bottom:16px;">📊</div>
                    <div style="font-size:16px; font-weight:500;">Mapa de Features</div>
                    <div style="font-size:14px; margin-top:8px;">Distribuição de funcionalidades por estado</div>
                </div>
            </div>
            '''

        logger.info(f"HTML do mapa de features gerado com sucesso: {len(map_html)} caracteres")
        return map_html
    except Exception as e:
        logger.error(f"Erro ao gerar mapa de features: {str(e)}", exc_info=True)
        return f'<div class="alert alert-danger">Erro ao gerar mapa de features: {str(e)}</div>'

def generate_usage_map(features_by_state: List[Any]) -> str:
    """Generate general usage rate map"""
    logger.info("Iniciando geração do mapa de taxa de uso geral")

    try:
        # Criar um mapa centralizado no Brasil
        m = folium.Map(
            location=[-15.7801, -47.9292],
            zoom_start=4,
            tiles='CartoDB positron',
            control_scale=True,
            prefer_canvas=True
        )

        # Calcular a taxa de uso média para cada estado
        usage_data = {}
        for state, features in features_by_state.items():
            usage_data[state] = sum(features.values()) / len(features)

        # Encontrar o valor máximo para normalizar os dados
        max_value = max(usage_data.values())

        # Criar uma escala de cores
        colormap = cm.LinearColormap(
            colors=['#E6F2FF', '#CCE5FF', '#99CCFF', '#66B2FF', '#3399FF', '#0080FF', '#0066CC'],
            index=[0, 0.1*max_value, 0.2*max_value, 0.4*max_value, 0.6*max_value, 0.8*max_value, max_value],
            vmin=0,
            vmax=max_value
        )

        # Adicionar a legenda
        colormap.caption = 'Taxa de Uso Geral (%)'
        colormap.add_to(m)

        # Adicionar marcadores para cada estado
        for state, usage in usage_data.items():
            if state in STATE_COORDINATES:
                # Calcular o tamanho do círculo com base na taxa de uso
                radius = 5 + (usage / max_value) * 25

                # Calcular a cor com base na taxa de uso
                color = colormap(usage)

                # Adicionar um círculo para o estado
                folium.CircleMarker(
                    location=STATE_COORDINATES[state],
                    radius=radius,
                    color=None,
                    fill=True,
                    fill_color=color,
                    fill_opacity=0.7,
                    tooltip=f"<div style='font-size: 12px; font-weight: bold;'>{state}</div><div style='font-size: 14px;'>Taxa de Uso Geral: {usage:.1f}%</div>"
                ).add_to(m)

                # Adicionar o nome do estado
                folium.Marker(
                    location=STATE_COORDINATES[state],
                    icon=folium.DivIcon(
                        icon_size=(0, 0),
                        html=f'<div style="font-size: 10pt; font-weight: bold; text-align: center;">{state}</div>'
                    )
                ).add_to(m)

        # Salvar o mapa como HTML
        map_html = m.get_root().render()

        # Limpar o HTML para remover mensagens de segurança do Jupyter
        if "Make this Notebook Trusted" in map_html:
            # Fallback: criar um mapa simples
            map_html = '''
            <div style="width:100%; height:100%; display:flex; align-items:center; justify-content:center; background:#f8f9fa; border:1px solid #dee2e6; border-radius:8px;">
                <div style="text-align:center; color:#6c757d;">
                    <div style="font-size:48px; margin-bottom:16px;">📈</div>
                    <div style="font-size:16px; font-weight:500;">Mapa de Taxa de Uso</div>
                    <div style="font-size:14px; margin-top:8px;">Taxa de uso geral por estado</div>
                </div>
            </div>
            '''

        logger.info(f"HTML do mapa de taxa de uso gerado com sucesso: {len(map_html)} caracteres")
        return map_html
    except Exception as e:
        logger.error(f"Erro ao gerar mapa de taxa de uso: {str(e)}", exc_info=True)
        return f'<div class="alert alert-danger">Erro ao gerar mapa de taxa de uso: {str(e)}</div>'

def generate_combined_users_map(users_by_state: Dict[str, Any]) -> str:
    """Generate combined free and premium users map"""
    logger.info("Iniciando geração do mapa combinado de usuários free e premium")

    try:
        # Criar um mapa centralizado no Brasil
        m = folium.Map(
            location=[-15.7801, -47.9292],
            zoom_start=4,
            tiles='CartoDB positron',
            control_scale=True,
            prefer_canvas=True
        )

        # Simular dados de usuários free e premium por estado
        free_users_by_state = {}
        premium_users_by_state = {}

        for state, users in users_by_state.items():
            if state != 'Outros':
                # 65% em média são usuários free
                free_percent = 0.65
                free_users_by_state[state] = int(users * free_percent)

                # 35% em média são usuários premium
                premium_percent = 0.35
                premium_users_by_state[state] = int(users * premium_percent)

        # Encontrar o valor máximo para normalizar os dados
        max_free_value = max(free_users_by_state.values()) if free_users_by_state else 1
        max_premium_value = max(premium_users_by_state.values()) if premium_users_by_state else 1

        # Criar grupos de features para os diferentes tipos de usuários
        free_group = folium.FeatureGroup(name="Usuários Free")
        premium_group = folium.FeatureGroup(name="Usuários Premium")

        # Adicionar marcadores para usuários free
        for state, users in free_users_by_state.items():
            if state in STATE_COORDINATES:
                # Calcular o tamanho do círculo com base no número de usuários
                radius = 5 + (users / max_free_value) * 20

                # Adicionar um círculo para o estado - usuários free
                # Deslocamento para a esquerda para evitar sobreposição
                location = [
                    STATE_COORDINATES[state][0],
                    STATE_COORDINATES[state][1] - 0.3
                ]

                folium.CircleMarker(
                    location=location,
                    radius=radius,
                    color='#3399FF',
                    fill=True,
                    fill_color='#3399FF',
                    fill_opacity=0.6,
                    weight=2,
                    tooltip=f"<div style='font-size: 12px; font-weight: bold;'>{state} - Free</div><div style='font-size: 14px;'>{users} usuários</div>"
                ).add_to(free_group)

        # Adicionar marcadores para usuários premium
        for state, users in premium_users_by_state.items():
            if state in STATE_COORDINATES:
                # Calcular o tamanho do círculo com base no número de usuários
                radius = 5 + (users / max_premium_value) * 20

                # Adicionar um círculo para o estado - usuários premium
                # Deslocamento para a direita para evitar sobreposição
                location = [
                    STATE_COORDINATES[state][0],
                    STATE_COORDINATES[state][1] + 0.3
                ]

                folium.CircleMarker(
                    location=location,
                    radius=radius,
                    color='#FF6B6B',
                    fill=True,
                    fill_color='#FF6B6B',
                    fill_opacity=0.6,
                    weight=2,
                    tooltip=f"<div style='font-size: 12px; font-weight: bold;'>{state} - Premium</div><div style='font-size: 14px;'>{users} usuários</div>"
                ).add_to(premium_group)

        # Adicionar os grupos ao mapa
        free_group.add_to(m)
        premium_group.add_to(m)

        # Adicionar controle de camadas
        folium.LayerControl().add_to(m)

        # Salvar o mapa como HTML
        map_html = m.get_root().render()

        # Limpar o HTML para remover mensagens de segurança do Jupyter
        if "Make this Notebook Trusted" in map_html:
            # Fallback: criar um mapa simples
            map_html = '''
            <div style="width:100%; height:100%; display:flex; align-items:center; justify-content:center; background:#f8f9fa; border:1px solid #dee2e6; border-radius:8px;">
                <div style="text-align:center; color:#6c757d;">
                    <div style="font-size:48px; margin-bottom:16px;">👥</div>
                    <div style="font-size:16px; font-weight:500;">Mapa de Usuários</div>
                    <div style="font-size:14px; margin-top:8px;">Distribuição de usuários Free vs Premium</div>
                </div>
            </div>
            '''

        logger.info(f"HTML do mapa combinado gerado com sucesso: {len(map_html)} caracteres")
        return map_html
    except Exception as e:
        logger.error(f"Erro ao gerar mapa combinado: {str(e)}", exc_info=True)
        return f'<div class="alert alert-danger">Erro ao gerar mapa combinado: {str(e)}</div>'
