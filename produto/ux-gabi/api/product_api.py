"""
Product Domain API Routes - Hybrid Structured Pattern
Reorganizes existing APIs following SSR+CSR structured pattern
"""

from flask import Blueprint, jsonify, session, request
from datetime import datetime
import logging
from typing import Dict, List, Any, Optional

# Import existing controllers
from controllers.dashboard_controller import DashboardController
from controllers.analytics_controller import AnalyticsController
from controllers.medical_controller import MedicalController
from controllers.user_controller import UserController

logger = logging.getLogger(__name__)

# Create API Blueprint with structured prefix
product_api = Blueprint('product_api', __name__, url_prefix='/api/product')

def require_auth() -> Optional[tuple]:
    """Check if user is authenticated for API calls"""
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401
    return None

def create_api_response(data: Any, status: str = 'success', message: str = None) -> Dict[str, Any]:
    """Create standardized API response"""
    response = {
        'status': status,
        'timestamp': datetime.now().isoformat(),
        'user': session.get('username', 'anonymous')
    }
    
    if status == 'success':
        response['data'] = data
    else:
        response['error'] = message or 'Unknown error'
        
    return response

# ============================================================================
# DASHBOARD APIs - Product analytics and metrics
# ============================================================================

@product_api.route('/dashboard/overview')
def dashboard_overview():
    """
    HYBRID STRUCTURED: Dashboard overview data (SSR)
    Used by: Initial page load for static dashboard content
    Pattern: Server-side rendering for fast first paint
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = DashboardController()
        
        # Static overview data for SSR
        overview_data = {
            'total_users': controller.get_total_users(),
            'total_records': controller.get_total_medical_records(),
            'system_status': controller.get_system_status(),
            'basic_metrics': controller.get_basic_metrics(),
            'last_updated': datetime.now().isoformat()
        }
        
        return jsonify(create_api_response(overview_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter overview do dashboard: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

@product_api.route('/dashboard/metrics')
def dashboard_metrics():
    """
    HYBRID STRUCTURED: Real-time dashboard metrics (CSR)
    Used by: HybridManager for dynamic updates
    Pattern: Client-side polling for live data
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = DashboardController()
        
        # Real-time metrics for CSR updates
        metrics_data = {
            'active_users': controller.get_active_users_count(),
            'connections_data': controller.get_connections_data(),
            'usage_analytics': controller.get_usage_analytics(),
            'performance_metrics': controller.get_performance_metrics(),
            'growth_indicators': {
                'user_growth': controller.get_user_growth(),
                'usage_growth': controller.get_usage_growth(),
                'engagement_growth': controller.get_engagement_growth()
            },
            'last_updated': datetime.now().isoformat()
        }
        
        return jsonify(create_api_response(metrics_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter métricas do dashboard: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

@product_api.route('/dashboard/charts')
def dashboard_charts():
    """
    HYBRID STRUCTURED: Chart data for dashboard visualizations (CSR)
    Used by: Chart components for dynamic data loading
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = DashboardController()
        analytics_controller = AnalyticsController()
        
        charts_data = {
            'user_activity_chart': analytics_controller.get_user_activity_chart_data(),
            'feature_usage_chart': analytics_controller.get_feature_usage_chart_data(),
            'performance_chart': analytics_controller.get_performance_chart_data(),
            'geographic_chart': analytics_controller.get_geographic_chart_data(),
            'timeline_chart': analytics_controller.get_timeline_chart_data()
        }
        
        return jsonify(create_api_response(charts_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter dados de gráficos: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

# ============================================================================
# USERS APIs - User analytics and behavior
# ============================================================================

@product_api.route('/users/active')
def users_active():
    """
    HYBRID STRUCTURED: Active users metrics (CSR)
    Used by: User metrics components for real-time updates
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = UserController()
        
        active_users_data = {
            'total_active': controller.get_active_users_count(),
            'growth_percentage': controller.get_user_growth_percentage(),
            'activity_breakdown': controller.get_activity_breakdown(),
            'engagement_metrics': controller.get_engagement_metrics(),
            'last_updated': datetime.now().isoformat()
        }
        
        return jsonify(create_api_response(active_users_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter dados de usuários ativos: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

@product_api.route('/users/activity')
def users_activity():
    """
    HYBRID STRUCTURED: User activity analytics (CSR)
    Used by: Activity tracking components
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = UserController()
        analytics_controller = AnalyticsController()
        
        activity_data = {
            'daily_activity': analytics_controller.get_daily_user_activity(),
            'feature_usage': analytics_controller.get_feature_usage_stats(),
            'session_analytics': analytics_controller.get_session_analytics(),
            'user_journeys': analytics_controller.get_user_journey_data(),
            'retention_metrics': analytics_controller.get_retention_metrics()
        }
        
        return jsonify(create_api_response(activity_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter atividade de usuários: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

@product_api.route('/users/behavior')
def users_behavior():
    """
    HYBRID STRUCTURED: User behavior analysis (CSR)
    Used by: Behavior analytics components
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        analytics_controller = AnalyticsController()
        
        behavior_data = {
            'behavior_patterns': analytics_controller.get_behavior_patterns(),
            'usage_patterns': analytics_controller.get_usage_patterns(),
            'navigation_flow': analytics_controller.get_navigation_flow(),
            'conversion_funnel': analytics_controller.get_conversion_funnel(),
            'abandonment_analysis': analytics_controller.get_abandonment_analysis()
        }
        
        return jsonify(create_api_response(behavior_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter análise comportamental: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

# ============================================================================
# MEDICAL APIs - Medical records and telemedicine
# ============================================================================

@product_api.route('/medical/records')
def medical_records():
    """
    HYBRID STRUCTURED: Medical records overview (SSR)
    Used by: Initial load of medical records page
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = MedicalController()
        
        records_data = {
            'total_records': controller.get_total_records(),
            'recent_records': controller.get_recent_records(limit=10),
            'record_types': controller.get_record_types_summary(),
            'compliance_status': controller.get_compliance_status(),
            'last_updated': datetime.now().isoformat()
        }
        
        return jsonify(create_api_response(records_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter registros médicos: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

@product_api.route('/medical/records/realtime')
def medical_records_realtime():
    """
    HYBRID STRUCTURED: Real-time medical records updates (CSR)
    Used by: Medical dashboard for live updates
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = MedicalController()
        
        realtime_data = {
            'active_sessions': controller.get_active_medical_sessions(),
            'recent_updates': controller.get_recent_record_updates(),
            'system_alerts': controller.get_system_alerts(),
            'queue_status': controller.get_queue_status(),
            'performance_metrics': controller.get_performance_metrics(),
            'last_updated': datetime.now().isoformat()
        }
        
        return jsonify(create_api_response(realtime_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter dados em tempo real: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

@product_api.route('/medical/signatures')
def medical_signatures():
    """
    HYBRID STRUCTURED: Digital signatures data (CSR)
    Used by: Signature tracking components
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = MedicalController()
        
        signatures_data = {
            'pending_signatures': controller.get_pending_signatures(),
            'completed_signatures': controller.get_completed_signatures(),
            'signature_analytics': controller.get_signature_analytics(),
            'compliance_metrics': controller.get_signature_compliance(),
            'workflow_status': controller.get_signature_workflow_status()
        }
        
        return jsonify(create_api_response(signatures_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter dados de assinaturas: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

@product_api.route('/medical/telemedicine')
def medical_telemedicine():
    """
    HYBRID STRUCTURED: Telemedicine data (CSR)
    Used by: Telemedicine dashboard components
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        controller = MedicalController()
        
        telemedicine_data = {
            'active_consultations': controller.get_active_consultations(),
            'consultation_queue': controller.get_consultation_queue(),
            'telemedicine_analytics': controller.get_telemedicine_analytics(),
            'quality_metrics': controller.get_consultation_quality_metrics(),
            'technical_status': controller.get_technical_status()
        }
        
        return jsonify(create_api_response(telemedicine_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter dados de telemedicina: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

# ============================================================================
# FEATURES APIs - Feature usage and performance
# ============================================================================

@product_api.route('/features/usage')
def features_usage():
    """
    HYBRID STRUCTURED: Feature usage analytics (CSR)
    Used by: Feature analytics components
    """
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        analytics_controller = AnalyticsController()
        
        usage_data = {
            'feature_adoption': analytics_controller.get_feature_adoption_rates(),
            'usage_trends': analytics_controller.get_feature_usage_trends(),
            'popular_features': analytics_controller.get_popular_features(),
            'feature_performance': analytics_controller.get_feature_performance(),
            'user_preferences': analytics_controller.get_user_feature_preferences()
        }
        
        return jsonify(create_api_response(usage_data))
        
    except Exception as e:
        logger.error(f"Erro ao obter dados de uso de funcionalidades: {e}")
        return jsonify(create_api_response(None, 'error', str(e))), 500

# ============================================================================
# HEALTH AND STATUS APIs
# ============================================================================

@product_api.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'product-domain-api',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@product_api.route('/status')
def status():
    """Detailed status endpoint"""
    try:
        return jsonify({
            'status': 'operational',
            'service': 'product-domain-api',
            'authenticated_user': session.get('username', 'anonymous'),
            'timestamp': datetime.now().isoformat(),
            'endpoints': {
                'dashboard': 'operational',
                'users': 'operational',
                'medical': 'operational',
                'features': 'operational'
            },
            'version': '1.0.0'
        })
    except Exception as e:
        logger.error(f"Erro ao obter status: {e}")
        return jsonify({
            'status': 'error',
            'service': 'product-domain-api',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500
