"""
Admin API routes for Product Domain
Provides REST API endpoints for admin functionality
"""

from flask import Blueprint, request, jsonify, session, Response
import sys
import os
import secrets
import string
from pathlib import Path
import logging
from typing import Dict, List, Any, Optional, Union, Tuple

# Add shared core to path
CORE_PATH = Path(__file__).parent.parent.parent.parent / 'shared' / 'datamesh-core'
sys.path.insert(0, str(CORE_PATH))

try:
    from admin.admin_service import AdminService
    from admin.monitoring_service import MonitoringService
    ADMIN_AVAILABLE = True
    print("✅ Admin service loaded successfully for Product domain")
except ImportError as e:
    ADMIN_AVAILABLE = False
    print(f"❌ Admin service not available: {e}")

logger = logging.getLogger(__name__)

admin_api = Blueprint('admin_api', __name__, url_prefix='/api/admin')

def require_admin() -> bool:
    """Check if user is admin"""
    if 'username' not in session:
        return False
    return session.get('username') in ['admin', 'TTK', 'bruno@abreu', 'bruno@bruno']

@admin_api.route('/stats', methods=['GET'])
def get_admin_stats() -> Optional[Dict[str, Any]]:
    """Get admin dashboard statistics"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    if not ADMIN_AVAILABLE:
        return jsonify({'error': 'Admin service not available'}), 503

    try:
        admin_service = AdminService('product')
        stats = admin_service.get_user_stats()
        return jsonify(stats)
    except Exception as e:
        logger.error(f"Error getting admin stats: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/users', methods=['GET'])
def get_users() -> Optional[Dict[str, Any]]:
    """Get all users"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    if not ADMIN_AVAILABLE:
        return jsonify({'error': 'Admin service not available'}), 503

    try:
        admin_service = AdminService('product')
        users = admin_service.get_all_users()
        return jsonify({'users': users})
    except Exception as e:
        logger.error(f"Error getting users: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/users/test', methods=['GET'])
def test_get_users() -> Response:
    """Test endpoint to get all users without auth (temporary)"""
    if not ADMIN_AVAILABLE:
        return jsonify({'error': 'Admin service not available'}), 503

    try:
        admin_service = AdminService('product')
        users = admin_service.get_all_users()
        return jsonify({
            'users': users,
            'count': len(users),
            'session_user': session.get('username', 'Not logged in'),
            'is_admin': require_admin()
        })
    except Exception as e:
        logger.error(f"Error getting users: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/users', methods=['POST'])
def create_user() -> Dict[str, Any]:
    """Create a new user"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    if not ADMIN_AVAILABLE:
        return jsonify({'error': 'Admin service not available'}), 503

    try:
        data = request.get_json()

        if not data or not all(k in data for k in ['username', 'email', 'password']):
            return jsonify({'error': 'Missing required fields'}), 400

        admin_service = AdminService('product')
        user = admin_service.create_user(
            username=data['username'],
            email=data['email'],
            password=data['password'],
            role=data.get('role', 'user'),
            permissions=data.get('permissions', [])
        )

        # Remove sensitive data
        if 'metadata' in user and 'password_hash' in user['metadata']:
            del user['metadata']['password_hash']

        return jsonify({'user': user}), 201
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/users/<username>/permissions', methods=['PUT'])
def update_user_permissions(username: str) -> bool:
    """Update user permissions"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    if not ADMIN_AVAILABLE:
        return jsonify({'error': 'Admin service not available'}), 503

    try:
        data = request.get_json()

        if not data or 'permissions' not in data:
            return jsonify({'error': 'Missing permissions field'}), 400

        admin_service = AdminService('product')
        success = admin_service.update_user_permissions(username, data['permissions'])

        if not success:
            return jsonify({'error': 'User not found'}), 404

        return jsonify({'message': 'Permissions updated successfully'})
    except Exception as e:
        logger.error(f"Error updating permissions: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/users/<username>/deactivate', methods=['POST'])
def deactivate_user(username: str) -> Response:
    """Deactivate a user"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    if not ADMIN_AVAILABLE:
        return jsonify({'error': 'Admin service not available'}), 503

    try:
        admin_service = AdminService('product')
        success = admin_service.deactivate_user(username)

        if not success:
            return jsonify({'error': 'User not found'}), 404

        return jsonify({'message': 'User deactivated successfully'})
    except Exception as e:
        logger.error(f"Error deactivating user: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/permissions', methods=['GET'])
def get_page_permissions() -> Optional[Dict[str, Any]]:
    """Get available page permissions"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    if not ADMIN_AVAILABLE:
        return jsonify({'error': 'Admin service not available'}), 503

    try:
        admin_service = AdminService('product')
        permissions = admin_service.get_page_permissions()
        return jsonify({'permissions': permissions})
    except Exception as e:
        logger.error(f"Error getting permissions: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/activity', methods=['GET'])
def get_activity_logs() -> Optional[Dict[str, Any]]:
    """Get activity logs"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    if not ADMIN_AVAILABLE:
        return jsonify({'error': 'Admin service not available'}), 503

    try:
        limit = request.args.get('limit', 100, type=int)
        admin_service = AdminService('product')
        logs = admin_service.get_activity_logs(limit)
        return jsonify({'logs': logs})
    except Exception as e:
        logger.error(f"Error getting activity logs: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/monitoring/dashboard', methods=['GET'])
def get_monitoring_dashboard() -> Optional[Dict[str, Any]]:
    """Get monitoring dashboard data"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    if not ADMIN_AVAILABLE:
        return jsonify({'error': 'Monitoring service not available'}), 503

    try:
        monitoring_service = MonitoringService('product')
        metrics = monitoring_service.get_dashboard_metrics()
        user_analytics = monitoring_service.get_user_analytics()
        page_analytics = monitoring_service.get_page_analytics()

        return jsonify({
            'metrics': metrics,
            'user_analytics': user_analytics,
            'page_analytics': page_analytics
        })
    except Exception as e:
        logger.error(f"Error getting monitoring data: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/monitoring/log', methods=['POST'])
def log_user_activity() -> Response:
    """Log user activity"""
    try:
        data = request.get_json()

        if not data or not all(k in data for k in ['user_id', 'action', 'page']):
            return jsonify({'error': 'Missing required fields'}), 400

        if ADMIN_AVAILABLE:
            monitoring_service = MonitoringService('product')
            monitoring_service.log_user_activity(
                user_id=data['user_id'],
                action=data['action'],
                page=data['page'],
                details=data.get('details', {})
            )

        return jsonify({'message': 'Activity logged successfully'})
    except Exception as e:
        logger.error(f"Error logging activity: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/invite', methods=['POST'])
def invite_user() -> Any:
    """Invite a new user"""
    if not require_admin():
        return jsonify({'error': 'Admin access required'}), 403

    try:
        data = request.get_json()

        if not data or not all(k in data for k in ['email', 'role']):
            return jsonify({'error': 'Missing required fields'}), 400

        # TODO: Implement email invitation system
        # For now, just create a temporary password
        temp_password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))
        username = data['email'].split('@')[0]

        if ADMIN_AVAILABLE:
            admin_service = AdminService('product')
            user = admin_service.create_user(
                username=username,
                email=data['email'],
                password=temp_password,
                role=data['role'],
                permissions=data.get('permissions', [])
            )

            return jsonify({
                'message': 'User invited successfully',
                'username': username,
                'temporary_password': temp_password,
                'note': 'In production, this would be sent via email'
            }), 201
        else:
            return jsonify({'error': 'Admin service not available'}), 503

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error inviting user: {e}")
        return jsonify({'error': 'Internal server error'}), 500
