"""
User Models
Data models for user operations
"""
from dataclasses import dataclass, field
from datetime import datetime, date
from typing import Dict, Any, List, Optional
import logging
from .base_model import BaseModel

logger = logging.getLogger(__name__)

@dataclass
class User(BaseModel):
    """User entity"""
    
    user_id: Optional[str] = None
    username: Optional[str] = None
    email: Optional[str] = None
    full_name: Optional[str] = None
    specialty: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    subscription_type: Optional[str] = None
    status: Optional[str] = None
    device_type: Optional[str] = None
    registration_date: Optional[date] = None
    last_access_date: Optional[date] = None
    age: Optional[int] = None
    gender: Optional[str] = None
    
    def is_active(self) -> bool:
        """Check if user is active"""
        return self.status == 'active'
    
    def is_premium(self) -> bool:
        """Check if user has premium subscription"""
        return self.subscription_type in ['premium', 'pro', 'enterprise']
    
    def get_age_group(self) -> str:
        """Get age group classification"""
        if not self.age:
            return 'unknown'
        
        if self.age < 25:
            return '18-24'
        elif self.age < 35:
            return '25-34'
        elif self.age < 45:
            return '35-44'
        elif self.age < 55:
            return '45-54'
        else:
            return '55+'

@dataclass
class UserStats(BaseModel):
    """User statistics"""
    
    total_patients: int = 0
    total_records_created: int = 0
    total_ai_chats: int = 0
    total_network_connections: int = 0
    total_financial_transactions: int = 0
    monthly_fee: float = 0.0
    
    def get_engagement_score(self) -> float:
        """Calculate user engagement score"""
        # Simple engagement calculation
        score = 0
        score += min(self.total_patients / 10, 10)  # Max 10 points for patients
        score += min(self.total_records_created / 50, 10)  # Max 10 points for records
        score += min(self.total_ai_chats / 20, 5)  # Max 5 points for AI usage
        score += min(self.total_network_connections / 10, 5)  # Max 5 points for connections
        return min(score, 30)  # Max 30 points total

@dataclass
class UserAnalytics(BaseModel):
    """User analytics data"""
    
    total_users: int = 0
    active_users: int = 0
    premium_users: int = 0
    free_users: int = 0
    users_by_device: Dict[str, int] = field(default_factory=dict)
    users_by_specialty: Dict[str, int] = field(default_factory=dict)
    users_by_state: Dict[str, int] = field(default_factory=dict)
    age_distribution: Dict[str, int] = field(default_factory=dict)
    growth_by_month: List[Dict[str, Any]] = field(default_factory=list)
    
    def calculate_premium_conversion_rate(self) -> float:
        """Calculate premium conversion rate"""
        if self.total_users > 0:
            return (self.premium_users / self.total_users) * 100
        return 0.0
    
    def get_top_specialties(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get top specialties by user count"""
        sorted_specialties = sorted(
            self.users_by_specialty.items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        return [
            {'specialty': specialty, 'count': count} 
            for specialty, count in sorted_specialties[:limit]
        ]
    
    def get_top_states(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top states by user count"""
        sorted_states = sorted(
            self.users_by_state.items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        return [
            {'state': state, 'count': count} 
            for state, count in sorted_states[:limit]
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with backward compatibility"""
        return {
            'total_users': self.total_users,
            'active_users': self.active_users,
            'premium_users': self.premium_users,
            'free_users': self.free_users,
            'users_by_device': self.users_by_device,
            'users_by_specialty': self.users_by_specialty,
            'users_by_state': self.users_by_state,
            'age_distribution': self.age_distribution,
            'growth_by_month': self.growth_by_month,
            'premium_conversion_rate': self.calculate_premium_conversion_rate(),
            'top_specialties': self.get_top_specialties(),
            'top_states': self.get_top_states()
        }
    
    @classmethod
    def from_legacy_dict(cls, data: Dict[str, Any]):
        """Create from legacy dictionary format for backward compatibility"""
        try:
            return cls(
                total_users=data.get('total_users', 0),
                active_users=data.get('active_users', 0),
                premium_users=data.get('premium_users', 0),
                free_users=data.get('free_users', 0),
                users_by_device=data.get('users_by_device', {}),
                users_by_specialty=data.get('users_by_specialty', {}),
                users_by_state=data.get('users_by_state', {}),
                age_distribution=data.get('age_distribution', {}),
                growth_by_month=data.get('growth_by_month', [])
            )
        except Exception as e:
            logger.warning(f"Error creating UserAnalytics from legacy dict: {e}")
            return cls()
