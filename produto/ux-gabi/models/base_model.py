"""
Base Model for all entities
Provides common functionality and validation
"""
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

@dataclass
class BaseModel:
    """Base model with common functionality"""
    
    created_at: Optional[datetime] = field(default_factory=datetime.now)
    updated_at: Optional[datetime] = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary for backward compatibility"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, datetime):
                result[key] = value.isoformat()
            elif hasattr(value, 'to_dict'):
                result[key] = value.to_dict()
            elif isinstance(value, list) and value and hasattr(value[0], 'to_dict'):
                result[key] = [item.to_dict() for item in value]
            else:
                result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create model from dictionary for backward compatibility"""
        try:
            # Filter only fields that exist in the dataclass
            valid_fields = {f.name for f in cls.__dataclass_fields__.values()}
            filtered_data = {k: v for k, v in data.items() if k in valid_fields}
            
            # Handle datetime fields
            for field_name, field_info in cls.__dataclass_fields__.items():
                if field_name in filtered_data:
                    if field_info.type == Optional[datetime] or field_info.type == datetime:
                        if isinstance(filtered_data[field_name], str):
                            try:
                                filtered_data[field_name] = datetime.fromisoformat(filtered_data[field_name])
                            except ValueError:
                                filtered_data[field_name] = None
            
            return cls(**filtered_data)
        except Exception as e:
            logger.warning(f"Error creating {cls.__name__} from dict: {e}")
            # Return a default instance if creation fails
            return cls()
    
    def update_timestamp(self):
        """Update the updated_at timestamp"""
        self.updated_at = datetime.now()
    
    def validate(self) -> bool:
        """Basic validation - can be overridden in subclasses"""
        return True
