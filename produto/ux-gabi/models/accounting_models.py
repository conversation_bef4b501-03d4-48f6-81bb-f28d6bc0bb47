"""
Accounting Models
Data models for financial/accounting operations
"""
from dataclasses import dataclass, field
from decimal import Decimal
from datetime import datetime, date
from typing import Dict, Any, List, Optional
import logging
from .base_model import BaseModel

logger = logging.getLogger(__name__)

@dataclass
class AccountingRecord(BaseModel):
    """Individual accounting record"""
    
    record_id: Optional[str] = None
    date: Optional[date] = None
    description: Optional[str] = None
    category: Optional[str] = None
    amount: Optional[Decimal] = None
    type: Optional[str] = None  # 'revenue' or 'expense'
    status: Optional[str] = None
    
    def __post_init__(self):
        """Convert amount to Decimal if it's not already"""
        if self.amount is not None and not isinstance(self.amount, Decimal):
            try:
                self.amount = Decimal(str(self.amount))
            except (ValueError, TypeError):
                self.amount = Decimal('0')
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create AccountingRecord from dictionary"""
        # Handle date conversion
        if 'date' in data and isinstance(data['date'], str):
            try:
                data['date'] = datetime.strptime(data['date'], '%Y-%m-%d').date()
            except ValueError:
                data['date'] = None
        
        return super().from_dict(data)

@dataclass
class FinancialSummary(BaseModel):
    """Financial summary with calculated metrics"""
    
    total_revenue: Decimal = field(default_factory=lambda: Decimal('0'))
    total_expenses: Decimal = field(default_factory=lambda: Decimal('0'))
    net_profit: Decimal = field(default_factory=lambda: Decimal('0'))
    profit_margin: float = 0.0
    period_start: Optional[date] = None
    period_end: Optional[date] = None
    
    def __post_init__(self):
        """Calculate derived metrics"""
        self.calculate_metrics()
    
    def calculate_metrics(self):
        """Calculate profit and margin"""
        self.net_profit = self.total_revenue - self.total_expenses
        
        if self.total_revenue > 0:
            self.profit_margin = float((self.net_profit / self.total_revenue) * 100)
        else:
            self.profit_margin = 0.0
    
    def add_revenue(self, amount: Decimal):
        """Add revenue and recalculate metrics"""
        self.total_revenue += amount
        self.calculate_metrics()
        self.update_timestamp()
    
    def add_expense(self, amount: Decimal):
        """Add expense and recalculate metrics"""
        self.total_expenses += amount
        self.calculate_metrics()
        self.update_timestamp()

@dataclass
class MonthlyTrend(BaseModel):
    """Monthly financial trend data"""
    
    month: str = ""
    year: int = 0
    revenue: Decimal = field(default_factory=lambda: Decimal('0'))
    expenses: Decimal = field(default_factory=lambda: Decimal('0'))
    profit: Decimal = field(default_factory=lambda: Decimal('0'))
    
    def __post_init__(self):
        """Calculate profit"""
        self.profit = self.revenue - self.expenses

@dataclass
class CategoryAnalysis(BaseModel):
    """Category-based financial analysis"""
    
    category: str = ""
    total_amount: Decimal = field(default_factory=lambda: Decimal('0'))
    transaction_count: int = 0
    average_amount: Decimal = field(default_factory=lambda: Decimal('0'))
    percentage_of_total: float = 0.0
    
    def calculate_average(self):
        """Calculate average amount"""
        if self.transaction_count > 0:
            self.average_amount = self.total_amount / self.transaction_count
        else:
            self.average_amount = Decimal('0')

@dataclass
class AccountingAnalytics(BaseModel):
    """Complete accounting analytics"""
    
    financial_summary: Optional[FinancialSummary] = None
    monthly_trends: List[MonthlyTrend] = field(default_factory=list)
    revenue_categories: List[CategoryAnalysis] = field(default_factory=list)
    expense_categories: List[CategoryAnalysis] = field(default_factory=list)
    records_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with backward compatibility"""
        result = {
            'financial_summary': self.financial_summary.to_dict() if self.financial_summary else {},
            'monthly_trends': {
                'revenue': [{'month': t.month, 'value': float(t.revenue)} for t in self.monthly_trends],
                'expenses': [{'month': t.month, 'value': float(t.expenses)} for t in self.monthly_trends],
                'profit': [{'month': t.month, 'value': float(t.profit)} for t in self.monthly_trends]
            },
            'category_analysis': {
                'revenue_by_category': {cat.category: float(cat.total_amount) for cat in self.revenue_categories},
                'expenses_by_category': {cat.category: float(cat.total_amount) for cat in self.expense_categories},
                'top_revenue': [{'category': cat.category, 'amount': float(cat.total_amount)} for cat in self.revenue_categories[:5]],
                'top_expenses': [{'category': cat.category, 'amount': float(cat.total_amount)} for cat in self.expense_categories[:5]]
            },
            'records_count': self.records_count
        }
        return result

    @classmethod
    def from_legacy_dict(cls, data: Dict[str, Any]):
        """Create from legacy dictionary format for backward compatibility"""
        try:
            # Extract financial summary
            financial_data = data.get('financial_summary', {})
            financial_summary = FinancialSummary(
                total_revenue=Decimal(str(financial_data.get('total_revenue', 0))),
                total_expenses=Decimal(str(financial_data.get('total_expenses', 0)))
            )

            # Extract monthly trends
            monthly_trends = []
            trends_data = data.get('monthly_trends', {})
            revenue_trends = trends_data.get('revenue', [])
            expense_trends = trends_data.get('expenses', [])

            for i, rev_trend in enumerate(revenue_trends):
                exp_trend = expense_trends[i] if i < len(expense_trends) else {'value': 0}
                monthly_trends.append(MonthlyTrend(
                    month=rev_trend.get('month', f'Month {i+1}'),
                    revenue=Decimal(str(rev_trend.get('value', 0))),
                    expenses=Decimal(str(exp_trend.get('value', 0)))
                ))

            # Extract categories
            category_data = data.get('category_analysis', {})
            revenue_categories = []
            expense_categories = []

            for cat, amount in category_data.get('revenue_by_category', {}).items():
                revenue_categories.append(CategoryAnalysis(
                    category=cat,
                    total_amount=Decimal(str(amount))
                ))

            for cat, amount in category_data.get('expenses_by_category', {}).items():
                expense_categories.append(CategoryAnalysis(
                    category=cat,
                    total_amount=Decimal(str(amount))
                ))

            return cls(
                financial_summary=financial_summary,
                monthly_trends=monthly_trends,
                revenue_categories=revenue_categories,
                expense_categories=expense_categories,
                records_count=data.get('records_count', 0)
            )
        except Exception as e:
            logger.warning(f"Error creating AccountingAnalytics from legacy dict: {e}")
            return cls()
