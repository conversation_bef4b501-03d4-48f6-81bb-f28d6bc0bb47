"""
Connection Models
Data models for network/connection operations
"""
from dataclasses import dataclass, field
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Set
import logging
from .base_model import BaseModel

logger = logging.getLogger(__name__)

@dataclass
class Connection(BaseModel):
    """Individual connection record"""
    
    source_profile_id: Optional[int] = None
    target_profile_id: Optional[int] = None
    status: Optional[str] = None  # 'ACCEPTED', 'PENDING', 'REFUSED'
    created_date: Optional[date] = None
    
    def is_accepted(self) -> bool:
        """Check if connection is accepted"""
        return self.status == 'ACCEPTED'
    
    def is_pending(self) -> bool:
        """Check if connection is pending"""
        return self.status == 'PENDING'
    
    def get_connection_pair(self) -> tuple:
        """Get sorted pair for undirected connections"""
        if self.source_profile_id and self.target_profile_id:
            return tuple(sorted([self.source_profile_id, self.target_profile_id]))
        return (None, None)

@dataclass
class NetworkMetrics(BaseModel):
    """Network-level metrics"""
    
    total_connections: int = 0
    accepted_connections: int = 0
    pending_connections: int = 0
    refused_connections: int = 0
    total_users: int = 0
    active_users: int = 0
    pending_only_users: int = 0
    density: float = 0.0
    avg_connections_per_user: float = 0.0
    acceptance_rate: float = 0.0
    
    def calculate_acceptance_rate(self):
        """Calculate acceptance rate"""
        total_processed = self.accepted_connections + self.refused_connections
        if total_processed > 0:
            self.acceptance_rate = (self.accepted_connections / total_processed) * 100
        else:
            self.acceptance_rate = 0.0
    
    def calculate_density(self):
        """Calculate network density"""
        if self.total_users > 1:
            max_possible = self.total_users * (self.total_users - 1) / 2
            self.density = (self.accepted_connections / max_possible) * 100
        else:
            self.density = 0.0

@dataclass
class UserBehavior(BaseModel):
    """User behavior analytics"""
    
    total_unique_users: int = 0
    avg_connections_sent: float = 0.0
    avg_connections_received: float = 0.0
    most_connected_users: List[Dict[str, Any]] = field(default_factory=list)
    connection_distribution: Dict[str, int] = field(default_factory=dict)
    
    def add_top_connector(self, user_id: int, connection_count: int):
        """Add a top connector"""
        self.most_connected_users.append({
            'user_id': user_id,
            'connection_count': connection_count
        })

@dataclass
class ConnectionAnalytics(BaseModel):
    """Complete connection analytics"""
    
    network_metrics: Optional[NetworkMetrics] = None
    user_behavior: Optional[UserBehavior] = None
    monthly_growth: List[Dict[str, Any]] = field(default_factory=list)
    top_connectors: List[Dict[str, Any]] = field(default_factory=list)
    influence_analysis: Dict[str, Any] = field(default_factory=dict)
    optimization_insights: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with backward compatibility"""
        result = {
            'network_metrics': self.network_metrics.to_dict() if self.network_metrics else {},
            'user_behavior': self.user_behavior.to_dict() if self.user_behavior else {},
            'monthly_growth': self.monthly_growth,
            'top_connectors': self.top_connectors,
            'influence_analysis': self.influence_analysis,
            'optimization_insights': self.optimization_insights
        }
        return result
    
    @classmethod
    def from_legacy_dict(cls, data: Dict[str, Any]):
        """Create from legacy dictionary format for backward compatibility"""
        try:
            # Extract network metrics
            network_data = data.get('network_metrics', {})
            network_metrics = NetworkMetrics(
                total_connections=network_data.get('total_connections', 0),
                accepted_connections=network_data.get('accepted_connections', 0),
                pending_connections=network_data.get('pending_connections', 0),
                refused_connections=network_data.get('refused_connections', 0),
                total_users=network_data.get('total_users', 0),
                active_users=network_data.get('active_users', 0),
                pending_only_users=network_data.get('pending_only_users', 0),
                density=network_data.get('density', 0.0),
                avg_connections_per_user=network_data.get('avg_connections_per_user', 0.0)
            )
            network_metrics.calculate_acceptance_rate()
            
            # Extract user behavior
            behavior_data = data.get('user_behavior', {})
            user_behavior = UserBehavior(
                total_unique_users=behavior_data.get('total_unique_users', 0),
                avg_connections_sent=behavior_data.get('avg_connections_sent', 0.0),
                avg_connections_received=behavior_data.get('avg_connections_received', 0.0),
                most_connected_users=behavior_data.get('most_connected_users', []),
                connection_distribution=behavior_data.get('connection_distribution', {})
            )
            
            return cls(
                network_metrics=network_metrics,
                user_behavior=user_behavior,
                monthly_growth=data.get('monthly_growth', []),
                top_connectors=data.get('top_connectors', []),
                influence_analysis=data.get('influence_analysis', {}),
                optimization_insights=data.get('optimization_insights', {})
            )
        except Exception as e:
            logger.warning(f"Error creating ConnectionAnalytics from legacy dict: {e}")
            return cls()

@dataclass
class InfluenceMetrics(BaseModel):
    """Influence analysis metrics"""
    
    user_id: int = 0
    influence_score: float = 0.0
    centrality_score: float = 0.0
    reach_potential: int = 0
    connection_quality: float = 0.0
    
    def calculate_overall_influence(self) -> float:
        """Calculate overall influence score"""
        return (self.influence_score * 0.4 + 
                self.centrality_score * 0.3 + 
                (self.reach_potential / 100) * 0.2 + 
                self.connection_quality * 0.1)
