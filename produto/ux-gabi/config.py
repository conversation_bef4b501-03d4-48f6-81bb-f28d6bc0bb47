"""
DataHub Amigo One - Product Domain Configuration
Simplified configuration without shared dependencies
"""

import os
import logging
from pathlib import Path

# Application root directory
APP_ROOT = Path(__file__).parent.absolute()

class Config:
    """Simplified Product Domain Configuration"""
    
    # Application root directory
    APP_ROOT = APP_ROOT

    # Flask settings
    SECRET_KEY = os.getenv('SECRET_KEY', 'product_secret_key_change_in_production')
    DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

    # Static and template folders
    STATIC_FOLDER = os.path.join(APP_ROOT, 'static')
    TEMPLATE_FOLDER = os.path.join(APP_ROOT, 'templates')

    # Database Configuration
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = os.getenv('DB_PORT', '5432')
    DB_NAME = os.getenv('DB_NAME', 'amigo_datahub_product')
    DB_USER = os.getenv('DB_USER', 'product_user')
    DB_PASSWORD = os.getenv('DB_PASSWORD', '')
    DB_SCHEMA = os.getenv('PRODUCT_DB_SCHEMA', 'product')

    # Session security
    SESSION_COOKIE_SECURE = os.getenv('SESSION_COOKIE_SECURE', 'False').lower() == 'true'
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = int(os.getenv('PERMANENT_SESSION_LIFETIME', '3600'))

    # Logging
    LOG_FILE = os.path.join(APP_ROOT, 'logs', 'app.log')
    LOG_LEVEL = logging.INFO

    # Ensure the logs directory exists
    os.makedirs(os.path.join(APP_ROOT, 'logs'), exist_ok=True)

    # Application settings
    APP_NAME = 'DataHub Amigo One - Produto'
    APP_VERSION = '2.0.0'
    APP_PORT = int(os.getenv('PRODUCT_PORT', '5001'))

    # Product domain specific features
    ENABLE_USER_ANALYTICS = os.getenv('ENABLE_USER_ANALYTICS', 'true').lower() == 'true'
    ENABLE_FEATURE_TRACKING = os.getenv('ENABLE_FEATURE_TRACKING', 'true').lower() == 'true'
    ENABLE_PAYMENT_ANALYTICS = os.getenv('ENABLE_PAYMENT_ANALYTICS', 'true').lower() == 'true'

    # Integration with Business domain (for navigation only)
    BUSINESS_APP_URL = os.getenv('BUSINESS_APP_URL', 'https://5mmmbhl1-5000.brs.devtunnels.ms/')

    # Data source mode: 'database' or 'files'
    DATA_SOURCE_MODE = os.getenv('DATA_SOURCE_MODE', 'files')

    @property
    def database_url(self) -> str:
        """Build database URL with product schema"""
        if self.DB_PASSWORD:
            base_url = f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
            base_url += f"?options=-csearch_path%3D{self.DB_SCHEMA}"
            return base_url
        return None

    # Color scheme
    COLORS = {
        'primary': '#0087EB',
        'secondary': '#98CFFF',
        'tertiary': '#E0F1FF',
        'success': '#34C759',
        'warning': '#FF9500',
        'danger': '#FF3B30',
    }

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False
    DATA_SOURCE_MODE = 'files'
    SESSION_COOKIE_SECURE = False

class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = False
    TESTING = True
    DATA_SOURCE_MODE = 'files'
    SESSION_COOKIE_SECURE = False

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    DATA_SOURCE_MODE = os.getenv('DATA_SOURCE_MODE', 'database')
    SESSION_COOKIE_SECURE = True

# Configuration dictionary
config_by_name = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig
}

def get_product_config(environment: str = 'development'):
    """Get product domain configuration for specific environment"""
    return config_by_name.get(environment, DevelopmentConfig)

# Export for direct import
config = get_product_config()
SECRET_KEY = config.SECRET_KEY
