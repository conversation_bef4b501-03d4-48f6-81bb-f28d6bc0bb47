"""
Web Routes - APENAS mapeamento de URLs
Responsabilidade: Definir quais URLs existem e delegar para controllers
"""
from flask import Blueprint
from controllers.dashboard_controller import DashboardController
from controllers.users_controller import UsersController
from controllers.connections_controller import ConnectionsController
from controllers.accounting_controller import AccountingController
from controllers.auth_controller import AuthController

# Initialize controllers
dashboard_controller = DashboardController()
users_controller = UsersController()
connections_controller = ConnectionsController()
accounting_controller = AccountingController()
auth_controller = AuthController()

# Create blueprint
web_routes = Blueprint('web_routes', __name__)

# Dashboard routes
@web_routes.route('/')
def index():
    """Dashboard principal"""
    auth_check = auth_controller.require_login()
    if auth_check:
        return auth_check
    return dashboard_controller.render_dashboard()

# Users routes
@web_routes.route('/users')
def users():
    """Página de usuários"""
    auth_check = auth_controller.require_login()
    if auth_check:
        return auth_check
    return users_controller.render_users_page()

# Connections routes
@web_routes.route('/connections')
def connections():
    """Página de conexões"""
    auth_check = auth_controller.require_login()
    if auth_check:
        return auth_check
    return connections_controller.render_connections_page()

# Other routes (keeping existing logic for now)
@web_routes.route('/payments')
def payments():
    """Página de pagamentos"""
    auth_check = auth_controller.require_login()
    if auth_check:
        return auth_check
    # TODO: Create PaymentsController
    from routes.main_routes import main_routes
    return main_routes.view_functions['payments']()

@web_routes.route('/agenda')
def agenda():
    """Página de agenda"""
    auth_check = auth_controller.require_login()
    if auth_check:
        return auth_check
    # TODO: Create AgendaController
    from routes.main_routes import main_routes
    return main_routes.view_functions['agenda']()

@web_routes.route('/medical_records')
def medical_records():
    """Página de prontuários"""
    auth_check = auth_controller.require_login()
    if auth_check:
        return auth_check
    # TODO: Create MedicalRecordsController
    from routes.main_routes import main_routes
    return main_routes.view_functions['medical_records']()

@web_routes.route('/accounting')
def accounting():
    """Página de contabilidade"""
    auth_check = auth_controller.require_login()
    if auth_check:
        return auth_check
    return accounting_controller.render_accounting_page()

@web_routes.route('/amigo_intelligence')
def amigo_intelligence():
    """Página Amigo Intelligence"""
    auth_check = auth_controller.require_login()
    if auth_check:
        return auth_check
    # TODO: Create AmigoIntelligenceController
    from routes.main_routes import main_routes
    return main_routes.view_functions['amigo_intelligence']()

@web_routes.route('/documentacao')
def documentacao():
    """Página de documentação"""
    auth_check = auth_controller.require_login()
    if auth_check:
        return auth_check
    # TODO: Create DocumentationController
    from routes.main_routes import main_routes
    return main_routes.view_functions['documentacao']()

@web_routes.route('/health')
def health():
    """Health check"""
    # TODO: Create HealthController
    from routes.main_routes import main_routes
    return main_routes.view_functions['health']()
