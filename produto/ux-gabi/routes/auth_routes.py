"""
Authentication Routes for Product Domain
"""
from flask import Blueprint, render_template, request, session, redirect, url_for, flash, jsonify
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

auth_routes = Blueprint('auth_routes', __name__)

# Simple user credentials (for development)
VALID_USERS = {
    'admin': 'admin123',
    'user': 'user123',
    'demo': 'demo123'
}

@auth_routes.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()
        
        # Validate credentials
        if username in VALID_USERS and VALID_USERS[username] == password:
            session['username'] = username
            session['login_time'] = datetime.now().isoformat()
            logger.info(f"User {username} logged in successfully")
            
            # Redirect to next page or dashboard
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('main_routes.index'))
        else:
            flash('Usuário ou senha inválidos', 'error')
            logger.warning(f"Failed login attempt for username: {username}")
    
    return render_template('login.html')

@auth_routes.route('/logout', methods=['GET', 'POST'])
def logout():
    """Logout user"""
    username = session.get('username', 'Unknown')
    session.clear()
    logger.info(f"User {username} logged out")
    flash('Logout realizado com sucesso', 'success')
    return redirect(url_for('auth_routes.login'))

@auth_routes.route('/check_auth')
def check_auth():
    """Check authentication status (API endpoint)"""
    if 'username' in session:
        return jsonify({
            'authenticated': True,
            'username': session['username'],
            'login_time': session.get('login_time')
        })
    else:
        return jsonify({
            'authenticated': False
        }), 401
