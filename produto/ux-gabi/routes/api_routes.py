"""
API Routes for Product Domain
"""
from flask import Blueprint, jsonify, session, request
import logging
from datetime import datetime

from services.data_service import get_empty_analytics_data, get_features_by_state, load_accounting_data, get_accounting_analytics, load_connections_data, get_connections_analytics
from controllers.accounting_controller import AccountingController
from controllers.connections_controller import ConnectionsController
from database_adapter import ProductDatabaseAdapter
from typing import Dict, List, Any, Optional, Union, Tuple
from flask import Response

logger = logging.getLogger(__name__)

# Initialize controllers
accounting_controller = AccountingController()
connections_controller = ConnectionsController()

api_routes = Blueprint('api_routes', __name__, url_prefix='/api')

def require_auth() -> None:
    """Check if user is authenticated for API calls"""
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401
    return None

@api_routes.route('/dashboard-data')
def dashboard_data() -> Response:
    """API endpoint for dashboard data"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        # Initialize database adapter
        db_adapter = ProductDatabaseAdapter()

        # Try to get data from database first
        if db_adapter.is_database_available():
            logger.info("Using real database data for API")
            data = db_adapter.get_dashboard_data()
            logger.info(f"Real data loaded successfully: {len(data)} categorias principais")
        else:
            logger.info("Database not available, using empty data for API")
            # Fallback to empty data
            data = get_empty_analytics_data()
            logger.info(f"Empty data generated: {len(data)} categorias principais")

        return jsonify({
            'status': 'success',
            'data': data,
            'timestamp': datetime.now().isoformat(),
            'source': 'database' if db_adapter.is_database_available() else 'mock'
        })
    except Exception as e:
        logger.error(f"Erro ao obter dados do dashboard via API: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/users-data')
def users_data() -> Response:
    """API endpoint for users data"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        data = get_empty_analytics_data()
        return jsonify({
            'status': 'success',
            'data': data['users'],
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erro ao obter dados de usuários via API: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/features-data')
def features_data() -> Response:
    """API endpoint for features data"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        data = get_empty_analytics_data()
        features_by_state = get_features_by_state()

        return jsonify({
            'status': 'success',
            'data': {
                'features': data['features'],
                'features_by_state': features_by_state
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erro ao obter dados de features via API: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/connections-data')
def connections_data() -> Response:
    """API endpoint for connections data"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        # Load real connections data
        connections_records = load_connections_data()
        analytics = get_connections_analytics(connections_records)

        return jsonify({
            'status': 'success',
            'data': analytics,
            'records_count': len(connections_records),
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erro ao obter dados de conexões via API: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/payments-data')
def payments_data() -> Response:
    """API endpoint for payments data"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        data = get_empty_analytics_data()
        return jsonify({
            'status': 'success',
            'data': data['amigo_pay'],
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erro ao obter dados de pagamentos via API: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/agenda-data')
def agenda_data() -> Response:
    """API endpoint for agenda data"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        data = get_empty_analytics_data()
        return jsonify({
            'status': 'success',
            'data': data['agenda'],
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erro ao obter dados de agenda via API: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/medical-records-data')
def medical_records_data() -> Response:
    """API endpoint for medical records data"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        data = get_empty_analytics_data()
        return jsonify({
            'status': 'success',
            'data': {
                'medical_records': data['features']['medical_records'],
                'digital_signature': data['features']['digital_signature'],
                'patients': data['patients']
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erro ao obter dados de prontuários via API: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/accounting-data')
def accounting_data() -> Response:
    """API endpoint for accounting data"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        # Use controller to get accounting data
        api_data = accounting_controller.get_accounting_api_data()
        return jsonify(api_data)
    except Exception as e:
        logger.error(f"Erro ao obter dados de contabilidade via API: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/accounting/metrics')
def accounting_metrics() -> Response:
    """API endpoint for accounting metrics"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        metrics = accounting_controller.get_financial_metrics()
        return jsonify({
            'status': 'success',
            'data': metrics,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erro ao obter métricas de contabilidade: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/accounting/revenue')
def accounting_revenue() -> Response:
    """API endpoint for revenue analysis"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        revenue_analysis = accounting_controller.get_revenue_analysis()
        return jsonify({
            'status': 'success',
            'data': revenue_analysis,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erro ao obter análise de receita: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/accounting/expenses')
def accounting_expenses() -> Response:
    """API endpoint for expense analysis"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        expense_analysis = accounting_controller.get_expense_analysis()
        return jsonify({
            'status': 'success',
            'data': expense_analysis,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erro ao obter análise de despesas: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/accounting/profitability')
def accounting_profitability() -> Response:
    """API endpoint for profitability analysis"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        profitability_analysis = accounting_controller.get_profitability_analysis()
        return jsonify({
            'status': 'success',
            'data': profitability_analysis,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erro ao obter análise de lucratividade: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/analytics-data')
def analytics_data() -> Response:
    """API endpoint for complete analytics data"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        data = get_empty_analytics_data()
        features_by_state = get_features_by_state()

        # Carregar dados reais de contabilidade
        accounting_records = load_accounting_data()
        accounting_analytics = get_accounting_analytics(accounting_records)

        return jsonify({
            'status': 'success',
            'data': {
                'users': data['users'],
                'features': data['features'],
                'connections': data['connections'],
                'accounting': accounting_analytics,
                'amigo_pay': data['amigo_pay'],
                'agenda': data['agenda'],
                'patients': data['patients'],
                'features_by_state': features_by_state
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erro ao obter dados de analytics via API: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/health')
def health() -> Response:
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'product-domain-api',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@api_routes.route('/status')
def status() -> Response:
    """Status endpoint with more details"""
    try:
        # Initialize database adapter to check database status
        db_adapter = ProductDatabaseAdapter()
        db_status = 'connected' if db_adapter.is_database_available() else 'disconnected'

        return jsonify({
            'status': 'operational',
            'service': 'product-domain-api',
            'database': db_status,
            'authenticated_user': session.get('username', 'anonymous'),
            'timestamp': datetime.now().isoformat(),
            'uptime': 'N/A',  # Would need to track actual uptime
            'version': '1.0.0'
        })
    except Exception as e:
        logger.error(f"Erro ao obter status: {e}")
        return jsonify({
            'status': 'error',
            'service': 'product-domain-api',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_routes.route('/metrics')
def metrics() -> Response:
    """Basic metrics endpoint"""
    auth_check = require_auth()
    if auth_check:
        return auth_check

    try:
        data = get_empty_analytics_data()

        # Calculate some basic metrics (all zeros now)
        total_users = data['users']['total_users']
        active_users = data['users']['active_users']
        activity_rate = 0.0

        total_transactions = 0
        total_events = 0

        return jsonify({
            'status': 'success',
            'metrics': {
                'total_users': total_users,
                'active_users': active_users,
                'activity_rate': activity_rate,
                'total_transactions': total_transactions,
                'total_events': total_events,
                'premium_conversion_rate': 0.0
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erro ao obter métricas via API: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500
