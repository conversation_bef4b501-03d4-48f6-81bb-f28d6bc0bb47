"""
Main Routes for Product Domain
"""
from flask import Blueprint, render_template, session, redirect, url_for, jsonify, Response
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Union, Tuple

from services.data_service import get_empty_analytics_data, get_features_by_state, load_accounting_data, get_accounting_analytics, load_connections_data, get_connections_analytics
from services.map_service import (
    generate_brazil_map,
    generate_features_map,
    generate_usage_map,
    generate_combined_users_map
)

logger = logging.getLogger(__name__)

# Constants
DEFAULT_USER = 'Usuário'
ERROR_TEMPLATE = 'error.html'

main_routes = Blueprint('main_routes', __name__)

def require_login() -> None:
    """Check if user is logged in"""
    # Check for both Google Auth and traditional login
    user = session.get('user', {})
    username = session.get('username')

    # Debug logging
    logger.info(f"Session check - user: {user}, username: {username}")

    if not user.get('authenticated', False) and 'username' not in session:
        logger.info("User not authenticated, redirecting to login")
        return redirect(url_for('auth_routes.login'))

    logger.info("User authenticated successfully")
    return None

@main_routes.route('/')
def index() -> str:
    """Dashboard principal"""
    auth_check = require_login()
    if auth_check:
        return auth_check

    try:
        # Gerar dados zerados
        empty_data = get_empty_analytics_data()
        features_by_state = get_features_by_state()

        # Gerar mapas para o dashboard (vazios)
        combined_users_map = generate_combined_users_map({})
        features_map = generate_features_map(features_by_state)

        # Dados completos para o dashboard
        dashboard_data = {
            'data': empty_data,  # Passar todos os dados zerados
            'features_by_state': features_by_state,
            'combined_users_map': combined_users_map,
            'features_map': features_map,
            'current_user': session.get('username', DEFAULT_USER),
            'current_time': datetime.now().strftime('%d/%m/%Y %H:%M')
        }

        return render_template('dashboard.html', **dashboard_data)
    except Exception as e:
        logger.error(f"Erro no dashboard: {e}")
        return render_template(ERROR_TEMPLATE, error=str(e))

@main_routes.route('/users')
def users() -> str:
    """Página de usuários"""
    auth_check = require_login()
    if auth_check:
        return auth_check

    try:
        empty_data = get_empty_analytics_data()

        # Gerar mapa do Brasil (vazio)
        brazil_map_html = generate_brazil_map({})

        # Gerar mapa combinado de usuários (vazio)
        combined_users_map_html = generate_combined_users_map({})

        users_data = {
            'total_users': empty_data['users']['total_users'],
            'active_users': empty_data['users']['active_users'],
            'premium_users': empty_data['users']['premium_users'],
            'free_users': empty_data['users']['free_users'],
            'users_by_device': empty_data['users']['users_by_device'],
            'users_by_specialty': empty_data['users']['users_by_specialty'],
            'users_by_state': empty_data['users']['users_by_state'],
            'age_distribution': empty_data['users']['age_distribution'],
            'growth_by_month': empty_data['users']['growth_by_month'],
            'brazil_map_html': brazil_map_html,
            'combined_users_map_html': combined_users_map_html,
            'current_user': session.get('username', 'Usuário')
        }

        return render_template('usuarios.html', data={'users': users_data})
    except Exception as e:
        logger.error(f"Erro na página de usuários: {e}")
        return render_template('error.html', error=str(e))



@main_routes.route('/connections')
def connections() -> str:
    """Página de conexões com dados reais"""
    auth_check = require_login()
    if auth_check:
        return auth_check

    try:
        # Carregar dados reais de conexões
        connections_records = load_connections_data()
        analytics = get_connections_analytics(connections_records)

        connections_data = {
            'current_user': session.get('username', 'Usuário'),
            'records_count': len(connections_records),
            'analytics': analytics
        }

        return render_template('rede_amigo_new.html', **connections_data)
    except Exception as e:
        logger.error(f"Erro na página de conexões: {e}")
        return render_template('error.html', error=str(e))

@main_routes.route('/payments')
def payments() -> str:
    """Página de pagamentos"""
    auth_check = require_login()
    if auth_check:
        return auth_check

    try:
        empty_data = get_empty_analytics_data()

        payments_data = {
            'data': empty_data,  # Adicionar dados zerados
            'transactions': empty_data['amigo_pay']['transactions'],
            'accounts': empty_data['amigo_pay']['accounts'],
            'transaction_volume': empty_data['amigo_pay']['transaction_volume'],
            'transaction_count': empty_data['amigo_pay']['transaction_count'],
            'current_user': session.get('username', 'Usuário')
        }

        return render_template('amigo_pay.html', **payments_data)
    except Exception as e:
        logger.error(f"Erro na página de pagamentos: {e}")
        return render_template('error.html', error=str(e))

@main_routes.route('/agenda')
def agenda() -> str:
    """Página de agenda"""
    auth_check = require_login()
    if auth_check:
        return auth_check

    try:
        empty_data = get_empty_analytics_data()

        agenda_data = {
            'data': empty_data,  # Adicionar dados zerados
            'event_types': empty_data['agenda']['event_types'],
            'attendance_types': empty_data['agenda']['attendance_types'],
            'status_percentages': empty_data['agenda']['status_percentages'],
            'events_by_month': empty_data['agenda']['events_by_month'],
            'current_user': session.get('username', 'Usuário')
        }

        return render_template('agenda.html', **agenda_data)
    except Exception as e:
        logger.error(f"Erro na página de agenda: {e}")
        return render_template('error.html', error=str(e))

@main_routes.route('/medical_records')
def medical_records() -> str:
    """Página de prontuários"""
    auth_check = require_login()
    if auth_check:
        return auth_check

    try:
        empty_data = get_empty_analytics_data()

        medical_records_data = {
            'data': empty_data,  # Adicionar dados zerados
            'medical_records': empty_data['features']['medical_records'],
            'digital_signature': empty_data['features']['digital_signature'],
            'patients': empty_data['patients'],
            'current_user': session.get('username', 'Usuário')
        }

        return render_template('prontuarios.html', **medical_records_data)
    except Exception as e:
        logger.error(f"Erro na página de prontuários: {e}")
        return render_template('error.html', error=str(e))

@main_routes.route('/accounting')
def accounting() -> str:
    """Página de contabilidade com dados reais"""
    auth_check = require_login()
    if auth_check:
        return auth_check

    try:
        # Carregar dados reais de contabilidade
        accounting_records = load_accounting_data()
        analytics = get_accounting_analytics(accounting_records)

        accounting_data = {
            'current_user': session.get('username', 'Usuário'),
            'records_count': len(accounting_records),
            'analytics': analytics
        }

        return render_template('contabilidade.html', **accounting_data)
    except Exception as e:
        logger.error(f"Erro na página de contabilidade: {e}")
        return render_template('error.html', error=str(e))



@main_routes.route('/amigo_intelligence')
def amigo_intelligence() -> str:
    """Amigo Intelligence analytics page"""
    auth_check = require_login()
    if auth_check:
        return auth_check

    try:
        # Generate empty data for AI usage
        empty_data = get_empty_analytics_data()

        # AI specific data
        ai_data = {
            'data': empty_data,
            'current_user': session.get('username', DEFAULT_USER),
            'current_time': datetime.now().strftime('%d/%m/%Y %H:%M')
        }

        return render_template('amigo_intelligence.html', **ai_data)
    except Exception as e:
        logger.error(f"Erro na página Amigo Intelligence: {e}")
        return render_template(ERROR_TEMPLATE, error=str(e))


@main_routes.route('/documentacao')
def documentacao() -> str:
    """Documentation page"""
    auth_check = require_login()
    if auth_check:
        return auth_check

    try:
        # Documentation data
        doc_data = {
            'current_user': session.get('username', DEFAULT_USER),
            'current_time': datetime.now().strftime('%d/%m/%Y %H:%M')
        }

        return render_template('documentacao.html', **doc_data)
    except Exception as e:
        logger.error(f"Erro na página de documentação: {e}")
        return render_template(ERROR_TEMPLATE, error=str(e))


@main_routes.route('/health')
def health() -> Response:
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': 'product-domain'
    })
