"""
Health Check Routes for Product Domain
Health check endpoints for container monitoring
"""

from flask import Blueprint, jsonify
import logging
import os
from datetime import datetime

logger = logging.getLogger(__name__)

health_routes = Blueprint('health_routes', __name__)

@health_routes.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint for container monitoring
    
    Returns:
        JSON response with health status
    """
    try:
        # Basic health checks
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'domain': 'product',
            'version': '2.0.0',
            'checks': {
                'application': 'ok',
                'data_directory': 'ok',
                'logs_directory': 'ok'
            }
        }
        
        # Check if data directory exists
        data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
        if not os.path.exists(data_dir):
            health_status['checks']['data_directory'] = 'warning'
            health_status['status'] = 'degraded'
        
        # Check if logs directory exists
        logs_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
        if not os.path.exists(logs_dir):
            health_status['checks']['logs_directory'] = 'warning'
            health_status['status'] = 'degraded'
        
        return jsonify(health_status), 200
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'timestamp': datetime.now().isoformat(),
            'domain': 'product',
            'error': str(e)
        }), 500

@health_routes.route('/ready', methods=['GET'])
def readiness_check():
    """
    Readiness check endpoint for container orchestration
    
    Returns:
        JSON response with readiness status
    """
    try:
        # More comprehensive readiness checks
        readiness_status = {
            'ready': True,
            'timestamp': datetime.now().isoformat(),
            'domain': 'product',
            'checks': {
                'configuration': 'ok',
                'data_access': 'ok',
                'shared_components': 'ok'
            }
        }
        
        # Check if shared directory is accessible
        shared_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'shared')
        if not os.path.exists(shared_dir):
            readiness_status['checks']['shared_components'] = 'fail'
            readiness_status['ready'] = False
        
        status_code = 200 if readiness_status['ready'] else 503
        return jsonify(readiness_status), status_code
        
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return jsonify({
            'ready': False,
            'timestamp': datetime.now().isoformat(),
            'domain': 'product',
            'error': str(e)
        }), 503
