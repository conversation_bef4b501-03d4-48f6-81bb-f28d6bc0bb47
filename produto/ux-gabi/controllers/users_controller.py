"""
Users Controller
Handles business logic for user operations
"""
from flask import session, render_template
import logging
from typing import Dict, Any

from services.data_service import get_empty_analytics_data
from services.map_service import generate_brazil_map, generate_combined_users_map

logger = logging.getLogger(__name__)

class UsersController:
    """Controller for user operations"""
    
    def __init__(self):
        self.default_user = 'Usuário'
        self.error_template = 'error.html'
    
    def get_users_data(self) -> Dict[str, Any]:
        """Get users data (currently using empty data)"""
        try:
            empty_data = get_empty_analytics_data()

            # Gerar mapa do Brasil (vazio)
            brazil_map_html = generate_brazil_map({})

            # Gerar mapa combinado de usuários (vazio)
            combined_users_map_html = generate_combined_users_map({})

            users_data = {
                'total_users': empty_data['users']['total_users'],
                'active_users': empty_data['users']['active_users'],
                'premium_users': empty_data['users']['premium_users'],
                'free_users': empty_data['users']['free_users'],
                'users_by_device': empty_data['users']['users_by_device'],
                'users_by_specialty': empty_data['users']['users_by_specialty'],
                'users_by_state': empty_data['users']['users_by_state'],
                'age_distribution': empty_data['users']['age_distribution'],
                'growth_by_month': empty_data['users']['growth_by_month'],
                'brazil_map_html': brazil_map_html,
                'combined_users_map_html': combined_users_map_html,
                'current_user': session.get('username', self.default_user)
            }

            return users_data
        except Exception as e:
            logger.error(f"Erro ao obter dados de usuários: {e}")
            raise
    
    def render_users_page(self) -> str:
        """Render users page"""
        try:
            users_data = self.get_users_data()
            return render_template('usuarios.html', data={'users': users_data})
        except Exception as e:
            logger.error(f"Erro na página de usuários: {e}")
            return render_template(self.error_template, error=str(e))
    
    def get_user_analytics(self) -> Dict[str, Any]:
        """Get user analytics data"""
        try:
            users_data = self.get_users_data()
            
            # Extract analytics metrics
            analytics = {
                'user_growth': {
                    'total_users': users_data['total_users'],
                    'active_users': users_data['active_users'],
                    'growth_rate': 0.0  # Calculate based on historical data
                },
                'user_segmentation': {
                    'premium_users': users_data['premium_users'],
                    'free_users': users_data['free_users'],
                    'premium_conversion_rate': 0.0
                },
                'geographic_distribution': users_data['users_by_state'],
                'device_usage': users_data['users_by_device'],
                'specialty_distribution': users_data['users_by_specialty']
            }
            
            return analytics
        except Exception as e:
            logger.error(f"Erro ao obter analytics de usuários: {e}")
            raise
