"""
Connections Controller
Handles business logic for connections/network operations
"""
from flask import session, render_template, jsonify
from datetime import datetime
import logging
from typing import Dict, Any, List

from services.data_service import load_connections_data, get_connections_analytics

logger = logging.getLogger(__name__)

class ConnectionsController:
    """Controller for connections/network operations"""
    
    def __init__(self):
        self.default_user = 'Usuário'
        self.error_template = 'error.html'
    
    def get_connections_data(self) -> Dict[str, Any]:
        """Get connections data with real data from CSV"""
        try:
            # Carregar dados reais de conexões
            connections_records = load_connections_data()
            analytics = get_connections_analytics(connections_records)

            connections_data = {
                'current_user': session.get('username', self.default_user),
                'records_count': len(connections_records),
                'analytics': analytics
            }

            logger.info(f"Loaded {len(connections_records)} connection records")
            return connections_data
        except Exception as e:
            logger.error(f"Erro ao obter dados de conexões: {e}")
            raise
    
    def render_connections_page(self) -> str:
        """Render connections page with network analysis"""
        try:
            connections_data = self.get_connections_data()
            return render_template('rede_amigo_new.html', **connections_data)
        except Exception as e:
            logger.error(f"Erro na página de conexões: {e}")
            return render_template(self.error_template, error=str(e))
    
    def get_connections_api_data(self) -> Dict[str, Any]:
        """Get connections data for API endpoints"""
        try:
            connections_records = load_connections_data()
            analytics = get_connections_analytics(connections_records)

            return {
                'status': 'success',
                'data': analytics,
                'records_count': len(connections_records),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Erro ao obter dados de conexões via API: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_network_metrics(self) -> Dict[str, Any]:
        """Get specific network metrics"""
        try:
            connections_data = self.get_connections_data()
            analytics = connections_data['analytics']
            
            # Extract key network metrics
            network_metrics = {
                'total_users': analytics.get('user_behavior', {}).get('total_unique_users', 0),
                'active_users': analytics.get('network_metrics', {}).get('active_users', 0),
                'total_connections': analytics.get('network_metrics', {}).get('total_connections', 0),
                'accepted_connections': analytics.get('network_metrics', {}).get('accepted_connections', 0),
                'pending_connections': analytics.get('network_metrics', {}).get('pending_connections', 0),
                'network_density': analytics.get('network_metrics', {}).get('density', 0),
                'avg_connections_per_user': analytics.get('network_metrics', {}).get('avg_connections_per_user', 0)
            }
            
            return network_metrics
        except Exception as e:
            logger.error(f"Erro ao obter métricas de rede: {e}")
            raise
