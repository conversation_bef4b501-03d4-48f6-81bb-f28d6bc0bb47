"""
Auth Controller for Product Domain
"""
from flask import session, redirect, url_for
import logging

logger = logging.getLogger(__name__)

class AuthController:
    """Controller for authentication logic"""
    
    def require_login(self):
        """Check if user is logged in"""
        # Check for both Google Auth and traditional login
        user = session.get('user', {})
        if not user.get('authenticated', False) and 'username' not in session:
            return redirect(url_for('auth_routes.login'))
        return None
    
    def is_authenticated(self):
        """Check if user is authenticated"""
        user = session.get('user', {})
        return user.get('authenticated', False) or 'username' in session
    
    def get_current_user(self):
        """Get current user information"""
        user = session.get('user', {})
        if user.get('authenticated', False):
            return {
                'name': user.get('name', 'Usu<PERSON><PERSON>'),
                'email': user.get('email', ''),
                'auth_method': user.get('auth_method', 'google')
            }
        elif 'username' in session:
            return {
                'name': session.get('username', 'Usu<PERSON>rio'),
                'email': '',
                'auth_method': 'traditional'
            }
        return None
