"""
Dashboard Controller
Handles business logic for dashboard operations
"""
from flask import session, render_template
from datetime import datetime
import logging
from typing import Dict, Any, Optional

from services.data_service import get_empty_analytics_data, get_features_by_state
from services.map_service import generate_combined_users_map, generate_features_map

logger = logging.getLogger(__name__)

class DashboardController:
    """Controller for dashboard operations"""
    
    def __init__(self):
        self.default_user = 'Usuário'
        self.error_template = 'error.html'
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get dashboard data"""
        try:
            # Gerar dados zerados
            empty_data = get_empty_analytics_data()
            features_by_state = get_features_by_state()

            # Gerar mapas para o dashboard (vazios)
            combined_users_map = generate_combined_users_map({})
            features_map = generate_features_map(features_by_state)

            # Dados completos para o dashboard
            dashboard_data = {
                'data': empty_data,
                'features_by_state': features_by_state,
                'combined_users_map': combined_users_map,
                'features_map': features_map,
                'current_user': session.get('username', self.default_user),
                'current_time': datetime.now().strftime('%d/%m/%Y %H:%M')
            }

            return dashboard_data
        except Exception as e:
            logger.error(f"Erro ao obter dados do dashboard: {e}")
            raise
    
    def render_dashboard(self) -> str:
        """Render dashboard page"""
        try:
            dashboard_data = self.get_dashboard_data()
            return render_template('dashboard.html', **dashboard_data)
        except Exception as e:
            logger.error(f"Erro no dashboard: {e}")
            return render_template(self.error_template, error=str(e))
