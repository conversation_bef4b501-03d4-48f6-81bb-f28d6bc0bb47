"""
Accounting Controller
Handles business logic for accounting/financial operations
"""
from flask import session, render_template, jsonify
from datetime import datetime
import logging
from typing import Dict, Any, List, Optional

# Simplified imports - removed problematic dependencies

# Backward compatibility imports
from services.data_service import load_accounting_data, get_accounting_analytics

logger = logging.getLogger(__name__)

class AccountingController:
    """Controller for accounting/financial operations"""

    def __init__(self):
        self.default_user = 'Usuário'
        self.error_template = 'error.html'
    
    def get_accounting_data(self) -> Dict[str, Any]:
        """Get accounting data with real data from CSV"""
        try:
            # NEW: Use repository pattern
            accounting_records = self.accounting_repository.find_all()

            # BACKWARD COMPATIBILITY: Convert to legacy format for existing analytics
            legacy_records = self.accounting_repository.get_raw_data()
            analytics = get_accounting_analytics(legacy_records)

            accounting_data = {
                'current_user': session.get('username', self.default_user),
                'records_count': len(accounting_records),
                'analytics': analytics
            }

            logger.info(f"Loaded {len(accounting_records)} accounting records via repository")
            return accounting_data
        except Exception as e:
            logger.error(f"Erro ao obter dados de contabilidade: {e}")
            # Fallback to legacy method for backward compatibility
            try:
                accounting_records = load_accounting_data()
                analytics = get_accounting_analytics(accounting_records)
                return {
                    'current_user': session.get('username', self.default_user),
                    'records_count': len(accounting_records),
                    'analytics': analytics
                }
            except Exception as fallback_error:
                logger.error(f"Fallback também falhou: {fallback_error}")
                raise Exception(f"Failed to load accounting data: {e}")
    
    def render_accounting_page(self) -> str:
        """Render accounting page with financial analysis"""
        try:
            accounting_data = self.get_accounting_data()
            return render_template('contabilidade.html', **accounting_data)
        except Exception as e:
            logger.error(f"Erro na página de contabilidade: {e}")
            return render_template(self.error_template, error=str(e))
    
    def get_accounting_api_data(self) -> Dict[str, Any]:
        """Get accounting data for API endpoints"""
        try:
            accounting_records = load_accounting_data()
            analytics = get_accounting_analytics(accounting_records)

            return {
                'status': 'success',
                'data': analytics,
                'records_count': len(accounting_records),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Erro ao obter dados de contabilidade via API: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_financial_metrics(self) -> Dict[str, Any]:
        """Get specific financial metrics"""
        try:
            accounting_data = self.get_accounting_data()
            analytics = accounting_data['analytics']
            
            # Extract key financial metrics
            financial_metrics = {
                'total_revenue': analytics.get('financial_summary', {}).get('total_revenue', 0),
                'total_expenses': analytics.get('financial_summary', {}).get('total_expenses', 0),
                'net_profit': analytics.get('financial_summary', {}).get('net_profit', 0),
                'profit_margin': analytics.get('financial_summary', {}).get('profit_margin', 0),
                'monthly_revenue': analytics.get('monthly_trends', {}).get('revenue', []),
                'monthly_expenses': analytics.get('monthly_trends', {}).get('expenses', []),
                'top_revenue_categories': analytics.get('category_analysis', {}).get('top_revenue', []),
                'top_expense_categories': analytics.get('category_analysis', {}).get('top_expenses', [])
            }
            
            return financial_metrics
        except Exception as e:
            logger.error(f"Erro ao obter métricas financeiras: {e}")
            raise
    
    def get_revenue_analysis(self) -> Dict[str, Any]:
        """Get detailed revenue analysis"""
        try:
            accounting_data = self.get_accounting_data()
            analytics = accounting_data['analytics']
            
            revenue_analysis = {
                'total_revenue': analytics.get('financial_summary', {}).get('total_revenue', 0),
                'monthly_revenue': analytics.get('monthly_trends', {}).get('revenue', []),
                'revenue_by_category': analytics.get('category_analysis', {}).get('revenue_by_category', {}),
                'revenue_growth_rate': self._calculate_growth_rate(
                    analytics.get('monthly_trends', {}).get('revenue', [])
                ),
                'top_revenue_sources': analytics.get('category_analysis', {}).get('top_revenue', [])
            }
            
            return revenue_analysis
        except Exception as e:
            logger.error(f"Erro ao obter análise de receita: {e}")
            raise
    
    def get_expense_analysis(self) -> Dict[str, Any]:
        """Get detailed expense analysis"""
        try:
            accounting_data = self.get_accounting_data()
            analytics = accounting_data['analytics']
            
            expense_analysis = {
                'total_expenses': analytics.get('financial_summary', {}).get('total_expenses', 0),
                'monthly_expenses': analytics.get('monthly_trends', {}).get('expenses', []),
                'expenses_by_category': analytics.get('category_analysis', {}).get('expenses_by_category', {}),
                'expense_growth_rate': self._calculate_growth_rate(
                    analytics.get('monthly_trends', {}).get('expenses', [])
                ),
                'top_expense_categories': analytics.get('category_analysis', {}).get('top_expenses', [])
            }
            
            return expense_analysis
        except Exception as e:
            logger.error(f"Erro ao obter análise de despesas: {e}")
            raise
    
    def get_profitability_analysis(self) -> Dict[str, Any]:
        """Get profitability analysis"""
        try:
            accounting_data = self.get_accounting_data()
            analytics = accounting_data['analytics']
            
            profitability_analysis = {
                'net_profit': analytics.get('financial_summary', {}).get('net_profit', 0),
                'profit_margin': analytics.get('financial_summary', {}).get('profit_margin', 0),
                'monthly_profit': self._calculate_monthly_profit(analytics),
                'profit_trend': self._calculate_profit_trend(analytics),
                'break_even_analysis': self._calculate_break_even(analytics)
            }
            
            return profitability_analysis
        except Exception as e:
            logger.error(f"Erro ao obter análise de lucratividade: {e}")
            raise
    
    def _calculate_growth_rate(self, monthly_data: List[Dict[str, Any]]) -> float:
        """Calculate growth rate from monthly data"""
        try:
            if len(monthly_data) < 2:
                return 0.0
            
            first_month = monthly_data[0].get('value', 0)
            last_month = monthly_data[-1].get('value', 0)
            
            if first_month == 0:
                return 0.0
            
            growth_rate = ((last_month - first_month) / first_month) * 100
            return round(growth_rate, 2)
        except Exception:
            return 0.0
    
    def _calculate_monthly_profit(self, analytics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Calculate monthly profit from revenue and expenses"""
        try:
            revenue_data = analytics.get('monthly_trends', {}).get('revenue', [])
            expense_data = analytics.get('monthly_trends', {}).get('expenses', [])
            
            monthly_profit = []
            for i in range(min(len(revenue_data), len(expense_data))):
                revenue = revenue_data[i].get('value', 0)
                expense = expense_data[i].get('value', 0)
                profit = revenue - expense
                
                monthly_profit.append({
                    'month': revenue_data[i].get('month', f'Mês {i+1}'),
                    'value': profit
                })
            
            return monthly_profit
        except Exception:
            return []
    
    def _calculate_profit_trend(self, analytics: Dict[str, Any]) -> str:
        """Calculate profit trend (increasing, decreasing, stable)"""
        try:
            monthly_profit = self._calculate_monthly_profit(analytics)
            
            if len(monthly_profit) < 2:
                return 'stable'
            
            first_profit = monthly_profit[0].get('value', 0)
            last_profit = monthly_profit[-1].get('value', 0)
            
            if last_profit > first_profit * 1.05:  # 5% threshold
                return 'increasing'
            elif last_profit < first_profit * 0.95:  # 5% threshold
                return 'decreasing'
            else:
                return 'stable'
        except Exception:
            return 'stable'
    
    def _calculate_break_even(self, analytics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate break-even analysis"""
        try:
            total_revenue = analytics.get('financial_summary', {}).get('total_revenue', 0)
            total_expenses = analytics.get('financial_summary', {}).get('total_expenses', 0)
            
            # Simplified break-even calculation
            break_even_point = total_expenses
            current_revenue = total_revenue
            revenue_needed = max(0, break_even_point - current_revenue)
            
            return {
                'break_even_point': break_even_point,
                'current_revenue': current_revenue,
                'revenue_needed': revenue_needed,
                'is_profitable': current_revenue > break_even_point
            }
        except Exception:
            return {
                'break_even_point': 0,
                'current_revenue': 0,
                'revenue_needed': 0,
                'is_profitable': False
            }
