"""
Google OAuth Authentication Controller for Product Domain
Handles Google OAuth login with domain restriction to @amigotech.com.br
"""

import os
import logging
from flask import Blueprint, request, redirect, url_for, session, flash, jsonify
from authlib.integrations.flask_client import OAuth
from authlib.common.errors import AuthlibBaseError

logger = logging.getLogger(__name__)

# Create blueprint
google_auth_bp = Blueprint('google_auth', __name__, url_prefix='/auth/google')

# Initialize OAuth
oauth = OAuth()

def init_google_oauth(app):
    """Initialize Google OAuth with the Flask app"""
    oauth.init_app(app)
    
    # Register Google OAuth provider with manual configuration
    google = oauth.register(
        name='google',
        client_id=os.getenv('GOOGLE_AUTH_CLIENT_ID'),
        client_secret=os.getenv('GOOGLE_AUTH_CLIENT_SECRET'),
        authorize_url='https://accounts.google.com/o/oauth2/auth',
        authorize_params=None,
        access_token_url='https://oauth2.googleapis.com/token',
        access_token_params=None,
        refresh_token_url=None,
        client_kwargs={
            'scope': 'openid email profile'
        }
    )
    
    return google

@google_auth_bp.route('/login')
def login():
    """Initiate Google OAuth login"""
    try:
        # Get the Google OAuth client
        google = oauth.google
        
        # Generate the authorization URL
        redirect_uri = url_for('google_auth.callback', _external=True)
        
        logger.info(f"Initiating Google OAuth login with redirect_uri: {redirect_uri}")
        
        return google.authorize_redirect(redirect_uri)
        
    except Exception as e:
        logger.error(f"Error initiating Google OAuth: {e}")
        flash('Erro ao iniciar login com Google. Tente novamente.', 'error')
        return redirect(url_for('main_routes.index'))

@google_auth_bp.route('/callback')
def callback():
    """Handle Google OAuth callback"""
    try:
        # Get the Google OAuth client
        google = oauth.google
        
        # Get the authorization token
        token = google.authorize_access_token()
        
        if not token:
            logger.error("No token received from Google")
            flash('Erro na autenticação. Tente novamente.', 'error')
            return redirect(url_for('main_routes.index'))
        
        # Get user info from Google
        user_info = token.get('userinfo')
        if not user_info:
            # Try to get user info from the token
            user_info = google.parse_id_token(token)
        
        if not user_info:
            logger.error("No user info received from Google")
            flash('Erro ao obter informações do usuário. Tente novamente.', 'error')
            return redirect(url_for('main_routes.index'))

        # Extract user data
        email = user_info.get('email', '')
        name = user_info.get('name', '')
        picture = user_info.get('picture', '')

        logger.info(f"Google OAuth callback received for user: {email}")

        # Check if email domain is allowed
        if not email.endswith('@amigotech.com.br'):
            logger.warning(f"Login attempt with unauthorized domain: {email}")
            flash('Acesso restrito a usuários @amigotech.com.br', 'error')
            return redirect(url_for('main_routes.index'))
        
        # Store user info in session
        session['user'] = {
            'email': email,
            'name': name,
            'picture': picture,
            'authenticated': True,
            'auth_method': 'google'
        }
        
        # Log successful login
        logger.info(f"Successful Google OAuth login for: {email}")
        flash(f'Login realizado com sucesso! Bem-vindo, {name}', 'success')
        
        # Redirect to dashboard
        return redirect(url_for('main_routes.index'))

    except AuthlibBaseError as e:
        logger.error(f"Authlib error in Google OAuth callback: {e}")
        flash('Erro na autenticação OAuth. Tente novamente.', 'error')
        return redirect(url_for('main_routes.index'))

    except Exception as e:
        logger.error(f"Unexpected error in Google OAuth callback: {e}")
        flash('Erro inesperado na autenticação. Tente novamente.', 'error')
        return redirect(url_for('main_routes.index'))

@google_auth_bp.route('/logout')
def logout():
    """Logout user"""
    try:
        user_email = session.get('user', {}).get('email', 'Unknown')
        
        # Clear session
        session.clear()
        
        logger.info(f"User logged out: {user_email}")
        flash('Logout realizado com sucesso!', 'info')
        
        return redirect(url_for('main_routes.index'))

    except Exception as e:
        logger.error(f"Error during logout: {e}")
        flash('Erro durante logout.', 'error')
        return redirect(url_for('main_routes.index'))

@google_auth_bp.route('/status')
def status():
    """Get authentication status"""
    try:
        user = session.get('user', {})
        is_authenticated = user.get('authenticated', False)
        
        return jsonify({
            'authenticated': is_authenticated,
            'user': {
                'email': user.get('email', ''),
                'name': user.get('name', ''),
                'picture': user.get('picture', '')
            } if is_authenticated else None
        })
        
    except Exception as e:
        logger.error(f"Error getting auth status: {e}")
        return jsonify({'error': 'Failed to get auth status'}), 500

def require_auth(f):
    """Decorator to require authentication"""
    from functools import wraps
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = session.get('user', {})
        if not user.get('authenticated', False):
            flash('Acesso negado. Faça login para continuar.', 'error')
            return redirect(url_for('google_auth.login'))
        return f(*args, **kwargs)
    return decorated_function

def require_amigotech_domain(f):
    """Decorator to require @amigotech.com.br domain"""
    from functools import wraps
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = session.get('user', {})
        email = user.get('email', '')
        
        if not email.endswith('@amigotech.com.br'):
            flash('Acesso restrito a usuários @amigotech.com.br', 'error')
            return redirect(url_for('google_auth.login'))
        return f(*args, **kwargs)
    return decorated_function
