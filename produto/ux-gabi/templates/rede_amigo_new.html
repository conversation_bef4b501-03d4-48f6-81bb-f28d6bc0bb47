{% extends 'base.html' %}

{% block title %}Rede de Conexões - Amigo One Analytics{% endblock %}

{% block header %}Análise da Rede de Conexões{% endblock %}

{% block styles %}
<style>
    /* Estilos para visualização de rede */
    .network-container {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        position: relative;
        overflow: hidden;
    }

    .node {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .node:hover {
        stroke-width: 3px;
    }

    .link {
        stroke: rgba(0, 122, 255, 0.3);
        stroke-width: 1px;
        transition: all 0.3s ease;
    }

    .link.highlighted {
        stroke: #007AFF;
        stroke-width: 2px;
    }

    .network-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 8px;
        padding: 8px;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 20px;
        border: none;
    }

    .status-accepted { background-color: #3b82f6; }
    .status-pending { background-color: #6b7280; }
    .status-refused { background-color: #374151; }

    .loading-spinner {
        border: 3px solid #f3f4f6;
        border-top: 3px solid #007AFF;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-gray-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20a3 3 0 01-3-3v-2a3 3 0 013-3h3a3 3 0 013 3v2a3 3 0 01-3 3H7zM8 9a3 3 0 116 0 3 3 0 01-6 0z"></path>
        </svg>
    </div>

    <div class="relative z-10">
        <div class="w-full">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">REDE DE CONEXÕES</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Análise da Rede de Conexões</h1>
            <p class="text-gray-600 mb-6">Dashboard com dados reais, análise de rede social e visualizações interativas.</p>



            <div class="flex flex-wrap gap-3">
                <button id="refresh-data" class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Atualizar Dados
                </button>
                <button id="network-360" class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Análise 360°
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Diagrama de Força de Rede Principal - OCULTO -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6" style="display: none;">
    <div class="flex justify-between items-center mb-4">
        <div>
            <h2 class="text-xl font-semibold text-gray-900">Diagrama de Rede</h2>
            <p id="network-coverage-info" class="text-xs text-gray-500 mt-1">Carregando informações...</p>
        </div>
        <div class="flex items-center space-x-3">
            <div class="flex items-center space-x-2">
                <label for="network-user-limit" class="text-xs text-gray-600">Usuários:</label>
                <select id="network-user-limit" class="text-sm border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="50">50 usuários</option>
                    <option value="100" selected>100 usuários</option>
                    <option value="200">200 usuários</option>
                    <option value="500">500 usuários</option>
                    <option value="1000">1000 usuários</option>
                    <option value="all">Todos os usuários</option>
                </select>
            </div>
            <select id="network-filter" class="text-sm border border-gray-200 rounded-md px-3 py-1 bg-white">
                <option value="all">Todas as Conexões</option>
                <option value="accepted">Apenas Aceitas</option>
                <option value="pending">Apenas Pendentes</option>
                <option value="refused">Apenas Rejeitadas</option>
                <option value="top-users">Top 15 Usuários</option>
            </select>
            <button id="fullscreen-network" class="text-systemBlue hover:text-blue-600">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                </svg>
            </button>
        </div>
    </div>

    <div class="network-container" style="height: 600px; position: relative;">
        <div id="network-loading" class="absolute inset-0 flex items-center justify-center bg-gray-50">
            <div class="text-center">
                <div class="loading-spinner mx-auto mb-4"></div>
                <p class="text-gray-600">Carregando diagrama de rede...</p>
            </div>
        </div>

        <div id="network-error" class="hidden absolute inset-0 flex items-center justify-center bg-gray-50">
            <div class="text-center text-gray-600">
                <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <p id="network-error-message">Erro ao carregar diagrama</p>
            </div>
        </div>

        <svg id="network-diagram" class="hidden w-full h-full"></svg>

        <div class="network-controls">
            <div class="flex flex-col space-y-2">
                <button id="zoom-in" class="p-1 text-gray-600 hover:text-systemBlue">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </button>
                <button id="zoom-out" class="p-1 text-gray-600 hover:text-systemBlue">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6"></path>
                    </svg>
                </button>
                <button id="reset-zoom" class="p-1 text-gray-600 hover:text-systemBlue">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <div class="mt-4 text-sm text-gray-600">
        <p><strong>Legenda:</strong>
            <span class="inline-flex items-center ml-2">
                <span class="w-3 h-3 bg-blue-500 rounded-full mr-1"></span>Conexões Aceitas
            </span>
            <span class="inline-flex items-center ml-4">
                <span class="w-3 h-3 bg-gray-500 rounded-full mr-1"></span>Conexões Pendentes
            </span>
            <span class="inline-flex items-center ml-4">
                <span class="w-3 h-3 bg-gray-700 rounded-full mr-1"></span>Conexões Rejeitadas
            </span>
        </p>
    </div>
</div>

<!-- Métricas de Rede -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Total de Conexões -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Total de Conexões</p>
        </div>
        <p id="metric-total-connections" class="text-3xl font-semibold text-gray-800 mb-1">0</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Registros carregados</span>
            <span id="metric-total-change" class="ml-auto text-systemBlue font-medium">--</span>
        </div>
    </div>

    <!-- Conexões Aceitas -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Conexões Aceitas</p>
        </div>
        <p id="metric-accepted-connections" class="text-3xl font-semibold text-gray-800 mb-1">0</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Status: ACCEPTED</span>
            <span id="metric-accepted-percentage" class="ml-auto text-blue-600 font-medium">0%</span>
        </div>
    </div>

    <!-- Conexões Pendentes -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Conexões Pendentes</p>
        </div>
        <p id="metric-pending-connections" class="text-3xl font-semibold text-gray-800 mb-1">0</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Status: PENDING</span>
            <span id="metric-pending-percentage" class="ml-auto text-gray-600 font-medium">0%</span>
        </div>
    </div>

    <!-- Taxa de Aceitação -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Taxa de Aceitação</p>
        </div>
        <p id="metric-acceptance-rate" class="text-3xl font-semibold text-gray-800 mb-1">0%</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Aceitas / Total</span>
            <span id="metric-acceptance-trend" class="ml-auto text-systemBlue font-medium">--</span>
        </div>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Métricas de Rede -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-gray-900">Métricas da Rede</h2>
            <div class="flex items-center space-x-2">
                <button id="network-metrics-tooltip" class="text-gray-400 hover:text-systemBlue" title="Informações sobre as métricas">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </button>
                <button id="refresh-network-metrics" class="text-systemBlue hover:text-blue-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
            </div>
        </div>

        <div class="space-y-4">
            <!-- Densidade Atual vs Potencial -->
            <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Densidade da Rede</span>
                    <div class="text-right">
                        <div class="text-sm font-bold text-systemBlue" id="network-density">0.000</div>
                        <div class="text-xs text-blue-600" id="potential-density">Potencial: 0.000</div>
                    </div>
                </div>
                <div class="space-y-1">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="network-density-bar" class="bg-systemBlue h-2 rounded-full" style="width: 0%"></div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-1">
                        <div id="potential-density-bar" class="bg-blue-400 h-1 rounded-full" style="width: 0%"></div>
                    </div>
                </div>
                <p class="text-xs text-gray-500 mt-2">
                    Atual: conexões aceitas | Potencial: se todos os pendentes fossem aceitos
                    <span id="density-increase" class="text-blue-600 font-medium"></span>
                </p>
            </div>

            <!-- Usuários Ativos -->
            <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Usuários Ativos</span>
                    <div class="text-right">
                        <div class="text-sm font-bold text-blue-600" id="active-users">0</div>
                        <div class="text-xs text-gray-500" id="active-users-percentage">0%</div>
                    </div>
                </div>
                <p class="text-xs text-gray-500 mt-2">Usuários com pelo menos 1 conexão aceita</p>
            </div>

            <!-- Usuários Apenas Pendentes -->
            <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Usuários Apenas Pendentes</span>
                    <div class="text-right">
                        <div class="text-sm font-bold text-gray-600" id="pending-only-users">0</div>
                        <div class="text-xs text-gray-500" id="pending-only-percentage">0%</div>
                    </div>
                </div>
                <p class="text-xs text-gray-500 mt-2">Usuários com convites pendentes mas sem aceitações</p>
            </div>

            <!-- Grau Médio -->
            <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Grau Médio</span>
                    <span id="avg-degree" class="text-sm font-bold text-systemBlue">0.0</span>
                </div>
                <p class="text-xs text-gray-500 mt-2">Número médio de conexões aceitas por usuário</p>
            </div>

            <!-- Total de Usuários -->
            <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Total de Usuários</span>
                    <span id="total-nodes" class="text-sm font-bold text-systemBlue">0</span>
                </div>
                <p class="text-xs text-gray-500 mt-2">Usuários únicos na rede</p>
            </div>
        </div>

        <div class="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100">
            <h4 class="text-sm font-medium text-gray-800 mb-2">Insights da Rede</h4>
            <div id="network-insights" class="text-xs text-gray-600 space-y-1">
                <p>• Carregando insights...</p>
            </div>
        </div>
    </div>


</div>

<!-- Top Conectores -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-gray-900">Top Conectores da Rede</h2>
        <div class="flex items-center space-x-2">
            <select id="top-connectors-filter" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                <option value="total">Por Total de Conexões</option>
                <option value="accepted">Por Conexões Aceitas</option>
                <option value="rate">Por Taxa de Aceitação</option>
            </select>
        </div>
    </div>

    <div id="top-connectors-loading" class="flex items-center justify-center py-8">
        <div class="text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-systemBlue mx-auto mb-2"></div>
            <p class="text-sm text-gray-600">Carregando top conectores...</p>
        </div>
    </div>

    <div id="top-connectors-list" class="hidden grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Top connectors will be populated here -->
    </div>
</div>

<!-- Gráficos Avançados D3.js -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Gráfico de Influência (Bubble Chart) -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-gray-900">Mapa de Influência</h2>
            <div class="flex items-center space-x-2">
                <select id="influence-metric" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="total">Por Total de Conexões</option>
                    <option value="accepted">Por Conexões Aceitas</option>
                    <option value="rate">Por Taxa de Aceitação</option>
                </select>
                <button id="refresh-influence-chart" class="text-systemBlue hover:text-blue-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
            </div>
        </div>

        <div class="h-96 bg-gray-50 rounded-md border border-gray-200 p-2">
            <div id="influence-chart-loading" class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-systemBlue mx-auto mb-2"></div>
                    <p class="text-xs text-gray-600">Carregando mapa de influência...</p>
                </div>
            </div>
            <svg id="influenceChart" class="hidden w-full h-full"></svg>
        </div>

        <div class="mt-3 text-xs text-gray-600">
            <p><strong>Legenda:</strong> Tamanho = Influência | Cor = Taxa de Aceitação | Posição = Centralidade na Rede</p>
        </div>
    </div>

    <!-- Gráfico de Hierarquia (Treemap) -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-gray-900">Hierarquia</h2>
            <div class="flex items-center space-x-2">
                <select id="hierarchy-view" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="connections">Por Conexões</option>
                    <option value="influence">Por Influência</option>
                    <option value="activity">Por Atividade</option>
                </select>
                <button id="refresh-hierarchy-chart" class="text-systemBlue hover:text-blue-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
            </div>
        </div>

        <div class="h-96 bg-gray-50 rounded-md border border-gray-200 p-2">
            <div id="hierarchy-chart-loading" class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-systemBlue mx-auto mb-2"></div>
                    <p class="text-xs text-gray-600">Carregando hierarquia...</p>
                </div>
            </div>
            <svg id="hierarchyChart" class="hidden w-full h-full"></svg>
        </div>

        <div class="mt-3 text-xs text-gray-600">
            <p><strong>Treemap:</strong> Área = Importância | Cor = Categoria | Clique para explorar</p>
        </div>
    </div>
</div>

<!-- Gráficos de Análise Temporal e Fluxo -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Gráfico de Fluxo de Conexões (Sankey) -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-gray-900">Fluxo</h2>
            <div class="flex items-center space-x-2">
                <select id="flow-period" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="all">Todo o Período</option>
                    <option value="recent">Últimos 6 Meses</option>
                    <option value="year">Último Ano</option>
                </select>
                <button id="refresh-flow-chart" class="text-systemBlue hover:text-blue-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
            </div>
        </div>

        <div class="h-80 bg-gray-50 rounded-md border border-gray-200 p-2">
            <div id="flow-chart-loading" class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-systemBlue mx-auto mb-2"></div>
                    <p class="text-xs text-gray-600">Carregando fluxo de conexões...</p>
                </div>
            </div>
            <svg id="flowChart" class="hidden w-full h-full"></svg>
        </div>

        <div class="mt-3 text-xs text-gray-600">
            <p><strong>Sankey:</strong> Largura = Volume de Conexões | Cores = Status (Verde=Aceita, Amarelo=Pendente, Vermelho=Rejeitada)</p>
        </div>
    </div>

    <!-- Gráfico de Evolução Temporal (Line Chart) -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-gray-900">Evolução Temporal</h2>
            <div class="flex items-center space-x-2">
                <select id="temporal-granularity" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="monthly" selected>Por Mês</option>
                    <option value="weekly">Por Semana</option>
                    <option value="daily">Por Dia</option>
                </select>
                <button id="refresh-temporal-chart" class="text-systemBlue hover:text-blue-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
            </div>
        </div>

        <div class="h-80 bg-gray-50 rounded-md border border-gray-200 p-2">
            <div id="temporal-chart-loading" class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-systemBlue mx-auto mb-2"></div>
                    <p class="text-xs text-gray-600">Carregando evolução temporal...</p>
                </div>
            </div>
            <svg id="temporalChart" class="hidden w-full h-full"></svg>
        </div>

        <div class="mt-3 text-xs text-gray-600">
            <p><strong>Linhas:</strong> Verde = Aceitas | Amarelo = Pendentes | Vermelho = Rejeitadas | Azul = Total</p>
        </div>
    </div>
</div>

<!-- Gráficos de Distribuição -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Distribuição de Conexões Aceitas -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-gray-900">Distribuição - Aceitas</h2>
            <button id="refresh-accepted-distribution" class="text-systemBlue hover:text-blue-600">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
            </button>
        </div>

        <div class="h-64 bg-gray-50 rounded-md border border-gray-200 p-2">
            <div id="accepted-distribution-loading" class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-systemBlue mx-auto mb-2"></div>
                    <p class="text-xs text-gray-600">Carregando distribuição...</p>
                </div>
            </div>
            <canvas id="acceptedDistributionChart" class="hidden w-full h-full"></canvas>
        </div>

        <div class="mt-3 grid grid-cols-3 gap-4 text-center text-xs">
            <div>
                <div class="font-medium text-gray-700">Média</div>
                <div id="accepted-mean" class="text-systemBlue font-bold">0</div>
            </div>
            <div>
                <div class="font-medium text-gray-700">Desvio Padrão</div>
                <div id="accepted-std" class="text-systemBlue font-bold">0</div>
            </div>
            <div>
                <div class="font-medium text-gray-700">Mediana</div>
                <div id="accepted-median" class="text-systemBlue font-bold">0</div>
            </div>
        </div>
    </div>

    <!-- Distribuição de Conexões Pendentes -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-gray-900">Distribuição - Pendentes</h2>
            <button id="refresh-pending-distribution" class="text-systemBlue hover:text-blue-600">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
            </button>
        </div>

        <div class="h-64 bg-gray-50 rounded-md border border-gray-200 p-2">
            <div id="pending-distribution-loading" class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-systemBlue mx-auto mb-2"></div>
                    <p class="text-xs text-gray-600">Carregando distribuição...</p>
                </div>
            </div>
            <canvas id="pendingDistributionChart" class="hidden w-full h-full"></canvas>
        </div>

        <div class="mt-3 grid grid-cols-3 gap-4 text-center text-xs">
            <div>
                <div class="font-medium text-gray-700">Média</div>
                <div id="pending-mean" class="text-gray-600 font-bold">0</div>
            </div>
            <div>
                <div class="font-medium text-gray-700">Desvio Padrão</div>
                <div id="pending-std" class="text-gray-600 font-bold">0</div>
            </div>
            <div>
                <div class="font-medium text-gray-700">Mediana</div>
                <div id="pending-median" class="text-gray-600 font-bold">0</div>
            </div>
        </div>
    </div>
</div>

<!-- Análise de Influência -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-900">🎯 Análise de Influência</h2>
        <button id="refresh-influence-analysis" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
            <i class="fas fa-sync-alt mr-2"></i>Atualizar
        </button>
    </div>

    <!-- Cards de Métricas de Influência -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <!-- Influenciadores Top -->
        <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-blue-600">Top Influenciadores</p>
                    <p id="top-influencers-count" class="text-2xl font-bold text-blue-900">0</p>
                    <p class="text-xs text-blue-600">Usuários com alta influência</p>
                </div>
                <div class="p-3 bg-blue-200 rounded-full">
                    <i class="fas fa-crown text-blue-600"></i>
                </div>
            </div>
        </div>

        <!-- Score Médio de Influência -->
        <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-blue-600">Score Médio</p>
                    <p id="avg-influence-score" class="text-2xl font-bold text-blue-900">0.0</p>
                    <p class="text-xs text-blue-600">Influência média da rede</p>
                </div>
                <div class="p-3 bg-blue-200 rounded-full">
                    <i class="fas fa-chart-line text-blue-600"></i>
                </div>
            </div>
        </div>

        <!-- Alcance Total -->
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-4 rounded-lg border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Alcance Total</p>
                    <p id="total-reach" class="text-2xl font-bold text-gray-900">0</p>
                    <p class="text-xs text-gray-600">Conexões potenciais</p>
                </div>
                <div class="p-3 bg-gray-200 rounded-full">
                    <i class="fas fa-broadcast-tower text-gray-600"></i>
                </div>
            </div>
        </div>

        <!-- Índice de Centralidade -->
        <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-blue-600">Centralidade</p>
                    <p id="centrality-index" class="text-2xl font-bold text-blue-900">0.0</p>
                    <p class="text-xs text-blue-600">Índice de centralização</p>
                </div>
                <div class="p-3 bg-blue-200 rounded-full">
                    <i class="fas fa-bullseye text-blue-600"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos de Influência -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Mapa de Influência Radial -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Mapa de Influência Radial</h3>
                <select id="influence-layout" class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                    <option value="radial">Layout Radial</option>
                    <option value="force">Layout de Força</option>
                    <option value="circular">Layout Circular</option>
                </select>
            </div>
            <div id="radial-influence-chart" class="w-full h-80 relative">
                <div id="radial-influence-loading" class="absolute inset-0 flex items-center justify-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
            </div>
        </div>

        <!-- Ranking de Influenciadores -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Ranking de Influenciadores</h3>
                <select id="influence-metric-ranking" class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                    <option value="betweenness">Centralidade de Intermediação</option>
                    <option value="closeness">Centralidade de Proximidade</option>
                    <option value="degree">Centralidade de Grau</option>
                    <option value="pagerank">PageRank</option>
                </select>
            </div>
            <div id="influence-ranking-chart" class="w-full h-80 relative">
                <div id="ranking-loading" class="absolute inset-0 flex items-center justify-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
            </div>
        </div>

        <!-- Análise de Clusters de Influência -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Clusters de Influência</h3>
                <select id="cluster-algorithm" class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                    <option value="modularity">Modularidade</option>
                    <option value="louvain">Louvain</option>
                    <option value="leiden">Leiden</option>
                </select>
            </div>
            <div id="influence-clusters-chart" class="w-full h-80 relative">
                <div id="clusters-loading" class="absolute inset-0 flex items-center justify-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
            </div>
        </div>

        <!-- Evolução da Influência -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Evolução da Influência</h3>
                <select id="influence-period" class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                    <option value="monthly">Mensal</option>
                    <option value="weekly">Semanal</option>
                    <option value="daily">Diário</option>
                </select>
            </div>
            <div id="influence-evolution-chart" class="w-full h-80 relative">
                <div id="evolution-loading" class="absolute inset-0 flex items-center justify-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Insights de Influência -->
    <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h4 class="font-medium text-blue-900 mb-3">💡 Insights de Influência</h4>
        <div id="influence-insights" class="space-y-2 text-sm text-blue-800">
            <p>• Carregando análise de influência...</p>
        </div>
    </div>
</div>

<!-- Lista de Todos os Usuários -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-gray-900">Lista de Usuários</h2>
        <div class="flex items-center space-x-2">
            <select id="all-users-filter" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                <option value="all">Todos os Usuários</option>
                <option value="active">Apenas Ativos</option>
                <option value="pending-only">Apenas Pendentes</option>
                <option value="high-activity">Alta Atividade (10+)</option>
            </select>
            <select id="all-users-sort" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                <option value="total">Por Total</option>
                <option value="accepted">Por Aceitas</option>
                <option value="pending">Por Pendentes</option>
                <option value="rate">Por Taxa</option>
            </select>
        </div>
    </div>

    <div id="all-users-loading" class="flex items-center justify-center py-8">
        <div class="text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-systemBlue mx-auto mb-2"></div>
            <p class="text-sm text-gray-600">Carregando lista de usuários...</p>
        </div>
    </div>

    <div id="all-users-container" class="hidden">
        <div class="mb-4 text-sm text-gray-600">
            <span id="all-users-count">0</span> usuários encontrados
        </div>

        <div class="max-h-96 overflow-y-auto">
            <table class="w-full text-sm">
                <thead class="bg-gray-50 sticky top-0">
                    <tr>
                        <th class="px-3 py-2 text-left font-medium text-gray-700">Usuário</th>
                        <th class="px-3 py-2 text-center font-medium text-gray-700">Total</th>
                        <th class="px-3 py-2 text-center font-medium text-gray-700">Aceitas</th>
                        <th class="px-3 py-2 text-center font-medium text-gray-700">Pendentes</th>
                        <th class="px-3 py-2 text-center font-medium text-gray-700">Taxa</th>
                        <th class="px-3 py-2 text-center font-medium text-gray-700">Status</th>
                    </tr>
                </thead>
                <tbody id="all-users-table-body" class="divide-y divide-gray-200">
                    <!-- Users will be populated here -->
                </tbody>
            </table>
        </div>
    </div>
</div>



{% endblock %}

{% block scripts %}
<!-- Bibliotecas para visualizações avançadas -->
<script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Global variables
let connectionsData = null;
let networkData = null;
let currentFilter = 'all';

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Verificar se as bibliotecas necessárias estão carregadas
    checkRequiredLibraries();

    // Inicializar imediatamente
    loadConnectionsData();
    setupEventListeners();
});

// Função para verificar se as bibliotecas necessárias estão carregadas
function checkRequiredLibraries() {
    console.log('Verificando bibliotecas necessárias...');

    // Verificar D3.js
    if (typeof d3 === 'undefined') {
        console.error('D3.js não está carregado!');
        loadScript('https://cdn.jsdelivr.net/npm/d3@7');
    } else {
        console.log('D3.js está carregado:', d3.version);
    }

    // Verificar Chart.js
    if (typeof Chart === 'undefined') {
        console.error('Chart.js não está carregado!');
        loadScript('https://cdn.jsdelivr.net/npm/chart.js');
    } else {
        console.log('Chart.js está carregado');
    }
}

// Função para carregar scripts dinamicamente
function loadScript(url) {
    return new Promise((resolve, reject) => {
        console.log(`Carregando script: ${url}`);
        const script = document.createElement('script');
        script.src = url;
        script.onload = () => {
            console.log(`Script carregado: ${url}`);
            resolve();
        };
        script.onerror = (error) => {
            console.error(`Erro ao carregar script ${url}:`, error);
            reject(error);
        };
        document.head.appendChild(script);
    });
}

function setupEventListeners() {
    // Helper function to safely add event listeners
    function safeAddEventListener(id, event, handler) {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener(event, handler);
        } else {
            console.warn(`Element with id '${id}' not found`);
        }
    }

    // Refresh data button
    safeAddEventListener('refresh-data', 'click', loadConnectionsData);

    // Network filter
    safeAddEventListener('network-filter', 'change', function(e) {
        currentFilter = e.target.value;
        updateNetworkVisualization();
    });

    // Network user limit filter
    safeAddEventListener('network-user-limit', 'change', function(e) {
        console.log('Limite de usuários alterado para:', e.target.value);
        // Recriar o diagrama com o novo limite
        createNetworkVisualization();
    });

    // Zoom controls
    safeAddEventListener('zoom-in', 'click', () => zoomNetwork(1.2));
    safeAddEventListener('zoom-out', 'click', () => zoomNetwork(0.8));
    safeAddEventListener('reset-zoom', 'click', resetNetworkZoom);

    // Top connectors filter
    safeAddEventListener('top-connectors-filter', 'change', updateTopConnectors);

    // Distribution charts refresh
    safeAddEventListener('refresh-accepted-distribution', 'click', () => updateDistributionCharts());
    safeAddEventListener('refresh-pending-distribution', 'click', () => updateDistributionCharts());

    // All users filters
    safeAddEventListener('all-users-filter', 'change', () => {
        if (connectionsData && connectionsData.all_users) {
            renderAllUsersList(connectionsData.all_users);
        }
    });
    safeAddEventListener('all-users-sort', 'change', () => {
        if (connectionsData && connectionsData.all_users) {
            renderAllUsersList(connectionsData.all_users);
        }
    });

    // Network metrics tooltip
    safeAddEventListener('network-metrics-tooltip', 'click', showNetworkMetricsTooltip);

    // Advanced D3 charts event listeners
    safeAddEventListener('influence-metric', 'change', createInfluenceChart);
    safeAddEventListener('refresh-influence-chart', 'click', createInfluenceChart);
    safeAddEventListener('hierarchy-view', 'change', createHierarchyChart);
    safeAddEventListener('refresh-hierarchy-chart', 'click', createHierarchyChart);
    safeAddEventListener('flow-period', 'change', createFlowChart);
    safeAddEventListener('refresh-flow-chart', 'click', createFlowChart);
    safeAddEventListener('temporal-granularity', 'change', createTemporalChart);
    safeAddEventListener('refresh-temporal-chart', 'click', createTemporalChart);

    // Influence analysis event listeners
    safeAddEventListener('refresh-influence-analysis', 'click', updateInfluenceAnalysis);
    safeAddEventListener('influence-layout', 'change', createRadialInfluenceChart);
    safeAddEventListener('influence-metric-ranking', 'change', createInfluenceRankingChart);
    safeAddEventListener('cluster-algorithm', 'change', createInfluenceClustersChart);
    safeAddEventListener('influence-period', 'change', createInfluenceEvolutionChart);
}

// Função para atualizar análises avançadas baseadas em dados reais
function updateAdvancedAnalytics() {
    if (!connectionsData) return;

    // Comentado - elementos removidos da seção Análise Estratégica
    // updateNetworkHealthScore();
    // updateRealGrowthAnalysis();
    // updateInequalityAnalysis();
    // updateBottlenecksAndOpportunities();
    // updateStrategicRecommendations();
    updateAdvancedD3Charts();
}

// Score de saúde da rede - COMENTADO (elementos removidos)
/*
function updateNetworkHealthScore() {
    const healthScore = connectionsData.advanced_analytics?.network_health_score;
    if (!healthScore) return;

    // Atualizar score circular
    const scoreValue = healthScore.score;
    const circle = document.getElementById('health-score-circle');
    const circumference = 2 * Math.PI * 15.9155;
    const offset = circumference - (scoreValue / 100) * circumference;

    circle.style.strokeDasharray = `${circumference}, ${circumference}`;
    circle.style.strokeDashoffset = offset;
    circle.style.stroke = healthScore.color;

    // Atualizar valores
    document.getElementById('health-score-value').textContent = scoreValue;
    document.getElementById('health-score-classification').textContent = healthScore.classification;
    document.getElementById('health-score-classification').style.color = healthScore.color;

    // Componentes do score
    if (healthScore.components) {
        document.getElementById('health-acceptance-rate').textContent = `${healthScore.components.acceptance_rate}%`;
        document.getElementById('health-engagement-rate').textContent = `${healthScore.components.engagement_rate}%`;
        document.getElementById('health-volume-score').textContent = healthScore.components.volume_score;
    }
}
*/

// Análise de crescimento real - COMENTADO (elementos removidos)
/*
function updateRealGrowthAnalysis() {
    const growthPotential = connectionsData.advanced_analytics?.growth_potential;
    if (!growthPotential) return;

    document.getElementById('current-acceptance-rate').textContent = `${growthPotential.current_acceptance_rate}%`;
    document.getElementById('potential-growth').textContent = `${growthPotential.potential_growth}%`;
    document.getElementById('growth-factor').textContent = `${growthPotential.growth_factor}x`;
    document.getElementById('growth-status').textContent = growthPotential.status;
}
*/

// Análise de desigualdade da rede - COMENTADO (elementos removidos)
/*
function updateInequalityAnalysis() {
    const analytics = connectionsData.advanced_analytics;
    if (!analytics) return;

    document.getElementById('gini-coefficient').textContent = analytics.gini_coefficient;
    document.getElementById('network-inequality').textContent = analytics.network_inequality;
    document.getElementById('top-connector-count').textContent = analytics.top_connector_count;

    // Insight baseado no coeficiente de Gini
    let insight = '';
    const gini = analytics.gini_coefficient;
    if (gini > 0.6) {
        insight = 'Alta desigualdade: poucos usuários concentram a maioria das conexões. Considere estratégias para distribuir melhor as conexões.';
    } else if (gini > 0.4) {
        insight = 'Desigualdade moderada: há uma distribuição razoável de conexões, mas ainda pode ser melhorada.';
    } else {
        insight = 'Baixa desigualdade: as conexões estão bem distribuídas entre os usuários da rede.';
    }

    document.getElementById('inequality-insight').textContent = insight;
}
*/

// Gargalos e oportunidades - COMENTADO (elementos removidos)
/*
function updateBottlenecksAndOpportunities() {
    const optimization = connectionsData.optimization_insights;
    if (!optimization) return;

    // Gargalos
    const bottlenecksList = document.getElementById('bottlenecks-list');
    bottlenecksList.innerHTML = '';

    if (optimization.bottlenecks && optimization.bottlenecks.length > 0) {
        optimization.bottlenecks.forEach(bottleneck => {
            const item = document.createElement('div');
            item.className = 'p-3 bg-red-50 border border-red-200 rounded-md';
            item.innerHTML = `
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            ${bottleneck.impact}
                        </span>
                    </div>
                    <div class="ml-3 flex-1">
                        <h4 class="text-sm font-medium text-red-800">${bottleneck.issue}</h4>
                        <p class="text-xs text-red-600 mt-1">${bottleneck.description}</p>
                        <p class="text-xs text-red-700 mt-2 font-medium">💡 ${bottleneck.recommendation}</p>
                    </div>
                </div>
            `;
            bottlenecksList.appendChild(item);
        });
    } else {
        bottlenecksList.innerHTML = '<div class="text-center py-4 text-gray-500">Nenhum gargalo crítico identificado! 🎉</div>';
    }

    // Oportunidades
    const opportunitiesList = document.getElementById('opportunities-list');
    opportunitiesList.innerHTML = '';

    if (optimization.opportunities && optimization.opportunities.length > 0) {
        optimization.opportunities.forEach(opportunity => {
            const item = document.createElement('div');
            item.className = 'p-3 bg-green-50 border border-green-200 rounded-md';

            const impactColor = opportunity.potential_impact === 'Alto' ? 'green' :
                               opportunity.potential_impact === 'Médio' ? 'yellow' : 'blue';

            item.innerHTML = `
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-${impactColor}-100 text-${impactColor}-800">
                            ${opportunity.potential_impact}
                        </span>
                    </div>
                    <div class="ml-3 flex-1">
                        <h4 class="text-sm font-medium text-green-800">${opportunity.title}</h4>
                        <p class="text-xs text-green-600 mt-1">${opportunity.description}</p>
                        <div class="flex justify-between items-center mt-2 text-xs">
                            <span class="text-green-700 font-medium">📈 ${opportunity.estimated_growth}</span>
                            <span class="text-green-600">⚡ ${opportunity.implementation}</span>
                        </div>
                    </div>
                </div>
            `;
            opportunitiesList.appendChild(item);
        });
    }
}
*/



// Projeções de crescimento
function updateGrowthProjections() {
    const growthPredictions = connectionsData.growth_predictions;
    if (!growthPredictions) return;

    // Atualizar métricas
    document.getElementById('current-growth-rate').textContent = `${growthPredictions.growth_rate}%`;

    const marketPotential = growthPredictions.market_potential;
    if (marketPotential) {
        document.getElementById('market-potential').textContent = marketPotential.total_addressable_market;
    }

    // Criar gráfico de projeções
    updateGrowthProjectionChart();
}

function updateGrowthProjectionChart() {
    const growthPredictions = connectionsData.growth_predictions;
    if (!growthPredictions || !growthPredictions.projections) return;

    const scenario = document.getElementById('growth-scenario').value;
    const projectionData = growthPredictions.projections[scenario];

    if (!projectionData) return;

    // Atualizar projeção 12 meses
    const lastMonth = projectionData[projectionData.length - 1];
    document.getElementById('projected-connections').textContent = lastMonth.connections;

    const canvas = document.getElementById('growthProjectionChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // Destruir gráfico existente
    if (window.growthChartInstance) {
        window.growthChartInstance.destroy();
    }

    const labels = projectionData.map(d => `Mês ${d.month}`);
    const data = projectionData.map(d => d.connections);

    window.growthChartInstance = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Conexões Projetadas',
                data: data,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                title: {
                    display: true,
                    text: `Projeção de Crescimento - Cenário ${scenario.charAt(0).toUpperCase() + scenario.slice(1)}`,
                    font: {
                        size: 14
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    title: {
                        display: true,
                        text: 'Número de Conexões'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    document.getElementById('growth-chart-loading').style.display = 'none';
    document.getElementById('growthProjectionChart').classList.remove('hidden');
}

// Recomendações estratégicas - COMENTADO (elementos removidos)
/*
function updateStrategicRecommendations() {
    const optimization = connectionsData.optimization_insights;
    if (!optimization) return;

    // Ações prioritárias
    const priorityActionsList = document.getElementById('priority-actions-list');
    if (!priorityActionsList) return;
    priorityActionsList.innerHTML = '';

    if (optimization.priority_actions && optimization.priority_actions.length > 0) {
        optimization.priority_actions.forEach(action => {
            const item = document.createElement('div');
            item.className = 'flex items-center justify-between p-2 bg-blue-50 border border-blue-200 rounded-md';

            const priorityColor = action.priority === 'Alta' ? 'red' : action.priority === 'Média' ? 'yellow' : 'green';

            item.innerHTML = `
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-800">${action.action}</div>
                    <div class="text-xs text-gray-600">${action.timeline}</div>
                </div>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-${priorityColor}-100 text-${priorityColor}-800">
                    ${action.priority}
                </span>
            `;
            priorityActionsList.appendChild(item);
        });
    }

    // Roadmap de implementação
    const roadmapContainer = document.getElementById('implementation-roadmap');
    if (!roadmapContainer) return;
    roadmapContainer.innerHTML = '';

    if (optimization.implementation_roadmap) {
        Object.entries(optimization.implementation_roadmap).forEach(([quarter, items]) => {
            const quarterDiv = document.createElement('div');
            quarterDiv.className = 'p-3 bg-gray-50 border border-gray-200 rounded-md';
            quarterDiv.innerHTML = `
                <h5 class="font-medium text-gray-800 mb-2">${quarter}</h5>
                <ul class="space-y-1">
                    ${items.map(item => `<li class="text-xs text-gray-600">• ${item}</li>`).join('')}
                </ul>
            `;
            roadmapContainer.appendChild(quarterDiv);
        });
    }

    // ROI e estimativas (elementos removidos, mas mantendo verificação segura)
    if (optimization.roi_estimates) {
        const estimatedGrowthElement = document.getElementById('estimated-total-growth');
        const paybackPeriodElement = document.getElementById('payback-period');

        if (estimatedGrowthElement) {
            estimatedGrowthElement.textContent = optimization.roi_estimates.estimated_total_growth;
        }
        if (paybackPeriodElement) {
            paybackPeriodElement.textContent = optimization.roi_estimates.payback_period;
        }
    }

    if (optimization.priority_score) {
        const priorityScoreElement = document.getElementById('priority-score');
        if (priorityScoreElement) {
            priorityScoreElement.textContent = `${optimization.priority_score.overall_score}/100`;
        }
    }
}
*/

// Função para atualizar gráficos D3.js avançados
function updateAdvancedD3Charts() {
    if (!connectionsData) return;

    createInfluenceChart();
    createHierarchyChart();
    createFlowChart();
    createTemporalChart();
}

// Gráfico de Influência (Bubble Chart)
function createInfluenceChart() {
    const allUsers = connectionsData.all_users;
    if (!allUsers || allUsers.length === 0) return;

    const container = document.getElementById('influenceChart');
    const containerRect = container.parentElement.getBoundingClientRect();
    const width = containerRect.width - 20;
    const height = containerRect.height - 20;

    // Limpar SVG existente
    d3.select('#influenceChart').selectAll('*').remove();

    const svg = d3.select('#influenceChart')
        .attr('width', width)
        .attr('height', height);

    const g = svg.append('g')
        .attr('transform', `translate(${width/2}, ${height/2})`);

    // Preparar dados para bubble chart
    const metric = document.getElementById('influence-metric').value;
    const bubbleData = allUsers.map(user => {
        let value, size;
        switch (metric) {
            case 'accepted':
                value = user.accepted_connections;
                size = Math.sqrt(user.accepted_connections) * 8 + 10;
                break;
            case 'rate':
                value = user.acceptance_rate;
                size = user.acceptance_rate * 2 + 10;
                break;
            default:
                value = user.total_connections;
                size = Math.sqrt(user.total_connections) * 6 + 8;
        }

        return {
            id: user.user_id,
            value: value,
            size: Math.min(size, 50), // Tamanho máximo
            rate: user.acceptance_rate,
            total: user.total_connections,
            accepted: user.accepted_connections,
            x: (Math.random() - 0.5) * width * 0.8,
            y: (Math.random() - 0.5) * height * 0.8
        };
    }).filter(d => d.value > 0);

    // Escala de cores baseada na taxa de aceitação
    const colorScale = d3.scaleSequential(d3.interpolateViridis)
        .domain([0, 100]);

    // Simulação de força para evitar sobreposição
    const simulation = d3.forceSimulation(bubbleData)
        .force('charge', d3.forceManyBody().strength(-50))
        .force('center', d3.forceCenter(0, 0))
        .force('collision', d3.forceCollide().radius(d => d.size + 2))
        .stop();

    // Executar simulação
    for (let i = 0; i < 300; ++i) simulation.tick();

    // Criar bubbles
    const bubbles = g.selectAll('.bubble')
        .data(bubbleData)
        .enter()
        .append('circle')
        .attr('class', 'bubble')
        .attr('cx', d => d.x)
        .attr('cy', d => d.y)
        .attr('r', d => d.size)
        .attr('fill', d => colorScale(d.rate))
        .attr('stroke', '#fff')
        .attr('stroke-width', 2)
        .style('cursor', 'pointer')
        .on('mouseover', function(event, d) {
            // Tooltip
            const tooltip = d3.select('body').append('div')
                .attr('class', 'influence-tooltip')
                .style('position', 'absolute')
                .style('background', 'rgba(0, 0, 0, 0.8)')
                .style('color', 'white')
                .style('padding', '8px')
                .style('border-radius', '4px')
                .style('font-size', '12px')
                .style('pointer-events', 'none')
                .style('z-index', '1000');

            tooltip.html(`
                <strong>Usuário ${d.id}</strong><br>
                Total: ${d.total}<br>
                Aceitas: ${d.accepted}<br>
                Taxa: ${d.rate}%
            `)
            .style('left', (event.pageX + 10) + 'px')
            .style('top', (event.pageY - 10) + 'px');

            // Destacar bubble
            d3.select(this)
                .transition()
                .duration(200)
                .attr('stroke-width', 4)
                .attr('stroke', '#ff6b6b');
        })
        .on('mouseout', function() {
            d3.selectAll('.influence-tooltip').remove();
            d3.select(this)
                .transition()
                .duration(200)
                .attr('stroke-width', 2)
                .attr('stroke', '#fff');
        });

    // Labels para os maiores bubbles
    const topBubbles = bubbleData.sort((a, b) => b.value - a.value).slice(0, 10);

    g.selectAll('.bubble-label')
        .data(topBubbles)
        .enter()
        .append('text')
        .attr('class', 'bubble-label')
        .attr('x', d => d.x)
        .attr('y', d => d.y + 4)
        .attr('text-anchor', 'middle')
        .attr('font-size', '10px')
        .attr('font-weight', 'bold')
        .attr('fill', '#fff')
        .attr('stroke', '#000')
        .attr('stroke-width', 0.5)
        .style('pointer-events', 'none')
        .text(d => d.id);

    // Mostrar gráfico
    document.getElementById('influence-chart-loading').style.display = 'none';
    document.getElementById('influenceChart').classList.remove('hidden');
}

// Gráfico de Hierarquia (Treemap)
function createHierarchyChart() {
    const allUsers = connectionsData.all_users;
    if (!allUsers || allUsers.length === 0) return;

    const container = document.getElementById('hierarchyChart');
    const containerRect = container.parentElement.getBoundingClientRect();
    const width = containerRect.width - 20;
    const height = containerRect.height - 20;

    // Limpar SVG existente
    d3.select('#hierarchyChart').selectAll('*').remove();

    const svg = d3.select('#hierarchyChart')
        .attr('width', width)
        .attr('height', height);

    // Preparar dados hierárquicos
    const view = document.getElementById('hierarchy-view').value;

    // Categorizar usuários
    const categories = {
        'high': { name: 'Alto Impacto', users: [], color: '#3b82f6' },
        'medium': { name: 'Médio Impacto', users: [], color: '#6366f1' },
        'low': { name: 'Baixo Impacto', users: [], color: '#6b7280' },
        'inactive': { name: 'Inativos', users: [], color: '#374151' }
    };

    allUsers.forEach(user => {
        let value;
        switch (view) {
            case 'influence':
                value = user.accepted_connections * (user.acceptance_rate / 100);
                break;
            case 'activity':
                value = user.total_connections;
                break;
            default:
                value = user.total_connections;
        }

        if (value >= 20) {
            categories.high.users.push({ ...user, value });
        } else if (value >= 10) {
            categories.medium.users.push({ ...user, value });
        } else if (value >= 5) {
            categories.low.users.push({ ...user, value });
        } else {
            categories.inactive.users.push({ ...user, value });
        }
    });

    // Criar estrutura hierárquica
    const hierarchyData = {
        name: 'Rede',
        children: Object.entries(categories)
            .filter(([key, cat]) => cat.users.length > 0)
            .map(([key, cat]) => ({
                name: cat.name,
                color: cat.color,
                children: cat.users.map(user => ({
                    name: `User ${user.user_id}`,
                    value: user.value,
                    user: user
                }))
            }))
    };

    // Criar treemap
    const treemap = d3.treemap()
        .size([width, height])
        .padding(2);

    const root = d3.hierarchy(hierarchyData)
        .sum(d => d.value || 0)
        .sort((a, b) => b.value - a.value);

    treemap(root);

    // Criar retângulos
    const leaf = svg.selectAll('g')
        .data(root.leaves())
        .enter()
        .append('g')
        .attr('transform', d => `translate(${d.x0},${d.y0})`);

    leaf.append('rect')
        .attr('width', d => d.x1 - d.x0)
        .attr('height', d => d.y1 - d.y0)
        .attr('fill', d => d.parent.data.color)
        .attr('stroke', '#fff')
        .attr('stroke-width', 1)
        .style('cursor', 'pointer')
        .on('mouseover', function(event, d) {
            const user = d.data.user;
            const tooltip = d3.select('body').append('div')
                .attr('class', 'hierarchy-tooltip')
                .style('position', 'absolute')
                .style('background', 'rgba(0, 0, 0, 0.8)')
                .style('color', 'white')
                .style('padding', '8px')
                .style('border-radius', '4px')
                .style('font-size', '12px')
                .style('pointer-events', 'none')
                .style('z-index', '1000');

            tooltip.html(`
                <strong>${d.data.name}</strong><br>
                Categoria: ${d.parent.data.name}<br>
                Total: ${user.total_connections}<br>
                Aceitas: ${user.accepted_connections}<br>
                Taxa: ${user.acceptance_rate}%
            `)
            .style('left', (event.pageX + 10) + 'px')
            .style('top', (event.pageY - 10) + 'px');
        })
        .on('mouseout', function() {
            d3.selectAll('.hierarchy-tooltip').remove();
        });

    // Adicionar labels para retângulos maiores
    leaf.filter(d => (d.x1 - d.x0) > 30 && (d.y1 - d.y0) > 20)
        .append('text')
        .attr('x', d => (d.x1 - d.x0) / 2)
        .attr('y', d => (d.y1 - d.y0) / 2)
        .attr('text-anchor', 'middle')
        .attr('dominant-baseline', 'middle')
        .attr('font-size', '10px')
        .attr('font-weight', 'bold')
        .attr('fill', '#fff')
        .text(d => d.data.name.replace('User ', ''));

    // Mostrar gráfico
    document.getElementById('hierarchy-chart-loading').style.display = 'none';
    document.getElementById('hierarchyChart').classList.remove('hidden');
}

// Gráfico de Fluxo de Conexões (Sankey simplificado)
function createFlowChart() {
    if (!connectionsData) return;

    const container = document.getElementById('flowChart');
    const containerRect = container.parentElement.getBoundingClientRect();
    const width = containerRect.width - 20;
    const height = containerRect.height - 20;

    // Limpar SVG existente
    d3.select('#flowChart').selectAll('*').remove();

    const svg = d3.select('#flowChart')
        .attr('width', width)
        .attr('height', height);

    // Dados de fluxo baseados nos status
    const flowData = [
        { source: 'Convites Enviados', target: 'Aceitas', value: connectionsData.accepted_connections, color: '#3b82f6' },
        { source: 'Convites Enviados', target: 'Pendentes', value: connectionsData.pending_connections, color: '#6b7280' },
        { source: 'Convites Enviados', target: 'Rejeitadas', value: connectionsData.refused_connections, color: '#374151' }
    ];

    const totalConnections = connectionsData.total_connections;

    // Posições dos nós
    const nodes = [
        { name: 'Convites Enviados', x: 50, y: height / 2, value: totalConnections },
        { name: 'Aceitas', x: width - 100, y: height * 0.2, value: connectionsData.accepted_connections },
        { name: 'Pendentes', x: width - 100, y: height * 0.5, value: connectionsData.pending_connections },
        { name: 'Rejeitadas', x: width - 100, y: height * 0.8, value: connectionsData.refused_connections }
    ];

    // Escala para largura dos links
    const linkScale = d3.scaleLinear()
        .domain([0, totalConnections])
        .range([2, 40]);

    // Criar links (fluxos)
    flowData.forEach(flow => {
        const sourceNode = nodes.find(n => n.name === flow.source);
        const targetNode = nodes.find(n => n.name === flow.target);

        if (flow.value > 0) {
            const linkWidth = linkScale(flow.value);

            // Criar path curvo
            const path = d3.path();
            path.moveTo(sourceNode.x + 80, sourceNode.y);
            path.bezierCurveTo(
                sourceNode.x + 150, sourceNode.y,
                targetNode.x - 150, targetNode.y,
                targetNode.x - 80, targetNode.y
            );

            svg.append('path')
                .attr('d', path.toString())
                .attr('stroke', flow.color)
                .attr('stroke-width', linkWidth)
                .attr('fill', 'none')
                .attr('opacity', 0.7)
                .on('mouseover', function() {
                    d3.select(this).attr('opacity', 1);
                })
                .on('mouseout', function() {
                    d3.select(this).attr('opacity', 0.7);
                });

            // Label do fluxo
            const midX = (sourceNode.x + targetNode.x) / 2;
            const midY = (sourceNode.y + targetNode.y) / 2;

            svg.append('text')
                .attr('x', midX)
                .attr('y', midY - 5)
                .attr('text-anchor', 'middle')
                .attr('font-size', '11px')
                .attr('font-weight', 'bold')
                .attr('fill', flow.color)
                .text(flow.value);
        }
    });

    // Criar nós
    nodes.forEach(node => {
        const nodeGroup = svg.append('g');

        // Retângulo do nó
        nodeGroup.append('rect')
            .attr('x', node.x - 40)
            .attr('y', node.y - 15)
            .attr('width', 80)
            .attr('height', 30)
            .attr('fill', node.name === 'Convites Enviados' ? '#6366f1' : '#e5e7eb')
            .attr('stroke', '#374151')
            .attr('stroke-width', 1)
            .attr('rx', 5);

        // Label do nó
        nodeGroup.append('text')
            .attr('x', node.x)
            .attr('y', node.y + 4)
            .attr('text-anchor', 'middle')
            .attr('font-size', '10px')
            .attr('font-weight', 'bold')
            .attr('fill', node.name === 'Convites Enviados' ? '#fff' : '#374151')
            .text(node.name);

        // Valor do nó
        nodeGroup.append('text')
            .attr('x', node.x)
            .attr('y', node.y + 35)
            .attr('text-anchor', 'middle')
            .attr('font-size', '12px')
            .attr('font-weight', 'bold')
            .attr('fill', '#374151')
            .text(node.value);
    });

    // Mostrar gráfico
    document.getElementById('flow-chart-loading').style.display = 'none';
    document.getElementById('flowChart').classList.remove('hidden');
}

// Gráfico de Evolução Temporal
function createTemporalChart() {
    if (!connectionsData) return;

    const container = document.getElementById('temporalChart');
    const containerRect = container.parentElement.getBoundingClientRect();
    const width = containerRect.width - 60;
    const height = containerRect.height - 60;

    // Limpar SVG existente
    d3.select('#temporalChart').selectAll('*').remove();

    const svg = d3.select('#temporalChart')
        .attr('width', containerRect.width - 20)
        .attr('height', containerRect.height - 20);

    const g = svg.append('g')
        .attr('transform', 'translate(40, 20)');

    // Preparar dados temporais - verificar se monthly_growth existe e é array
    let monthlyData = connectionsData.monthly_growth;
    if (!monthlyData || !Array.isArray(monthlyData)) {
        // Criar dados simulados se não existir
        monthlyData = [
            { month: '2024-01', connections: Math.round(connectionsData.total_connections * 0.3) },
            { month: '2024-02', connections: Math.round(connectionsData.total_connections * 0.5) },
            { month: '2024-03', connections: Math.round(connectionsData.total_connections * 0.7) },
            { month: '2024-04', connections: connectionsData.total_connections }
        ];
    }

    // Simular dados por status baseado nos dados mensais
    const temporalData = monthlyData.map(month => {
        const total = month.connections || 0;
        const acceptanceRate = (connectionsData.acceptance_rate || 0) / 100;
        const pendingRate = connectionsData.pending_connections / (connectionsData.total_connections || 1);

        return {
            month: month.month,
            date: new Date(month.month),
            accepted: Math.round(total * acceptanceRate),
            pending: Math.round(total * pendingRate),
            refused: Math.round(total * (1 - acceptanceRate - pendingRate)),
            total: total
        };
    });

    // Escalas
    const xScale = d3.scaleTime()
        .domain(d3.extent(temporalData, d => d.date))
        .range([0, width]);

    const yScale = d3.scaleLinear()
        .domain([0, d3.max(temporalData, d => d.total)])
        .range([height, 0]);

    // Linha para cada status
    const lineGenerator = d3.line()
        .x(d => xScale(d.date))
        .y(d => yScale(d.value))
        .curve(d3.curveMonotoneX);

    const lines = [
        { key: 'accepted', color: '#3b82f6', name: 'Aceitas' },
        { key: 'pending', color: '#6b7280', name: 'Pendentes' },
        { key: 'refused', color: '#374151', name: 'Rejeitadas' },
        { key: 'total', color: '#1e40af', name: 'Total' }
    ];

    lines.forEach(line => {
        const lineData = temporalData.map(d => ({
            date: d.date,
            value: d[line.key]
        }));

        g.append('path')
            .datum(lineData)
            .attr('fill', 'none')
            .attr('stroke', line.color)
            .attr('stroke-width', 2)
            .attr('d', lineGenerator)
            .style('opacity', 0.8);

        // Pontos
        g.selectAll(`.point-${line.key}`)
            .data(lineData)
            .enter()
            .append('circle')
            .attr('class', `point-${line.key}`)
            .attr('cx', d => xScale(d.date))
            .attr('cy', d => yScale(d.value))
            .attr('r', 3)
            .attr('fill', line.color)
            .style('cursor', 'pointer')
            .on('mouseover', function(event, d) {
                const tooltip = d3.select('body').append('div')
                    .attr('class', 'temporal-tooltip')
                    .style('position', 'absolute')
                    .style('background', 'rgba(0, 0, 0, 0.8)')
                    .style('color', 'white')
                    .style('padding', '8px')
                    .style('border-radius', '4px')
                    .style('font-size', '12px')
                    .style('pointer-events', 'none')
                    .style('z-index', '1000');

                tooltip.html(`
                    <strong>${line.name}</strong><br>
                    Data: ${d.date.toLocaleDateString()}<br>
                    Valor: ${d.value}
                `)
                .style('left', (event.pageX + 10) + 'px')
                .style('top', (event.pageY - 10) + 'px');
            })
            .on('mouseout', function() {
                d3.selectAll('.temporal-tooltip').remove();
            });
    });

    // Eixos
    const xAxis = d3.axisBottom(xScale)
        .tickFormat(d3.timeFormat('%b %Y'));

    const yAxis = d3.axisLeft(yScale);

    g.append('g')
        .attr('transform', `translate(0, ${height})`)
        .call(xAxis)
        .selectAll('text')
        .style('font-size', '10px');

    g.append('g')
        .call(yAxis)
        .selectAll('text')
        .style('font-size', '10px');

    // Legenda
    const legend = g.append('g')
        .attr('transform', `translate(${width - 100}, 20)`);

    lines.forEach((line, i) => {
        const legendItem = legend.append('g')
            .attr('transform', `translate(0, ${i * 20})`);

        legendItem.append('line')
            .attr('x1', 0)
            .attr('x2', 15)
            .attr('y1', 0)
            .attr('y2', 0)
            .attr('stroke', line.color)
            .attr('stroke-width', 2);

        legendItem.append('text')
            .attr('x', 20)
            .attr('y', 4)
            .attr('font-size', '10px')
            .attr('fill', '#374151')
            .text(line.name);
    });

    // Mostrar gráfico
    document.getElementById('temporal-chart-loading').style.display = 'none';
    document.getElementById('temporalChart').classList.remove('hidden');
}

async function loadConnectionsData() {
    try {
        showLoading();

        const response = await fetch('/api/connections-data');
        const result = await response.json();

        console.log('Resposta da API:', result);

        if (result.status === 'success') {
            connectionsData = result.data;
            console.log('Dados de conexões carregados:', connectionsData);
            updateAllMetrics();
            createNetworkVisualization();
            updateTemporalChart();
            updateTopConnectors();
            hideLoading();
        } else {
            throw new Error(result.message || 'Erro ao carregar dados');
        }
    } catch (error) {
        console.error('Erro ao carregar dados de conexões:', error);
        showError(error.message);
    }
}

function showLoading() {
    // Mostrar loading nos gráficos principais
    const loadingElements = [
        'network-loading',
        'top-connectors-loading',
        'all-users-loading'
    ];

    loadingElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) element.style.display = 'flex';
    });
}

function hideLoading() {
    // Esconder loading nos gráficos principais
    const loadingElements = [
        'network-loading',
        'top-connectors-loading',
        'all-users-loading'
    ];

    loadingElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) element.style.display = 'none';
    });
}

function showError(message) {
    console.error('Erro:', message);

    // Mostrar erro no diagrama de rede
    const networkError = document.getElementById('network-error');
    const networkErrorMessage = document.getElementById('network-error-message');

    if (networkError && networkErrorMessage) {
        networkError.classList.remove('hidden');
        networkErrorMessage.textContent = message;
    }

    hideLoading();
}

function updateAllMetrics() {
    if (!connectionsData) return;

    // Update main metrics (only if elements exist)
    safeUpdateElement('metric-total-connections', connectionsData.total_connections || 0);
    safeUpdateElement('metric-accepted-connections', connectionsData.accepted_connections || 0);
    safeUpdateElement('metric-pending-connections', connectionsData.pending_connections || 0);
    safeUpdateElement('metric-acceptance-rate', (connectionsData.acceptance_rate || 0) + '%');

    // Update percentages
    const total = connectionsData.total_connections || 1;
    const acceptedPerc = Math.round((connectionsData.accepted_connections / total) * 100);
    const pendingPerc = Math.round((connectionsData.pending_connections / total) * 100);

    safeUpdateElement('metric-accepted-percentage', acceptedPerc + '%');
    safeUpdateElement('metric-pending-percentage', pendingPerc + '%');

    // Update network metrics
    if (connectionsData.network_metrics) {
        const metrics = connectionsData.network_metrics;

        // Densidade atual e potencial
        safeUpdateElement('network-density', metrics.density || '0.000');
        safeUpdateElement('potential-density', `Potencial: ${metrics.potential_density || '0.000'}`);
        safeUpdateElement('avg-degree', metrics.avg_degree || '0.0');
        safeUpdateElement('total-nodes', metrics.total_nodes || '0');

        // Usuários ativos e pendentes
        safeUpdateElement('active-users', metrics.active_users || '0');
        safeUpdateElement('active-users-percentage', `${metrics.active_users_percentage || 0}%`);
        safeUpdateElement('pending-only-users', metrics.pending_only_users || '0');
        safeUpdateElement('pending-only-percentage', `${metrics.pending_only_percentage || 0}%`);

        // Update density bars
        const densityPerc = (metrics.density || 0) * 100;
        const potentialDensityPerc = (metrics.potential_density || 0) * 100;
        safeUpdateStyle('network-density-bar', 'width', densityPerc + '%');
        safeUpdateStyle('potential-density-bar', 'width', potentialDensityPerc + '%');

        // Aumento de densidade
        if (metrics.density_increase) {
            safeUpdateElement('density-increase', ` (+${metrics.density_increase}%)`);
        }
    }

    updateNetworkInsights();
    updateDistributionCharts();
    updateAllUsersList();
    updateAdvancedAnalytics();
    updateInfluenceAnalysis();
}

function updateNetworkInsights() {
    const insights = [
        `• Rede com ${connectionsData.network_metrics?.total_nodes || 0} usuários únicos`,
        `• Taxa de aceitação de ${connectionsData.acceptance_rate || 0}%`,
        `• Densidade da rede: ${connectionsData.network_metrics?.density || 0}`,
        `• ${connectionsData.accepted_connections || 0} conexões ativas na rede`
    ];

    document.getElementById('network-insights').innerHTML = insights.join('<br>');
}

// Network visualization variables
let svg, simulation, nodes, links, nodeElements, linkElements;
let transform = null;

// Helper functions for safe DOM updates
function safeUpdateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

function safeUpdateStyle(id, property, value) {
    const element = document.getElementById(id);
    if (element) {
        element.style[property] = value;
    }
}

function safeUpdateHTML(id, html) {
    const element = document.getElementById(id);
    if (element) {
        element.innerHTML = html;
    }
}

function updateNetworkCoverageInfo(nodesCreated) {
    if (!connectionsData) return;

    const totalUsers = connectionsData.user_behavior?.total_unique_users || 0;
    const activeUsers = connectionsData.network_metrics?.active_users || 0;
    const pendingOnlyUsers = connectionsData.network_metrics?.pending_only_users || 0;

    const coveragePercentage = totalUsers > 0 ? ((nodesCreated / totalUsers) * 100).toFixed(1) : 0;

    const coverageInfo = `Exibindo ${nodesCreated} de ${totalUsers} usuários (${coveragePercentage}%) • ${activeUsers} ativos • ${pendingOnlyUsers} apenas pendentes`;

    safeUpdateElement('network-coverage-info', coverageInfo);
}

function createNetworkVisualization() {
    console.log('Iniciando createNetworkVisualization...');

    if (typeof d3 === 'undefined') {
        console.error('D3.js is not loaded');
        document.getElementById('network-loading').style.display = 'none';
        document.getElementById('network-error').classList.remove('hidden');
        document.getElementById('network-error-message').textContent = 'D3.js não carregou corretamente';
        return;
    }

    if (!connectionsData) {
        console.log('connectionsData não disponível');
        return;
    }

    if (!connectionsData.top_connectors) {
        console.log('top_connectors não disponível, criando dados simulados...');
        // Criar dados simulados para teste
        connectionsData.top_connectors = [
            {user_id: 1, total_connections: 25, accepted_connections: 20, acceptance_rate: 80},
            {user_id: 2, total_connections: 18, accepted_connections: 15, acceptance_rate: 83},
            {user_id: 3, total_connections: 22, accepted_connections: 18, acceptance_rate: 82},
            {user_id: 4, total_connections: 15, accepted_connections: 12, acceptance_rate: 80},
            {user_id: 5, total_connections: 30, accepted_connections: 25, acceptance_rate: 83}
        ];
    }

    console.log('Dados disponíveis para visualização:', connectionsData.top_connectors);

    // Initialize transform now that D3 is available
    if (!transform) {
        transform = d3.zoomIdentity;
    }

    // Show network diagram, hide loading
    document.getElementById('network-loading').style.display = 'none';
    document.getElementById('network-diagram').classList.remove('hidden');

    // Prepare data for D3.js
    const networkData = prepareNetworkData();
    console.log('Network data preparado:', networkData);

    // Clear existing SVG
    d3.select('#network-diagram').selectAll('*').remove();

    // Set up SVG
    const container = document.querySelector('.network-container');
    const width = container.clientWidth;
    const height = container.clientHeight;

    console.log('Container dimensions:', width, height);

    svg = d3.select('#network-diagram')
        .attr('width', width)
        .attr('height', height);

    console.log('SVG criado:', svg.node());

    // Create zoom behavior
    const zoom = d3.zoom()
        .scaleExtent([0.1, 4])
        .on('zoom', (event) => {
            transform = event.transform;
            svg.selectAll('g').attr('transform', transform);
        });

    svg.call(zoom);

    // Create main group
    const g = svg.append('g');

    // Create force simulation
    simulation = d3.forceSimulation(networkData.nodes)
        .force('link', d3.forceLink(networkData.links).id(d => d.id).distance(80))
        .force('charge', d3.forceManyBody().strength(-300))
        .force('center', d3.forceCenter(width / 2, height / 2))
        .force('collision', d3.forceCollide().radius(20));

    // Create links
    linkElements = g.append('g')
        .selectAll('line')
        .data(networkData.links)
        .enter()
        .append('line')
        .attr('class', 'link')
        .attr('stroke-width', 1)
        .attr('stroke', '#e5e7eb')
        .attr('opacity', 0.6);

    // Create nodes
    nodeElements = g.append('g')
        .selectAll('circle')
        .data(networkData.nodes)
        .enter()
        .append('circle')
        .attr('class', 'node')
        .attr('r', d => getNodeSize(d.connections))
        .attr('fill', d => getNodeColor(d.colorIndex))
        .style('cursor', 'pointer')
        .call(d3.drag()
            .on('start', dragstarted)
            .on('drag', dragged)
            .on('end', dragended))
        .on('mouseover', showNodeTooltip)
        .on('mouseout', hideNodeTooltip)
        .on('click', highlightConnections);

    // Add labels
    const labels = g.append('g')
        .selectAll('text')
        .data(networkData.nodes)
        .enter()
        .append('text')
        .text(d => `${d.id}`)
        .attr('font-size', d => Math.max(8, getNodeSize(d.connections) / 2))
        .attr('font-weight', 'bold')
        .attr('text-anchor', 'middle')
        .attr('dy', 4)
        .attr('fill', '#fff')
        .attr('stroke', '#000')
        .attr('stroke-width', 0.5)
        .style('pointer-events', 'none')
        .style('text-shadow', '1px 1px 2px rgba(0,0,0,0.8)');

    // Update positions on simulation tick
    simulation.on('tick', () => {
        linkElements
            .attr('x1', d => d.source.x)
            .attr('y1', d => d.source.y)
            .attr('x2', d => d.target.x)
            .attr('y2', d => d.target.y);

        nodeElements
            .attr('cx', d => d.x)
            .attr('cy', d => d.y);

        labels
            .attr('x', d => d.x)
            .attr('y', d => d.y);
    });

    // Store references for updates
    nodes = networkData.nodes;
    links = networkData.links;
}

function prepareNetworkData() {
    const nodes = [];
    const links = [];
    const nodeMap = new Map();

    // Criar nós a partir de todos os usuários únicos nos dados
    const allUserIds = new Set();

    // Se temos dados reais, usar todos os usuários
    if (connectionsData.user_behavior && connectionsData.user_behavior.total_unique_users > 0) {
        const totalUsers = connectionsData.user_behavior.total_unique_users;
        const activeUsers = connectionsData.network_metrics?.active_users || 0;

        // Obter limite do filtro do usuário
        const userLimitSelect = document.getElementById('network-user-limit');
        const userLimit = userLimitSelect ? userLimitSelect.value : '100';

        let maxUsers;
        if (userLimit === 'all') {
            maxUsers = Math.min(totalUsers, 2000); // Limite absoluto para evitar travamento
        } else {
            maxUsers = Math.min(parseInt(userLimit), totalUsers);
        }

        console.log(`📊 Dados reais encontrados:`);
        console.log(`   Total de usuários únicos: ${totalUsers}`);
        console.log(`   Usuários ativos: ${activeUsers}`);
        console.log(`   Limite selecionado: ${userLimit}`);
        console.log(`   Criando ${maxUsers} nós no diagrama (${((maxUsers/totalUsers)*100).toFixed(1)}% do total)`);

        // Gerar usuários baseados nos dados reais
        for (let i = 1; i <= maxUsers; i++) {
            allUserIds.add(i);
        }
    } else {
        // Usar top connectors como fallback
        console.log(`📊 Usando fallback - Top connectors: ${connectionsData.top_connectors?.length || 0}`);
        connectionsData.top_connectors.forEach(connector => {
            allUserIds.add(connector.user_id);
        });
    }

    // Criar nós para todos os usuários
    allUserIds.forEach((userId, index) => {
        const connector = connectionsData.top_connectors.find(c => c.user_id === userId);
        const connections = connector ? connector.total_connections : Math.floor(Math.random() * 15) + 1;
        const accepted = connector ? connector.accepted_connections : Math.floor(connections * 0.7);
        const rate = connector ? connector.acceptance_rate : Math.round((accepted / connections) * 100);

        nodes.push({
            id: userId,
            connections: connections,
            accepted: accepted,
            pending: connections - accepted,
            rate: rate,
            type: connections > 20 ? 'high' : connections > 10 ? 'medium' : 'low',
            colorIndex: index % 12 // Para usar 12 cores diferentes
        });
        nodeMap.set(userId, nodes.length - 1);
    });

    // Criar links mais realistas baseados nos dados
    const totalConnections = connectionsData.total_connections || 100;
    const acceptedRatio = connectionsData.acceptance_rate / 100 || 0.7;
    const pendingRatio = (connectionsData.pending_connections / totalConnections) || 0.2;

    // Gerar conexões entre usuários
    const nodeArray = Array.from(allUserIds);
    for (let i = 0; i < nodeArray.length; i++) {
        for (let j = i + 1; j < nodeArray.length; j++) {
            // Probabilidade de conexão baseada na proximidade dos IDs e dados reais
            const connectionProbability = Math.max(0.1, 0.8 - (Math.abs(nodeArray[i] - nodeArray[j]) / nodeArray.length));

            if (Math.random() < connectionProbability) {
                let status;
                const rand = Math.random();
                if (rand < acceptedRatio) {
                    status = 'ACCEPTED';
                } else if (rand < acceptedRatio + pendingRatio) {
                    status = 'PENDING';
                } else {
                    status = 'REFUSED';
                }

                links.push({
                    source: nodeArray[i],
                    target: nodeArray[j],
                    value: Math.random() * 3 + 1,
                    status: status,
                    strength: status === 'ACCEPTED' ? 'strong' : status === 'PENDING' ? 'medium' : 'weak'
                });
            }
        }
    }

    console.log(`📊 RESUMO DO DIAGRAMA DE REDE:`);
    console.log(`   Nós criados: ${nodes.length}`);
    console.log(`   Links criados: ${links.length}`);
    console.log(`   Usuários únicos totais: ${connectionsData.user_behavior?.total_unique_users || 'N/A'}`);
    console.log(`   Usuários ativos: ${connectionsData.network_metrics?.active_users || 'N/A'}`);
    console.log(`   Usuários apenas pendentes: ${connectionsData.network_metrics?.pending_only_users || 'N/A'}`);

    // Atualizar informação de cobertura na interface
    updateNetworkCoverageInfo(nodes.length);

    return { nodes, links };
}

function getConnectionColor(status) {
    switch (status) {
        case 'ACCEPTED': return '#3b82f6'; // Azul moderno
        case 'PENDING': return '#6b7280';  // Cinza moderno
        case 'REFUSED': return '#374151';  // Cinza escuro moderno
        default: return '#6b7280';         // Cinza moderno
    }
}

// Paleta de cores modernas para usuários - Azul e Cinza
function getNodeColor(colorIndex) {
    const modernColors = [
        '#3b82f6', // Blue
        '#6366f1', // Indigo
        '#1e40af', // Blue-800
        '#2563eb', // Blue-600
        '#60a5fa', // Blue-400
        '#93c5fd', // Blue-300
        '#6b7280', // Gray-500
        '#4b5563', // Gray-600
        '#374151', // Gray-700
        '#1f2937', // Gray-800
        '#9ca3af', // Gray-400
        '#d1d5db'  // Gray-300
    ];

    return modernColors[colorIndex % modernColors.length];
}

// Função para calcular o tamanho do nó baseado no número de conexões
function getNodeSize(connections) {
    // Tamanho mínimo: 6, máximo: 35 para maior contraste
    const minSize = 6;
    const maxSize = 35;
    const maxConnections = 50; // Assumindo máximo de 50 conexões

    // Usar escala logarítmica para maior diferenciação visual
    const normalizedConnections = Math.min(connections / maxConnections, 1);
    const logScale = Math.log(1 + normalizedConnections * 9) / Math.log(10); // Log base 10

    return minSize + (logScale * (maxSize - minSize));
}

// Drag functions
function dragstarted(event, d) {
    if (!event.active) simulation.alphaTarget(0.3).restart();
    d.fx = d.x;
    d.fy = d.y;
}

function dragged(event, d) {
    d.fx = event.x;
    d.fy = event.y;
}

function dragended(event, d) {
    if (!event.active) simulation.alphaTarget(0);
    d.fx = null;
    d.fy = null;
}

// Tooltip functions
function showNodeTooltip(event, d) {
    const tooltip = d3.select('body').append('div')
        .attr('class', 'network-tooltip')
        .style('position', 'absolute')
        .style('background', 'linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(30, 30, 30, 0.9))')
        .style('color', 'white')
        .style('padding', '12px')
        .style('border-radius', '8px')
        .style('font-size', '12px')
        .style('pointer-events', 'none')
        .style('z-index', '1000')
        .style('box-shadow', '0 4px 12px rgba(0, 0, 0, 0.3)')
        .style('border', '1px solid rgba(255, 255, 255, 0.1)')
        .style('backdrop-filter', 'blur(10px)');

    const nodeColor = getNodeColor(d.colorIndex);

    tooltip.html(`
        <div style="border-left: 3px solid ${nodeColor}; padding-left: 8px;">
            <div style="font-weight: bold; font-size: 14px; margin-bottom: 6px;">
                👤 Usuário ${d.id}
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 11px;">
                <div>
                    <div style="color: #3b82f6;">✅ Aceitas: ${d.accepted}</div>
                    <div style="color: #6b7280;">⏳ Pendentes: ${d.pending}</div>
                </div>
                <div>
                    <div style="color: #1e40af;">🔗 Total: ${d.connections}</div>
                    <div style="color: #6366f1;">📊 Taxa: ${d.rate}%</div>
                </div>
            </div>
            <div style="margin-top: 6px; font-size: 10px; color: #9ca3af;">
                Tamanho: ${Math.round(getNodeSize(d.connections))}px • Tipo: ${d.type}
            </div>
        </div>
    `)
    .style('left', (event.pageX + 15) + 'px')
    .style('top', (event.pageY - 10) + 'px');

    // Adicionar efeito de hover no nó
    d3.select(event.target)
        .transition()
        .duration(200)
        .style('filter', `drop-shadow(0 0 12px ${nodeColor})`)
        .attr('r', d => getNodeSize(d.connections) * 1.2);
}

function hideNodeTooltip(event, d) {
    // Remover tooltips
    d3.selectAll('.network-tooltip').remove();

    // Resetar efeitos visuais do nó
    if (event && event.target) {
        d3.select(event.target)
            .transition()
            .duration(200)
            .style('filter', 'none')
            .attr('r', d => getNodeSize(d.connections));
    }
}

function highlightConnections(event, d) {
    // Reset all elements
    nodeElements.attr('opacity', 0.3);
    linkElements.attr('opacity', 0.1);

    // Highlight selected node
    d3.select(this).attr('opacity', 1);

    // Highlight connected links and nodes
    linkElements
        .filter(link => link.source.id === d.id || link.target.id === d.id)
        .attr('opacity', 1)
        .each(function(link) {
            nodeElements
                .filter(node => node.id === link.source.id || node.id === link.target.id)
                .attr('opacity', 1);
        });

    // Reset after 3 seconds
    setTimeout(() => {
        nodeElements.attr('opacity', 1);
        linkElements.attr('opacity', 1);
    }, 3000);
}

function updateNetworkVisualization() {
    if (!simulation || !linkElements || !nodeElements) {
        console.log('Simulação ou elementos não disponíveis para filtro');
        return;
    }

    console.log('Aplicando filtro:', currentFilter);

    // Filter data based on current filter
    let filteredLinks = links;
    let filteredNodes = nodes;

    switch (currentFilter) {
        case 'accepted':
            filteredLinks = links.filter(link => link.status === 'ACCEPTED');
            console.log(`Filtro aceitas: ${filteredLinks.length} de ${links.length} links`);
            break;
        case 'pending':
            filteredLinks = links.filter(link => link.status === 'PENDING');
            console.log(`Filtro pendentes: ${filteredLinks.length} de ${links.length} links`);
            break;
        case 'refused':
            filteredLinks = links.filter(link => link.status === 'REFUSED');
            console.log(`Filtro rejeitadas: ${filteredLinks.length} de ${links.length} links`);
            break;
        case 'top-users':
            const topUserIds = connectionsData.top_connectors.slice(0, 15).map(c => c.user_id);
            filteredLinks = links.filter(link =>
                topUserIds.includes(link.source.id || link.source) &&
                topUserIds.includes(link.target.id || link.target)
            );
            filteredNodes = nodes.filter(node => topUserIds.includes(node.id));
            console.log(`Filtro top users: ${filteredNodes.length} nós, ${filteredLinks.length} links`);
            break;
        case 'all':
        default:
            // Mostrar todos
            console.log(`Mostrando todos: ${nodes.length} nós, ${links.length} links`);
            break;
    }

    // Update link visibility and style
    linkElements
        .style('opacity', d => {
            const isVisible = filteredLinks.some(link =>
                (link.source.id || link.source) === (d.source.id || d.source) &&
                (link.target.id || link.target) === (d.target.id || d.target)
            );
            return isVisible ? 1 : 0.1;
        })
        .style('stroke-width', d => {
            const isVisible = filteredLinks.some(link =>
                (link.source.id || link.source) === (d.source.id || d.source) &&
                (link.target.id || link.target) === (d.target.id || d.target)
            );
            return isVisible ? 1.5 : 0.5;
        });

    // Update node visibility
    nodeElements
        .style('opacity', d => {
            if (currentFilter === 'top-users') {
                return filteredNodes.some(node => node.id === d.id) ? 1 : 0.3;
            }
            // Para outros filtros, mostrar nós que têm pelo menos uma conexão visível
            const hasVisibleConnection = filteredLinks.some(link =>
                (link.source.id || link.source) === d.id ||
                (link.target.id || link.target) === d.id
            );
            return hasVisibleConnection ? 1 : 0.3;
        });

    // Update labels visibility
    if (typeof labels !== 'undefined') {
        labels.style('opacity', d => {
            if (currentFilter === 'top-users') {
                return filteredNodes.some(node => node.id === d.id) ? 1 : 0.3;
            }
            const hasVisibleConnection = filteredLinks.some(link =>
                (link.source.id || link.source) === d.id ||
                (link.target.id || link.target) === d.id
            );
            return hasVisibleConnection ? 1 : 0.3;
        });
    }

    // Restart simulation with lower alpha for smooth transition
    simulation.alpha(0.1).restart();
}

// Funções para gráficos de distribuição
function updateDistributionCharts() {
    if (!connectionsData.network_metrics) return;

    const acceptedDist = connectionsData.network_metrics.accepted_distribution;
    const pendingDist = connectionsData.network_metrics.pending_distribution;

    // Atualizar estatísticas
    if (acceptedDist) {
        document.getElementById('accepted-mean').textContent = acceptedDist.mean || '0';
        document.getElementById('accepted-std').textContent = acceptedDist.std_dev || '0';
        document.getElementById('accepted-median').textContent = acceptedDist.median || '0';
        createDistributionChart('acceptedDistributionChart', acceptedDist, '#3b82f6');
        document.getElementById('accepted-distribution-loading').style.display = 'none';
        document.getElementById('acceptedDistributionChart').classList.remove('hidden');
    }

    if (pendingDist) {
        document.getElementById('pending-mean').textContent = pendingDist.mean || '0';
        document.getElementById('pending-std').textContent = pendingDist.std_dev || '0';
        document.getElementById('pending-median').textContent = pendingDist.median || '0';
        createDistributionChart('pendingDistributionChart', pendingDist, '#6b7280');
        document.getElementById('pending-distribution-loading').style.display = 'none';
        document.getElementById('pendingDistributionChart').classList.remove('hidden');
    }
}

function createDistributionChart(canvasId, distribution, color) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // Destruir gráfico existente se houver
    if (window[canvasId + 'Instance']) {
        window[canvasId + 'Instance'].destroy();
    }

    const labels = distribution.distribution.map(d => d.range);
    const data = distribution.distribution.map(d => d.count);

    window[canvasId + 'Instance'] = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Número de Usuários',
                data: data,
                backgroundColor: color + '80',
                borderColor: color,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                title: {
                    display: true,
                    text: `Distribuição Normal (μ=${distribution.mean}, σ=${distribution.std_dev})`,
                    font: {
                        size: 12
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    title: {
                        display: true,
                        text: 'Número de Usuários'
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Faixa de Conexões'
                    }
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const percentage = distribution.distribution[context.dataIndex].percentage;
                        return `${context.parsed.y} usuários (${percentage}%)`;
                    }
                }
            }
        }
    });
}

// Função para lista de todos os usuários
function updateAllUsersList() {
    if (!connectionsData.all_users) return;

    document.getElementById('all-users-loading').style.display = 'none';
    document.getElementById('all-users-container').classList.remove('hidden');

    renderAllUsersList(connectionsData.all_users);
}

function renderAllUsersList(users) {
    const filter = document.getElementById('all-users-filter').value;
    const sort = document.getElementById('all-users-sort').value;

    // Filtrar usuários
    let filteredUsers = users.filter(user => {
        switch (filter) {
            case 'active':
                return user.is_active;
            case 'pending-only':
                return user.pending_only;
            case 'high-activity':
                return user.total_connections >= 10;
            default:
                return true;
        }
    });

    // Ordenar usuários
    filteredUsers.sort((a, b) => {
        switch (sort) {
            case 'accepted':
                return b.accepted_connections - a.accepted_connections;
            case 'pending':
                return b.pending_connections - a.pending_connections;
            case 'rate':
                return b.acceptance_rate - a.acceptance_rate;
            default:
                return b.total_connections - a.total_connections;
        }
    });

    // Atualizar contador
    document.getElementById('all-users-count').textContent = filteredUsers.length;

    // Renderizar tabela
    const tbody = document.getElementById('all-users-table-body');
    tbody.innerHTML = '';

    filteredUsers.forEach(user => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const statusBadge = user.is_active ?
            '<span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Ativo</span>' :
            user.pending_only ?
            '<span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Pendente</span>' :
            '<span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Inativo</span>';

        row.innerHTML = `
            <td class="px-3 py-2 font-medium text-gray-900">Usuário ${user.user_id}</td>
            <td class="px-3 py-2 text-center text-gray-700">${user.total_connections}</td>
            <td class="px-3 py-2 text-center text-blue-600 font-medium">${user.accepted_connections}</td>
            <td class="px-3 py-2 text-center text-gray-600 font-medium">${user.pending_connections}</td>
            <td class="px-3 py-2 text-center text-systemBlue font-medium">${user.acceptance_rate}%</td>
            <td class="px-3 py-2 text-center">${statusBadge}</td>
        `;

        tbody.appendChild(row);
    });
}

// Função para tooltip das métricas de rede
function showNetworkMetricsTooltip() {
    const tooltip = document.createElement('div');
    tooltip.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    tooltip.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md mx-4 shadow-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Métricas da Rede</h3>
                <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="space-y-3 text-sm text-gray-600">
                <div>
                    <strong class="text-gray-800">Densidade da Rede:</strong>
                    <p>Percentual de conexões existentes em relação ao total possível. Valores próximos a 1 indicam rede muito conectada.</p>
                </div>
                <div>
                    <strong class="text-gray-800">Densidade Potencial:</strong>
                    <p>Densidade se todos os convites pendentes fossem aceitos. Mostra o potencial de crescimento da rede.</p>
                </div>
                <div>
                    <strong class="text-gray-800">Usuários Ativos:</strong>
                    <p>Usuários com pelo menos 1 conexão aceita. Indica engajamento real na rede.</p>
                </div>
                <div>
                    <strong class="text-gray-800">Usuários Apenas Pendentes:</strong>
                    <p>Usuários com convites pendentes mas sem aceitações. Podem precisar de incentivo para engajar.</p>
                </div>
                <div>
                    <strong class="text-gray-800">Grau Médio:</strong>
                    <p>Número médio de conexões aceitas por usuário. Indica o nível de conectividade da rede.</p>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(tooltip);

    // Fechar ao clicar fora
    tooltip.addEventListener('click', (e) => {
        if (e.target === tooltip) {
            tooltip.remove();
        }
    });
}

function zoomNetwork(factor) {
    if (!svg) return;

    const newTransform = transform.scale(transform.k * factor);
    svg.transition().duration(300).call(
        d3.zoom().transform,
        newTransform
    );
}

function resetNetworkZoom() {
    if (!svg) return;

    svg.transition().duration(500).call(
        d3.zoom().transform,
        d3.zoomIdentity
    );
}

function updateTemporalChart() {
    // Esta função foi substituída pela implementação D3.js createTemporalChart
    console.log('updateTemporalChart: Função descontinuada, usando createTemporalChart D3.js');
    return;

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: Object.keys(monthlyData),
            datasets: [{
                label: 'Conexões por Mês',
                data: Object.values(monthlyData),
                borderColor: '#007AFF',
                backgroundColor: 'rgba(0, 122, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Update insight
    const totalGrowth = Object.values(monthlyData).reduce((a, b) => a + b, 0);
    const avgMonthly = Math.round(totalGrowth / Object.keys(monthlyData).length);
    document.getElementById('temporal-insight').innerHTML =
        `<strong>Insight:</strong> Crescimento médio de ${avgMonthly} conexões por mês. Pico em ${Object.keys(monthlyData).reduce((a, b) => monthlyData[a] > monthlyData[b] ? a : b)}.`;
}

function updateTopConnectors() {
    if (!connectionsData.top_connectors) return;

    document.getElementById('top-connectors-loading').style.display = 'none';
    document.getElementById('top-connectors-list').classList.remove('hidden');

    const container = document.getElementById('top-connectors-list');
    container.innerHTML = '';

    connectionsData.top_connectors.slice(0, 9).forEach((connector, index) => {
        const card = document.createElement('div');
        card.className = 'bg-gray-50 p-4 rounded-lg border border-gray-200';

        card.innerHTML = `
            <div class="flex items-center justify-between mb-2">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-systemBlue text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                        ${index + 1}
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Usuário ${connector.user_id}</h4>
                        <p class="text-xs text-gray-500">ID: ${connector.user_id}</p>
                    </div>
                </div>
            </div>
            <div class="space-y-2">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-600">Total de Conexões:</span>
                    <span class="font-medium">${connector.total_connections}</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-600">Conexões Aceitas:</span>
                    <span class="font-medium text-blue-600">${connector.accepted_connections}</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-600">Taxa de Aceitação:</span>
                    <span class="font-medium text-systemBlue">${connector.acceptance_rate}%</span>
                </div>
            </div>
            <div class="mt-3 w-full bg-gray-200 rounded-full h-2">
                <div class="bg-systemBlue h-2 rounded-full" style="width: ${connector.acceptance_rate}%"></div>
            </div>
        `;

        container.appendChild(card);
    });
}

// ===== ANÁLISE DE INFLUÊNCIA =====

function updateInfluenceAnalysis() {
    if (!connectionsData) return;

    // Calcular métricas de influência baseadas nos dados reais
    const influenceMetrics = calculateInfluenceMetrics();

    // Atualizar cards de métricas
    updateInfluenceCards(influenceMetrics);

    // Criar gráficos
    createRadialInfluenceChart();
    createInfluenceRankingChart();
    createInfluenceClustersChart();
    createInfluenceEvolutionChart();

    // Atualizar insights
    updateInfluenceInsights(influenceMetrics);
}

function calculateInfluenceMetrics() {
    if (!connectionsData || !connectionsData.user_behavior) return {};

    const users = connectionsData.user_behavior.total_unique_users || 50;
    const acceptedConnections = connectionsData.accepted_connections || 0;
    const totalConnections = connectionsData.total_connections || 1;

    // Simular métricas de influência baseadas em dados reais
    const topInfluencers = Math.ceil(users * 0.1); // Top 10%
    const avgInfluenceScore = (acceptedConnections / totalConnections * 100).toFixed(1);
    const totalReach = acceptedConnections * 2.5; // Estimativa de alcance
    const centralityIndex = (acceptedConnections / (users * (users - 1) / 2)).toFixed(3);

    return {
        topInfluencers,
        avgInfluenceScore,
        totalReach: Math.round(totalReach),
        centralityIndex
    };
}

function updateInfluenceCards(metrics) {
    safeUpdateElement('top-influencers-count', metrics.topInfluencers || 0);
    safeUpdateElement('avg-influence-score', metrics.avgInfluenceScore || '0.0');
    safeUpdateElement('total-reach', metrics.totalReach || 0);
    safeUpdateElement('centrality-index', metrics.centralityIndex || '0.0');
}

function createRadialInfluenceChart() {
    const container = document.getElementById('radial-influence-chart');
    if (!container || !connectionsData) return;

    // Limpar container
    d3.select(container).selectAll('svg').remove();
    const loadingElement = document.getElementById('radial-influence-loading');
    if (loadingElement) loadingElement.style.display = 'none';

    const width = container.clientWidth;
    const height = 320;
    const radius = Math.min(width, height) / 2 - 40;

    const svg = d3.select(container)
        .append('svg')
        .attr('width', width)
        .attr('height', height);

    const g = svg.append('g')
        .attr('transform', `translate(${width/2},${height/2})`);

    // Dados simulados baseados nos dados reais
    const users = connectionsData.user_behavior?.total_unique_users || 20;
    const nodes = [];

    for (let i = 0; i < Math.min(users, 20); i++) {
        const influence = Math.random() * 100;
        const connections = Math.floor(Math.random() * 50) + 1;

        nodes.push({
            id: `user_${i}`,
            influence: influence,
            connections: connections,
            radius: Math.sqrt(connections) * 3 + 5,
            angle: (i / Math.min(users, 20)) * 2 * Math.PI
        });
    }

    // Layout radial
    const layout = document.getElementById('influence-layout')?.value || 'radial';

    if (layout === 'radial') {
        nodes.forEach((d, i) => {
            const r = (d.influence / 100) * radius * 0.8 + radius * 0.2;
            d.x = Math.cos(d.angle) * r;
            d.y = Math.sin(d.angle) * r;
        });
    } else if (layout === 'circular') {
        nodes.forEach((d, i) => {
            d.x = Math.cos(d.angle) * radius * 0.7;
            d.y = Math.sin(d.angle) * radius * 0.7;
        });
    }

    // Escala de cores baseada na influência
    const colorScale = d3.scaleSequential(d3.interpolateViridis)
        .domain([0, 100]);

    // Desenhar nós
    const nodeGroup = g.selectAll('.influence-node')
        .data(nodes)
        .enter()
        .append('g')
        .attr('class', 'influence-node')
        .attr('transform', d => `translate(${d.x},${d.y})`);

    nodeGroup.append('circle')
        .attr('r', d => d.radius)
        .attr('fill', d => colorScale(d.influence))
        .attr('stroke', '#fff')
        .attr('stroke-width', 2)
        .style('opacity', 0.8);

    // Adicionar labels
    nodeGroup.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', '.35em')
        .style('font-size', '10px')
        .style('fill', '#fff')
        .style('font-weight', 'bold')
        .text(d => Math.round(d.influence));

    // Tooltip
    nodeGroup.on('mouseover', function(event, d) {
        d3.select(this).select('circle').style('opacity', 1);

        // Criar tooltip
        const tooltip = d3.select('body').append('div')
            .attr('class', 'tooltip')
            .style('position', 'absolute')
            .style('background', 'rgba(0,0,0,0.8)')
            .style('color', 'white')
            .style('padding', '8px')
            .style('border-radius', '4px')
            .style('font-size', '12px')
            .style('pointer-events', 'none')
            .style('z-index', '1000');

        tooltip.html(`
            <strong>Usuário ${d.id}</strong><br>
            Influência: ${d.influence.toFixed(1)}<br>
            Conexões: ${d.connections}
        `)
        .style('left', (event.pageX + 10) + 'px')
        .style('top', (event.pageY - 10) + 'px');
    })
    .on('mouseout', function(event, d) {
        d3.select(this).select('circle').style('opacity', 0.8);
        d3.selectAll('.tooltip').remove();
    });
}

function createInfluenceRankingChart() {
    const container = document.getElementById('influence-ranking-chart');
    if (!container || !connectionsData) return;

    // Limpar container
    d3.select(container).selectAll('svg').remove();
    const loadingElement = document.getElementById('ranking-loading');
    if (loadingElement) loadingElement.style.display = 'none';

    const margin = {top: 20, right: 30, bottom: 40, left: 60};
    const width = container.clientWidth - margin.left - margin.right;
    const height = 320 - margin.top - margin.bottom;

    const svg = d3.select(container)
        .append('svg')
        .attr('width', width + margin.left + margin.right)
        .attr('height', height + margin.top + margin.bottom);

    const g = svg.append('g')
        .attr('transform', `translate(${margin.left},${margin.top})`);

    // Dados simulados de ranking
    const users = connectionsData.user_behavior?.total_unique_users || 10;
    const rankingData = [];

    for (let i = 0; i < Math.min(users, 10); i++) {
        rankingData.push({
            user: `Usuário ${i + 1}`,
            score: Math.random() * 100,
            rank: i + 1
        });
    }

    // Ordenar por score
    rankingData.sort((a, b) => b.score - a.score);

    // Escalas
    const xScale = d3.scaleLinear()
        .domain([0, d3.max(rankingData, d => d.score)])
        .range([0, width]);

    const yScale = d3.scaleBand()
        .domain(rankingData.map(d => d.user))
        .range([0, height])
        .padding(0.1);

    // Escala de cores
    const colorScale = d3.scaleSequential(d3.interpolateBlues)
        .domain([0, d3.max(rankingData, d => d.score)]);

    // Barras
    g.selectAll('.bar')
        .data(rankingData)
        .enter()
        .append('rect')
        .attr('class', 'bar')
        .attr('x', 0)
        .attr('y', d => yScale(d.user))
        .attr('width', d => xScale(d.score))
        .attr('height', yScale.bandwidth())
        .attr('fill', d => colorScale(d.score))
        .style('opacity', 0.8);

    // Labels de valores
    g.selectAll('.label')
        .data(rankingData)
        .enter()
        .append('text')
        .attr('class', 'label')
        .attr('x', d => xScale(d.score) + 5)
        .attr('y', d => yScale(d.user) + yScale.bandwidth() / 2)
        .attr('dy', '.35em')
        .style('font-size', '11px')
        .style('fill', '#333')
        .text(d => d.score.toFixed(1));

    // Eixos
    g.append('g')
        .attr('transform', `translate(0,${height})`)
        .call(d3.axisBottom(xScale));

    g.append('g')
        .call(d3.axisLeft(yScale));
}

function createInfluenceClustersChart() {
    const container = document.getElementById('influence-clusters-chart');
    if (!container || !connectionsData) return;

    // Limpar container
    d3.select(container).selectAll('svg').remove();
    const loadingElement = document.getElementById('clusters-loading');
    if (loadingElement) loadingElement.style.display = 'none';

    const width = container.clientWidth;
    const height = 320;

    const svg = d3.select(container)
        .append('svg')
        .attr('width', width)
        .attr('height', height);

    // Simular clusters baseados nos dados
    const users = connectionsData.user_behavior?.total_unique_users || 20;
    const numClusters = Math.min(5, Math.ceil(users / 4));
    const clusters = [];

    for (let i = 0; i < numClusters; i++) {
        const clusterSize = Math.floor(Math.random() * 8) + 3;
        const centerX = (i + 1) * (width / (numClusters + 1));
        const centerY = height / 2 + (Math.random() - 0.5) * 100;

        clusters.push({
            id: i,
            size: clusterSize,
            centerX: centerX,
            centerY: centerY,
            color: d3.schemeCategory10[i % 10]
        });
    }

    // Desenhar clusters
    clusters.forEach(cluster => {
        const clusterGroup = svg.append('g')
            .attr('class', `cluster-${cluster.id}`);

        // Círculo do cluster
        clusterGroup.append('circle')
            .attr('cx', cluster.centerX)
            .attr('cy', cluster.centerY)
            .attr('r', cluster.size * 8)
            .attr('fill', cluster.color)
            .style('opacity', 0.3)
            .attr('stroke', cluster.color)
            .attr('stroke-width', 2);

        // Nós do cluster
        for (let j = 0; j < cluster.size; j++) {
            const angle = (j / cluster.size) * 2 * Math.PI;
            const radius = cluster.size * 4;
            const x = cluster.centerX + Math.cos(angle) * radius;
            const y = cluster.centerY + Math.sin(angle) * radius;

            clusterGroup.append('circle')
                .attr('cx', x)
                .attr('cy', y)
                .attr('r', 4)
                .attr('fill', cluster.color)
                .style('opacity', 0.8);
        }

        // Label do cluster
        clusterGroup.append('text')
            .attr('x', cluster.centerX)
            .attr('y', cluster.centerY)
            .attr('text-anchor', 'middle')
            .attr('dy', '.35em')
            .style('font-size', '12px')
            .style('font-weight', 'bold')
            .style('fill', '#333')
            .text(`C${cluster.id + 1}`);
    });
}

function createInfluenceEvolutionChart() {
    const container = document.getElementById('influence-evolution-chart');
    if (!container || !connectionsData) return;

    // Limpar container
    d3.select(container).selectAll('svg').remove();
    const loadingElement = document.getElementById('evolution-loading');
    if (loadingElement) loadingElement.style.display = 'none';

    const margin = {top: 20, right: 30, bottom: 40, left: 50};
    const width = container.clientWidth - margin.left - margin.right;
    const height = 320 - margin.top - margin.bottom;

    const svg = d3.select(container)
        .append('svg')
        .attr('width', width + margin.left + margin.right)
        .attr('height', height + margin.top + margin.bottom);

    const g = svg.append('g')
        .attr('transform', `translate(${margin.left},${margin.top})`);

    // Dados temporais baseados nos dados reais
    const monthlyData = connectionsData.temporal_analysis?.monthly_growth || [];
    const evolutionData = monthlyData.map(d => ({
        month: d.month,
        influence: d.acceptance_rate || 0,
        connections: d.connections || 0
    }));

    if (evolutionData.length === 0) {
        // Dados simulados se não houver dados reais
        for (let i = 0; i < 12; i++) {
            evolutionData.push({
                month: `2024-${String(i + 1).padStart(2, '0')}`,
                influence: Math.random() * 100,
                connections: Math.floor(Math.random() * 50) + 10
            });
        }
    }

    // Escalas
    const xScale = d3.scalePoint()
        .domain(evolutionData.map(d => d.month))
        .range([0, width]);

    const yScale = d3.scaleLinear()
        .domain([0, d3.max(evolutionData, d => d.influence)])
        .range([height, 0]);

    // Linha de evolução
    const line = d3.line()
        .x(d => xScale(d.month))
        .y(d => yScale(d.influence))
        .curve(d3.curveMonotoneX);

    // Desenhar linha
    g.append('path')
        .datum(evolutionData)
        .attr('fill', 'none')
        .attr('stroke', '#3b82f6')
        .attr('stroke-width', 3)
        .attr('d', line);

    // Pontos
    g.selectAll('.dot')
        .data(evolutionData)
        .enter()
        .append('circle')
        .attr('class', 'dot')
        .attr('cx', d => xScale(d.month))
        .attr('cy', d => yScale(d.influence))
        .attr('r', 4)
        .attr('fill', '#3b82f6');

    // Eixos
    g.append('g')
        .attr('transform', `translate(0,${height})`)
        .call(d3.axisBottom(xScale))
        .selectAll('text')
        .style('text-anchor', 'end')
        .attr('dx', '-.8em')
        .attr('dy', '.15em')
        .attr('transform', 'rotate(-45)');

    g.append('g')
        .call(d3.axisLeft(yScale));
}

function updateInfluenceInsights(metrics) {
    const insights = [
        `• Identificados ${metrics.topInfluencers} usuários com alta influência na rede`,
        `• Score médio de influência: ${metrics.avgInfluenceScore}% - ${metrics.avgInfluenceScore > 70 ? 'Excelente' : metrics.avgInfluenceScore > 50 ? 'Bom' : 'Precisa melhorar'}`,
        `• Alcance total estimado: ${metrics.totalReach} conexões potenciais`,
        `• Índice de centralidade: ${metrics.centralityIndex} - Rede ${metrics.centralityIndex > 0.1 ? 'bem conectada' : 'com oportunidades de expansão'}`,
        `• Recomendação: ${metrics.avgInfluenceScore > 70 ? 'Focar em retenção dos influenciadores' : 'Desenvolver programa de incentivo para influenciadores'}`
    ];

    const container = document.getElementById('influence-insights');
    if (container) {
        container.innerHTML = insights.map(insight => `<p>${insight}</p>`).join('');
    }
}

</script>
{% endblock %}
