{# HYBRID STRUCTURED: Product Domain Components - Unified with Business Domain #}

{# KPI Card - Consistent with Business Domain but adapted for Product #}
{% macro kpi_card(title, value, subtitle=None, icon=None, color="blue", percentage=None, percentage_label=None, is_positive=true, footer=None, api_endpoint=None, metric_id=None) %}
<div class="bg-white rounded-view p-5 flex flex-col border border-gray-200 metric-card card-hover hover-scale"
     {% if api_endpoint %}data-api-endpoint="{{ api_endpoint }}"{% endif %}
     {% if metric_id %}data-metric-id="{{ metric_id }}"{% endif %}>
    <div class="flex items-center mb-2">
        {% if icon %}
        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {{ icon|safe }}
            </svg>
        </div>
        {% endif %}
        <p class="text-sm font-semibold text-gray-800">{{ title|e }}</p>
    </div>
    <div class="flex items-end justify-between">
        <div>
            {# SSR: Initial value from server #}
            <p class="text-2xl font-bold text-gray-900" 
               {% if metric_id %}id="{{ metric_id }}" data-initial="{{ value }}"{% endif %}>
                {{ value|e }}
            </p>
            {% if percentage %}
            <p class="text-xs {% if is_positive %}text-green-600{% else %}text-red-600{% endif %} flex items-center mt-1">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    {% if is_positive %}
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                    {% else %}
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                    {% endif %}
                </svg>
                {{ percentage }}{% if percentage_label %} {{ percentage_label }}{% endif %}
            </p>
            {% endif %}
        </div>
    </div>
    {% if footer %}
    <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
        <span>{{ footer|e }}</span>
    </div>
    {% endif %}
</div>
{% endmacro %}

{# Chart Container - Structured for hybrid loading #}
{% macro chart_container(title, chart_id, height="64", api_endpoint=None, filters=None) %}
<div class="bg-white rounded-view p-6 border border-gray-200 chart-container card-hover"
     {% if api_endpoint %}data-chart-api="{{ api_endpoint }}"{% endif %}>
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">{{ title|e }}</h3>
        {% if filters %}
        <div class="flex items-center space-x-2">
            {{ filters|safe }}
        </div>
        {% endif %}
    </div>
    <div class="h-{{ height }}">
        <canvas id="{{ chart_id }}"></canvas>
    </div>
</div>
{% endmacro %}

{# Metric Grid - Responsive grid for metrics #}
{% macro metric_grid(columns="5") %}
<div class="grid grid-cols-1 md:grid-cols-{{ columns }} gap-6 mb-6 metric-grid">
    {{ caller() }}
</div>
{% endmacro %}

{# Loading State - For dynamic content #}
{% macro loading_state(text="Carregando...") %}
<div class="flex items-center justify-center p-8">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    <span class="ml-3 text-gray-600">{{ text|e }}</span>
</div>
{% endmacro %}

{# Error State - For failed dynamic content #}
{% macro error_state(message="Erro ao carregar dados") %}
<div class="flex items-center justify-center p-8 text-gray-500">
    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
    </svg>
    <span>{{ message|e }}</span>
</div>
{% endmacro %}
