<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data:; font-src 'self' data:;">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - DataHub Amigo One Produto</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                primary: '#60a5fa',
                secondary: '#93c5fd',
                tertiary: '#dbeafe',
                success: '#34C759',
                warning: '#FF9500',
                danger: '#FF3B30',
                systemBlue: '#60a5fa',
                systemGreen: '#34C759',
                lightBlue: '#93c5fd',
                systemGray: {
                  DEFAULT: '#8E8E93',
                  light: '#AEAEB2',
                  lighter: '#C7C7CC',
                  lightest: '#D1D1D6',
                  extralight: '#E5E5EA',
                  ultralight: '#F2F2F7'
                }
              }
            }
          }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        .hero-gradient {
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 30%, #f3f4f6 70%, #e5e7eb 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.98);
            border: 1px solid rgba(59, 130, 246, 0.2);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .pulse-glow {
            animation: pulse-glow 3s ease-in-out infinite;
        }
        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 20px rgba(88, 86, 214, 0.3); }
            50% { box-shadow: 0 0 40px rgba(88, 86, 214, 0.6); }
        }

        .wave-hand {
            animation: wave 2.5s ease-in-out infinite;
            transform-origin: 70% 70%;
        }

        @keyframes wave {
            0%, 100% { transform: rotate(0deg); }
            10%, 30%, 50%, 70%, 90% { transform: rotate(8deg); }
            20%, 40%, 60%, 80% { transform: rotate(-4deg); }
        }

        .hero-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 30%, #e2e8f0 100%);
        }

        .login-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(59, 130, 246, 0.15);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
        }

        .elegant-fade-in {
            animation: elegantFadeIn 1.2s ease-out;
        }

        @keyframes elegantFadeIn {
            0% {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .slide-in-left {
            animation: slideInLeft 1s ease-out;
        }

        @keyframes slideInLeft {
            0% {
                opacity: 0;
                transform: translateX(-50px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .button-glow {
            position: relative;
            overflow: hidden;
        }

        .button-glow::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .button-glow:hover::before {
            left: 100%;
        }

        /* Ensure Tailwind classes work */
        .min-h-screen { min-height: 100vh; }
        .flex { display: flex; }
        .items-center { align-items: center; }
        .justify-center { justify-content: center; }
        .grid { display: grid; }
        .space-y-4 > * + * { margin-top: 1rem; }
        .space-y-5 > * + * { margin-top: 1.25rem; }
        .rounded-xl { border-radius: 0.75rem; }
        .rounded-2xl { border-radius: 1rem; }
        .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
        .shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
    </style>
</head>
<body class="min-h-screen hero-gradient flex items-center justify-center p-4">
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-gray-100 opacity-30 rounded-full floating-animation"></div>
        <div class="absolute bottom-1/4 right-1/4 w-48 h-48 bg-gray-200 opacity-20 rounded-full floating-animation" style="animation-delay: -3s;"></div>
        <div class="absolute top-3/4 left-1/3 w-32 h-32 bg-blue-100 opacity-40 rounded-full floating-animation" style="animation-delay: -1.5s;"></div>
        <div class="absolute top-1/2 right-1/3 w-20 h-20 bg-blue-50 opacity-50 rounded-full floating-animation" style="animation-delay: -4s;"></div>
    </div>

    <div class="relative z-10 w-full max-w-5xl mx-auto px-4">
        <div class="grid lg:grid-cols-2 gap-6 lg:gap-12 items-center min-h-[85vh] py-8 lg:py-0">
            <!-- Hero Section -->
            <div class="slide-in-left text-gray-900 space-y-4 lg:space-y-6 lg:pr-8 order-2 lg:order-1">
                <!-- Logo Principal -->
                <div class="flex justify-center lg:justify-start">
                    <img src="{{ url_for('static', filename='image.png') }}" alt="Amigo One" class="w-24 h-24 lg:w-28 lg:h-28 object-contain opacity-90">
                </div>

                <!-- Título Principal -->
                <div class="space-y-3 text-center lg:text-left">
                    <h1 class="text-2xl lg:text-4xl font-bold text-gray-900 leading-tight">
                        DataHub Amigo One
                    </h1>
                    <p class="text-blue-600 text-base lg:text-lg font-semibold">Análise de Produto</p>
                </div>

                <!-- Descrição -->
                <div class="space-y-4 text-center lg:text-left">
                    <p class="text-gray-600 text-sm lg:text-base leading-relaxed">
                        Plataforma interna para análise do uso do produto pelos nossos clientes médicos.
                    </p>

                    <!-- Saudação com emoji após o texto -->
                    <div class="flex items-center justify-center lg:justify-start space-x-3">
                        <span class="text-gray-700 font-medium text-sm lg:text-base">Seja bem-vindo!</span>
                        <span class="text-2xl wave-hand">👋</span>
                    </div>
                </div>

                <!-- Features simplificadas - apenas no desktop -->
                <div class="hidden lg:block space-y-3">
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-bar text-white text-xs"></i>
                        </div>
                        <span class="text-gray-700 text-sm">Analytics de comportamento</span>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-white text-xs"></i>
                        </div>
                        <span class="text-gray-700 text-sm">Experiência do usuário</span>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-blue-400 rounded-lg flex items-center justify-center">
                            <i class="fas fa-cogs text-white text-xs"></i>
                        </div>
                        <span class="text-gray-700 text-sm">Monitoramento 24/7</span>
                    </div>
                </div>
            </div>

            <!-- Login Form -->
            <div class="w-full max-w-sm mx-auto elegant-fade-in lg:max-w-md order-1 lg:order-2">
                <div class="login-card rounded-2xl shadow-2xl p-6 lg:p-8">
                    <!-- Header do formulário -->
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Acesso ao Sistema</h3>
                        <p class="text-gray-500 text-sm">Entre com suas credenciais</p>
                    </div>

                    {% if error %}
                        <div class="mb-4 p-4 rounded-lg bg-red-50 border border-red-200 text-red-700">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                {{ error|e }}
                            </div>
                        </div>
                    {% endif %}

                    <form method="POST" class="space-y-5">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <div class="space-y-2">
                            <label for="username" class="block text-sm font-semibold text-gray-700">
                                <i class="fas fa-user mr-2 text-blue-600"></i>Usuário
                            </label>
                            <input
                                type="text"
                                id="username"
                                name="username"
                                required
                                class="w-full px-4 py-2.5 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 bg-gray-50 focus:bg-white shadow-sm text-sm"
                                placeholder="Digite seu usuário"
                            >
                        </div>

                        <div class="space-y-2">
                            <label for="password" class="block text-sm font-semibold text-gray-700">
                                <i class="fas fa-lock mr-2 text-blue-600"></i>Senha
                            </label>
                            <input
                                type="password"
                                id="password"
                                name="password"
                                required
                                class="w-full px-4 py-2.5 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 bg-gray-50 focus:bg-white shadow-sm text-sm"
                                placeholder="Digite sua senha"
                            >
                        </div>

                        <button
                            type="submit"
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2.5 px-4 rounded-xl transition-all duration-300 transform hover:scale-[1.02] focus:ring-4 focus:ring-blue-300 shadow-lg hover:shadow-xl button-glow text-sm"
                        >
                            <span class="relative z-10">
                                <i class="fas fa-sign-in-alt mr-2"></i>Entrar
                            </span>
                        </button>
                    </form>

                    <div class="mt-6 pt-4 border-t border-gray-100">
                        <div class="text-center">
                            <p class="text-xs text-gray-500 mb-2">
                                <i class="fas fa-shield-alt mr-1 text-blue-600"></i>
                                Ambiente seguro e compatível com LGPD
                            </p>
                            <div class="flex items-center justify-center space-x-3 text-xs text-gray-400">
                                <span><i class="fas fa-user-shield mr-1"></i>LGPD</span>
                                <span><i class="fas fa-lock mr-1"></i>Seguro</span>
                                <span><i class="fas fa-heartbeat mr-1"></i>Disponível</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            });
        }, 5000);

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                e.preventDefault();
                alert('Por favor, preencha todos os campos.');
            }
        });
    </script>
</body>
</html>
