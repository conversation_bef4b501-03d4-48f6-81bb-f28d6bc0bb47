{% extends 'base.html' %}

{% block title %}Amigo Intelligence - Amigo One Analytics{% endblock %}

{% block header %}Análise do Amigo Intelligence{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">INTELIGÊNCIA ARTIFICIAL</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Análise do Amigo Intelligence</h1>
            <p class="text-gray-600 mb-6">Monitore o uso da inteligência artificial, tipos de prompts gerados e interações com o assistente virtual para otimizar a experiência dos médicos.</p>
            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ data.features.ai_usage.total_chats|e }}</div>
                    <div class="text-xs text-gray-500">Total de chats</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ data.features.ai_usage.text_prompts|e }}</div>
                    <div class="text-xs text-gray-500">Prompts de texto</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ (data.features.ai_usage.image_prompts + data.features.ai_usage.pdf_prompts)|e }}</div>
                    <div class="text-xs text-gray-500">Arquivos gerados</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Gerar Relatório
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Filtrar Dados
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">{{ (data.features.ai_usage.text_prompts / data.features.ai_usage.total_chats * 100) | round | int }}%</div>
                    <div class="text-sm text-gray-500">Texto</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- KPI Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Chats Totais -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Chats Totais</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.features.ai_usage.total_chats|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Total de interações com IA</span>
            <span class="ml-auto text-systemBlue font-medium">+18%</span>
        </div>
    </div>

    <!-- Prompts de Texto -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Prompts de Texto</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.features.ai_usage.text_prompts|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Respostas em texto</span>
            <span class="ml-auto text-systemBlue font-medium">+15.2%</span>
        </div>
    </div>

    <!-- Prompts de Imagem -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Prompts de Imagem</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.features.ai_usage.image_prompts|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Imagens geradas</span>
            <span class="ml-auto text-systemBlue font-medium">+23.7%</span>
        </div>
    </div>

    <!-- Prompts de PDF -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Prompts de PDF</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.features.ai_usage.pdf_prompts|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>PDFs gerados</span>
            <span class="ml-auto text-systemBlue font-medium">+31.5%</span>
        </div>
    </div>
</div>

<!-- KPIs Adicionais -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- Taxa de Engajamento -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Taxa de Engajamento</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ (data.features.ai_usage.total_chats / data.users.active_users * 100) | round | int }}%</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Usuários ativos que usam IA</span>
            <span class="ml-auto text-systemBlue font-medium">+12.3%</span>
        </div>
    </div>

    <!-- Média de Prompts por Usuário -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Prompts por Usuário</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ (data.features.ai_usage.total_chats / data.features.ai_usage.unique_users) | round(1) }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Média mensal por usuário ativo</span>
            <span class="ml-auto text-systemBlue font-medium">+8.7%</span>
        </div>
    </div>

    <!-- Satisfação do Usuário -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Satisfação</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">4.7<span class="text-sm text-gray-500">/5</span></p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Avaliação média das respostas</span>
            <span class="ml-auto text-systemBlue font-medium">+0.3</span>
        </div>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Distribuição de Tipos de Prompts -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Distribuição de Tipos de Prompts</h2>
            <div class="flex items-center">
                <select id="promptTypesPeriod" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="month">Este Mês</option>
                    <option value="quarter">Este Trimestre</option>
                    <option value="year">Este Ano</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="promptTypesChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Os prompts de texto representam a maioria das interações (85%), seguidos por imagens (10%) e PDFs (5%). Notamos um crescimento de 18% na geração de imagens no último trimestre, indicando maior adoção dessa funcionalidade.</p>
        </div>
    </div>

    <!-- Tendência de Uso da IA -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Tendência de Uso da IA</h2>
            <div class="flex items-center">
                <div class="flex space-x-2">
                    <span class="inline-block w-3 h-3 bg-systemBlue rounded-full"></span>
                    <span class="text-xs text-gray-600 mr-3">Total</span>
                    <span class="inline-block w-3 h-3 bg-blue-300 rounded-full"></span>
                    <span class="text-xs text-gray-600">Premium</span>
                </div>
            </div>
        </div>
        <div class="h-64">
            <canvas id="aiUsageTrendChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> O uso da IA tem crescido consistentemente, com aumento de 45% nos últimos 6 meses. Usuários premium utilizam a IA 2.3x mais que usuários free, com maior frequência de uso durante a semana.</p>
        </div>
    </div>
</div>

<!-- Análise de Tópicos e Comportamento -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Tópicos Populares -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Tópicos Populares</h2>
            <div class="flex items-center">
                <button class="text-xs text-systemBlue hover:underline">Ver todos os tópicos</button>
            </div>
        </div>
        <div class="h-64">
            <canvas id="popularTopicsChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Consultas sobre diagnósticos (32%), medicamentos (28%) e procedimentos (24%) são os tópicos mais frequentes. Observamos um aumento de 41% em consultas relacionadas a novas terapias e tratamentos emergentes.</p>
        </div>
    </div>

    <!-- Padrões de Uso -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Padrões de Uso</h2>
            <div class="flex items-center">
                <select id="usagePatternFilter" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="hourly">Por Hora</option>
                    <option value="daily">Por Dia</option>
                    <option value="specialty">Por Especialidade</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="usagePatternsChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Picos de uso ocorrem entre 10-12h e 14-16h, com maior atividade às terças e quintas. Dermatologistas e cardiologistas são os maiores usuários da IA, com média de 8.3 e 7.9 prompts por dia, respectivamente.</p>
        </div>
    </div>
</div>

<!-- Desempenho e Qualidade -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Tempo de Resposta -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Tempo de Resposta</h2>
            <div class="flex items-center">
                <span class="text-xs text-blue-500 font-medium">Média: 2.3s</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="responseTimeChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> O tempo médio de resposta é de 2.3 segundos, com melhoria de 15% nos últimos 3 meses. Prompts de texto são processados mais rapidamente (1.8s) que prompts de imagem (3.5s) e PDF (4.2s).</p>
        </div>
    </div>

    <!-- Qualidade das Respostas -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Qualidade das Respostas</h2>
            <div class="flex items-center">
                <div class="flex space-x-1">
                    <svg class="w-4 h-4 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                    </svg>
                    <svg class="w-4 h-4 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                    </svg>
                    <svg class="w-4 h-4 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                    </svg>
                    <svg class="w-4 h-4 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                    </svg>
                    <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                    </svg>
                </div>
            </div>
        </div>
        <div class="h-64">
            <canvas id="responseQualityChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> 92% das respostas são avaliadas como "úteis" ou "muito úteis" pelos usuários. A taxa de rejeição (respostas descartadas) caiu de 8.2% para 4.7% nos últimos 6 meses, indicando melhoria na qualidade.</p>
        </div>
    </div>
</div>

<!-- Transcrição de Áudio -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <div class="flex items-center">
            <h2 class="text-base font-semibold text-label-DEFAULT">Transcrição de Áudio</h2>
            <div class="ml-2 px-2 py-0.5 bg-blue-100 rounded-full flex items-center">
                <svg class="w-3 h-3 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">Nova Funcionalidade</span>
            </div>
        </div>
        <div class="flex items-center">
            <button class="text-xs text-systemBlue hover:underline flex items-center">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Ver detalhes
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Estatísticas de Transcrição -->
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div class="bg-blue-50 p-4 rounded-view border border-blue-100">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-sm font-semibold">Áudios Transcritos</h3>
                        <p class="text-xs text-label-secondary">Total</p>
                    </div>
                </div>
                <div class="text-center py-2">
                    <div class="text-3xl font-bold text-systemBlue">324</div>
                    <p class="text-xs text-gray-500 mt-1">+42% no último mês</p>
                </div>
            </div>

            <div class="bg-blue-50 p-4 rounded-view border border-blue-100">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-sm font-semibold">Tempo Médio</h3>
                        <p class="text-xs text-label-secondary">Duração</p>
                    </div>
                </div>
                <div class="text-center py-2">
                    <div class="text-3xl font-bold text-systemBlue">2:45</div>
                    <p class="text-xs text-gray-500 mt-1">Minutos:segundos</p>
                </div>
            </div>

            <div class="bg-blue-50 p-4 rounded-view border border-blue-100">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-sm font-semibold">Precisão</h3>
                        <p class="text-xs text-label-secondary">Taxa</p>
                    </div>
                </div>
                <div class="text-center py-2">
                    <div class="text-3xl font-bold text-systemBlue">97.8%</div>
                    <p class="text-xs text-gray-500 mt-1">+1.2% vs. mês anterior</p>
                </div>
            </div>
        </div>

        <!-- Gráfico de Adoção -->
        <div class="bg-white p-4 rounded-view border border-gray-200">
            <div class="flex items-center mb-3">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                    <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Adoção da Transcrição</h3>
                    <p class="text-xs text-label-secondary">Crescimento desde o lançamento</p>
                </div>
            </div>
            <div class="h-48">
                <canvas id="audioTranscriptionChart"></canvas>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Casos de Uso -->
        <div class="bg-white p-4 rounded-view border border-gray-200">
            <div class="flex items-center mb-3">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                    <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Casos de Uso</h3>
                    <p class="text-xs text-label-secondary">Principais aplicações</p>
                </div>
            </div>
            <div class="h-48">
                <canvas id="transcriptionUseCasesChart"></canvas>
            </div>
            <div class="mt-2 text-xs text-label-secondary">
                <p><strong>Insight:</strong> Transcrição de consultas (42%) e anotações rápidas (31%) são os principais casos de uso, seguidos por documentação de procedimentos (18%).</p>
            </div>
        </div>

        <!-- Idiomas Detectados -->
        <div class="bg-white p-4 rounded-view border border-gray-200">
            <div class="flex items-center mb-3">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                    <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Idiomas Detectados</h3>
                    <p class="text-xs text-label-secondary">Distribuição por idioma</p>
                </div>
            </div>
            <div class="h-48">
                <canvas id="transcriptionLanguagesChart"></canvas>
            </div>
            <div class="mt-2 text-xs text-label-secondary">
                <p><strong>Insight:</strong> Português brasileiro representa 94.2% das transcrições, com espanhol (3.5%) e inglês (2.3%) completando o top 3. A precisão é maior em português (98.1%) vs. outros idiomas (92.4%).</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados do dashboard
        const dashboardData = {{ data | tojson |e }};

        // Inicializar gráficos
        initializeAICharts(dashboardData);
    });

    function initializeAICharts(data) {
        // Gráfico de Distribuição de Tipos de Prompts
        if (document.getElementById('promptTypesChart')) {
            const promptTypesCtx = document.getElementById('promptTypesChart').getContext('2d');
            const promptTypesChart = new Chart(promptTypesCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Texto', 'Imagem', 'PDF'],
                    datasets: [{
                        data: [
                            data.features.ai_usage.text_prompts,
                            data.features.ai_usage.image_prompts,
                            data.features.ai_usage.pdf_prompts
                        ],
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(0, 122, 255, 0.5)',
                            'rgba(0, 122, 255, 0.3)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                padding: 15,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });

            // Adicionar event listener para o filtro de período
            if (document.getElementById('promptTypesPeriod')) {
                document.getElementById('promptTypesPeriod').addEventListener('change', function() {
                    // Simulação de dados diferentes para cada período
                    let newData;
                    if (this.value === 'month') {
                        newData = [
                            data.features.ai_usage.text_prompts,
                            data.features.ai_usage.image_prompts,
                            data.features.ai_usage.pdf_prompts
                        ];
                    } else if (this.value === 'quarter') {
                        newData = [
                            data.features.ai_usage.text_prompts * 3,
                            data.features.ai_usage.image_prompts * 3.2,
                            data.features.ai_usage.pdf_prompts * 2.8
                        ];
                    } else {
                        newData = [
                            data.features.ai_usage.text_prompts * 12,
                            data.features.ai_usage.image_prompts * 11.5,
                            data.features.ai_usage.pdf_prompts * 10.2
                        ];
                    }

                    promptTypesChart.data.datasets[0].data = newData;
                    promptTypesChart.update();
                });
            }
        }

        // Gráfico de Tendência de Uso da IA
        if (document.getElementById('aiUsageTrendChart')) {
            const aiUsageTrendCtx = document.getElementById('aiUsageTrendChart').getContext('2d');

            // Dados simulados para uso da IA por mês
            const months = ['Jan/2025', 'Fev/2025', 'Mar/2025', 'Abr/2025', 'Mai/2025', 'Jun/2025', 'Jul/2025', 'Ago/2025'];
            const totalUsage = [432, 487, 543, 598, 654, 712, 776, 832];
            const premiumUsage = [324, 365, 407, 448, 490, 534, 582, 624];

            new Chart(aiUsageTrendCtx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: [
                        {
                            label: 'Total',
                            data: totalUsage,
                            backgroundColor: 'rgba(0, 122, 255, 0.1)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Premium',
                            data: premiumUsage,
                            backgroundColor: 'transparent',
                            borderColor: 'rgba(0, 122, 255, 0.5)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: false,
                            borderDash: [5, 5]
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Tópicos Populares
        if (document.getElementById('popularTopicsChart')) {
            const popularTopicsCtx = document.getElementById('popularTopicsChart').getContext('2d');

            // Dados simulados para tópicos populares
            const popularTopics = {
                'Diagnósticos': 876,
                'Medicamentos': 765,
                'Procedimentos': 654,
                'Exames': 543,
                'Sintomas': 432,
                'Tratamentos': 321,
                'Outros': 210
            };

            new Chart(popularTopicsCtx, {
                type: 'bar',
                data: {
                    labels: Object.keys(popularTopics),
                    datasets: [{
                        label: 'Consultas',
                        data: Object.values(popularTopics),
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Padrões de Uso
        if (document.getElementById('usagePatternsChart')) {
            const usagePatternsCtx = document.getElementById('usagePatternsChart').getContext('2d');

            // Inicialmente mostrar padrão por hora
            renderUsagePatternChart(usagePatternsCtx, 'hourly');

            // Adicionar event listener para o filtro
            if (document.getElementById('usagePatternFilter')) {
                document.getElementById('usagePatternFilter').addEventListener('change', function() {
                    renderUsagePatternChart(usagePatternsCtx, this.value);
                });
            }

            function renderUsagePatternChart(ctx, type) {
                let chartData = {};
                let chartType = 'bar';
                let chartTitle = '';

                if (type === 'hourly') {
                    chartData = {
                        '8h': 42,
                        '9h': 78,
                        '10h': 124,
                        '11h': 145,
                        '12h': 87,
                        '13h': 65,
                        '14h': 112,
                        '15h': 132,
                        '16h': 98,
                        '17h': 76,
                        '18h': 54,
                        '19h': 32
                    };
                    chartTitle = 'Uso por Hora do Dia';
                } else if (type === 'daily') {
                    chartData = {
                        'Segunda': 432,
                        'Terça': 567,
                        'Quarta': 489,
                        'Quinta': 543,
                        'Sexta': 476,
                        'Sábado': 234,
                        'Domingo': 187
                    };
                    chartTitle = 'Uso por Dia da Semana';
                } else if (type === 'specialty') {
                    chartData = {
                        'Dermatologia': 832,
                        'Cardiologia': 765,
                        'Pediatria': 654,
                        'Ortopedia': 543,
                        'Ginecologia': 487,
                        'Neurologia': 432,
                        'Outras': 876
                    };
                    chartTitle = 'Uso por Especialidade';
                }

                // Destruir gráfico anterior se existir
                if (window.usagePatternChartInstance) {
                    window.usagePatternChartInstance.destroy();
                }

                // Criar novo gráfico
                window.usagePatternChartInstance = new Chart(ctx, {
                    type: chartType,
                    data: {
                        labels: Object.keys(chartData),
                        datasets: [{
                            label: chartTitle,
                            data: Object.values(chartData),
                            backgroundColor: 'rgba(0, 122, 255, 0.7)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            title: {
                                display: true,
                                text: chartTitle,
                                font: {
                                    size: 14
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    display: true,
                                    color: 'rgba(0, 0, 0, 0.05)'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }
        }

        // Gráfico de Tempo de Resposta
        if (document.getElementById('responseTimeChart')) {
            const responseTimeCtx = document.getElementById('responseTimeChart').getContext('2d');

            // Dados simulados para tempo de resposta
            const responseTime = {
                'Jan/2025': 2.8,
                'Fev/2025': 2.7,
                'Mar/2025': 2.6,
                'Abr/2025': 2.5,
                'Mai/2025': 2.4,
                'Jun/2025': 2.3,
                'Jul/2025': 2.3,
                'Ago/2025': 2.2
            };

            new Chart(responseTimeCtx, {
                type: 'line',
                data: {
                    labels: Object.keys(responseTime),
                    datasets: [{
                        label: 'Tempo (segundos)',
                        data: Object.values(responseTime),
                        backgroundColor: 'transparent',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        pointBackgroundColor: 'rgba(0, 122, 255, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Qualidade das Respostas
        if (document.getElementById('responseQualityChart')) {
            const responseQualityCtx = document.getElementById('responseQualityChart').getContext('2d');

            // Dados simulados para qualidade das respostas
            const responseQuality = {
                'Muito útil': 45,
                'Útil': 47,
                'Neutro': 5,
                'Pouco útil': 2,
                'Não útil': 1
            };

            new Chart(responseQualityCtx, {
                type: 'pie',
                data: {
                    labels: Object.keys(responseQuality),
                    datasets: [{
                        data: Object.values(responseQuality),
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.9)',
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(0, 122, 255, 0.5)',
                            'rgba(0, 122, 255, 0.3)',
                            'rgba(0, 122, 255, 0.1)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                padding: 15,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${percentage}%`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Adoção da Transcrição de Áudio
        if (document.getElementById('audioTranscriptionChart')) {
            const audioTranscriptionCtx = document.getElementById('audioTranscriptionChart').getContext('2d');

            // Dados simulados para adoção da transcrição
            const transcriptionAdoption = {
                'Semana 1': 12,
                'Semana 2': 28,
                'Semana 3': 45,
                'Semana 4': 67,
                'Semana 5': 98,
                'Semana 6': 145,
                'Semana 7': 198,
                'Semana 8': 267,
                'Semana 9': 324
            };

            new Chart(audioTranscriptionCtx, {
                type: 'line',
                data: {
                    labels: Object.keys(transcriptionAdoption),
                    datasets: [{
                        label: 'Transcrições',
                        data: Object.values(transcriptionAdoption),
                        backgroundColor: 'rgba(0, 122, 255, 0.1)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Casos de Uso da Transcrição
        if (document.getElementById('transcriptionUseCasesChart')) {
            const transcriptionUseCasesCtx = document.getElementById('transcriptionUseCasesChart').getContext('2d');

            // Dados simulados para casos de uso
            const transcriptionUseCases = {
                'Consultas': 42,
                'Anotações': 31,
                'Procedimentos': 18,
                'Outros': 9
            };

            new Chart(transcriptionUseCasesCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(transcriptionUseCases),
                    datasets: [{
                        data: Object.values(transcriptionUseCases),
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.8)',
                            'rgba(0, 122, 255, 0.6)',
                            'rgba(0, 122, 255, 0.4)',
                            'rgba(0, 122, 255, 0.2)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                padding: 15,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${percentage}%`;
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });
        }

        // Gráfico de Idiomas Detectados
        if (document.getElementById('transcriptionLanguagesChart')) {
            const transcriptionLanguagesCtx = document.getElementById('transcriptionLanguagesChart').getContext('2d');

            // Dados simulados para idiomas detectados
            const transcriptionLanguages = {
                'Português': 94.2,
                'Espanhol': 3.5,
                'Inglês': 2.3
            };

            new Chart(transcriptionLanguagesCtx, {
                type: 'pie',
                data: {
                    labels: Object.keys(transcriptionLanguages),
                    datasets: [{
                        data: Object.values(transcriptionLanguages),
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.8)',
                            'rgba(0, 122, 255, 0.5)',
                            'rgba(0, 122, 255, 0.3)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                padding: 15,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value}%`;
                                }
                            }
                        }
                    }
                }
            });
        }
    }
</script>
{% endblock %}
