{% extends 'base.html' %}

{% block title %}Agenda - Amigo One Analytics{% endblock %}

{% block header %}An<PERSON><PERSON><PERSON>nda{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">AGENDA</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Análise de Eventos da Agenda</h1>
            <p class="text-gray-600 mb-6">Monitore os eventos da agenda, analise os tipos de eventos e seus status para otimizar o gerenciamento de horários e atendimentos.</p>
            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ (data.agenda.event_types.BLOCK + data.agenda.event_types.DUTY + data.agenda.event_types.ATTENDANCE)|e }}</div>
                    <div class="text-xs text-gray-500">Total de eventos</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ data.agenda.event_types.ATTENDANCE|e }}</div>
                    <div class="text-xs text-gray-500">Atendimentos</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ (data.agenda.event_types.BLOCK + data.agenda.event_types.DUTY)|e }}</div>
                    <div class="text-xs text-gray-500">Bloqueios e Plantões</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    Gerar Relatório
                </button>
                <button class="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Filtrar Dados
                </button>
            </div>
        </div>
        <div class="md:w-1/3">
            <div class="bg-white p-6 rounded-view border border-gray-200 shadow-sm">
                <h3 class="text-sm font-semibold text-gray-800 mb-4">Distribuição de Eventos</h3>
                <div class="h-48">
                    <canvas id="eventDistributionChart"></canvas>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <p>Distribuição dos tipos de eventos na agenda</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- KPI Cards - Principais Métricas -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- Bloqueios -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Bloqueios (BLOCK)</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.agenda.event_types.BLOCK|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Bloqueios de agenda</span>
            <span class="ml-auto text-systemBlue font-medium">+8%</span>
        </div>
    </div>

    <!-- Plantões -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Plantões (DUTY)</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.agenda.event_types.DUTY|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Plantões agendados</span>
            <span class="ml-auto text-systemBlue font-medium">+12%</span>
        </div>
    </div>

    <!-- Atendimentos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Atendimentos (ATTENDANCE)</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.agenda.event_types.ATTENDANCE|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Atendimentos agendados</span>
            <span class="ml-auto text-systemBlue font-medium">+15%</span>
        </div>
    </div>
</div>

<!-- KPI Cards - Métricas Adicionais -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Taxa de Ocupação -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Taxa de Ocupação</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">78%</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Horários ocupados vs. disponíveis</span>
            <span class="ml-auto text-systemBlue font-medium">+5.2%</span>
        </div>
    </div>

    <!-- Taxa de Confirmação -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Taxa de Confirmação</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">85%</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Eventos confirmados</span>
            <span class="ml-auto text-systemBlue font-medium">+3.7%</span>
        </div>
    </div>

    <!-- Taxa de Cancelamento -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Taxa de Cancelamento</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">12%</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Eventos cancelados</span>
            <span class="ml-auto text-systemBlue font-medium">-2.1%</span>
        </div>
    </div>

    <!-- Tempo Médio entre Agendamentos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Tempo entre Agendamentos</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">3.2<span class="text-sm text-gray-500">dias</span></p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Média entre agendamentos</span>
            <span class="ml-auto text-systemBlue font-medium">-0.5 dias</span>
        </div>
    </div>
</div>

<!-- Gráficos Principais -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Eventos por Tipo -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Eventos por Tipo</h2>
            <div class="flex items-center">
                <select id="eventTypePeriod" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="month">Este Mês</option>
                    <option value="quarter">Este Trimestre</option>
                    <option value="year">Este Ano</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="eventTypesChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Atendimentos (ATTENDANCE) representam a maior parte dos eventos na agenda (75%). A proporção de bloqueios diminuiu 3.2% no último trimestre, indicando otimização da agenda.</p>
        </div>
    </div>

    <!-- Status Percentuais -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Status Percentuais por Tipo</h2>
            <div class="flex items-center">
                <button class="text-xs text-systemBlue hover:underline">Ver detalhes</button>
            </div>
        </div>
        <div class="h-64">
            <canvas id="statusPercentagesChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Plantões (DUTY) têm a maior taxa de confirmação (92%), enquanto atendimentos têm a maior taxa de cancelamento (15%). Implementar lembretes 24h antes reduziu cancelamentos em 4.3%.</p>
        </div>
    </div>
</div>

<!-- Análise Temporal e Distribuição -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Evolução Mensal -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Evolução Mensal de Eventos</h2>
            <div class="flex items-center">
                <div class="flex space-x-2">
                    <span class="inline-block w-3 h-3 bg-systemBlue rounded-full"></span>
                    <span class="text-xs text-gray-600 mr-3">ATTENDANCE</span>
                    <span class="inline-block w-3 h-3 bg-blue-300 rounded-full"></span>
                    <span class="text-xs text-gray-600">DUTY</span>
                </div>
            </div>
        </div>
        <div class="h-64">
            <canvas id="monthlyEvolutionChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Todos os tipos de eventos mostram tendência de crescimento nos últimos 8 meses. Atendimentos cresceram 23% no último trimestre, enquanto plantões aumentaram 17%.</p>
        </div>
    </div>

    <!-- Distribuição por Dia e Hora -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Distribuição por Período</h2>
            <div class="flex items-center">
                <select id="distributionFilter" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="weekday">Dia da Semana</option>
                    <option value="hour">Hora do Dia</option>
                    <option value="month">Mês</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="timeDistributionChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Terça e quinta-feira são os dias com maior volume de atendimentos (42% do total). O horário de pico é entre 9-11h (32%) e 14-16h (28%), sugerindo oportunidade para otimização de horários alternativos.</p>
        </div>
    </div>
</div>

<!-- Análise Detalhada -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Tipos de Atendimento -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Tipos de Atendimento</h2>
            <div class="flex items-center">
                <span class="text-xs text-systemBlue font-medium">Total: {{ data.agenda.event_types.ATTENDANCE|e }}</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="attendanceTypesChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Consultas representam 47% dos atendimentos, seguidas por retornos (23%). Atendimentos de emergência tiveram aumento de 8.5% no último mês, indicando possível necessidade de ajuste na disponibilidade.</p>
        </div>
    </div>

    <!-- Eficiência da Agenda -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Eficiência da Agenda</h2>
            <div class="flex items-center">
                <span class="text-xs text-blue-500 cursor-pointer hover:underline">Ver métricas completas</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="agendaEfficiencyChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> A taxa de utilização efetiva da agenda (tempo ocupado vs. disponível) é de 82%. Há oportunidade de otimização nos horários de 12-14h e após 17h, que têm ocupação abaixo de 60%.</p>
        </div>
    </div>
</div>

<!-- Legenda e Informações -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Legenda e Informações</h2>
        <div class="flex items-center">
            <button class="text-xs text-systemBlue hover:underline flex items-center">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                Exportar documentação
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
            <h3 class="text-sm font-medium text-gray-800 mb-2">Tipos de Eventos</h3>
            <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                    <div class="w-4 h-4 bg-systemBlue rounded-full mt-0.5 mr-2"></div>
                    <div>
                        <span class="font-medium">BLOCK:</span> Bloqueio de agenda - períodos reservados onde não são permitidos agendamentos.
                    </div>
                </li>
                <li class="flex items-start">
                    <div class="w-4 h-4 bg-blue-400 rounded-full mt-0.5 mr-2"></div>
                    <div>
                        <span class="font-medium">DUTY:</span> Plantão - períodos reservados para plantões médicos.
                    </div>
                </li>
                <li class="flex items-start">
                    <div class="w-4 h-4 bg-blue-300 rounded-full mt-0.5 mr-2"></div>
                    <div>
                        <span class="font-medium">ATTENDANCE:</span> Atendimento - eventos referenciados na coluna event_id de new_attendances, que leva para o id do evento correspondente na tabela agenda_events.
                    </div>
                </li>
            </ul>
        </div>

        <div>
            <h3 class="text-sm font-medium text-gray-800 mb-2">Status dos Eventos</h3>
            <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                    <div class="w-4 h-4 bg-blue-500 rounded-full mt-0.5 mr-2"></div>
                    <div>
                        <span class="font-medium">CONFIRMADO:</span> Eventos confirmados e agendados.
                    </div>
                </li>
                <li class="flex items-start">
                    <div class="w-4 h-4 bg-blue-200 rounded-full mt-0.5 mr-2"></div>
                    <div>
                        <span class="font-medium">CANCELADO:</span> Eventos que foram cancelados.
                    </div>
                </li>
                <li class="flex items-start">
                    <div class="w-4 h-4 bg-gray-300 rounded-full mt-0.5 mr-2"></div>
                    <div>
                        <span class="font-medium">PENDENTE:</span> Eventos que ainda não foram confirmados.
                    </div>
                </li>
            </ul>
        </div>

        <div>
            <h3 class="text-sm font-medium text-gray-800 mb-2">Métricas de Eficiência</h3>
            <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                    <div class="w-4 h-4 flex items-center justify-center bg-blue-100 rounded-full mt-0.5 mr-2">
                        <span class="text-xs text-blue-500">%</span>
                    </div>
                    <div>
                        <span class="font-medium">Taxa de Ocupação:</span> Percentual de horários ocupados em relação ao total disponível.
                    </div>
                </li>
                <li class="flex items-start">
                    <div class="w-4 h-4 flex items-center justify-center bg-blue-100 rounded-full mt-0.5 mr-2">
                        <span class="text-xs text-blue-500">✓</span>
                    </div>
                    <div>
                        <span class="font-medium">Taxa de Confirmação:</span> Percentual de eventos confirmados em relação ao total agendado.
                    </div>
                </li>
                <li class="flex items-start">
                    <div class="w-4 h-4 flex items-center justify-center bg-blue-100 rounded-full mt-0.5 mr-2">
                        <span class="text-xs text-blue-500">⏱</span>
                    </div>
                    <div>
                        <span class="font-medium">Tempo entre Agendamentos:</span> Média de dias entre a solicitação e a data do evento.
                    </div>
                </li>
            </ul>
        </div>
    </div>

    <div class="mt-6 p-4 bg-blue-50 rounded-md border border-blue-100">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-800">Nota sobre os dados</h3>
                <p class="mt-1 text-xs text-gray-600">Os dados são provenientes das tabelas <code>new_attendances</code> e <code>agenda_events</code>. A análise considera eventos dos últimos 12 meses, com atualização diária. Para análises personalizadas ou exportação de dados brutos, utilize a opção "Exportar documentação".</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados do dashboard
        const dashboardData = {{ data | tojson |e }};

        // Inicializar gráficos
        initializeAgendaCharts(dashboardData);
    });

    function initializeAgendaCharts(data) {
        // Gráfico de Distribuição de Eventos
        if (document.getElementById('eventDistributionChart')) {
            const eventDistributionCtx = document.getElementById('eventDistributionChart').getContext('2d');
            new Chart(eventDistributionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Bloqueios', 'Plantões', 'Atendimentos'],
                    datasets: [{
                        data: [
                            data.agenda.event_types.BLOCK,
                            data.agenda.event_types.DUTY,
                            data.agenda.event_types.ATTENDANCE
                        ],
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.8)',
                            'rgba(0, 122, 255, 0.6)',
                            'rgba(0, 122, 255, 0.4)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });
        }

        // Gráfico de Eventos por Tipo
        if (document.getElementById('eventTypesChart')) {
            const eventTypesCtx = document.getElementById('eventTypesChart').getContext('2d');
            const eventTypesChart = new Chart(eventTypesCtx, {
                type: 'bar',
                data: {
                    labels: ['BLOCK', 'DUTY', 'ATTENDANCE'],
                    datasets: [{
                        label: 'Quantidade',
                        data: [
                            data.agenda.event_types.BLOCK,
                            data.agenda.event_types.DUTY,
                            data.agenda.event_types.ATTENDANCE
                        ],
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.8)',
                            'rgba(0, 122, 255, 0.6)',
                            'rgba(0, 122, 255, 0.4)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Adicionar event listener para o filtro de período
            if (document.getElementById('eventTypePeriod')) {
                document.getElementById('eventTypePeriod').addEventListener('change', function() {
                    // Simulação de dados diferentes para cada período
                    let newData;
                    if (this.value === 'month') {
                        newData = [
                            data.agenda.event_types.BLOCK,
                            data.agenda.event_types.DUTY,
                            data.agenda.event_types.ATTENDANCE
                        ];
                    } else if (this.value === 'quarter') {
                        newData = [
                            data.agenda.event_types.BLOCK * 3,
                            data.agenda.event_types.DUTY * 3,
                            data.agenda.event_types.ATTENDANCE * 3
                        ];
                    } else {
                        newData = [
                            data.agenda.event_types.BLOCK * 12,
                            data.agenda.event_types.DUTY * 12,
                            data.agenda.event_types.ATTENDANCE * 12
                        ];
                    }

                    eventTypesChart.data.datasets[0].data = newData;
                    eventTypesChart.update();
                });
            }
        }

        // Gráfico de Status Percentuais
        if (document.getElementById('statusPercentagesChart')) {
            const statusPercentagesCtx = document.getElementById('statusPercentagesChart').getContext('2d');
            new Chart(statusPercentagesCtx, {
                type: 'bar',
                data: {
                    labels: ['BLOCK', 'DUTY', 'ATTENDANCE'],
                    datasets: [
                        {
                            label: 'Confirmado',
                            data: [
                                data.agenda.status_percentages.BLOCK.CONFIRMADO,
                                data.agenda.status_percentages.DUTY.CONFIRMADO,
                                data.agenda.status_percentages.ATTENDANCE.CONFIRMADO
                            ],
                            backgroundColor: 'rgba(0, 122, 255, 0.8)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Cancelado',
                            data: [
                                data.agenda.status_percentages.BLOCK.CANCELADO,
                                data.agenda.status_percentages.DUTY.CANCELADO,
                                data.agenda.status_percentages.ATTENDANCE.CANCELADO
                            ],
                            backgroundColor: 'rgba(0, 122, 255, 0.4)',
                            borderColor: 'rgba(0, 122, 255, 0.8)',
                            borderWidth: 1
                        },
                        {
                            label: 'Pendente',
                            data: [
                                data.agenda.status_percentages.BLOCK.PENDENTE,
                                data.agenda.status_percentages.DUTY.PENDENTE,
                                data.agenda.status_percentages.ATTENDANCE.PENDENTE
                            ],
                            backgroundColor: 'rgba(156, 163, 175, 0.5)',
                            borderColor: 'rgba(156, 163, 175, 0.8)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 11
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Evolução Mensal
        if (document.getElementById('monthlyEvolutionChart')) {
            const monthlyEvolutionCtx = document.getElementById('monthlyEvolutionChart').getContext('2d');

            // Preparar dados para o gráfico
            const months = Object.keys(data.agenda.events_by_month);
            const eventTypes = ['ATTENDANCE', 'DUTY']; // Removido BLOCK para simplificar

            const datasets = eventTypes.map((type, index) => {
                const colors = [
                    'rgba(0, 122, 255, 1)', // Azul para ATTENDANCE
                    'rgba(0, 122, 255, 0.6)' // Azul mais claro para DUTY
                ];

                return {
                    label: type,
                    data: months.map(month => data.agenda.events_by_month[month][type]),
                    borderColor: colors[index],
                    backgroundColor: colors[index].replace('1)', '0.1)').replace('0.6)', '0.05)'),
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                };
            });

            new Chart(monthlyEvolutionCtx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Distribuição por Período
        if (document.getElementById('timeDistributionChart')) {
            const timeDistributionCtx = document.getElementById('timeDistributionChart').getContext('2d');

            // Inicialmente mostrar distribuição por dia da semana
            renderTimeDistributionChart(timeDistributionCtx, 'weekday');

            // Adicionar event listener para o filtro
            if (document.getElementById('distributionFilter')) {
                document.getElementById('distributionFilter').addEventListener('change', function() {
                    renderTimeDistributionChart(timeDistributionCtx, this.value);
                });
            }

            function renderTimeDistributionChart(ctx, type) {
                let chartData = {};
                let chartTitle = '';

                if (type === 'weekday') {
                    chartData = {
                        'Segunda': 145,
                        'Terça': 187,
                        'Quarta': 156,
                        'Quinta': 192,
                        'Sexta': 167,
                        'Sábado': 98,
                        'Domingo': 45
                    };
                    chartTitle = 'Distribuição por Dia da Semana';
                } else if (type === 'hour') {
                    chartData = {
                        '8-9h': 87,
                        '9-10h': 156,
                        '10-11h': 178,
                        '11-12h': 145,
                        '12-13h': 67,
                        '13-14h': 78,
                        '14-15h': 167,
                        '15-16h': 154,
                        '16-17h': 132,
                        '17-18h': 98,
                        '18-19h': 76
                    };
                    chartTitle = 'Distribuição por Hora do Dia';
                } else if (type === 'month') {
                    chartData = {
                        'Jan': 876,
                        'Fev': 923,
                        'Mar': 1045,
                        'Abr': 987,
                        'Mai': 1123,
                        'Jun': 1056,
                        'Jul': 1234,
                        'Ago': 1345,
                        'Set': 1287,
                        'Out': 1432,
                        'Nov': 1345,
                        'Dez': 1156
                    };
                    chartTitle = 'Distribuição por Mês';
                }

                // Destruir gráfico anterior se existir
                if (window.timeDistributionChartInstance) {
                    window.timeDistributionChartInstance.destroy();
                }

                // Criar novo gráfico
                window.timeDistributionChartInstance = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: Object.keys(chartData),
                        datasets: [{
                            label: 'Eventos',
                            data: Object.values(chartData),
                            backgroundColor: 'rgba(0, 122, 255, 0.7)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            title: {
                                display: true,
                                text: chartTitle,
                                font: {
                                    size: 14
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    display: true,
                                    color: 'rgba(0, 0, 0, 0.05)'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }
        }

        // Gráfico de Tipos de Atendimento
        if (document.getElementById('attendanceTypesChart')) {
            const attendanceTypesCtx = document.getElementById('attendanceTypesChart').getContext('2d');
            new Chart(attendanceTypesCtx, {
                type: 'pie',
                data: {
                    labels: Object.keys(data.agenda.attendance_types),
                    datasets: [{
                        data: Object.values(data.agenda.attendance_types),
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.8)',
                            'rgba(0, 122, 255, 0.6)',
                            'rgba(0, 122, 255, 0.4)',
                            'rgba(0, 122, 255, 0.2)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${percentage}%`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Eficiência da Agenda
        if (document.getElementById('agendaEfficiencyChart')) {
            const agendaEfficiencyCtx = document.getElementById('agendaEfficiencyChart').getContext('2d');

            // Dados simulados para eficiência da agenda
            const timeSlots = ['8-10h', '10-12h', '12-14h', '14-16h', '16-18h', '18-20h'];
            const efficiencyData = [78, 92, 45, 87, 76, 38];

            new Chart(agendaEfficiencyCtx, {
                type: 'bar',
                data: {
                    labels: timeSlots,
                    datasets: [{
                        label: 'Taxa de Ocupação (%)',
                        data: efficiencyData,
                        backgroundColor: function(context) {
                            const value = context.dataset.data[context.dataIndex];
                            return value > 80 ? 'rgba(0, 122, 255, 0.8)' :
                                   value > 60 ? 'rgba(0, 122, 255, 0.6)' :
                                   'rgba(0, 122, 255, 0.4)';
                        },
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }
    }
</script>
{% endblock %}
