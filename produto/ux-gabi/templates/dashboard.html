{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, chart_container, metric_grid, loading_state, error_state %}

{% block title %}Dashboard - Produto{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section bg-gradient-to-r from-blue-50 to-indigo-50 rounded-view p-8 mb-6">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">
                Ol<PERSON>, {{ current_user.name }}! 
                <span class="wave-animation">👋</span>
            </h1>
            <p class="text-lg text-gray-600">
                Bem-vindo ao painel de análise do produto. Aqui você encontra insights sobre o uso da plataforma.
            </p>
        </div>
        <div class="hidden md:block">
            <div class="bg-white rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500">Última atualização</div>
                <div class="text-lg font-semibold text-gray-900">{{ data.last_updated }}</div>
            </div>
        </div>
    </div>
</div>

<!-- HYBRID STRUCTURED: KPI Cards using new macro system -->
{% call metric_grid("5") %}
    <!-- Usuários Ativos - HYBRID: Static template + Dynamic updates -->
    {{ kpi_card(
        title="Usuários Ativos",
        value=data.users.total_active if data.users.total_active else '0',
        percentage='+' + (data.users.growth_percentage|string if data.users.growth_percentage else '0') + '%',
        is_positive=true,
        icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>',
        api_endpoint="/api/product/users/active",
        metric_id="activeUsers"
    ) }}

    <!-- Sessões Hoje - HYBRID: Using macro pattern -->
    {{ kpi_card(
        title="Sessões Hoje",
        value=data.users.sessions_today if data.users.sessions_today else '0',
        percentage='+' + (data.users.sessions_growth|string if data.users.sessions_growth else '0') + '%',
        is_positive=true,
        icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>',
        api_endpoint="/api/product/users/activity",
        metric_id="todaySessions"
    ) }}

    <!-- Tempo Médio de Sessão - HYBRID: Using macro pattern -->
    {{ kpi_card(
        title="Tempo Médio",
        value=data.users.avg_session_time if data.users.avg_session_time else '0min',
        percentage='+' + (data.users.session_time_growth|string if data.users.session_time_growth else '0') + '%',
        is_positive=true,
        icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>',
        api_endpoint="/api/product/users/activity",
        metric_id="avgSessionTime"
    ) }}

    <!-- Taxa de Retenção - HYBRID: Using macro pattern -->
    {{ kpi_card(
        title="Taxa Retenção",
        value=(data.users.retention_rate|string if data.users.retention_rate else '0') + '%',
        percentage='+' + (data.users.retention_growth|string if data.users.retention_growth else '0') + '%',
        is_positive=true,
        icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>',
        api_endpoint="/api/product/users/behavior",
        metric_id="retentionRate"
    ) }}

    <!-- NPS Score - HYBRID: Using macro pattern -->
    {{ kpi_card(
        title="NPS Score",
        value=data.users.nps_score if data.users.nps_score else '0',
        percentage='+' + (data.users.nps_growth|string if data.users.nps_growth else '0'),
        is_positive=true,
        icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976-2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>',
        api_endpoint="/api/product/users/behavior",
        metric_id="npsScore"
    ) }}
{% endcall %}

<!-- Gráficos Principais -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6 chart-grid">
    <!-- Gráfico de Crescimento de Usuários -->
    {{ chart_container(
        title="Crescimento de Usuários",
        chart_id="userGrowthChart",
        height="64",
        api_endpoint="/api/product/dashboard/charts",
        filters='<select id="userGrowthPeriod" class="text-sm border border-gray-300 rounded-md px-2 py-1">
                    <option value="month">Último Mês</option>
                    <option value="quarter">Último Trimestre</option>
                    <option value="year">Último Ano</option>
                </select>'
    ) }}

    <!-- Gráfico de Distribuição por Especialidade -->
    {{ chart_container(
        title="Distribuição por Especialidade",
        chart_id="specialtyDistributionChart",
        height="64",
        api_endpoint="/api/product/dashboard/charts",
        filters='<button class="text-sm text-blue-600 hover:text-blue-700" onclick="showSpecialtyDetails()">
                    Ver Detalhes
                </button>'
    ) }}
</div>

<!-- Uso de Funcionalidades -->
{{ chart_container(
    title="Uso de Funcionalidades",
    chart_id="featureUsageChart",
    height="80",
    api_endpoint="/api/product/features/usage",
    filters='<select id="featureTimeRange" class="text-sm border border-gray-300 rounded-md px-2 py-1">
                <option value="week">Última Semana</option>
                <option value="month">Último Mês</option>
                <option value="quarter">Último Trimestre</option>
            </select>
            <select id="featureSelect" class="text-sm border border-gray-300 rounded-md px-2 py-1">
                <option value="all">Todas as Features</option>
                <option value="prontuario">Prontuário</option>
                <option value="agenda">Agenda</option>
                <option value="intelligence">Amigo Intelligence</option>
                <option value="pay">Amigo Pay</option>
                <option value="network">Rede Amigo</option>
                <option value="accounting">Contabilidade</option>
            </select>'
) }}

<!-- Análise Demográfica e Geográfica -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Análise Demográfica -->
    {{ chart_container(
        title="Análise Demográfica",
        chart_id="demographicChart",
        height="64",
        api_endpoint="/api/product/users/behavior",
        filters='<select id="demographicFilter" class="text-sm border border-gray-300 rounded-md px-2 py-1">
                    <option value="age">Por Idade</option>
                    <option value="specialty">Por Especialidade</option>
                    <option value="device">Por Dispositivo</option>
                </select>'
    ) }}

    <!-- Distribuição Geográfica -->
    {{ chart_container(
        title="Distribuição Geográfica",
        chart_id="geographicChart",
        height="64",
        api_endpoint="/api/product/users/behavior",
        filters='<select id="geoFilter" class="text-sm border border-gray-300 rounded-md px-2 py-1">
                    <option value="state">Por Estado</option>
                    <option value="region">Por Região</option>
                    <option value="city">Por Cidade</option>
                </select>'
    ) }}
</div>
{% endblock %}

{% block styles %}
<style>
    /* Animações e estilos customizados */
    @keyframes wave {
        0% { transform: rotate(0deg); }
        10% { transform: rotate(14deg); }
        20% { transform: rotate(-8deg); }
        30% { transform: rotate(14deg); }
        40% { transform: rotate(-4deg); }
        50% { transform: rotate(10deg); }
        60% { transform: rotate(0deg); }
        100% { transform: rotate(0deg); }
    }

    .wave-animation {
        animation: wave 2s ease-in-out infinite;
        transform-origin: 70% 70%;
        display: inline-block;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .rounded-view {
        border-radius: 12px;
    }

    .card-hover {
        transition: all 0.3s ease;
    }

    .card-hover:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .metric-card {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;
    }

    .metric-card:nth-child(1) { animation-delay: 0.1s; }
    .metric-card:nth-child(2) { animation-delay: 0.2s; }
    .metric-card:nth-child(3) { animation-delay: 0.3s; }
    .metric-card:nth-child(4) { animation-delay: 0.4s; }
    .metric-card:nth-child(5) { animation-delay: 0.5s; }

    .chart-container {
        animation: fadeInUp 0.8s ease-out;
        animation-fill-mode: both;
    }

    .hover-scale {
        transition: transform 0.2s ease;
    }

    .hover-scale:hover {
        transform: scale(1.02);
    }

    /* Responsividade melhorada */
    @media (max-width: 768px) {
        .hero-section {
            padding: 2rem 1rem;
        }

        .metric-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .chart-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<!-- Hybrid Manager - Structured pattern for SSR + CSR -->
<script src="{{ url_for('static', filename='js/hybrid-manager.js') }}"></script>

<script>
// Initialize Hybrid Manager for structured SSR + CSR pattern
document.addEventListener('DOMContentLoaded', function() {
    // Check if HybridManager is already defined
    if (typeof HybridManager !== 'undefined') {
        const hybridManager = new HybridManager({
            updateInterval: 60000, // Update every 60 seconds
            debug: true
        });

        hybridManager.init();

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            hybridManager.cleanup();
        });
    } else {
        console.error('HybridManager not found. Please check if the script is loaded correctly.');
    }
});
</script>
{% endblock %}
