{% extends 'base.html' %}

{% block title %}Amigo Pay - Amigo One Analytics{% endblock %}

{% block header %}Análise do Amigo Pay{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">FINANCEIRO</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Análise do Amigo Pay</h1>
            <p class="text-gray-600 mb-6">Monitore transações financeiras, contas ativas e volume de movimentação para otimizar a experiência de pagamentos dos médicos.</p>
            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ data.amigo_pay.accounts.total|e }}</div>
                    <div class="text-xs text-gray-500">Contas ativas</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ data.amigo_pay.transactions.PIX + data.amigo_pay.transactions.CARD + data.amigo_pay.transactions.BANK_SLIP + data.amigo_pay.transactions.TRANSFER + data.amigo_pay.transactions.TED + data.amigo_pay.transactions.PIX_REVERSAL|e }}</div>
                    <div class="text-xs text-gray-500">Transações totais</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">R$ {{ (data.amigo_pay.transaction_volume["Ago/2025"] / 100) | round | int }}</div>
                    <div class="text-xs text-gray-500">Volume mensal (R$)</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Gerar Relatório
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Filtrar Dados
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">{{ (data.amigo_pay.accounts.with_movement / data.amigo_pay.accounts.total * 100) | round | int }}%</div>
                    <div class="text-sm text-gray-500">Contas ativas</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- KPI Cards - Principais Métricas -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Transações PIX -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Transações PIX</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.amigo_pay.transactions.PIX|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Total de PIX realizados</span>
            <span class="ml-auto text-systemBlue font-medium">+22%</span>
        </div>
    </div>

    <!-- Transações Cartão -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Transações Cartão</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.amigo_pay.transactions.CARD|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Total de pagamentos com cartão</span>
            <span class="ml-auto text-systemBlue font-medium">+15.5%</span>
        </div>
    </div>

    <!-- Transações Boleto -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Transações Boleto</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.amigo_pay.transactions.BANK_SLIP|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Total de boletos pagos</span>
            <span class="ml-auto text-systemBlue font-medium">+8.2%</span>
        </div>
    </div>

    <!-- Transferências -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Transferências</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.amigo_pay.transactions.TRANSFER + data.amigo_pay.transactions.TED|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Total de transferências</span>
            <span class="ml-auto text-systemBlue font-medium">+12.7%</span>
        </div>
    </div>
</div>

<!-- KPI Cards - Métricas Adicionais -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Taxa de Conversão -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Taxa de Conversão</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ (data.amigo_pay.accounts.with_movement / data.amigo_pay.accounts.total * 100) | round | int }}<span class="text-sm text-gray-500">%</span></p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Contas ativas vs. cadastradas</span>
            <span class="ml-auto text-systemBlue font-medium">+5.2%</span>
        </div>
    </div>

    <!-- Ticket Médio -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Ticket Médio</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">R$ 292</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Valor médio por transação</span>
            <span class="ml-auto text-systemBlue font-medium">+5.2%</span>
        </div>
    </div>

    <!-- Taxa de Aprovação KYC -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Taxa de Aprovação KYC</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ (data.amigo_pay.accounts.kyc_approved / data.amigo_pay.accounts.total * 100) | round | int }}<span class="text-sm text-gray-500">%</span></p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Contas verificadas</span>
            <span class="ml-auto text-systemBlue font-medium">+3.7%</span>
        </div>
    </div>

    <!-- Taxa de Crescimento -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Taxa de Crescimento</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">59<span class="text-sm text-gray-500">%</span></p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Crescimento em 8 meses</span>
            <span class="ml-auto text-systemBlue font-medium">+12.3%</span>
        </div>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Distribuição de Transações -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Distribuição de Transações</h2>
            <div class="flex items-center">
                <select id="transactionPeriod" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="month">Este Mês</option>
                    <option value="quarter">Este Trimestre</option>
                    <option value="year">Este Ano</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="transactionDistributionChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> PIX é o método de pagamento mais utilizado (45%), seguido por cartão (30%). A proporção de PIX aumentou 8.3% no último trimestre, indicando preferência crescente por este método.</p>
        </div>
    </div>

    <!-- Volume de Transações -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Volume de Transações</h2>
            <div class="flex items-center">
                <div class="flex space-x-2">
                    <span class="inline-block w-3 h-3 bg-systemBlue rounded-full"></span>
                    <span class="text-xs text-gray-600 mr-3">Volume (R$)</span>
                </div>
            </div>
        </div>
        <div class="h-64">
            <canvas id="transactionVolumeChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> O volume de transações cresceu 59% nos últimos 8 meses, com tendência de alta contínua. O crescimento mês a mês médio é de 6.1%, com picos no meio do mês.</p>
        </div>
    </div>
</div>

<!-- Contas e Transações -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Contas por Integração -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Contas por Integração</h2>
            <div class="flex items-center">
                <span class="text-xs text-systemBlue font-medium">Total: {{ data.amigo_pay.accounts.total|e }}</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="accountIntegrationChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> {{ (data.amigo_pay.accounts.dock / data.amigo_pay.accounts.total * 100) | round | int }}% das contas estão integradas com Dock e {{ (data.amigo_pay.accounts.celcoin / data.amigo_pay.accounts.total * 100) | round | int }}% com Celcoin. Contas Dock têm taxa de ativação 12% maior que Celcoin.</p>
        </div>
    </div>

    <!-- Quantidade de Transações -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Quantidade de Transações</h2>
            <div class="flex items-center">
                <button class="text-xs text-systemBlue hover:underline">Ver detalhes</button>
            </div>
        </div>
        <div class="h-64">
            <canvas id="transactionCountChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> A quantidade de transações aumentou 49% nos últimos 8 meses. Dias úteis concentram 87% das transações, com terça e quarta-feira sendo os dias de maior volume.</p>
        </div>
    </div>
</div>

<!-- Análises Detalhadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Análise de Horários -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Análise de Horários</h2>
            <div class="flex items-center">
                <select id="timeAnalysisFilter" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="hour">Por Hora</option>
                    <option value="weekday">Por Dia da Semana</option>
                    <option value="month">Por Mês</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="timeAnalysisChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Os horários de pico são entre 10-12h (28%) e 14-16h (32%). Transações fora do horário comercial representam apenas 15% do total, indicando oportunidade para incentivos.</p>
        </div>
    </div>

    <!-- Análise de Retenção -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Análise de Retenção</h2>
            <div class="flex items-center">
                <span class="text-xs text-blue-500 cursor-pointer hover:underline">Ver métricas completas</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="retentionAnalysisChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> 78% dos usuários realizam pelo menos uma transação por mês após a primeira. A taxa de retenção após 3 meses é de 65%, indicando boa adesão ao serviço.</p>
        </div>
    </div>
</div>

<!-- Análise de KYC e Status de Contas -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Status de Contas e KYC</h2>
        <div class="flex items-center">
            <button class="text-xs text-systemBlue hover:underline flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Análise Detalhada
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-blue-50 p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">KYC Aprovado</h3>
                    <p class="text-xs text-label-secondary">Know Your Customer</p>
                </div>
            </div>
            <div class="text-center py-4">
                <div class="text-3xl font-bold text-systemBlue">{{ data.amigo_pay.accounts.kyc_approved|e }}</div>
                <p class="text-xs text-gray-500 mt-1">Contas verificadas</p>
            </div>
            <div class="flex items-center justify-between text-xs mt-2">
                <span class="text-gray-500">Taxa de aprovação</span>
                <span class="font-medium text-systemBlue">{{ (data.amigo_pay.accounts.kyc_approved / data.amigo_pay.accounts.total * 100) | round | int }}%</span>
            </div>
        </div>

        <div class="bg-blue-50 p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Contas com Movimentação</h3>
                    <p class="text-xs text-label-secondary">Contas ativas</p>
                </div>
            </div>
            <div class="text-center py-4">
                <div class="text-3xl font-bold text-systemBlue">{{ data.amigo_pay.accounts.with_movement|e }}</div>
                <p class="text-xs text-gray-500 mt-1">Contas com transações</p>
            </div>
            <div class="flex items-center justify-between text-xs mt-2">
                <span class="text-gray-500">Taxa de ativação</span>
                <span class="font-medium text-systemBlue">{{ (data.amigo_pay.accounts.with_movement / data.amigo_pay.accounts.total * 100) | round | int }}%</span>
            </div>
        </div>

        <div class="bg-blue-50 p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Ticket Médio</h3>
                    <p class="text-xs text-label-secondary">Valor médio por transação</p>
                </div>
            </div>
            <div class="text-center py-4">
                <div class="text-3xl font-bold text-systemBlue">R$ 292</div>
                <p class="text-xs text-gray-500 mt-1">Média por transação</p>
            </div>
            <div class="flex items-center justify-between text-xs mt-2">
                <span class="text-gray-500">Variação mensal</span>
                <span class="font-medium text-systemBlue">+5.2%</span>
            </div>
        </div>

        <div class="bg-blue-50 p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Tempo de Ativação</h3>
                    <p class="text-xs text-label-secondary">Primeira transação</p>
                </div>
            </div>
            <div class="text-center py-4">
                <div class="text-3xl font-bold text-systemBlue">3.2<span class="text-lg">dias</span></div>
                <p class="text-xs text-gray-500 mt-1">Média após cadastro</p>
            </div>
            <div class="flex items-center justify-between text-xs mt-2">
                <span class="text-gray-500">Melhoria</span>
                <span class="font-medium text-systemBlue">-0.8 dias</span>
            </div>
        </div>
    </div>

    <div class="mt-6 p-4 bg-blue-50 rounded-md border border-blue-100">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-800">Oportunidades de Melhoria</h3>
                <p class="mt-1 text-xs text-gray-600">A análise de funil mostra que 15% dos usuários abandonam o processo de KYC. Implementar um processo mais simplificado pode aumentar a taxa de conversão em até 8%. Usuários que completam o KYC em menos de 24h têm 3x mais chances de realizar uma transação na primeira semana.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados do dashboard
        const dashboardData = {{ data | tojson |e }};

        // Inicializar gráficos
        initializeAmigoPayCharts(dashboardData);
    });

    function initializeAmigoPayCharts(data) {
        // Gráfico de Distribuição de Transações
        if (document.getElementById('transactionDistributionChart')) {
            const transactionDistributionCtx = document.getElementById('transactionDistributionChart').getContext('2d');

            // Preparar dados para o gráfico
            const transactionTypes = {
                'PIX': data.amigo_pay.transactions.PIX,
                'Cartão': data.amigo_pay.transactions.CARD,
                'Boleto': data.amigo_pay.transactions.BANK_SLIP,
                'Transferência': data.amigo_pay.transactions.TRANSFER,
                'TED': data.amigo_pay.transactions.TED,
                'Estorno PIX': data.amigo_pay.transactions.PIX_REVERSAL
            };

            const transactionDistributionChart = new Chart(transactionDistributionCtx, {
                type: 'pie',
                data: {
                    labels: Object.keys(transactionTypes),
                    datasets: [{
                        data: Object.values(transactionTypes),
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.9)',
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(0, 122, 255, 0.5)',
                            'rgba(0, 122, 255, 0.4)',
                            'rgba(0, 122, 255, 0.3)',
                            'rgba(0, 122, 255, 0.2)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });

            // Adicionar event listener para o filtro de período
            if (document.getElementById('transactionPeriod')) {
                document.getElementById('transactionPeriod').addEventListener('change', function() {
                    // Simulação de dados diferentes para cada período
                    let multiplier = 1;
                    if (this.value === 'quarter') {
                        multiplier = 3;
                    } else if (this.value === 'year') {
                        multiplier = 12;
                    }

                    const newData = Object.values(transactionTypes).map(value => value * multiplier);
                    transactionDistributionChart.data.datasets[0].data = newData;
                    transactionDistributionChart.update();
                });
            }
        }

        // Gráfico de Volume de Transações
        if (document.getElementById('transactionVolumeChart')) {
            const transactionVolumeCtx = document.getElementById('transactionVolumeChart').getContext('2d');

            // Converter valores de centavos para reais
            const volumeInReais = {};
            Object.keys(data.amigo_pay.transaction_volume).forEach(month => {
                volumeInReais[month] = data.amigo_pay.transaction_volume[month] / 100;
            });

            new Chart(transactionVolumeCtx, {
                type: 'line',
                data: {
                    labels: Object.keys(volumeInReais),
                    datasets: [{
                        label: 'Volume (R$)',
                        data: Object.values(volumeInReais),
                        backgroundColor: 'rgba(0, 122, 255, 0.1)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.raw || 0;
                                    return `R$ ${value.toLocaleString('pt-BR')}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return `R$ ${value}`;
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Contas por Integração
        if (document.getElementById('accountIntegrationChart')) {
            const accountIntegrationCtx = document.getElementById('accountIntegrationChart').getContext('2d');
            new Chart(accountIntegrationCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Dock', 'Celcoin'],
                    datasets: [{
                        data: [data.amigo_pay.accounts.dock, data.amigo_pay.accounts.celcoin],
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(0, 122, 255, 0.4)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });
        }

        // Gráfico de Quantidade de Transações
        if (document.getElementById('transactionCountChart')) {
            const transactionCountCtx = document.getElementById('transactionCountChart').getContext('2d');
            new Chart(transactionCountCtx, {
                type: 'bar',
                data: {
                    labels: Object.keys(data.amigo_pay.transaction_count),
                    datasets: [{
                        label: 'Quantidade',
                        data: Object.values(data.amigo_pay.transaction_count),
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Análise de Horários
        if (document.getElementById('timeAnalysisChart')) {
            const timeAnalysisCtx = document.getElementById('timeAnalysisChart').getContext('2d');

            // Inicialmente mostrar análise por hora
            renderTimeAnalysisChart(timeAnalysisCtx, 'hour');

            // Adicionar event listener para o filtro
            if (document.getElementById('timeAnalysisFilter')) {
                document.getElementById('timeAnalysisFilter').addEventListener('change', function() {
                    renderTimeAnalysisChart(timeAnalysisCtx, this.value);
                });
            }

            function renderTimeAnalysisChart(ctx, type) {
                let chartData = {};
                let chartTitle = '';

                if (type === 'hour') {
                    chartData = {
                        '8-9h': 87,
                        '9-10h': 156,
                        '10-11h': 178,
                        '11-12h': 145,
                        '12-13h': 67,
                        '13-14h': 78,
                        '14-15h': 167,
                        '15-16h': 154,
                        '16-17h': 132,
                        '17-18h': 98,
                        '18-19h': 76,
                        '19-20h': 45
                    };
                    chartTitle = 'Distribuição por Hora do Dia';
                } else if (type === 'weekday') {
                    chartData = {
                        'Segunda': 145,
                        'Terça': 187,
                        'Quarta': 176,
                        'Quinta': 154,
                        'Sexta': 132,
                        'Sábado': 76,
                        'Domingo': 45
                    };
                    chartTitle = 'Distribuição por Dia da Semana';
                } else if (type === 'month') {
                    chartData = {
                        'Jan': 876,
                        'Fev': 923,
                        'Mar': 1045,
                        'Abr': 987,
                        'Mai': 1123,
                        'Jun': 1056,
                        'Jul': 1234,
                        'Ago': 1345
                    };
                    chartTitle = 'Distribuição por Mês';
                }

                // Destruir gráfico anterior se existir
                if (window.timeAnalysisChartInstance) {
                    window.timeAnalysisChartInstance.destroy();
                }

                // Criar novo gráfico
                window.timeAnalysisChartInstance = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: Object.keys(chartData),
                        datasets: [{
                            label: 'Transações',
                            data: Object.values(chartData),
                            backgroundColor: 'rgba(0, 122, 255, 0.7)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            title: {
                                display: true,
                                text: chartTitle,
                                font: {
                                    size: 14
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    display: true,
                                    color: 'rgba(0, 0, 0, 0.05)'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }
        }

        // Gráfico de Análise de Retenção
        if (document.getElementById('retentionAnalysisChart')) {
            const retentionAnalysisCtx = document.getElementById('retentionAnalysisChart').getContext('2d');

            // Dados simulados para análise de retenção
            const retentionData = {
                'Mês 1': 100,
                'Mês 2': 78,
                'Mês 3': 65,
                'Mês 4': 58,
                'Mês 5': 52,
                'Mês 6': 48,
                'Mês 7': 45,
                'Mês 8': 43
            };

            new Chart(retentionAnalysisCtx, {
                type: 'line',
                data: {
                    labels: Object.keys(retentionData),
                    datasets: [{
                        label: 'Taxa de Retenção (%)',
                        data: Object.values(retentionData),
                        backgroundColor: 'rgba(0, 122, 255, 0.1)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }
    }
</script>
{% endblock %}
