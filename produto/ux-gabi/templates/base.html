<!DOCTYPE html>
<html lang="pt-br">
<head>
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <!-- SECURITY: Enhanced CSP -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data: https:; font-src 'self' data: https://cdn.jsdelivr.net;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Product Analytics - Amigo One{% endblock %}</title>
    <!-- External resources -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                systemBlue: '#007AFF',
                systemGreen: '#34C759',
                systemGray: {
                  DEFAULT: '#8E8E93',
                  light: '#AEAEB2',
                  lighter: '#C7C7CC',
                  lightest: '#D1D1D6',
                  extralight: '#E5E5EA',
                  ultralight: '#F2F2F7'
                },
                label: {
                    DEFAULT: '#000000',
                    secondary: 'rgba(60, 60, 67, 0.6)',
                    tertiary: 'rgba(60, 60, 67, 0.3)',
                    quaternary: 'rgba(60, 60, 67, 0.18)'
                }
              },
              borderRadius: {
                  'view': '10px',
                  'control': '7px'
              }
            }
          }
        }
    </script>
    <style>
        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            background-color: #F2F2F7;
        }

        /* Wave animation for hand emoji */
        @keyframes wave {
            0% { transform: rotate(0deg); }
            10% { transform: rotate(14deg); }
            20% { transform: rotate(-8deg); }
            30% { transform: rotate(14deg); }
            40% { transform: rotate(-4deg); }
            50% { transform: rotate(10deg); }
            60% { transform: rotate(0deg); }
            100% { transform: rotate(0deg); }
        }

        /* Sidebar styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 16rem; /* 64px */
            z-index: 50;
            transition: transform 0.3s ease;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }
        }

        /* Main content styles */
        .main-content {
            margin-left: 0;
        }

        @media (min-width: 768px) {
            .main-content {
                margin-left: 16rem; /* 64px */
            }
        }
    </style>
    <script src="{{ url_for('static', filename='js/dashboard-utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dashboard-charts.js') }}"></script>
    <script>
        // Debug function for chart rendering
        window.debugChart = function(elementId, chartData, error) {
            if (typeof console !== 'undefined' && console.group) {
                console.group('Chart Debug: ' + elementId);
                console.log('Element exists:', !!document.getElementById(elementId));
                console.log('Chart data:', chartData);
                if (error) {
                    console.error('Error:', error);
                }
                console.groupEnd();
            }
        };
    </script>
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- Sidebar -->
    <div class="flex h-screen overflow-hidden">
        <div class="sidebar bg-white border-r border-gray-200">
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center flex-col">
                    <div class="flex items-center">
                        <div class="h-11 mr-2">
                            <img src="/static/amigo-logo.png" alt="Amigo One Logo" class="h-full">
                        </div>
                        <div class="h-5 w-0.5 bg-blue-500 mx-2"></div>
                        <h1 class="text-lg text-gray-900"><span class="font-semibold">Data</span>Hub</h1>
                    </div>
                    <span class="text-xs text-gray-500 mt-1">DataHub Amigo One - Produto</span>
                </div>
            </div>
            <div class="p-4 overflow-y-auto h-[calc(100%-4rem)]">
                <nav class="space-y-1">
                    {% set nav_items = [
                        {'url': '/', 'name': 'Dashboard', 'icon': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>'},
                        {'url': '/users', 'name': 'Usuários', 'icon': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>'},

                        {'url': '/medical_records', 'name': 'Prontuários', 'icon': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>'},
                        {'url': '/connections', 'name': 'Rede Amigo', 'icon': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>'},
                        {'url': '/payments', 'name': 'Amigo Pay', 'icon': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>'},
                        {'url': '/agenda', 'name': 'Agenda', 'icon': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>'},
                        {'url': '/accounting', 'name': 'Contabilidade', 'icon': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>'},

                        {'url': '/amigo_intelligence', 'name': 'Amigo Intelligence', 'icon': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>'},
                        {'url': '/documentacao', 'name': 'Documentação', 'icon': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>'}
                    ] %}

                    {% set admin_items = [
                        {'url': '/admin/users', 'name': 'Gerenciar Usuários', 'icon': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>'},
                        {'url': '/admin/monitoring', 'name': 'Monitoramento', 'icon': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>'}
                    ] %}

                    {% for item in nav_items %}
                        {% set is_active = request.path == item.url or (request.path == '/' and item.url == '/') %}
                        <a href="{{ item.url|e }}"
                           class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out
                                  {% if is_active %}
                                  text-systemBlue bg-blue-50 border-l-4 border-systemBlue
                                  {% else %}
                                  text-gray-600 hover:bg-systemGray-ultralight hover:text-gray-900
                                  {% endif %}">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 {% if is_active %}text-systemBlue{% else %}text-gray-500 group-hover:text-gray-700{% endif %} transition-colors duration-200"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    {{ item.icon|e }}
                                </svg>
                                <span class="ml-3">{{ item.name|e }}</span>
                            </div>
                            {% if is_active %}
                            <div class="ml-auto">
                                <div class="h-2 w-2 rounded-full bg-systemBlue"></div>
                            </div>
                            {% endif %}
                        </a>
                    {% endfor %}

                    <!-- Admin Section (only for admin users) -->
                    {% if session.username in ['admin', 'TTK', 'bruno@abreu', 'bruno@bruno'] %}
                        <div class="mt-6 pt-4 border-t border-gray-200">
                            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
                                Administração
                            </h3>
                            {% for item in admin_items %}
                                {% set is_active = request.path == item.url %}
                                <a href="{{ item.url|e }}"
                                   class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out
                                          {% if is_active %}
                                          text-red-600 bg-red-50 border-l-4 border-red-600
                                          {% else %}
                                          text-gray-600 hover:bg-red-50 hover:text-red-700
                                          {% endif %}">
                                    <div class="flex items-center">
                                        <svg class="h-5 w-5 {% if is_active %}text-red-600{% else %}text-gray-500 group-hover:text-red-600{% endif %} transition-colors duration-200"
                                             fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            {{ item.icon|e }}
                                        </svg>
                                        <span class="ml-3">{{ item.name|e }}</span>
                                    </div>
                                </a>
                            {% endfor %}
                        </div>
                    {% endif %}
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white border-b border-gray-200 fixed top-0 right-0 left-0 md:left-64 z-40">
                <div class="px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <button class="md:hidden mr-2 text-gray-500 hover:text-gray-700" id="sidebarToggle">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                        <h1 class="text-lg font-semibold">{% block header %}Dashboard{% endblock %}</h1>
                    </div>
                    <div class="flex items-center">
                        <div class="relative">
                            <div class="flex items-center text-sm font-medium text-gray-700">
                                <span class="mr-2">{{ session.get('full_name', 'Usuário')|e }}</span>
                                <div class="h-8 w-8 rounded-full bg-systemGray-ultralight flex items-center justify-center">
                                    <svg class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <button id="goto-business-btn" class="ml-4 px-3 py-1.5 bg-systemBlue text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors duration-200 flex items-center">
                            <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                            Ir para Negócios
                        </button>
                        <form method="POST" action="{{ url_for('auth_routes.logout') }}" class="ml-4">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <button type="submit" class="text-sm text-systemBlue hover:text-blue-700 flex items-center">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                Sair
                            </button>
                        </form>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto bg-systemGray-ultralight mt-16">
                <div class="w-full px-4 sm:px-6 lg:px-8 py-6">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>

        <script>
            // Inicializar o estado do sidebar
            document.addEventListener('DOMContentLoaded', function() {
                const sidebar = document.querySelector('.sidebar');
                if (sidebar) {
                    // Esconder o sidebar em dispositivos móveis por padrão
                    if (window.innerWidth < 768) {
                        sidebar.classList.add('hidden');
                    }
                }
            });

            // Toggle sidebar on mobile
            document.getElementById('sidebarToggle')?.addEventListener('click', function() {
                const sidebar = document.querySelector('.sidebar');
                if (sidebar) {
                    sidebar.classList.toggle('hidden');
                    sidebar.classList.toggle('active');
                }
            });

            // Ajustar sidebar ao redimensionar a janela
            window.addEventListener('resize', function() {
                const sidebar = document.querySelector('.sidebar');
                if (sidebar) {
                    if (window.innerWidth >= 768) {
                        sidebar.classList.remove('hidden');
                    } else if (!sidebar.classList.contains('active')) {
                        sidebar.classList.add('hidden');
                    }
                }
            });
        </script>
    </div>

    <!-- Modal de Redirecionamento para o App de Negócios -->
    <div id="redirect-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6 transform transition-all">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Redirecionamento</h3>
                <button id="close-modal" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="mb-5">
                <p class="text-gray-600 mb-3">Você está sendo redirecionado para o aplicativo de Negócios.</p>
                <div class="bg-blue-50 p-3 rounded-md">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 text-systemBlue mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p class="text-sm text-systemBlue">Você está saindo do aplicativo de <strong>Produto</strong> e indo para o aplicativo de <strong>Negócios</strong>.</p>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3">
                <button id="cancel-redirect" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    Cancelar
                </button>
                <a href="https://5mmmbhl1-5000.brs.devtunnels.ms/" class="px-4 py-2 bg-systemBlue text-white text-sm font-medium rounded-md hover:bg-blue-700">
                    Continuar
                </a>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/redirect.js') }}"></script>

    {% block scripts %}{% endblock %}

    <script>
        // Script para o modal de redirecionamento
        document.addEventListener('DOMContentLoaded', function() {
            const gotoBusinessBtn = document.getElementById('goto-business-btn');
            const redirectModal = document.getElementById('redirect-modal');
            const closeModal = document.getElementById('close-modal');
            const cancelRedirect = document.getElementById('cancel-redirect');
            const continueRedirect = document.querySelector('#redirect-modal a');

            // Atualizar o link de redirecionamento com a URL correta
            if (continueRedirect && window.AmigoApp && window.AmigoApp.APP_URLS) {
                continueRedirect.href = window.AmigoApp.APP_URLS.business;
            }

            gotoBusinessBtn.addEventListener('click', function() {
                redirectModal.classList.remove('hidden');
            });

            closeModal.addEventListener('click', function() {
                redirectModal.classList.add('hidden');
            });

            cancelRedirect.addEventListener('click', function() {
                redirectModal.classList.add('hidden');
            });

            // Fechar o modal ao clicar fora dele
            redirectModal.addEventListener('click', function(e) {
                if (e.target === redirectModal) {
                    redirectModal.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>
