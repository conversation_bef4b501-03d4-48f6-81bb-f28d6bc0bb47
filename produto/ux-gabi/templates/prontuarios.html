{% extends 'base.html' %}
{% from "macros/components.html" import kpi_card, chart_container, metric_grid, loading_state, error_state %}

{% block title %}Prontuários - Amigo One Analytics{% endblock %}

{% block header %}Análise de Prontuários{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">PRONTUÁRIOS</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Análise de Prontuários Médicos</h1>
            <p class="text-gray-600 mb-6">Monitore os tipos de registros médicos, uso de assinatura digital e telemedicina para otimizar a experiência clínica dos médicos.</p>
            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ data.features.medical_records.TELEMEDICINE + data.features.medical_records.MEDICINE + data.features.medical_records.CERTIFICATE + data.features.medical_records.EXAM_REQUEST + data.features.medical_records.TEXT + data.features.medical_records.EVOLUTION + data.features.medical_records.PHOTO + data.features.medical_records.CUSTOM|e }}</div>
                    <div class="text-xs text-gray-500">Registros totais</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ data.features.digital_signature.signed|e }}</div>
                    <div class="text-xs text-gray-500">Documentos assinados</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ data.features.medical_records.TELEMEDICINE|e }}</div>
                    <div class="text-xs text-gray-500">Telemedicinas</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Gerar Relatório
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Filtrar Dados
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">{{ (data.features.digital_signature.signed / (data.features.digital_signature.signed + data.features.digital_signature.unsigned) * 100) | round | int }}%</div>
                    <div class="text-sm text-gray-500">Assinados</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- KPI Cards - Principais Métricas -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Receitas Digitais -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Receitas Digitais</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.features.medical_records.MEDICINE|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Total de receitas emitidas</span>
            <span class="ml-auto text-systemBlue font-medium">+12%</span>
        </div>
    </div>

    <!-- Solicitações de Exames -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Solicitações de Exames</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.features.medical_records.EXAM_REQUEST|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Total de exames solicitados</span>
            <span class="ml-auto text-systemBlue font-medium">+8.5%</span>
        </div>
    </div>

    <!-- Atestados -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Atestados</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.features.medical_records.CERTIFICATE|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Total de atestados emitidos</span>
            <span class="ml-auto text-systemBlue font-medium">+15.2%</span>
        </div>
    </div>

    <!-- Evoluções -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Evoluções</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.features.medical_records.EVOLUTION|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Total de evoluções registradas</span>
            <span class="ml-auto text-systemBlue font-medium">****%</span>
        </div>
    </div>
</div>

<!-- KPI Cards - Métricas Adicionais -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Telemedicinas -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Telemedicinas</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.features.medical_records.TELEMEDICINE|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Consultas por telemedicina</span>
            <span class="ml-auto text-systemBlue font-medium">+25%</span>
        </div>
    </div>

    <!-- Duração Média -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Duração Média</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">18<span class="text-sm text-gray-500">min</span></p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Tempo médio de telemedicina</span>
            <span class="ml-auto text-systemBlue font-medium">-2.5min</span>
        </div>
    </div>

    <!-- Pacientes por Médico -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Pacientes por Médico</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.patients.avg_patients_per_doctor|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Média de pacientes</span>
            <span class="ml-auto text-systemBlue font-medium">+4.2%</span>
        </div>
    </div>

    <!-- Medicamentos Manuais -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Medicamentos Manuais</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.features.manual_medications|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Adicionados manualmente</span>
            <span class="ml-auto text-systemBlue font-medium">+3.7%</span>
        </div>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Tipos de Registros Médicos -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Tipos de Registros Médicos</h2>
            <div class="flex items-center">
                <select id="recordTypePeriod" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="month">Este Mês</option>
                    <option value="quarter">Este Trimestre</option>
                    <option value="year">Este Ano</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="medicalRecordsChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Receitas digitais (MEDICINE) e solicitações de exames (EXAM_REQUEST) são os tipos de registros mais utilizados, representando 65% do total. A proporção de telemedicina aumentou 8.3% no último trimestre.</p>
        </div>
    </div>

    <!-- Uso de Funcionalidades por Mês -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Uso de Funcionalidades por Mês</h2>
            <div class="flex items-center">
                <div class="flex space-x-2">
                    <span class="inline-block w-3 h-3 bg-systemBlue rounded-full"></span>
                    <span class="text-xs text-gray-600 mr-3">Receitas</span>
                    <span class="inline-block w-3 h-3 bg-blue-300 rounded-full"></span>
                    <span class="text-xs text-gray-600">Exames</span>
                </div>
            </div>
        </div>
        <div class="h-64">
            <canvas id="featureUsageChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Todas as funcionalidades apresentam crescimento consistente, com destaque para receitas digitais (+18% no último trimestre). Picos de uso ocorrem no meio da semana (terça a quinta).</p>
        </div>
    </div>
</div>

<!-- Assinatura Digital e Telemedicina -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Assinatura Digital -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Assinatura Digital</h2>
            <div class="flex items-center">
                <span class="text-xs text-systemBlue font-medium">Total: {{ (data.features.digital_signature.signed + data.features.digital_signature.unsigned)|e }}</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="digitalSignatureChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> {{ (data.features.digital_signature.signed / (data.features.digital_signature.signed + data.features.digital_signature.unsigned) * 100) | round | int }}% dos documentos são assinados digitalmente, indicando alta adoção desta funcionalidade. Médicos com mais de 45 anos têm taxa de adoção 15% menor.</p>
        </div>
    </div>

    <!-- Telemedicina -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Telemedicina</h2>
            <div class="flex items-center">
                <button class="text-xs text-systemBlue hover:underline">Ver detalhes</button>
            </div>
        </div>
        <div class="h-64">
            <canvas id="telemedicineChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> O uso de telemedicina cresceu 25% nos últimos 6 meses, com duração média de 18 minutos por consulta. Especialidades com maior adoção: Psiquiatria (42%), Dermatologia (38%) e Clínica Geral (27%).</p>
        </div>
    </div>
</div>

<!-- Análises Detalhadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Distribuição por Especialidade -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Distribuição por Especialidade</h2>
            <div class="flex items-center">
                <select id="specialtyFilter" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="all">Todas Especialidades</option>
                    <option value="top5">Top 5 Especialidades</option>
                    <option value="growth">Maior Crescimento</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="specialtyDistributionChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Clínica Geral, Pediatria e Ginecologia são responsáveis por 65% dos registros em prontuários. Neurologia apresentou o maior crescimento (+32%) no último trimestre.</p>
        </div>
    </div>

    <!-- Eficiência de Documentação -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Eficiência de Documentação</h2>
            <div class="flex items-center">
                <span class="text-xs text-blue-500 cursor-pointer hover:underline">Ver métricas completas</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="documentationEfficiencyChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> O tempo médio para completar a documentação diminuiu 23% nos últimos 6 meses. Médicos que utilizam modelos pré-definidos são 35% mais rápidos na documentação.</p>
        </div>
    </div>
</div>

<!-- Nota Informativa -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-gray-800">Sobre os Tipos de Registros Médicos</h3>
            <div class="mt-2 text-xs text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
                <ul class="space-y-1">
                    <li><span class="font-medium">MEDICINE:</span> Receitas médicas digitais</li>
                    <li><span class="font-medium">EXAM_REQUEST:</span> Solicitações de exames</li>
                    <li><span class="font-medium">CERTIFICATE:</span> Atestados médicos</li>
                    <li><span class="font-medium">EVOLUTION:</span> Registros de evolução do paciente</li>
                </ul>
                <ul class="space-y-1">
                    <li><span class="font-medium">TELEMEDICINE:</span> Consultas por telemedicina</li>
                    <li><span class="font-medium">TEXT:</span> Anotações em texto livre</li>
                    <li><span class="font-medium">PHOTO:</span> Imagens e fotografias</li>
                    <li><span class="font-medium">CUSTOM:</span> Modelos personalizados</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // HYBRID STRUCTURED: Static data from server (SSR)
        const staticData = {{ data | tojson |e }};

        // HYBRID STRUCTURED: Initialize with static data, then enhance with dynamic
        initializeProntuariosCharts(staticData);

        // HYBRID STRUCTURED: Setup dynamic data loading for real-time updates
        setupDynamicUpdates();
    });

    // HYBRID STRUCTURED: Dynamic data loading function
    function setupDynamicUpdates() {
        // Update medical records data every 30 seconds
        setInterval(async function() {
            try {
                const response = await fetch('/api/product/medical/records/realtime');
                if (response.ok) {
                    const dynamicData = await response.json();
                    updateChartsWithDynamicData(dynamicData);
                }
            } catch (error) {
                console.warn('Dynamic update failed:', error);
            }
        }, 30000);
    }

    function initializeProntuariosCharts(data) {
        // Gráfico de Tipos de Registros Médicos
        if (document.getElementById('medicalRecordsChart')) {
            const medicalRecordsCtx = document.getElementById('medicalRecordsChart').getContext('2d');
            const medicalRecordsChart = new Chart(medicalRecordsCtx, {
                type: 'bar',
                data: {
                    labels: Object.keys(data.features.medical_records),
                    datasets: [{
                        label: 'Quantidade',
                        data: Object.values(data.features.medical_records),
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Adicionar event listener para o filtro de período
            if (document.getElementById('recordTypePeriod')) {
                document.getElementById('recordTypePeriod').addEventListener('change', function() {
                    // Simulação de dados diferentes para cada período
                    let multiplier = 1;
                    if (this.value === 'quarter') {
                        multiplier = 3;
                    } else if (this.value === 'year') {
                        multiplier = 12;
                    }

                    const newData = Object.values(data.features.medical_records).map(value => value * multiplier);
                    medicalRecordsChart.data.datasets[0].data = newData;
                    medicalRecordsChart.update();
                });
            }
        }

        // Gráfico de Uso de Funcionalidades por Mês
        if (document.getElementById('featureUsageChart')) {
            const featureUsageCtx = document.getElementById('featureUsageChart').getContext('2d');

            // Preparar dados para o gráfico
            const months = Object.keys(data.features.feature_usage_by_month);
            // Limitamos a apenas dois tipos para simplificar a visualização
            const selectedFeatureTypes = ['MEDICINE', 'EXAM_REQUEST'];

            // Criar datasets para cada tipo de funcionalidade
            const datasets = selectedFeatureTypes.map((type, index) => {
                const colors = [
                    'rgba(0, 122, 255, 1)',
                    'rgba(0, 122, 255, 0.6)'
                ];

                return {
                    label: type,
                    data: months.map(month => data.features.feature_usage_by_month[month][type]),
                    borderColor: colors[index],
                    backgroundColor: colors[index].replace('1)', '0.1)').replace('0.6)', '0.05)'),
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                };
            });

            new Chart(featureUsageCtx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Assinatura Digital
        if (document.getElementById('digitalSignatureChart')) {
            const digitalSignatureCtx = document.getElementById('digitalSignatureChart').getContext('2d');
            new Chart(digitalSignatureCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Assinados', 'Não Assinados'],
                    datasets: [{
                        data: [data.features.digital_signature.signed, data.features.digital_signature.unsigned],
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(156, 163, 175, 0.5)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(156, 163, 175, 0.8)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });
        }

        // Gráfico de Telemedicina
        if (document.getElementById('telemedicineChart')) {
            const telemedicineCtx = document.getElementById('telemedicineChart').getContext('2d');

            // Dados simulados para telemedicina por mês
            const telemedicineData = {
                'Jan/2025': 132,
                'Fev/2025': 145,
                'Mar/2025': 156,
                'Abr/2025': 167,
                'Mai/2025': 178,
                'Jun/2025': 187,
                'Jul/2025': 198,
                'Ago/2025': 212
            };

            new Chart(telemedicineCtx, {
                type: 'line',
                data: {
                    labels: Object.keys(telemedicineData),
                    datasets: [{
                        label: 'Telemedicinas',
                        data: Object.values(telemedicineData),
                        backgroundColor: 'rgba(0, 122, 255, 0.1)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Distribuição por Especialidade
        if (document.getElementById('specialtyDistributionChart')) {
            const specialtyDistributionCtx = document.getElementById('specialtyDistributionChart').getContext('2d');

            // Dados simulados para distribuição por especialidade
            const specialtyData = {
                'Clínica Geral': 32,
                'Pediatria': 18,
                'Ginecologia': 15,
                'Dermatologia': 12,
                'Ortopedia': 8,
                'Neurologia': 7,
                'Cardiologia': 5,
                'Outras': 3
            };

            const specialtyChart = new Chart(specialtyDistributionCtx, {
                type: 'pie',
                data: {
                    labels: Object.keys(specialtyData),
                    datasets: [{
                        data: Object.values(specialtyData),
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.9)',
                            'rgba(0, 122, 255, 0.8)',
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(0, 122, 255, 0.6)',
                            'rgba(0, 122, 255, 0.5)',
                            'rgba(0, 122, 255, 0.4)',
                            'rgba(0, 122, 255, 0.3)',
                            'rgba(0, 122, 255, 0.2)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${percentage}%`;
                                }
                            }
                        }
                    }
                }
            });

            // Adicionar event listener para o filtro de especialidade
            if (document.getElementById('specialtyFilter')) {
                document.getElementById('specialtyFilter').addEventListener('change', function() {
                    let filteredData = {};

                    if (this.value === 'top5') {
                        // Filtrar apenas as top 5 especialidades
                        const entries = Object.entries(specialtyData).sort((a, b) => b[1] - a[1]).slice(0, 5);
                        entries.forEach(([key, value]) => {
                            filteredData[key] = value;
                        });
                    } else if (this.value === 'growth') {
                        // Dados simulados para especialidades com maior crescimento
                        filteredData = {
                            'Neurologia': 32,
                            'Dermatologia': 28,
                            'Psiquiatria': 25,
                            'Endocrinologia': 22,
                            'Oftalmologia': 18
                        };
                    } else {
                        // Todas as especialidades
                        filteredData = specialtyData;
                    }

                    specialtyChart.data.labels = Object.keys(filteredData);
                    specialtyChart.data.datasets[0].data = Object.values(filteredData);
                    specialtyChart.update();
                });
            }
        }

        // Gráfico de Eficiência de Documentação
        if (document.getElementById('documentationEfficiencyChart')) {
            const documentationEfficiencyCtx = document.getElementById('documentationEfficiencyChart').getContext('2d');

            // Dados simulados para eficiência de documentação
            const efficiencyData = {
                'Jan/2025': 8.5,
                'Fev/2025': 8.2,
                'Mar/2025': 7.8,
                'Abr/2025': 7.5,
                'Mai/2025': 7.1,
                'Jun/2025': 6.8,
                'Jul/2025': 6.5,
                'Ago/2025': 6.2
            };

            new Chart(documentationEfficiencyCtx, {
                type: 'line',
                data: {
                    labels: Object.keys(efficiencyData),
                    datasets: [{
                        label: 'Tempo Médio (minutos)',
                        data: Object.values(efficiencyData),
                        backgroundColor: 'transparent',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        pointBackgroundColor: 'rgba(0, 122, 255, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            title: {
                                display: true,
                                text: 'Minutos',
                                font: {
                                    size: 10
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }
    }
</script>
{% endblock %}
