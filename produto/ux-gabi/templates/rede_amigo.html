{% extends 'base.html' %}

{% block title %}Rede Amigo - Amigo One Analytics{% endblock %}

{% block header %}Análise da Rede Amigo{% endblock %}

{% block styles %}
<style>
    /* Estilos específicos para visualizações de rede */
    .node {
        stroke: #fff;
        stroke-width: 1.5px;
    }

    .link {
        stroke: rgba(0, 122, 255, 0.2);
        stroke-width: 1px;
    }

    /* Estilos para o modal de Interações 360 */
    #interacoes360Modal {
        backdrop-filter: blur(5px);
    }

    /* Estilos para os containers de visualização */
    .visualization-container {
        min-height: 300px;
        background-color: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 0.375rem;
        padding: 0.5rem;
    }
</style>
{% endblock %}

<!-- Bibliotecas adicionais para visualizações de rede -->
<!-- Estas bibliotecas serão incluídas no bloco scripts no final do arquivo -->

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">REDE SOCIAL</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Análise da Rede Amigo</h1>
            <p class="text-gray-600 mb-6">Monitore as conexões entre profissionais, interações com o feed e crescimento da rede social para médicos.</p>
            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ data.connections.accepted_connections|e }}</div>
                    <div class="text-xs text-gray-500">Conexões aceitas</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ data.connections.pending_invites|e }}</div>
                    <div class="text-xs text-gray-500">Convites pendentes</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ (data.connections.feed_interactions.likes + data.connections.feed_interactions.comments + data.connections.feed_interactions.shares)|e }}</div>
                    <div class="text-xs text-gray-500">Interações totais</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button id="interacoes360Btn" class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Interações 360
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Filtrar Dados
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">{{ (data.connections.accepted_connections / (data.connections.accepted_connections + data.connections.pending_invites) * 100) | round | int }}%</div>
                    <div class="text-sm text-gray-500">Taxa de aceitação</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Visualização da Rede Principal -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <div>
            <h2 class="text-xl font-semibold text-gray-800">Mapa da Rede de Conexões</h2>
            <p class="text-sm text-gray-600 mt-1">Visualização interativa das conexões entre profissionais por especialidade</p>
        </div>
        <div class="flex items-center space-x-3">
            <select id="mainNetworkViewType" class="text-sm border border-gray-200 rounded-md px-3 py-2 bg-white">
                <option value="force">Diagrama de Força</option>
                <option value="radial">Visualização Radial</option>
                <option value="hierarchical">Visualização Hierárquica</option>
            </select>
            <button id="fullscreenNetworkBtn" class="bg-systemBlue hover:bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                </svg>
                Análise Avançada
            </button>
        </div>
    </div>

    <!-- Container da Visualização Principal -->
    <div class="h-[500px] bg-gray-50 rounded-lg border border-gray-200 relative overflow-hidden">
        <div id="mainNetworkVisualization" class="w-full h-full">
            <!-- Loading state -->
            <div class="flex items-center justify-center h-full">
                <div class="text-center">
                    <svg class="w-12 h-12 mx-auto text-gray-400 animate-spin mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    <p class="text-gray-500 text-sm">Carregando visualização da rede...</p>
                </div>
            </div>
        </div>

        <!-- Legenda da Visualização -->
        <div class="absolute top-4 left-4 bg-white bg-opacity-95 rounded-lg p-3 shadow-lg border border-gray-200">
            <h4 class="text-xs font-semibold text-gray-700 mb-2">Especialidades</h4>
            <div class="space-y-1 text-xs">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                    <span>Cardiologia</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                    <span>Neurologia</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span>Dermatologia</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                    <span>Outras</span>
                </div>
            </div>
        </div>

        <!-- Métricas da Rede -->
        <div class="absolute top-4 right-4 bg-white bg-opacity-95 rounded-lg p-3 shadow-lg border border-gray-200">
            <h4 class="text-xs font-semibold text-gray-700 mb-2">Métricas da Rede</h4>
            <div class="space-y-1 text-xs">
                <div class="flex justify-between">
                    <span class="text-gray-600">Profissionais:</span>
                    <span class="font-medium" id="networkNodeCount">100</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Conexões:</span>
                    <span class="font-medium" id="networkLinkCount">250</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Densidade:</span>
                    <span class="font-medium" id="networkDensity">0.05</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Especialidades:</span>
                    <span class="font-medium" id="networkSpecialties">8</span>
                </div>
            </div>
        </div>

        <!-- Controles de Navegação -->
        <div class="absolute bottom-4 right-4 bg-white bg-opacity-95 rounded-lg p-2 shadow-lg border border-gray-200">
            <div class="flex space-x-2">
                <button id="zoomInBtn" class="p-1 text-gray-600 hover:text-gray-800" title="Zoom In">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </button>
                <button id="zoomOutBtn" class="p-1 text-gray-600 hover:text-gray-800" title="Zoom Out">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6"></path>
                    </svg>
                </button>
                <button id="resetViewBtn" class="p-1 text-gray-600 hover:text-gray-800" title="Reset View">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Insights da Rede -->
    <div class="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-100">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-800">Insights da Rede</h3>
                <p class="mt-1 text-xs text-gray-600">
                    <strong>Conectividade:</strong> A rede apresenta alta conectividade entre especialidades complementares.
                    <strong>Hubs Principais:</strong> Cardiologistas e Neurologistas atuam como conectores principais entre diferentes especialidades.
                    <strong>Oportunidade:</strong> Dermatologia e Psiquiatria mostram potencial para aumentar conexões interdisciplinares em 28%.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- KPI Cards - Principais Métricas -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Conexões Aceitas -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Conexões Aceitas</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.connections.accepted_connections|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Total de conexões realizadas</span>
            <span class="ml-auto text-systemBlue font-medium">+12%</span>
        </div>
    </div>

    <!-- Curtidas -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Curtidas</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.connections.feed_interactions.likes|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Total de curtidas no feed</span>
            <span class="ml-auto text-systemBlue font-medium">+18.5%</span>
        </div>
    </div>

    <!-- Comentários -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Comentários</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.connections.feed_interactions.comments|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Total de comentários no feed</span>
            <span class="ml-auto text-systemBlue font-medium">+15.2%</span>
        </div>
    </div>

    <!-- Compartilhamentos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Compartilhamentos</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.connections.feed_interactions.shares|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Total de compartilhamentos</span>
            <span class="ml-auto text-systemBlue font-medium">+9.7%</span>
        </div>
    </div>
</div>

<!-- KPI Cards - Métricas Adicionais -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Taxa de Aceitação -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Taxa de Aceitação</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ (data.connections.accepted_connections / (data.connections.accepted_connections + data.connections.pending_invites) * 100) | round | int }}<span class="text-sm text-gray-500">%</span></p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Convites aceitos vs. enviados</span>
            <span class="ml-auto text-systemBlue font-medium">+5.2%</span>
        </div>
    </div>

    <!-- Engajamento Médio -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Engajamento Médio</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">23<span class="text-sm text-gray-500">%</span></p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Interações por visualização</span>
            <span class="ml-auto text-systemBlue font-medium">+3.7%</span>
        </div>
    </div>

    <!-- Conexões por Usuário -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Conexões por Usuário</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">18.4</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Média de conexões</span>
            <span class="ml-auto text-systemBlue font-medium">+2.3</span>
        </div>
    </div>

    <!-- Tempo no Feed -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-gray-800">Tempo no Feed</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">4:12<span class="text-sm text-gray-500">min</span></p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Tempo médio de navegação</span>
            <span class="ml-auto text-systemBlue font-medium">+0:32</span>
        </div>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Crescimento da Rede -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Crescimento da Rede</h2>
            <div class="flex items-center">
                <select id="networkGrowthPeriod" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="month">Últimos 8 Meses</option>
                    <option value="quarter">Últimos 4 Trimestres</option>
                    <option value="year">Últimos 2 Anos</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="networkGrowthChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> A rede de conexões cresceu 22% nos últimos 8 meses, com aceleração nos últimos 3 meses. A taxa de crescimento mensal média é de 2.8%, indicando tendência de expansão contínua.</p>
        </div>
    </div>

    <!-- Distribuição de Interações -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Distribuição de Interações</h2>
            <div class="flex items-center">
                <span class="text-xs text-systemBlue font-medium">Total: {{ (data.connections.feed_interactions.likes + data.connections.feed_interactions.comments + data.connections.feed_interactions.shares)|e }}</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="interactionsDistributionChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> As curtidas representam 65% das interações, seguidas por comentários (25%) e compartilhamentos (10%). Publicações com imagens têm taxa de engajamento 2.3x maior que publicações apenas com texto.</p>
        </div>
    </div>
</div>

<!-- Mapa de Conexões e Tópicos Populares -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Mapa de Conexões -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Mapa de Conexões por Especialidade</h2>
            <div class="flex items-center">
                <select id="connectionsMapFilter" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="all">Todas Especialidades</option>
                    <option value="top5">Top 5 Especialidades</option>
                    <option value="growth">Maior Crescimento</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="connectionsMapChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Cardiologistas e Neurologistas têm a maior taxa de conexões interdisciplinares (42% e 38% respectivamente). Especialidades com maior crescimento de conexões: Dermatologia (+28%) e Psiquiatria (+24%).</p>
        </div>
    </div>

    <!-- Tópicos Populares no Feed -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Tópicos Populares no Feed</h2>
            <div class="flex items-center">
                <button class="text-xs text-systemBlue hover:underline">Ver detalhes</button>
            </div>
        </div>
        <div class="h-64">
            <canvas id="popularFeedTopicsChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Publicações sobre casos clínicos e atualizações científicas recebem mais engajamento (3.2x e 2.8x acima da média, respectivamente). Conteúdos sobre gestão de consultório têm crescido 34% em popularidade nos últimos 3 meses.</p>
        </div>
    </div>
</div>

<!-- Análise de Comportamento -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Horários de Maior Atividade -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Horários de Maior Atividade</h2>
            <div class="flex items-center">
                <select id="activityTimeFilter" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                    <option value="hour">Por Hora</option>
                    <option value="weekday">Por Dia da Semana</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="activityTimeChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Os horários de pico são entre 12-14h (28%) e 20-22h (32%). Terça e quinta-feira são os dias com maior atividade, enquanto domingo tem apenas 15% da atividade média semanal.</p>
        </div>
    </div>

    <!-- Retenção de Usuários -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Retenção de Usuários</h2>
            <div class="flex items-center">
                <span class="text-xs text-blue-500 cursor-pointer hover:underline">Ver métricas completas</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="userRetentionChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> 82% dos usuários retornam ao feed pelo menos uma vez por semana. Usuários que fazem mais de 5 conexões nos primeiros 7 dias têm taxa de retenção 3.5x maior após 30 dias.</p>
        </div>
    </div>
</div>

<!-- Análise de Engajamento -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Análise de Engajamento</h2>
        <div class="flex items-center">
            <button class="text-xs text-systemBlue hover:underline flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Análise Detalhada
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-blue-50 p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Taxa de Visualização</h3>
                    <p class="text-xs text-label-secondary">Média por publicação</p>
                </div>
            </div>
            <div class="text-center py-4">
                <div class="text-3xl font-bold text-systemBlue">87%</div>
                <p class="text-xs text-gray-500 mt-1">Dos usuários conectados</p>
            </div>
            <div class="flex items-center justify-between text-xs mt-2 pt-2 border-t border-blue-100">
                <span class="text-gray-500">Variação mensal</span>
                <span class="font-medium text-systemBlue">+3.2%</span>
            </div>
        </div>

        <div class="bg-blue-50 p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Taxa de Engajamento</h3>
                    <p class="text-xs text-label-secondary">Interações por visualização</p>
                </div>
            </div>
            <div class="text-center py-4">
                <div class="text-3xl font-bold text-systemBlue">23%</div>
                <p class="text-xs text-gray-500 mt-1">Média de engajamento</p>
            </div>
            <div class="flex items-center justify-between text-xs mt-2 pt-2 border-t border-blue-100">
                <span class="text-gray-500">Variação mensal</span>
                <span class="font-medium text-systemBlue">+1.8%</span>
            </div>
        </div>

        <div class="bg-blue-50 p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Tempo no Feed</h3>
                    <p class="text-xs text-label-secondary">Tempo médio de navegação</p>
                </div>
            </div>
            <div class="text-center py-4">
                <div class="text-3xl font-bold text-systemBlue">4:12</div>
                <p class="text-xs text-gray-500 mt-1">Minutos:segundos</p>
            </div>
            <div class="flex items-center justify-between text-xs mt-2 pt-2 border-t border-blue-100">
                <span class="text-gray-500">Variação mensal</span>
                <span class="font-medium text-systemBlue">+0:32</span>
            </div>
        </div>

        <div class="bg-blue-50 p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Frequência de Visitas</h3>
                    <p class="text-xs text-label-secondary">Visitas por semana</p>
                </div>
            </div>
            <div class="text-center py-4">
                <div class="text-3xl font-bold text-systemBlue">3.8</div>
                <p class="text-xs text-gray-500 mt-1">Média por usuário</p>
            </div>
            <div class="flex items-center justify-between text-xs mt-2 pt-2 border-t border-blue-100">
                <span class="text-gray-500">Variação mensal</span>
                <span class="font-medium text-systemBlue">+0.5</span>
            </div>
        </div>
    </div>

    <div class="mt-6 p-4 bg-blue-50 rounded-md border border-blue-100">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-800">Oportunidades de Melhoria</h3>
                <p class="mt-1 text-xs text-gray-600">A análise de comportamento mostra que usuários que recebem notificações personalizadas têm 2.7x mais engajamento. Implementar sugestões de conexões baseadas em especialidade e interesses pode aumentar a taxa de aceitação em até 35%. Conteúdo educacional e casos clínicos são os tipos de publicação com maior potencial de crescimento.</p>
            </div>
        </div>
    </div>
</div>
<!-- Modal Interações 360 -->
<div id="interacoes360Modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center overflow-auto">
    <div class="bg-white rounded-lg w-11/12 h-5/6 max-w-7xl max-h-screen overflow-hidden flex flex-col">
        <!-- Modal Header -->
        <div class="bg-systemBlue text-white px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h2 class="text-xl font-bold">Interações 360 - Análise Avançada de Rede</h2>
            </div>
            <button id="closeInteracoes360Modal" class="text-white hover:text-gray-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="flex-1 overflow-auto p-6">
            <!-- Tabs -->
            <div class="border-b border-gray-200 mb-6">
                <nav class="-mb-px flex space-x-8">
                    <button class="interacoes-tab whitespace-nowrap py-4 px-1 border-b-2 border-systemBlue font-medium text-sm text-systemBlue" data-tab="network-analysis">
                        Análise de Rede
                    </button>
                    <button class="interacoes-tab whitespace-nowrap py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="engagement-patterns">
                        Padrões de Engajamento
                    </button>
                    <button class="interacoes-tab whitespace-nowrap py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="influence-analysis">
                        Análise de Influência
                    </button>
                    <button class="interacoes-tab whitespace-nowrap py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="community-detection">
                        Detecção de Comunidades
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div id="network-analysis" class="interacoes-content">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <!-- Visualização da Rede -->
                    <div class="lg:col-span-2 bg-white rounded-lg border border-gray-200 p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">Visualização da Rede</h3>
                            <div class="flex items-center space-x-2">
                                <select id="networkViewType" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="force">Diagrama de Força (ForceGraph)</option>
                                    <option value="d3-force">Diagrama de Força (D3.js Nativo)</option>
                                    <option value="radial">Diagrama Radial</option>
                                    <option value="hierarchical">Hierárquico</option>
                                </select>
                                <button class="text-xs text-systemBlue hover:underline flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Ajuda
                                </button>
                            </div>
                        </div>
                        <div class="h-[500px] bg-gray-50 rounded-md border border-gray-200 flex items-center justify-center">
                            <div id="networkVisualization" class="w-full h-full">
                                <!-- Visualização da rede será renderizada aqui via D3.js -->
                                <div class="flex items-center justify-center h-full">
                                    <div class="text-center">
                                        <svg class="w-12 h-12 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        <p class="mt-4 text-gray-600">Carregando visualização da rede...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-gray-600 bg-blue-50 p-3 rounded-md border border-blue-100">
                            <div class="flex items-start">
                                <svg class="w-4 h-4 text-systemBlue mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <p class="font-medium mb-1">Como interpretar:</p>
                                    <ul class="list-disc list-inside pl-1 space-y-1">
                                        <li>Cada nó representa um médico na rede</li>
                                        <li>O tamanho do nó indica o número de conexões</li>
                                        <li>As cores representam diferentes especialidades</li>
                                        <li>A espessura das linhas indica a força da conexão</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Métricas de Rede -->
                    <div class="bg-white rounded-lg border border-gray-200 p-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Métricas de Rede</h3>

                        <div class="space-y-4">
                            <!-- Densidade da Rede -->
                            <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700">Densidade da Rede</span>
                                    <span class="text-sm font-bold text-systemBlue">0.42</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-systemBlue h-2 rounded-full" style="width: 42%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Percentual de conexões existentes em relação ao total possível</p>
                            </div>

                            <!-- Centralidade Média -->
                            <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700">Centralidade Média</span>
                                    <span class="text-sm font-bold text-systemBlue">0.68</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-systemBlue h-2 rounded-full" style="width: 68%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Média de centralidade dos nós na rede</p>
                            </div>

                            <!-- Coeficiente de Agrupamento -->
                            <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700">Coeficiente de Agrupamento</span>
                                    <span class="text-sm font-bold text-systemBlue">0.75</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-systemBlue h-2 rounded-full" style="width: 75%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Tendência dos nós formarem grupos coesos</p>
                            </div>

                            <!-- Distância Média -->
                            <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700">Distância Média</span>
                                    <span class="text-sm font-bold text-systemBlue">2.3</span>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Número médio de conexões entre quaisquer dois nós</p>
                            </div>
                        </div>

                        <div class="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100">
                            <h4 class="text-sm font-medium text-gray-800 mb-2">Insights da Rede</h4>
                            <ul class="text-xs text-gray-600 space-y-2">
                                <li class="flex items-start">
                                    <svg class="w-4 h-4 text-systemBlue mt-0.5 mr-1.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>A rede apresenta alta coesão entre especialidades similares</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-4 h-4 text-systemBlue mt-0.5 mr-1.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>Existem 5 "pontes" principais conectando diferentes comunidades</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-4 h-4 text-systemBlue mt-0.5 mr-1.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>A rede mostra características de "mundo pequeno" com alta eficiência</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Análise de Conexões -->
                <div class="bg-white rounded-lg border border-gray-200 p-4 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Análise de Conexões por Especialidade</h3>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Matriz de Conexões -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-700">Matriz de Conexões</h4>
                                <select id="connectionMatrixFilter" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="all">Todas Especialidades</option>
                                    <option value="top10">Top 10 Especialidades</option>
                                </select>
                            </div>
                            <div class="h-[300px] bg-gray-50 rounded-md border border-gray-200 p-2">
                                <!-- Matriz será renderizada aqui via D3.js -->
                                <div class="flex items-center justify-center h-full">
                                    <div class="text-center">
                                        <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        <p class="mt-2 text-xs text-gray-600">Carregando matriz de conexões...</p>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Intensidade de conexões entre diferentes especialidades</p>
                        </div>

                        <!-- Distribuição de Conexões -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-700">Distribuição de Conexões</h4>
                                <select id="connectionDistributionView" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="specialty">Por Especialidade</option>
                                    <option value="region">Por Região</option>
                                    <option value="age">Por Faixa Etária</option>
                                </select>
                            </div>
                            <div class="h-[300px] bg-gray-50 rounded-md border border-gray-200 p-2">
                                <!-- Gráfico será renderizado aqui via ApexCharts -->
                                <div class="flex items-center justify-center h-full">
                                    <div class="text-center">
                                        <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        <p class="mt-2 text-xs text-gray-600">Carregando distribuição de conexões...</p>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Distribuição do número de conexões por categoria</p>
                        </div>
                    </div>

                    <div class="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100">
                        <h4 class="text-sm font-medium text-gray-800 mb-2">Insights de Conexões</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h5 class="text-xs font-medium text-gray-700 mb-1">Conexões Interdisciplinares</h5>
                                <ul class="text-xs text-gray-600 space-y-1 list-disc list-inside">
                                    <li>Cardiologistas e Neurologistas têm a maior taxa de conexões interdisciplinares (42% e 38%)</li>
                                    <li>Especialidades cirúrgicas tendem a formar grupos mais fechados</li>
                                    <li>Médicos de família têm o maior número médio de conexões (24.3)</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="text-xs font-medium text-gray-700 mb-1">Tendências Emergentes</h5>
                                <ul class="text-xs text-gray-600 space-y-1 list-disc list-inside">
                                    <li>Crescimento de 28% nas conexões entre Dermatologia e Endocrinologia</li>
                                    <li>Aumento de 35% nas conexões entre médicos de diferentes regiões</li>
                                    <li>Médicos recém-formados têm taxa de conexão 1.7x maior que a média</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="engagement-patterns" class="interacoes-content hidden">
                <!-- Visão Geral de Engajamento -->
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
                    <!-- Métricas de Engajamento -->
                    <div class="lg:col-span-3 bg-white rounded-lg border border-gray-200 p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">Métricas de Engajamento</h3>
                            <div class="flex items-center space-x-2">
                                <select id="engagementPeriod" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="week">Última Semana</option>
                                    <option value="month" selected>Último Mês</option>
                                    <option value="quarter">Último Trimestre</option>
                                    <option value="year">Último Ano</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <!-- Taxa de Engajamento -->
                            <div class="bg-blue-50 p-4 rounded-lg border border-blue-100">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="text-sm font-medium text-gray-800">Taxa de Engajamento</h4>
                                    <span class="text-xs px-2 py-0.5 bg-green-100 text-green-800 rounded-full">+3.2%</span>
                                </div>
                                <p class="text-3xl font-bold text-systemBlue">23%</p>
                                <p class="text-xs text-gray-600 mt-1">Interações por visualização</p>
                                <div class="mt-3 h-1 w-full bg-blue-200 rounded-full overflow-hidden">
                                    <div class="bg-systemBlue h-1" style="width: 23%"></div>
                                </div>
                            </div>

                            <!-- Tempo Médio no Feed -->
                            <div class="bg-blue-50 p-4 rounded-lg border border-blue-100">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="text-sm font-medium text-gray-800">Tempo Médio no Feed</h4>
                                    <span class="text-xs px-2 py-0.5 bg-green-100 text-green-800 rounded-full">+0:32</span>
                                </div>
                                <p class="text-3xl font-bold text-systemBlue">4:12</p>
                                <p class="text-xs text-gray-600 mt-1">Minutos por sessão</p>
                                <div class="mt-3 h-1 w-full bg-blue-200 rounded-full overflow-hidden">
                                    <div class="bg-systemBlue h-1" style="width: 65%"></div>
                                </div>
                            </div>

                            <!-- Frequência de Visitas -->
                            <div class="bg-blue-50 p-4 rounded-lg border border-blue-100">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="text-sm font-medium text-gray-800">Frequência de Visitas</h4>
                                    <span class="text-xs px-2 py-0.5 bg-green-100 text-green-800 rounded-full">+0.5</span>
                                </div>
                                <p class="text-3xl font-bold text-systemBlue">3.8</p>
                                <p class="text-xs text-gray-600 mt-1">Visitas por semana</p>
                                <div class="mt-3 h-1 w-full bg-blue-200 rounded-full overflow-hidden">
                                    <div class="bg-systemBlue h-1" style="width: 76%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Gráfico de Tendência de Engajamento -->
                        <div class="h-[300px] bg-gray-50 rounded-md border border-gray-200 p-2">
                            <!-- Gráfico será renderizado aqui via ApexCharts -->
                            <div class="flex items-center justify-center h-full">
                                <div class="text-center">
                                    <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    <p class="mt-2 text-xs text-gray-600">Carregando tendências de engajamento...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Distribuição de Interações -->
                    <div class="bg-white rounded-lg border border-gray-200 p-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Distribuição de Interações</h3>

                        <div class="h-[250px] bg-gray-50 rounded-md border border-gray-200 p-2 mb-4">
                            <!-- Gráfico de pizza será renderizado aqui via ApexCharts -->
                            <div class="flex items-center justify-center h-full">
                                <div class="text-center">
                                    <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    <p class="mt-2 text-xs text-gray-600">Carregando distribuição...</p>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                    <span class="text-xs text-gray-700">Curtidas</span>
                                </div>
                                <span class="text-xs font-medium">65%</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                    <span class="text-xs text-gray-700">Comentários</span>
                                </div>
                                <span class="text-xs font-medium">25%</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                                    <span class="text-xs text-gray-700">Compartilhamentos</span>
                                </div>
                                <span class="text-xs font-medium">10%</span>
                            </div>
                        </div>

                        <div class="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100">
                            <h4 class="text-xs font-medium text-gray-800 mb-2">Insight</h4>
                            <p class="text-xs text-gray-600">Publicações com imagens têm taxa de engajamento 2.3x maior que publicações apenas com texto.</p>
                        </div>
                    </div>
                </div>

                <!-- Análise de Conteúdo -->
                <div class="bg-white rounded-lg border border-gray-200 p-4 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Análise de Conteúdo e Engajamento</h3>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Tipos de Conteúdo -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-700">Engajamento por Tipo de Conteúdo</h4>
                                <select id="contentTypeFilter" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="all">Todos os Tipos</option>
                                    <option value="engagement">Por Engajamento</option>
                                    <option value="growth">Por Crescimento</option>
                                </select>
                            </div>
                            <div class="h-[300px] bg-gray-50 rounded-md border border-gray-200 p-2">
                                <!-- Gráfico será renderizado aqui via ApexCharts -->
                                <div class="flex items-center justify-center h-full">
                                    <div class="text-center">
                                        <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        <p class="mt-2 text-xs text-gray-600">Carregando análise de conteúdo...</p>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Taxa de engajamento por tipo de conteúdo publicado</p>
                        </div>

                        <!-- Tópicos Populares -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-700">Tópicos Populares</h4>
                                <select id="topicsTimeframe" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="week">Esta Semana</option>
                                    <option value="month" selected>Este Mês</option>
                                    <option value="quarter">Este Trimestre</option>
                                </select>
                            </div>
                            <div class="h-[300px] bg-gray-50 rounded-md border border-gray-200 p-2">
                                <!-- Gráfico será renderizado aqui via ApexCharts -->
                                <div class="flex items-center justify-center h-full">
                                    <div class="text-center">
                                        <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        <p class="mt-2 text-xs text-gray-600">Carregando tópicos populares...</p>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Tópicos mais populares por volume de interações</p>
                        </div>
                    </div>

                    <div class="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100">
                        <h4 class="text-sm font-medium text-gray-800 mb-2">Insights de Conteúdo</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h5 class="text-xs font-medium text-gray-700 mb-1">Conteúdo de Alto Engajamento</h5>
                                <ul class="text-xs text-gray-600 space-y-1 list-disc list-inside">
                                    <li>Casos clínicos recebem 3.2x mais engajamento que a média</li>
                                    <li>Atualizações científicas têm 2.8x mais engajamento</li>
                                    <li>Conteúdo com imagens tem 2.3x mais interações</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="text-xs font-medium text-gray-700 mb-1">Tendências Emergentes</h5>
                                <ul class="text-xs text-gray-600 space-y-1 list-disc list-inside">
                                    <li>Conteúdo sobre gestão de consultório cresceu 34% em popularidade</li>
                                    <li>Discussões sobre novas tecnologias médicas aumentaram 28%</li>
                                    <li>Publicações com vídeos curtos têm crescimento de 45%</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Padrões Temporais -->
                <div class="bg-white rounded-lg border border-gray-200 p-4 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Padrões Temporais de Engajamento</h3>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Horários de Maior Atividade -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-700">Horários de Maior Atividade</h4>
                                <select id="activityTimeView" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="hour">Por Hora</option>
                                    <option value="weekday">Por Dia da Semana</option>
                                </select>
                            </div>
                            <div class="h-[300px] bg-gray-50 rounded-md border border-gray-200 p-2">
                                <!-- Gráfico será renderizado aqui via ApexCharts -->
                                <div class="flex items-center justify-center h-full">
                                    <div class="text-center">
                                        <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        <p class="mt-2 text-xs text-gray-600">Carregando padrões de atividade...</p>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Distribuição de atividade por período</p>
                        </div>

                        <!-- Mapa de Calor de Engajamento -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-700">Mapa de Calor de Engajamento</h4>
                                <select id="heatmapType" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="all">Todas Interações</option>
                                    <option value="likes">Curtidas</option>
                                    <option value="comments">Comentários</option>
                                    <option value="shares">Compartilhamentos</option>
                                </select>
                            </div>
                            <div class="h-[300px] bg-gray-50 rounded-md border border-gray-200 p-2">
                                <!-- Mapa de calor será renderizado aqui via ApexCharts -->
                                <div class="flex items-center justify-center h-full">
                                    <div class="text-center">
                                        <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        <p class="mt-2 text-xs text-gray-600">Carregando mapa de calor...</p>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Intensidade de engajamento por dia e hora</p>
                        </div>
                    </div>

                    <div class="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100">
                        <h4 class="text-sm font-medium text-gray-800 mb-2">Insights Temporais</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h5 class="text-xs font-medium text-gray-700 mb-1">Horários Ideais</h5>
                                <ul class="text-xs text-gray-600 space-y-1 list-disc list-inside">
                                    <li>Os horários de pico são entre 12-14h (28%) e 20-22h (32%)</li>
                                    <li>Publicações entre 20-22h têm 1.4x mais comentários</li>
                                    <li>Conteúdo postado às terças e quintas tem maior alcance</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="text-xs font-medium text-gray-700 mb-1">Padrões Sazonais</h5>
                                <ul class="text-xs text-gray-600 space-y-1 list-disc list-inside">
                                    <li>Domingo tem apenas 15% da atividade média semanal</li>
                                    <li>Engajamento aumenta 22% durante congressos médicos</li>
                                    <li>Períodos de férias (janeiro/julho) mostram queda de 18%</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="influence-analysis" class="interacoes-content hidden">
                <!-- Visão Geral de Influenciadores -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <!-- Mapa de Influência -->
                    <div class="lg:col-span-2 bg-white rounded-lg border border-gray-200 p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">Mapa de Influência na Rede</h3>
                            <div class="flex items-center space-x-2">
                                <select id="influenceMapFilter" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="all">Toda a Rede</option>
                                    <option value="specialty">Por Especialidade</option>
                                    <option value="region">Por Região</option>
                                </select>
                            </div>
                        </div>
                        <div class="h-[400px] bg-gray-50 rounded-md border border-gray-200 p-2">
                            <!-- Visualização será renderizada aqui via D3.js -->
                            <div class="flex items-center justify-center h-full">
                                <div class="text-center">
                                    <svg class="w-12 h-12 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    <p class="mt-4 text-gray-600">Carregando mapa de influência...</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-gray-600 bg-blue-50 p-3 rounded-md border border-blue-100">
                            <div class="flex items-start">
                                <svg class="w-4 h-4 text-systemBlue mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <p class="font-medium mb-1">Como interpretar:</p>
                                    <ul class="list-disc list-inside pl-1 space-y-1">
                                        <li>O tamanho do nó indica o grau de influência</li>
                                        <li>As cores representam diferentes especialidades</li>
                                        <li>A espessura das linhas indica a força da influência</li>
                                        <li>Nós mais centrais têm maior alcance na rede</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Métricas de Influência -->
                    <div class="bg-white rounded-lg border border-gray-200 p-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Métricas de Influência</h3>

                        <div class="space-y-4">
                            <!-- Top 5 Influenciadores -->
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-3">Top 5 Influenciadores</h4>
                                <div class="space-y-2">
                                    <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                <span class="text-sm font-bold text-systemBlue">1</span>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-800">Dr. Carlos Mendes</p>
                                                <p class="text-xs text-gray-500">Cardiologia • São Paulo</p>
                                            </div>
                                            <div class="ml-auto">
                                                <span class="text-sm font-bold text-systemBlue">0.92</span>
                                                <p class="text-xs text-gray-500">Score</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                <span class="text-sm font-bold text-systemBlue">2</span>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-800">Dra. Ana Soares</p>
                                                <p class="text-xs text-gray-500">Neurologia • Rio de Janeiro</p>
                                            </div>
                                            <div class="ml-auto">
                                                <span class="text-sm font-bold text-systemBlue">0.87</span>
                                                <p class="text-xs text-gray-500">Score</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                <span class="text-sm font-bold text-systemBlue">3</span>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-800">Dr. Paulo Ribeiro</p>
                                                <p class="text-xs text-gray-500">Dermatologia • Brasília</p>
                                            </div>
                                            <div class="ml-auto">
                                                <span class="text-sm font-bold text-systemBlue">0.81</span>
                                                <p class="text-xs text-gray-500">Score</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button class="mt-2 text-xs text-systemBlue hover:underline">Ver todos os influenciadores</button>
                            </div>

                            <!-- Distribuição de Influência -->
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-3">Distribuição de Influência</h4>
                                <div class="h-[150px] bg-gray-50 rounded-md border border-gray-200 p-2">
                                    <!-- Gráfico será renderizado aqui via ApexCharts -->
                                    <div class="flex items-center justify-center h-full">
                                        <div class="text-center">
                                            <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                            </svg>
                                            <p class="mt-2 text-xs text-gray-600">Carregando distribuição...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100">
                            <h4 class="text-xs font-medium text-gray-800 mb-2">Insights de Influência</h4>
                            <ul class="text-xs text-gray-600 space-y-2">
                                <li class="flex items-start">
                                    <svg class="w-4 h-4 text-systemBlue mt-0.5 mr-1.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>5% dos usuários geram 42% do engajamento total</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-4 h-4 text-systemBlue mt-0.5 mr-1.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>Influenciadores têm 3.7x mais conexões que a média</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Análise de Alcance e Impacto -->
                <div class="bg-white rounded-lg border border-gray-200 p-4 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Análise de Alcance e Impacto</h3>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Alcance por Especialidade -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-700">Alcance por Especialidade</h4>
                                <select id="reachTimeframe" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="month">Último Mês</option>
                                    <option value="quarter" selected>Último Trimestre</option>
                                    <option value="year">Último Ano</option>
                                </select>
                            </div>
                            <div class="h-[300px] bg-gray-50 rounded-md border border-gray-200 p-2">
                                <!-- Gráfico será renderizado aqui via ApexCharts -->
                                <div class="flex items-center justify-center h-full">
                                    <div class="text-center">
                                        <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        <p class="mt-2 text-xs text-gray-600">Carregando análise de alcance...</p>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Alcance médio de conteúdo por especialidade</p>
                        </div>

                        <!-- Impacto de Conteúdo -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-700">Impacto de Conteúdo</h4>
                                <select id="contentImpactFilter" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="all">Todos os Tipos</option>
                                    <option value="clinical">Casos Clínicos</option>
                                    <option value="scientific">Científico</option>
                                    <option value="management">Gestão</option>
                                </select>
                            </div>
                            <div class="h-[300px] bg-gray-50 rounded-md border border-gray-200 p-2">
                                <!-- Gráfico será renderizado aqui via ApexCharts -->
                                <div class="flex items-center justify-center h-full">
                                    <div class="text-center">
                                        <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        <p class="mt-2 text-xs text-gray-600">Carregando análise de impacto...</p>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Impacto por tipo de conteúdo (engajamento x alcance)</p>
                        </div>
                    </div>

                    <div class="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100">
                        <h4 class="text-sm font-medium text-gray-800 mb-2">Insights de Alcance e Impacto</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h5 class="text-xs font-medium text-gray-700 mb-1">Alcance por Especialidade</h5>
                                <ul class="text-xs text-gray-600 space-y-1 list-disc list-inside">
                                    <li>Dermatologistas têm o maior alcance médio (245 visualizações)</li>
                                    <li>Cardiologistas têm o maior alcance interdisciplinar (42%)</li>
                                    <li>Especialidades cirúrgicas têm alcance mais restrito à própria área</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="text-xs font-medium text-gray-700 mb-1">Impacto de Conteúdo</h5>
                                <ul class="text-xs text-gray-600 space-y-1 list-disc list-inside">
                                    <li>Casos clínicos têm o maior impacto (3.2x a média)</li>
                                    <li>Conteúdo científico tem maior alcance entre especialidades</li>
                                    <li>Conteúdo de gestão tem crescimento de 34% no último trimestre</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="community-detection" class="interacoes-content hidden">
                <!-- Visão Geral de Comunidades -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <!-- Visualização de Comunidades -->
                    <div class="lg:col-span-2 bg-white rounded-lg border border-gray-200 p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">Visualização de Comunidades</h3>
                            <div class="flex items-center space-x-2">
                                <select id="communityAlgorithm" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="louvain" selected>Algoritmo Louvain</option>
                                    <option value="infomap">Algoritmo Infomap</option>
                                    <option value="leiden">Algoritmo Leiden</option>
                                </select>
                                <button class="text-xs text-systemBlue hover:underline flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Ajuda
                                </button>
                            </div>
                        </div>
                        <div class="h-[400px] bg-gray-50 rounded-md border border-gray-200 p-2">
                            <!-- Visualização será renderizada aqui via D3.js -->
                            <div class="flex items-center justify-center h-full">
                                <div class="text-center">
                                    <svg class="w-12 h-12 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    <p class="mt-4 text-gray-600">Carregando comunidades...</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-gray-600 bg-blue-50 p-3 rounded-md border border-blue-100">
                            <div class="flex items-start">
                                <svg class="w-4 h-4 text-systemBlue mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <p class="font-medium mb-1">Como interpretar:</p>
                                    <ul class="list-disc list-inside pl-1 space-y-1">
                                        <li>Cada cor representa uma comunidade distinta</li>
                                        <li>O tamanho do nó indica o número de conexões</li>
                                        <li>A proximidade indica a força das relações</li>
                                        <li>Linhas entre comunidades representam pontes importantes</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Métricas de Comunidades -->
                    <div class="bg-white rounded-lg border border-gray-200 p-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Métricas de Comunidades</h3>

                        <div class="space-y-4">
                            <!-- Estatísticas Gerais -->
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-3">Estatísticas Gerais</h4>
                                <div class="grid grid-cols-2 gap-3">
                                    <div class="bg-gray-50 p-3 rounded-md border border-gray-200 text-center">
                                        <p class="text-2xl font-bold text-systemBlue">8</p>
                                        <p class="text-xs text-gray-500">Comunidades</p>
                                    </div>
                                    <div class="bg-gray-50 p-3 rounded-md border border-gray-200 text-center">
                                        <p class="text-2xl font-bold text-systemBlue">0.68</p>
                                        <p class="text-xs text-gray-500">Modularidade</p>
                                    </div>
                                    <div class="bg-gray-50 p-3 rounded-md border border-gray-200 text-center">
                                        <p class="text-2xl font-bold text-systemBlue">24.5</p>
                                        <p class="text-xs text-gray-500">Tamanho Médio</p>
                                    </div>
                                    <div class="bg-gray-50 p-3 rounded-md border border-gray-200 text-center">
                                        <p class="text-2xl font-bold text-systemBlue">12</p>
                                        <p class="text-xs text-gray-500">Pontes</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Distribuição de Tamanho -->
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-3">Distribuição de Tamanho</h4>
                                <div class="h-[150px] bg-gray-50 rounded-md border border-gray-200 p-2">
                                    <!-- Gráfico será renderizado aqui via ApexCharts -->
                                    <div class="flex items-center justify-center h-full">
                                        <div class="text-center">
                                            <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                            </svg>
                                            <p class="mt-2 text-xs text-gray-600">Carregando distribuição...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100">
                            <h4 class="text-xs font-medium text-gray-800 mb-2">Insights de Comunidades</h4>
                            <ul class="text-xs text-gray-600 space-y-2">
                                <li class="flex items-start">
                                    <svg class="w-4 h-4 text-systemBlue mt-0.5 mr-1.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>Comunidades formadas principalmente por especialidade (72%)</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-4 h-4 text-systemBlue mt-0.5 mr-1.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>Comunidades menores têm maior coesão interna (0.82 vs 0.65)</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Análise Detalhada de Comunidades -->
                <div class="bg-white rounded-lg border border-gray-200 p-4 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Análise Detalhada de Comunidades</h3>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Características das Comunidades -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-700">Características das Comunidades</h4>
                                <select id="communityDetailFilter" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="all">Todas as Comunidades</option>
                                    <option value="top5">Top 5 Comunidades</option>
                                    <option value="specialty">Por Especialidade</option>
                                </select>
                            </div>
                            <div class="h-[300px] bg-gray-50 rounded-md border border-gray-200 p-2">
                                <!-- Tabela será renderizada aqui -->
                                <div class="flex items-center justify-center h-full">
                                    <div class="text-center">
                                        <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        <p class="mt-2 text-xs text-gray-600">Carregando características...</p>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Características principais de cada comunidade</p>
                        </div>

                        <!-- Interações Entre Comunidades -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-700">Interações Entre Comunidades</h4>
                                <select id="communityInteractionView" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                    <option value="matrix">Matriz de Interações</option>
                                    <option value="chord">Diagrama de Acordes</option>
                                    <option value="sankey">Diagrama de Sankey</option>
                                </select>
                            </div>
                            <div class="h-[300px] bg-gray-50 rounded-md border border-gray-200 p-2">
                                <!-- Visualização será renderizada aqui via D3.js -->
                                <div class="flex items-center justify-center h-full">
                                    <div class="text-center">
                                        <svg class="w-8 h-8 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        <p class="mt-2 text-xs text-gray-600">Carregando interações...</p>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Padrões de interação entre diferentes comunidades</p>
                        </div>
                    </div>

                    <div class="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100">
                        <h4 class="text-sm font-medium text-gray-800 mb-2">Insights de Interações</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h5 class="text-xs font-medium text-gray-700 mb-1">Comunidades Principais</h5>
                                <ul class="text-xs text-gray-600 space-y-1 list-disc list-inside">
                                    <li>Comunidade 1: Predominantemente cardiologistas (78%)</li>
                                    <li>Comunidade 2: Mista de neurologistas e psiquiatras</li>
                                    <li>Comunidade 3: Dermatologistas e endocrinologistas</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="text-xs font-medium text-gray-700 mb-1">Pontes Entre Comunidades</h5>
                                <ul class="text-xs text-gray-600 space-y-1 list-disc list-inside">
                                    <li>Médicos de família atuam como principais pontes (42%)</li>
                                    <li>Comunidades 1 e 3 têm a maior taxa de interação (0.38)</li>
                                    <li>Comunidade 5 é a mais isolada (apenas 0.12 de interação)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Evolução Temporal das Comunidades -->
                <div class="bg-white rounded-lg border border-gray-200 p-4 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">Evolução Temporal das Comunidades</h3>
                        <div class="flex items-center space-x-2">
                            <select id="communityEvolutionPeriod" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                                <option value="6months">Últimos 6 Meses</option>
                                <option value="year" selected>Último Ano</option>
                                <option value="2years">Últimos 2 Anos</option>
                            </select>
                        </div>
                    </div>

                    <div class="h-[400px] bg-gray-50 rounded-md border border-gray-200 p-2">
                        <!-- Visualização será renderizada aqui via D3.js -->
                        <div class="flex items-center justify-center h-full">
                            <div class="text-center">
                                <svg class="w-12 h-12 mx-auto text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <p class="mt-4 text-gray-600">Carregando evolução temporal...</p>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100">
                        <h4 class="text-sm font-medium text-gray-800 mb-2">Insights de Evolução</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h5 class="text-xs font-medium text-gray-700 mb-1">Tendências de Crescimento</h5>
                                <ul class="text-xs text-gray-600 space-y-1 list-disc list-inside">
                                    <li>Comunidade 3 teve o maior crescimento (45% em 6 meses)</li>
                                    <li>Comunidades interdisciplinares crescem 2.3x mais rápido</li>
                                    <li>Comunidade 7 surgiu nos últimos 3 meses (telemedicina)</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="text-xs font-medium text-gray-700 mb-1">Mudanças Estruturais</h5>
                                <ul class="text-xs text-gray-600 space-y-1 list-disc list-inside">
                                    <li>Comunidades 2 e 4 estão em processo de fusão</li>
                                    <li>Comunidade 1 dividiu-se em duas comunidades especializadas</li>
                                    <li>Número total de comunidades aumentou de 5 para 8 no último ano</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="bg-gray-50 px-6 py-3 flex justify-between items-center border-t border-gray-200">
            <div class="text-xs text-gray-500">
                <span class="font-medium">Última atualização:</span> Hoje às 14:32
            </div>
            <div class="flex space-x-2">
                <button class="px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                    Exportar Dados
                </button>
                <button class="px-4 py-2 bg-systemBlue border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-600">
                    Atualizar Análise
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<!-- Bibliotecas para visualizações avançadas -->
<script src="https://d3js.org/d3.v7.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script src="https://cdn.jsdelivr.net/npm/force-graph"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM carregado, iniciando aplicação...');

        // Verificar se as bibliotecas necessárias estão carregadas
        checkRequiredLibraries();

        // Dados do dashboard
        const dashboardData = {{ data | tojson |e }};

        // Inicializar gráficos
        initializeNetworkCharts(dashboardData);

        // Aguardar carregamento das bibliotecas e inicializar visualização principal
        waitForLibrariesAndInitialize();

        // Inicializar modal de Interações 360
        initializeInteracoes360Modal();
    });

    // Função para aguardar o carregamento das bibliotecas
    function waitForLibrariesAndInitialize() {
        let attempts = 0;
        const maxAttempts = 10;

        function checkAndInit() {
            attempts++;
            console.log(`Tentativa ${attempts} de inicializar visualização principal...`);

            if (typeof d3 !== 'undefined' || typeof ForceGraph !== 'undefined') {
                console.log('Bibliotecas carregadas, inicializando visualização principal...');
                initializeMainNetworkVisualization();
                return;
            }

            if (attempts < maxAttempts) {
                console.log('Bibliotecas ainda não carregadas, tentando novamente em 500ms...');
                setTimeout(checkAndInit, 500);
            } else {
                console.log('Timeout aguardando bibliotecas, criando visualização de fallback...');
                const container = document.getElementById('mainNetworkVisualization');
                if (container) {
                    createFallbackVisualization(container, generateNetworkData());
                }
            }
        }

        // Iniciar verificação após 500ms
        setTimeout(checkAndInit, 500);
    }

    // Função para verificar se as bibliotecas necessárias estão carregadas
    function checkRequiredLibraries() {
        console.log('Verificando bibliotecas necessárias...');

        // Verificar D3.js
        if (typeof d3 === 'undefined') {
            console.error('D3.js não está carregado!');
            loadScript('https://cdn.jsdelivr.net/npm/d3@7');
        } else {
            console.log('D3.js está carregado:', d3.version);
        }

        // Verificar ApexCharts
        if (typeof ApexCharts === 'undefined') {
            console.error('ApexCharts não está carregado!');
            loadScript('https://cdn.jsdelivr.net/npm/apexcharts');
        } else {
            console.log('ApexCharts está carregado');
        }

        // Verificar ForceGraph
        if (typeof ForceGraph === 'undefined') {
            console.error('ForceGraph não está carregado!');
            loadScript('https://unpkg.com/force-graph');
        } else {
            console.log('ForceGraph está carregado');
        }

        // Verificar D3-Sankey
        if (typeof d3.sankey === 'undefined') {
            console.error('D3-Sankey não está carregado!');
            loadScript('https://unpkg.com/d3-sankey@0.12.3/dist/d3-sankey.min.js');
        } else {
            console.log('D3-Sankey está carregado');
        }
    }

    // Função para carregar scripts dinamicamente
    function loadScript(url) {
        return new Promise((resolve, reject) => {
            console.log(`Carregando script: ${url}`);
            const script = document.createElement('script');
            script.src = url;
            script.onload = () => {
                console.log(`Script carregado: ${url}`);
                resolve();
            };
            script.onerror = (error) => {
                console.error(`Erro ao carregar script ${url}:`, error);
                reject(error);
            };
            document.head.appendChild(script);
        });
    }

    // Função para inicializar o modal de Interações 360
    function initializeInteracoes360Modal() {
        console.log('Inicializando modal de Interações 360');
        const modal = document.getElementById('interacoes360Modal');
        const openModalBtn = document.getElementById('interacoes360Btn');
        const closeModalBtn = document.getElementById('closeInteracoes360Modal');
        const tabs = document.querySelectorAll('.interacoes-tab');
        const tabContents = document.querySelectorAll('.interacoes-content');

        console.log('Modal:', modal);
        console.log('Botão de abrir:', openModalBtn);
        console.log('Botão de fechar:', closeModalBtn);

        // Abrir modal
        if (openModalBtn) {
            console.log('Adicionando evento de clique ao botão de abrir');
            openModalBtn.addEventListener('click', function() {
                console.log('Botão de abrir clicado');
                modal.classList.remove('hidden');
                document.body.classList.add('overflow-hidden');

                // Inicializar a primeira aba (Análise de Rede) por padrão
                if (!window.networkVisualizationInitialized) {
                    console.log('Inicializando visualização de rede');
                    initializeNetworkVisualization();
                    initializeConnectionMatrix();
                    initializeConnectionDistribution();
                    window.networkVisualizationInitialized = true;
                }
            });
        }

        // Fechar modal
        if (closeModalBtn) {
            console.log('Adicionando evento de clique ao botão de fechar');
            closeModalBtn.addEventListener('click', function() {
                console.log('Botão de fechar clicado');
                modal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            });
        }

        // Fechar modal ao clicar fora do conteúdo
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                    document.body.classList.remove('overflow-hidden');
                }
            });
        }

        // Alternar entre abas
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const tabId = this.getAttribute('data-tab');

                // Atualizar estado das abas
                tabs.forEach(t => {
                    t.classList.remove('border-systemBlue', 'text-systemBlue');
                    t.classList.add('border-transparent', 'text-gray-500');
                });
                this.classList.remove('border-transparent', 'text-gray-500');
                this.classList.add('border-systemBlue', 'text-systemBlue');

                // Atualizar conteúdo visível
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });
                document.getElementById(tabId).classList.remove('hidden');

                // Inicializar visualizações específicas da aba
                console.log('Verificando inicialização da aba:', tabId);

                if (tabId === 'network-analysis' && !window.networkVisualizationInitialized) {
                    console.log('Inicializando visualizações da aba Análise de Rede');
                    setTimeout(() => {
                        initializeNetworkVisualization();
                        initializeConnectionMatrix();
                        initializeConnectionDistribution();
                        window.networkVisualizationInitialized = true;
                    }, 300);
                } else if (tabId === 'engagement-patterns' && !window.engagementPatternsInitialized) {
                    console.log('Inicializando visualizações da aba Padrões de Engajamento');
                    setTimeout(() => {
                        initializeEngagementMetrics();
                        initializeEngagementTrends();
                        initializeContentAnalysis();
                        initializeTemporalPatterns();
                        window.engagementPatternsInitialized = true;
                    }, 300);
                } else if (tabId === 'influence-analysis' && !window.influenceAnalysisInitialized) {
                    console.log('Inicializando visualizações da aba Análise de Influência');
                    setTimeout(() => {
                        try {
                            // Garantir que os containers existam antes de inicializar
                            const influenceMapContainer = findVisualizationContainer('influenceMapFilter', 'h-[400px]');
                            if (influenceMapContainer) {
                                console.log('Container de mapa de influência encontrado:', influenceMapContainer);
                                initializeInfluenceMap();
                            } else {
                                console.error('Container de mapa de influência não encontrado');
                            }

                            // Inicializar outras visualizações
                            initializeInfluenceMetrics();
                            initializeReachAnalysis();
                            initializeContentImpact();
                            window.influenceAnalysisInitialized = true;
                        } catch (e) {
                            console.error('Erro ao inicializar visualizações de Análise de Influência:', e);
                        }
                    }, 500);
                } else if (tabId === 'community-detection' && !window.communityDetectionInitialized) {
                    console.log('Inicializando visualizações da aba Detecção de Comunidades');
                    setTimeout(() => {
                        try {
                            // Garantir que os containers existam antes de inicializar
                            const communityContainer = findVisualizationContainer('communityAlgorithm', 'h-[400px]');
                            if (communityContainer) {
                                console.log('Container de visualização de comunidades encontrado:', communityContainer);
                                initializeCommunityVisualization();
                            } else {
                                console.error('Container de visualização de comunidades não encontrado');
                            }

                            // Inicializar outras visualizações
                            initializeCommunityMetrics();
                            initializeCommunityDetails();
                            initializeCommunityEvolution();
                            window.communityDetectionInitialized = true;
                        } catch (e) {
                            console.error('Erro ao inicializar visualizações de Detecção de Comunidades:', e);
                        }
                    }, 500);
                }
            });
        });

        // Inicializar visualizações quando o modal for aberto
        if (openModalBtn) {
            openModalBtn.addEventListener('click', function() {
                console.log('Modal aberto, inicializando primeira aba');

                // Selecionar a primeira aba
                if (tabs.length > 0) {
                    const firstTab = tabs[0];
                    const firstTabId = firstTab.getAttribute('data-tab');

                    // Atualizar classes da primeira aba
                    tabs.forEach(t => {
                        t.classList.remove('border-systemBlue', 'text-systemBlue');
                        t.classList.add('border-transparent', 'text-gray-500');
                    });
                    firstTab.classList.remove('border-transparent', 'text-gray-500');
                    firstTab.classList.add('border-systemBlue', 'text-systemBlue');

                    // Mostrar conteúdo da primeira aba
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    const firstTabContent = document.getElementById(firstTabId);
                    if (firstTabContent) {
                        firstTabContent.classList.remove('hidden');
                    }
                }

                // Inicializar a primeira aba (Análise de Rede) por padrão
                if (!window.networkVisualizationInitialized) {
                    console.log('Inicializando visualização de rede');
                    setTimeout(() => {
                        initializeNetworkVisualization();
                        initializeConnectionMatrix();
                        initializeConnectionDistribution();
                        window.networkVisualizationInitialized = true;
                    }, 500); // Pequeno atraso para garantir que o DOM esteja pronto
                }
            });
        }

        // Inicializar seletores de visualização
        initializeViewSelectors();

        // Inicializar controles da visualização principal
        initializeMainNetworkControls();
    }

    // Função auxiliar para encontrar containers de visualização
    function findVisualizationContainer(parentId, containerClass = 'h-[300px]') {
        console.log(`Procurando container para ${parentId} com classe ${containerClass}`);

        try {
            // Estratégia 1: Tentar encontrar pelo ID do parent
            const parent = document.getElementById(parentId);
            if (!parent) {
                console.log(`Parent ${parentId} não encontrado`);

                // Tentar encontrar o container pelo ID da aba
                const tabId = parentId.split('Filter')[0].split('View')[0];
                const tabContent = document.getElementById(tabId + '-tab-content');
                if (tabContent) {
                    console.log(`Tentando encontrar container dentro da aba ${tabId}`);
                    const containerInTab = tabContent.querySelector('div[class*="' + containerClass + '"]');
                    if (containerInTab) {
                        console.log(`Container encontrado dentro da aba ${tabId}`);
                        return containerInTab;
                    }
                }

                return createNewContainer(parentId, containerClass);
            }

            // Estratégia 2: Procurar pelo elemento mais próximo
            const parentElement = parent.closest('div');
            if (!parentElement) {
                console.log(`Elemento pai para ${parentId} não encontrado`);
                return createNewContainer(parentId, containerClass);
            }

            // Estratégia 3: Procurar por qualquer div com a classe especificada dentro do parent
            let container = null;

            // Primeiro, tentar encontrar diretamente pelo seletor de classe (mesmo que não seja válido para querySelector)
            const allDivs = parentElement.querySelectorAll('div');
            for (const div of allDivs) {
                if (div.className && div.className.includes(containerClass)) {
                    container = div;
                    break;
                }
            }

            // Se não encontrou, tentar o segundo elemento div (geralmente é o container de visualização)
            if (!container) {
                const divs = parentElement.querySelectorAll('div');
                if (divs.length > 1) {
                    container = divs[1]; // Geralmente o segundo div é o container
                }
            }

            // Se ainda não encontrou, criar um novo container
            if (!container) {
                return createNewContainer(parentId, containerClass, parentElement);
            }

            return container;
        } catch (e) {
            console.error(`Erro ao procurar container para ${parentId}:`, e);
            return createNewContainer(parentId, containerClass);
        }
    }

    // Função auxiliar para criar um novo container
    function createNewContainer(parentId, containerClass, parentElement = null) {
        console.log(`Criando novo container para ${parentId}`);

        try {
            const container = document.createElement('div');
            container.id = parentId + '-container';
            container.className = 'visualization-container ' + containerClass;
            container.style.minHeight = containerClass.includes('400px') ? '400px' : '300px';

            // Se não temos um elemento pai, tentar encontrar a aba
            if (!parentElement) {
                const tabId = parentId.split('Filter')[0].split('View')[0];
                const tabContent = document.getElementById(tabId + '-tab-content') ||
                                  document.getElementById(tabId);

                if (tabContent) {
                    console.log(`Adicionando container à aba ${tabId}`);
                    tabContent.appendChild(container);
                } else {
                    // Último recurso: adicionar ao corpo do modal
                    const modal = document.querySelector('.interacoes-content:not(.hidden)');
                    if (modal) {
                        console.log('Adicionando container ao modal');
                        modal.appendChild(container);
                    } else {
                        console.error('Não foi possível encontrar um local para adicionar o container');
                        return null;
                    }
                }
            } else {
                // Adicionar o container ao parent
                if (parentElement.children.length > 0) {
                    // Adicionar após o primeiro filho (geralmente o título/seletor)
                    parentElement.insertBefore(container, parentElement.children[0].nextSibling);
                } else {
                    parentElement.appendChild(container);
                }
            }

            console.log('Novo container criado:', container);
            return container;
        } catch (e) {
            console.error('Erro ao criar novo container:', e);
            return null;
        }
    }

    // Função para inicializar controles da visualização principal
    function initializeMainNetworkControls() {
        // Seletor de tipo de visualização principal
        const mainNetworkViewType = document.getElementById('mainNetworkViewType');
        if (mainNetworkViewType) {
            mainNetworkViewType.addEventListener('change', function() {
                console.log('Mudando tipo de visualização principal para:', this.value);
                initializeMainNetworkVisualization(this.value);
            });
        }

        // Botão de tela cheia
        const fullscreenBtn = document.getElementById('fullscreenNetworkBtn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', function() {
                console.log('Abrindo modal de Interações 360');
                // Abrir o modal de Interações 360
                const modal = document.getElementById('interacoes360Modal');
                if (modal) {
                    modal.classList.remove('hidden');
                    document.body.classList.add('overflow-hidden');

                    // Inicializar visualizações do modal se necessário
                    if (!window.networkVisualizationInitialized) {
                        setTimeout(() => {
                            initializeNetworkVisualization();
                            initializeConnectionMatrix();
                            initializeConnectionDistribution();
                            window.networkVisualizationInitialized = true;
                        }, 300);
                    }
                }
            });
        }
    }

    // Função para inicializar os seletores de visualização
    function initializeViewSelectors() {
        // Tipo de visualização de rede
        const networkViewType = document.getElementById('networkViewType');
        if (networkViewType) {
            networkViewType.addEventListener('change', function() {
                initializeNetworkVisualization(this.value);
            });
        }

        // Filtro de matriz de conexões
        const connectionMatrixFilter = document.getElementById('connectionMatrixFilter');
        if (connectionMatrixFilter) {
            connectionMatrixFilter.addEventListener('change', function() {
                initializeConnectionMatrix(this.value);
            });
        }

        // Visualização de distribuição de conexões
        const connectionDistributionView = document.getElementById('connectionDistributionView');
        if (connectionDistributionView) {
            connectionDistributionView.addEventListener('change', function() {
                initializeConnectionDistribution(this.value);
            });
        }

        // Algoritmo de detecção de comunidades
        const communityAlgorithm = document.getElementById('communityAlgorithm');
        if (communityAlgorithm) {
            communityAlgorithm.addEventListener('change', function() {
                console.log('Algoritmo de comunidade alterado para:', this.value);
                initializeCommunityVisualization(this.value);
            });
        }

        // Filtro de detalhes de comunidades
        const communityDetailFilter = document.getElementById('communityDetailFilter');
        if (communityDetailFilter) {
            communityDetailFilter.addEventListener('change', function() {
                console.log('Filtro de detalhes de comunidade alterado para:', this.value);
                initializeCommunityDetails(this.value);
            });
        }

        // Visualização de interações entre comunidades
        const communityInteractionView = document.getElementById('communityInteractionView');
        if (communityInteractionView) {
            communityInteractionView.addEventListener('change', function() {
                console.log('Visualização de interações alterada para:', this.value);
                initializeCommunityInteractions(this.value);
            });
        }

        // Período de evolução de comunidades
        const communityEvolutionPeriod = document.getElementById('communityEvolutionPeriod');
        if (communityEvolutionPeriod) {
            communityEvolutionPeriod.addEventListener('change', function() {
                console.log('Período de evolução alterado para:', this.value);
                initializeCommunityEvolution(this.value);
            });
        }
    }

    // Função para inicializar a visualização principal da rede (na parte superior da página)
    function initializeMainNetworkVisualization(viewType = 'force') {
        console.log('Inicializando visualização principal da rede com tipo:', viewType);
        const container = document.getElementById('mainNetworkVisualization');

        if (!container) {
            console.error('Container mainNetworkVisualization não encontrado!');
            return;
        }

        // Limpar o container
        container.innerHTML = '';

        // Gerar dados da rede
        const networkData = generateNetworkData();
        console.log('Dados da rede gerados:', networkData);

        // Atualizar métricas na interface
        updateNetworkMetrics(networkData);

        // Sempre usar D3.js para máxima compatibilidade
        createD3NetworkVisualization(container, networkData, viewType);
    }

    // Função para atualizar métricas da rede na interface
    function updateNetworkMetrics(networkData) {
        const nodeCountEl = document.getElementById('networkNodeCount');
        const linkCountEl = document.getElementById('networkLinkCount');
        const densityEl = document.getElementById('networkDensity');
        const specialtiesEl = document.getElementById('networkSpecialties');

        if (nodeCountEl) nodeCountEl.textContent = networkData.nodes.length;
        if (linkCountEl) linkCountEl.textContent = networkData.links.length;
        if (densityEl) {
            const maxPossibleLinks = (networkData.nodes.length * (networkData.nodes.length - 1)) / 2;
            const density = (networkData.links.length / maxPossibleLinks).toFixed(3);
            densityEl.textContent = density;
        }
        if (specialtiesEl) {
            const specialties = [...new Set(networkData.nodes.map(n => n.specialty))];
            specialtiesEl.textContent = specialties.length;
        }
    }

    // Função para criar visualização D3.js
    function createD3NetworkVisualization(container, networkData, viewType) {
        const width = container.clientWidth;
        const height = container.clientHeight;

        // Verificar se D3 está disponível
        if (typeof d3 === 'undefined') {
            console.error('D3.js não está disponível, criando visualização de fallback');
            createFallbackVisualization(container, networkData);
            return;
        }

        console.log('Criando visualização D3.js...');

        const svg = d3.select(container)
            .append('svg')
            .attr('width', width)
            .attr('height', height)
            .style('background', 'transparent');

        if (viewType === 'force') {
            createForceDirectedGraph(svg, networkData, width, height);
        } else if (viewType === 'radial') {
            createRadialGraph(svg, networkData, width, height);
        } else if (viewType === 'hierarchical') {
            createHierarchicalGraph(svg, networkData, width, height);
        } else {
            // Default para force
            createForceDirectedGraph(svg, networkData, width, height);
        }
    }

    // Função para criar gráfico de força dirigida
    function createForceDirectedGraph(svg, networkData, width, height) {
        console.log('Criando gráfico de força dirigida...');

        // Criar simulação de força
        const simulation = d3.forceSimulation(networkData.nodes)
            .force('link', d3.forceLink(networkData.links).id(d => d.id).distance(100))
            .force('charge', d3.forceManyBody().strength(-400))
            .force('center', d3.forceCenter(width / 2, height / 2))
            .force('collision', d3.forceCollide().radius(d => Math.sqrt(d.connections) * 3 + 10));

        // Criar grupo principal
        const g = svg.append('g');

        // Criar links
        const link = g.append('g')
            .attr('class', 'links')
            .selectAll('line')
            .data(networkData.links)
            .enter()
            .append('line')
            .attr('stroke', '#999')
            .attr('stroke-opacity', 0.6)
            .attr('stroke-width', d => Math.sqrt(d.strength) * 3);

        // Criar nós
        const node = g.append('g')
            .attr('class', 'nodes')
            .selectAll('circle')
            .data(networkData.nodes)
            .enter()
            .append('circle')
            .attr('r', d => Math.sqrt(d.connections) * 2 + 5)
            .attr('fill', d => d.color)
            .attr('stroke', '#fff')
            .attr('stroke-width', 2)
            .style('cursor', 'pointer')
            .call(d3.drag()
                .on('start', dragstarted)
                .on('drag', dragged)
                .on('end', dragended));

        // Adicionar tooltips
        node.append('title')
            .text(d => `${d.name}\nEspecialidade: ${d.specialty}\nConexões: ${d.connections}`);

        // Adicionar labels para nós importantes
        const labels = g.append('g')
            .attr('class', 'labels')
            .selectAll('text')
            .data(networkData.nodes.filter(d => d.connections > 20))
            .enter()
            .append('text')
            .attr('text-anchor', 'middle')
            .attr('dy', '.35em')
            .attr('font-size', '10px')
            .attr('font-weight', 'bold')
            .attr('fill', '#333')
            .attr('pointer-events', 'none')
            .text(d => d.name.split(' ')[0]);

        // Atualizar posições na simulação
        simulation.on('tick', () => {
            link
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y);

            node
                .attr('cx', d => d.x)
                .attr('cy', d => d.y);

            labels
                .attr('x', d => d.x)
                .attr('y', d => d.y);
        });

        // Funções de drag
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }

        // Adicionar zoom e pan
        const zoom = d3.zoom()
            .scaleExtent([0.1, 4])
            .on('zoom', (event) => {
                g.attr('transform', event.transform);
            });

        svg.call(zoom);

        // Configurar controles de zoom
        setupZoomControls(svg, zoom);

        console.log('Gráfico de força dirigida criado com sucesso!');
    }

    // Função para configurar controles de zoom
    function setupZoomControls(svg, zoom) {
        const zoomInBtn = document.getElementById('zoomInBtn');
        const zoomOutBtn = document.getElementById('zoomOutBtn');
        const resetViewBtn = document.getElementById('resetViewBtn');

        if (zoomInBtn) {
            zoomInBtn.onclick = () => {
                svg.transition().duration(300).call(
                    zoom.scaleBy, 1.5
                );
            };
        }

        if (zoomOutBtn) {
            zoomOutBtn.onclick = () => {
                svg.transition().duration(300).call(
                    zoom.scaleBy, 1 / 1.5
                );
            };
        }

        if (resetViewBtn) {
            resetViewBtn.onclick = () => {
                svg.transition().duration(750).call(
                    zoom.transform,
                    d3.zoomIdentity
                );
            };
        }
    }

    // Função para criar gráfico radial
    function createRadialGraph(svg, networkData, width, height) {
        console.log('Criando gráfico radial...');

        const centerX = width / 2;
        const centerY = height / 2;
        const radius = Math.min(width, height) / 2 - 50;

        // Agrupar nós por especialidade
        const specialtyGroups = {};
        networkData.nodes.forEach(node => {
            if (!specialtyGroups[node.specialty]) {
                specialtyGroups[node.specialty] = [];
            }
            specialtyGroups[node.specialty].push(node);
        });

        // Posicionar nós em círculos concêntricos
        let angleOffset = 0;
        Object.keys(specialtyGroups).forEach((specialty, i) => {
            const nodes = specialtyGroups[specialty];
            const angleStep = (2 * Math.PI) / nodes.length;
            const specialtyRadius = radius * (0.4 + (i * 0.15));

            nodes.forEach((node, j) => {
                const angle = angleOffset + (j * angleStep);
                node.x = centerX + specialtyRadius * Math.cos(angle);
                node.y = centerY + specialtyRadius * Math.sin(angle);
            });

            angleOffset += Math.PI / 4;
        });

        // Criar grupo principal
        const g = svg.append('g');

        // Desenhar links
        g.selectAll('line')
            .data(networkData.links)
            .enter()
            .append('line')
            .attr('x1', d => networkData.nodes.find(n => n.id === d.source).x)
            .attr('y1', d => networkData.nodes.find(n => n.id === d.source).y)
            .attr('x2', d => networkData.nodes.find(n => n.id === d.target).x)
            .attr('y2', d => networkData.nodes.find(n => n.id === d.target).y)
            .attr('stroke', '#999')
            .attr('stroke-opacity', 0.3)
            .attr('stroke-width', d => d.strength * 2);

        // Desenhar nós
        g.selectAll('circle')
            .data(networkData.nodes)
            .enter()
            .append('circle')
            .attr('cx', d => d.x)
            .attr('cy', d => d.y)
            .attr('r', d => Math.sqrt(d.connections) * 2 + 3)
            .attr('fill', d => d.color)
            .attr('stroke', '#fff')
            .attr('stroke-width', 2)
            .append('title')
            .text(d => `${d.name}\nEspecialidade: ${d.specialty}\nConexões: ${d.connections}`);

        // Adicionar zoom
        const zoom = d3.zoom()
            .scaleExtent([0.1, 4])
            .on('zoom', (event) => {
                g.attr('transform', event.transform);
            });

        svg.call(zoom);
        setupZoomControls(svg, zoom);

        console.log('Gráfico radial criado com sucesso!');
    }

    // Função para criar gráfico hierárquico
    function createHierarchicalGraph(svg, networkData, width, height) {
        console.log('Criando gráfico hierárquico...');

        // Agrupar nós por especialidade
        const specialtyGroups = {};
        networkData.nodes.forEach(node => {
            if (!specialtyGroups[node.specialty]) {
                specialtyGroups[node.specialty] = [];
            }
            specialtyGroups[node.specialty].push(node);
        });

        // Criar dados hierárquicos
        const hierarchyData = {
            name: "Rede Médica",
            children: Object.keys(specialtyGroups).map(specialty => ({
                name: specialty,
                children: specialtyGroups[specialty].map(node => ({
                    name: node.name,
                    value: node.connections,
                    color: node.color,
                    id: node.id
                }))
            }))
        };

        // Criar layout de árvore
        const treeLayout = d3.tree()
            .size([height - 100, width - 200]);

        const root = d3.hierarchy(hierarchyData);
        treeLayout(root);

        // Criar grupo principal
        const g = svg.append('g')
            .attr('transform', 'translate(50, 50)');

        // Desenhar links
        g.selectAll('path')
            .data(root.links())
            .enter()
            .append('path')
            .attr('d', d3.linkHorizontal()
                .x(d => d.y)
                .y(d => d.x))
            .attr('fill', 'none')
            .attr('stroke', '#999')
            .attr('stroke-opacity', 0.6)
            .attr('stroke-width', 1.5);

        // Desenhar nós
        const nodes = g.selectAll('g')
            .data(root.descendants())
            .enter()
            .append('g')
            .attr('transform', d => `translate(${d.y},${d.x})`);

        nodes.append('circle')
            .attr('r', d => d.depth === 0 ? 10 : d.depth === 1 ? 8 : Math.sqrt(d.data.value || 5) * 1.5)
            .attr('fill', d => d.depth === 0 ? '#4A5568' : d.depth === 1 ? '#2D3748' : d.data.color || '#A0AEC0')
            .attr('stroke', '#fff')
            .attr('stroke-width', 2);

        nodes.append('text')
            .attr('dy', d => d.depth === 0 ? -15 : d.depth === 1 ? -12 : 4)
            .attr('x', d => d.depth === 0 ? 0 : d.depth === 1 ? -8 : 8)
            .attr('text-anchor', d => d.depth === 0 ? 'middle' : d.depth === 1 ? 'end' : 'start')
            .attr('font-size', d => d.depth === 0 ? 14 : d.depth === 1 ? 12 : 10)
            .attr('font-weight', d => d.depth < 2 ? 'bold' : 'normal')
            .text(d => d.depth < 2 ? d.data.name : d.data.name.split(' ')[0]);

        // Adicionar zoom
        const zoom = d3.zoom()
            .scaleExtent([0.1, 4])
            .on('zoom', (event) => {
                g.attr('transform', `translate(50, 50) ${event.transform}`);
            });

        svg.call(zoom);
        setupZoomControls(svg, zoom);

        console.log('Gráfico hierárquico criado com sucesso!');
    }
            // Implementar diagrama de força com D3.js nativo
            const width = container.clientWidth;
            const height = container.clientHeight;

            const svg = d3.select(container)
                .append('svg')
                .attr('width', width)
                .attr('height', height);

            // Criar simulação de força
            const simulation = d3.forceSimulation(networkData.nodes)
                .force('link', d3.forceLink(networkData.links).id(d => d.id).distance(80))
                .force('charge', d3.forceManyBody().strength(-300))
                .force('center', d3.forceCenter(width / 2, height / 2))
                .force('collision', d3.forceCollide().radius(d => Math.sqrt(d.connections) * 2 + 5));

            // Criar links
            const link = svg.append('g')
                .attr('class', 'links')
                .selectAll('line')
                .data(networkData.links)
                .enter()
                .append('line')
                .attr('stroke', 'rgba(0, 122, 255, 0.3)')
                .attr('stroke-width', d => d.strength * 3)
                .attr('stroke-opacity', 0.6);

            // Criar nós
            const node = svg.append('g')
                .attr('class', 'nodes')
                .selectAll('circle')
                .data(networkData.nodes)
                .enter()
                .append('circle')
                .attr('r', d => Math.sqrt(d.connections) * 2)
                .attr('fill', d => d.color)
                .attr('stroke', '#fff')
                .attr('stroke-width', 2)
                .call(d3.drag()
                    .on('start', dragstarted)
                    .on('drag', dragged)
                    .on('end', dragended));

            // Adicionar tooltips
            node.append('title')
                .text(d => `${d.name}\nEspecialidade: ${d.specialty}\nConexões: ${d.connections}`);

            // Atualizar posições na simulação
            simulation.on('tick', () => {
                link
                    .attr('x1', d => d.source.x)
                    .attr('y1', d => d.source.y)
                    .attr('x2', d => d.target.x)
                    .attr('y2', d => d.target.y);

                node
                    .attr('cx', d => d.x)
                    .attr('cy', d => d.y);
            });

            // Funções de drag
            function dragstarted(event, d) {
                if (!event.active) simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            }

            function dragged(event, d) {
                d.fx = event.x;
                d.fy = event.y;
            }

            function dragended(event, d) {
                if (!event.active) simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            }

            // Adicionar zoom e pan
            const zoom = d3.zoom()
                .scaleExtent([0.1, 4])
                .on('zoom', (event) => {
                    svg.selectAll('g').attr('transform', event.transform);
                });

            svg.call(zoom);
        } else {
            // Para outros tipos, usar a mesma lógica da função original
            console.log('Usando visualização alternativa:', viewType);
            initializeNetworkVisualization(viewType, container);
        }

        // Se nenhuma visualização funcionou, mostrar fallback
        setTimeout(() => {
            if (container.children.length === 0 || container.innerHTML.includes('Carregando')) {
                console.log('Criando visualização de fallback...');
                createFallbackVisualization(container, networkData);
            }
        }, 2000);
    }

    // Função de fallback para criar uma visualização simples
    function createFallbackVisualization(container, networkData) {
        container.innerHTML = '';

        // Criar uma visualização simples com HTML/CSS
        const fallbackDiv = document.createElement('div');
        fallbackDiv.className = 'w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100';

        fallbackDiv.innerHTML = `
            <div class="text-center p-8">
                <div class="mb-6">
                    <svg class="w-24 h-24 mx-auto text-blue-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">Rede de Conexões</h3>
                <p class="text-gray-600 mb-4">Visualização da rede social médica</p>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="bg-white rounded-lg p-3 shadow-sm">
                        <div class="text-2xl font-bold text-blue-600">${networkData.nodes.length}</div>
                        <div class="text-gray-600">Profissionais</div>
                    </div>
                    <div class="bg-white rounded-lg p-3 shadow-sm">
                        <div class="text-2xl font-bold text-green-600">${networkData.links.length}</div>
                        <div class="text-gray-600">Conexões</div>
                    </div>
                </div>
                <button onclick="document.getElementById('interacoes360Btn').click()" class="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm">
                    Ver Análise Detalhada
                </button>
            </div>
        `;

        container.appendChild(fallbackDiv);
        console.log('Visualização de fallback criada');
    }
    }

    // Função auxiliar para gerar dados da rede
    function generateNetworkData() {
        const networkData = {
            nodes: [],
            links: []
        };

        // Gerar nós para diferentes especialidades
        const specialties = ['Cardiologia', 'Neurologia', 'Dermatologia', 'Ortopedia', 'Pediatria', 'Psiquiatria', 'Oftalmologia', 'Endocrinologia'];
        const colors = ['#4299E1', '#F56565', '#48BB78', '#ED8936', '#9F7AEA', '#667EEA', '#F687B3', '#38B2AC'];

        // Criar nós
        for (let i = 0; i < 100; i++) {
            const specialtyIndex = Math.floor(Math.random() * specialties.length);
            networkData.nodes.push({
                id: `node${i}`,
                name: `Dr. ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}. ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
                specialty: specialties[specialtyIndex],
                connections: Math.floor(Math.random() * 30) + 5,
                color: colors[specialtyIndex]
            });
        }

        // Criar links (conexões entre nós)
        for (let i = 0; i < 250; i++) {
            const source = networkData.nodes[Math.floor(Math.random() * networkData.nodes.length)].id;
            let target;
            do {
                target = networkData.nodes[Math.floor(Math.random() * networkData.nodes.length)].id;
            } while (source === target);

            networkData.links.push({
                source,
                target,
                strength: Math.random() * 0.9 + 0.1
            });
        }

        return networkData;
    }

    // Função para inicializar a visualização da rede (modal)
    function initializeNetworkVisualization(viewType = 'force', customContainer = null) {
        console.log('Inicializando visualização da rede com tipo:', viewType);
        const container = customContainer || document.getElementById('networkVisualization');
        console.log('Container de visualização da rede:', container);
        if (!container) return;

        // Limpar o container
        container.innerHTML = '';

        // Usar os dados gerados pela função auxiliar
        const networkData = generateNetworkData();

        // Criar visualização baseada no tipo selecionado
        if (viewType === 'force') {
            // Usar Force-Graph para visualização de força
            const Graph = ForceGraph()
                .graphData(networkData)
                .nodeId('id')
                .nodeVal('connections')
                .nodeLabel(node => `${node.name}<br>Especialidade: ${node.specialty}<br>Conexões: ${node.connections}`)
                .nodeColor('color')
                .linkWidth(link => link.strength * 3)
                .linkColor(() => 'rgba(0, 122, 255, 0.2)')
                .width(container.clientWidth)
                .height(container.clientHeight);

            // Renderizar o gráfico
            Graph(container);
        } else if (viewType === 'd3-force') {
            // Implementar diagrama de força com D3.js nativo
            const width = container.clientWidth;
            const height = container.clientHeight;

            const svg = d3.select(container)
                .append('svg')
                .attr('width', width)
                .attr('height', height);

            // Criar simulação de força
            const simulation = d3.forceSimulation(networkData.nodes)
                .force('link', d3.forceLink(networkData.links).id(d => d.id).distance(80))
                .force('charge', d3.forceManyBody().strength(-300))
                .force('center', d3.forceCenter(width / 2, height / 2))
                .force('collision', d3.forceCollide().radius(d => Math.sqrt(d.connections) * 2 + 5));

            // Criar links
            const link = svg.append('g')
                .attr('class', 'links')
                .selectAll('line')
                .data(networkData.links)
                .enter()
                .append('line')
                .attr('stroke', 'rgba(0, 122, 255, 0.3)')
                .attr('stroke-width', d => d.strength * 3)
                .attr('stroke-opacity', 0.6);

            // Criar nós
            const node = svg.append('g')
                .attr('class', 'nodes')
                .selectAll('circle')
                .data(networkData.nodes)
                .enter()
                .append('circle')
                .attr('r', d => Math.sqrt(d.connections) * 2)
                .attr('fill', d => d.color)
                .attr('stroke', '#fff')
                .attr('stroke-width', 2)
                .call(d3.drag()
                    .on('start', dragstarted)
                    .on('drag', dragged)
                    .on('end', dragended));

            // Adicionar tooltips
            node.append('title')
                .text(d => `${d.name}\nEspecialidade: ${d.specialty}\nConexões: ${d.connections}`);

            // Adicionar labels para nós importantes (com muitas conexões)
            const labels = svg.append('g')
                .attr('class', 'labels')
                .selectAll('text')
                .data(networkData.nodes.filter(d => d.connections > 20))
                .enter()
                .append('text')
                .attr('text-anchor', 'middle')
                .attr('dy', '.35em')
                .attr('font-size', '10px')
                .attr('font-weight', 'bold')
                .attr('fill', '#333')
                .text(d => d.name.split(' ')[0]); // Mostrar apenas o primeiro nome

            // Atualizar posições na simulação
            simulation.on('tick', () => {
                link
                    .attr('x1', d => d.source.x)
                    .attr('y1', d => d.source.y)
                    .attr('x2', d => d.target.x)
                    .attr('y2', d => d.target.y);

                node
                    .attr('cx', d => d.x)
                    .attr('cy', d => d.y);

                labels
                    .attr('x', d => d.x)
                    .attr('y', d => d.y);
            });

            // Funções de drag
            function dragstarted(event, d) {
                if (!event.active) simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            }

            function dragged(event, d) {
                d.fx = event.x;
                d.fy = event.y;
            }

            function dragended(event, d) {
                if (!event.active) simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            }

            // Adicionar zoom e pan
            const zoom = d3.zoom()
                .scaleExtent([0.1, 4])
                .on('zoom', (event) => {
                    svg.selectAll('g').attr('transform', event.transform);
                });

            svg.call(zoom);

            // Adicionar controles de zoom
            const controls = d3.select(container)
                .append('div')
                .style('position', 'absolute')
                .style('top', '10px')
                .style('right', '10px')
                .style('background', 'rgba(255, 255, 255, 0.9)')
                .style('padding', '5px')
                .style('border-radius', '5px')
                .style('border', '1px solid #ddd');

            controls.append('button')
                .text('Reset Zoom')
                .style('margin', '2px')
                .style('padding', '2px 8px')
                .style('font-size', '10px')
                .on('click', () => {
                    svg.transition().duration(750).call(
                        zoom.transform,
                        d3.zoomIdentity
                    );
                });

            controls.append('button')
                .text('Pausar/Retomar')
                .style('margin', '2px')
                .style('padding', '2px 8px')
                .style('font-size', '10px')
                .on('click', () => {
                    if (simulation.alpha() > 0) {
                        simulation.stop();
                    } else {
                        simulation.restart();
                    }
                });

        } else if (viewType === 'radial') {
            // Implementar visualização radial com D3.js
            const width = container.clientWidth;
            const height = container.clientHeight;

            const svg = d3.select(container)
                .append('svg')
                .attr('width', width)
                .attr('height', height)
                .append('g')
                .attr('transform', `translate(${width / 2}, ${height / 2})`);

            // Criar layout radial
            const radius = Math.min(width, height) / 2 - 50;

            // Agrupar nós por especialidade
            const specialtyGroups = {};
            networkData.nodes.forEach(node => {
                if (!specialtyGroups[node.specialty]) {
                    specialtyGroups[node.specialty] = [];
                }
                specialtyGroups[node.specialty].push(node);
            });

            // Posicionar nós em círculos concêntricos por especialidade
            let angleOffset = 0;
            Object.keys(specialtyGroups).forEach((specialty, i) => {
                const nodes = specialtyGroups[specialty];
                const angleStep = (2 * Math.PI) / nodes.length;
                const specialtyRadius = radius * (0.4 + (i * 0.1));

                nodes.forEach((node, j) => {
                    const angle = angleOffset + (j * angleStep);
                    node.x = specialtyRadius * Math.cos(angle);
                    node.y = specialtyRadius * Math.sin(angle);
                });

                angleOffset += Math.PI / 4;
            });

            // Desenhar links
            svg.selectAll('line')
                .data(networkData.links)
                .enter()
                .append('line')
                .attr('x1', d => networkData.nodes.find(n => n.id === d.source).x)
                .attr('y1', d => networkData.nodes.find(n => n.id === d.source).y)
                .attr('x2', d => networkData.nodes.find(n => n.id === d.target).x)
                .attr('y2', d => networkData.nodes.find(n => n.id === d.target).y)
                .attr('stroke', 'rgba(0, 122, 255, 0.2)')
                .attr('stroke-width', d => d.strength * 2);

            // Desenhar nós
            svg.selectAll('circle')
                .data(networkData.nodes)
                .enter()
                .append('circle')
                .attr('cx', d => d.x)
                .attr('cy', d => d.y)
                .attr('r', d => Math.sqrt(d.connections) * 1.5)
                .attr('fill', d => d.color)
                .append('title')
                .text(d => `${d.name}\nEspecialidade: ${d.specialty}\nConexões: ${d.connections}`);
        } else if (viewType === 'hierarchical') {
            // Implementar visualização hierárquica com D3.js
            const width = container.clientWidth;
            const height = container.clientHeight;

            const svg = d3.select(container)
                .append('svg')
                .attr('width', width)
                .attr('height', height)
                .append('g')
                .attr('transform', `translate(50, 50)`);

            // Criar estrutura hierárquica
            const specialtyGroups = {};
            networkData.nodes.forEach(node => {
                if (!specialtyGroups[node.specialty]) {
                    specialtyGroups[node.specialty] = [];
                }
                specialtyGroups[node.specialty].push(node);
            });

            // Criar dados hierárquicos
            const hierarchyData = {
                name: "Rede Médica",
                children: Object.keys(specialtyGroups).map(specialty => ({
                    name: specialty,
                    children: specialtyGroups[specialty].map(node => ({
                        name: node.name,
                        value: node.connections,
                        color: node.color,
                        id: node.id
                    }))
                }))
            };

            // Criar layout de cluster
            const treeLayout = d3.tree()
                .size([height - 100, width - 200]);

            const root = d3.hierarchy(hierarchyData);
            treeLayout(root);

            // Desenhar links
            svg.selectAll('path')
                .data(root.links())
                .enter()
                .append('path')
                .attr('d', d3.linkHorizontal()
                    .x(d => d.y)
                    .y(d => d.x))
                .attr('fill', 'none')
                .attr('stroke', 'rgba(0, 122, 255, 0.4)')
                .attr('stroke-width', 1.5);

            // Desenhar nós
            const nodes = svg.selectAll('g')
                .data(root.descendants())
                .enter()
                .append('g')
                .attr('transform', d => `translate(${d.y},${d.x})`);

            nodes.append('circle')
                .attr('r', d => d.depth === 0 ? 10 : d.depth === 1 ? 8 : Math.sqrt(d.data.value || 5) * 1.5)
                .attr('fill', d => d.depth === 0 ? '#4A5568' : d.depth === 1 ? '#2D3748' : d.data.color || '#A0AEC0');

            nodes.append('text')
                .attr('dy', d => d.depth === 0 ? -15 : d.depth === 1 ? -12 : 4)
                .attr('x', d => d.depth === 0 ? 0 : d.depth === 1 ? -8 : 8)
                .attr('text-anchor', d => d.depth === 0 ? 'middle' : d.depth === 1 ? 'end' : 'start')
                .attr('font-size', d => d.depth === 0 ? 14 : d.depth === 1 ? 12 : 10)
                .text(d => d.depth < 2 ? d.data.name : '');
        }
    }

    // Função para inicializar a matriz de conexões
    function initializeConnectionMatrix(filter = 'all') {
        // Usar o ID diretamente ou encontrar o container por uma classe mais simples
        const containerParent = document.getElementById('connectionMatrixFilter')?.closest('div');
        const container = containerParent?.querySelector('[class*="h-[300px]"]') || containerParent?.querySelector('div:nth-child(2)');
        console.log('Container de matriz de conexões:', container);
        if (!container) return;

        // Limpar o container
        container.innerHTML = '';

        // Dados de exemplo para a matriz
        let specialties = ['Cardiologia', 'Neurologia', 'Dermatologia', 'Ortopedia', 'Pediatria', 'Psiquiatria', 'Oftalmologia', 'Endocrinologia'];

        // Filtrar especialidades se necessário
        if (filter === 'top10') {
            specialties = specialties.slice(0, 5);
        }

        // Criar matriz de conexões
        const matrix = [];
        for (let i = 0; i < specialties.length; i++) {
            matrix[i] = [];
            for (let j = 0; j < specialties.length; j++) {
                // Mais conexões dentro da mesma especialidade
                if (i === j) {
                    matrix[i][j] = Math.floor(Math.random() * 50) + 50;
                } else {
                    matrix[i][j] = Math.floor(Math.random() * 40) + 10;
                }
            }
        }

        // Configurar visualização
        const width = container.clientWidth;
        const height = container.clientHeight;
        const padding = 50;

        const svg = d3.select(container)
            .append('svg')
            .attr('width', width)
            .attr('height', height);

        // Escala de cores
        const colorScale = d3.scaleLinear()
            .domain([0, 100])
            .range(['#E2E8F0', '#3182CE']);

        // Tamanho da célula
        const cellSize = Math.min((width - padding * 2) / specialties.length, (height - padding * 2) / specialties.length);

        // Criar células da matriz
        const cells = svg.append('g')
            .attr('transform', `translate(${padding}, ${padding})`)
            .selectAll('rect')
            .data(matrix.flatMap((row, i) => row.map((value, j) => ({ value, i, j }))))
            .enter()
            .append('rect')
            .attr('x', d => d.j * cellSize)
            .attr('y', d => d.i * cellSize)
            .attr('width', cellSize)
            .attr('height', cellSize)
            .attr('fill', d => colorScale(d.value))
            .attr('stroke', '#E2E8F0')
            .attr('stroke-width', 1)
            .append('title')
            .text(d => `${specialties[d.i]} → ${specialties[d.j]}: ${d.value} conexões`);

        // Adicionar rótulos para linhas
        svg.append('g')
            .attr('transform', `translate(${padding - 5}, ${padding})`)
            .selectAll('text')
            .data(specialties)
            .enter()
            .append('text')
            .attr('y', (d, i) => i * cellSize + cellSize / 2)
            .attr('text-anchor', 'end')
            .attr('dominant-baseline', 'middle')
            .attr('font-size', '10px')
            .text(d => d);

        // Adicionar rótulos para colunas
        svg.append('g')
            .attr('transform', `translate(${padding}, ${padding - 5})`)
            .selectAll('text')
            .data(specialties)
            .enter()
            .append('text')
            .attr('x', (d, i) => i * cellSize + cellSize / 2)
            .attr('text-anchor', 'middle')
            .attr('font-size', '10px')
            .attr('transform', (d, i) => `rotate(-45, ${i * cellSize + cellSize / 2}, 0)`)
            .text(d => d);
    }

    // Função para inicializar a distribuição de conexões
    function initializeConnectionDistribution(viewType = 'specialty') {
        // Usar o ID diretamente ou encontrar o container por uma classe mais simples
        const containerParent = document.getElementById('connectionDistributionView')?.closest('div');
        const container = containerParent?.querySelector('[class*="h-[300px]"]') || containerParent?.querySelector('div:nth-child(2)');
        console.log('Container de distribuição de conexões:', container);
        if (!container) return;

        // Limpar o container
        container.innerHTML = '';

        // Dados de exemplo para a distribuição
        let categories, values;

        if (viewType === 'specialty') {
            categories = ['Cardiologia', 'Neurologia', 'Dermatologia', 'Ortopedia', 'Pediatria', 'Psiquiatria', 'Oftalmologia', 'Endocrinologia'];
            values = [42, 38, 35, 30, 28, 25, 22, 20];
        } else if (viewType === 'region') {
            categories = ['Sudeste', 'Sul', 'Nordeste', 'Centro-Oeste', 'Norte'];
            values = [45, 38, 32, 28, 25];
        } else if (viewType === 'age') {
            categories = ['25-30', '31-35', '36-40', '41-45', '46-50', '51-55', '56-60', '61+'];
            values = [48, 42, 38, 35, 30, 25, 20, 15];
        }

        // Criar gráfico com ApexCharts
        const options = {
            series: [{
                name: 'Conexões Médias',
                data: values
            }],
            chart: {
                type: 'bar',
                height: container.clientHeight,
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    borderRadius: 4,
                    horizontal: false,
                    columnWidth: '70%'
                }
            },
            dataLabels: {
                enabled: false
            },
            colors: ['#3182CE'],
            xaxis: {
                categories: categories,
                labels: {
                    style: {
                        fontSize: '10px'
                    },
                    rotate: -45,
                    rotateAlways: false
                }
            },
            yaxis: {
                title: {
                    text: 'Conexões Médias',
                    style: {
                        fontSize: '12px'
                    }
                }
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + " conexões";
                    }
                }
            }
        };

        const chart = new ApexCharts(container, options);
        chart.render();
    }

    // Funções para a aba de Padrões de Engajamento

    // Inicializar métricas de engajamento
    function initializeEngagementMetrics() {
        // Esta função apenas atualiza os valores estáticos nas métricas
        // Os valores já estão definidos no HTML
    }

    // Inicializar gráfico de tendências de engajamento
    function initializeEngagementTrends() {
        console.log('Inicializando tendências de engajamento');
        // Selecionar o container correto dentro da aba de padrões de engajamento
        const tabContent = document.getElementById('engagement-patterns');
        // Encontrar o primeiro div com altura fixa dentro da aba
        const container = tabContent?.querySelector('[class*="h-[300px]"]') || tabContent?.querySelector('div > div:first-child');
        console.log('Container de tendências de engajamento:', container);
        if (!container) return;

        // Limpar o container
        container.innerHTML = '';

        // Dados de exemplo para tendências de engajamento
        const months = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
        const engagementRate = [18, 19, 20, 21, 22, 23, 23, 24, 25, 26, 27, 28];
        const timeOnFeed = [3.2, 3.4, 3.5, 3.7, 3.8, 3.9, 4.0, 4.1, 4.2, 4.3, 4.4, 4.5];
        const visitFrequency = [2.8, 3.0, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9, 4.0];

        // Criar gráfico com ApexCharts
        const options = {
            series: [
                {
                    name: 'Taxa de Engajamento (%)',
                    type: 'line',
                    data: engagementRate
                },
                {
                    name: 'Tempo no Feed (min)',
                    type: 'line',
                    data: timeOnFeed.map(val => val * 10) // Escalar para visualização
                },
                {
                    name: 'Frequência de Visitas',
                    type: 'line',
                    data: visitFrequency.map(val => val * 7) // Escalar para visualização
                }
            ],
            chart: {
                height: container.clientHeight,
                type: 'line',
                toolbar: {
                    show: false
                },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800
                }
            },
            stroke: {
                width: [3, 3, 3],
                curve: 'smooth'
            },
            colors: ['#3182CE', '#48BB78', '#ED8936'],
            xaxis: {
                categories: months
            },
            yaxis: [
                {
                    title: {
                        text: 'Taxa de Engajamento (%)',
                        style: {
                            fontSize: '12px'
                        }
                    },
                    min: 0,
                    max: 40
                },
                {
                    show: false,
                    min: 0,
                    max: 60
                },
                {
                    show: false,
                    min: 0,
                    max: 60
                }
            ],
            markers: {
                size: 4,
                strokeWidth: 0,
                hover: {
                    size: 6
                }
            },
            tooltip: {
                y: {
                    formatter: function(val, { seriesIndex }) {
                        if (seriesIndex === 0) return val + '%';
                        if (seriesIndex === 1) return (val / 10).toFixed(1) + ' min';
                        if (seriesIndex === 2) return (val / 7).toFixed(1) + ' visitas/semana';
                        return val;
                    }
                }
            },
            legend: {
                position: 'top'
            }
        };

        const chart = new ApexCharts(container, options);
        chart.render();
    }

    // Inicializar análise de conteúdo
    function initializeContentAnalysis() {
        console.log('Inicializando análise de conteúdo');
        // Engajamento por Tipo de Conteúdo
        const contentTypeParent = document.getElementById('contentTypeFilter')?.closest('div');
        const contentTypeContainer = contentTypeParent?.querySelector('[class*="h-[300px]"]') || contentTypeParent?.querySelector('div:nth-child(2)');
        console.log('Container de tipo de conteúdo:', contentTypeContainer);
        if (contentTypeContainer) {
            // Limpar o container
            contentTypeContainer.innerHTML = '';

            // Dados de exemplo
            const contentTypes = ['Casos Clínicos', 'Atualizações Científicas', 'Imagens', 'Vídeos', 'Texto', 'Gestão', 'Eventos', 'Perguntas'];
            const engagementRates = [32, 28, 23, 22, 14, 18, 20, 16];

            // Criar gráfico com ApexCharts
            const options = {
                series: [{
                    name: 'Taxa de Engajamento',
                    data: engagementRates
                }],
                chart: {
                    type: 'bar',
                    height: contentTypeContainer.clientHeight,
                    toolbar: {
                        show: false
                    }
                },
                plotOptions: {
                    bar: {
                        borderRadius: 4,
                        horizontal: true,
                        distributed: true,
                        dataLabels: {
                            position: 'top'
                        }
                    }
                },
                dataLabels: {
                    enabled: true,
                    formatter: function(val) {
                        return val + '%';
                    },
                    offsetX: 30,
                    style: {
                        fontSize: '12px',
                        colors: ['#304758']
                    }
                },
                colors: ['#3182CE', '#4299E1', '#63B3ED', '#90CDF4', '#BEE3F8', '#EBF8FF', '#E6FFFA', '#B2F5EA'],
                xaxis: {
                    categories: contentTypes,
                    labels: {
                        style: {
                            fontSize: '12px'
                        }
                    }
                },
                yaxis: {
                    labels: {
                        style: {
                            fontSize: '12px'
                        }
                    }
                }
            };

            const chart = new ApexCharts(contentTypeContainer, options);
            chart.render();
        }

        // Tópicos Populares
        const topicsParent = document.getElementById('topicsTimeframe')?.closest('div');
        const topicsContainer = topicsParent?.querySelector('[class*="h-[300px]"]') || topicsParent?.querySelector('div:nth-child(2)');
        console.log('Container de tópicos populares:', topicsContainer);
        if (topicsContainer) {
            // Limpar o container
            topicsContainer.innerHTML = '';

            // Dados de exemplo
            const topics = ['COVID-19', 'Diabetes', 'Saúde Mental', 'Cardiologia', 'Oncologia', 'Pediatria', 'Gestão', 'Tecnologia'];
            const interactions = [1250, 980, 850, 720, 650, 580, 520, 480];

            // Criar gráfico com ApexCharts
            const options = {
                series: [{
                    name: 'Interações',
                    data: interactions
                }],
                chart: {
                    type: 'bar',
                    height: topicsContainer.clientHeight,
                    toolbar: {
                        show: false
                    }
                },
                plotOptions: {
                    bar: {
                        borderRadius: 4,
                        horizontal: false,
                        columnWidth: '60%'
                    }
                },
                dataLabels: {
                    enabled: false
                },
                colors: ['#805AD5'],
                xaxis: {
                    categories: topics,
                    labels: {
                        style: {
                            fontSize: '12px'
                        },
                        rotate: -45,
                        rotateAlways: false
                    }
                },
                yaxis: {
                    title: {
                        text: 'Total de Interações',
                        style: {
                            fontSize: '12px'
                        }
                    }
                }
            };

            const chart = new ApexCharts(topicsContainer, options);
            chart.render();
        }
    }

    // Inicializar padrões temporais
    function initializeTemporalPatterns() {
        console.log('Inicializando padrões temporais');
        // Horários de Maior Atividade
        const activityTimeParent = document.getElementById('activityTimeView')?.closest('div');
        const activityTimeContainer = activityTimeParent?.querySelector('[class*="h-[300px]"]') || activityTimeParent?.querySelector('div:nth-child(2)');
        console.log('Container de horários de atividade:', activityTimeContainer);
        if (activityTimeContainer) {
            // Limpar o container
            activityTimeContainer.innerHTML = '';

            // Dados de exemplo
            const hours = ['00h', '01h', '02h', '03h', '04h', '05h', '06h', '07h', '08h', '09h', '10h', '11h', '12h', '13h', '14h', '15h', '16h', '17h', '18h', '19h', '20h', '21h', '22h', '23h'];
            const activity = [2, 1, 1, 0, 0, 1, 3, 8, 15, 22, 25, 28, 32, 30, 25, 22, 20, 18, 15, 18, 25, 32, 20, 8];

            // Criar gráfico com ApexCharts
            const options = {
                series: [{
                    name: 'Atividade (%)',
                    data: activity
                }],
                chart: {
                    type: 'area',
                    height: activityTimeContainer.clientHeight,
                    toolbar: {
                        show: false
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 2
                },
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.3,
                        stops: [0, 90, 100]
                    }
                },
                colors: ['#3182CE'],
                xaxis: {
                    categories: hours,
                    labels: {
                        style: {
                            fontSize: '10px'
                        },
                        rotate: -45,
                        rotateAlways: false
                    }
                },
                yaxis: {
                    title: {
                        text: 'Atividade (%)',
                        style: {
                            fontSize: '12px'
                        }
                    }
                },
                markers: {
                    size: 3,
                    strokeWidth: 0,
                    hover: {
                        size: 5
                    }
                }
            };

            const chart = new ApexCharts(activityTimeContainer, options);
            chart.render();
        }

        // Mapa de Calor de Engajamento
        const heatmapParent = document.getElementById('heatmapType')?.closest('div');
        const heatmapContainer = heatmapParent?.querySelector('[class*="h-[300px]"]') || heatmapParent?.querySelector('div:nth-child(2)');
        console.log('Container de mapa de calor:', heatmapContainer);
        if (heatmapContainer) {
            // Limpar o container
            heatmapContainer.innerHTML = '';

            // Dados de exemplo
            const days = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
            const timeSlots = ['00-04h', '04-08h', '08-12h', '12-16h', '16-20h', '20-24h'];

            // Gerar dados para o mapa de calor
            const generateHeatmapData = () => {
                const data = [];
                for (let i = 0; i < days.length; i++) {
                    for (let j = 0; j < timeSlots.length; j++) {
                        // Valores mais altos durante dias úteis e horários comerciais/noite
                        let value;
                        if (i === 0 || i === 6) { // Fim de semana
                            value = Math.floor(Math.random() * 30) + 10;
                        } else { // Dias úteis
                            if (j === 0 || j === 1) { // Madrugada/manhã cedo
                                value = Math.floor(Math.random() * 20) + 5;
                            } else if (j === 2 || j === 3) { // Horário comercial
                                value = Math.floor(Math.random() * 40) + 40;
                            } else { // Noite
                                value = Math.floor(Math.random() * 30) + 50;
                            }
                        }
                        data.push({
                            x: days[i],
                            y: timeSlots[j],
                            value: value
                        });
                    }
                }
                return data;
            };

            // Criar gráfico com ApexCharts
            const options = {
                series: [{
                    name: 'Engajamento',
                    data: generateHeatmapData()
                }],
                chart: {
                    height: heatmapContainer.clientHeight,
                    type: 'heatmap',
                    toolbar: {
                        show: false
                    }
                },
                dataLabels: {
                    enabled: false
                },
                colors: ['#3182CE'],
                xaxis: {
                    categories: days,
                    labels: {
                        style: {
                            fontSize: '12px'
                        }
                    }
                },
                yaxis: {
                    categories: timeSlots,
                    labels: {
                        style: {
                            fontSize: '12px'
                        }
                    }
                },
                plotOptions: {
                    heatmap: {
                        shadeIntensity: 0.5,
                        radius: 0,
                        colorScale: {
                            ranges: [
                                {
                                    from: 0,
                                    to: 20,
                                    color: '#BBDEFB',
                                    name: 'Baixo'
                                },
                                {
                                    from: 21,
                                    to: 40,
                                    color: '#64B5F6',
                                    name: 'Médio'
                                },
                                {
                                    from: 41,
                                    to: 60,
                                    color: '#2196F3',
                                    name: 'Alto'
                                },
                                {
                                    from: 61,
                                    to: 100,
                                    color: '#1565C0',
                                    name: 'Muito Alto'
                                }
                            ]
                        }
                    }
                },
                tooltip: {
                    y: {
                        formatter: function(val) {
                            return val + '% de atividade';
                        }
                    }
                }
            };

            const chart = new ApexCharts(heatmapContainer, options);
            chart.render();
        }
    }

    // Funções para a aba de Análise de Influência

    // Inicializar mapa de influência
    function initializeInfluenceMap() {
        console.log('Inicializando mapa de influência');
        const container = findVisualizationContainer('influenceMapFilter', 'h-[400px]');
        console.log('Container de mapa de influência:', container);
        if (!container) {
            console.error('Container de mapa de influência não encontrado');
            return;
        }

        // Limpar o container
        container.innerHTML = '';

        // Dados de exemplo para o mapa de influência
        const networkData = {
            nodes: [],
            links: []
        };

        // Gerar nós para diferentes especialidades com diferentes níveis de influência
        const specialties = ['Cardiologia', 'Neurologia', 'Dermatologia', 'Ortopedia', 'Pediatria', 'Psiquiatria', 'Oftalmologia', 'Endocrinologia'];
        const colors = ['#4299E1', '#F56565', '#48BB78', '#ED8936', '#9F7AEA', '#667EEA', '#F687B3', '#38B2AC'];

        // Criar nós com diferentes níveis de influência
        for (let i = 0; i < 80; i++) {
            const specialtyIndex = Math.floor(Math.random() * specialties.length);
            // Alguns nós têm influência muito maior
            const isInfluencer = Math.random() < 0.15;
            const influenceScore = isInfluencer ?
                Math.random() * 0.3 + 0.7 : // 0.7 a 1.0 para influenciadores
                Math.random() * 0.6 + 0.1;  // 0.1 a 0.7 para outros

            networkData.nodes.push({
                id: `node${i}`,
                name: `Dr. ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}. ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
                specialty: specialties[specialtyIndex],
                influence: influenceScore,
                color: colors[specialtyIndex]
            });
        }

        // Criar links (conexões entre nós) - mais conexões para nós influentes
        for (let i = 0; i < networkData.nodes.length; i++) {
            const node = networkData.nodes[i];
            // Número de conexões baseado na influência
            const numConnections = Math.floor(node.influence * 30) + 2;

            for (let j = 0; j < numConnections; j++) {
                let targetIndex;
                do {
                    // Use deterministic calculation instead of random
                    targetIndex = (i + j + 1) % networkData.nodes.length;
                } while (targetIndex === i);

                networkData.links.push({
                    source: node.id,
                    target: networkData.nodes[targetIndex].id,
                    strength: ((i + j) % 10) / 20 + 0.1 // Deterministic strength calculation
                });
            }
        }

        // Criar visualização com ForceGraph
        const Graph = ForceGraph()
            .graphData(networkData)
            .nodeId('id')
            .nodeVal(node => Math.pow(node.influence * 15, 2)) // Tamanho baseado na influência
            .nodeLabel(node => `${node.name}<br>Especialidade: ${node.specialty}<br>Score de Influência: ${node.influence.toFixed(2)}`)
            .nodeColor('color')
            .linkWidth(link => link.strength * 2)
            .linkColor(() => 'rgba(0, 122, 255, 0.15)')
            .width(container.clientWidth)
            .height(container.clientHeight);

        // Renderizar o gráfico
        Graph(container);
    }

    // Inicializar métricas de influência
    function initializeInfluenceMetrics() {
        console.log('Inicializando métricas de influência');
        // Esta função atualiza os valores estáticos nas métricas

        // Encontrar a seção de métricas de influência
        const metricsSection = document.querySelector('#influence-analysis .bg-white:nth-child(1) .space-y-4 > div:nth-child(1)');
        console.log('Seção de métricas de influência:', metricsSection);

        if (metricsSection) {
            // Atualizar valores de métricas se necessário
            const metrics = [
                { label: 'Influenciadores', value: '12%', change: '+2.3%' },
                { label: 'Alcance Médio', value: '215', change: '+15' },
                { label: 'Engajamento', value: '24%', change: '+1.8%' },
                { label: 'Impacto de Conteúdo', value: '3.7', change: '+0.4' }
            ];

            // Encontrar todos os elementos de métrica
            const metricElements = metricsSection.querySelectorAll('.bg-blue-50');
            console.log('Elementos de métrica encontrados:', metricElements.length);

            // Atualizar cada elemento de métrica
            metricElements.forEach((element, index) => {
                if (index < metrics.length) {
                    const valueElement = element.querySelector('.text-3xl');
                    const changeElement = element.querySelector('.font-medium');

                    if (valueElement) {
                        valueElement.textContent = metrics[index].value;
                    }

                    if (changeElement) {
                        changeElement.textContent = metrics[index].change;
                    }
                }
            });
        }
    }

    // Inicializar análise de alcance
    function initializeReachAnalysis() {
        console.log('Inicializando análise de alcance');
        const container = findVisualizationContainer('reachTimeframe', 'h-[300px]');
        console.log('Container de análise de alcance:', container);
        if (!container) return;

        // Limpar o container
        container.innerHTML = '';

        // Dados de exemplo para alcance por especialidade
        const specialties = ['Cardiologia', 'Neurologia', 'Dermatologia', 'Ortopedia', 'Pediatria', 'Psiquiatria', 'Oftalmologia', 'Endocrinologia'];
        const reach = [245, 210, 195, 180, 165, 150, 140, 130];
        const interdisciplinaryReach = [42, 38, 35, 30, 28, 25, 22, 20]; // Percentual

        // Criar gráfico com ApexCharts
        const options = {
            series: [
                {
                    name: 'Alcance Médio',
                    type: 'column',
                    data: reach
                },
                {
                    name: 'Alcance Interdisciplinar (%)',
                    type: 'line',
                    data: interdisciplinaryReach
                }
            ],
            chart: {
                height: container.clientHeight,
                type: 'line',
                toolbar: {
                    show: false
                }
            },
            stroke: {
                width: [0, 3],
                curve: 'smooth'
            },
            plotOptions: {
                bar: {
                    borderRadius: 4,
                    columnWidth: '60%'
                }
            },
            colors: ['#3182CE', '#F56565'],
            xaxis: {
                categories: specialties,
                labels: {
                    style: {
                        fontSize: '10px'
                    },
                    rotate: -45,
                    rotateAlways: false
                }
            },
            yaxis: [
                {
                    title: {
                        text: 'Alcance Médio (visualizações)',
                        style: {
                            fontSize: '12px'
                        }
                    },
                    min: 0
                },
                {
                    opposite: true,
                    title: {
                        text: 'Alcance Interdisciplinar (%)',
                        style: {
                            fontSize: '12px'
                        }
                    },
                    min: 0,
                    max: 50
                }
            ],
            dataLabels: {
                enabled: false
            },
            legend: {
                position: 'top'
            }
        };

        const chart = new ApexCharts(container, options);
        chart.render();
    }

    // Inicializar impacto de conteúdo
    function initializeContentImpact() {
        console.log('Inicializando impacto de conteúdo');
        const container = findVisualizationContainer('contentImpactFilter', 'h-[300px]');
        console.log('Container de impacto de conteúdo:', container);
        if (!container) return;

        // Limpar o container
        container.innerHTML = '';

        // Dados de exemplo para impacto de conteúdo
        const contentTypes = ['Casos Clínicos', 'Científico', 'Imagens', 'Vídeos', 'Gestão', 'Eventos'];

        // Criar dados para o gráfico de dispersão
        const generateScatterData = () => {
            const data = [];

            // Casos Clínicos - alto engajamento e alcance
            data.push({
                x: 320, // Alcance
                y: 32,  // Engajamento (%)
                z: 250, // Tamanho (total de interações)
                name: 'Casos Clínicos',
                fillColor: '#3182CE'
            });

            // Científico - alto alcance, médio engajamento
            data.push({
                x: 280,
                y: 28,
                z: 200,
                name: 'Científico',
                fillColor: '#4299E1'
            });

            // Imagens - médio alcance, médio engajamento
            data.push({
                x: 230,
                y: 23,
                z: 180,
                name: 'Imagens',
                fillColor: '#63B3ED'
            });

            // Vídeos - médio alcance, médio engajamento
            data.push({
                x: 220,
                y: 22,
                z: 170,
                name: 'Vídeos',
                fillColor: '#90CDF4'
            });

            // Gestão - baixo alcance, baixo engajamento, mas crescendo
            data.push({
                x: 180,
                y: 18,
                z: 150,
                name: 'Gestão',
                fillColor: '#BEE3F8'
            });

            // Eventos - médio alcance, baixo engajamento
            data.push({
                x: 200,
                y: 20,
                z: 160,
                name: 'Eventos',
                fillColor: '#EBF8FF'
            });

            return data;
        };

        // Criar gráfico com ApexCharts
        const options = {
            series: [{
                name: 'Impacto de Conteúdo',
                data: generateScatterData()
            }],
            chart: {
                height: container.clientHeight,
                type: 'bubble',
                toolbar: {
                    show: false
                }
            },
            xaxis: {
                title: {
                    text: 'Alcance Médio (visualizações)',
                    style: {
                        fontSize: '12px'
                    }
                },
                min: 150,
                max: 350
            },
            yaxis: {
                title: {
                    text: 'Taxa de Engajamento (%)',
                    style: {
                        fontSize: '12px'
                    }
                },
                min: 15,
                max: 35
            },
            fill: {
                opacity: 0.8
            },
            tooltip: {
                theme: 'light',
                x: {
                    show: true,
                    formatter: function(val) {
                        return val + ' visualizações';
                    }
                },
                y: {
                    formatter: function(val) {
                        return val + '% de engajamento';
                    }
                },
                z: {
                    formatter: function(val) {
                        return 'Interações: ' + val;
                    }
                },
                marker: {
                    show: true
                }
            }
        };

        const chart = new ApexCharts(container, options);
        chart.render();
    }

    // Funções para a aba de Detecção de Comunidades

    // Inicializar visualização de comunidades
    function initializeCommunityVisualization(algorithm = 'louvain') {
        console.log('Inicializando visualização de comunidades com algoritmo:', algorithm);
        const container = findVisualizationContainer('communityAlgorithm', 'h-[400px]');
        console.log('Container de visualização de comunidades:', container);
        if (!container) {
            console.error('Container de visualização de comunidades não encontrado');
            return;
        }

        // Limpar o container
        container.innerHTML = '';

        // Dados de exemplo para comunidades
        const networkData = {
            nodes: [],
            links: []
        };

        // Definir comunidades com base no algoritmo selecionado
        let communities;

        if (algorithm === 'louvain') {
            // Algoritmo Louvain - 8 comunidades bem definidas
            communities = [
                { name: 'Comunidade 1', color: '#3182CE', specialty: 'Cardiologia', size: 30 },
                { name: 'Comunidade 2', color: '#E53E3E', specialty: 'Neurologia/Psiquiatria', size: 25 },
                { name: 'Comunidade 3', color: '#38A169', specialty: 'Dermatologia/Endocrinologia', size: 20 },
                { name: 'Comunidade 4', color: '#DD6B20', specialty: 'Ortopedia', size: 18 },
                { name: 'Comunidade 5', color: '#805AD5', specialty: 'Pediatria', size: 15 },
                { name: 'Comunidade 6', color: '#3182CE', specialty: 'Oftalmologia', size: 12 },
                { name: 'Comunidade 7', color: '#F56565', specialty: 'Telemedicina', size: 10 },
                { name: 'Comunidade 8', color: '#48BB78', specialty: 'Medicina de Família', size: 8 }
            ];
        }
        else if (algorithm === 'infomap') {
            // Algoritmo Infomap - 6 comunidades com distribuição diferente
            communities = [
                { name: 'Comunidade A', color: '#3182CE', specialty: 'Cardiologia/Cirurgia', size: 35 },
                { name: 'Comunidade B', color: '#E53E3E', specialty: 'Neurologia/Psiquiatria', size: 30 },
                { name: 'Comunidade C', color: '#38A169', specialty: 'Dermatologia/Endocrinologia', size: 25 },
                { name: 'Comunidade D', color: '#DD6B20', specialty: 'Ortopedia/Fisioterapia', size: 22 },
                { name: 'Comunidade E', color: '#805AD5', specialty: 'Pediatria/Neonatologia', size: 18 },
                { name: 'Comunidade F', color: '#F56565', specialty: 'Medicina Geral', size: 15 }
            ];
        }
        else if (algorithm === 'leiden') {
            // Algoritmo Leiden - 10 comunidades mais granulares
            communities = [
                { name: 'Grupo 1', color: '#3182CE', specialty: 'Cardiologia', size: 22 },
                { name: 'Grupo 2', color: '#E53E3E', specialty: 'Neurologia', size: 18 },
                { name: 'Grupo 3', color: '#38A169', specialty: 'Dermatologia', size: 16 },
                { name: 'Grupo 4', color: '#DD6B20', specialty: 'Ortopedia', size: 15 },
                { name: 'Grupo 5', color: '#805AD5', specialty: 'Pediatria', size: 14 },
                { name: 'Grupo 6', color: '#3182CE', specialty: 'Psiquiatria', size: 12 },
                { name: 'Grupo 7', color: '#F56565', specialty: 'Oftalmologia', size: 10 },
                { name: 'Grupo 8', color: '#48BB78', specialty: 'Endocrinologia', size: 9 },
                { name: 'Grupo 9', color: '#ED8936', specialty: 'Telemedicina', size: 8 },
                { name: 'Grupo 10', color: '#667EEA', specialty: 'Medicina de Família', size: 7 }
            ];
        }

        // Criar nós para cada comunidade
        let nodeId = 0;
        communities.forEach(community => {
            // Criar nós para esta comunidade
            for (let i = 0; i < community.size; i++) {
                networkData.nodes.push({
                    id: `node${nodeId}`,
                    name: `Dr. ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}. ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
                    community: community.name,
                    specialty: community.specialty,
                    color: community.color
                });
                nodeId++;
            }
        });

        // Criar links dentro de cada comunidade (alta densidade)
        networkData.nodes.forEach((node, i) => {
            // Encontrar outros nós da mesma comunidade
            const communityNodes = networkData.nodes.filter(n => n.community === node.community && n.id !== node.id);

            // Criar links com alta probabilidade dentro da comunidade
            communityNodes.forEach((targetNode, index) => {
                if ((i + index) % 10 < 7) { // 70% de chance de conexão dentro da comunidade (deterministic)
                    networkData.links.push({
                        source: node.id,
                        target: targetNode.id,
                        strength: ((i + index) % 10) / 20 + 0.5 // 0.5 a 1.0 (deterministic)
                    });
                }
            });

            // Criar alguns links entre comunidades (baixa densidade)
            const otherNodes = networkData.nodes.filter(n => n.community !== node.community);
            const numExternalLinks = i % 3; // 0 a 2 links externos (deterministic)

            for (let j = 0; j < numExternalLinks; j++) {
                const targetNode = otherNodes[j % otherNodes.length];
                networkData.links.push({
                    source: node.id,
                    target: targetNode.id,
                    strength: ((i + j) % 6) / 20 + 0.1 // 0.1 a 0.4 (deterministic)
                });
            }
        });

        // Criar visualização com ForceGraph
        const Graph = ForceGraph()
            .graphData(networkData)
            .nodeId('id')
            .nodeVal(8)
            .nodeLabel(node => `${node.name}<br>Comunidade: ${node.community}<br>Especialidade: ${node.specialty}`)
            .nodeColor('color')
            .linkWidth(link => link.strength * 3)
            .linkColor(link => {
                const sourceNode = networkData.nodes.find(n => n.id === link.source);
                const targetNode = networkData.nodes.find(n => n.id === link.target);
                return sourceNode.community === targetNode.community ?
                    sourceNode.color : 'rgba(150, 150, 150, 0.2)';
            })
            .width(container.clientWidth)
            .height(container.clientHeight);

        // Renderizar o gráfico
        Graph(container);
    }

    // Inicializar métricas de comunidades
    function initializeCommunityMetrics() {
        console.log('Inicializando métricas de comunidades');
        // Esta função apenas atualiza os valores estáticos nas métricas
        // Os valores já estão definidos no HTML

        // Distribuição de Tamanho - procurar dentro da div de métricas de comunidades
        const metricsSection = document.querySelector('#community-detection .bg-white:nth-child(1) .space-y-4 > div:nth-child(2)');
        const sizeDistContainer = metricsSection?.querySelector('div[class*="h-[150px]"]');
        console.log('Container de distribuição de tamanho:', sizeDistContainer);
        if (sizeDistContainer) {
            // Limpar o container
            sizeDistContainer.innerHTML = '';

            // Dados de exemplo
            const communities = ['Com 1', 'Com 2', 'Com 3', 'Com 4', 'Com 5', 'Com 6', 'Com 7', 'Com 8'];
            const sizes = [30, 25, 20, 18, 15, 12, 10, 8];

            // Criar gráfico com ApexCharts
            const options = {
                series: [{
                    name: 'Tamanho',
                    data: sizes
                }],
                chart: {
                    type: 'bar',
                    height: sizeDistContainer.clientHeight,
                    toolbar: {
                        show: false
                    }
                },
                plotOptions: {
                    bar: {
                        borderRadius: 4,
                        horizontal: false,
                        columnWidth: '60%',
                        distributed: true
                    }
                },
                dataLabels: {
                    enabled: false
                },
                colors: ['#3182CE', '#E53E3E', '#38A169', '#DD6B20', '#805AD5', '#3182CE', '#F56565', '#48BB78'],
                xaxis: {
                    categories: communities,
                    labels: {
                        style: {
                            fontSize: '10px'
                        }
                    }
                },
                yaxis: {
                    title: {
                        text: 'Número de Membros',
                        style: {
                            fontSize: '12px'
                        }
                    }
                },
                legend: {
                    show: false
                }
            };

            const chart = new ApexCharts(sizeDistContainer, options);
            chart.render();
        }
    }

    // Inicializar detalhes de comunidades
    function initializeCommunityDetails(filter = 'all') {
        console.log('Inicializando detalhes de comunidades com filtro:', filter);
        // Características das Comunidades
        const communityDetailsContainer = findVisualizationContainer('communityDetailFilter', 'h-[300px]');
        console.log('Container de detalhes de comunidades:', communityDetailsContainer);
        if (communityDetailsContainer) {
            // Limpar o container
            communityDetailsContainer.innerHTML = '';

            // Criar tabela de características
            const table = document.createElement('table');
            table.className = 'w-full text-xs';

            // Cabeçalho da tabela
            const thead = document.createElement('thead');
            thead.innerHTML = `
                <tr class="bg-gray-100 border-b border-gray-200">
                    <th class="p-2 text-left">Comunidade</th>
                    <th class="p-2 text-left">Especialidade</th>
                    <th class="p-2 text-center">Tamanho</th>
                    <th class="p-2 text-center">Densidade</th>
                    <th class="p-2 text-center">Coesão</th>
                </tr>
            `;
            table.appendChild(thead);

            // Corpo da tabela
            const tbody = document.createElement('tbody');

            // Dados de exemplo
            const communities = [
                { name: 'Comunidade 1', specialty: 'Cardiologia', size: 30, density: 0.68, cohesion: 0.75 },
                { name: 'Comunidade 2', specialty: 'Neurologia/Psiquiatria', size: 25, density: 0.72, cohesion: 0.70 },
                { name: 'Comunidade 3', specialty: 'Dermatologia/Endocrinologia', size: 20, density: 0.65, cohesion: 0.68 },
                { name: 'Comunidade 4', specialty: 'Ortopedia', size: 18, density: 0.70, cohesion: 0.72 },
                { name: 'Comunidade 5', specialty: 'Pediatria', size: 15, density: 0.58, cohesion: 0.65 },
                { name: 'Comunidade 6', specialty: 'Oftalmologia', size: 12, density: 0.75, cohesion: 0.80 },
                { name: 'Comunidade 7', specialty: 'Telemedicina', size: 10, density: 0.82, cohesion: 0.85 },
                { name: 'Comunidade 8', specialty: 'Medicina de Família', size: 8, density: 0.78, cohesion: 0.82 }
            ];

            communities.forEach((community, index) => {
                const tr = document.createElement('tr');
                tr.className = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';
                tr.innerHTML = `
                    <td class="p-2 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full mr-2" style="background-color: ${['#3182CE', '#E53E3E', '#38A169', '#DD6B20', '#805AD5', '#3182CE', '#F56565', '#48BB78'][index]}"></div>
                            ${community.name}
                        </div>
                    </td>
                    <td class="p-2 border-b border-gray-200">${community.specialty}</td>
                    <td class="p-2 border-b border-gray-200 text-center">${community.size}</td>
                    <td class="p-2 border-b border-gray-200 text-center">${community.density.toFixed(2)}</td>
                    <td class="p-2 border-b border-gray-200 text-center">${community.cohesion.toFixed(2)}</td>
                `;
                tbody.appendChild(tr);
            });

            table.appendChild(tbody);
            communityDetailsContainer.appendChild(table);
        }

        // Inicializar interações entre comunidades usando a função separada
        initializeCommunityInteractions();
    }

    // Função para inicializar interações entre comunidades
    function initializeCommunityInteractions(viewType = 'matrix') {
        console.log('Inicializando interações entre comunidades com tipo:', viewType);
        const communityInteractionContainer = findVisualizationContainer('communityInteractionView', 'h-[300px]');
        console.log('Container de interações entre comunidades:', communityInteractionContainer);
        if (!communityInteractionContainer) return;

        // Limpar o container
        communityInteractionContainer.innerHTML = '';

        // Dados de exemplo para matriz de interações
        const communities = ['Com 1', 'Com 2', 'Com 3', 'Com 4', 'Com 5', 'Com 6', 'Com 7', 'Com 8'];

        // Criar matriz de interações
        const matrix = [];
        for (let i = 0; i < communities.length; i++) {
            matrix[i] = [];
            for (let j = 0; j < communities.length; j++) {
                if (i === j) {
                    matrix[i][j] = 0; // Sem interação consigo mesmo na visualização
                } else {
                    // Algumas comunidades têm mais interações entre si
                    if ((i === 0 && j === 2) || (i === 2 && j === 0)) {
                        matrix[i][j] = 0.38; // Comunidades 1 e 3 têm alta interação
                    } else if (i === 4 || j === 4) {
                        matrix[i][j] = 0.12; // Comunidade 5 é mais isolada
                    } else {
                        matrix[i][j] = Math.random() * 0.2 + 0.1; // 0.1 a 0.3
                    }
                }
            }
        }

        // Escolher visualização com base no tipo selecionado
        if (viewType === 'matrix' || viewType === undefined) {
            // Visualização de matriz
            const width = communityInteractionContainer.clientWidth;
            const height = communityInteractionContainer.clientHeight;
            const padding = 50;

            const svg = d3.select(communityInteractionContainer)
                .append('svg')
                .attr('width', width)
                .attr('height', height);

            // Escala de cores
            const colorScale = d3.scaleLinear()
                .domain([0, 0.4])
                .range(['#E2E8F0', '#3182CE']);

            // Tamanho da célula
            const cellSize = Math.min((width - padding * 2) / communities.length, (height - padding * 2) / communities.length);

            // Criar células da matriz
            const cells = svg.append('g')
                .attr('transform', `translate(${padding}, ${padding})`)
                .selectAll('rect')
                .data(matrix.flatMap((row, i) => row.map((value, j) => ({ value, i, j }))))
                .enter()
                .append('rect')
                .attr('x', d => d.j * cellSize)
                .attr('y', d => d.i * cellSize)
                .attr('width', cellSize)
                .attr('height', cellSize)
                .attr('fill', d => d.i === d.j ? '#F7FAFC' : colorScale(d.value))
                .attr('stroke', '#E2E8F0')
                .attr('stroke-width', 1)
                .append('title')
                .text(d => d.i === d.j ? `${communities[d.i]}` : `${communities[d.i]} → ${communities[d.j]}: ${d.value.toFixed(2)}`);

            // Adicionar rótulos para linhas
            svg.append('g')
                .attr('transform', `translate(${padding - 5}, ${padding})`)
                .selectAll('text')
                .data(communities)
                .enter()
                .append('text')
                .attr('y', (d, i) => i * cellSize + cellSize / 2)
                .attr('text-anchor', 'end')
                .attr('dominant-baseline', 'middle')
                .attr('font-size', '10px')
                .text(d => d);

            // Adicionar rótulos para colunas
            svg.append('g')
                .attr('transform', `translate(${padding}, ${padding - 5})`)
                .selectAll('text')
                .data(communities)
                .enter()
                .append('text')
                .attr('x', (d, i) => i * cellSize + cellSize / 2)
                .attr('text-anchor', 'middle')
                .attr('font-size', '10px')
                .attr('transform', (d, i) => `rotate(-45, ${i * cellSize + cellSize / 2}, 0)`)
                .text(d => d);
        }
        else if (viewType === 'chord') {
            // Visualização de diagrama de acordes
            const width = communityInteractionContainer.clientWidth;
            const height = communityInteractionContainer.clientHeight;
            const radius = Math.min(width, height) / 2 - 40;

            const svg = d3.select(communityInteractionContainer)
                .append('svg')
                .attr('width', width)
                .attr('height', height)
                .append('g')
                .attr('transform', `translate(${width / 2}, ${height / 2})`);

            // Criar dados para o diagrama de acordes
            const chord = d3.chord()
                .padAngle(0.05)
                .sortSubgroups(d3.descending);

            const chordData = chord(matrix);

            // Arcos externos (grupos)
            const arc = d3.arc()
                .innerRadius(radius - 10)
                .outerRadius(radius);

            const outerArcs = svg.append('g')
                .selectAll('g')
                .data(chordData.groups)
                .enter()
                .append('g');

            outerArcs.append('path')
                .attr('d', arc)
                .attr('fill', (d, i) => ['#3182CE', '#E53E3E', '#38A169', '#DD6B20', '#805AD5', '#3182CE', '#F56565', '#48BB78'][i])
                .attr('stroke', '#fff');

            // Adicionar rótulos
            outerArcs.append('text')
                .each(d => { d.angle = (d.startAngle + d.endAngle) / 2; })
                .attr('dy', '.35em')
                .attr('transform', d => {
                    return `rotate(${d.angle * 180 / Math.PI - 90}) translate(${radius + 10}) ${d.angle > Math.PI ? 'rotate(180)' : ''}`;
                })
                .attr('text-anchor', d => d.angle > Math.PI ? 'end' : 'start')
                .attr('font-size', '10px')
                .text((d, i) => communities[i]);

            // Conexões (cordas)
            svg.append('g')
                .selectAll('path')
                .data(chordData)
                .enter()
                .append('path')
                .attr('d', d3.ribbon().radius(radius - 10))
                .attr('fill', d => {
                    // Usar cor da comunidade de origem com transparência
                    return d3.color(['#3182CE', '#E53E3E', '#38A169', '#DD6B20', '#805AD5', '#3182CE', '#F56565', '#48BB78'][d.source.index]).copy({opacity: 0.7});
                })
                .attr('stroke', '#fff')
                .attr('stroke-width', 0.5);
        }
        else if (viewType === 'sankey') {
            // Visualização de diagrama de Sankey
            const width = communityInteractionContainer.clientWidth;
            const height = communityInteractionContainer.clientHeight;
            const margin = {top: 10, right: 10, bottom: 10, left: 10};

            const svg = d3.select(communityInteractionContainer)
                .append('svg')
                .attr('width', width)
                .attr('height', height);

            // Preparar dados para o diagrama de Sankey
            const nodes = communities.map((name, i) => ({ name, id: i }));

            // Criar links a partir da matriz
            const links = [];
            for (let i = 0; i < matrix.length; i++) {
                for (let j = 0; j < matrix[i].length; j++) {
                    if (i !== j && matrix[i][j] > 0) {
                        links.push({
                            source: i,
                            target: j,
                            value: matrix[i][j] * 100 // Escalar para melhor visualização
                        });
                    }
                }
            }

            // Criar layout de Sankey
            const sankey = d3.sankey()
                .nodeWidth(15)
                .nodePadding(10)
                .extent([[margin.left, margin.top], [width - margin.right, height - margin.bottom]]);

            // Aplicar layout
            const sankeyData = sankey({
                nodes: nodes.map(d => Object.assign({}, d)),
                links: links.map(d => Object.assign({}, d))
            });

            // Desenhar links
            svg.append('g')
                .selectAll('path')
                .data(sankeyData.links)
                .enter()
                .append('path')
                .attr('d', d3.sankeyLinkHorizontal())
                .attr('stroke', d => {
                    // Usar cor da comunidade de origem com transparência
                    return d3.color(['#3182CE', '#E53E3E', '#38A169', '#DD6B20', '#805AD5', '#3182CE', '#F56565', '#48BB78'][d.source.id]).copy({opacity: 0.5});
                })
                .attr('stroke-width', d => Math.max(1, d.width))
                .attr('fill', 'none')
                .append('title')
                .text(d => `${communities[d.source.id]} → ${communities[d.target.id]}: ${(d.value / 100).toFixed(2)}`);

            // Desenhar nós
            const nodes_g = svg.append('g')
                .selectAll('g')
                .data(sankeyData.nodes)
                .enter()
                .append('g');

            nodes_g.append('rect')
                .attr('x', d => d.x0)
                .attr('y', d => d.y0)
                .attr('height', d => d.y1 - d.y0)
                .attr('width', d => d.x1 - d.x0)
                .attr('fill', d => ['#3182CE', '#E53E3E', '#38A169', '#DD6B20', '#805AD5', '#3182CE', '#F56565', '#48BB78'][d.id])
                .attr('stroke', '#fff');

            // Adicionar rótulos
            nodes_g.append('text')
                .attr('x', d => d.x0 < width / 2 ? d.x1 + 6 : d.x0 - 6)
                .attr('y', d => (d.y1 + d.y0) / 2)
                .attr('dy', '0.35em')
                .attr('text-anchor', d => d.x0 < width / 2 ? 'start' : 'end')
                .attr('font-size', '10px')
                .text(d => communities[d.id]);
        }
    }

    // Inicializar evolução de comunidades
    function initializeCommunityEvolution(period = 'year') {
        console.log('Inicializando evolução de comunidades com período:', period);
        const container = findVisualizationContainer('communityEvolutionPeriod', 'h-[400px]');
        console.log('Container de evolução de comunidades:', container);
        if (!container) return;

        // Limpar o container
        container.innerHTML = '';

        // Dados de exemplo para evolução temporal com base no período selecionado
        let categories, communityData;

        if (period === '6months') {
            // Últimos 6 meses
            categories = ['Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
            communityData = [
                { name: 'Comunidade 1', data: [26, 25, 24, 22, 20, 18] }, // Dividindo-se
                { name: 'Comunidade 2', data: [24, 23, 22, 21, 20, 19] }, // Estável
                { name: 'Comunidade 3', data: [21, 22, 23, 24, 25, 26] }, // Crescendo
                { name: 'Comunidade 4', data: [21, 21, 22, 22, 23, 23] }, // Crescimento lento
                { name: 'Comunidade 5', data: [15, 15, 15, 15, 15, 15] }, // Estável
                { name: 'Comunidade 6', data: [12, 12, 12, 12, 12, 12] }, // Estável
                { name: 'Comunidade 7', data: [0, 0, 5, 8, 9, 10] },      // Nova comunidade
                { name: 'Comunidade 8', data: [8, 8, 8, 8, 8, 8] }        // Estável
            ];
        }
        else if (period === 'year') {
            // Último ano (padrão)
            categories = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
            communityData = [
                { name: 'Comunidade 1', data: [28, 28, 29, 30, 30, 28, 26, 25, 24, 22, 20, 18] }, // Dividindo-se
                { name: 'Comunidade 2', data: [20, 21, 22, 23, 24, 25, 24, 23, 22, 21, 20, 19] }, // Estável
                { name: 'Comunidade 3', data: [15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26] }, // Crescendo
                { name: 'Comunidade 4', data: [18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23] }, // Crescimento lento
                { name: 'Comunidade 5', data: [15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15] }, // Estável
                { name: 'Comunidade 6', data: [12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12] }, // Estável
                { name: 'Comunidade 7', data: [0, 0, 0, 0, 0, 0, 0, 0, 5, 8, 9, 10] },           // Nova comunidade
                { name: 'Comunidade 8', data: [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8] }             // Estável
            ];
        }
        else if (period === '2years') {
            // Últimos 2 anos
            categories = [
                'Jan-22', 'Fev-22', 'Mar-22', 'Abr-22', 'Mai-22', 'Jun-22',
                'Jul-22', 'Ago-22', 'Set-22', 'Out-22', 'Nov-22', 'Dez-22',
                'Jan-23', 'Fev-23', 'Mar-23', 'Abr-23', 'Mai-23', 'Jun-23',
                'Jul-23', 'Ago-23', 'Set-23', 'Out-23', 'Nov-23', 'Dez-23'
            ];

            // Gerar dados para 2 anos
            communityData = [
                {
                    name: 'Comunidade 1',
                    data: [15, 16, 18, 19, 20, 22, 24, 25, 26, 27, 28, 28, 28, 28, 29, 30, 30, 28, 26, 25, 24, 22, 20, 18]
                },
                {
                    name: 'Comunidade 2',
                    data: [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 20, 20, 21, 22, 23, 24, 25, 24, 23, 22, 21, 20, 19]
                },
                {
                    name: 'Comunidade 3',
                    data: [5, 5, 6, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]
                },
                {
                    name: 'Comunidade 4',
                    data: [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 18, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23]
                },
                {
                    name: 'Comunidade 5',
                    data: [15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15]
                },
                {
                    name: 'Comunidade 6',
                    data: [0, 0, 0, 0, 0, 0, 0, 5, 8, 10, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12]
                },
                {
                    name: 'Comunidade 7',
                    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 8, 9, 10]
                },
                {
                    name: 'Comunidade 8',
                    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8]
                }
            ];
        }

        // Criar gráfico com ApexCharts
        const options = {
            series: communityData,
            chart: {
                height: container.clientHeight,
                type: 'line',
                toolbar: {
                    show: false
                },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800
                }
            },
            stroke: {
                width: 3,
                curve: 'smooth'
            },
            colors: ['#3182CE', '#E53E3E', '#38A169', '#DD6B20', '#805AD5', '#3182CE', '#F56565', '#48BB78'],
            xaxis: {
                categories: categories
            },
            yaxis: {
                title: {
                    text: 'Tamanho da Comunidade',
                    style: {
                        fontSize: '12px'
                    }
                },
                min: 0
            },
            markers: {
                size: 4,
                strokeWidth: 0,
                hover: {
                    size: 6
                }
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + ' membros';
                    }
                }
            },
            legend: {
                position: 'top'
            }
        };

        const chart = new ApexCharts(container, options);
        chart.render();
    }

    function initializeNetworkCharts(data) {
        // Gráfico de Crescimento da Rede
        if (document.getElementById('networkGrowthChart')) {
            const networkGrowthCtx = document.getElementById('networkGrowthChart').getContext('2d');
            const networkGrowthChart = new Chart(networkGrowthCtx, {
                type: 'line',
                data: {
                    labels: Object.keys(data.connections.connections_by_month),
                    datasets: [{
                        label: 'Conexões',
                        data: Object.values(data.connections.connections_by_month),
                        backgroundColor: 'rgba(0, 122, 255, 0.1)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Adicionar event listener para o filtro de período
            if (document.getElementById('networkGrowthPeriod')) {
                document.getElementById('networkGrowthPeriod').addEventListener('change', function() {
                    // Simulação de dados diferentes para cada período
                    let newLabels, newData;

                    if (this.value === 'month') {
                        newLabels = Object.keys(data.connections.connections_by_month);
                        newData = Object.values(data.connections.connections_by_month);
                    } else if (this.value === 'quarter') {
                        newLabels = ['Q1 2024', 'Q2 2024', 'Q3 2024', 'Q4 2024'];
                        newData = [1245, 1567, 1876, 2134];
                    } else {
                        newLabels = ['Jan-Jun 2023', 'Jul-Dec 2023', 'Jan-Jun 2024', 'Jul-Dec 2024'];
                        newData = [2345, 3456, 4567, 5678];
                    }

                    networkGrowthChart.data.labels = newLabels;
                    networkGrowthChart.data.datasets[0].data = newData;
                    networkGrowthChart.update();
                });
            }
        }

        // Gráfico de Distribuição de Interações
        if (document.getElementById('interactionsDistributionChart')) {
            const interactionsDistributionCtx = document.getElementById('interactionsDistributionChart').getContext('2d');
            new Chart(interactionsDistributionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Curtidas', 'Comentários', 'Compartilhamentos'],
                    datasets: [{
                        data: [
                            data.connections.feed_interactions.likes,
                            data.connections.feed_interactions.comments,
                            data.connections.feed_interactions.shares
                        ],
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.8)',
                            'rgba(0, 122, 255, 0.5)',
                            'rgba(0, 122, 255, 0.3)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(0, 122, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });
        }

        // Gráfico de Mapa de Conexões por Especialidade
        if (document.getElementById('connectionsMapChart')) {
            const connectionsMapCtx = document.getElementById('connectionsMapChart').getContext('2d');

            // Dados simulados para conexões por especialidade
            const specialtyConnections = {
                'Cardiologia': 432,
                'Neurologia': 387,
                'Dermatologia': 345,
                'Ortopedia': 312,
                'Pediatria': 287,
                'Ginecologia': 254,
                'Oftalmologia': 198,
                'Outras': 661
            };

            const connectionsMapChart = new Chart(connectionsMapCtx, {
                type: 'bar',
                data: {
                    labels: Object.keys(specialtyConnections),
                    datasets: [{
                        label: 'Conexões',
                        data: Object.values(specialtyConnections),
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Adicionar event listener para o filtro de especialidades
            if (document.getElementById('connectionsMapFilter')) {
                document.getElementById('connectionsMapFilter').addEventListener('change', function() {
                    let filteredData = {};

                    if (this.value === 'top5') {
                        // Filtrar apenas as top 5 especialidades
                        const entries = Object.entries(specialtyConnections).sort((a, b) => b[1] - a[1]).slice(0, 5);
                        entries.forEach(([key, value]) => {
                            filteredData[key] = value;
                        });
                    } else if (this.value === 'growth') {
                        // Dados simulados para especialidades com maior crescimento
                        filteredData = {
                            'Dermatologia': 345,
                            'Psiquiatria': 276,
                            'Endocrinologia': 245,
                            'Oncologia': 234,
                            'Reumatologia': 187
                        };
                    } else {
                        // Todas as especialidades
                        filteredData = specialtyConnections;
                    }

                    connectionsMapChart.data.labels = Object.keys(filteredData);
                    connectionsMapChart.data.datasets[0].data = Object.values(filteredData);
                    connectionsMapChart.update();
                });
            }
        }

        // Gráfico de Tópicos Populares no Feed
        if (document.getElementById('popularFeedTopicsChart')) {
            const popularFeedTopicsCtx = document.getElementById('popularFeedTopicsChart').getContext('2d');

            // Dados simulados para tópicos populares no feed
            const popularFeedTopics = {
                'Casos Clínicos': 876,
                'Atualizações Científicas': 765,
                'Eventos Médicos': 654,
                'Dúvidas Profissionais': 543,
                'Tecnologia Médica': 432,
                'Gestão de Consultório': 321,
                'Outros': 210
            };

            new Chart(popularFeedTopicsCtx, {
                type: 'bar',
                data: {
                    labels: Object.keys(popularFeedTopics),
                    datasets: [{
                        label: 'Publicações',
                        data: Object.values(popularFeedTopics),
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Horários de Maior Atividade
        if (document.getElementById('activityTimeChart')) {
            const activityTimeCtx = document.getElementById('activityTimeChart').getContext('2d');

            // Inicialmente mostrar análise por hora
            renderActivityTimeChart(activityTimeCtx, 'hour');

            // Adicionar event listener para o filtro
            if (document.getElementById('activityTimeFilter')) {
                document.getElementById('activityTimeFilter').addEventListener('change', function() {
                    renderActivityTimeChart(activityTimeCtx, this.value);
                });
            }

            function renderActivityTimeChart(ctx, type) {
                let chartData = {};
                let chartTitle = '';

                if (type === 'hour') {
                    chartData = {
                        '8-10h': 87,
                        '10-12h': 156,
                        '12-14h': 178,
                        '14-16h': 145,
                        '16-18h': 132,
                        '18-20h': 167,
                        '20-22h': 187,
                        '22-24h': 76
                    };
                    chartTitle = 'Atividade por Hora do Dia';
                } else if (type === 'weekday') {
                    chartData = {
                        'Segunda': 145,
                        'Terça': 187,
                        'Quarta': 156,
                        'Quinta': 176,
                        'Sexta': 143,
                        'Sábado': 87,
                        'Domingo': 45
                    };
                    chartTitle = 'Atividade por Dia da Semana';
                }

                // Destruir gráfico anterior se existir
                if (window.activityTimeChartInstance) {
                    window.activityTimeChartInstance.destroy();
                }

                // Criar novo gráfico
                window.activityTimeChartInstance = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: Object.keys(chartData),
                        datasets: [{
                            label: 'Atividade',
                            data: Object.values(chartData),
                            backgroundColor: 'rgba(0, 122, 255, 0.7)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            title: {
                                display: true,
                                text: chartTitle,
                                font: {
                                    size: 14
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    display: true,
                                    color: 'rgba(0, 0, 0, 0.05)'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }
        }

        // Gráfico de Retenção de Usuários
        if (document.getElementById('userRetentionChart')) {
            const userRetentionCtx = document.getElementById('userRetentionChart').getContext('2d');

            // Dados simulados para retenção de usuários
            const retentionData = {
                'Semana 1': 100,
                'Semana 2': 82,
                'Semana 3': 76,
                'Semana 4': 72,
                'Semana 5': 68,
                'Semana 6': 65,
                'Semana 7': 63,
                'Semana 8': 62
            };

            new Chart(userRetentionCtx, {
                type: 'line',
                data: {
                    labels: Object.keys(retentionData),
                    datasets: [{
                        label: 'Taxa de Retenção (%)',
                        data: Object.values(retentionData),
                        backgroundColor: 'rgba(0, 122, 255, 0.1)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }
    }
</script>

<!-- Biblioteca ForceGraph para visualizações de rede -->
<script src="https://unpkg.com/force-graph"></script>
<!-- Biblioteca D3-Sankey para diagramas de Sankey -->
<script src="https://unpkg.com/d3-sankey@0.12.3/dist/d3-sankey.min.js"></script>
<!-- Biblioteca D3-Chord para diagramas de acordes -->
<script src="https://unpkg.com/d3-chord@3.0.1/dist/d3-chord.min.js"></script>
<!-- Biblioteca D3-Hierarchy para visualizações hierárquicas -->
<script src="https://unpkg.com/d3-hierarchy@3.1.2/dist/d3-hierarchy.min.js"></script>
{% endblock %}