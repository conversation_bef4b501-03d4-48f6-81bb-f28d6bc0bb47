{% extends 'base.html' %}

{% block title %}Contabilidade - Amigo One Analytics{% endblock %}

{% block header %}Análise de Contabilidade{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">CONTABILIDADE</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Análise Avançada de Contabilidade</h1>
            <p class="text-gray-600 mb-6">Dashboard completo com dados reais de transações, análise de performance, tendências e indicadores de risco para otimização da gestão financeira.</p>

            <!-- Loading/Error States -->
            <div id="hero-loading" class="flex items-center space-x-2 text-systemBlue">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-systemBlue"></div>
                <span class="text-sm">Carregando dados...</span>
            </div>

            <div id="hero-error" class="hidden bg-red-50 border border-red-200 rounded-lg p-3 text-red-700 text-sm">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <span id="hero-error-message">Erro ao carregar dados</span>
            </div>

            <!-- Hero Metrics -->
            <div id="hero-metrics" class="hidden grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div id="hero-total-transactions" class="text-2xl font-bold text-systemBlue">0</div>
                    <div class="text-xs text-gray-500">Total de Transações</div>
                </div>
                <div class="text-center">
                    <div id="hero-success-rate" class="text-2xl font-bold text-systemBlue">0%</div>
                    <div class="text-xs text-gray-500">Taxa de Sucesso</div>
                </div>
                <div class="text-center">
                    <div id="hero-total-revenue" class="text-2xl font-bold text-systemBlue">R$ 0</div>
                    <div class="text-xs text-gray-500">Receita Total</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button id="refresh-data" class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Atualizar Dados
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Exportar Relatório
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div id="hero-chart-container" class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div id="hero-chart-loading" class="absolute inset-0 flex items-center justify-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-systemBlue"></div>
                </div>
                <canvas id="heroChart" class="hidden"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- KPI Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
    <!-- Total de Transações -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Total de Transações</p>
        </div>
        <p id="total-transactions" class="text-3xl font-semibold text-gray-800 mb-1">0</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Processadas no período</span>
            <span id="transactions-trend" class="ml-auto text-systemGreen font-medium">--</span>
        </div>
    </div>

    <!-- Receita Total -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Receita Total</p>
        </div>
        <p id="total-revenue" class="text-3xl font-semibold text-gray-800 mb-1">R$ 0</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Apenas transações autorizadas</span>
            <span id="revenue-trend" class="ml-auto text-systemGreen font-medium">--</span>
        </div>
    </div>

    <!-- Ticket Médio -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Ticket Médio</p>
        </div>
        <p id="avg-transaction" class="text-3xl font-semibold text-gray-800 mb-1">R$ 0</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Valor médio por transação</span>
            <span id="avg-trend" class="ml-auto text-systemGreen font-medium">--</span>
        </div>
    </div>

    <!-- Taxa de Sucesso -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Taxa de Sucesso</p>
        </div>
        <p id="success-rate" class="text-3xl font-semibold text-gray-800 mb-1">0%</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Transações autorizadas</span>
            <span id="success-trend" class="ml-auto text-systemGreen font-medium">--</span>
        </div>
    </div>

    <!-- Usuários Únicos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Usuários Únicos</p>
        </div>
        <p id="unique-users" class="text-3xl font-semibold text-gray-800 mb-1">0</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Usuários com transações</span>
            <span id="users-trend" class="ml-auto text-systemGreen font-medium">--</span>
        </div>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Status de Transações -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Status de Transações</h2>
        </div>
        <div class="h-64">
            <canvas id="statusChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> <span id="status-insight">Carregando análise...</span></p>
        </div>
    </div>

    <!-- Receita Mensal -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Evolução da Receita</h2>
        </div>
        <div class="h-64">
            <canvas id="revenueChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> <span id="revenue-insight">Carregando análise...</span></p>
        </div>
    </div>
</div>

<!-- Análises Comportamentais -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Receita por Hora -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Receita por Hora do Dia</h2>
        </div>
        <div class="h-64">
            <canvas id="hourlyChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> <span id="hourly-insight">Carregando análise...</span></p>
        </div>
    </div>

    <!-- Tendências de Crescimento -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Crescimento Mensal</h2>
        </div>
        <div class="h-64">
            <canvas id="growthChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> <span id="growth-insight">Carregando análise...</span></p>
        </div>
    </div>
</div>

<!-- Análises de Performance e Risco -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
    <!-- Indicadores de Performance -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Indicadores de Performance</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">Performance</span>
            </div>
        </div>

        <div class="space-y-4">
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Taxa de Autorização</span>
                <span id="authorization-rate" class="font-semibold text-systemBlue">0%</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Taxa de Rejeição</span>
                <span id="rejection-rate" class="font-semibold text-red-600">0%</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Taxa de Cancelamento</span>
                <span id="cancellation-rate" class="font-semibold text-orange-600">0%</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Transações Processadas</span>
                <span id="processed-transactions" class="font-semibold text-gray-800">0</span>
            </div>
        </div>
    </div>

    <!-- Análise de Risco -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Análise de Risco</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"></path>
                </svg>
                <span class="text-xs text-red-500 font-medium">Risco</span>
            </div>
        </div>

        <div class="space-y-4">
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Transações Alto Valor</span>
                <span id="high-value-transactions" class="font-semibold text-red-600">0</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Usuários Alto Risco</span>
                <span id="high-risk-users" class="font-semibold text-red-600">0</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Ratio Alto Valor</span>
                <span id="high-value-ratio" class="font-semibold text-orange-600">0%</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Concentração Rejeições</span>
                <span id="rejection-concentration" class="font-semibold text-red-600">0</span>
            </div>
        </div>
    </div>

    <!-- Comportamento de Usuários -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Comportamento de Usuários</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20a3 3 0 01-3-3v-2a3 3 0 013-3h3a3 3 0 013 3v2a3 3 0 01-3 3H7zM8 9a3 3 0 116 0 3 3 0 01-6 0z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">Usuários</span>
            </div>
        </div>

        <div class="space-y-4">
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Usuários Únicos</span>
                <span id="total-unique-users" class="font-semibold text-systemBlue">0</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Transações/Usuário</span>
                <span id="avg-transactions-per-user" class="font-semibold text-gray-800">0</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Valor/Usuário</span>
                <span id="avg-value-per-user" class="font-semibold text-gray-800">R$ 0</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Usuários Alto Valor</span>
                <span id="high-value-users" class="font-semibold text-systemGreen">0</span>
            </div>
        </div>
    </div>
</div>

<!-- Top Usuários e Tendências -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Top Usuários -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Top 10 Usuários por Volume</h2>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Pos</th>
                        <th class="text-left text-xs font-medium text-gray-500 uppercase tracking-wider py-2">User ID</th>
                        <th class="text-right text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Volume</th>
                        <th class="text-right text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Transações</th>
                        <th class="text-right text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Média</th>
                    </tr>
                </thead>
                <tbody id="top-users-table" class="divide-y divide-gray-200">
                    <!-- Dynamic content -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Tendências Diárias -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Tendências de Transações</h2>
        </div>
        <div class="space-y-4">
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Dia de Pico</span>
                <span id="peak-day" class="font-semibold text-systemBlue">--</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Transações no Pico</span>
                <span id="peak-transactions" class="font-semibold text-systemBlue">0</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Média Diária</span>
                <span id="avg-daily-transactions" class="font-semibold text-gray-800">0</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Crescimento Médio</span>
                <span id="avg-growth-rate" class="font-semibold text-systemGreen">0%</span>
            </div>
        </div>

        <div class="mt-4 pt-4 border-t border-gray-200">
            <h3 class="text-sm font-semibold text-gray-800 mb-2">Distribuição de Usuários por Valor</h3>
            <div class="space-y-2">
                <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-600">Alto Valor (>R$ 1000)</span>
                    <span id="high-value-user-count" class="text-xs font-semibold text-systemGreen">0</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-600">Médio Valor (R$ 100-1000)</span>
                    <span id="medium-value-user-count" class="text-xs font-semibold text-systemBlue">0</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-600">Baixo Valor (<R$ 100)</span>
                    <span id="low-value-user-count" class="text-xs font-semibold text-gray-600">0</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar carregamento de dados
        loadAccountingData();

        // Event listener para botão de refresh
        document.getElementById('refresh-data').addEventListener('click', function() {
            loadAccountingData();
        });
    });

    let statusChart, revenueChart, hourlyChart, growthChart, heroChart;

    async function loadAccountingData() {
        try {
            // Show loading states
            showLoadingStates();

            const response = await fetch('/api/accounting-data');
            const result = await response.json();

            if (result.status === 'success') {
                displayData(result.data, result.records_count || 0);
            } else {
                showError(result.message || 'Erro desconhecido');
            }
        } catch (error) {
            console.error('Erro ao carregar dados:', error);
            showError('Erro de conexão: ' + error.message);
        }
    }

    function showLoadingStates() {
        // Hero section
        document.getElementById('hero-loading').classList.remove('hidden');
        document.getElementById('hero-error').classList.add('hidden');
        document.getElementById('hero-metrics').classList.add('hidden');
        document.getElementById('hero-chart-loading').classList.remove('hidden');
        document.getElementById('heroChart').classList.add('hidden');
    }

    function displayData(data, recordsCount) {
        // Hide loading states
        document.getElementById('hero-loading').classList.add('hidden');
        document.getElementById('hero-chart-loading').classList.add('hidden');

        // Show content
        document.getElementById('hero-metrics').classList.remove('hidden');
        document.getElementById('heroChart').classList.remove('hidden');

        // Update hero metrics
        document.getElementById('hero-total-transactions').textContent = data.total_transactions.toLocaleString('pt-BR');
        document.getElementById('hero-success-rate').textContent = data.performance_indicators.success_rate + '%';
        document.getElementById('hero-total-revenue').textContent = 'R$ ' + data.total_revenue.toLocaleString('pt-BR', {minimumFractionDigits: 2});

        // Update KPI cards
        updateKPICards(data);

        // Update performance indicators
        updatePerformanceIndicators(data);

        // Update risk analysis
        updateRiskAnalysis(data);

        // Update user behavior
        updateUserBehavior(data);

        // Update trends
        updateTrends(data);

        // Create charts
        createAllCharts(data);

        // Update top users table
        updateTopUsersTable(data.top_users);

        // Update insights
        updateInsights(data);
    }

    function updateKPICards(data) {
        document.getElementById('total-transactions').textContent = data.total_transactions.toLocaleString('pt-BR');
        document.getElementById('total-revenue').textContent = 'R$ ' + data.total_revenue.toLocaleString('pt-BR', {minimumFractionDigits: 2});
        document.getElementById('avg-transaction').textContent = 'R$ ' + data.avg_transaction_value.toLocaleString('pt-BR', {minimumFractionDigits: 2});
        document.getElementById('success-rate').textContent = data.performance_indicators.success_rate + '%';
        document.getElementById('unique-users').textContent = data.user_behavior.total_unique_users.toLocaleString('pt-BR');
    }

    function updatePerformanceIndicators(data) {
        const perf = data.performance_indicators;
        document.getElementById('authorization-rate').textContent = perf.success_rate + '%';
        document.getElementById('rejection-rate').textContent = perf.rejection_rate + '%';
        document.getElementById('cancellation-rate').textContent = perf.cancellation_rate + '%';
        document.getElementById('processed-transactions').textContent = perf.total_processed.toLocaleString('pt-BR');
    }

    function updateRiskAnalysis(data) {
        const risk = data.risk_analysis;
        document.getElementById('high-value-transactions').textContent = risk.high_value_transactions.toLocaleString('pt-BR');
        document.getElementById('high-risk-users').textContent = risk.high_risk_users.toLocaleString('pt-BR');
        document.getElementById('high-value-ratio').textContent = risk.risk_indicators.high_value_ratio + '%';
        document.getElementById('rejection-concentration').textContent = risk.risk_indicators.rejection_concentration.toLocaleString('pt-BR');
    }

    function updateUserBehavior(data) {
        const behavior = data.user_behavior;
        document.getElementById('total-unique-users').textContent = behavior.total_unique_users.toLocaleString('pt-BR');
        document.getElementById('avg-transactions-per-user').textContent = behavior.avg_transactions_per_user.toLocaleString('pt-BR');
        document.getElementById('avg-value-per-user').textContent = 'R$ ' + behavior.avg_value_per_user.toLocaleString('pt-BR', {minimumFractionDigits: 2});
        document.getElementById('high-value-users').textContent = behavior.user_distribution.high_value.toLocaleString('pt-BR');

        // User distribution
        document.getElementById('high-value-user-count').textContent = behavior.user_distribution.high_value.toLocaleString('pt-BR');
        document.getElementById('medium-value-user-count').textContent = behavior.user_distribution.medium_value.toLocaleString('pt-BR');
        document.getElementById('low-value-user-count').textContent = behavior.user_distribution.low_value.toLocaleString('pt-BR');
    }

    function updateTrends(data) {
        const trends = data.transaction_trends;
        const growth = data.growth_metrics;

        document.getElementById('peak-day').textContent = trends.peak_day[0] || '--';
        document.getElementById('peak-transactions').textContent = trends.peak_day[1]?.toLocaleString('pt-BR') || '0';
        document.getElementById('avg-daily-transactions').textContent = trends.avg_daily.toLocaleString('pt-BR');
        document.getElementById('avg-growth-rate').textContent = growth.avg_growth_rate + '%';
    }

    function createAllCharts(data) {
        createHeroChart(data.status_distribution);
        createStatusChart(data.status_distribution);
        createRevenueChart(data.monthly_revenue);
        createHourlyChart(data.revenue_by_hour);
        createGrowthChart(data.growth_metrics);
    }

    function createHeroChart(statusData) {
        const ctx = document.getElementById('heroChart').getContext('2d');

        if (heroChart) {
            heroChart.destroy();
        }

        const labels = Object.keys(statusData);
        const values = Object.values(statusData);
        const colors = ['#007AFF', '#34C759', '#FF9500', '#FF3B30'];

        heroChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: colors,
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    function createStatusChart(statusData) {
        const ctx = document.getElementById('statusChart').getContext('2d');

        if (statusChart) {
            statusChart.destroy();
        }

        const labels = Object.keys(statusData);
        const values = Object.values(statusData);
        const colors = labels.map(status => {
            switch(status) {
                case 'AUTORIZADA': return '#007AFF';
                case 'REJEITADA': return '#FF3B30';
                case 'CANCELADA': return '#FF9500';
                default: return '#8E8E93';
            }
        });

        statusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: colors,
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                },
                cutout: '60%'
            }
        });
    }

    function createRevenueChart(revenueData) {
        const ctx = document.getElementById('revenueChart').getContext('2d');

        if (revenueChart) {
            revenueChart.destroy();
        }

        const sortedEntries = Object.entries(revenueData).sort();
        const labels = sortedEntries.map(([month]) => month);
        const values = sortedEntries.map(([, value]) => value);

        revenueChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Receita (R$)',
                    data: values,
                    backgroundColor: 'rgba(0, 122, 255, 0.1)',
                    borderColor: '#007AFF',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    function createHourlyChart(hourlyData) {
        const ctx = document.getElementById('hourlyChart').getContext('2d');

        if (hourlyChart) {
            hourlyChart.destroy();
        }

        // Criar array de 24 horas
        const hours = Array.from({length: 24}, (_, i) => i);
        const values = hours.map(hour => hourlyData[hour.toString()] || 0);

        hourlyChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: hours.map(h => h + 'h'),
                datasets: [{
                    label: 'Receita por Hora',
                    data: values,
                    backgroundColor: 'rgba(0, 122, 255, 0.7)',
                    borderColor: '#007AFF',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        }
                    }
                }
            }
        });
    }

    function createGrowthChart(growthData) {
        const ctx = document.getElementById('growthChart').getContext('2d');

        if (growthChart) {
            growthChart.destroy();
        }

        const sortedEntries = Object.entries(growthData.monthly_growth_rates || {}).sort();
        const labels = sortedEntries.map(([month]) => month);
        const values = sortedEntries.map(([, rate]) => rate);

        growthChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Taxa de Crescimento (%)',
                    data: values,
                    backgroundColor: 'rgba(52, 199, 89, 0.1)',
                    borderColor: '#34C759',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    function updateTopUsersTable(topUsers) {
        const tbody = document.getElementById('top-users-table');
        tbody.innerHTML = '';

        if (topUsers.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="py-4 text-center text-gray-500 text-sm">Nenhum dado disponível</td></tr>';
            return;
        }

        topUsers.forEach((user, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="py-2 text-sm font-medium text-gray-900">${index + 1}º</td>
                <td class="py-2 text-sm text-gray-900">${user.user_id}</td>
                <td class="py-2 text-sm text-gray-900 text-right">R$ ${user.total_value.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</td>
                <td class="py-2 text-sm text-gray-900 text-right">${user.transaction_count}</td>
                <td class="py-2 text-sm text-gray-900 text-right">R$ ${user.avg_transaction.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</td>
            `;
            tbody.appendChild(row);
        });
    }

    function updateInsights(data) {
        // Status insight
        const totalTransactions = data.total_transactions;
        const successRate = data.performance_indicators.success_rate;
        document.getElementById('status-insight').textContent =
            `${successRate}% das ${totalTransactions.toLocaleString('pt-BR')} transações foram autorizadas com sucesso.`;

        // Revenue insight
        const totalRevenue = data.total_revenue;
        const monthlyData = Object.values(data.monthly_revenue);
        const avgMonthly = monthlyData.length > 0 ? monthlyData.reduce((a, b) => a + b, 0) / monthlyData.length : 0;
        document.getElementById('revenue-insight').textContent =
            `Receita total de R$ ${totalRevenue.toLocaleString('pt-BR', {minimumFractionDigits: 2})} com média mensal de R$ ${avgMonthly.toLocaleString('pt-BR', {minimumFractionDigits: 2})}.`;

        // Hourly insight
        const hourlyData = Object.entries(data.revenue_by_hour);
        if (hourlyData.length > 0) {
            const peakHour = hourlyData.reduce((max, current) => current[1] > max[1] ? current : max);
            document.getElementById('hourly-insight').textContent =
                `Pico de receita às ${peakHour[0]}h com R$ ${peakHour[1].toLocaleString('pt-BR', {minimumFractionDigits: 2})}.`;
        }

        // Growth insight
        const avgGrowth = data.growth_metrics.avg_growth_rate;
        document.getElementById('growth-insight').textContent =
            `Taxa média de crescimento mensal de ${avgGrowth}% no período analisado.`;
    }

    function showError(message) {
        document.getElementById('hero-loading').classList.add('hidden');
        document.getElementById('hero-error').classList.remove('hidden');
        document.getElementById('hero-error-message').textContent = message;
    }
</script>
{% endblock %}
