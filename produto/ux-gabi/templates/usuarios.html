{% extends 'base.html' %}

{% block title %}Usuários - Amigo One Analytics{% endblock %}

{% block header %}An<PERSON><PERSON><PERSON> de Usuários{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">USUÁRIOS</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Análise de Usuários</h1>
            <p class="text-gray-600 mb-6">Monitore o perfil dos usuários, analise a distribuição demográfica e identifique padrões de uso para melhorar a experiência no aplicativo.</p>
            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ data.users.total_users|e }}</div>
                    <div class="text-xs text-gray-500">Usuários totais</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ data.users.premium_users|e }}</div>
                    <div class="text-xs text-gray-500">Usuários premium</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ data.users.free_users|e }}</div>
                    <div class="text-xs text-gray-500">Usuários free</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Gerar Relatório
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Filtrar Dados
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">{{ (data.users.premium_users / data.users.total_users * 100) | round | int }}%</div>
                    <div class="text-sm text-gray-500">Premium</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- KPI Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Usuários Ativos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Usuários Ativos</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.users.active_users|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Usuários que acessaram no último mês</span>
            <span class="ml-auto text-systemGreen font-medium">+12%</span>
        </div>
    </div>

    <!-- Usuários por Dispositivo -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">iOS</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.users.users_by_device.iOS|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Usuários em dispositivos iOS</span>
            <span class="ml-auto text-systemGreen font-medium">+8.5%</span>
        </div>
    </div>

    <!-- Usuários Android -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Android</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ data.users.users_by_device.Android|e }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Usuários em dispositivos Android</span>
            <span class="ml-auto text-systemGreen font-medium">+15.2%</span>
        </div>
    </div>

    <!-- Crescimento Mensal -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Crescimento Mensal</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">+{{ ((data.users.growth_by_month["Ago/2025"] - data.users.growth_by_month["Jul/2025"]) / data.users.growth_by_month["Jul/2025"] * 100) | round | int }}%</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Crescimento no último mês</span>
            <span class="ml-auto text-systemGreen font-medium">↑</span>
        </div>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Crescimento de Usuários -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Crescimento de Usuários</h2>
        </div>
        <div class="h-64">
            <canvas id="userGrowthChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Crescimento consistente de usuários nos últimos 8 meses, com aumento de 29% no total.</p>
        </div>
    </div>

    <!-- Distribuição por Especialidade -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Distribuição por Especialidade</h2>
        </div>
        <div class="h-64">
            <canvas id="specialtyDistributionChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Cardiologia, Dermatologia e Ortopedia são as especialidades com maior número de usuários.</p>
        </div>
    </div>
</div>

<!-- Distribuição Geográfica e Idade -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Distribuição Geográfica -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Distribuição Geográfica</h2>
        </div>
        <div class="h-64">
            <canvas id="geographicDistributionChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> São Paulo, Rio de Janeiro e Minas Gerais concentram a maior parte dos usuários.</p>
        </div>
    </div>

    <!-- Distribuição por Idade -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Distribuição por Idade</h2>
        </div>
        <div class="h-64">
            <canvas id="ageDistributionChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> A faixa etária de 31-35 anos representa a maior parte dos usuários (26%).</p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados do dashboard
        const dashboardData = {{ data | tojson |e }};
        
        // Inicializar gráficos
        initializeUserCharts(dashboardData);
    });
    
    function initializeUserCharts(data) {
        // Gráfico de Crescimento de Usuários
        const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
        new Chart(userGrowthCtx, {
            type: 'line',
            data: {
                labels: Object.keys(data.users.growth_by_month),
                datasets: [{
                    label: 'Usuários',
                    data: Object.values(data.users.growth_by_month),
                    backgroundColor: 'rgba(0, 122, 255, 0.1)',
                    borderColor: 'rgba(0, 122, 255, 1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
        
        // Gráfico de Distribuição por Especialidade
        const specialtyCtx = document.getElementById('specialtyDistributionChart').getContext('2d');
        new Chart(specialtyCtx, {
            type: 'pie',
            data: {
                labels: Object.keys(data.users.users_by_specialty),
                datasets: [{
                    data: Object.values(data.users.users_by_specialty),
                    backgroundColor: [
                        'rgba(0, 122, 255, 0.9)',
                        'rgba(0, 122, 255, 0.8)',
                        'rgba(0, 122, 255, 0.7)',
                        'rgba(0, 122, 255, 0.6)',
                        'rgba(0, 122, 255, 0.5)',
                        'rgba(0, 122, 255, 0.4)',
                        'rgba(0, 122, 255, 0.3)',
                        'rgba(120, 120, 120, 0.5)'
                    ],
                    borderWidth: 1,
                    borderColor: 'rgba(255, 255, 255, 0.8)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            color: '#333'
                        }
                    }
                }
            }
        });
        
        // Gráfico de Distribuição Geográfica
        const geoDistributionCtx = document.getElementById('geographicDistributionChart').getContext('2d');
        new Chart(geoDistributionCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(data.users.users_by_state),
                datasets: [{
                    label: 'Usuários',
                    data: Object.values(data.users.users_by_state),
                    backgroundColor: 'rgba(0, 122, 255, 0.7)',
                    borderColor: 'rgba(0, 122, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
        
        // Gráfico de Distribuição por Idade
        const ageDistributionCtx = document.getElementById('ageDistributionChart').getContext('2d');
        new Chart(ageDistributionCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(data.users.age_distribution),
                datasets: [{
                    label: 'Usuários',
                    data: Object.values(data.users.age_distribution),
                    backgroundColor: 'rgba(0, 122, 255, 0.7)',
                    borderColor: 'rgba(0, 122, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }
</script>
{% endblock %}
