{% extends 'base.html' %}

{% block title %}Documentação - Amigo One Analytics{% endblock %}

{% block header %}Documentação da Base de Dados{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">DOCUMENTAÇÃO</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Documentação da Base de Dados</h1>
            <p class="text-gray-600 mb-6">Guia completo sobre a estrutura da base de dados do Amigo One, incluindo tabelas, relacionamentos e métricas para análise.</p>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    Baixar Documentação
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                    Ver Esquema
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-lg shadow-lg flex items-center justify-center p-4 relative">
                <svg class="w-48 h-48 text-systemBlue opacity-80" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 8h14M5 8a1 1 0 110-2h14a1 1 0 110 2M5 8v10a1 1 0 001 1h12a1 1 0 001-1V8M10 12h4"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Navegação da Documentação -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Índice da Documentação</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <a href="#estrutura" class="flex items-center p-3 rounded-md border border-gray-200 hover:bg-blue-50 hover:border-blue-200 transition-colors">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                </svg>
            </div>
            <div>
                <h3 class="font-medium text-gray-800">Estrutura da Base</h3>
                <p class="text-xs text-gray-500">Visão geral das tabelas</p>
            </div>
        </a>
        <a href="#usuarios" class="flex items-center p-3 rounded-md border border-gray-200 hover:bg-blue-50 hover:border-blue-200 transition-colors">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
            </div>
            <div>
                <h3 class="font-medium text-gray-800">Usuários</h3>
                <p class="text-xs text-gray-500">Dados de usuários e dispositivos</p>
            </div>
        </a>
        <a href="#prontuarios" class="flex items-center p-3 rounded-md border border-gray-200 hover:bg-blue-50 hover:border-blue-200 transition-colors">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
            </div>
            <div>
                <h3 class="font-medium text-gray-800">Prontuários</h3>
                <p class="text-xs text-gray-500">Registros médicos e assinaturas</p>
            </div>
        </a>
        <a href="#amigo-intelligence" class="flex items-center p-3 rounded-md border border-gray-200 hover:bg-blue-50 hover:border-blue-200 transition-colors">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
            </div>
            <div>
                <h3 class="font-medium text-gray-800">Amigo Intelligence</h3>
                <p class="text-xs text-gray-500">Dados de IA e interações</p>
            </div>
        </a>
        <a href="#rede-amigo" class="flex items-center p-3 rounded-md border border-gray-200 hover:bg-blue-50 hover:border-blue-200 transition-colors">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <div>
                <h3 class="font-medium text-gray-800">Rede Amigo</h3>
                <p class="text-xs text-gray-500">Conexões e interações sociais</p>
            </div>
        </a>
        <a href="#contabilidade" class="flex items-center p-3 rounded-md border border-gray-200 hover:bg-blue-50 hover:border-blue-200 transition-colors">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div>
                <h3 class="font-medium text-gray-800">Contabilidade</h3>
                <p class="text-xs text-gray-500">Notas fiscais e certificados</p>
            </div>
        </a>
        <a href="#amigo-pay" class="flex items-center p-3 rounded-md border border-gray-200 hover:bg-blue-50 hover:border-blue-200 transition-colors">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <div>
                <h3 class="font-medium text-gray-800">Amigo Pay</h3>
                <p class="text-xs text-gray-500">Transações e contas financeiras</p>
            </div>
        </a>
        <a href="#agenda" class="flex items-center p-3 rounded-md border border-gray-200 hover:bg-blue-50 hover:border-blue-200 transition-colors">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>
            <div>
                <h3 class="font-medium text-gray-800">Agenda</h3>
                <p class="text-xs text-gray-500">Eventos e agendamentos</p>
            </div>
        </a>
        <a href="#esquema" class="flex items-center p-3 rounded-md border border-gray-200 hover:bg-blue-50 hover:border-blue-200 transition-colors">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"></path>
                </svg>
            </div>
            <div>
                <h3 class="font-medium text-gray-800">Esquema Visual</h3>
                <p class="text-xs text-gray-500">Diagrama de relacionamentos</p>
            </div>
        </a>
    </div>
</div>

<!-- Conteúdo da Documentação - Parte 1 -->
<div id="estrutura" class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Estrutura da Base de Dados</h2>
    <p class="text-gray-600 mb-6">O Amigo One utiliza um modelo de banco de dados relacional com tabelas dimensionais (dim_) e tabelas de fatos (fat_). Abaixo está a estrutura geral das principais tabelas:</p>

    <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
            <thead>
                <tr>
                    <th class="py-3 px-4 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Tipo</th>
                    <th class="py-3 px-4 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Tabela</th>
                    <th class="py-3 px-4 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Descrição</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                <tr>
                    <td class="py-2 px-4 text-sm text-gray-700">Dimensão</td>
                    <td class="py-2 px-4 text-sm font-medium text-gray-900">dim_company</td>
                    <td class="py-2 px-4 text-sm text-gray-700">Informações sobre empresas/clínicas</td>
                </tr>
                <tr>
                    <td class="py-2 px-4 text-sm text-gray-700">Dimensão</td>
                    <td class="py-2 px-4 text-sm font-medium text-gray-900">dim_user</td>
                    <td class="py-2 px-4 text-sm text-gray-700">Informações sobre usuários médicos</td>
                </tr>
                <tr>
                    <td class="py-2 px-4 text-sm text-gray-700">Fato</td>
                    <td class="py-2 px-4 text-sm font-medium text-gray-900">fat_attendance_records</td>
                    <td class="py-2 px-4 text-sm text-gray-700">Registros de atendimentos médicos</td>
                </tr>
                <tr>
                    <td class="py-2 px-4 text-sm text-gray-700">Fato</td>
                    <td class="py-2 px-4 text-sm font-medium text-gray-900">fat_conexoes</td>
                    <td class="py-2 px-4 text-sm text-gray-700">Conexões entre profissionais</td>
                </tr>
                <tr>
                    <td class="py-2 px-4 text-sm text-gray-700">Fato</td>
                    <td class="py-2 px-4 text-sm font-medium text-gray-900">fat_user_devices</td>
                    <td class="py-2 px-4 text-sm text-gray-700">Dispositivos utilizados pelos usuários</td>
                </tr>
                <tr>
                    <td class="py-2 px-4 text-sm text-gray-700">Fato</td>
                    <td class="py-2 px-4 text-sm font-medium text-gray-900">app_subscriptions</td>
                    <td class="py-2 px-4 text-sm text-gray-700">Assinaturas de usuários (Premium/Free)</td>
                </tr>
                <tr>
                    <td class="py-2 px-4 text-sm text-gray-700">Fato</td>
                    <td class="py-2 px-4 text-sm font-medium text-gray-900">chat_requests</td>
                    <td class="py-2 px-4 text-sm text-gray-700">Solicitações de chat com IA</td>
                </tr>
                <tr>
                    <td class="py-2 px-4 text-sm text-gray-700">Fato</td>
                    <td class="py-2 px-4 text-sm font-medium text-gray-900">chat_files</td>
                    <td class="py-2 px-4 text-sm text-gray-700">Arquivos gerados pela IA (imagens, PDFs)</td>
                </tr>
                <tr>
                    <td class="py-2 px-4 text-sm text-gray-700">Fato</td>
                    <td class="py-2 px-4 text-sm font-medium text-gray-900">finance_nfses</td>
                    <td class="py-2 px-4 text-sm text-gray-700">Notas fiscais emitidas</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Usuários -->
<div id="usuarios" class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Usuários</h2>
    <p class="text-gray-600 mb-6">O módulo de usuários contém informações sobre os médicos e profissionais de saúde que utilizam o Amigo One, incluindo dados demográficos, especialidades e dispositivos utilizados.</p>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
            <h3 class="text-lg font-medium text-gray-800 mb-3">Tabela dim_user</h3>
            <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                <ul class="space-y-2 text-sm">
                    <li><span class="font-medium">user_id:</span> Identificador único do usuário</li>
                    <li><span class="font-medium">user_name:</span> Nome do usuário</li>
                    <li><span class="font-medium">specialty_name:</span> Especialidade médica</li>
                    <li><span class="font-medium">gender:</span> Gênero do usuário</li>
                    <li><span class="font-medium">address_city, address_state:</span> Localização</li>
                    <li><span class="font-medium">born:</span> Data de nascimento</li>
                    <li><span class="font-medium">ind_amigo_one:</span> Flag para usuários Amigo One</li>
                    <li><span class="font-medium">ind_premium:</span> Status de assinatura premium</li>
                    <li><span class="font-medium">total_patients_created:</span> Número de pacientes criados</li>
                </ul>
            </div>
        </div>

        <div>
            <h3 class="text-lg font-medium text-gray-800 mb-3">Tabela fat_user_devices</h3>
            <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                <ul class="space-y-2 text-sm">
                    <li><span class="font-medium">user_id:</span> Usuário associado</li>
                    <li><span class="font-medium">marca:</span> Marca do dispositivo</li>
                    <li><span class="font-medium">aparelho:</span> Nome do dispositivo</li>
                    <li><span class="font-medium">modelo:</span> Modelo do dispositivo</li>
                    <li><span class="font-medium">tipo:</span> Tipo de dispositivo</li>
                    <li><span class="font-medium">sistema:</span> Sistema operacional</li>
                    <li><span class="font-medium">versao_sistema:</span> Versão do SO</li>
                    <li><span class="font-medium">status:</span> Status do dispositivo</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="bg-blue-50 p-4 rounded-md border border-blue-200 mb-6">
        <h3 class="text-md font-medium text-blue-800 mb-2 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            Informações Importantes
        </h3>
        <ul class="space-y-1 text-sm text-blue-700">
            <li>• Usuários Premium são identificados na tabela <code>app_subscriptions</code> com status "ACTIVE"</li>
            <li>• Usuários Free são todos os usuários que não são Premium</li>
            <li>• Para análise de dispositivos, é necessário filtrar apenas dispositivos móveis (excluir Desktop)</li>
            <li>• A distribuição demográfica pode ser analisada pelas colunas <code>address_city</code> e <code>address_state</code></li>
            <li>• O perfil de idade dos usuários pode ser calculado a partir da coluna <code>born</code></li>
        </ul>
    </div>

    <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 class="text-md font-medium text-gray-800 mb-2">Métricas Principais</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Usuários por Tipo</h4>
                <p class="text-xs text-gray-500">Premium vs. Free</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Distribuição por Especialidade</h4>
                <p class="text-xs text-gray-500">Segmentação por área médica</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Distribuição Geográfica</h4>
                <p class="text-xs text-gray-500">Por estado/cidade</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Perfil de Idade</h4>
                <p class="text-xs text-gray-500">Faixas etárias dos usuários</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Dispositivos</h4>
                <p class="text-xs text-gray-500">iOS vs. Android</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Crescimento</h4>
                <p class="text-xs text-gray-500">Evolução mensal de usuários</p>
            </div>
        </div>
    </div>
</div>

<!-- Prontuários -->
<div id="prontuarios" class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Prontuários</h2>
    <p class="text-gray-600 mb-6">O módulo de prontuários contém informações sobre os registros médicos criados pelos usuários, incluindo diferentes tipos de documentos, assinaturas digitais e telemedicina.</p>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
            <h3 class="text-lg font-medium text-gray-800 mb-3">Tabela fat_attendance_records</h3>
            <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                <ul class="space-y-2 text-sm">
                    <li><span class="font-medium">user_id:</span> Usuário que criou o registro</li>
                    <li><span class="font-medium">company_id:</span> Empresa associada</li>
                    <li><span class="font-medium">type:</span> Tipo de registro (TELEMEDICINE, CERTIFICATE, MEDICINE, etc.)</li>
                    <li><span class="font-medium">subtype:</span> Subtipo do registro</li>
                    <li><span class="font-medium">has_digital_signature:</span> Se possui assinatura digital</li>
                    <li><span class="font-medium">data_referencia:</span> Data de referência</li>
                    <li><span class="font-medium">qtd:</span> Quantidade</li>
                </ul>
            </div>
        </div>

        <div>
            <h3 class="text-lg font-medium text-gray-800 mb-3">Tipos de Lançamentos</h3>
            <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                <ul class="space-y-2 text-sm">
                    <li><span class="font-medium">Anamnese</span> <span class="text-xs text-gray-500">(type CUSTOM, subtype ANAMNESE)</span></li>
                    <li><span class="font-medium">Anotação</span> <span class="text-xs text-gray-500">(type TEXT, sem subtype)</span></li>
                    <li><span class="font-medium">Evolução</span> <span class="text-xs text-gray-500">(type EVOLUTION, sem subtype)</span></li>
                    <li><span class="font-medium">Imagem</span> <span class="text-xs text-gray-500">(type PHOTO, sem subtype)</span></li>
                    <li><span class="font-medium">Teleconsulta</span> <span class="text-xs text-gray-500">(type TELEMEDICINE, sem subtype)</span></li>
                    <li><span class="font-medium">Receita Digital</span> <span class="text-xs text-gray-500">(type MEDICINE, sem subtype)</span></li>
                    <li><span class="font-medium">Solicitação de Exame</span> <span class="text-xs text-gray-500">(type EXAM_REQUEST, sem subtype)</span></li>
                    <li><span class="font-medium">Atestado</span> <span class="text-xs text-gray-500">(type CERTIFICATE, subtype ATESTADO)</span></li>
                    <li><span class="font-medium">Outros</span> <span class="text-xs text-gray-500">(type CERTIFICATE, subtype OUTROS)</span></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="bg-blue-50 p-4 rounded-md border border-blue-200 mb-6">
        <h3 class="text-md font-medium text-blue-800 mb-2 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            Informações Importantes
        </h3>
        <ul class="space-y-1 text-sm text-blue-700">
            <li>• Documentos assinados têm <code>has_digital_signature</code> como TRUE</li>
            <li>• Documentos sem assinatura têm <code>has_digital_signature</code> como FALSE</li>
            <li>• Medicamentos adicionados manualmente são registrados na tabela <code>company_medicaments</code></li>
            <li>• Telemedicinas realizadas são registradas na tabela <code>telemedicine_rooms</code></li>
            <li>• A duração média das teleconsultas pode ser calculada a partir dos registros de telemedicina</li>
            <li>• Pacientes são gerenciados na tabela <code>patients</code></li>
        </ul>
    </div>

    <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 class="text-md font-medium text-gray-800 mb-2">Métricas Principais</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Tipos de Registros</h4>
                <p class="text-xs text-gray-500">Distribuição por tipo de documento</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Assinatura Digital</h4>
                <p class="text-xs text-gray-500">Taxa de uso de assinatura</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Telemedicina</h4>
                <p class="text-xs text-gray-500">Volume e duração de teleconsultas</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Receitas Digitais</h4>
                <p class="text-xs text-gray-500">Medicamentos mais prescritos</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Exames</h4>
                <p class="text-xs text-gray-500">Tipos de exames solicitados</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Pacientes</h4>
                <p class="text-xs text-gray-500">Média de pacientes por usuário</p>
            </div>
        </div>
    </div>
</div>

<!-- Amigo Intelligence -->
<div id="amigo-intelligence" class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Amigo Intelligence</h2>
    <p class="text-gray-600 mb-6">O módulo Amigo Intelligence contém informações sobre o uso da inteligência artificial no aplicativo, incluindo chats, prompts e arquivos gerados.</p>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
            <h3 class="text-lg font-medium text-gray-800 mb-3">Tabela chat_requests</h3>
            <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                <ul class="space-y-2 text-sm">
                    <li><span class="font-medium">id:</span> Identificador único do chat</li>
                    <li><span class="font-medium">user_id:</span> Usuário que iniciou o chat</li>
                    <li><span class="font-medium">prompt:</span> Texto enviado pelo usuário</li>
                    <li><span class="font-medium">response:</span> Resposta da IA</li>
                    <li><span class="font-medium">created_at:</span> Data de criação</li>
                    <li><span class="font-medium">status:</span> Status da solicitação</li>
                </ul>
            </div>
        </div>

        <div>
            <h3 class="text-lg font-medium text-gray-800 mb-3">Tabela chat_files</h3>
            <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                <ul class="space-y-2 text-sm">
                    <li><span class="font-medium">id:</span> Identificador único do arquivo</li>
                    <li><span class="font-medium">chat_id:</span> ID do chat relacionado</li>
                    <li><span class="font-medium">file_type:</span> Tipo do arquivo (image/jpeg, application/pdf)</li>
                    <li><span class="font-medium">file_name:</span> Nome do arquivo</li>
                    <li><span class="font-medium">file_size:</span> Tamanho do arquivo</li>
                    <li><span class="font-medium">created_at:</span> Data de criação</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="bg-blue-50 p-4 rounded-md border border-blue-200 mb-6">
        <h3 class="text-md font-medium text-blue-800 mb-2 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            Informações Importantes
        </h3>
        <ul class="space-y-1 text-sm text-blue-700">
            <li>• Os prompts referenciados são registrados na tabela <code>chat_requests</code></li>
            <li>• Os tipos de prompts gerados podem ser:
                <ul class="ml-4 mt-1 space-y-1">
                    <li>- Imagem/Foto (tipo "image/jpeg" na tabela <code>chat_files</code>)</li>
                    <li>- PDF (tipo "application/pdf" na tabela <code>chat_files</code>)</li>
                    <li>- Apenas texto (sem referência na tabela <code>chat_files</code>)</li>
                </ul>
            </li>
            <li>• Chats abertos são registrados com status específico</li>
            <li>• Áudios transcritos ainda não são registrados no banco</li>
            <li>• Usuários que utilizaram a IA pelo menos uma vez podem ser identificados pela presença na tabela <code>chat_requests</code></li>
        </ul>
    </div>

    <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 class="text-md font-medium text-gray-800 mb-2">Métricas Principais</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Total de Chats</h4>
                <p class="text-xs text-gray-500">Número de interações com a IA</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Tipos de Prompts</h4>
                <p class="text-xs text-gray-500">Texto, imagem e PDF</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Taxa de Adoção</h4>
                <p class="text-xs text-gray-500">Percentual de usuários que usam IA</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Tempo de Resposta</h4>
                <p class="text-xs text-gray-500">Tempo médio para gerar respostas</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Tópicos Populares</h4>
                <p class="text-xs text-gray-500">Assuntos mais consultados</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Transcrição de Áudio</h4>
                <p class="text-xs text-gray-500">Uso da funcionalidade de transcrição</p>
            </div>
        </div>
    </div>
</div>

<!-- Uso de Funcionalidades por Mês - Container Grande -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800">Uso de Funcionalidades por Mês</h2>
        <div class="flex space-x-2">
            <div class="relative">
                <select class="bg-white border border-gray-300 text-gray-700 py-1 px-3 pr-8 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none">
                    <option>Comparar Ano a Ano (Y/Y)</option>
                    <option>Comparar Mês a Mês (M/M)</option>
                    <option>Comparar Dia a Dia (D/D)</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
            </div>
            <div class="relative">
                <select class="bg-white border border-gray-300 text-gray-700 py-1 px-3 pr-8 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none">
                    <option>Todos os Tipos</option>
                    <option>Receita Digital</option>
                    <option>Solicitação de Exame</option>
                    <option>Teleconsulta</option>
                    <option>Atestado</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <div class="h-96 mb-4">
        <canvas id="functionalityUsageChart"></canvas>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-sm font-medium text-gray-800">Receita Digital</h3>
                <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">+15.2%</span>
            </div>
            <p class="text-2xl font-semibold text-gray-800 mt-2">3,876</p>
            <p class="text-xs text-gray-500 mt-1">Crescimento constante</p>
        </div>
        <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-sm font-medium text-gray-800">Solicitação de Exame</h3>
                <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">+12.8%</span>
            </div>
            <p class="text-2xl font-semibold text-gray-800 mt-2">2,543</p>
            <p class="text-xs text-gray-500 mt-1">Segundo mais utilizado</p>
        </div>
        <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-sm font-medium text-gray-800">Teleconsulta</h3>
                <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">+25.4%</span>
            </div>
            <p class="text-2xl font-semibold text-gray-800 mt-2">1,245</p>
            <p class="text-xs text-gray-500 mt-1">Maior taxa de crescimento</p>
        </div>
        <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-sm font-medium text-gray-800">Atestado</h3>
                <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">****%</span>
            </div>
            <p class="text-2xl font-semibold text-gray-800 mt-2">987</p>
            <p class="text-xs text-gray-500 mt-1">Uso consistente</p>
        </div>
    </div>
</div>

<!-- Rede Amigo -->
<div id="rede-amigo" class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Rede Amigo</h2>
    <p class="text-gray-600 mb-6">O módulo Rede Amigo contém informações sobre a rede social profissional do aplicativo, incluindo conexões entre médicos e interações com o feed de conteúdo.</p>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
            <h3 class="text-lg font-medium text-gray-800 mb-3">Tabela fat_conexoes</h3>
            <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                <ul class="space-y-2 text-sm">
                    <li><span class="font-medium">data_referencia:</span> Data de referência</li>
                    <li><span class="font-medium">status:</span> Status da conexão (PENDING, ACCEPTED)</li>
                    <li><span class="font-medium">profile_id:</span> ID do perfil do usuário</li>
                    <li><span class="font-medium">name:</span> Nome do usuário</li>
                    <li><span class="font-medium">quantidade:</span> Quantidade</li>
                </ul>
            </div>
        </div>

        <div>
            <h3 class="text-lg font-medium text-gray-800 mb-3">Interações com o Feed</h3>
            <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                <ul class="space-y-2 text-sm">
                    <li><span class="font-medium">Curtidas:</span> Registradas como total_likes</li>
                    <li><span class="font-medium">Comentários:</span> Registrados como total_comments</li>
                    <li><span class="font-medium">Compartilhamentos:</span> Registrados como total_shares</li>
                    <li><span class="font-medium">Visualizações:</span> Registradas por publicação</li>
                    <li><span class="font-medium">Tempo no Feed:</span> Tempo médio de navegação</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="bg-blue-50 p-4 rounded-md border border-blue-200 mb-6">
        <h3 class="text-md font-medium text-blue-800 mb-2 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            Informações Importantes
        </h3>
        <ul class="space-y-1 text-sm text-blue-700">
            <li>• Convites pendentes têm status PENDING e is_deleted FALSE</li>
            <li>• Conexões realizadas têm status ACCEPTED e is_deleted FALSE</li>
            <li>• As interações com o feed são contabilizadas por:
                <ul class="ml-4 mt-1 space-y-1">
                    <li>- Curtidas (total_likes)</li>
                    <li>- Comentários (total_comments)</li>
                    <li>- Compartilhamentos (total_shares)</li>
                </ul>
            </li>
            <li>• A taxa de engajamento é calculada como interações divididas por visualizações</li>
            <li>• O tempo médio no feed é um indicador importante de engajamento</li>
        </ul>
    </div>

    <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 class="text-md font-medium text-gray-800 mb-2">Métricas Principais</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Conexões</h4>
                <p class="text-xs text-gray-500">Total de conexões entre profissionais</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Taxa de Aceitação</h4>
                <p class="text-xs text-gray-500">Convites aceitos vs. pendentes</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Interações</h4>
                <p class="text-xs text-gray-500">Curtidas, comentários e compartilhamentos</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Taxa de Visualização</h4>
                <p class="text-xs text-gray-500">Percentual de usuários que veem publicações</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Taxa de Engajamento</h4>
                <p class="text-xs text-gray-500">Interações por visualização</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Tempo no Feed</h4>
                <p class="text-xs text-gray-500">Tempo médio de navegação</p>
            </div>
        </div>
    </div>
</div>

<!-- Amigo Pay -->
<div id="amigo-pay" class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Amigo Pay</h2>
    <p class="text-gray-600 mb-6">O módulo Amigo Pay contém informações sobre transações financeiras, contas ativas e volume de movimentação dos usuários do Amigo One.</p>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
            <h3 class="text-lg font-medium text-gray-800 mb-3">Tabela finance_transactions</h3>
            <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                <ul class="space-y-2 text-sm">
                    <li><span class="font-medium">id:</span> Identificador único da transação</li>
                    <li><span class="font-medium">user_id:</span> Usuário associado</li>
                    <li><span class="font-medium">type:</span> Tipo de transação (PIX, CARD, BANK_SLIP, etc.)</li>
                    <li><span class="font-medium">status:</span> Status da transação</li>
                    <li><span class="font-medium">amount:</span> Valor da transação (em centavos)</li>
                    <li><span class="font-medium">created_at:</span> Data de criação</li>
                    <li><span class="font-medium">provider:</span> Provedor da transação (dock, celcoin)</li>
                </ul>
            </div>
        </div>

        <div>
            <h3 class="text-lg font-medium text-gray-800 mb-3">Tabela finance_accounts</h3>
            <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                <ul class="space-y-2 text-sm">
                    <li><span class="font-medium">id:</span> Identificador único da conta</li>
                    <li><span class="font-medium">user_id:</span> Usuário associado</li>
                    <li><span class="font-medium">status:</span> Status da conta</li>
                    <li><span class="font-medium">provider:</span> Provedor da conta (dock, celcoin)</li>
                    <li><span class="font-medium">kyc_status:</span> Status de verificação KYC</li>
                    <li><span class="font-medium">has_movement:</span> Se possui movimentação</li>
                    <li><span class="font-medium">created_at:</span> Data de criação</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="bg-blue-50 p-4 rounded-md border border-blue-200 mb-6">
        <h3 class="text-md font-medium text-blue-800 mb-2 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            Informações Importantes
        </h3>
        <ul class="space-y-1 text-sm text-blue-700">
            <li>• Os tipos de transações incluem:
                <ul class="ml-4 mt-1 space-y-1">
                    <li>- PIX: Transferências via PIX</li>
                    <li>- CARD: Pagamentos com cartão</li>
                    <li>- BANK_SLIP: Pagamentos com boleto</li>
                    <li>- TRANSFER: Transferências bancárias</li>
                    <li>- TED: Transferências eletrônicas</li>
                    <li>- PIX_REVERSAL: Estornos de PIX</li>
                </ul>
            </li>
            <li>• Contas com KYC aprovado têm <code>kyc_status</code> como APPROVED</li>
            <li>• Contas com movimentação têm <code>has_movement</code> como TRUE</li>
            <li>• Os provedores de serviços financeiros são Dock e Celcoin</li>
            <li>• O volume de transações é calculado somando o campo <code>amount</code> (em centavos)</li>
            <li>• O ticket médio é calculado dividindo o volume total pelo número de transações</li>
        </ul>
    </div>

    <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 class="text-md font-medium text-gray-800 mb-2">Métricas Principais</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Contas Ativas</h4>
                <p class="text-xs text-gray-500">Total de contas com movimentação</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Transações</h4>
                <p class="text-xs text-gray-500">Volume e quantidade de transações</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Tipos de Transações</h4>
                <p class="text-xs text-gray-500">Distribuição por tipo (PIX, cartão, etc.)</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">KYC</h4>
                <p class="text-xs text-gray-500">Taxa de aprovação de verificação</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Ticket Médio</h4>
                <p class="text-xs text-gray-500">Valor médio por transação</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Provedores</h4>
                <p class="text-xs text-gray-500">Distribuição entre Dock e Celcoin</p>
            </div>
        </div>
    </div>
</div>

<!-- Contabilidade -->
<div id="contabilidade" class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Contabilidade</h2>
    <p class="text-gray-600 mb-6">O módulo de Contabilidade contém informações sobre notas fiscais, certificados digitais e pendências contábeis dos usuários do Amigo One.</p>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
            <h3 class="text-lg font-medium text-gray-800 mb-3">Tabela finance_nfses</h3>
            <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                <ul class="space-y-2 text-sm">
                    <li><span class="font-medium">id:</span> Identificador único da nota fiscal</li>
                    <li><span class="font-medium">user_id:</span> Usuário que emitiu a nota</li>
                    <li><span class="font-medium">company_id:</span> Empresa associada</li>
                    <li><span class="font-medium">status:</span> Status da nota (AUTORIZADA, CANCELADA, REJEITADA)</li>
                    <li><span class="font-medium">new_attendance_id:</span> ID do atendimento (se emitida via agenda)</li>
                    <li><span class="font-medium">created_at:</span> Data de emissão</li>
                    <li><span class="font-medium">valor:</span> Valor da nota fiscal</li>
                </ul>
            </div>
        </div>

        <div>
            <h3 class="text-lg font-medium text-gray-800 mb-3">Certificados Digitais</h3>
            <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                <ul class="space-y-2 text-sm">
                    <li><span class="font-medium">Certificados Válidos:</span> Certificados ativos e dentro da validade</li>
                    <li><span class="font-medium">Certificados Expirados:</span> Certificados que precisam ser renovados</li>
                    <li><span class="font-medium">Sem Certificado:</span> Usuários que não possuem certificado digital</li>
                    <li><span class="font-medium">Certificado A1:</span> Armazenado no computador</li>
                    <li><span class="font-medium">Certificado A3:</span> Armazenado em dispositivo externo</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="bg-blue-50 p-4 rounded-md border border-blue-200 mb-6">
        <h3 class="text-md font-medium text-blue-800 mb-2 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            Informações Importantes
        </h3>
        <ul class="space-y-1 text-sm text-blue-700">
            <li>• NFS-e emitidas são registradas na tabela <code>finance_nfses</code></li>
            <li>• NFS-e com sucesso têm status AUTORIZADA</li>
            <li>• NFS-e com erro têm status CANCELADA ou REJEITADA</li>
            <li>• NFS-e a partir da agenda têm valor em <code>new_attendance_id</code></li>
            <li>• NFS-e avulsa não tem valor em <code>new_attendance_id</code></li>
            <li>• Pendências contábeis comuns incluem:
                <ul class="ml-4 mt-1 space-y-1">
                    <li>- Falta de sócio cadastrado</li>
                    <li>- Falta de registro eSocial</li>
                    <li>- Falta de procuração válida</li>
                    <li>- Falta de certificado válido</li>
                    <li>- Certificado expirado</li>
                </ul>
            </li>
        </ul>
    </div>

    <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 class="text-md font-medium text-gray-800 mb-2">Métricas Principais</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Notas Fiscais</h4>
                <p class="text-xs text-gray-500">Total de NFS-e emitidas</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Taxa de Sucesso</h4>
                <p class="text-xs text-gray-500">NFS-e com sucesso vs. erro</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Origem das Notas</h4>
                <p class="text-xs text-gray-500">Agenda vs. avulsas</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Certificados</h4>
                <p class="text-xs text-gray-500">Status dos certificados digitais</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Pendências</h4>
                <p class="text-xs text-gray-500">Principais pendências contábeis</p>
            </div>
            <div class="bg-white p-3 rounded-md border border-gray-200">
                <h4 class="text-sm font-medium text-gray-700">Faturamento</h4>
                <p class="text-xs text-gray-500">Valor total faturado</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Adicionar comportamento de scroll suave para links de âncora
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 100,
                        behavior: 'smooth'
                    });
                }
            });
        });
    });
</script>
{% endblock %}
