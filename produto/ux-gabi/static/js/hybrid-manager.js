/**
 * HYBRID STRUCTURED MANAGER
 * Manages the structured hybrid pattern across Business and Product domains
 * Provides clear separation between SSR and CSR responsibilities
 */

class HybridManager {
    constructor(config = {}) {
        this.config = {
            updateInterval: config.updateInterval || 60000, // 1 minute default
            retryAttempts: config.retryAttempts || 3,
            retryDelay: config.retryDelay || 5000,
            debug: config.debug || false,
            ...config
        };
        
        this.activeUpdaters = new Map();
        this.retryCounters = new Map();
        
        this.log('HybridManager initialized', this.config);
    }

    /**
     * Initialize hybrid components on page load
     * SSR: Use server-rendered data for initial display
     * CSR: Setup dynamic updates for real-time data
     */
    init() {
        this.log('Initializing hybrid components');
        
        // Initialize all metric cards with dynamic capabilities
        this.initializeMetricCards();
        
        // Initialize all chart containers with API endpoints
        this.initializeChartContainers();
        
        // Setup global error handling for dynamic updates
        this.setupErrorHandling();
        
        this.log('Hybrid initialization complete');
    }

    /**
     * Initialize metric cards that have API endpoints
     * Pattern: Static initial value (SSR) + Dynamic updates (CSR)
     */
    initializeMetricCards() {
        const metricCards = document.querySelectorAll('[data-api-endpoint][data-metric-id]');
        
        metricCards.forEach(card => {
            const endpoint = card.dataset.apiEndpoint;
            const metricId = card.dataset.metricId;
            
            if (endpoint && metricId) {
                this.log(`Setting up dynamic updates for metric: ${metricId}`);
                this.setupDynamicUpdates(endpoint, metricId);
            }
        });
    }

    /**
     * Initialize chart containers with API data loading
     * Pattern: Static container (SSR) + Dynamic data loading (CSR)
     */
    initializeChartContainers() {
        const chartContainers = document.querySelectorAll('[data-chart-api]');
        
        chartContainers.forEach(container => {
            const apiEndpoint = container.dataset.chartApi;
            const chartId = container.querySelector('canvas')?.id;
            
            if (apiEndpoint && chartId) {
                this.log(`Setting up chart data loading for: ${chartId}`);
                this.loadChartData(apiEndpoint, chartId);
            }
        });
    }

    /**
     * Setup dynamic updates for a specific metric
     * Uses polling with exponential backoff on errors
     */
    setupDynamicUpdates(endpoint, metricId) {
        const updateFunction = async () => {
            try {
                const response = await fetch(endpoint);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                this.updateMetricValue(metricId, data);
                
                // Reset retry counter on success
                this.retryCounters.delete(metricId);
                
            } catch (error) {
                this.handleUpdateError(metricId, error, endpoint);
            }
        };

        // Initial update
        updateFunction();
        
        // Setup periodic updates
        const intervalId = setInterval(updateFunction, this.config.updateInterval);
        this.activeUpdaters.set(metricId, intervalId);
    }

    /**
     * Load chart data from API endpoint
     * Pattern: Show loading state, fetch data, render chart or show error
     */
    async loadChartData(endpoint, chartId) {
        const chartContainer = document.getElementById(chartId)?.parentElement;
        
        if (!chartContainer) {
            this.log(`Chart container not found for: ${chartId}`, 'error');
            return;
        }

        try {
            // Show loading state
            this.showChartLoading(chartContainer);
            
            const response = await fetch(endpoint);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            // Hide loading and render chart
            this.hideChartLoading(chartContainer);
            this.renderChart(chartId, data);
            
        } catch (error) {
            this.log(`Chart loading failed for ${chartId}: ${error.message}`, 'error');
            this.showChartError(chartContainer, error.message);
        }
    }

    /**
     * Update metric value in the DOM
     * Preserves initial server-rendered value as fallback
     */
    updateMetricValue(metricId, data) {
        const element = document.getElementById(metricId);
        
        if (!element) {
            this.log(`Metric element not found: ${metricId}`, 'error');
            return;
        }

        // Update main value
        if (data.value !== undefined) {
            element.textContent = data.value;
        }

        // Update growth indicator if present
        const growthElement = document.getElementById(`${metricId}Growth`);
        if (growthElement && data.growth !== undefined) {
            growthElement.textContent = `${data.growth > 0 ? '+' : ''}${data.growth}%`;
            
            // Update growth color
            growthElement.className = growthElement.className.replace(
                /(text-success|text-danger)/g, 
                data.growth >= 0 ? 'text-success' : 'text-danger'
            );
        }

        this.log(`Updated metric ${metricId}:`, data);
    }

    /**
     * Handle update errors with retry logic
     */
    handleUpdateError(metricId, error, endpoint) {
        const retryCount = this.retryCounters.get(metricId) || 0;
        
        this.log(`Update failed for ${metricId} (attempt ${retryCount + 1}): ${error.message}`, 'error');
        
        if (retryCount < this.config.retryAttempts) {
            this.retryCounters.set(metricId, retryCount + 1);
            
            // Retry with exponential backoff
            setTimeout(() => {
                this.setupDynamicUpdates(endpoint, metricId);
            }, this.config.retryDelay * Math.pow(2, retryCount));
        } else {
            this.log(`Max retry attempts reached for ${metricId}`, 'error');
            this.showMetricError(metricId);
        }
    }

    /**
     * Show loading state for charts
     */
    showChartLoading(container) {
        const canvas = container.querySelector('canvas');
        if (canvas) {
            canvas.style.display = 'none';
        }
        
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'chart-loading flex items-center justify-center h-full';
        loadingDiv.innerHTML = `
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span class="ml-3 text-gray-600">Carregando gráfico...</span>
        `;
        
        container.appendChild(loadingDiv);
    }

    /**
     * Hide loading state for charts
     */
    hideChartLoading(container) {
        const loadingDiv = container.querySelector('.chart-loading');
        if (loadingDiv) {
            loadingDiv.remove();
        }
        
        const canvas = container.querySelector('canvas');
        if (canvas) {
            canvas.style.display = 'block';
        }
    }

    /**
     * Show error state for charts
     */
    showChartError(container, message) {
        this.hideChartLoading(container);
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'chart-error flex items-center justify-center h-full text-gray-500';
        errorDiv.innerHTML = `
            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <span>Erro ao carregar: ${message}</span>
        `;
        
        container.appendChild(errorDiv);
    }

    /**
     * Show error state for metrics
     */
    showMetricError(metricId) {
        const element = document.getElementById(metricId);
        if (element) {
            element.textContent = 'Erro';
            element.className += ' text-danger';
        }
    }

    /**
     * Render chart with data (placeholder - implement specific chart logic)
     */
    renderChart(chartId, data) {
        this.log(`Rendering chart ${chartId} with data:`, data);
        // Chart-specific rendering logic should be implemented here
        // This is a placeholder that can be extended by specific chart libraries
    }

    /**
     * Setup global error handling
     */
    setupErrorHandling() {
        window.addEventListener('unhandledrejection', (event) => {
            this.log(`Unhandled promise rejection: ${event.reason}`, 'error');
        });
    }

    /**
     * Cleanup function to stop all active updaters
     */
    cleanup() {
        this.activeUpdaters.forEach((intervalId, metricId) => {
            clearInterval(intervalId);
            this.log(`Stopped updates for metric: ${metricId}`);
        });
        
        this.activeUpdaters.clear();
        this.retryCounters.clear();
    }

    /**
     * Logging utility
     */
    log(message, level = 'info') {
        if (this.config.debug || level === 'error') {
            const timestamp = new Date().toISOString();
            const logMessage = `[HybridManager ${timestamp}] ${message}`;

            // Use appropriate console method
            if (level === 'error' && console.error) {
                console.error(logMessage);
            } else if (level === 'warn' && console.warn) {
                console.warn(logMessage);
            } else if (console.log) {
                console.log(logMessage);
            }
        }
    }
}

// Export for use in both domains
window.HybridManager = HybridManager;
