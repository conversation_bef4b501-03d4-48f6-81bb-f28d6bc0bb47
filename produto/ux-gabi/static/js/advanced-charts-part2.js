/**
 * Advanced Charts for Amigo One Dashboard - Parte 2
 * Implementação de gráficos adicionais usando ApexCharts
 */

document.addEventListener('DOMContentLoaded', function() {
    // Dados simulados para os gráficos
    const dashboardData = window.dashboardData || {
        users: {
            users_by_state: {
                'SP': 35,
                'RJ': 22,
                'MG': 18,
                'RS': 8,
                'PR': 7,
                'BA': 5,
                'SC': 4,
                'DF': 3,
                'Outros': 8
            }
        },
        features: {
            usage_by_product: {
                'Prontuário': 92,
                'Agenda': 87,
                'Amigo Intelligence': 65,
                'Amigo Pay': 48,
                'Rede Amigo': 42,
                'Contabilidade': 38,
                'Telemedicina': 35
            }
        },
        finance: {
            monthly_revenue: [120000, 135000, 142000, 158000, 172000, 185000, 198000, 210000],
            transaction_types: {
                'PIX': 45,
                'Cartão': 31,
                'Boleto': 15,
                'Transferência': 9
            }
        }
    };

    // Compartilhar os dados com outros scripts
    window.dashboardData = dashboardData;

    const segmentData = {
        age: {
            categories: ['Tempo de Sessão (min)', 'Frequência Semanal', 'Features Utilizadas', 'Taxa de Retenção (%)'],
            series: [
                {
                    name: '25-30 anos',
                    data: [22, 4.2, 3.8, 72]
                },
                {
                    name: '31-40 anos',
                    data: [27, 5.1, 4.5, 85]
                },
                {
                    name: '41-50 anos',
                    data: [24, 3.8, 3.2, 87]
                }
            ]
        },
        specialty: {
            categories: ['Tempo de Sessão (min)', 'Frequência Semanal', 'Features Utilizadas', 'Taxa de Retenção (%)'],
            series: [
                {
                    name: 'Cardiologia',
                    data: [26, 4.8, 4.2, 83]
                },
                {
                    name: 'Dermatologia',
                    data: [23, 4.2, 3.5, 79]
                },
                {
                    name: 'Pediatria',
                    data: [25, 5.0, 3.8, 81]
                }
            ]
        },
        region: {
            categories: ['Tempo de Sessão (min)', 'Frequência Semanal', 'Features Utilizadas', 'Taxa de Retenção (%)'],
            series: [
                {
                    name: 'Sudeste',
                    data: [25, 4.7, 4.3, 84]
                },
                {
                    name: 'Nordeste',
                    data: [22, 4.1, 3.6, 76]
                },
                {
                    name: 'Sul',
                    data: [24, 4.5, 4.0, 82]
                }
            ]
        }
    };

    // Gráfico de Comportamento por Segmento (ApexCharts)
    if (document.getElementById('segmentBehaviorChart')) {
        // Inicialmente mostrar dados por idade
        let currentSegmentType = 'age';

        const segmentOptions = {
            chart: {
                height: 350,
                type: 'radar',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                toolbar: {
                    show: false
                },
                dropShadow: {
                    enabled: true,
                    blur: 1,
                    left: 1,
                    top: 1
                }
            },
            colors: ['#007AFF', '#34C759', '#FF9500'],
            series: segmentData[currentSegmentType].series,
            stroke: {
                width: 2
            },
            fill: {
                opacity: 0.1
            },
            markers: {
                size: 4,
                hover: {
                    size: 6
                }
            },
            xaxis: {
                categories: segmentData[currentSegmentType].categories
            },
            yaxis: {
                max: 100,
                tickAmount: 5
            },
            dataLabels: {
                enabled: false
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val;
                    }
                }
            }
        };

        const segmentChart = new ApexCharts(document.getElementById('segmentBehaviorChart'), segmentOptions);
        segmentChart.render();

        // Adicionar event listener para o filtro
        if (document.getElementById('segmentFilter')) {
            document.getElementById('segmentFilter').addEventListener('change', function() {
                currentSegmentType = this.value;

                segmentChart.updateOptions({
                    xaxis: {
                        categories: segmentData[currentSegmentType].categories
                    }
                });

                segmentChart.updateSeries(segmentData[currentSegmentType].series);
            });
        }
    }

    // Dados para o mapa de calor
    const heatmapData = {
        features: ['Prontuário', 'Agenda', 'Amigo Intelligence', 'Amigo Pay', 'Rede Amigo', 'Contabilidade'],
        weekday: {
            days: ['Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado', 'Domingo'],
            data: [
                [75, 87, 82, 78, 73, 45, 30], // Prontuário
                [80, 85, 83, 80, 75, 40, 25], // Agenda
                [65, 75, 70, 72, 68, 30, 20], // Amigo Intelligence
                [45, 50, 48, 52, 55, 25, 15], // Amigo Pay
                [40, 45, 42, 44, 48, 30, 20], // Rede Amigo
                [70, 65, 60, 55, 75, 20, 10]  // Contabilidade
            ]
        },
        hourly: {
            hours: ['8-9h', '9-10h', '10-11h', '11-12h', '12-13h', '13-14h', '14-15h', '15-16h', '16-17h', '17-18h', '18-19h', '19-20h'],
            data: [
                [45, 75, 85, 70, 40, 55, 80, 75, 65, 50, 35, 25], // Prontuário
                [80, 85, 75, 65, 35, 50, 70, 65, 60, 55, 40, 30], // Agenda
                [30, 55, 70, 65, 25, 40, 75, 80, 70, 60, 45, 35], // Amigo Intelligence
                [20, 35, 45, 50, 30, 35, 55, 60, 55, 45, 35, 25], // Amigo Pay
                [15, 25, 35, 40, 20, 30, 45, 50, 45, 40, 30, 20], // Rede Amigo
                [40, 60, 65, 55, 25, 45, 70, 75, 65, 50, 35, 20]  // Contabilidade
            ]
        }
    };

    // Dados para o gráfico de uso por produto
    const productUsageData = {
        products: Object.keys(dashboardData.features.usage_by_product),
        values: Object.values(dashboardData.features.usage_by_product)
    };

    // Dados para o gráfico de contabilidade e financeiro
    const financeData = {
        months: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago'],
        revenue: dashboardData.finance.monthly_revenue,
        transaction_types: {
            labels: Object.keys(dashboardData.finance.transaction_types),
            values: Object.values(dashboardData.finance.transaction_types)
        }
    };

    // Mapa de Calor de Uso de Features (ApexCharts)
    if (document.getElementById('featureHeatmapChart')) {
        // Inicialmente mostrar dados por dia da semana
        let currentHeatmapType = 'weekday';

        function generateHeatmapSeries(type) {
            const labels = type === 'weekday' ? heatmapData.weekday.days : heatmapData.hourly.hours;
            const data = type === 'weekday' ? heatmapData.weekday.data : heatmapData.hourly.data;

            return heatmapData.features.map((feature, i) => {
                return {
                    name: feature,
                    data: labels.map((label, j) => {
                        return {
                            x: label,
                            y: data[i][j]
                        };
                    })
                };
            });
        }

        const heatmapOptions = {
            chart: {
                height: 580,
                type: 'heatmap',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: true,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    }
                },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800,
                    animateGradually: {
                        enabled: true,
                        delay: 150
                    },
                    dynamicAnimation: {
                        enabled: true,
                        speed: 350
                    }
                }
            },
            dataLabels: {
                enabled: true,
                formatter: function(val) {
                    return Math.round(val) + '%';
                },
                style: {
                    fontSize: '10px',
                    colors: ['#fff']
                }
            },
            colors: ["#007AFF"],
            series: generateHeatmapSeries(currentHeatmapType),
            title: {
                text: 'Uso de Features por Dia da Semana',
                align: 'center',
                style: {
                    fontSize: '14px',
                    fontWeight: 'normal',
                    color: '#333'
                }
            },
            tooltip: {
                custom: function({series, seriesIndex, dataPointIndex, w}) {
                    const feature = w.globals.seriesNames[seriesIndex];
                    const timeLabel = w.globals.labels[dataPointIndex];
                    const value = series[seriesIndex][dataPointIndex];

                    return `
                    <div class="apexcharts-tooltip-title" style="font-weight: bold; margin-bottom: 5px;">${feature}</div>
                    <div class="apexcharts-tooltip-series-group">
                        <span>${timeLabel}: <b>${value}%</b> de uso</span>
                    </div>
                    `;
                }
            },
            plotOptions: {
                heatmap: {
                    shadeIntensity: 0.5,
                    radius: 0,
                    useFillColorAsStroke: true,
                    colorScale: {
                        ranges: [
                            {
                                from: 0,
                                to: 20,
                                name: 'Muito Baixo',
                                color: '#E6F2FF'
                            },
                            {
                                from: 21,
                                to: 40,
                                name: 'Baixo',
                                color: '#99CCFF'
                            },
                            {
                                from: 41,
                                to: 60,
                                name: 'Médio',
                                color: '#3399FF'
                            },
                            {
                                from: 61,
                                to: 80,
                                name: 'Alto',
                                color: '#0066CC'
                            },
                            {
                                from: 81,
                                to: 100,
                                name: 'Muito Alto',
                                color: '#004080'
                            }
                        ]
                    }
                }
            }
        };

        const heatmapChart = new ApexCharts(document.getElementById('featureHeatmapChart'), heatmapOptions);
        heatmapChart.render();

        // Adicionar event listener para o filtro de visualização
        if (document.getElementById('heatmapView')) {
            document.getElementById('heatmapView').addEventListener('change', function() {
                currentHeatmapType = this.value;

                const title = currentHeatmapType === 'weekday' ?
                    'Uso de Features por Dia da Semana' :
                    'Uso de Features por Hora do Dia';

                heatmapChart.updateOptions({
                    title: {
                        text: title
                    }
                });

                heatmapChart.updateSeries(generateHeatmapSeries(currentHeatmapType));
            });
        }
    }

    // Mapa de Correlação entre Features (ApexCharts)
    if (document.getElementById('featureCorrelationChart')) {
        // Matriz de correlação (valores entre 0 e 1)
        const correlationMatrix = [
            [1.00, 0.78, 0.82, 0.65, 0.45, 0.35], // Prontuário
            [0.78, 1.00, 0.70, 0.60, 0.50, 0.40], // Agenda
            [0.82, 0.70, 1.00, 0.55, 0.48, 0.30], // Amigo Intelligence
            [0.65, 0.60, 0.55, 1.00, 0.42, 0.38], // Amigo Pay
            [0.45, 0.50, 0.48, 0.42, 1.00, 0.23], // Rede Amigo
            [0.35, 0.40, 0.30, 0.38, 0.23, 1.00]  // Contabilidade
        ];

        function generateCorrelationSeries() {
            return heatmapData.features.map((feature, i) => {
                return {
                    name: feature,
                    data: heatmapData.features.map((feature2, j) => {
                        return {
                            x: feature2,
                            y: correlationMatrix[i][j] * 100
                        };
                    })
                };
            });
        }

        const correlationOptions = {
            chart: {
                height: 580,
                type: 'heatmap',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: true,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    }
                },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800,
                    animateGradually: {
                        enabled: true,
                        delay: 150
                    },
                    dynamicAnimation: {
                        enabled: true,
                        speed: 350
                    }
                }
            },
            dataLabels: {
                enabled: true,
                formatter: function(val) {
                    return Math.round(val) + '%';
                },
                style: {
                    fontSize: '10px',
                    colors: ['#fff']
                }
            },
            colors: ["#007AFF"],
            series: generateCorrelationSeries(),
            tooltip: {
                custom: function({series, seriesIndex, dataPointIndex, w}) {
                    const feature1 = w.globals.seriesNames[seriesIndex];
                    const feature2 = w.globals.labels[dataPointIndex];
                    const value = series[seriesIndex][dataPointIndex];
                    let strength = '';

                    if (value >= 80) strength = 'Muito Forte';
                    else if (value >= 60) strength = 'Forte';
                    else if (value >= 40) strength = 'Moderada';
                    else if (value >= 20) strength = 'Fraca';
                    else strength = 'Muito Fraca';

                    return `
                    <div class="apexcharts-tooltip-title" style="font-weight: bold; margin-bottom: 5px;">Correlação</div>
                    <div class="apexcharts-tooltip-series-group">
                        <span>${feature1} → ${feature2}</span><br>
                        <span>Valor: <b>${Math.round(value)}%</b> (${strength})</span>
                    </div>
                    `;
                }
            },
            plotOptions: {
                heatmap: {
                    shadeIntensity: 0.5,
                    radius: 0,
                    useFillColorAsStroke: true,
                    colorScale: {
                        ranges: [
                            {
                                from: 0,
                                to: 20,
                                name: 'Muito Fraca',
                                color: '#E6F2FF'
                            },
                            {
                                from: 21,
                                to: 40,
                                name: 'Fraca',
                                color: '#99CCFF'
                            },
                            {
                                from: 41,
                                to: 60,
                                name: 'Moderada',
                                color: '#3399FF'
                            },
                            {
                                from: 61,
                                to: 80,
                                name: 'Forte',
                                color: '#0066CC'
                            },
                            {
                                from: 81,
                                to: 100,
                                name: 'Muito Forte',
                                color: '#004080'
                            }
                        ]
                    }
                }
            }
        };

        const correlationChart = new ApexCharts(document.getElementById('featureCorrelationChart'), correlationOptions);
        correlationChart.render();

        // Adicionar event listener para o botão de informações
        if (document.getElementById('correlationInfoBtn')) {
            document.getElementById('correlationInfoBtn').addEventListener('click', function() {
                alert('Análise de Correlação entre Features\n\n' +
                      'Este gráfico mostra a força da relação entre diferentes features do sistema.\n\n' +
                      'Correlações mais fortes (>70%) indicam que usuários que utilizam uma feature têm alta probabilidade de usar a outra.\n\n' +
                      'Insights principais:\n' +
                      '- Amigo Intelligence e Prontuário têm a correlação mais forte (82%)\n' +
                      '- Prontuário e Agenda também têm correlação forte (78%)\n' +
                      '- Contabilidade e Rede Amigo têm a correlação mais fraca (23%)');
            });
        }
    }
});
