/**
 * Advanced Charts for Amigo One Dashboard
 * Using ApexCharts and D3.js for enhanced visualizations
 */

document.addEventListener('DOMContentLoaded', function() {
    // Obter dados do dashboard (simulados para demonstração)
    const dashboardData = {
        users: {
            users_by_state: {
                'SP': 35,
                'RJ': 22,
                'MG': 18,
                'RS': 8,
                'PR': 7,
                'BA': 5,
                'SC': 4,
                'DF': 3,
                'Outros': 8
            }
        },
        features: {
            usage_by_product: {
                'Prontuário': 92,
                'Agenda': 87,
                'Amigo Intelligence': 65,
                'Amigo Pay': 48,
                'Rede Amigo': 42,
                'Contabilidade': 38,
                'Telemedicina': 35
            }
        },
        finance: {
            monthly_revenue: [120000, 135000, 142000, 158000, 172000, 185000, 198000, 210000],
            transaction_types: {
                'PIX': 45,
                'Cartão': 31,
                'Boleto': 15,
                'Transferência': 9
            }
        }
    };
    // Gráfico de Idade dos Usuários (ApexCharts)
    if (document.getElementById('ageDistributionChart')) {
        const ageData = {
            series: [5, 22, 48, 19, 5, 1],
            labels: ['18-24', '25-30', '31-40', '41-50', '51-60', '61+']
        };

        const ageChartOptions = {
            chart: {
                type: 'donut',
                height: 320,
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
            },
            colors: ['#007AFF', '#5AC8FA', '#34C759', '#FF9500', '#FF3B30', '#AF52DE'],
            labels: ageData.labels,
            series: ageData.series,
            legend: {
                position: 'bottom',
                fontSize: '14px'
            },
            dataLabels: {
                enabled: true,
                formatter: function(val) {
                    return Math.round(val) + '%';
                }
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '60%',
                        labels: {
                            show: true,
                            name: {
                                show: true,
                                fontSize: '16px',
                                fontWeight: 600
                            },
                            value: {
                                show: true,
                                fontSize: '20px',
                                fontWeight: 400,
                                formatter: function(val) {
                                    return val + '%';
                                }
                            },
                            total: {
                                show: true,
                                label: 'Total',
                                formatter: function() {
                                    return '100%';
                                }
                            }
                        }
                    }
                }
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        height: 280
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }],
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + '%';
                    }
                }
            }
        };

        const ageChart = new ApexCharts(document.getElementById('ageDistributionChart'), ageChartOptions);
        ageChart.render();
    }

    // Gráfico de Uso vs. Retenção (ApexCharts)
    if (document.getElementById('usageRetentionScatterChart')) {
        const scatterData = [
            {
                name: 'Prontuário',
                data: [[92, 85, 250]]
            },
            {
                name: 'Agenda',
                data: [[87, 82, 230]]
            },
            {
                name: 'Amigo Intelligence',
                data: [[65, 78, 180]]
            },
            {
                name: 'Amigo Pay',
                data: [[48, 72, 150]]
            },
            {
                name: 'Rede Amigo',
                data: [[42, 68, 140]]
            },
            {
                name: 'Contabilidade',
                data: [[38, 65, 130]]
            },
            {
                name: 'Telemedicina',
                data: [[35, 70, 120]]
            }
        ];

        const scatterOptions = {
            chart: {
                height: 350,
                type: 'bubble',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                toolbar: {
                    show: false
                }
            },
            colors: ['#007AFF', '#5AC8FA', '#34C759', '#FF9500', '#FF3B30', '#AF52DE', '#FF2D55'],
            dataLabels: {
                enabled: true,
                formatter: function(val, opt) {
                    return opt.w.globals.seriesNames[opt.seriesIndex];
                },
                style: {
                    fontSize: '11px',
                    fontWeight: 'normal'
                },
                offsetY: -5
            },
            series: scatterData,
            xaxis: {
                title: {
                    text: 'Taxa de Uso (%)',
                    style: {
                        fontSize: '12px',
                        fontWeight: 500
                    }
                },
                min: 30,
                max: 100,
                tickAmount: 7
            },
            yaxis: {
                title: {
                    text: 'Taxa de Retenção (%)',
                    style: {
                        fontSize: '12px',
                        fontWeight: 500
                    }
                },
                min: 60,
                max: 90,
                tickAmount: 6
            },
            tooltip: {
                shared: false,
                intersect: true,
                custom: function({series, seriesIndex, dataPointIndex, w}) {
                    const data = w.globals.seriesNames[seriesIndex];
                    const x = w.globals.seriesX[seriesIndex][dataPointIndex];
                    const y = w.globals.seriesY[seriesIndex][dataPointIndex];
                    const z = w.globals.seriesZ[seriesIndex][dataPointIndex];

                    return `
                    <div class="apexcharts-tooltip-title" style="font-weight: bold; margin-bottom: 5px;">${data}</div>
                    <div class="apexcharts-tooltip-series-group">
                        <span>Taxa de Uso: <b>${x}%</b></span><br>
                        <span>Taxa de Retenção: <b>${y}%</b></span><br>
                        <span>Base de Usuários: <b>${z}</b></span>
                    </div>
                    `;
                }
            },
            legend: {
                show: false
            },
            grid: {
                borderColor: '#f1f1f1',
                xaxis: {
                    lines: {
                        show: true
                    }
                }
            }
        };

        const scatterChart = new ApexCharts(document.getElementById('usageRetentionScatterChart'), scatterOptions);
        scatterChart.render();

        // Adicionar event listener para o filtro de produtos
        if (document.getElementById('productFilter')) {
            document.getElementById('productFilter').addEventListener('change', function() {
                // Simulação de dados diferentes para cada filtro
                // Em um ambiente real, esses dados viriam do backend
            });
        }
    }

    // Gráficos de Perfil de Uso por Especialidade (ApexCharts) - Separados por especialidade
    const specialtyData = {
        categories: ['Prontuário', 'Agenda', 'Amigo Intelligence', 'Amigo Pay', 'Rede Amigo', 'Contabilidade'],
        cardio: {
            name: 'Cardiologia',
            data: [80, 65, 78, 45, 82, 30]
        },
        dermato: {
            name: 'Dermatologia',
            data: [55, 78, 42, 70, 40, 65]
        },
        pediatria: {
            name: 'Pediatria',
            data: [70, 82, 35, 50, 45, 40]
        }
    };

    // Função para criar um gráfico de radar para uma especialidade específica
    function createSpecialtyChart(elementId, specialtyKey) {
        if (document.getElementById(elementId)) {
            const options = {
                chart: {
                    height: 240,
                    type: 'radar',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                    toolbar: {
                        show: false
                    },
                    dropShadow: {
                        enabled: true,
                        blur: 1,
                        left: 1,
                        top: 1
                    }
                },
                colors: ['#007AFF'],
                series: [{
                    name: specialtyData[specialtyKey].name,
                    data: specialtyData[specialtyKey].data
                }],
                stroke: {
                    width: 2
                },
                fill: {
                    opacity: 0.6
                },
                markers: {
                    size: 4,
                    hover: {
                        size: 6
                    }
                },
                xaxis: {
                    categories: specialtyData.categories,
                    labels: {
                        style: {
                            fontSize: '10px'
                        }
                    }
                },
                yaxis: {
                    max: 100,
                    tickAmount: 5,
                    labels: {
                        style: {
                            fontSize: '10px'
                        }
                    }
                },
                dataLabels: {
                    enabled: true,
                    background: {
                        enabled: true,
                        borderRadius: 2
                    },
                    style: {
                        fontSize: '9px',
                        fontWeight: 'bold'
                    },
                    formatter: function(val) {
                        return val + '%';
                    }
                },
                tooltip: {
                    y: {
                        formatter: function(val) {
                            return val + '%';
                        }
                    }
                }
            };

            const chart = new ApexCharts(document.getElementById(elementId), options);
            chart.render();
            // // console.log(`Gráfico de ${specialtyData[specialtyKey].name} renderizado com sucesso`);
        } else {
            console.warn(`Elemento ${elementId} não encontrado`);
        }
    }

    // Criar gráficos para cada especialidade
    createSpecialtyChart('specialtyProfileChart_cardio', 'cardio');
    createSpecialtyChart('specialtyProfileChart_dermato', 'dermato');
    createSpecialtyChart('specialtyProfileChart_pediatria', 'pediatria');

    // Adicionar event listener para o filtro de especialidade
    if (document.getElementById('specialtyRadarFilter')) {
        document.getElementById('specialtyRadarFilter').addEventListener('change', function() {
            const specialty = this.value;
            // // console.log(`Filtro de especialidade alterado para: ${specialty}`);
            // Em um ambiente real, isso atualizaria os gráficos com dados do backend
        });
    }

    // Gráfico de Distribuição Geográfica (ApexCharts)
    if (document.getElementById('geographicDistributionChart')) {
        const geoData = {
            states: Object.keys(dashboardData.users.users_by_state),
            values: Object.values(dashboardData.users.users_by_state)
        };

        const geoOptions = {
            chart: {
                type: 'bar',
                height: 320,
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    horizontal: true,
                    barHeight: '70%',
                    distributed: true,
                    dataLabels: {
                        position: 'top'
                    }
                }
            },
            colors: ['#007AFF', '#5AC8FA', '#34C759', '#FF9500', '#FF3B30', '#AF52DE', '#FF2D55', '#007AFF', '#5AC8FA'],
            dataLabels: {
                enabled: true,
                formatter: function(val) {
                    return val + '%';
                },
                offsetX: 20,
                style: {
                    fontSize: '12px',
                    colors: ['#333']
                }
            },
            series: [{
                name: 'Usuários',
                data: geoData.values
            }],
            xaxis: {
                categories: geoData.states,
                labels: {
                    style: {
                        fontSize: '12px'
                    }
                }
            },
            yaxis: {
                labels: {
                    show: false
                }
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + '%';
                    }
                }
            },
            legend: {
                show: false
            },
            grid: {
                show: false
            }
        };

        const geoChart = new ApexCharts(document.getElementById('geographicDistributionChart'), geoOptions);
        geoChart.render();

        // Adicionar event listener para alternar entre gráfico e mapa
        if (document.getElementById('geoViewType')) {
            document.getElementById('geoViewType').addEventListener('change', function() {
                const viewType = this.value;

                if (viewType === 'map') {
                    // Esconder o gráfico e mostrar o mapa
                    document.getElementById('geographicDistributionChart').style.display = 'none';

                    // Verificar se o elemento do mapa existe
                    if (document.getElementById('brazilMap')) {
                        document.getElementById('brazilMap').style.display = 'block';

                        // Renderizar o mapa do Brasil usando D3.js
                        renderBrazilMap(dashboardData.users.users_by_state);
                    }
                } else {
                    // Esconder o mapa e mostrar o gráfico
                    document.getElementById('geographicDistributionChart').style.display = 'block';
                    if (document.getElementById('brazilMap')) {
                        document.getElementById('brazilMap').style.display = 'none';
                    }
                }
            });
        }

        // Função para renderizar o mapa do Brasil
        function renderBrazilMap(data) {
            // Esta função seria implementada com D3.js para criar um mapa interativo
            // Por simplicidade, apenas exibimos uma mensagem no elemento do mapa
            if (document.getElementById('brazilMap')) {
                document.getElementById('brazilMap').innerHTML = `
                    <div style="height: 100%; display: flex; align-items: center; justify-content: center; flex-direction: column;">
                        <svg width="200" height="200" viewBox="0 0 200 200">
                            <path d="M100,10 L120,50 L180,60 L150,90 L160,150 L100,130 L40,150 L50,90 L20,60 L80,50 Z"
                                  fill="#007AFF" fill-opacity="0.7" stroke="#007AFF" stroke-width="2"></path>
                        </svg>
                        <p style="text-align: center; margin-top: 10px; font-size: 14px;">
                            Mapa do Brasil com distribuição de usuários<br>
                            <span style="font-size: 12px; color: #666;">
                                SP (35%), RJ (22%), MG (18%), Outros (25%)
                            </span>
                        </p>
                    </div>
                `;
            }
        }
    }
});
