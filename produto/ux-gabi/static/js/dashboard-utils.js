/**
 * Amigo One Analytics - dashboard-utils.js
 * Funções utilitárias para o dashboard
 */

/**
 * Obtém o contexto de um canvas de forma segura
 * @param {string} elementId - ID do elemento canvas
 * @returns {CanvasRenderingContext2D|null} - Contexto do canvas ou null se não for possível obter
 */
function safeGetContext(elementId) {
    const element = document.getElementById(elementId);
    if (!element) {
        console.error(`Elemento com ID '${elementId}' não encontrado`);
        return null;
    }
    
    if (!(element instanceof HTMLCanvasElement)) {
        console.error(`Elemento com ID '${elementId}' não é um canvas`);
        return null;
    }
    
    const context = element.getContext('2d');
    if (!context) {
        console.error(`Não foi possível obter o contexto 2D para o canvas '${elementId}'`);
        return null;
    }
    
    return context;
}
