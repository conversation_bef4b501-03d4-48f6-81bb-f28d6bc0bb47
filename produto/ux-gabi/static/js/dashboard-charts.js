
// Input validation added for security
function validateInput(input) {
    // Remove potentially dangerous characters
    return input.replace(/[<>"'&]/g, '');
}
    /**
 * Amigo One Analytics - dashboard-charts.js
 * Script para inicializar os gráficos do dashboard
 */

document.addEventListener('DOMContentLoaded', function() {
    // Buscar dados do dashboard via API
    fetch('/api/dashboard-data')
        .then(response => response.json())
        .then(data => {
            // Inicializar gráficos restantes
            initializeRemainingCharts(data);
        })
        .catch(error => console.error('Erro ao carregar dados:', error));
});

function initializeRemainingCharts(data) {
    // Gráfico de Tipos de Registros Médicos
    if (document.getElementById('medicalRecordsChart')) {
        // Verificar se o elemento é um canvas antes de obter o contexto
        const medicalRecordsCtx = safeGetContext('medicalRecordsChart');
        if (medicalRecordsCtx) {
            new Chart(medicalRecordsCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(data.features.medical_records),
                datasets: [{
                    label: 'Quantidade',
                    data: Object.values(data.features.medical_records),
                    backgroundColor: 'rgba(0, 122, 255, 0.7)',
                    borderColor: 'rgba(0, 122, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
        }
    }

    // Gráfico de Uso de Funcionalidades por Mês
    if (document.getElementById('featureUsageChart')) {
        // Verificar se o elemento é um canvas antes de obter o contexto
        const featureUsageCtx = safeGetContext('featureUsageChart');
        if (featureUsageCtx) {

        // Verificar se os dados existem, caso contrário, usar dados simulados
        let months = [];
        let featureTypes = [];
        let datasets = [];
        let compareMode = 'current'; // 'current', 'yoy', 'mom', 'dod'

        // Dados simulados para cada feature - ano atual
        const currentYearData = {
            'Prontuário': [1200, 1350, 1500, 1650, 1800, 2000, 2200, 2400, 2600, 2800, 3000, 3200],
            'Agenda': [800, 950, 1100, 1250, 1400, 1550, 1700, 1850, 2000, 2150, 2300, 2450],
            'Amigo Intelligence': [300, 450, 600, 750, 900, 1050, 1200, 1350, 1500, 1650, 1800, 1950],
            'Amigo Pay': [500, 600, 700, 800, 900, 1000, 1100, 1200, 1300, 1400, 1500, 1600]
        };

        // Dados simulados para cada feature - ano anterior
        const previousYearData = {
            'Prontuário': [800, 900, 1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900],
            'Agenda': [600, 700, 800, 900, 1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700],
            'Amigo Intelligence': [150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 650, 700],
            'Amigo Pay': [300, 350, 400, 450, 500, 550, 600, 650, 700, 750, 800, 850]
        };

        // Dados simulados para cada feature - mês anterior
        const previousMonthData = {
            'Prontuário': [1150, 1300, 1450, 1600, 1750, 1950, 2150, 2350, 2550, 2750, 2950, 3150],
            'Agenda': [750, 900, 1050, 1200, 1350, 1500, 1650, 1800, 1950, 2100, 2250, 2400],
            'Amigo Intelligence': [280, 430, 580, 730, 880, 1030, 1180, 1330, 1480, 1630, 1780, 1930],
            'Amigo Pay': [480, 580, 680, 780, 880, 980, 1080, 1180, 1280, 1380, 1480, 1580]
        };

        // Dados simulados para cada feature - dia anterior
        const previousDayData = {
            'Prontuário': [1190, 1340, 1490, 1640, 1790, 1990, 2190, 2390, 2590, 2790, 2990, 3190],
            'Agenda': [790, 940, 1090, 1240, 1390, 1540, 1690, 1840, 1990, 2140, 2290, 2440],
            'Amigo Intelligence': [290, 440, 590, 740, 890, 1040, 1190, 1340, 1490, 1640, 1790, 1940],
            'Amigo Pay': [490, 590, 690, 790, 890, 990, 1090, 1190, 1290, 1390, 1490, 1590]
        };

        // Meses completos
        const allMonths = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];

        // Inicializar com dados do ano atual
        months = allMonths;
        featureTypes = Object.keys(currentYearData);

        // Função para criar datasets com base no modo de comparação
        function createDatasets(mode) {
            let datasets = [];
            const colors = {
                'Prontuário': 'rgba(0, 122, 255, 1)',
                'Agenda': 'rgba(52, 199, 89, 1)',
                'Amigo Intelligence': 'rgba(255, 149, 0, 1)',
                'Amigo Pay': 'rgba(175, 82, 222, 1)'
            };

            if (mode === 'current') {
                // Mostrar apenas dados do ano atual
                datasets = featureTypes.map(type => ({
                    label: type,
                    data: currentYearData[type],
                    borderColor: colors[type],
                    backgroundColor: 'transparent',
                    borderWidth: 3,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    pointBackgroundColor: colors[type],
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2
                }));
            } else if (mode === 'yoy') {
                // Comparar ano atual com ano anterior
                featureTypes.forEach(type => {
                    // Dataset para o ano atual
                    datasets.push({
                        label: `${type} (2024)`,
                        data: currentYearData[type],
                        borderColor: colors[type],
                        backgroundColor: 'transparent',
                        borderWidth: 3,
                        tension: 0.4,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        pointBackgroundColor: colors[type],
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2
                    });

                    // Dataset para o ano anterior (linha tracejada)
                    datasets.push({
                        label: `${type} (2023)`,
                        data: previousYearData[type],
                        borderColor: colors[type],
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        tension: 0.4,
                        pointRadius: 3,
                        pointHoverRadius: 5,
                        pointBackgroundColor: '#fff',
                        pointBorderColor: colors[type],
                        pointBorderWidth: 1
                    });
                });
            } else if (mode === 'mom') {
                // Comparar mês atual com mês anterior
                featureTypes.forEach(type => {
                    // Dataset para o mês atual
                    datasets.push({
                        label: `${type} (Atual)`,
                        data: currentYearData[type],
                        borderColor: colors[type],
                        backgroundColor: 'transparent',
                        borderWidth: 3,
                        tension: 0.4,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        pointBackgroundColor: colors[type],
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2
                    });

                    // Dataset para o mês anterior (linha tracejada)
                    datasets.push({
                        label: `${type} (Mês Anterior)`,
                        data: previousMonthData[type],
                        borderColor: colors[type],
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        tension: 0.4,
                        pointRadius: 3,
                        pointHoverRadius: 5,
                        pointBackgroundColor: '#fff',
                        pointBorderColor: colors[type],
                        pointBorderWidth: 1
                    });
                });
            } else if (mode === 'dod') {
                // Comparar dia atual com dia anterior
                featureTypes.forEach(type => {
                    // Dataset para o dia atual
                    datasets.push({
                        label: `${type} (Atual)`,
                        data: currentYearData[type],
                        borderColor: colors[type],
                        backgroundColor: 'transparent',
                        borderWidth: 3,
                        tension: 0.4,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        pointBackgroundColor: colors[type],
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2
                    });

                    // Dataset para o dia anterior (linha tracejada)
                    datasets.push({
                        label: `${type} (Dia Anterior)`,
                        data: previousDayData[type],
                        borderColor: colors[type],
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        tension: 0.4,
                        pointRadius: 3,
                        pointHoverRadius: 5,
                        pointBackgroundColor: '#fff',
                        pointBorderColor: colors[type],
                        pointBorderWidth: 1
                    });
                });
            }

            return datasets;
        }

        // Inicializar datasets
        datasets = createDatasets(compareMode);

        // Criar o gráfico
        const featureUsageChart = new Chart(featureUsageCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 15,
                            padding: 15,
                            font: {
                                size: 13,
                                weight: 'bold'
                            },
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 12,
                        cornerRadius: 6,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw || 0;

                                // Calcular crescimento percentual para comparações
                                if (compareMode !== 'current' && !context.dataset.borderDash) {
                                    const datasetIndex = context.datasetIndex;
                                    // O dataset anterior é o dataset com índice datasetIndex + 1
                                    const previousDatasetIndex = datasetIndex + 1;

                                    if (previousDatasetIndex < context.chart.data.datasets.length &&
                                        context.chart.data.datasets[previousDatasetIndex].borderDash) {
                                        const previousValue = context.chart.data.datasets[previousDatasetIndex].data[context.dataIndex];
                                        const growthPercent = ((value - previousValue) / previousValue * 100).toFixed(1);
                                        return `${label}: ${value} (${growthPercent > 0 ? '+' : ''}${growthPercent}%)`;
                                    }
                                }

                                return `${label}: ${value}`;
                            },
                            title: function(tooltipItems) {
                                return tooltipItems[0].label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            font: {
                                size: 12
                            },
                            color: '#666',
                            padding: 10,
                            callback: function(value) {
                                if (value >= 1000) {
                                    return (value / 1000).toFixed(1) + 'k';
                                }
                                return value;
                            }
                        },
                        title: {
                            display: true,
                            text: 'Número de Usos',
                            font: {
                                size: 14,
                                weight: 'bold'
                            },
                            color: '#333',
                            padding: {top: 10, bottom: 10}
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 12
                            },
                            color: '#666',
                            padding: 10
                        },
                        title: {
                            display: true,
                            text: 'Período',
                            font: {
                                size: 14,
                                weight: 'bold'
                            },
                            color: '#333',
                            padding: {top: 10, bottom: 0}
                        }
                    }
                }
            }
        });

        // Adicionar event listener para o filtro de período
        if (document.getElementById('featureTimeRange')) {
            document.getElementById('featureTimeRange').addEventListener('change', function() {
                let newLabels;
                let selectedFeature = document.getElementById('featureSelect') ?
                                     document.getElementById('featureSelect').value : 'all';

                if (this.value === 'month') {
                    newLabels = allMonths;
                } else if (this.value === 'quarter') {
                    newLabels = ['Q1', 'Q2', 'Q3', 'Q4'];
                } else {
                    newLabels = ['2023', '2024'];
                }

                // Atualizar labels
                featureUsageChart.data.labels = newLabels;

                // Atualizar o gráfico
                featureUsageChart.update();
            });
        }

        // Adicionar event listener para o filtro de comparação
        if (document.getElementById('featureCompare')) {
            document.getElementById('featureCompare').addEventListener('change', function() {
                compareMode = this.value;

                // Atualizar datasets com base no novo modo de comparação
                featureUsageChart.data.datasets = createDatasets(compareMode);

                // Atualizar o gráfico
                featureUsageChart.update();
            });
        }

        // Adicionar event listener para o filtro de feature
        if (document.getElementById('featureSelect')) {
            document.getElementById('featureSelect').addEventListener('change', function() {
                const selectedFeature = this.value;

                // Filtrar datasets para mostrar apenas a feature selecionada
                if (selectedFeature === 'all') {
                    // Mostrar todas as features
                    featureUsageChart.data.datasets = createDatasets(compareMode);
                } else {
                    // Filtrar para mostrar apenas a feature selecionada
                    featureUsageChart.data.datasets = createDatasets(compareMode).filter(dataset =>
                        dataset.label.includes(selectedFeature)
                    );
                }

                // Atualizar o gráfico
                featureUsageChart.update();
            });
        }
        }
    }

    // Gráfico de Idade dos Usuários
    if (document.getElementById('ageDistributionChart')) {
        // Verificar se o elemento é um canvas antes de obter o contexto
        const ageDistributionCtx = safeGetContext('ageDistributionChart');
        if (ageDistributionCtx) {

        // Dados para o gráfico de idade
        const ageData = {
            labels: ['18-24', '25-30', '31-40', '41-50', '51-60', '61+'],
            datasets: [{
                label: 'Distribuição por Idade',
                data: [5, 22, 48, 19, 5, 1],
                backgroundColor: [
                    'rgba(0, 122, 255, 0.3)',
                    'rgba(0, 122, 255, 0.5)',
                    'rgba(0, 122, 255, 0.8)',
                    'rgba(0, 122, 255, 0.6)',
                    'rgba(0, 122, 255, 0.4)',
                    'rgba(0, 122, 255, 0.2)'
                ],
                borderColor: [
                    'rgba(0, 122, 255, 1)',
                    'rgba(0, 122, 255, 1)',
                    'rgba(0, 122, 255, 1)',
                    'rgba(0, 122, 255, 1)',
                    'rgba(0, 122, 255, 1)',
                    'rgba(0, 122, 255, 1)'
                ],
                borderWidth: 1,
                hoverOffset: 4
            }]
        };

        new Chart(ageDistributionCtx, {
            type: 'doughnut',
            data: ageData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 11
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                return `${label}: ${value}%`;
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });
        }
    }

    // Gráfico de Análise Demográfica
    if (document.getElementById('demographicChart')) {
        // Verificar se o elemento é um canvas antes de obter o contexto
        const demographicCtx = safeGetContext('demographicChart');
        if (demographicCtx) {

        // Dados simulados para análise demográfica
        const demographicData = {
            age: {
                labels: ['18-24', '25-30', '31-40', '41-50', '51-60', '61+'],
                data: [5, 22, 48, 19, 5, 1]
            },
            specialty: {
                labels: ['Clínica Geral', 'Cardiologia', 'Pediatria', 'Ortopedia', 'Dermatologia', 'Outras'],
                data: [25, 15, 12, 10, 8, 30]
            },
            device: {
                labels: ['iOS', 'Android', 'Web Desktop', 'Web Mobile'],
                data: [45, 35, 15, 5]
            }
        };

        // Inicialmente mostrar dados por idade
        let currentDemographicType = 'age';

        // Verificar se já existe um gráfico neste canvas
        const existingChart = Chart.getChart(demographicCtx.canvas);
        if (existingChart) {
            // // console.log('Destruindo gráfico existente para demographicChart');
            existingChart.destroy();
        }

        try {
            const demographicChart = new Chart(demographicCtx, {
                type: 'bar',
                data: {
                    labels: demographicData[currentDemographicType].labels,
                    datasets: [{
                        label: 'Distribuição',
                        data: demographicData[currentDemographicType].data,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value}%`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Adicionar event listener para o filtro
            if (document.getElementById('demographicFilter')) {
                document.getElementById('demographicFilter').addEventListener('change', function() {
                    currentDemographicType = this.value;

                    demographicChart.data.labels = demographicData[currentDemographicType].labels;
                    demographicChart.data.datasets[0].data = demographicData[currentDemographicType].data;

                    demographicChart.update();
                });
            }
        } catch (error) {
            console.error('Erro ao criar gráfico demographicChart:', error);
        }
    }

    // Gráfico de Comportamento por Segmento
    if (document.getElementById('segmentBehaviorChart')) {
        // Verificar se o elemento é um canvas antes de obter o contexto
        const segmentBehaviorCtx = safeGetContext('segmentBehaviorChart');
        if (segmentBehaviorCtx) {

        // Dados simulados para comportamento por segmento
        const segmentData = {
            age: {
                labels: ['Tempo de Sessão (min)', 'Frequência Semanal', 'Features Utilizadas', 'Taxa de Retenção (%)'],
                datasets: [
                    {
                        label: '25-30 anos',
                        data: [22, 4.2, 3.8, 72],
                        backgroundColor: 'rgba(0, 122, 255, 0.2)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgba(0, 122, 255, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(0, 122, 255, 1)'
                    },
                    {
                        label: '31-40 anos',
                        data: [27, 5.1, 4.5, 85],
                        backgroundColor: 'rgba(0, 122, 255, 0.5)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgba(0, 122, 255, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(0, 122, 255, 1)'
                    },
                    {
                        label: '41-50 anos',
                        data: [24, 3.8, 3.2, 87],
                        backgroundColor: 'rgba(0, 122, 255, 0.3)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgba(0, 122, 255, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(0, 122, 255, 1)'
                    }
                ]
            },
            specialty: {
                labels: ['Tempo de Sessão (min)', 'Frequência Semanal', 'Features Utilizadas', 'Taxa de Retenção (%)'],
                datasets: [
                    {
                        label: 'Cardiologia',
                        data: [26, 4.8, 4.2, 83],
                        backgroundColor: 'rgba(0, 122, 255, 0.2)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgba(0, 122, 255, 1)',
                        pointBorderColor: '#fff'
                    },
                    {
                        label: 'Dermatologia',
                        data: [23, 4.2, 3.5, 79],
                        backgroundColor: 'rgba(0, 122, 255, 0.5)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgba(0, 122, 255, 1)',
                        pointBorderColor: '#fff'
                    },
                    {
                        label: 'Pediatria',
                        data: [25, 5.0, 3.8, 81],
                        backgroundColor: 'rgba(0, 122, 255, 0.3)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgba(0, 122, 255, 1)',
                        pointBorderColor: '#fff'
                    }
                ]
            },
            region: {
                labels: ['Tempo de Sessão (min)', 'Frequência Semanal', 'Features Utilizadas', 'Taxa de Retenção (%)'],
                datasets: [
                    {
                        label: 'Sudeste',
                        data: [25, 4.7, 4.3, 84],
                        backgroundColor: 'rgba(0, 122, 255, 0.2)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgba(0, 122, 255, 1)',
                        pointBorderColor: '#fff'
                    },
                    {
                        label: 'Nordeste',
                        data: [22, 4.1, 3.6, 76],
                        backgroundColor: 'rgba(0, 122, 255, 0.5)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgba(0, 122, 255, 1)',
                        pointBorderColor: '#fff'
                    },
                    {
                        label: 'Sul',
                        data: [24, 4.5, 4.0, 82],
                        backgroundColor: 'rgba(0, 122, 255, 0.3)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgba(0, 122, 255, 1)',
                        pointBorderColor: '#fff'
                    }
                ]
            }
        };

        // Inicialmente mostrar dados por idade
        let currentSegmentType = 'age';

        const segmentBehaviorChart = new Chart(segmentBehaviorCtx, {
            type: 'radar',
            data: {
                labels: segmentData[currentSegmentType].labels,
                datasets: segmentData[currentSegmentType].datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        angleLines: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        pointLabels: {
                            font: {
                                size: 10
                            }
                        },
                        ticks: {
                            backdropColor: 'transparent',
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 11
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw || 0;
                                return `${label}: ${value}`;
                            }
                        }
                    }
                }
            }
        });

        // Adicionar event listener para o filtro
        if (document.getElementById('segmentFilter')) {
            document.getElementById('segmentFilter').addEventListener('change', function() {
                currentSegmentType = this.value;

                segmentBehaviorChart.data.labels = segmentData[currentSegmentType].labels;
                segmentBehaviorChart.data.datasets = segmentData[currentSegmentType].datasets;

                segmentBehaviorChart.update();
            });
        }
        }
    }

    // Gráfico de Distribuição Geográfica
    if (document.getElementById('geographicDistributionChart')) {
        // Verificar se o elemento é um canvas antes de obter o contexto
        const geoDistributionCtx = safeGetContext('geographicDistributionChart');
        if (geoDistributionCtx) {

        // Verificar se os dados existem, caso contrário, usar dados simulados
        let stateLabels = [];
        let stateData = [];

        if (data.users && data.users.users_by_state && Object.keys(data.users.users_by_state).length > 0) {
            stateLabels = Object.keys(data.users.users_by_state);
            stateData = Object.values(data.users.users_by_state);
        } else {
            // Dados simulados para estados brasileiros
            stateLabels = [
                'SP', 'RJ', 'MG', 'RS', 'PR', 'BA', 'SC', 'PE', 'CE', 'GO',
                'PA', 'ES', 'PB', 'AM', 'MA', 'RN', 'MT', 'MS', 'DF', 'AL',
                'PI', 'SE', 'RO', 'TO', 'AC', 'AP', 'RR'
            ];

            // Dados simulados de usuários por estado
            stateData = [
                4500, 2800, 2200, 1500, 1400, 1200, 1100, 950, 850, 800,
                700, 650, 600, 550, 500, 450, 400, 350, 300, 250,
                200, 180, 150, 120, 100, 90, 80
            ];
        }

        // Criar gráfico de barras
        const geoChart = new Chart(geoDistributionCtx, {
            type: 'bar',
            data: {
                labels: stateLabels,
                datasets: [{
                    label: 'Usuários',
                    data: stateData,
                    backgroundColor: 'rgba(0, 122, 255, 0.7)',
                    borderColor: 'rgba(0, 122, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // Adicionar mapa do Brasil
        if (document.getElementById('brazilMap')) {
            // Criar mapa do Brasil com dados simulados
            const brazilMap = document.getElementById('brazilMap');

            // Dados simulados para o mapa (UF: valor)
            const mapData = {
                'SP': 4500, 'RJ': 2800, 'MG': 2200, 'RS': 1500, 'PR': 1400,
                'BA': 1200, 'SC': 1100, 'PE': 950, 'CE': 850, 'GO': 800,
                'PA': 700, 'ES': 650, 'PB': 600, 'AM': 550, 'MA': 500,
                'RN': 450, 'MT': 400, 'MS': 350, 'DF': 300, 'AL': 250,
                'PI': 200, 'SE': 180, 'RO': 150, 'TO': 120, 'AC': 100,
                'AP': 90, 'RR': 80
            };

            // Criar HTML para o mapa do Brasil
            const mapHTML = `
                <div class="brazil-map-container">
                    <svg width="100%" height="100%" viewBox="0 0 800 800" style="max-height: 100%;">
                        <!-- Mapa do Brasil simplificado -->
                        <g class="states">
                            <!-- Norte -->
                            <path class="state" id="AC" d="M100,300 L150,300 L150,330 L100,330 Z" data-state="AC" data-value="${mapData['AC']}"></path>
                            <path class="state" id="AM" d="M160,250 L260,250 L260,320 L160,320 Z" data-state="AM" data-value="${mapData['AM']}"></path>
                            <path class="state" id="RR" d="M180,200 L230,200 L230,240 L180,240 Z" data-state="RR" data-value="${mapData['RR']}"></path>
                            <path class="state" id="PA" d="M270,230 L370,230 L370,320 L270,320 Z" data-state="PA" data-value="${mapData['PA']}"></path>
                            <path class="state" id="AP" d="M300,180 L350,180 L350,220 L300,220 Z" data-state="AP" data-value="${mapData['AP']}"></path>
                            <path class="state" id="TO" d="M350,330 L390,330 L390,380 L350,380 Z" data-state="TO" data-value="${mapData['TO']}"></path>
                            <path class="state" id="RO" d="M200,330 L250,330 L250,380 L200,380 Z" data-state="RO" data-value="${mapData['RO']}"></path>

                            <!-- Nordeste -->
                            <path class="state" id="MA" d="M380,270 L430,270 L430,320 L380,320 Z" data-state="MA" data-value="${mapData['MA']}"></path>
                            <path class="state" id="PI" d="M440,290 L480,290 L480,340 L440,340 Z" data-state="PI" data-value="${mapData['PI']}"></path>
                            <path class="state" id="CE" d="M490,270 L530,270 L530,320 L490,320 Z" data-state="CE" data-value="${mapData['CE']}"></path>
                            <path class="state" id="RN" d="M540,270 L570,270 L570,300 L540,300 Z" data-state="RN" data-value="${mapData['RN']}"></path>
                            <path class="state" id="PB" d="M550,310 L580,310 L580,330 L550,330 Z" data-state="PB" data-value="${mapData['PB']}"></path>
                            <path class="state" id="PE" d="M500,330 L550,330 L550,360 L500,360 Z" data-state="PE" data-value="${mapData['PE']}"></path>
                            <path class="state" id="AL" d="M560,340 L580,340 L580,360 L560,360 Z" data-state="AL" data-value="${mapData['AL']}"></path>
                            <path class="state" id="SE" d="M560,370 L580,370 L580,390 L560,390 Z" data-state="SE" data-value="${mapData['SE']}"></path>
                            <path class="state" id="BA" d="M450,350 L520,350 L520,420 L450,420 Z" data-state="BA" data-value="${mapData['BA']}"></path>

                            <!-- Centro-Oeste -->
                            <path class="state" id="MT" d="M300,350 L350,350 L350,420 L300,420 Z" data-state="MT" data-value="${mapData['MT']}"></path>
                            <path class="state" id="MS" d="M300,430 L350,430 L350,480 L300,480 Z" data-state="MS" data-value="${mapData['MS']}"></path>
                            <path class="state" id="GO" d="M370,370 L420,370 L420,430 L370,430 Z" data-state="GO" data-value="${mapData['GO']}"></path>
                            <path class="state" id="DF" d="M390,390 L410,390 L410,410 L390,410 Z" data-state="DF" data-value="${mapData['DF']}"></path>

                            <!-- Sudeste -->
                            <path class="state" id="MG" d="M400,430 L470,430 L470,500 L400,500 Z" data-state="MG" data-value="${mapData['MG']}"></path>
                            <path class="state" id="ES" d="M480,460 L500,460 L500,490 L480,490 Z" data-state="ES" data-value="${mapData['ES']}"></path>
                            <path class="state" id="RJ" d="M450,500 L490,500 L490,530 L450,530 Z" data-state="RJ" data-value="${mapData['RJ']}"></path>
                            <path class="state" id="SP" d="M350,490 L440,490 L440,550 L350,550 Z" data-state="SP" data-value="${mapData['SP']}"></path>

                            <!-- Sul -->
                            <path class="state" id="PR" d="M350,560 L420,560 L420,600 L350,600 Z" data-state="PR" data-value="${mapData['PR']}"></path>
                            <path class="state" id="SC" d="M370,610 L420,610 L420,640 L370,640 Z" data-state="SC" data-value="${mapData['SC']}"></path>
                            <path class="state" id="RS" d="M350,650 L420,650 L420,700 L350,700 Z" data-state="RS" data-value="${mapData['RS']}"></path>
                        </g>
                    </svg>
                    <div class="map-legend">
                        <div class="legend-item">
                            <div class="color-box" style="background-color: rgba(0, 122, 255, 0.2);"></div>
                            <span>0-500</span>
                        </div>
                        <div class="legend-item">
                            <div class="color-box" style="background-color: rgba(0, 122, 255, 0.4);"></div>
                            <span>501-1000</span>
                        </div>
                        <div class="legend-item">
                            <div class="color-box" style="background-color: rgba(0, 122, 255, 0.6);"></div>
                            <span>1001-2000</span>
                        </div>
                        <div class="legend-item">
                            <div class="color-box" style="background-color: rgba(0, 122, 255, 0.8);"></div>
                            <span>2001+</span>
                        </div>
                    </div>
                </div>
            `;

            brazilMap.textContent = mapHTML;

            // Adicionar estilos CSS para o mapa
            const style = document.createElement('style');
            style.textContent = `
                .brazil-map-container {
                    position: relative;
                    width: 100%;
                    height: 100%;
                }
                .state {
                    stroke: white;
                    stroke-width: 1;
                    cursor: pointer;
                    transition: fill 0.3s;
                }
                .state:hover {
                    stroke-width: 2;
                    stroke: #333;
                }
                .map-legend {
                    position: absolute;
                    bottom: 10px;
                    right: 10px;
                    background-color: rgba(255, 255, 255, 0.8);
                    padding: 5px;
                    border-radius: 4px;
                    font-size: 10px;
                }
                .legend-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 2px;
                }
                .color-box {
                    width: 12px;
                    height: 12px;
                    margin-right: 5px;
                    border: 1px solid rgba(0, 0, 0, 0.2);
                }
            `;
            document.head.appendChild(style);

            // Colorir os estados com base nos valores
            document.querySelectorAll('.state').forEach(state => {
                const value = parseInt(state.getAttribute('data-value'));
                let fillColor;

                if (value > 2000) {
                    fillColor = 'rgba(0, 122, 255, 0.8)';
                } else if (value > 1000) {
                    fillColor = 'rgba(0, 122, 255, 0.6)';
                } else if (value > 500) {
                    fillColor = 'rgba(0, 122, 255, 0.4)';
                } else {
                    fillColor = 'rgba(0, 122, 255, 0.2)';
                }

                state.style.fill = fillColor;

                // Adicionar tooltip ao passar o mouse
                state.addEventListener('mouseover', function(e) {
                    const tooltip = document.createElement('div');
                    tooltip.className = 'map-tooltip';
                    tooltip.textContent = `${state.getAttribute('data-state')}: ${state.getAttribute('data-value')} usuários`;
                    tooltip.style.position = 'absolute';
                    tooltip.style.left = `${e.pageX + 10}px`;
                    tooltip.style.top = `${e.pageY + 10}px`;
                    tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                    tooltip.style.color = 'white';
                    tooltip.style.padding = '5px 10px';
                    tooltip.style.borderRadius = '4px';
                    tooltip.style.fontSize = '12px';
                    tooltip.style.zIndex = '1000';
                    document.body.appendChild(tooltip);

                    state.addEventListener('mousemove', function(e) {
                        tooltip.style.left = `${e.pageX + 10}px`;
                        tooltip.style.top = `${e.pageY + 10}px`;
                    });

                    state.addEventListener('mouseout', function() {
                        document.body.removeChild(tooltip);
                    });
                });
            });
        }

        // Adicionar event listener para alternar entre gráfico e mapa
        if (document.getElementById('geoViewType')) {
            document.getElementById('geoViewType').addEventListener('change', function() {
                const views = document.querySelectorAll('.geo-view');
                views.forEach(view => {
                    if (view.getAttribute('data-view') === this.value) {
                        view.classList.remove('hidden');
                    } else {
                        view.classList.add('hidden');
                    }
                });
            });
        }
        }
    }

    // Gráfico de Distribuição por Dispositivo
    if (document.getElementById('deviceDistributionChart')) {
        // Verificar se o elemento é um canvas antes de obter o contexto
        const deviceDistributionCtx = safeGetContext('deviceDistributionChart');
        if (deviceDistributionCtx) {
        new Chart(deviceDistributionCtx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(data.users.users_by_device),
                datasets: [{
                    data: Object.values(data.users.users_by_device),
                    backgroundColor: [
                        'rgba(0, 122, 255, 0.7)',
                        'rgba(52, 199, 89, 0.7)'
                    ],
                    borderColor: [
                        'rgba(0, 122, 255, 1)',
                        'rgba(52, 199, 89, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                cutout: '60%'
            }
        });
        }
    }

    // Mapa de Calor de Uso de Features - UI Avançada
    if (document.getElementById('featureHeatmapChart')) {
        // Este elemento é um DIV, não um canvas - criar visualização simples
        const featureHeatmapElement = document.getElementById('featureHeatmapChart');
        if (featureHeatmapElement) {
            // Criar uma visualização simples de mapa de calor
            featureHeatmapElement.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: linear-gradient(45deg, rgba(0,122,255,0.1) 0%, rgba(0,122,255,0.3) 50%, rgba(0,122,255,0.5) 100%); border-radius: 8px;">
                    <div style="text-align: center; color: #333;">
                        <div style="font-size: 24px; margin-bottom: 10px;">📊</div>
                        <div style="font-weight: bold; margin-bottom: 5px;">Mapa de Calor de Features</div>
                        <div style="font-size: 12px; opacity: 0.7;">Visualização interativa em desenvolvimento</div>
                    </div>
                </div>
            `;
        }

        // Event listeners para os controles (sem funcionalidade de gráfico por enquanto)
        if (document.getElementById('heatmapPeriod')) {
            document.getElementById('heatmapPeriod').addEventListener('change', function() {
                console.log('Período alterado para:', this.value);
            });
        }

        if (document.getElementById('heatmapView')) {
            document.getElementById('heatmapView').addEventListener('change', function() {
                console.log('Visualização alterada para:', this.value);
            });
        }
    }

    // Gráfico de Dispersão: Uso vs. Retenção (ApexCharts)
    if (document.getElementById('usageRetentionScatterChart')) {
        // Dados simulados para o gráfico de dispersão
        const scatterData = {
            'Prontuário': { usage: 92, retention: 85, size: 250 },
            'Agenda': { usage: 87, retention: 82, size: 230 },
            'Amigo Intelligence': { usage: 65, retention: 78, size: 180 },
            'Amigo Pay': { usage: 48, retention: 72, size: 150 },
            'Rede Amigo': { usage: 42, retention: 68, size: 140 },
            'Contabilidade': { usage: 38, retention: 65, size: 130 },
            'Telemedicina': { usage: 35, retention: 70, size: 120 }
        };

        // Preparar dados para ApexCharts
        const scatterSeries = [{
            name: 'Produtos',
            data: Object.entries(scatterData).map(([product, data]) => ({
                x: data.usage,
                y: data.retention,
                z: data.size,
                name: product
            }))
        }];

        // Opções para o gráfico de dispersão
        const scatterOptions = {
            chart: {
                height: 580,
                type: 'bubble',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: true,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    }
                },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800,
                    animateGradually: {
                        enabled: true,
                        delay: 150
                    },
                    dynamicAnimation: {
                        enabled: true,
                        speed: 350
                    }
                }
            },
            colors: ['#007AFF'],
            fill: {
                opacity: 0.8,
                gradient: {
                    enabled: false
                }
            },
            title: {
                text: 'Relação entre Uso e Retenção de Usuários',
                align: 'center',
                style: {
                    fontSize: '16px',
                    fontWeight: 'bold',
                    color: '#333'
                }
            },
            xaxis: {
                title: {
                    text: 'Taxa de Uso (%)',
                    style: {
                        fontSize: '14px',
                        fontWeight: 'normal'
                    }
                },
                min: 30,
                max: 100,
                tickAmount: 7,
                labels: {
                    formatter: function(val) {
                        return val.toFixed(0) + '%';
                    }
                }
            },
            yaxis: {
                title: {
                    text: 'Taxa de Retenção (%)',
                    style: {
                        fontSize: '14px',
                        fontWeight: 'normal'
                    }
                },
                min: 60,
                max: 90,
                tickAmount: 6,
                labels: {
                    formatter: function(val) {
                        return val.toFixed(0) + '%';
                    }
                }
            },
            tooltip: {
                theme: 'dark',
                custom: function({series, seriesIndex, dataPointIndex, w}) {
                    const data = w.globals.initialSeries[seriesIndex].data[dataPointIndex];
                    return `
                    <div class="apexcharts-tooltip-title" style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">${data.name}</div>
                    <div class="apexcharts-tooltip-series-group" style="padding: 8px;">
                        <span style="display: block; margin-bottom: 4px;">Taxa de Uso: <b>${data.x}%</b></span>
                        <span style="display: block; margin-bottom: 4px;">Taxa de Retenção: <b>${data.y}%</b></span>
                        <span style="display: block;">Base de Usuários: <b>${data.z * 40}</b></span>
                    </div>
                    `;
                }
            },
            markers: {
                size: [4, 20],
                strokeWidth: 2,
                hover: {
                    size: 6,
                    sizeOffset: 3
                }
            },
            legend: {
                show: false
            },
            grid: {
                borderColor: '#f1f1f1',
                row: {
                    colors: ['#f3f3f3', 'transparent'],
                    opacity: 0.5
                }
            }
        };

        // Renderizar o gráfico
        const scatterChart = new ApexCharts(document.getElementById('usageRetentionScatterChart'), scatterOptions);
        scatterChart.render();

        // Adicionar event listeners para os filtros
        if (document.getElementById('scatterPlotFilter')) {
            document.getElementById('scatterPlotFilter').addEventListener('change', function() {
                let filteredData;

                if (this.value === 'core') {
                    filteredData = {
                        'Prontuário': scatterData['Prontuário'],
                        'Agenda': scatterData['Agenda'],
                        'Telemedicina': scatterData['Telemedicina']
                    };
                } else if (this.value === 'premium') {
                    filteredData = {
                        'Amigo Intelligence': scatterData['Amigo Intelligence'],
                        'Amigo Pay': scatterData['Amigo Pay'],
                        'Contabilidade': scatterData['Contabilidade']
                    };
                } else {
                    filteredData = scatterData;
                }

                // Atualizar dados
                const newSeries = [{
                    name: 'Produtos',
                    data: Object.entries(filteredData).map(([product, data]) => ({
                        x: data.usage,
                        y: data.retention,
                        z: data.size,
                        name: product
                    }))
                }];

                scatterChart.updateSeries(newSeries);
            });
        }

        // Adicionar event listener para o período
        if (document.getElementById('scatterPlotPeriod')) {
            document.getElementById('scatterPlotPeriod').addEventListener('change', function() {
                let periodData = {};

                if (this.value === 'month') {
                    // Dados do último mês (valores originais)
                    periodData = scatterData;
                } else if (this.value === 'quarter') {
                    // Dados do último trimestre (valores ligeiramente diferentes)
                    periodData = {
                        'Prontuário': { usage: 90, retention: 83, size: 240 },
                        'Agenda': { usage: 85, retention: 80, size: 220 },
                        'Amigo Intelligence': { usage: 62, retention: 75, size: 170 },
                        'Amigo Pay': { usage: 45, retention: 70, size: 140 },
                        'Rede Amigo': { usage: 40, retention: 65, size: 130 },
                        'Contabilidade': { usage: 35, retention: 62, size: 120 },
                        'Telemedicina': { usage: 32, retention: 68, size: 110 }
                    };
                } else if (this.value === 'year') {
                    // Dados do último ano (valores mais diferentes)
                    periodData = {
                        'Prontuário': { usage: 88, retention: 80, size: 220 },
                        'Agenda': { usage: 82, retention: 78, size: 200 },
                        'Amigo Intelligence': { usage: 58, retention: 72, size: 150 },
                        'Amigo Pay': { usage: 42, retention: 68, size: 130 },
                        'Rede Amigo': { usage: 38, retention: 62, size: 120 },
                        'Contabilidade': { usage: 32, retention: 60, size: 110 },
                        'Telemedicina': { usage: 30, retention: 65, size: 100 }
                    };
                }

                // Aplicar filtro atual
                const currentFilter = document.getElementById('scatterPlotFilter').value;
                let filteredData = periodData;

                if (currentFilter === 'core') {
                    filteredData = {
                        'Prontuário': periodData['Prontuário'],
                        'Agenda': periodData['Agenda'],
                        'Telemedicina': periodData['Telemedicina']
                    };
                } else if (currentFilter === 'premium') {
                    filteredData = {
                        'Amigo Intelligence': periodData['Amigo Intelligence'],
                        'Amigo Pay': periodData['Amigo Pay'],
                        'Contabilidade': periodData['Contabilidade']
                    };
                }

                // Atualizar dados
                const newSeries = [{
                    name: 'Produtos',
                    data: Object.entries(filteredData).map(([product, data]) => ({
                        x: data.usage,
                        y: data.retention,
                        z: data.size,
                        name: product
                    }))
                }];

                scatterChart.updateSeries(newSeries);
            });
        }

        // Adicionar event listener para o botão de informações
        if (document.getElementById('scatterPlotInfoBtn')) {
            document.getElementById('scatterPlotInfoBtn').addEventListener('click', function() {
                alert('Análise de Uso vs. Retenção\n\n' +
                      'Este gráfico mostra a relação entre a taxa de uso das features e a taxa de retenção dos usuários.\n\n' +
                      'Cada bolha representa um produto, onde:\n' +
                      '- Posição X: Taxa de uso do produto (%)\n' +
                      '- Posição Y: Taxa de retenção dos usuários (%)\n' +
                      '- Tamanho: Número de usuários ativos\n\n' +
                      'Insights principais:\n' +
                      '- Produtos com maior taxa de uso tendem a ter maior retenção\n' +
                      '- Prontuário e Agenda são os produtos com maior uso e retenção\n' +
                      '- Produtos com menos de 50% de uso têm retenção significativamente menor');
            });
        }
    }

    // Gráfico de Radar: Perfil de Uso por Especialidade
    function initializeSpecialtyCharts() {
        // Dados simulados para o gráfico de radar
        const features = ['Prontuário', 'Agenda', 'Amigo Intelligence', 'Amigo Pay', 'Rede Amigo', 'Contabilidade', 'Telemedicina'];

        // Variáveis globais para compartilhar entre as funções
        window.specialtyFeatures = features;

        // Dados expandidos para todas as especialidades
        window.specialtyData = {
            'cardio': [95, 85, 90, 60, 50, 45, 65],
            'neuro': [90, 80, 85, 55, 60, 40, 70],
            'dermato': [85, 75, 70, 50, 80, 35, 90],
            'ortho': [92, 88, 65, 45, 55, 50, 60],
            'pediatria': [88, 92, 75, 40, 65, 55, 75],
            'gineco': [82, 90, 65, 55, 70, 60, 85],
            'oftalmo': [88, 75, 80, 40, 45, 50, 65],
            'psiquiatria': [75, 85, 95, 35, 60, 40, 80],
            'endocrino': [85, 80, 90, 50, 55, 65, 70],
            'gastro': [80, 75, 85, 60, 50, 70, 65],
            'otorrino': [82, 78, 70, 45, 55, 40, 75],
            'urologia': [85, 80, 75, 50, 60, 65, 55],
            'geriatria': [70, 85, 80, 60, 65, 75, 50]
        };

        // Cores para cada especialidade (padronizadas em tons de azul)
        window.specialtyColors = {
            'cardio': {
                primary: 'rgba(0, 122, 255, 1)',
                background: 'rgba(0, 122, 255, 0.2)',
                name: 'Cardiologia'
            },
            'neuro': {
                primary: 'rgba(0, 70, 150, 1)',
                background: 'rgba(0, 70, 150, 0.2)',
                name: 'Neurologia'
            },
            'dermato': {
                primary: 'rgba(0, 99, 220, 1)',
                background: 'rgba(0, 99, 220, 0.2)',
                name: 'Dermatologia'
            },
            'ortho': {
                primary: 'rgba(0, 140, 230, 1)',
                background: 'rgba(0, 140, 230, 0.2)',
                name: 'Ortopedia'
            },
            'pediatria': {
                primary: 'rgba(0, 84, 180, 1)',
                background: 'rgba(0, 84, 180, 0.2)',
                name: 'Pediatria'
            },
            'gineco': {
                primary: 'rgba(30, 110, 190, 1)',
                background: 'rgba(30, 110, 190, 0.2)',
                name: 'Ginecologia'
            },
            'oftalmo': {
                primary: 'rgba(60, 150, 255, 1)',
                background: 'rgba(60, 150, 255, 0.2)',
                name: 'Oftalmologia'
            },
            'psiquiatria': {
                primary: 'rgba(40, 130, 220, 1)',
                background: 'rgba(40, 130, 220, 0.2)',
                name: 'Psiquiatria'
            },
            'endocrino': {
                primary: 'rgba(70, 160, 240, 1)',
                background: 'rgba(70, 160, 240, 0.2)',
                name: 'Endocrinologia'
            },
            'gastro': {
                primary: 'rgba(50, 140, 230, 1)',
                background: 'rgba(50, 140, 230, 0.2)',
                name: 'Gastroenterologia'
            },
            'otorrino': {
                primary: 'rgba(20, 100, 200, 1)',
                background: 'rgba(20, 100, 200, 0.2)',
                name: 'Otorrinolaringologia'
            },
            'urologia': {
                primary: 'rgba(10, 90, 190, 1)',
                background: 'rgba(10, 90, 190, 0.2)',
                name: 'Urologia'
            },
            'geriatria': {
                primary: 'rgba(80, 170, 250, 1)',
                background: 'rgba(80, 170, 250, 0.2)',
                name: 'Geriatria'
            }
        };

        // Armazenar referências aos gráficos para atualizações futuras
        window.specialtyCharts = {};

        // Inicializar gráficos para cada especialidade
        for (const specialty of ['cardio', 'dermato', 'pediatria']) {
            const chartId = `specialtyProfileChart_${specialty}`;
            const canvas = document.getElementById(chartId);

            if (canvas && canvas instanceof HTMLCanvasElement) {
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    console.error(`Não foi possível obter o contexto 2D para o canvas '${chartId}'`);
                    continue;
                }

                // Obter nome da especialidade formatado
                const specialtyName = window.specialtyColors[specialty].name;

                // Configurar o gráfico de radar
                const chart = new Chart(ctx, {
                    type: 'radar',
                    data: {
                        labels: window.specialtyFeatures,
                        datasets: [{
                            label: specialtyName,
                            data: window.specialtyData[specialty],
                            backgroundColor: window.specialtyColors[specialty].background,
                            borderColor: window.specialtyColors[specialty].primary,
                            borderWidth: 3,
                            pointBackgroundColor: window.specialtyColors[specialty].primary,
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: window.specialtyColors[specialty].primary,
                            pointRadius: 5,
                            pointHoverRadius: 7
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            r: {
                                angleLines: {
                                    display: true,
                                    color: 'rgba(0, 0, 0, 0.1)'
                                },
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                },
                                pointLabels: {
                                    font: {
                                        size: 12,
                                        weight: 'bold'
                                    },
                                    color: '#333',
                                    padding: 10
                                },
                                suggestedMin: 0,
                                suggestedMax: 100,
                                ticks: {
                                    stepSize: 20,
                                    backdropColor: 'transparent',
                                    color: '#666',
                                    font: {
                                        size: 10
                                    },
                                    showLabelBackdrop: false,
                                    z: 1
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false // Ocultar legenda nos gráficos individuais
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleFont: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                bodyFont: {
                                    size: 13
                                },
                                padding: 12,
                                cornerRadius: 6,
                                displayColors: true,
                                callbacks: {
                                    title: function(context) {
                                        return specialtyName;
                                    },
                                    label: function(context) {
                                        return `${context.label}: ${context.raw}%`;
                                    },
                                    afterLabel: function(context) {
                                        // Adicionar informações específicas com base na especialidade e feature
                                        const feature = context.label;

                                        if (specialty === 'cardio' && feature === 'Prontuário') {
                                            return 'Maior uso entre todas as especialidades';
                                        } else if (specialty === 'dermato' && feature === 'Telemedicina') {
                                            return 'Crescimento de 62% no último trimestre';
                                        } else if (specialty === 'pediatria' && feature === 'Agenda') {
                                            return 'Uso intensivo para agendamento de consultas';
                                        }

                                        return null;
                                    }
                                }
                            }
                        }
                    }
                });

                // Armazenar referência ao gráfico
                window.specialtyCharts[specialty] = chart;

                // Adicionar interatividade ao clicar no card
                const cardElement = canvas.closest('.bg-white.p-6.rounded-md.border');
                if (cardElement) {
                    cardElement.addEventListener('click', function() {
                        // Atualizar o seletor de especialidade
                        const specialtyRadarFilter = document.getElementById('specialtyRadarFilter');
                        if (specialtyRadarFilter) {
                            specialtyRadarFilter.value = specialty;

                            // Disparar o evento change para atualizar o gráfico principal
                            const event = new Event('change');
                            specialtyRadarFilter.dispatchEvent(event);

                            // Rolar para o gráfico principal
                            document.getElementById('specialtyRadarChart').scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });
                        }
                    });

                    // Adicionar efeito de hover
                    cardElement.classList.add('cursor-pointer');
                }
            }
        }
    }

    // Inicializar os gráficos de especialidade
    initializeSpecialtyCharts();

    // Gráfico de Radar: Perfil de Uso por Especialidade (gráfico principal)
    if (document.getElementById('specialtyRadarChart')) {
        // Verificar se o elemento é um canvas antes de obter o contexto
        const radarCtx = safeGetContext('specialtyRadarChart');
        if (!radarCtx) {
            console.error('Não foi possível obter o contexto para o gráfico de radar');
            return;
        }

        // Usar as variáveis globais definidas anteriormente
        const features = window.specialtyFeatures;
        const specialtyData = window.specialtyData;
        const specialtyColors = window.specialtyColors;

        // Criar datasets para todas as especialidades (inicialmente ocultas exceto a primeira)
        const datasets = Object.keys(specialtyData).map((specialty, index) => {
            const specialtyName = specialtyColors[specialty].name;

            return {
                label: specialtyName,
                data: specialtyData[specialty],
                backgroundColor: specialtyColors[specialty].background,
                borderColor: specialtyColors[specialty].primary,
                borderWidth: 3,
                pointBackgroundColor: specialtyColors[specialty].primary,
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: specialtyColors[specialty].primary,
                pointRadius: 5,
                pointHoverRadius: 7,
                hidden: index !== 0 // Apenas o primeiro dataset é visível inicialmente
            };
        });

        // Criar o gráfico de radar
        const radarChart = new Chart(radarCtx, {
            type: 'radar',
            data: {
                labels: features,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        angleLines: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        pointLabels: {
                            font: {
                                size: 16,
                                weight: 'bold'
                            },
                            color: '#333',
                            padding: 20
                        },
                        suggestedMin: 0,
                        suggestedMax: 100,
                        ticks: {
                            stepSize: 20,
                            backdropColor: 'transparent',
                            color: '#666',
                            font: {
                                size: 12
                            },
                            showLabelBackdrop: false,
                            z: 1
                        }
                    }
                },
                plugins: {
                    zoom: {
                        pan: {
                            enabled: true,
                            mode: 'xy',
                            overScaleMode: 'xy'
                        },
                        zoom: {
                            wheel: {
                                enabled: true,
                                speed: 0.1
                            },
                            pinch: {
                                enabled: true
                            },
                            mode: 'xy',
                            overScaleMode: 'xy',
                            onZoomComplete: function({chart}) {
                                // Mostrar notificação de zoom
                                showNotification('Zoom aplicado! Use o botão "Resetar Zoom" para voltar à visualização original.', 'info');
                            }
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            boxWidth: 15,
                            padding: 15,
                            font: {
                                size: 14,
                                weight: 'bold'
                            },
                            usePointStyle: true,
                            pointStyle: 'circle'
                        },
                        onClick: function(e, legendItem, legend) {
                            // Implementação personalizada do clique na legenda
                            const index = legendItem.datasetIndex;
                            const chart = legend.chart;

                            // Se o usuário clicou em um item já ativo
                            if (!chart.isDatasetVisible(index)) {
                                // Ocultar todos os datasets
                                chart.data.datasets.forEach((dataset, i) => {
                                    chart.setDatasetVisibility(i, false);
                                });

                                // Mostrar apenas o dataset clicado
                                chart.setDatasetVisibility(index, true);
                            }

                            chart.update();
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 12,
                        cornerRadius: 6,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label} - ${context.label}: ${context.raw}%`;
                            },
                            afterLabel: function(context) {
                                // Adicionar informações adicionais com base na especialidade e feature
                                const specialty = context.dataset.label;
                                const feature = context.label;

                                // Insights específicos para cada especialidade e feature
                                const insights = {
                                    'Cardiologia': {
                                        'Prontuário': 'Maior uso entre todas as especialidades (95%)',
                                        'Amigo Intelligence': 'Crescimento de 45% no uso de IA nos últimos 3 meses'
                                    },
                                    'Dermatologia': {
                                        'Telemedicina': 'Crescimento de 62% no último trimestre',
                                        'Rede Amigo': 'Maior taxa de encaminhamentos entre especialidades'
                                    },
                                    'Pediatria': {
                                        'Agenda': 'Uso intensivo para agendamento de consultas',
                                        'Amigo Pay': 'Aumento de 28% no uso nos últimos 2 meses'
                                    },
                                    'Neurologia': {
                                        'Amigo Intelligence': 'Uso intensivo para análise de exames de imagem',
                                        'Prontuário': 'Maior tempo médio de registro por paciente'
                                    },
                                    'Psiquiatria': {
                                        'Amigo Intelligence': 'Maior taxa de uso (95%) entre todas as especialidades',
                                        'Telemedicina': 'Alta taxa de adoção para consultas de acompanhamento'
                                    }
                                };

                                // Retornar insight específico se existir
                                if (insights[specialty] && insights[specialty][feature]) {
                                    return insights[specialty][feature];
                                }

                                return null;
                            }
                        }
                    }
                }
            }
        });

        // Função para mostrar notificações
        function showNotification(message, type = 'success') {
            // Remover notificações anteriores
            const existingNotifications = document.querySelectorAll('.chart-notification');
            existingNotifications.forEach(notification => notification.remove());

            // Criar nova notificação
            const notification = document.createElement('div');
            notification.className = `chart-notification fixed top-4 right-4 px-4 py-3 rounded z-50 shadow-md`;

            // Definir estilo com base no tipo
            if (type === 'success') {
                notification.classList.add('bg-green-100', 'border', 'border-green-400', 'text-green-700');
            } else if (type === 'info') {
                notification.classList.add('bg-blue-100', 'border', 'border-blue-400', 'text-blue-700');
            } else if (type === 'warning') {
                notification.classList.add('bg-yellow-100', 'border', 'border-yellow-400', 'text-yellow-700');
            }

            // Adicionar conteúdo
            notification.textContent = `
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>${message}</span>
                </div>
            `;

            // Adicionar ao DOM
            document.body.appendChild(notification);

            // Remover após 3 segundos
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Adicionar botão de reset zoom
        const chartContainer = document.querySelector('.h-[600px]');
        if (chartContainer) {
            const resetZoomBtn = document.createElement('button');
            resetZoomBtn.className = 'absolute top-2 right-2 bg-systemBlue text-white text-xs px-3 py-1 rounded-md hover:bg-blue-600 transition-colors duration-200 z-10';
            resetZoomBtn.textContent = `
                <svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Resetar Zoom
            `;
            resetZoomBtn.addEventListener('click', function() {
                radarChart.resetZoom();
                showNotification('Zoom resetado!', 'info');
            });
            chartContainer.style.position = 'relative';
            chartContainer.appendChild(resetZoomBtn);
        }

        // Adicionar event listener para o filtro
        if (document.getElementById('specialtyRadarFilter')) {
            document.getElementById('specialtyRadarFilter').addEventListener('change', function() {
                const specialty = this.value;
                const specialtyIndex = Object.keys(specialtyData).indexOf(specialty);

                // // console.log(`Filtro alterado para: ${specialty} (índice: ${specialtyIndex})`);

                // Verificar se estamos no modo de comparação
                const isCompareMode = document.body.classList.contains('compare-mode');

                if (!isCompareMode) {
                    // Modo normal - mostrar apenas a especialidade selecionada
                    radarChart.data.datasets.forEach((dataset, i) => {
                        radarChart.setDatasetVisibility(i, false);
                    });

                    // Mostrar apenas o dataset selecionado
                    if (specialtyIndex >= 0) {
                        radarChart.setDatasetVisibility(specialtyIndex, true);
                    } else {
                        console.warn(`Especialidade não encontrada: ${specialty}`);
                    }
                }

                // Atualizar o gráfico
                radarChart.update();

                // Atualizar também os gráficos individuais
                updateIndividualCharts(specialty);

                // Mostrar notificação
                if (window.specialtyColors[specialty]) {
                    const specialtyName = window.specialtyColors[specialty].name;
                    showNotification(`Visualizando dados de ${specialtyName}`, 'info');
                }
            });
        }

        // Adicionar event listener para o botão de comparação
        if (document.getElementById('compareSpecialtiesBtn')) {
            document.getElementById('compareSpecialtiesBtn').addEventListener('click', function() {
                // Verificar se já estamos no modo de comparação
                const isCompareMode = document.body.classList.contains('compare-mode');

                if (isCompareMode) {
                    // Desativar modo de comparação
                    document.body.classList.remove('compare-mode');
                    this.textContent = `
                        <svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        Comparar
                    `;

                    // Voltar ao modo normal - mostrar apenas a especialidade selecionada
                    const specialty = document.getElementById('specialtyRadarFilter').value;
                    const specialtyIndex = Object.keys(specialtyData).indexOf(specialty);

                    radarChart.data.datasets.forEach((dataset, i) => {
                        radarChart.setDatasetVisibility(i, false);
                    });

                    radarChart.setDatasetVisibility(specialtyIndex, true);
                    radarChart.update();

                    showNotification('Modo de comparação desativado', 'info');
                } else {
                    // Ativar modo de comparação
                    document.body.classList.add('compare-mode');
                    this.textContent = `
                        <svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Finalizar
                    `;

                    // Mostrar modal de seleção de especialidades
                    showSpecialtySelectionModal(specialtyData, specialtyColors, radarChart);
                }
            });
        }

        // Função para mostrar o modal de seleção de especialidades
        function showSpecialtySelectionModal(specialtyData, specialtyColors, chart) {
            // Criar o modal
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.id = 'specialtySelectionModal';

            // Verificar quais especialidades estão atualmente visíveis
            const visibleSpecialties = [];
            chart.data.datasets.forEach((dataset, i) => {
                if (chart.isDatasetVisible(i)) {
                    const specialty = Object.keys(specialtyData)[i];
                    visibleSpecialties.push(specialty);
                }
            });

            // Conteúdo do modal
            modal.textContent = `
                <div class="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Selecione as especialidades para comparar</h3>
                        <button id="closeModalBtn" class="text-gray-400 hover:text-gray-500">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3 mb-6">
                        ${Object.keys(specialtyData).map(key => `
                            <div class="specialty-option flex items-center p-3 border rounded-md hover:bg-blue-50 cursor-pointer ${visibleSpecialties.includes(key) ? 'bg-blue-50 border-blue-300' : ''}" data-specialty="${key}">
                                <input type="checkbox" id="specialty_${key}" class="mr-2 h-4 w-4 text-systemBlue" ${visibleSpecialties.includes(key) ? 'checked' : ''}>
                                <label for="specialty_${key}" class="cursor-pointer w-full">
                                    <div class="flex items-center">
                                        <span class="w-3 h-3 rounded-full mr-2" style="background-color: ${window.specialtyColors[key].primary}"></span>
                                        <span>${window.specialtyColors[key].name}</span>
                                    </div>
                                </label>
                            </div>
                        `).join('')}
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button id="selectAllBtn" class="px-4 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">Selecionar Todos</button>
                        <button id="clearAllBtn" class="px-4 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">Limpar Todos</button>
                        <button id="applySelectionBtn" class="px-4 py-2 text-sm text-white bg-systemBlue rounded-md hover:bg-blue-600">Aplicar Seleção</button>
                    </div>
                </div>
            `;

            // Adicionar o modal ao DOM
            document.body.appendChild(modal);

            // Event listeners para o modal
            document.getElementById('closeModalBtn').addEventListener('click', function() {
                modal.remove();
            });

            // Tornar as divs clicáveis para selecionar/deselecionar
            document.querySelectorAll('.specialty-option').forEach(option => {
                option.addEventListener('click', function(e) {
                    // Não disparar se o clique foi diretamente no checkbox
                    if (e.target.type !== 'checkbox') {
                        const checkbox = this.querySelector('input[type="checkbox"]');
                        checkbox.checked = !checkbox.checked;

                        // Atualizar estilo visual
                        if (checkbox.checked) {
                            this.classList.add('bg-blue-50', 'border-blue-300');
                        } else {
                            this.classList.remove('bg-blue-50', 'border-blue-300');
                        }
                    }
                });

                // Adicionar evento para o checkbox também atualizar o estilo
                const checkbox = option.querySelector('input[type="checkbox"]');
                checkbox.addEventListener('change', function() {
                    const parent = this.closest('.specialty-option');
                    if (this.checked) {
                        parent.classList.add('bg-blue-50', 'border-blue-300');
                    } else {
                        parent.classList.remove('bg-blue-50', 'border-blue-300');
                    }
                });
            });

            // Selecionar todos
            document.getElementById('selectAllBtn').addEventListener('click', function() {
                document.querySelectorAll('.specialty-option input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = true;
                    checkbox.closest('.specialty-option').classList.add('bg-blue-50', 'border-blue-300');
                });
            });

            // Limpar todos
            document.getElementById('clearAllBtn').addEventListener('click', function() {
                document.querySelectorAll('.specialty-option input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = false;
                    checkbox.closest('.specialty-option').classList.remove('bg-blue-50', 'border-blue-300');
                });
            });

            // Aplicar seleção
            document.getElementById('applySelectionBtn').addEventListener('click', function() {
                // Obter especialidades selecionadas
                const selectedSpecialties = [];
                document.querySelectorAll('.specialty-option input[type="checkbox"]:checked').forEach(checkbox => {
                    const specialty = checkbox.closest('.specialty-option').dataset.specialty;
                    selectedSpecialties.push(specialty);
                });

                // Verificar se pelo menos uma especialidade foi selecionada
                if (selectedSpecialties.length === 0) {
                    showNotification('Selecione pelo menos uma especialidade para comparar', 'warning');
                    return;
                }

                // Atualizar o gráfico para mostrar apenas as especialidades selecionadas
                chart.data.datasets.forEach((dataset, i) => {
                    const specialty = Object.keys(specialtyData)[i];
                    chart.setDatasetVisibility(i, selectedSpecialties.includes(specialty));
                });

                chart.update();

                // Fechar o modal
                modal.remove();

                // Atualizar o dropdown para refletir a primeira especialidade selecionada
                if (selectedSpecialties.length > 0 && document.getElementById('specialtyRadarFilter')) {
                    document.getElementById('specialtyRadarFilter').value = selectedSpecialties[0];
                }

                // Mostrar notificação
                showNotification(`Comparando ${selectedSpecialties.length} especialidades`, 'success');

                // Destacar os cards correspondentes às especialidades selecionadas
                updateMultipleSpecialtyCards(selectedSpecialties);
            });
        }

        // Função para atualizar os gráficos individuais
        function updateIndividualCharts(specialty) {
            // Atualizar os gráficos individuais se necessário
            // // console.log(`Atualizando gráficos individuais para: ${specialty}`);

            // Remover destaque de todos os cards
            document.querySelectorAll('.bg-white.p-6.rounded-md.border').forEach(card => {
                card.classList.remove('ring-2', 'ring-systemBlue');
            });

            // Encontrar o card correspondente à especialidade selecionada
            const specialtyCards = {
                'cardio': 0,
                'dermato': 1,
                'pediatria': 2
            };

            if (specialtyCards.hasOwnProperty(specialty)) {
                const cardIndex = specialtyCards[specialty];
                const cards = document.querySelectorAll('.bg-white.p-6.rounded-md.border');
                if (cards.length > cardIndex) {
                    cards[cardIndex].classList.add('ring-2', 'ring-systemBlue');
                }
            }
        }

        // Função para atualizar múltiplos cards de especialidade
        function updateMultipleSpecialtyCards(specialties) {
            // Remover destaque de todos os cards
            document.querySelectorAll('.bg-white.p-6.rounded-md.border').forEach(card => {
                card.classList.remove('ring-2', 'ring-systemBlue');
            });

            // Mapeamento de especialidades para índices de cards
            const specialtyCards = {
                'cardio': 0,
                'dermato': 1,
                'pediatria': 2
            };

            // Destacar os cards correspondentes às especialidades selecionadas
            specialties.forEach(specialty => {
                if (specialtyCards.hasOwnProperty(specialty)) {
                    const cardIndex = specialtyCards[specialty];
                    const cards = document.querySelectorAll('.bg-white.p-6.rounded-md.border');
                    if (cards.length > cardIndex) {
                        cards[cardIndex].classList.add('ring-2', 'ring-systemBlue');
                    }
                }
            });
        }
    }

    // Mapa de Correlação entre Features - UI Avançada
    if (document.getElementById('featureCorrelationChart')) {
        const featureCorrelationCtx = document.getElementById('featureCorrelationChart').getContext('2d');

        // Dados simulados para o mapa de correlação
        const features = ['Prontuário', 'Agenda', 'Amigo Intelligence', 'Amigo Pay', 'Rede Amigo', 'Contabilidade'];

        // Matriz de correlação (valores entre 0 e 1)
        const correlationMatrix = [
            [1.00, 0.78, 0.82, 0.65, 0.45, 0.35], // Prontuário
            [0.78, 1.00, 0.70, 0.60, 0.50, 0.40], // Agenda
            [0.82, 0.70, 1.00, 0.55, 0.48, 0.30], // Amigo Intelligence
            [0.65, 0.60, 0.55, 1.00, 0.42, 0.38], // Amigo Pay
            [0.45, 0.50, 0.48, 0.42, 1.00, 0.23], // Rede Amigo
            [0.35, 0.40, 0.30, 0.38, 0.23, 1.00]  // Contabilidade
        ];

        // Função para renderizar a matriz de correlação
        function renderCorrelationMatrix() {
            // Destruir gráfico anterior se existir
            if (window.correlationMatrixChart) {
                window.correlationMatrixChart.destroy();
            }

            // Criar novo gráfico
            window.correlationMatrixChart = new Chart(featureCorrelationCtx, {
                type: 'matrix',
                data: {
                    datasets: [{
                        label: 'Correlação entre Features',
                        data: features.flatMap((feature1, i) =>
                            features.map((feature2, j) => ({
                                x: j,
                                y: i,
                                v: correlationMatrix[i][j]
                            }))
                        ),
                        backgroundColor(context) {
                            const value = context.dataset.data[context.dataIndex].v;
                            // Usar escala de azul mais sofisticada para correlação
                            if (value < 0.3) return 'rgba(0, 122, 255, 0.2)';
                            if (value < 0.5) return 'rgba(0, 122, 255, 0.4)';
                            if (value < 0.7) return 'rgba(0, 122, 255, 0.6)';
                            if (value < 0.9) return 'rgba(0, 122, 255, 0.8)';
                            return 'rgba(0, 122, 255, 1.0)';
                        },
                        borderColor: 'white',
                        borderWidth: 1,
                        width: ({ chart }) => (chart.chartArea || {}).width / features.length - 1,
                        height: ({ chart }) => (chart.chartArea || {}).height / features.length - 1,
                        hoverBackgroundColor: 'rgba(0, 122, 255, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                title() {
                                    return '';
                                },
                                label(context) {
                                    const v = context.dataset.data[context.dataIndex];
                                    const correlation = Math.round(v.v * 100);
                                    let strength = '';

                                    if (correlation >= 80) strength = 'Muito Forte';
                                    else if (correlation >= 60) strength = 'Forte';
                                    else if (correlation >= 40) strength = 'Moderada';
                                    else if (correlation >= 20) strength = 'Fraca';
                                    else strength = 'Muito Fraca';

                                    return [
                                        `${features[v.y]} → ${features[v.x]}`,
                                        `Correlação: ${correlation}% (${strength})`
                                    ];
                                }
                            },
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleFont: {
                                size: 12
                            },
                            bodyFont: {
                                size: 12
                            },
                            padding: 10,
                            cornerRadius: 4
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            type: 'category',
                            labels: features,
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        },
                        y: {
                            type: 'category',
                            labels: features,
                            offset: true,
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        }
                    },
                    animation: {
                        duration: 500
                    }
                }
            });
        }

        // Função para renderizar o gráfico de rede
        function renderNetworkGraph() {
            // Limpar o canvas atual
            if (window.correlationMatrixChart) {
                window.correlationMatrixChart.destroy();
            }

            // Obter o elemento para o gráfico de rede
            const networkGraphElement = document.getElementById('networkGraph');
            if (!networkGraphElement) return;

            // Mostrar o elemento de rede e esconder o canvas
            networkGraphElement.classList.remove('hidden');
            featureCorrelationCtx.canvas.classList.add('hidden');

            // Criar HTML para o gráfico de rede
            const networkHTML = `
                <div class="network-graph-container" style="width: 100%; height: 100%; position: relative;">
                    <svg width="100%" height="100%" viewBox="0 0 500 300">
                        <!-- Nós (Features) -->
                        <g class="nodes">
                            <circle cx="250" cy="150" r="30" class="node" data-feature="Prontuário" style="fill: rgba(0, 122, 255, 0.8);"></circle>
                            <circle cx="150" cy="100" r="25" class="node" data-feature="Agenda" style="fill: rgba(0, 122, 255, 0.7);"></circle>
                            <circle cx="350" cy="100" r="28" class="node" data-feature="Amigo Intelligence" style="fill: rgba(0, 122, 255, 0.75);"></circle>
                            <circle cx="150" cy="200" r="22" class="node" data-feature="Amigo Pay" style="fill: rgba(0, 122, 255, 0.6);"></circle>
                            <circle cx="350" cy="200" r="20" class="node" data-feature="Rede Amigo" style="fill: rgba(0, 122, 255, 0.55);"></circle>
                            <circle cx="250" cy="250" r="18" class="node" data-feature="Contabilidade" style="fill: rgba(0, 122, 255, 0.5);"></circle>
                        </g>

                        <!-- Arestas (Correlações) -->
                        <g class="edges">
                            <!-- Prontuário - Agenda (0.78) -->
                            <line x1="250" y1="150" x2="150" y2="100" class="edge" style="stroke: rgba(0, 122, 255, 0.78); stroke-width: 3;"></line>
                            <!-- Prontuário - Amigo Intelligence (0.82) -->
                            <line x1="250" y1="150" x2="350" y2="100" class="edge" style="stroke: rgba(0, 122, 255, 0.82); stroke-width: 4;"></line>
                            <!-- Prontuário - Amigo Pay (0.65) -->
                            <line x1="250" y1="150" x2="150" y2="200" class="edge" style="stroke: rgba(0, 122, 255, 0.65); stroke-width: 2.5;"></line>
                            <!-- Prontuário - Rede Amigo (0.45) -->
                            <line x1="250" y1="150" x2="350" y2="200" class="edge" style="stroke: rgba(0, 122, 255, 0.45); stroke-width: 2;"></line>
                            <!-- Prontuário - Contabilidade (0.35) -->
                            <line x1="250" y1="150" x2="250" y2="250" class="edge" style="stroke: rgba(0, 122, 255, 0.35); stroke-width: 1.5;"></line>

                            <!-- Agenda - Amigo Intelligence (0.70) -->
                            <line x1="150" y1="100" x2="350" y2="100" class="edge" style="stroke: rgba(0, 122, 255, 0.70); stroke-width: 3;"></line>
                            <!-- Agenda - Amigo Pay (0.60) -->
                            <line x1="150" y1="100" x2="150" y2="200" class="edge" style="stroke: rgba(0, 122, 255, 0.60); stroke-width: 2.5;"></line>
                            <!-- Agenda - Rede Amigo (0.50) -->
                            <line x1="150" y1="100" x2="350" y2="200" class="edge" style="stroke: rgba(0, 122, 255, 0.50); stroke-width: 2;"></line>
                            <!-- Agenda - Contabilidade (0.40) -->
                            <line x1="150" y1="100" x2="250" y2="250" class="edge" style="stroke: rgba(0, 122, 255, 0.40); stroke-width: 1.5;"></line>

                            <!-- Amigo Intelligence - Amigo Pay (0.55) -->
                            <line x1="350" y1="100" x2="150" y2="200" class="edge" style="stroke: rgba(0, 122, 255, 0.55); stroke-width: 2;"></line>
                            <!-- Amigo Intelligence - Rede Amigo (0.48) -->
                            <line x1="350" y1="100" x2="350" y2="200" class="edge" style="stroke: rgba(0, 122, 255, 0.48); stroke-width: 2;"></line>
                            <!-- Amigo Intelligence - Contabilidade (0.30) -->
                            <line x1="350" y1="100" x2="250" y2="250" class="edge" style="stroke: rgba(0, 122, 255, 0.30); stroke-width: 1.5;"></line>

                            <!-- Amigo Pay - Rede Amigo (0.42) -->
                            <line x1="150" y1="200" x2="350" y2="200" class="edge" style="stroke: rgba(0, 122, 255, 0.42); stroke-width: 1.5;"></line>
                            <!-- Amigo Pay - Contabilidade (0.38) -->
                            <line x1="150" y1="200" x2="250" y2="250" class="edge" style="stroke: rgba(0, 122, 255, 0.38); stroke-width: 1.5;"></line>

                            <!-- Rede Amigo - Contabilidade (0.23) -->
                            <line x1="350" y1="200" x2="250" y2="250" class="edge" style="stroke: rgba(0, 122, 255, 0.23); stroke-width: 1;"></line>
                        </g>

                        <!-- Rótulos -->
                        <g class="labels">
                            <text x="250" y="150" dy=".35em" text-anchor="middle" class="label" style="font-size: 10px; fill: white; font-weight: bold;">Prontuário</text>
                            <text x="150" y="100" dy=".35em" text-anchor="middle" class="label" style="font-size: 10px; fill: white; font-weight: bold;">Agenda</text>
                            <text x="350" y="100" dy=".35em" text-anchor="middle" class="label" style="font-size: 9px; fill: white; font-weight: bold;">Amigo IA</text>
                            <text x="150" y="200" dy=".35em" text-anchor="middle" class="label" style="font-size: 10px; fill: white; font-weight: bold;">Amigo Pay</text>
                            <text x="350" y="200" dy=".35em" text-anchor="middle" class="label" style="font-size: 10px; fill: white; font-weight: bold;">Rede Amigo</text>
                            <text x="250" y="250" dy=".35em" text-anchor="middle" class="label" style="font-size: 9px; fill: white; font-weight: bold;">Contabilidade</text>
                        </g>
                    </svg>

                    <div class="network-legend" style="position: absolute; bottom: 10px; right: 10px; background-color: rgba(255, 255, 255, 0.8); padding: 5px; border-radius: 4px; font-size: 10px;">
                        <div style="margin-bottom: 2px;">Força da Correlação:</div>
                        <div style="display: flex; align-items: center; margin-bottom: 2px;">
                            <div style="width: 20px; height: 2px; background-color: rgba(0, 122, 255, 0.3); margin-right: 5px;"></div>
                            <span>Fraca</span>
                        </div>
                        <div style="display: flex; align-items: center; margin-bottom: 2px;">
                            <div style="width: 20px; height: 3px; background-color: rgba(0, 122, 255, 0.6); margin-right: 5px;"></div>
                            <span>Média</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div style="width: 20px; height: 4px; background-color: rgba(0, 122, 255, 0.9); margin-right: 5px;"></div>
                            <span>Forte</span>
                        </div>
                    </div>
                </div>
            `;

            networkGraphElement.textContent = networkHTML;

            // Adicionar interatividade aos nós
            document.querySelectorAll('.node').forEach(node => {
                node.addEventListener('mouseover', function() {
                    const feature = this.getAttribute('data-feature');

                    // Destacar o nó atual
                    this.style.stroke = '#333';
                    this.style.strokeWidth = '2px';

                    // Criar tooltip
                    const tooltip = document.createElement('div');
                    tooltip.className = 'network-tooltip';
                    tooltip.textContent = `<strong>${feature}</strong>`;
                    tooltip.style.position = 'absolute';
                    tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                    tooltip.style.color = 'white';
                    tooltip.style.padding = '5px 10px';
                    tooltip.style.borderRadius = '4px';
                    tooltip.style.fontSize = '12px';
                    tooltip.style.zIndex = '1000';

                    // Posicionar tooltip
                    const rect = this.getBoundingClientRect();
                    const containerRect = networkGraphElement.getBoundingClientRect();
                    tooltip.style.left = `${rect.left - containerRect.left + rect.width / 2}px`;
                    tooltip.style.top = `${rect.top - containerRect.top - 30}px`;
                    tooltip.style.transform = 'translateX(-50%)';

                    networkGraphElement.appendChild(tooltip);

                    // Remover tooltip ao tirar o mouse
                    this.addEventListener('mouseout', function() {
                        this.style.stroke = 'none';
                        if (document.querySelector('.network-tooltip')) {
                            networkGraphElement.removeChild(document.querySelector('.network-tooltip'));
                        }
                    });
                });
            });
        }

        // Inicializar com a matriz de correlação
        renderCorrelationMatrix();

        // Adicionar event listener para alternar entre visualizações
        if (document.getElementById('correlationView')) {
            document.getElementById('correlationView').addEventListener('change', function() {
                if (this.value === 'matrix') {
                    // Mostrar o canvas e esconder o gráfico de rede
                    featureCorrelationCtx.canvas.classList.remove('hidden');
                    if (document.getElementById('networkGraph')) {
                        document.getElementById('networkGraph').classList.add('hidden');
                    }
                    renderCorrelationMatrix();
                } else {
                    renderNetworkGraph();
                }
            });
        }

        // Adicionar event listener para o botão de informações
        if (document.getElementById('correlationInfoBtn')) {
            document.getElementById('correlationInfoBtn').addEventListener('click', function() {
                alert('Análise de Correlação entre Features\n\n' +
                      'Este gráfico mostra a força da relação entre diferentes features do sistema.\n\n' +
                      'Correlações mais fortes (>70%) indicam que usuários que utilizam uma feature têm alta probabilidade de usar a outra.\n\n' +
                      'Insights principais:\n' +
                      '- Amigo Intelligence e Prontuário têm a correlação mais forte (82%)\n' +
                      '- Prontuário e Agenda também têm correlação forte (78%)\n' +
                      '- Contabilidade e Rede Amigo têm a correlação mais fraca (23%)');
            });
        }
    }

    // Gráfico de Funil de Engajamento
    if (document.getElementById('userEngagementFunnelChart')) {
        const funnelCtx = document.getElementById('userEngagementFunnelChart').getContext('2d');

        // Dados simulados para o funil
        const funnelData = {
            labels: ['Visitantes', 'Cadastros', 'Ativação', 'Uso Regular', 'Retenção'],
            datasets: [{
                data: [100, 75, 60, 45, 35],
                backgroundColor: [
                    'rgba(0, 122, 255, 0.9)',
                    'rgba(0, 122, 255, 0.8)',
                    'rgba(0, 122, 255, 0.7)',
                    'rgba(0, 122, 255, 0.6)',
                    'rgba(0, 122, 255, 0.5)'
                ],
                borderWidth: 0
            }]
        };

        new Chart(funnelCtx, {
            type: 'bar',
            data: funnelData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const prevValue = context.dataIndex === 0 ? 100 : funnelData.datasets[0].data[context.dataIndex - 1];
                                const conversionRate = Math.round((value / prevValue) * 100);

                                return [
                                    `${context.label}: ${value}%`,
                                    `Taxa de Conversão: ${conversionRate}%`
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        max: 100,
                        grid: {
                            display: false
                        },
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // Adicionar event listener para o filtro de período
        if (document.getElementById('engagementPeriod')) {
            document.getElementById('engagementPeriod').addEventListener('change', function() {
                // Simulação de dados diferentes para cada período
                // Em um ambiente real, esses dados viriam do backend
            });
        }
        }
    }

    // Adicionar funcionalidade aos botões
    document.querySelectorAll('button').forEach(button => {
        button.addEventListener('click', function() {
            if (this.textContent.includes('Gerar Relatório')) {
                alert('Gerando relatório... Em um ambiente real, seria gerado um PDF com os dados do dashboard.');
            } else if (this.textContent.includes('Filtrar Dados')) {
                alert('Em um ambiente real, seria aberto um modal para filtrar os dados por período, especialidade, etc.');
            }
        });
    });
}
