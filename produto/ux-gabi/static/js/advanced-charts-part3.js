/**
 * Advanced Charts for Amigo One Dashboard - Parte 3
 * Implementação de gráficos adicionais usando ApexCharts
 */

document.addEventListener('DOMContentLoaded', function() {
    // Dados simulados para os gráficos
    const dashboardData = window.dashboardData || {
        users: {
            users_by_state: {
                'SP': 35,
                'RJ': 22,
                'MG': 18,
                'RS': 8,
                'PR': 7,
                'BA': 5,
                'SC': 4,
                'DF': 3,
                'Outros': 8
            }
        },
        features: {
            usage_by_product: {
                'Prontuário': 92,
                'Agenda': 87,
                'Amigo Intelligence': 65,
                'Amigo Pay': 48,
                'Rede Amigo': 42,
                'Contabilidade': 38,
                'Telemedicina': 35
            }
        },
        finance: {
            monthly_revenue: [120000, 135000, 142000, 158000, 172000, 185000, 198000, 210000],
            transaction_types: {
                'PIX': 45,
                'Cartão': 31,
                'Boleto': 15,
                'Transferência': 9
            }
        }
    };

    // Gráfico de Uso por Produto (ApexCharts)
    if (document.getElementById('productUsageChart')) {
        const productData = {
            products: Object.keys(dashboardData.features.usage_by_product),
            values: Object.values(dashboardData.features.usage_by_product)
        };

        const productOptions = {
            chart: {
                type: 'bar',
                height: 350,
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    distributed: true,
                    borderRadius: 4,
                    horizontal: false,
                    columnWidth: '60%'
                }
            },
            colors: ['#007AFF', '#0066CC', '#0052A3', '#003D7A', '#002952', '#00142B', '#004080'],
            dataLabels: {
                enabled: false
            },
            legend: {
                show: false
            },
            series: [{
                name: 'Uso',
                data: productData.values
            }],
            xaxis: {
                categories: productData.products,
                labels: {
                    style: {
                        fontSize: '12px',
                        fontWeight: 400
                    },
                    rotate: -45,
                    rotateAlways: false,
                    hideOverlappingLabels: true
                }
            },
            yaxis: {
                title: {
                    text: 'Percentual de Uso (%)',
                    style: {
                        fontSize: '12px',
                        fontWeight: 500
                    }
                },
                min: 0,
                max: 100
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + '%';
                    }
                }
            }
        };

        const productChart = new ApexCharts(document.getElementById('productUsageChart'), productOptions);
        productChart.render();

        // Adicionar event listener para o filtro de visualização
        if (document.getElementById('productUsageView')) {
            document.getElementById('productUsageView').addEventListener('change', function() {
                const viewType = this.value;

                if (viewType === 'absolute') {
                    // Dados absolutos (simulados)
                    const absoluteData = productData.values.map(value => Math.round(value * 25)); // Simulação

                    productChart.updateOptions({
                        yaxis: {
                            title: {
                                text: 'Número de Usuários',
                                style: {
                                    fontSize: '12px',
                                    fontWeight: 500
                                }
                            },
                            min: 0,
                            max: 2500
                        },
                        tooltip: {
                            y: {
                                formatter: function(val) {
                                    return val + ' usuários';
                                }
                            }
                        }
                    });

                    productChart.updateSeries([{
                        name: 'Usuários',
                        data: absoluteData
                    }]);
                } else {
                    // Dados percentuais
                    productChart.updateOptions({
                        yaxis: {
                            title: {
                                text: 'Percentual de Uso (%)',
                                style: {
                                    fontSize: '12px',
                                    fontWeight: 500
                                }
                            },
                            min: 0,
                            max: 100
                        },
                        tooltip: {
                            y: {
                                formatter: function(val) {
                                    return val + '%';
                                }
                            }
                        }
                    });

                    productChart.updateSeries([{
                        name: 'Uso',
                        data: productData.values
                    }]);
                }
            });
        }
    }

    // Gráfico de Faturamento Mensal (ApexCharts)
    if (document.getElementById('revenueChart')) {
        const revenueOptions = {
            chart: {
                type: 'area',
                height: 250,
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                toolbar: {
                    show: false
                },
                zoom: {
                    enabled: false
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth',
                width: 3
            },
            series: [{
                name: 'Faturamento',
                data: dashboardData.finance.monthly_revenue
            }],
            colors: ['#007AFF'],
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.2,
                    stops: [0, 90, 100]
                }
            },
            xaxis: {
                categories: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago'],
                labels: {
                    style: {
                        fontSize: '10px'
                    }
                }
            },
            yaxis: {
                labels: {
                    formatter: function(val) {
                        return 'R$ ' + (val / 1000).toFixed(0) + 'k';
                    },
                    style: {
                        fontSize: '10px'
                    }
                }
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return 'R$ ' + val.toLocaleString('pt-BR');
                    }
                }
            },
            grid: {
                borderColor: '#f1f1f1',
                row: {
                    colors: ['transparent', 'transparent'],
                    opacity: 0.5
                }
            },
            markers: {
                size: 4
            }
        };

        const revenueChart = new ApexCharts(document.getElementById('revenueChart'), revenueOptions);
        revenueChart.render();
    }

    // Gráfico de Transações por Tipo (ApexCharts)
    if (document.getElementById('transactionTypesChart')) {
        const transactionOptions = {
            chart: {
                type: 'donut',
                height: 250,
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
            },
            series: Object.values(dashboardData.finance.transaction_types),
            labels: Object.keys(dashboardData.finance.transaction_types),
            colors: ['#007AFF', '#0066CC', '#0052A3', '#003D7A'],
            legend: {
                position: 'bottom',
                fontSize: '12px',
                markers: {
                    width: 12,
                    height: 12,
                    radius: 12
                },
                itemMargin: {
                    horizontal: 10,
                    vertical: 0
                }
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '60%',
                        labels: {
                            show: true,
                            total: {
                                show: true,
                                label: 'Total',
                                formatter: function() {
                                    return '100%';
                                }
                            }
                        }
                    }
                }
            },
            dataLabels: {
                enabled: true,
                formatter: function(val) {
                    return Math.round(val) + '%';
                },
                style: {
                    fontSize: '10px'
                },
                dropShadow: {
                    enabled: false
                }
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + '%';
                    }
                }
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        height: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };

        const transactionChart = new ApexCharts(document.getElementById('transactionTypesChart'), transactionOptions);
        transactionChart.render();
    }

    // Gráfico de Funil de Engajamento (ApexCharts)
    if (document.getElementById('userEngagementFunnelChart')) {
        const funnelData = {
            labels: ['Visitantes', 'Cadastros', 'Ativação', 'Uso Regular', 'Retenção'],
            series: [100, 75, 60, 45, 35]
        };

        const funnelOptions = {
            chart: {
                type: 'bar',
                height: 350,
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    horizontal: true,
                    distributed: true,
                    dataLabels: {
                        position: 'bottom'
                    }
                }
            },
            colors: [
                'rgba(0, 122, 255, 0.9)',
                'rgba(0, 122, 255, 0.8)',
                'rgba(0, 122, 255, 0.7)',
                'rgba(0, 122, 255, 0.6)',
                'rgba(0, 122, 255, 0.5)'
            ],
            dataLabels: {
                enabled: true,
                formatter: function(val) {
                    return val + '%';
                },
                style: {
                    fontSize: '12px',
                    colors: ['#333']
                }
            },
            series: [{
                data: funnelData.series
            }],
            xaxis: {
                categories: funnelData.labels,
                labels: {
                    style: {
                        fontSize: '12px'
                    }
                }
            },
            yaxis: {
                max: 100,
                labels: {
                    show: false
                }
            },
            tooltip: {
                custom: function({series, seriesIndex, dataPointIndex, w}) {
                    const stage = w.globals.labels[dataPointIndex];
                    const value = series[seriesIndex][dataPointIndex];
                    const prevValue = dataPointIndex === 0 ? 100 : series[seriesIndex][dataPointIndex - 1];
                    const conversionRate = Math.round((value / prevValue) * 100);

                    return `
                    <div class="apexcharts-tooltip-title" style="font-weight: bold; margin-bottom: 5px;">${stage}</div>
                    <div class="apexcharts-tooltip-series-group">
                        <span>Valor: <b>${value}%</b></span><br>
                        <span>Taxa de Conversão: <b>${conversionRate}%</b></span>
                    </div>
                    `;
                }
            },
            legend: {
                show: false
            },
            grid: {
                show: false
            }
        };

        const funnelChart = new ApexCharts(document.getElementById('userEngagementFunnelChart'), funnelOptions);
        funnelChart.render();

        // Adicionar event listener para o filtro de período
        if (document.getElementById('engagementPeriod')) {
            document.getElementById('engagementPeriod').addEventListener('change', function() {
                // Simulação de dados diferentes para cada período
                let newData = [];

                if (this.value === 'week') {
                    newData = [100, 75, 60, 45, 35];
                } else if (this.value === 'month') {
                    newData = [100, 80, 65, 50, 40];
                } else if (this.value === 'quarter') {
                    newData = [100, 85, 70, 55, 45];
                }

                funnelChart.updateSeries([{
                    data: newData
                }]);
            });
        }
    }
});
