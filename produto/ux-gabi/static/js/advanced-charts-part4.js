/**
 * Advanced Charts for Amigo One Dashboard - Parte 4
 * Implementação de gráficos adicionais usando ApexCharts
 */

document.addEventListener('DOMContentLoaded', function() {
    // Usar dados compartilhados ou criar novos
    const dashboardData = window.dashboardData || {
        users: {
            users_by_state: {
                'SP': 35,
                'RJ': 22,
                'MG': 18,
                'RS': 8,
                'PR': 7,
                'BA': 5,
                'SC': 4,
                'DF': 3,
                'Outros': 8
            }
        },
        features: {
            usage_by_product: {
                'Prontuário': 92,
                'Agenda': 87,
                'Amigo Intelligence': 65,
                'Amigo Pay': 48,
                'Rede Amigo': 42,
                'Contabilidade': 38,
                'Telemedicina': 35
            }
        },
        finance: {
            monthly_revenue: [120000, 135000, 142000, 158000, 172000, 185000, 198000, 210000],
            transaction_types: {
                'PIX': 45,
                'Cartão': 31,
                'Boleto': 15,
                'Transferência': 9
            }
        }
    };
    
    // Compartilhar os dados com outros scripts
    window.dashboardData = dashboardData;
    
    // Gráfico de Dispersão: Uso vs. Retenção (ApexCharts)
    if (document.getElementById('usageRetentionScatterChart')) {
        // Dados simulados para o gráfico de dispersão
        const scatterData = [
            {
                name: 'Prontuário',
                data: [
                    { x: 92, y: 95, z: 'Prontuário' }
                ]
            },
            {
                name: 'Agenda',
                data: [
                    { x: 87, y: 90, z: 'Agenda' }
                ]
            },
            {
                name: 'Amigo Intelligence',
                data: [
                    { x: 65, y: 78, z: 'Amigo Intelligence' }
                ]
            },
            {
                name: 'Amigo Pay',
                data: [
                    { x: 48, y: 62, z: 'Amigo Pay' }
                ]
            },
            {
                name: 'Rede Amigo',
                data: [
                    { x: 42, y: 55, z: 'Rede Amigo' }
                ]
            },
            {
                name: 'Contabilidade',
                data: [
                    { x: 38, y: 45, z: 'Contabilidade' }
                ]
            },
            {
                name: 'Telemedicina',
                data: [
                    { x: 35, y: 40, z: 'Telemedicina' }
                ]
            }
        ];
        
        const scatterOptions = {
            chart: {
                height: 450,
                type: 'scatter',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                zoom: {
                    enabled: true,
                    type: 'xy'
                },
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: true,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    }
                },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800,
                    animateGradually: {
                        enabled: true,
                        delay: 150
                    },
                    dynamicAnimation: {
                        enabled: true,
                        speed: 350
                    }
                }
            },
            colors: ['#007AFF', '#34C759', '#5856D6', '#FF9500', '#FF2D55', '#AF52DE', '#5AC8FA'],
            series: scatterData,
            xaxis: {
                title: {
                    text: 'Taxa de Uso (%)',
                    style: {
                        fontSize: '12px',
                        fontWeight: 500
                    }
                },
                min: 0,
                max: 100,
                tickAmount: 10
            },
            yaxis: {
                title: {
                    text: 'Taxa de Retenção (%)',
                    style: {
                        fontSize: '12px',
                        fontWeight: 500
                    }
                },
                min: 0,
                max: 100,
                tickAmount: 10
            },
            markers: {
                size: [10, 15],
                strokeWidth: 0
            },
            tooltip: {
                custom: function({series, seriesIndex, dataPointIndex, w}) {
                    const product = w.globals.seriesNames[seriesIndex];
                    const usage = w.globals.series[seriesIndex][dataPointIndex].x;
                    const retention = w.globals.series[seriesIndex][dataPointIndex].y;
                    
                    return `
                    <div class="apexcharts-tooltip-title" style="font-weight: bold; margin-bottom: 5px;">${product}</div>
                    <div class="apexcharts-tooltip-series-group">
                        <span>Uso: <b>${usage}%</b></span><br>
                        <span>Retenção: <b>${retention}%</b></span><br>
                        <span>Correlação: <b>${Math.round((retention/usage) * 100)}%</b></span>
                    </div>
                    `;
                }
            },
            grid: {
                borderColor: '#f1f1f1',
                xaxis: {
                    lines: {
                        show: true
                    }
                },
                yaxis: {
                    lines: {
                        show: true
                    }
                }
            },
            legend: {
                position: 'top',
                horizontalAlign: 'right',
                floating: true,
                offsetY: -25,
                offsetX: -5
            },
            annotations: {
                xaxis: [
                    {
                        x: 50,
                        strokeDashArray: 0,
                        borderColor: '#775DD0',
                        label: {
                            borderColor: '#775DD0',
                            style: {
                                color: '#fff',
                                background: '#775DD0'
                            },
                            text: 'Uso Médio'
                        }
                    }
                ],
                yaxis: [
                    {
                        y: 65,
                        strokeDashArray: 0,
                        borderColor: '#FF4560',
                        label: {
                            borderColor: '#FF4560',
                            style: {
                                color: '#fff',
                                background: '#FF4560'
                            },
                            text: 'Retenção Média'
                        }
                    }
                ]
            }
        };
        
        const scatterChart = new ApexCharts(document.getElementById('usageRetentionScatterChart'), scatterOptions);
        scatterChart.render();
        
        // Adicionar event listener para o filtro
        if (document.getElementById('scatterPlotFilter')) {
            document.getElementById('scatterPlotFilter').addEventListener('change', function() {
                const filterValue = this.value;
                let filteredData = [];
                
                if (filterValue === 'core') {
                    filteredData = scatterData.filter(item => 
                        ['Prontuário', 'Agenda', 'Amigo Intelligence'].includes(item.name)
                    );
                } else if (filterValue === 'premium') {
                    filteredData = scatterData.filter(item => 
                        ['Amigo Pay', 'Rede Amigo', 'Telemedicina'].includes(item.name)
                    );
                } else {
                    filteredData = scatterData;
                }
                
                scatterChart.updateSeries(filteredData);
            });
        }
    }
});
