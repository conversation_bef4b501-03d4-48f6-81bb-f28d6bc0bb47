/**
 * Chart Compatibility Layer
 * Este script fornece compatibilidade entre Chart.js e ApexCharts
 * Ele verifica o tipo de elemento antes de tentar inicializar um gráfico
 */

// Função para verificar se um elemento é um canvas ou div
function isCanvasElement(elementId) {
    const element = document.getElementById(elementId);
    return element && element.tagName.toLowerCase() === 'canvas';
}

// Função para verificar se um elemento é um div (para ApexCharts)
function isDivElement(elementId) {
    const element = document.getElementById(elementId);
    return element && element.tagName.toLowerCase() === 'div';
}

// Função segura para obter o contexto de um canvas
function safeGetContext(elementId) {
    const element = document.getElementById(elementId);
    if (!element) {
        console.warn(`Elemento #${elementId} não encontrado.`);
        return null;
    }

    if (element.tagName.toLowerCase() === 'canvas') {
        try {
            return element.getContext('2d');
        } catch (error) {
            console.error(`Erro ao obter contexto 2d para #${elementId}:`, error);
            return null;
        }
    }

    console.warn(`Elemento #${elementId} não é um canvas. Não é possível obter contexto 2d.`);
    return null;
}

// Função para inicializar gráficos de forma segura
function initializeChart(elementId, chartType, chartData, chartOptions, chartLibrary = 'chartjs') {
    // Verificar se o elemento existe
    const element = document.getElementById(elementId);
    if (!element) {
        console.warn(`Elemento #${elementId} não encontrado.`);
        return null;
    }

    // Verificar compatibilidade entre o tipo de elemento e a biblioteca
    if (chartLibrary === 'chartjs' && !isCanvasElement(elementId)) {
        console.warn(`Elemento #${elementId} não é um canvas. Chart.js requer elementos canvas.`);
        return null;
    }

    if (chartLibrary === 'apexcharts' && !isDivElement(elementId)) {
        console.warn(`Elemento #${elementId} não é um div. ApexCharts requer elementos div.`);
        return null;
    }

    // Inicializar o gráfico com a biblioteca apropriada
    try {
        if (chartLibrary === 'chartjs') {
            const ctx = safeGetContext(elementId);
            if (ctx) {
                return new Chart(ctx, {
                    type: chartType,
                    data: chartData,
                    options: chartOptions
                });
            }
        } else if (chartLibrary === 'apexcharts') {
            const options = {
                chart: {
                    type: chartType,
                    height: 350
                },
                series: chartData,
                ...chartOptions
            };
            return new ApexCharts(element, options);
        }
    } catch (error) {
        console.error(`Erro ao inicializar gráfico #${elementId}:`, error);
    }

    return null;
}

// Função para verificar e corrigir elementos de gráfico
function fixChartElements() {
    // Lista de IDs de elementos que foram convertidos de canvas para div (ApexCharts)
    const apexChartsElements = [
        'featureHeatmapChart',
        'featureCorrelationChart',
        'usageRetentionScatterChart',
        'productUsageChart',
        'revenueChart',
        'transactionTypesChart',
        'userEngagementFunnelChart'
    ];

    // Verificar cada elemento e adicionar um aviso no console
    apexChartsElements.forEach(id => {
        const element = document.getElementById(id);
        if (element && element.tagName.toLowerCase() === 'div') {
            console.info(`Elemento #${id} é um div para ApexCharts.`);
        }
    });
}

// Executar a verificação quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    fixChartElements();

    // Substituir a função getContext para evitar erros
    HTMLDivElement.prototype.getContext = function() {
        console.warn('Tentativa de chamar getContext() em um elemento div. Esta função só está disponível para elementos canvas.');
        return null;
    };
});
