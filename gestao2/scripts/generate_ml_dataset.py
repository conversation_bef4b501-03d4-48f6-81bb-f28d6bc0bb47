import pandas as pd
import numpy as np
import logging
import os
import sys

# Adicionar o diretório do app ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Importar Flask app para contexto
from app import create_app
from app.services.data_loader import DataLoader
from app.services.ML.clustering_dataset_service import ClusteringDatasetService

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Regenerar dataset ML de clusterização"""
    try:
        logger.info("🚀 Iniciando regeneração do dataset ML de clusterização...")

        # Criar contexto da aplicação Flask
        app = create_app()

        with app.app_context():
            # 1. Carregar dados brutos
            logger.info("📊 Carregando dados brutos...")
            data_loader = DataLoader()
            df = data_loader.load_data()

            if df is None or df.empty:
                logger.error("❌ Erro: Não foi possível carregar os dados")
                return False

            logger.info(f"✅ Dados carregados: {len(df)} registros")

            # 2. Criar serviço de dataset
            dataset_service = ClusteringDatasetService()

            # 3. Criar dataset de clusterização
            logger.info("🔧 Criando dataset de clusterização...")
            clustering_dataset = dataset_service.create_clustering_dataset(df)

            if clustering_dataset is None or clustering_dataset.empty:
                logger.error("❌ Erro: Não foi possível criar o dataset de clusterização")
                return False

            logger.info(f"✅ Dataset criado: {len(clustering_dataset)} turmas")

            # 4. Validar dataset
            logger.info("🔍 Validando dataset...")
            validation_report = dataset_service.validate_dataset(clustering_dataset)

            # Log estatísticas importantes
            if 'statistics' in validation_report:
                stats = validation_report['statistics']
                if 'taxa_conversao' in stats:
                    conv_stats = stats['taxa_conversao']
                    logger.info(f"📈 Taxa de conversão - Min: {conv_stats['min']:.2f}%, Max: {conv_stats['max']:.2f}%, Média: {conv_stats['mean']:.2f}%")
                    logger.info(f"📊 Taxa de conversão - Zeros: {conv_stats['zeros']}, Não-zeros: {conv_stats['non_zeros']}")

            # 5. Criar mapeamento de IDs numéricos
            logger.info("🔢 Criando mapeamento de IDs numéricos...")
            turma_mapping = pd.DataFrame({
                'turma_id': range(1, len(clustering_dataset) + 1),
                'turma_nome': clustering_dataset['Turma'].values
            })

            # 6. Criar dataset ML final com IDs numéricos
            ml_dataset = clustering_dataset.copy()
            ml_dataset['turma_id'] = range(1, len(ml_dataset) + 1)

            # Reordenar colunas para ter turma_id primeiro
            feature_columns = ['total_leads', 'taxa_conversao', 'volume_implementacoes', 'receita_por_lead']
            ml_dataset_final = ml_dataset[['turma_id'] + feature_columns]

            # 7. Salvar arquivos
            logger.info("💾 Salvando arquivos...")

            # Criar diretórios se não existirem
            os.makedirs('data/ML/datasets', exist_ok=True)

            # Salvar dataset ML principal
            ml_dataset_path = 'data/ML/datasets/clustering_dataset_ml_ready.csv'
            ml_dataset_final.to_csv(ml_dataset_path, index=False, encoding='utf-8')
            logger.info(f"✅ Dataset ML salvo: {ml_dataset_path}")

            # Salvar mapeamento de turmas
            mapping_path = 'data/ML/datasets/turma_id_mapping.csv'
            turma_mapping.to_csv(mapping_path, index=False, encoding='utf-8')
            logger.info(f"✅ Mapeamento salvo: {mapping_path}")

            # Salvar dataset completo (backup)
            backup_path = 'data/ML/datasets/clustering_dataset_complete.csv'
            clustering_dataset.to_csv(backup_path, index=False, encoding='utf-8')
            logger.info(f"✅ Backup completo salvo: {backup_path}")

            # 8. Mostrar estatísticas finais
            logger.info("📊 Estatísticas finais:")
            logger.info(f"   • Total de turmas: {len(ml_dataset_final)}")
            logger.info(f"   • Features: {len(feature_columns)} (removido total_registros)")
            logger.info(f"   • Taxa de conversão média: {ml_dataset_final['taxa_conversao'].mean():.2f}%")
            logger.info(f"   • Turmas com conversão > 0: {(ml_dataset_final['taxa_conversao'] > 0).sum()}")
            logger.info(f"   • Receita média por lead: R$ {ml_dataset_final['receita_por_lead'].mean():.2f}")

            logger.info("🎉 Dataset ML regenerado com sucesso!")
            return True

    except Exception as e:
        logger.error(f"❌ Erro na regeneração do dataset: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
