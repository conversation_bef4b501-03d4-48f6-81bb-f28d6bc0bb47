[{"id": "log_001", "timestamp": "2024-12-19T11:15:30.000Z", "user_id": "bruno@abreu", "action": "login", "description": "Usuário fez login no sistema", "metadata": {"ip_address": "*************", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)", "session_id": "sess_abc123"}, "domain": "business"}, {"id": "log_002", "timestamp": "2024-12-19T11:16:00.000Z", "user_id": "bruno@abreu", "action": "page_view", "description": "Acessou página Dashboard", "metadata": {"page": "dashboard.index", "response_time_ms": 245, "session_id": "sess_abc123"}, "domain": "business"}, {"id": "log_003", "timestamp": "2024-12-19T11:18:15.000Z", "user_id": "bruno@abreu", "action": "page_view", "description": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> Responsáveis", "metadata": {"page": "dashboard.responsibles", "response_time_ms": 180, "session_id": "sess_abc123"}, "domain": "business"}, {"id": "log_004", "timestamp": "2024-12-19T10:30:45.000Z", "user_id": "sergio", "action": "login", "description": "Usuário fez login no sistema", "metadata": {"ip_address": "*************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)", "session_id": "sess_def456"}, "domain": "business"}, {"id": "log_005", "timestamp": "2024-12-19T10:31:00.000Z", "user_id": "sergio", "action": "page_view", "description": "Acessou página Qualidade de Dados", "metadata": {"page": "dashboard.data_quality", "response_time_ms": 320, "session_id": "sess_def456"}, "domain": "business"}, {"id": "log_006", "timestamp": "2024-12-19T09:45:20.000Z", "user_id": "ana.silva", "action": "login", "description": "Usuário fez login no sistema", "metadata": {"ip_address": "*************", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0)", "session_id": "sess_ghi789"}, "domain": "business"}, {"id": "log_007", "timestamp": "2024-12-19T09:46:00.000Z", "user_id": "ana.silva", "action": "page_view", "description": "<PERSON><PERSON><PERSON> p<PERSON> Leads", "metadata": {"page": "dashboard.leads", "response_time_ms": 195, "session_id": "sess_ghi789"}, "domain": "business"}, {"id": "log_008", "timestamp": "2024-12-19T08:20:10.000Z", "user_id": "carlos.santos", "action": "login", "description": "Usuário fez login no sistema", "metadata": {"ip_address": "*************", "user_agent": "Mozilla/5.0 (X11; Linux x86_64)", "session_id": "sess_jkl012"}, "domain": "business"}, {"id": "log_009", "timestamp": "2024-12-19T08:21:30.000Z", "user_id": "carlos.santos", "action": "page_view", "description": "Acessou página Implementações", "metadata": {"page": "dashboard.implementations", "response_time_ms": 280, "session_id": "sess_jkl012"}, "domain": "business"}, {"id": "log_010", "timestamp": "2024-12-19T07:15:45.000Z", "user_id": "TTK", "action": "user_invite", "description": "Convidou novo usuário para o sistema", "metadata": {"invited_email": "<EMAIL>", "role": "user", "permissions_count": 4, "session_id": "sess_admin001"}, "domain": "business"}, {"id": "log_011", "timestamp": "2024-12-18T16:30:00.000Z", "user_id": "bruno@abreu", "action": "permission_update", "description": "Atualizou permissões de usuário", "metadata": {"target_user": "ana.silva", "permissions_added": ["opportunities.view"], "permissions_removed": [], "session_id": "sess_admin002"}, "domain": "business"}, {"id": "log_012", "timestamp": "2024-12-18T14:45:20.000Z", "user_id": "admin", "action": "user_deactivate", "description": "Desativou usuário do sistema", "metadata": {"target_user": "maria.costa", "reason": "Transferida para outro projeto", "session_id": "sess_admin003"}, "domain": "business"}, {"id": "log_013", "timestamp": "2024-12-18T10:20:15.000Z", "user_id": "sergio", "action": "data_export", "description": "Exportou dados do sistema", "metadata": {"export_type": "leads_csv", "records_count": 1250, "file_size_mb": 2.3, "session_id": "sess_def456"}, "domain": "business"}, {"id": "log_014", "timestamp": "2024-12-17T15:30:00.000Z", "user_id": "bruno", "action": "page_view", "description": "Acessou página Classes", "metadata": {"page": "dashboard.classes", "response_time_ms": 210, "session_id": "sess_bruno001"}, "domain": "business"}, {"id": "log_015", "timestamp": "2024-12-17T11:45:30.000Z", "user_id": "carlos.santos", "action": "permission_check", "description": "Tentativa de acesso negada", "metadata": {"page": "users.manage", "permission": "users.manage", "granted": false, "session_id": "sess_jkl012"}, "domain": "business"}]