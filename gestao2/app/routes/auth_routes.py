"""
Secure Authentication routes for Business Domain
Uses SQLite database with bcrypt password hashing and secure HTTP practices
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session, current_app
from app.services.secure_auth_service import SecureAuthService
import logging
from typing import Any
from functools import wraps

logger = logging.getLogger(__name__)

auth_routes = Blueprint('auth', __name__, url_prefix='/auth')

# Initialize secure auth service
auth_service = SecureAuthService()

# Security decorators
def require_login(f):
    """Decorator to require login"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Acesso negado. Faça login primeiro.', 'error')
            return redirect(url_for('auth.login'))

        # Validate session
        if not auth_service.is_session_valid(session['user_id']):
            session.clear()
            flash('Sessão expirada. Faça login novamente.', 'warning')
            return redirect(url_for('auth.login'))

        return f(*args, **kwargs)
    return decorated_function

def require_permission(permission):
    """Decorator to require specific permission"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                flash('Acesso negado. Faça login primeiro.', 'error')
                return redirect(url_for('auth.login'))

            user_permissions = session.get('permissions', [])
            user_role = session.get('role', '')

            # Admin has all permissions
            if user_role == 'admin' or permission in user_permissions:
                return f(*args, **kwargs)
            else:
                flash('Acesso negado. Permissão insuficiente.', 'error')
                return redirect(url_for('main.dashboard'))

        return decorated_function
    return decorator

def require_admin(f):
    """Decorator to require admin role"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Acesso negado. Faça login primeiro.', 'error')
            return redirect(url_for('auth.login'))

        if session.get('role') != 'admin':
            flash('Acesso negado. Permissão de administrador necessária.', 'error')
            return redirect(url_for('main.dashboard'))

        return f(*args, **kwargs)
    return decorated_function

@auth_routes.route('/login', methods=['GET', 'POST'])  # SECURITY: Explicit HTTP methods
def login() -> str:
    """Secure login page for business domain"""
    # Redirect if already logged in
    if 'user_id' in session and auth_service.is_session_valid(session['user_id']):
        return redirect('/')

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')

        if not username or not password:
            flash('Por favor, preencha todos os campos', 'error')
            return render_template('auth/login_business.html')

        # Authenticate using secure service
        user = auth_service.authenticate_user(username, password)

        if user:
            # Create secure session
            session.permanent = True
            session['user_id'] = str(user['id'])
            session['username'] = user['username']
            session['email'] = user['email']
            session['role'] = user['role']
            session['permissions'] = user['permissions']
            session['domain'] = user['domain']

            # Log successful login
            auth_service.log_activity(
                user_id=str(user['id']),
                action='login_success',
                description=f'Login bem-sucedido para usuário {user["username"]}',
                page='auth.login',
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent'),
                metadata={'role': user['role'], 'domain': user['domain']}
            )

            flash(f'Bem-vindo, {user["username"]}!', 'success')

            # Redirect to intended page or dashboard
            next_page = request.args.get('next')
            if next_page and next_page.startswith('/'):
                return redirect(next_page)
            return redirect('/')
        else:
            # Log failed login attempt
            auth_service.log_activity(
                user_id=username,
                action='login_failed',
                description=f'Tentativa de login falhada para usuário {username}',
                page='auth.login',
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent'),
                metadata={'attempted_username': username}
            )
            flash('Usuário ou senha inválidos', 'error')

    return render_template('auth/login_business.html')

@auth_routes.route('/logout', methods=['GET', 'POST'])  # SECURITY: Explicit HTTP methods
def logout() -> Any:
    """Secure logout"""
    if 'user_id' in session:
        # Log logout activity
        auth_service.log_activity(
            user_id=session['user_id'],
            action='logout',
            description=f'User {session.get("username", "unknown")} logged out',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

    # Clear session
    session.clear()
    flash('Logout realizado com sucesso', 'success')
    return redirect(url_for('auth.login'))

@auth_routes.route('/profile', methods=['GET'])  # SECURITY: GET method is safe for read operations
def profile() -> str:
    """User profile page"""
    if 'user_id' not in session:
        flash('Acesso negado. Faça login primeiro.', 'error')
        return redirect(url_for('auth.login'))

    return render_template('auth/profile.html', user=session)

@auth_routes.route('/change-password', methods=['GET', 'POST'])  # SECURITY: Explicit HTTP methods
@require_login
def change_password() -> str:
    """Secure password change"""
    if request.method == 'POST':
        current_password = request.form.get('current_password', '')
        new_password = request.form.get('new_password', '')
        confirm_password = request.form.get('confirm_password', '')

        if not all([current_password, new_password, confirm_password]):
            flash('Por favor, preencha todos os campos', 'error')
            return render_template('auth/change_password.html')

        if new_password != confirm_password:
            flash('Nova senha e confirmação não coincidem', 'error')
            return render_template('auth/change_password.html')

        if len(new_password) < 6:
            flash('Nova senha deve ter pelo menos 6 caracteres', 'error')
            return render_template('auth/change_password.html')

        # Change password using secure service
        if auth_service.change_password(session['user_id'], current_password, new_password):
            flash('Senha alterada com sucesso', 'success')
            return redirect(url_for('auth.profile'))
        else:
            flash('Senha atual incorreta', 'error')

    return render_template('auth/change_password.html')

@auth_routes.route('/admin', methods=['GET'])  # SECURITY: GET method is safe for read operations
@require_admin
def admin_dashboard() -> str:
    """Admin dashboard"""
    return render_template('admin/dashboard.html')

@auth_routes.route('/admin/users', methods=['GET'])  # SECURITY: GET method is safe for read operations
@require_admin
def admin_users() -> str:
    """Admin users management"""
    return render_template('admin/users.html')

@auth_routes.route('/admin/monitoring', methods=['GET'])  # SECURITY: GET method is safe for read operations
@require_admin
def admin_monitoring() -> Any:
    """Admin monitoring dashboard"""
    return redirect('/api/monitoring/dashboard')

@auth_routes.route('/admin/create-user', methods=['GET', 'POST'])  # SECURITY: Explicit HTTP methods
@require_admin
def admin_create_user() -> str:
    """Create new user (admin only)"""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '')
        role = request.form.get('role', 'user')

        if not all([username, email, password]):
            flash('Por favor, preencha todos os campos', 'error')
            return render_template('admin/create_user.html')

        if len(password) < 6:
            flash('Senha deve ter pelo menos 6 caracteres', 'error')
            return render_template('admin/create_user.html')

        # Default permissions based on role
        permissions = []
        if role == 'admin':
            permissions = [
                'dashboard.view', 'leads.view', 'leads.edit',
                'opportunities.view', 'opportunities.edit',
                'implementations.view', 'implementations.edit',
                'universities.view', 'universities.edit',
                'responsibles.view', 'responsibles.edit',
                'classes.view', 'classes.edit',
                'coupons.view', 'coupons.edit',
                'conversion.view', 'subscriptions.view',
                'data_quality.view', 'business_rules.view',
                'users.manage'
            ]
        else:
            permissions = [
                'dashboard.view', 'leads.view',
                'opportunities.view', 'implementations.view',
                'universities.view', 'responsibles.view',
                'classes.view', 'coupons.view',
                'conversion.view', 'subscriptions.view'
            ]

        if auth_service.create_user(username, email, password, role, permissions):
            flash(f'Usuário {username} criado com sucesso', 'success')
            return redirect(url_for('auth.admin_users'))
        else:
            flash('Erro ao criar usuário. Username ou email já existem.', 'error')

    return render_template('admin/create_user.html')
