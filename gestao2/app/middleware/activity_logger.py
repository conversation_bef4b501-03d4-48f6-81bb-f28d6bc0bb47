"""
Activity Logger Middleware
Automatically logs user activities for monitoring and audit purposes
"""

import logging
from functools import wraps
from flask import request, session, g
from typing import Dict, Any, Optional
import json

logger = logging.getLogger(__name__)

class ActivityLogger:
    """Middleware for logging user activities"""

    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)

    def init_app(self, app):
        """Initialize the activity logger with Flask app"""
        # Registrar com prioridade baixa para não conflitar com outros middlewares
        app.before_request_funcs.setdefault(None, []).append(self.before_request)
        app.after_request_funcs.setdefault(None, []).append(self.after_request)

    def before_request(self):
        """Log request start - only set user info, don't interfere with timing"""
        # Definir informações do usuário para logging (sem interferir com start_time)
        if not hasattr(g, 'user_id'):
            g.user_id = session.get('username', 'anonymous')
        if not hasattr(g, 'page'):
            g.page = request.endpoint or 'unknown'

    def after_request(self, response):
        """Log request completion"""
        try:
            # Only log for successful page views
            if response.status_code == 200 and request.method == 'GET':
                self.log_page_view(
                    user_id=g.get('user_id', 'anonymous'),
                    page=g.get('page', 'unknown'),
                    status_code=response.status_code
                )
        except Exception as e:
            logger.error(f"Error in activity logger: {e}")

        return response

    def log_page_view(self, user_id: str, page: str, status_code: int = 200):
        """Log a page view activity"""
        try:
            from app.services.secure_auth_service import SecureAuthService

            auth_service = SecureAuthService()

            # Create description based on page
            page_descriptions = {
                'dashboard.index': 'Acessou Dashboard Principal',
                'lead.index': 'Visualizou Lista de Leads',
                'opportunity.index': 'Visualizou Lista de Oportunidades',
                'conversion.index': 'Acessou Análise de Conversão',
                'commercial_intelligence.index': 'Acessou Inteligência Comercial',
                'admin.monitoring': 'Acessou Painel de Monitoramento',
                'auth.login': 'Página de Login',
                'auth.logout': 'Logout do Sistema'
            }

            description = page_descriptions.get(page, f'Acessou página: {page}')

            auth_service.log_activity(
                user_id=user_id,
                action='page_view',
                description=description,
                page=page,
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent'),
                metadata={
                    'status_code': status_code,
                    'method': request.method,
                    'url': request.url
                }
            )

        except Exception as e:
            logger.error(f"Error logging page view: {e}")

def log_activity(action: str, description: str = None, metadata: Dict[str, Any] = None):
    """Decorator to log specific activities"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # Execute the function first
                result = func(*args, **kwargs)

                # Log the activity after successful execution
                from app.services.secure_auth_service import SecureAuthService

                auth_service = SecureAuthService()
                user_id = session.get('username', 'anonymous')

                auth_service.log_activity(
                    user_id=user_id,
                    action=action,
                    description=description or f'Executou ação: {action}',
                    page=request.endpoint,
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent'),
                    metadata=metadata or {}
                )

                return result

            except Exception as e:
                logger.error(f"Error in activity logging decorator: {e}")
                # Still return the function result even if logging fails
                return func(*args, **kwargs)

        return wrapper
    return decorator

def log_login_activity(user_id: str, success: bool = True):
    """Log login activity"""
    try:
        from app.services.secure_auth_service import SecureAuthService

        auth_service = SecureAuthService()

        action = 'login_success' if success else 'login_failed'
        description = f'Login {"bem-sucedido" if success else "falhou"} para usuário {user_id}'

        auth_service.log_activity(
            user_id=user_id,
            action=action,
            description=description,
            page='auth.login',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            metadata={
                'success': success,
                'timestamp': None  # Will be set by the database
            }
        )

    except Exception as e:
        logger.error(f"Error logging login activity: {e}")

def log_logout_activity(user_id: str):
    """Log logout activity"""
    try:
        from app.services.secure_auth_service import SecureAuthService

        auth_service = SecureAuthService()

        auth_service.log_activity(
            user_id=user_id,
            action='logout',
            description=f'Usuário {user_id} fez logout do sistema',
            page='auth.logout',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            metadata={}
        )

    except Exception as e:
        logger.error(f"Error logging logout activity: {e}")

def log_data_export(user_id: str, export_type: str, filename: str = None):
    """Log data export activity"""
    try:
        from app.services.secure_auth_service import SecureAuthService

        auth_service = SecureAuthService()

        description = f'Exportou dados: {export_type}'
        if filename:
            description += f' (arquivo: {filename})'

        auth_service.log_activity(
            user_id=user_id,
            action='data_export',
            description=description,
            page=request.endpoint,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            metadata={
                'export_type': export_type,
                'filename': filename
            }
        )

    except Exception as e:
        logger.error(f"Error logging data export: {e}")

# Initialize the activity logger
activity_logger = ActivityLogger()
