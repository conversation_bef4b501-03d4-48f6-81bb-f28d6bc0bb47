{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table %}

{% block title %}Comparação de Clusterizações - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Comparação de Clusterizações",
    subtitle="Compare diferentes tipos de segmentação lado a lado para identificar os melhores insights para seu negócio.",
    stats=[
        {
            "label": "Tipos Comparados",
            "value": comparison_results|length,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />'
        },
        {
            "label": "Opções Disponíveis",
            "value": clustering_options|length,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />'
        }
    ]
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    {% if comparison_results %}
    <!-- Comparison Overview -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">📊 Visão Geral da Comparação</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for clustering_type, result in comparison_results.items() %}
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-semibold text-gray-900">
                        {{ clustering_options.get(clustering_type, {}).get('name', clustering_type.title()) }}
                    </h4>
                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {{ result.clusters|length }} clusters
                    </span>
                </div>
                
                <p class="text-sm text-gray-600 mb-4">
                    {{ clustering_options.get(clustering_type, {}).get('description', 'Segmentação personalizada') }}
                </p>
                
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total de Turmas:</span>
                        <span class="font-medium">{{ result.summary.get('total_turmas', 0)|e }}</span>
                    </div>
                    {% if result.summary.get('total_receita') %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Receita Total:</span>
                        <span class="font-medium">R$ {{ "{:,.2f}".format(result.summary.total_receita) }}</span>
                    </div>
                    {% endif %}
                    {% if result.summary.get('avg_performance_score') %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Score Médio:</span>
                        <span class="font-medium">{{ "{:.1%}".format(result.summary.avg_performance_score) }}</span>
                    </div>
                    {% endif %}
                </div>
                
                <div class="mt-4">
                    <a href="{{ url_for('clustering.results', type=clustering_type, clusters=result.n_clusters or 5) }}" 
                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        Ver Detalhes →
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Detailed Comparison -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">🔍 Comparação Detalhada</h3>
        
        {% for clustering_type, result in comparison_results.items() %}
        <div class="mb-8 last:mb-0">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-base font-medium text-gray-900">
                    {{ clustering_options.get(clustering_type, {}).get('name', clustering_type.title()) }}
                </h4>
                <span class="text-sm text-gray-500">{{ result.clusters|length }} clusters</span>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {% for cluster in result.clusters %}
                <div class="border border-gray-200 rounded-lg p-3 bg-gray-50">
                    <div class="flex justify-between items-start mb-2">
                        <h5 class="font-medium text-gray-900 text-sm">{{ cluster.name|e }}</h5>
                        <span class="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded">
                            {{ cluster.size|e }} turmas
                        </span>
                    </div>
                    
                    <div class="space-y-1 text-xs">
                        {% if cluster.get('total_revenue') %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">Receita:</span>
                            <span class="font-medium">R$ {{ "{:,.0f}".format(cluster.total_revenue) }}</span>
                        </div>
                        {% endif %}
                        {% if cluster.get('total_leads') %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">Leads:</span>
                            <span class="font-medium">{{ cluster.total_leads|e }}</span>
                        </div>
                        {% endif %}
                        {% if cluster.get('avg_performance_score') %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">Performance:</span>
                            <span class="font-medium">{{ "{:.1%}".format(cluster.avg_performance_score) }}</span>
                        </div>
                        {% endif %}
                        {% if cluster.get('avg_value') %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">Valor Médio:</span>
                            <span class="font-medium">R$ {{ "{:,.2f}".format(cluster.avg_value) }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Insights and Recommendations -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">💡 Insights da Comparação</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="font-medium text-gray-900 mb-3">📈 Principais Descobertas</h4>
                <ul class="space-y-2 text-sm text-gray-700">
                    {% set total_types = comparison_results|length %}
                    <li class="flex items-start">
                        <span class="text-blue-600 mr-2">•</span>
                        <span>{{ total_types|e }} tipos de segmentação foram comparados</span>
                    </li>
                    {% for clustering_type, result in comparison_results.items() %}
                    <li class="flex items-start">
                        <span class="text-blue-600 mr-2">•</span>
                        <span>{{ clustering_options.get(clustering_type, {}).get('name', clustering_type.title()) }}: {{ result.clusters|length }} clusters identificados</span>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            
            <div>
                <h4 class="font-medium text-gray-900 mb-3">🎯 Recomendações</h4>
                <ul class="space-y-2 text-sm text-gray-700">
                    <li class="flex items-start">
                        <span class="text-green-600 mr-2">✓</span>
                        <span>Analise cada tipo de segmentação para diferentes objetivos de negócio</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-600 mr-2">✓</span>
                        <span>Use segmentação por valor para estratégias de pricing</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-600 mr-2">✓</span>
                        <span>Aplique segmentação por performance para otimização de processos</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-600 mr-2">✓</span>
                        <span>Combine diferentes segmentações para insights mais profundos</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    {% else %}
    <!-- No Results -->
    <div class="bg-white rounded-lg shadow-sm p-8 border border-gray-200 text-center">
        <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma Comparação Disponível</h3>
        <p class="text-gray-600 mb-6">Não foi possível gerar comparações com os dados disponíveis.</p>
        <a href="{{ url_for('clustering.index') }}" 
           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
            Voltar à Clusterização
        </a>
    </div>
    {% endif %}

    <!-- Actions -->
    <div class="mt-8 flex justify-center space-x-4">
        <a href="{{ url_for('clustering.index') }}" 
           class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Voltar à Clusterização
        </a>
        
        {% if comparison_results %}
        <button onclick="window.print()" 
                class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
            </svg>
            Imprimir Comparação
        </button>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add any interactive functionality here if needed
        console.log('Comparison page loaded');
    });
</script>
{% endblock %}
