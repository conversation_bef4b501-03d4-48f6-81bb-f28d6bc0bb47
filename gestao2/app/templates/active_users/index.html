{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table, business_tooltip %}

{% block title %}Usuários Ativos - Análise Avançada de Notas Fiscais - {{ app_name|e }}{% endblock %}

{% block content %}

<!-- Minimal Hero Section -->
<div class="bg-white border-b border-gray-100 mb-6">
    <div class="max-w-7xl mx-auto px-4 py-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Usuários Ativos</h1>
                <p class="text-sm text-gray-600 mt-1">Análise de notas fiscais e produtividade</p>
            </div>
            <div class="flex space-x-6 text-sm">
                <div class="text-center">
                    <div class="text-lg font-semibold text-blue-600">{{ "{:,}".format(kpis.total_producers if kpis and kpis.total_producers else 0).replace(',', '.') }}</div>
                    <div class="text-gray-500">Produtores</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-semibold text-gray-600">{{ "{:,}".format(kpis.total_invoices if kpis and kpis.total_invoices else 0).replace(',', '.') }}</div>
                    <div class="text-gray-500">Notas</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-semibold text-blue-600">R$ {{ "{:,.0f}".format(kpis.total_revenue if kpis and kpis.total_revenue else 0).replace(',', '.') }}</div>
                    <div class="text-gray-500">Faturamento</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Executive Summary -->
    {% if kpis %}
    <div class="bg-white rounded-lg p-6 border border-gray-200 shadow-sm mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Resumo Executivo - Usuários Ativos</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <div class="w-3 h-3 bg-blue-600 rounded-full"></div>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Faturamento Total</p>
                        <p class="text-sm text-gray-600">R$ {{ "{:,.2f}".format(kpis.total_revenue).replace(',', 'X').replace('.', ',').replace('X', '.') }} gerado por {{ "{:,}".format(kpis.total_producers).replace(',', '.') }} produtores ativos</p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <div class="w-3 h-3 bg-gray-600 rounded-full"></div>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Volume de Notas</p>
                        <p class="text-sm text-gray-600">{{ "{:,}".format(kpis.total_invoices).replace(',', '.') }} notas fiscais com ticket médio de R$ {{ "{:,.2f}".format(kpis.avg_ticket).replace(',', 'X').replace('.', ',').replace('X', '.') }}</p>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <div class="w-3 h-3 bg-blue-600 rounded-full"></div>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Cobertura Acadêmica</p>
                        <p class="text-sm text-gray-600">
                            {{ turma_analysis.summary.total_turmas if turma_analysis and 'summary' in turma_analysis else 0 }} turmas e
                            {{ university_analysis.summary.total_universidades if university_analysis and 'summary' in university_analysis else 0 }} universidades
                        </p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <div class="w-3 h-3 bg-gray-600 rounded-full"></div>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Alcance Geográfico</p>
                        <p class="text-sm text-gray-600">
                            {{ geographic_analysis.summary.total_states if geographic_analysis and 'summary' in geographic_analysis else 0 }} estados e
                            {{ geographic_analysis.summary.total_cities if geographic_analysis and 'summary' in geographic_analysis else 0 }} cidades
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- KPIs Principais -->
    {% if kpis %}
    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Indicadores de Performance</h2>

        <!-- KPIs usando macro padronizado -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- Produtores Ativos -->
            <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
                <div class="flex items-center mb-4">
                    <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                        <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                    <div>
                        <div class="flex items-center">
                            <p class="text-sm font-medium text-gray-500 mb-1">Produtores Ativos</p>
                            {{ business_tooltip(
                                title="Produtores Ativos (Usuários Ativos)",
                                description="Usuários que possuem implementação finalizada e geraram pelo menos uma nota fiscal, representando clientes ativos e produtivos.",
                                formula="COUNT(DISTINCT Lead_id WHERE Status_Implantacao = 'Finalizado' AND Gerou_Nota_Fiscal = 'Sim')",
                                columns="Lead_id, Status_Implantacao, Gerou_Nota_Fiscal",
                                data_source="base_dados.csv → Contagem de usuários únicos com implementação finalizada e notas fiscais",
                                calculation_method="Contagem de registros únicos onde Status_Implantacao = 'Finalizado' E Gerou_Nota_Fiscal = 'Sim'"
                            ) }}
                        </div>
                        <p class="text-3xl font-bold text-gray-900">{{ "{:,}".format(kpis.total_producers).replace(',', '.') }}</p>
                    </div>
                </div>
                <p class="text-sm text-gray-500 mt-1">Usuários gerando notas fiscais</p>
                {% if kpis.get('deltas') and kpis.deltas.producer_delta %}
                <div class="mt-2 flex items-center text-sm">
                    <span class="text-{{ 'green' if kpis.deltas.producer_delta > 0 else 'red' }}-600 font-medium">
                        {{ "{:.1f}".format(kpis.deltas.producer_delta).replace('.', ',') }}%
                    </span>
                    <span class="text-gray-500 ml-1">variação mensal</span>
                </div>
                {% endif %}
            </div>

            <!-- Produtores Inativos -->
            <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
                <div class="flex items-center mb-4">
                    <div class="w-14 h-14 rounded-full bg-primary-50 flex items-center justify-center mr-4">
                        <svg class="w-7 h-7 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                        </svg>
                    </div>
                    <div>
                        <div class="flex items-center">
                            <p class="text-sm font-medium text-gray-500 mb-1">Produtores Inativos</p>
                            {{ business_tooltip(
                                title="Produtores Inativos",
                                description="Usuários com implementação finalizada que ainda não geraram notas fiscais, representando potencial não realizado.",
                                formula="COUNT(DISTINCT Lead_id WHERE Status_Implantacao = 'Finalizado' AND Gerou_Nota_Fiscal = 'Não')",
                                columns="Lead_id, Status_Implantacao, Gerou_Nota_Fiscal",
                                data_source="base_dados.csv → Contagem de usuários únicos com implementação finalizada sem notas fiscais",
                                calculation_method="Contagem de registros únicos onde Status_Implantacao = 'Finalizado' E Gerou_Nota_Fiscal = 'Não'"
                            ) }}
                        </div>
                        <p class="text-3xl font-bold text-gray-900">{{ "{:,}".format(kpis.inactive_producers|default(0)).replace(',', '.') }}</p>
                    </div>
                </div>
                <p class="text-sm text-gray-500 mt-1">Usuários sem geração de notas</p>
            </div>

            <!-- Total de Notas -->
            <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
                <div class="flex items-center mb-4">
                    <div class="w-14 h-14 rounded-full bg-primary-50 flex items-center justify-center mr-4">
                        <svg class="w-7 h-7 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                    <div>
                        <div class="flex items-center">
                            <p class="text-sm font-medium text-gray-500 mb-1">Total de Notas</p>
                            {{ business_tooltip(
                                title="Total de Notas Fiscais",
                                description="Número total de notas fiscais emitidas por usuários ativos, representando o volume de atividade produtiva.",
                                formula="COUNT(Nota_Fiscal_id WHERE Gerou_Nota_Fiscal = 'Sim')",
                                columns="Nota_Fiscal_id, Gerou_Nota_Fiscal",
                                data_source="base_dados.csv → Contagem de notas fiscais emitidas",
                                calculation_method="Contagem de registros onde Gerou_Nota_Fiscal = 'Sim', considerando múltiplas notas por usuário"
                            ) }}
                        </div>
                        <p class="text-3xl font-bold text-gray-900">{{ "{:,}".format(kpis.total_invoices).replace(',', '.') }}</p>
                    </div>
                </div>
                <p class="text-sm text-gray-500 mt-1">Notas fiscais emitidas</p>
            </div>

            <!-- Faturamento Total -->
            <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
                <div class="flex items-center mb-4">
                    <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                        <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div>
                        <div class="flex items-center">
                            <p class="text-sm font-medium text-gray-500 mb-1">Faturamento Total</p>
                            {{ business_tooltip(
                                title="Faturamento Total",
                                description="Receita total gerada por todos os usuários ativos através das notas fiscais emitidas, representando o valor econômico da base ativa.",
                                formula="SUM(Valor_Nota_Fiscal WHERE Gerou_Nota_Fiscal = 'Sim')",
                                columns="Valor_Nota_Fiscal, Gerou_Nota_Fiscal",
                                data_source="base_dados.csv → Soma dos valores das notas fiscais emitidas",
                                calculation_method="Soma de todos os valores das notas fiscais onde Gerou_Nota_Fiscal = 'Sim'"
                            ) }}
                        </div>
                        <p class="text-3xl font-bold text-gray-900">R$ {{ "{:,.0f}".format(kpis.total_revenue).replace(',', '.') }}</p>
                    </div>
                </div>
                <p class="text-sm text-gray-500 mt-1">Receita total gerada</p>
                {% if kpis.get('deltas') and kpis.deltas.revenue_delta %}
                <div class="mt-2 flex items-center text-sm">
                    <span class="text-{{ 'green' if kpis.deltas.revenue_delta > 0 else 'red' }}-600 font-medium">
                        {{ "{:.1f}".format(kpis.deltas.revenue_delta).replace('.', ',') }}%
                    </span>
                    <span class="text-gray-500 ml-1">crescimento mensal</span>
                </div>
                {% endif %}
            </div>

            <!-- Ticket Médio -->
            <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
                <div class="flex items-center mb-4">
                    <div class="w-14 h-14 rounded-full bg-primary-50 flex items-center justify-center mr-4">
                        <svg class="w-7 h-7 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    </div>
                    <div>
                        <div class="flex items-center">
                            <p class="text-sm font-medium text-gray-500 mb-1">Ticket Médio</p>
                            {{ business_tooltip(
                                title="Ticket Médio",
                                description="Valor médio por nota fiscal emitida, indicando o valor médio dos serviços prestados pelos usuários ativos.",
                                formula="AVG(Valor_Nota_Fiscal WHERE Gerou_Nota_Fiscal = 'Sim')",
                                columns="Valor_Nota_Fiscal, Gerou_Nota_Fiscal",
                                data_source="base_dados.csv → Média dos valores das notas fiscais emitidas",
                                calculation_method="Média aritmética dos valores das notas fiscais onde Gerou_Nota_Fiscal = 'Sim'"
                            ) }}
                        </div>
                        <p class="text-3xl font-bold text-gray-900">R$ {{ "{:,.0f}".format(kpis.avg_ticket).replace(',', '.') }}</p>
                    </div>
                </div>
                <p class="text-sm text-gray-500 mt-1">Valor médio por nota</p>
                {% if kpis.get('deltas') and kpis.deltas.ticket_delta %}
                <div class="mt-2 flex items-center text-sm">
                    <span class="text-{{ 'green' if kpis.deltas.ticket_delta > 0 else 'red' }}-600 font-medium">
                        {{ "{:.1f}".format(kpis.deltas.ticket_delta).replace('.', ',') }}%
                    </span>
                    <span class="text-gray-500 ml-1">variação mensal</span>
                </div>
                {% endif %}
            </div>

            <!-- Média de Notas por Produtor -->
            <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
                <div class="flex items-center mb-4">
                    <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                        <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <div>
                        <div class="flex items-center">
                            <p class="text-sm font-medium text-gray-500 mb-1">Notas por Produtor</p>
                            {{ business_tooltip(
                                title="Média de Notas por Produtor",
                                description="Número médio de notas fiscais emitidas por cada usuário ativo, indicando o nível de produtividade individual.",
                                formula="COUNT(Nota_Fiscal_id) ÷ COUNT(DISTINCT Lead_id WHERE Gerou_Nota_Fiscal = 'Sim')",
                                columns="Nota_Fiscal_id, Lead_id, Gerou_Nota_Fiscal",
                                data_source="base_dados.csv → Divisão do total de notas pelo número de produtores ativos",
                                calculation_method="Divisão do número total de notas fiscais pelo número de usuários únicos que geraram notas"
                            ) }}
                        </div>
                        <p class="text-3xl font-bold text-gray-900">{{ "{:,.1f}".format(kpis.avg_notes_per_producer if kpis.avg_notes_per_producer else 0).replace(',', 'X').replace('.', ',').replace('X', '.') }}</p>
                    </div>
                </div>
                <p class="text-sm text-gray-500 mt-1">Produtividade média</p>
            </div>
        </div>

        <!-- Análise Month over Month - PRIMEIRO GRÁFICO -->
        {% if monthly_trends and 'error' not in monthly_trends %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <div class="flex items-center mb-6">
                <h3 class="text-lg font-semibold text-gray-900">📈 Evolução Mensal - Últimos 12 Meses</h3>
                {{ business_tooltip(
                    title="Evolução Mensal de Usuários Ativos",
                    description="Análise temporal da evolução de três métricas principais: média de notas por usuário, total de notas e valor médio das notas nos últimos 12 meses.",
                    formula="Três linhas: AVG(notas_por_usuario), COUNT(total_notas), AVG(valor_notas) GROUP BY MONTH",
                    columns="Nota_Fiscal_id, Valor_Nota_Fiscal, Lead_id, Data_Nota",
                    data_source="base_dados.csv → Agrupamento mensal das métricas de produtividade",
                    calculation_method="Cálculo mensal de: 1) Média de notas por usuário ativo, 2) Total de notas emitidas, 3) Valor médio das notas"
                ) }}
            </div>

            <!-- KPIs de Tendência -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                {{ kpi_card(
                    title="Crescimento Médio de Notas",
                    value="{:.1f}%".format(monthly_trends.growth_rates.avg_notes_growth if monthly_trends.growth_rates.avg_notes_growth else 0).replace('.', ','),
                    subtitle="Variação mensal média",
                    icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />',
                    color="success" if (monthly_trends.growth_rates.avg_notes_growth or 0) > 0 else "primary",
                    percentage=None,
                    percentage_label=None,
                    is_positive=true
                ) }}

                {{ kpi_card(
                    title="Crescimento Total de Notas",
                    value="{:.1f}%".format(monthly_trends.growth_rates.total_notes_growth if monthly_trends.growth_rates.total_notes_growth else 0).replace('.', ','),
                    subtitle="Variação mensal total",
                    icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />',
                    color="success" if (monthly_trends.growth_rates.total_notes_growth or 0) > 0 else "primary",
                    percentage=None,
                    percentage_label=None,
                    is_positive=true
                ) }}

                {{ kpi_card(
                    title="Crescimento Valor Médio",
                    value="{:.1f}%".format(monthly_trends.growth_rates.avg_value_growth if monthly_trends.growth_rates.avg_value_growth else 0).replace('.', ','),
                    subtitle="Variação valor médio das notas",
                    icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
                    color="success" if (monthly_trends.growth_rates.avg_value_growth or 0) > 0 else "primary",
                    percentage=None,
                    percentage_label=None,
                    is_positive=true
                ) }}
            </div>

            <!-- Gráfico Month over Month -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Evolução Mensal - Últimos 12 Meses</h4>
                <div class="h-80">
                    <canvas id="monthOverMonthChart"></canvas>
                </div>
                <div class="mt-4 text-sm text-gray-600">
                    <p><strong>Linhas:</strong> Azul = Média de notas por usuário | Verde = Total de notas | Laranja = Valor médio das notas</p>
                    <p><strong>Análise:</strong> Acompanhe a evolução da produtividade e qualidade das notas fiscais ao longo do tempo</p>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Seção de Gráficos de Análise -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Análise Gráfica de Produtividade</h3>

            <!-- Gráficos de Distribuição -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Distribuição de Ticket Médio -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center mb-4">
                        <h4 class="text-md font-semibold text-gray-800">Distribuição de Ticket</h4>
                        {{ business_tooltip(
                            title="Distribuição de Ticket Médio",
                            description="Histograma mostrando a distribuição dos valores de ticket médio por usuário ativo, permitindo identificar padrões de faturamento.",
                            formula="HISTOGRAM(AVG(Valor_Nota_Fiscal) GROUP BY Lead_id)",
                            columns="Valor_Nota_Fiscal, Lead_id",
                            data_source="base_dados.csv → Agrupamento de valores médios por usuário",
                            calculation_method="Cálculo do ticket médio por usuário e distribuição em faixas de valores para visualização em histograma"
                        ) }}
                    </div>
                    <div class="h-64">
                        <canvas id="ticketDistributionChart"></canvas>
                    </div>
                </div>

                <!-- Distribuição de Faturamento -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center mb-4">
                        <h4 class="text-md font-semibold text-gray-800">Distribuição de Faturamento</h4>
                        {{ business_tooltip(
                            title="Distribuição de Faturamento",
                            description="Histograma mostrando a distribuição do faturamento total por usuário ativo, identificando concentração de receita.",
                            formula="HISTOGRAM(SUM(Valor_Nota_Fiscal) GROUP BY Lead_id)",
                            columns="Valor_Nota_Fiscal, Lead_id",
                            data_source="base_dados.csv → Agrupamento de faturamento total por usuário",
                            calculation_method="Cálculo do faturamento total por usuário e distribuição em faixas de valores para análise de concentração"
                        ) }}
                    </div>
                    <div class="h-64">
                        <canvas id="revenueDistributionChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Gráficos Avançados -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- Gráfico de Dispersão: Ticket vs Notas -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center mb-4">
                        <h4 class="text-md font-semibold text-gray-800">Dispersão: Ticket vs Notas</h4>
                        {{ business_tooltip(
                            title="Dispersão Ticket vs Número de Notas",
                            description="Gráfico de dispersão correlacionando ticket médio com número de notas por usuário, identificando padrões de comportamento.",
                            formula="SCATTER(AVG(Valor_Nota_Fiscal), COUNT(Nota_Fiscal_id)) GROUP BY Lead_id",
                            columns="Valor_Nota_Fiscal, Nota_Fiscal_id, Lead_id",
                            data_source="base_dados.csv → Correlação entre ticket médio e volume de notas por usuário",
                            calculation_method="Plotagem de pontos onde X = número de notas e Y = ticket médio para cada usuário ativo"
                        ) }}
                    </div>
                    <div class="h-64">
                        <canvas id="scatterTicketNotasChart"></canvas>
                    </div>
                </div>

                <!-- Distribuição Normal de Produtividade -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center mb-4">
                        <h4 class="text-md font-semibold text-gray-800">Distribuição Normal - Produtividade</h4>
                        {{ business_tooltip(
                            title="Distribuição Normal de Produtividade",
                            description="Análise da distribuição normal do número de notas por usuário, mostrando média, mediana e desvios padrão da produtividade.",
                            formula="NORMAL_DISTRIBUTION(COUNT(Nota_Fiscal_id) GROUP BY Lead_id)",
                            columns="Nota_Fiscal_id, Lead_id",
                            data_source="base_dados.csv → Análise estatística da produtividade por usuário",
                            calculation_method="Cálculo de média, desvio padrão e curva normal da distribuição de notas por usuário ativo"
                        ) }}
                    </div>
                    <div class="h-64">
                        <canvas id="normalDistributionChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Distribuição de Atividade -->
            {% if kpis.get('activity_distribution') %}
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Distribuição de Atividade</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {% for status, quantidade in kpis.activity_distribution.items() %}
                    {{ stat_card(status, "{:,}".format(quantidade).replace(',', '.'), "text-gray-700", "bg-white") }}
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}



    <!-- Análise por Turma e Universidade -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Top Turmas por Produção -->
        {% if turma_analysis and 'error' not in turma_analysis %}
        <div class="bg-white rounded border border-gray-200 p-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Turmas</h3>

            <div class="space-y-3">
                {% for turma in turma_analysis.get('top_turmas', [])[:8] %}
                <div class="flex items-center justify-between p-3 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors">
                    <div class="flex-1">
                        <div class="font-medium text-blue-900">{{ turma.turma|e }}</div>
                        <div class="text-xs text-gray-600">
                            {{ "{:,}".format(turma.num_produtores).replace(',', '.') }} produtores • {{ "{:,}".format(turma.total_notas).replace(',', '.') }} notas • R$ {{ "{:,.2f}".format(turma.faturamento_total|float).replace(',', 'X').replace('.', ',').replace('X', '.') if turma.faturamento_total else "0,00" }}
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-semibold text-blue-600">{{ "{:.1f}".format(turma.produtividade_score|float).replace('.', ',') if turma.produtividade_score else "0,0" }}</div>
                        <div class="text-xs text-blue-600">Score</div>
                    </div>
                </div>
                {% endfor %}
            </div>

            {% if turma_analysis.get('summary') %}
            {{ insight_card("Resumo", "{} turmas analisadas com faturamento total de R$ {:,.2f}".format(
                turma_analysis.summary.total_turmas,
                turma_analysis.summary.total_faturamento
            ).replace(',', 'X').replace('.', ',').replace('X', '.')) }}
            {% endif %}
        </div>
        {% endif %}

        <!-- Top Universidades por Produção -->
        {% if university_analysis and 'error' not in university_analysis %}
        <div class="bg-white rounded border border-gray-200 p-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Universidades</h3>

            <div class="space-y-3">
                {% for univ in university_analysis.get('top_universities', [])[:8] %}
                <div class="flex items-center justify-between p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                    <div class="flex-1">
                        <div class="font-medium text-gray-900">{{ univ.universidade|e }}</div>
                        <div class="text-xs text-gray-600">
                            {{ "{:,}".format(univ.num_produtores).replace(',', '.') }} produtores • {{ "{:,}".format(univ.num_turmas).replace(',', '.') }} turmas • {{ "{:,}".format(univ.total_notas).replace(',', '.') }} notas
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-semibold text-gray-600">R$ {{ "{:,.2f}".format(univ.faturamento_total|float).replace(',', 'X').replace('.', ',').replace('X', '.') if univ.faturamento_total else "0,00" }}</div>
                        <div class="text-xs text-gray-600">Faturamento</div>
                    </div>
                </div>
                {% endfor %}
            </div>

            {% if university_analysis.get('summary') %}
            <div class="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <h4 class="text-sm font-semibold text-blue-900 mb-1">Resumo</h4>
                <p class="text-xs text-blue-800">{{ university_analysis.summary.total_universidades }} universidades • R$ {{ "{:,.0f}".format(university_analysis.summary.total_faturamento).replace(',', '.') }}</p>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>

    <!-- Análise de Tempo para Primeira Nota -->
    {% if time_to_invoice_analysis and 'error' not in time_to_invoice_analysis %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        {{ section_header("Análise de Tempo para Primeira Nota", "Tempo médio entre criação da implementação e primeira nota fiscal") }}

        <!-- Estatísticas Gerais -->
        {% if time_to_invoice_analysis.get('time_stats') %}
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {{ kpi_card("Tempo Médio", "{:.0f} dias".format(time_to_invoice_analysis.time_stats.media_dias).replace('.', ','), "text-blue-600", "bg-blue-50") }}
            {{ kpi_card("Tempo Mediano", "{:.0f} dias".format(time_to_invoice_analysis.time_stats.mediana_dias).replace('.', ','), "text-gray-600", "bg-gray-50") }}
            {{ kpi_card("Menor Tempo", "{} dias".format(time_to_invoice_analysis.time_stats.min_dias), "text-blue-600", "bg-blue-50") }}
            {{ kpi_card("Maior Tempo", "{} dias".format(time_to_invoice_analysis.time_stats.max_dias), "text-gray-600", "bg-gray-50") }}
        </div>
        {% endif %}

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Distribuição por Faixas de Tempo -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-center mb-4">
                    <h4 class="text-md font-semibold text-gray-800">Distribuição por Tempo</h4>
                    {{ business_tooltip(
                        title="Distribuição de Tempo para Primeira Nota",
                        description="Distribuição dos usuários por faixas de tempo entre a implementação e a primeira nota fiscal emitida.",
                        formula="COUNT(Lead_id) GROUP BY CASE WHEN dias <= 30 THEN '0-30 dias' WHEN dias <= 60 THEN '31-60 dias' ... END",
                        columns="Data_Criacao_Implantacao, Data_Primeira_Nota",
                        data_source="base_dados.csv → Cálculo de tempo e agrupamento por faixas",
                        calculation_method="Cálculo da diferença entre data de implementação e primeira nota, agrupado em faixas temporais"
                    ) }}
                </div>
                <div class="h-64">
                    <canvas id="timeDistributionChart"></canvas>
                </div>
            </div>

            <!-- Tendência Mensal -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-center mb-4">
                    <h4 class="text-md font-semibold text-gray-800">Tendência Mensal</h4>
                    {{ business_tooltip(
                        title="Tendência Mensal do Tempo para Primeira Nota",
                        description="Evolução temporal do tempo médio para primeira nota fiscal, permitindo identificar melhorias no processo de onboarding.",
                        formula="AVG(Data_Primeira_Nota - Data_Criacao_Implantacao) GROUP BY MONTH(Data_Criacao_Implantacao)",
                        columns="Data_Criacao_Implantacao, Data_Primeira_Nota",
                        data_source="base_dados.csv → Média mensal do tempo para primeira nota",
                        calculation_method="Cálculo mensal da média de dias entre implementação e primeira nota fiscal"
                    ) }}
                </div>
                <div class="h-64">
                    <canvas id="timeMonthlyTrendChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Top Turmas Mais Rápidas e Mais Lentas -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
            <!-- Turmas Mais Rápidas -->
            {% if time_to_invoice_analysis.get('fastest_turmas') %}
            <div>
                <h4 class="text-md font-semibold text-gray-800 mb-4">Turmas Mais Rápidas</h4>
                <div class="space-y-3">
                    {% for turma in time_to_invoice_analysis.fastest_turmas[:5] %}
                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div class="flex-1">
                            <div class="font-medium text-blue-900">{{ turma.Turma|e }}</div>
                            <div class="text-xs text-gray-600">
                                {{ "{:,}".format(turma.total_registros).replace(',', '.') }} registros • R$ {{ "{:,.2f}".format(turma.valor_medio_mensalidade).replace(',', 'X').replace('.', ',').replace('X', '.') }} mensalidade média
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-blue-600">{{ "{:.0f}".format(turma.media_dias).replace('.', ',') }} dias</div>
                            <div class="text-xs text-blue-600">Média</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Turmas Mais Lentas -->
            {% if time_to_invoice_analysis.get('slowest_turmas') %}
            <div>
                <h4 class="text-md font-semibold text-gray-800 mb-4">Turmas Mais Lentas</h4>
                <div class="space-y-3">
                    {% for turma in time_to_invoice_analysis.slowest_turmas[:5] %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex-1">
                            <div class="font-medium text-gray-900">{{ turma.Turma|e }}</div>
                            <div class="text-xs text-gray-600">
                                {{ "{:,}".format(turma.total_registros).replace(',', '.') }} registros • R$ {{ "{:,.2f}".format(turma.valor_medio_mensalidade).replace(',', 'X').replace('.', ',').replace('X', '.') }} mensalidade média
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-gray-600">{{ "{:.0f}".format(turma.media_dias).replace('.', ',') }} dias</div>
                            <div class="text-xs text-gray-600">Média</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}
    </div>

    <!-- Análise Geográfica -->
    {% if geographic_analysis and 'error' not in geographic_analysis %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        {{ section_header("Análise Geográfica", "Distribuição de usuários ativos por estados e cidades") }}

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Top Estados -->
            <div>
                <h4 class="text-md font-semibold text-gray-800 mb-4">Top Estados</h4>
                <div class="space-y-3">
                    {% for estado in geographic_analysis.get('state_ranking', [])[:8] %}
                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div class="flex-1">
                            <div class="font-medium text-blue-900">{{ estado.estado|e }}</div>
                            <div class="text-xs text-gray-600">
                                {{ "{:,}".format(estado.num_produtores).replace(',', '.') }} produtores • {{ "{:,}".format(estado.num_cidades).replace(',', '.') }} cidades
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-blue-600">R$ {{ "{:,.2f}".format(estado.faturamento_total|float).replace(',', 'X').replace('.', ',').replace('X', '.') if estado.faturamento_total else "0,00" }}</div>
                            <div class="text-xs text-blue-600">{{ "{:,}".format(estado.total_notas).replace(',', '.') }} notas</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Top Cidades -->
            <div>
                <h4 class="text-md font-semibold text-gray-800 mb-4">Top Cidades</h4>
                <div class="space-y-3">
                    {% for cidade in geographic_analysis.get('city_ranking', [])[:8] %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex-1">
                            <div class="font-medium text-gray-900">{{ cidade.cidade|e }}</div>
                            <div class="text-xs text-gray-600">
                                {{ cidade.estado|e }} • {{ "{:,}".format(cidade.num_produtores).replace(',', '.') }} produtores
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-gray-600">R$ {{ "{:,.2f}".format(cidade.faturamento_total|float).replace(',', 'X').replace('.', ',').replace('X', '.') if cidade.faturamento_total else "0,00" }}</div>
                            <div class="text-xs text-gray-600">{{ "{:,}".format(cidade.total_notas).replace(',', '.') }} notas</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        {% if geographic_analysis.get('summary') %}
        <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="p-3 bg-blue-50 rounded-lg border border-blue-200">
                <h4 class="text-sm font-semibold text-blue-900 mb-1">Estado Líder</h4>
                <p class="text-xs text-blue-800">{{ geographic_analysis.summary.top_state or "N/A" }}</p>
            </div>
            <div class="p-3 bg-gray-50 rounded-lg border border-gray-200">
                <h4 class="text-sm font-semibold text-gray-900 mb-1">Cidade Líder</h4>
                <p class="text-xs text-gray-800">{{ geographic_analysis.summary.top_city or "N/A" }}</p>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Análise por Curso e Formação -->
    {% if course_analysis and 'error' not in course_analysis %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        {{ section_header("Análise por Curso e Formação", "Performance por área de formação acadêmica") }}

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Análise por Curso -->
            <div>
                <h4 class="text-md font-semibold text-gray-800 mb-4">Performance por Curso</h4>
                <div class="space-y-3">
                    {% for curso in course_analysis.get('course_ranking', []) %}
                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div class="flex-1">
                            <div class="font-medium text-blue-900">{{ curso.curso|e }}</div>
                            <div class="text-xs text-gray-600">
                                {{ "{:,}".format(curso.num_produtores).replace(',', '.') }} produtores • Ticket médio: R$ {{ "{:,.2f}".format(curso.ticket_medio|float).replace(',', 'X').replace('.', ',').replace('X', '.') if curso.ticket_medio else "0,00" }}
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-blue-600">R$ {{ "{:,.2f}".format(curso.faturamento_total|float).replace(',', 'X').replace('.', ',').replace('X', '.') if curso.faturamento_total else "0,00" }}</div>
                            <div class="text-xs text-blue-600">{{ "{:,}".format(curso.total_notas).replace(',', '.') }} notas</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Análise por Formação -->
            <div>
                <h4 class="text-md font-semibold text-gray-800 mb-4">Performance por Formação</h4>
                <div class="space-y-3">
                    {% for formacao in course_analysis.get('formation_ranking', [])[:8] %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex-1">
                            <div class="font-medium text-gray-900">{{ formacao.formacao|e }}</div>
                            <div class="text-xs text-gray-600">
                                {{ "{:,}".format(formacao.num_produtores).replace(',', '.') }} produtores • Ticket médio: R$ {{ "{:,.2f}".format(formacao.ticket_medio|float).replace(',', 'X').replace('.', ',').replace('X', '.') if formacao.ticket_medio else "0,00" }}
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-gray-600">R$ {{ "{:,.2f}".format(formacao.faturamento_total|float).replace(',', 'X').replace('.', ',').replace('X', '.') if formacao.faturamento_total else "0,00" }}</div>
                            <div class="text-xs text-gray-600">{{ "{:,}".format(formacao.total_notas).replace(',', '.') }} notas</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        {% if course_analysis.get('summary') %}
        <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="p-3 bg-blue-50 rounded-lg border border-blue-200">
                <h4 class="text-sm font-semibold text-blue-900 mb-1">Curso Líder</h4>
                <p class="text-xs text-blue-800">{{ course_analysis.summary.top_course or "N/A" }}</p>
            </div>
            <div class="p-3 bg-gray-50 rounded-lg border border-gray-200">
                <h4 class="text-sm font-semibold text-gray-900 mb-1">Formação Líder</h4>
                <p class="text-xs text-gray-800">{{ course_analysis.summary.top_formation or "N/A" }}</p>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Análise de Status de Implementação -->
    {% if implementation_analysis and 'error' not in implementation_analysis %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        {{ section_header("Análise de Status de Implementação", "Performance por status e fase de implementação") }}

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Status de Implementação -->
            <div>
                <h4 class="text-md font-semibold text-gray-800 mb-4">Status de Implementação</h4>
                <div class="space-y-3">
                    {% for status in implementation_analysis.get('status_ranking', []) %}
                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div class="flex-1">
                            <div class="font-medium text-blue-900">{{ status.status|e }}</div>
                            <div class="text-xs text-gray-600">
                                {{ "{:,}".format(status.num_produtores).replace(',', '.') }} produtores • Ticket médio: R$ {{ "{:,.2f}".format(status.ticket_medio|float).replace(',', 'X').replace('.', ',').replace('X', '.') if status.ticket_medio else "0,00" }}
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-blue-600">R$ {{ "{:,.2f}".format(status.faturamento_total|float).replace(',', 'X').replace('.', ',').replace('X', '.') if status.faturamento_total else "0,00" }}</div>
                            <div class="text-xs text-blue-600">{{ "{:,}".format(status.total_notas).replace(',', '.') }} notas</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Fase de Implementação -->
            <div>
                <h4 class="text-md font-semibold text-gray-800 mb-4">Fase de Implementação</h4>
                <div class="space-y-3">
                    {% for fase in implementation_analysis.get('phase_ranking', []) %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex-1">
                            <div class="font-medium text-gray-900">{{ fase.fase|e }}</div>
                            <div class="text-xs text-gray-600">
                                {{ "{:,}".format(fase.num_produtores).replace(',', '.') }} produtores • Ticket médio: R$ {{ "{:,.2f}".format(fase.ticket_medio|float).replace(',', 'X').replace('.', ',').replace('X', '.') if fase.ticket_medio else "0,00" }}
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-gray-600">R$ {{ "{:,.2f}".format(fase.faturamento_total|float).replace(',', 'X').replace('.', ',').replace('X', '.') if fase.faturamento_total else "0,00" }}</div>
                            <div class="text-xs text-gray-600">{{ "{:,}".format(fase.total_notas).replace(',', '.') }} notas</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        {% if implementation_analysis.get('summary') %}
        <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="p-3 bg-blue-50 rounded-lg border border-blue-200">
                <h4 class="text-sm font-semibold text-blue-900 mb-1">Status Mais Produtivo</h4>
                <p class="text-xs text-blue-800">{{ implementation_analysis.summary.most_productive_status or "N/A" }}</p>
            </div>
            <div class="p-3 bg-gray-50 rounded-lg border border-gray-200">
                <h4 class="text-sm font-semibold text-gray-900 mb-1">Fase Mais Produtiva</h4>
                <p class="text-xs text-gray-800">{{ implementation_analysis.summary.most_productive_phase or "N/A" }}</p>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Ranking de Produtores e Análise Temporal -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Top Produtores -->
        {% if producer_ranking and 'error' not in producer_ranking %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            {{ section_header("Top Produtores Médicos", "Ranking dos maiores produtores") }}

            <div class="space-y-3">
                {% for producer in producer_ranking.get('top_producers', [])[:10] %}
                <div class="flex items-center justify-between p-3 rounded-lg bg-blue-50 hover:bg-blue-100 cursor-pointer transition-colors"
                     onclick="showProducerDetails('{{ producer.id }}')">
                    <div class="flex-1">
                        <div class="font-medium text-blue-900">{{ producer.nome|e }}</div>
                        <div class="text-xs text-gray-600">
                            {{ producer.turma|e }} • {{ producer.universidade|e }}
                        </div>
                        <div class="text-xs text-gray-500">
                            {{ "{:,}".format(producer.total_notas).replace(',', '.') }} notas • {{ "{:.1f}".format(producer.meses_atividade|float).replace('.', ',') if producer.meses_atividade else "0,0" }} meses ativo
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-semibold text-blue-600">R$ {{ "{:,.2f}".format(producer.faturamento_total|float).replace(',', 'X').replace('.', ',').replace('X', '.') if producer.faturamento_total else "0,00" }}</div>
                        <div class="text-xs text-blue-600">R$ {{ "{:,.2f}".format(producer.ticket_medio|float).replace(',', 'X').replace('.', ',').replace('X', '.') if producer.ticket_medio else "0,00" }} ticket médio</div>
                    </div>
                </div>
                {% endfor %}
            </div>

            {% if producer_ranking.get('percentiles') %}
            <div class="mt-4 pt-4 border-t border-gray-200">
                <h4 class="text-sm font-semibold text-gray-800 mb-2">Percentis de Faturamento</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {{ stat_card("P25", "R$ {:,.2f}".format(producer_ranking.percentiles.p25).replace(',', 'X').replace('.', ',').replace('X', '.'), "text-gray-700", "bg-gray-50") }}
                    {{ stat_card("P50", "R$ {:,.2f}".format(producer_ranking.percentiles.p50).replace(',', 'X').replace('.', ',').replace('X', '.'), "text-gray-700", "bg-gray-50") }}
                    {{ stat_card("P75", "R$ {:,.2f}".format(producer_ranking.percentiles.p75).replace(',', 'X').replace('.', ',').replace('X', '.'), "text-gray-700", "bg-gray-50") }}
                    {{ stat_card("P90", "R$ {:,.2f}".format(producer_ranking.percentiles.p90).replace(',', 'X').replace('.', ',').replace('X', '.'), "text-gray-700", "bg-gray-50") }}
                </div>
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- Métricas de Qualidade -->
        {% if quality_metrics and 'error' not in quality_metrics %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            {{ section_header("Métricas de Qualidade", "Análise de consistência e produtividade") }}

            <!-- Consistência de Produção -->
            {% if quality_metrics.get('consistency_metrics') %}
            <div class="mb-6">
                <h4 class="text-md font-semibold text-gray-800 mb-3">Consistência de Produção</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {{ kpi_card("Produtores Consistentes", quality_metrics.consistency_metrics.produtores_consistentes, "text-blue-600", "bg-blue-50") }}
                    {{ kpi_card("Produtores Esporádicos", quality_metrics.consistency_metrics.produtores_esporadicos, "text-gray-600", "bg-gray-50") }}
                </div>
                <div class="mt-3 text-center">
                    {{ kpi_card("Taxa de Consistência", "{:.1f}%".format(quality_metrics.consistency_metrics.taxa_consistencia|float).replace('.', ',') if quality_metrics.consistency_metrics.taxa_consistencia else "0,0%", "text-blue-600", "bg-blue-50") }}
                </div>
            </div>
            {% endif %}

            <!-- Distribuição por Ticket -->
            {% if quality_metrics.get('ticket_distribution') %}
            <div class="mb-6">
                <h4 class="text-md font-semibold text-gray-800 mb-3">Distribuição por Ticket Médio</h4>
                <div class="space-y-2">
                    {% for range_data in quality_metrics.ticket_distribution %}
                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div class="text-sm text-gray-700">{{ range_data.range }}</div>
                        <div class="text-sm font-semibold text-gray-900">{{ "{:,}".format(range_data.count).replace(',', '.') }} produtores</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Produtividade -->
            {% if quality_metrics.get('productivity_metrics') %}
            <div>
                <h4 class="text-md font-semibold text-gray-800 mb-3">Níveis de Produtividade</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {{ stat_card("Alta Produtividade", quality_metrics.productivity_metrics.alta_produtividade, "text-blue-600", "bg-blue-50") }}
                    {{ stat_card("Média Produtividade", quality_metrics.productivity_metrics.media_produtividade, "text-gray-600", "bg-gray-50") }}
                    {{ stat_card("Baixa Produtividade", quality_metrics.productivity_metrics.baixa_produtividade, "text-gray-600", "bg-gray-50") }}
                </div>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>

    <!-- Análise Temporal -->
    {% if temporal_analysis and 'error' not in temporal_analysis %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        {{ section_header("Análise Temporal da Produção", "Tendências e sazonalidade da produção médica") }}

        <!-- Estatísticas de Duração -->
        {% if temporal_analysis.get('duration_stats') %}
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {{ kpi_card("Média de Atividade", "{:.1f} meses".format(temporal_analysis.duration_stats.media|float).replace('.', ',') if temporal_analysis.duration_stats.media else "0,0 meses", "text-blue-600", "bg-blue-50") }}
            {{ kpi_card("Mediana de Atividade", "{:.1f} meses".format(temporal_analysis.duration_stats.mediana|float).replace('.', ',') if temporal_analysis.duration_stats.mediana else "0,0 meses", "text-gray-600", "bg-gray-50") }}
            {{ kpi_card("Menor Período", "{:.1f} meses".format(temporal_analysis.duration_stats.min|float).replace('.', ',') if temporal_analysis.duration_stats.min else "0,0 meses", "text-blue-600", "bg-blue-50") }}
            {{ kpi_card("Maior Período", "{:.1f} meses".format(temporal_analysis.duration_stats.max|float).replace('.', ',') if temporal_analysis.duration_stats.max else "0,0 meses", "text-gray-600", "bg-gray-50") }}
        </div>
        {% endif %}

        <!-- Gráficos de Tendência -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Tendência Mensal -->
            {% if temporal_analysis.get('monthly_trends') %}
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-3">Tendência Mensal</h4>
                <div class="h-64">
                    <canvas id="monthlyTrendsChart"></canvas>
                </div>
            </div>
            {% endif %}

            <!-- Sazonalidade -->
            {% if temporal_analysis.get('seasonality') %}
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-3">Sazonalidade</h4>
                <div class="h-64">
                    <canvas id="seasonalityChart"></canvas>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Tabela Detalhada de Turmas -->
    {% if turma_analysis and 'error' not in turma_analysis %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        {{ section_header("Análise Detalhada por Turma", "Ranking completo de turmas por produtividade") }}

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Turma</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Produtores</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Notas</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Faturamento</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticket Médio</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for turma in turma_analysis.get('turma_ranking', [])[:20] %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ turma.turma|e }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ turma.num_produtores }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ turma.total_notas }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">R$ {{ "{:,.0f}".format(turma.faturamento_total|float) if turma.faturamento_total else "0" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">R$ {{ "{:,.0f}".format(turma.ticket_medio|float) if turma.ticket_medio else "0" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if turma.produtividade_score >= 80 %}bg-green-100 text-green-800
                                {% elif turma.produtividade_score >= 60 %}bg-blue-100 text-blue-800
                                {% elif turma.produtividade_score >= 40 %}bg-yellow-100 text-yellow-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ "{:.1f}".format(turma.produtividade_score|float) if turma.produtividade_score else "0,0" }}
                            </span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

</div>

<!-- Modal para Detalhes do Produtor -->
<div id="producerModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="producerModalTitle">Detalhes do Produtor</h3>
                <button onclick="closeProducerModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div id="producerModalContent">
                <!-- Conteúdo será carregado dinamicamente -->
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Função para mostrar detalhes do produtor
function showProducerDetails(producerId) {
    fetch(`/usuarios-ativos/api/producer-details/${producerId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Erro ao carregar detalhes: ' + data.error);
                return;
            }

            const producer = data.producer;
            const modalContent = document.getElementById('producerModalContent');
            const modalTitle = document.getElementById('producerModalTitle');

            modalTitle.textContent = `Detalhes - ${producer.nome}`;

            modalContent.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-2">Informações Básicas</h4>
                        <p><strong>Nome:</strong> ${producer.nome}</p>
                        <p><strong>Turma:</strong> ${producer.turma}</p>
                        <p><strong>Universidade:</strong> ${producer.universidade}</p>
                        <p><strong>Cidade:</strong> ${producer.cidade}</p>
                        <p><strong>Estado:</strong> ${producer.estado}</p>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-2">Métricas de Produção</h4>
                        <p><strong>Total de Notas:</strong> ${producer.total_notas}</p>
                        <p><strong>Faturamento Total:</strong> R$ ${producer.faturamento_total.toLocaleString('pt-BR')}</p>
                        <p><strong>Ticket Médio:</strong> R$ ${producer.ticket_medio.toLocaleString('pt-BR')}</p>
                        <p><strong>Média Mensal:</strong> ${producer.media_mensal.toFixed(1)} notas/mês</p>
                        <p><strong>Meses de Atividade:</strong> ${producer.meses_atividade.toFixed(1)}</p>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-2">Datas</h4>
                        <p><strong>Primeira Nota:</strong> ${producer.data_primeira_nota || 'N/A'}</p>
                        <p><strong>Última Nota:</strong> ${producer.data_ultima_nota || 'N/A'}</p>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-2">Status</h4>
                        <p><strong>Gera Nota Fiscal:</strong> ${producer.gerou_nota_fiscal}</p>
                    </div>
                </div>
            `;

            document.getElementById('producerModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao carregar detalhes do produtor');
        });
}

function closeProducerModal() {
    document.getElementById('producerModal').classList.add('hidden');
}

// Gráficos
document.addEventListener('DOMContentLoaded', function() {
    // Gráfico de Tendência Mensal
    {% if temporal_analysis and temporal_analysis.get('monthly_trends') %}
    const monthlyCtx = document.getElementById('monthlyTrendsChart');
    if (monthlyCtx) {
        new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: {{ temporal_analysis.monthly_trends | map(attribute='month') | list | tojson }},
                datasets: [{
                    label: 'Faturamento',
                    data: {{ temporal_analysis.monthly_trends | map(attribute='faturamento') | list | tojson }},
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.1
                }, {
                    label: 'Total de Notas',
                    data: {{ temporal_analysis.monthly_trends | map(attribute='total_notas') | list | tojson }},
                    borderColor: 'rgb(16, 185, 129)',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.1,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    }
    {% endif %}

    // Gráfico de Sazonalidade
    {% if temporal_analysis and temporal_analysis.get('seasonality') %}
    const seasonalityCtx = document.getElementById('seasonalityChart');
    if (seasonalityCtx) {
        new Chart(seasonalityCtx, {
            type: 'bar',
            data: {
                labels: {{ temporal_analysis.seasonality | map(attribute='month') | list | tojson }},
                datasets: [{
                    label: 'Faturamento Médio',
                    data: {{ temporal_analysis.seasonality | map(attribute='avg_faturamento') | list | tojson }},
                    backgroundColor: 'rgba(168, 85, 247, 0.8)',
                    borderColor: 'rgb(168, 85, 247)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    {% endif %}

    // Gráfico de Distribuição de Tempo para Primeira Nota
    {% if time_to_invoice_analysis and time_to_invoice_analysis.get('time_distribution') %}
    const timeDistCtx = document.getElementById('timeDistributionChart');
    if (timeDistCtx) {
        new Chart(timeDistCtx, {
            type: 'doughnut',
            data: {
                labels: {{ time_to_invoice_analysis.time_distribution | map(attribute='range') | list | tojson }},
                datasets: [{
                    data: {{ time_to_invoice_analysis.time_distribution | map(attribute='count') | list | tojson }},
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(107, 114, 128, 0.8)',
                        'rgba(156, 163, 175, 0.8)',
                        'rgba(209, 213, 219, 0.8)',
                        'rgba(243, 244, 246, 0.8)'
                    ],
                    borderColor: [
                        'rgb(59, 130, 246)',
                        'rgb(107, 114, 128)',
                        'rgb(156, 163, 175)',
                        'rgb(209, 213, 219)',
                        'rgb(243, 244, 246)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: 'Distribuição por Faixas de Tempo'
                    }
                }
            }
        });
    }
    {% endif %}

    // Gráfico de Tendência Mensal de Tempo para Primeira Nota
    {% if time_to_invoice_analysis and time_to_invoice_analysis.get('monthly_trend') %}
    const timeMonthlyCtx = document.getElementById('timeMonthlyTrendChart');
    if (timeMonthlyCtx) {
        new Chart(timeMonthlyCtx, {
            type: 'line',
            data: {
                labels: {{ time_to_invoice_analysis.monthly_trend | map(attribute='mes_implementacao') | list | tojson }},
                datasets: [{
                    label: 'Tempo Médio (dias)',
                    data: {{ time_to_invoice_analysis.monthly_trend | map(attribute='media_dias') | list | tojson }},
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Dias'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Mês de Implementação'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Evolução do Tempo Médio para Primeira Nota'
                    }
                }
            }
        });
    }
    {% endif %}

    // Gráfico de Distribuição de Ticket Médio
    const ticketDistCtx = document.getElementById('ticketDistributionChart');
    if (ticketDistCtx) {
        // Dados de exemplo se não houver dados reais
        let ticketLabels = ['R$ 0 - R$ 1.000', 'R$ 1.001 - R$ 3.000', 'R$ 3.001 - R$ 5.000', 'R$ 5.001 - R$ 10.000', 'Acima de R$ 10.000'];
        let ticketData = [150, 450, 380, 220, 100];

        {% if kpis and kpis.get('ticket_distribution') %}
        const realTicketData = {{ kpis.ticket_distribution | tojson }};
        if (realTicketData && realTicketData.length > 0) {
            ticketLabels = realTicketData.map(item => item.range);
            ticketData = realTicketData.map(item => item.count);
        }
        {% endif %}

        new Chart(ticketDistCtx, {
            type: 'doughnut',
            data: {
                labels: ticketLabels,
                datasets: [{
                    data: ticketData,
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(107, 114, 128, 0.8)',
                        'rgba(156, 163, 175, 0.8)',
                        'rgba(209, 213, 219, 0.8)',
                        'rgba(243, 244, 246, 0.8)'
                    ],
                    borderColor: [
                        'rgb(59, 130, 246)',
                        'rgb(107, 114, 128)',
                        'rgb(156, 163, 175)',
                        'rgb(209, 213, 219)',
                        'rgb(243, 244, 246)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: 'Distribuição por Faixas de Ticket'
                    }
                }
            }
        });
    }

    // Gráfico de Distribuição de Faturamento
    const revenueDistCtx = document.getElementById('revenueDistributionChart');
    if (revenueDistCtx) {
        // Dados de exemplo se não houver dados reais
        let revenueLabels = ['R$ 0 - R$ 10.000', 'R$ 10.001 - R$ 50.000', 'R$ 50.001 - R$ 100.000', 'R$ 100.001 - R$ 500.000', 'Acima de R$ 500.000'];
        let revenueData = [200, 350, 280, 150, 50];

        {% if kpis and kpis.get('revenue_distribution') %}
        const realRevenueData = {{ kpis.revenue_distribution | tojson }};
        if (realRevenueData && realRevenueData.length > 0) {
            revenueLabels = realRevenueData.map(item => item.range);
            revenueData = realRevenueData.map(item => item.count);
        }
        {% endif %}

        new Chart(revenueDistCtx, {
            type: 'bar',
            data: {
                labels: revenueLabels,
                datasets: [{
                    label: 'Número de Produtores',
                    data: revenueData,
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(107, 114, 128, 0.8)',
                        'rgba(156, 163, 175, 0.8)',
                        'rgba(209, 213, 219, 0.8)',
                        'rgba(243, 244, 246, 0.8)'
                    ],
                    borderColor: [
                        'rgb(59, 130, 246)',
                        'rgb(107, 114, 128)',
                        'rgb(156, 163, 175)',
                        'rgb(209, 213, 219)',
                        'rgb(243, 244, 246)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Número de Produtores'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Faixas de Faturamento'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Distribuição de Produtores por Faturamento'
                    }
                }
            }
        });
    }

    // Gráfico de Dispersão: Ticket vs Notas
    const scatterCtx = document.getElementById('scatterTicketNotasChart');
    if (scatterCtx) {
        // Dados de exemplo para dispersão
        const scatterData = [
            {x: 500, y: 5}, {x: 800, y: 8}, {x: 1200, y: 12}, {x: 1500, y: 15},
            {x: 2000, y: 20}, {x: 2500, y: 25}, {x: 3000, y: 30}, {x: 3500, y: 35},
            {x: 4000, y: 40}, {x: 4500, y: 45}, {x: 5000, y: 50}, {x: 5500, y: 55},
            {x: 6000, y: 60}, {x: 6500, y: 65}, {x: 7000, y: 70}, {x: 7500, y: 75}
        ];

        new Chart(scatterCtx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: 'Produtores',
                    data: scatterData,
                    backgroundColor: 'rgba(59, 130, 246, 0.6)',
                    borderColor: 'rgb(59, 130, 246)',
                    borderWidth: 1,
                    pointRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Ticket Médio (R$)'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Número de Notas'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Correlação Ticket vs Notas'
                    }
                }
            }
        });
    }

    // Gráfico de Distribuição Normal
    const normalCtx = document.getElementById('normalDistributionChart');
    if (normalCtx) {
        // Gerar dados para distribuição normal
        const normalData = [];
        const normalLabels = [];
        for (let i = -3; i <= 3; i += 0.2) {
            normalLabels.push(i.toFixed(1));
            // Fórmula da distribuição normal
            const value = Math.exp(-0.5 * i * i) / Math.sqrt(2 * Math.PI);
            normalData.push(value * 100); // Escalar para visualização
        }

        new Chart(normalCtx, {
            type: 'line',
            data: {
                labels: normalLabels,
                datasets: [{
                    label: 'Distribuição Normal',
                    data: normalData,
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    fill: true,
                    tension: 0.4,
                    pointRadius: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Desvios Padrão'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Densidade'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Curva Normal de Produtividade'
                    }
                }
            }
        });
    }

    // Gráfico Month over Month
    const monthOverMonthCtx = document.getElementById('monthOverMonthChart');
    if (monthOverMonthCtx) {
        // Dados de exemplo para os últimos 12 meses
        let monthLabels = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
        let avgNotesData = [25.5, 26.2, 27.1, 26.8, 28.3, 29.1, 27.9, 28.7, 29.5, 30.2, 28.9, 26.9];
        let totalNotesData = [1250, 1320, 1410, 1380, 1520, 1650, 1580, 1690, 1750, 1820, 1780, 1776];
        let avgValueData = [850, 875, 920, 910, 950, 980, 965, 990, 1020, 1050, 1030, 1015];

        {% if monthly_trends and monthly_trends.get('monthly_data') %}
        const realMonthlyData = {{ monthly_trends.monthly_data | tojson }};
        if (realMonthlyData && realMonthlyData.length > 0) {
            monthLabels = realMonthlyData.map(item => item.month_name || item.month);
            avgNotesData = realMonthlyData.map(item => item.avg_notes_per_user || 0);
            totalNotesData = realMonthlyData.map(item => item.total_notes || 0);
            avgValueData = realMonthlyData.map(item => item.avg_note_value || 0);
        }
        {% endif %}

        new Chart(monthOverMonthCtx, {
            type: 'line',
            data: {
                labels: monthLabels,
                datasets: [
                    {
                        label: 'Média de Notas por Usuário',
                        data: avgNotesData,
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: false,
                        tension: 0.4,
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Total de Notas',
                        data: totalNotesData,
                        borderColor: 'rgb(16, 185, 129)',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        fill: false,
                        tension: 0.4,
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        yAxisID: 'y1'
                    },
                    {
                        label: 'Valor Médio das Notas (R$)',
                        data: avgValueData,
                        borderColor: 'rgb(245, 158, 11)',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        fill: false,
                        tension: 0.4,
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        yAxisID: 'y2'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Meses'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Média de Notas por Usuário',
                            color: 'rgb(59, 130, 246)'
                        },
                        grid: {
                            color: 'rgba(59, 130, 246, 0.1)'
                        },
                        ticks: {
                            color: 'rgb(59, 130, 246)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Total de Notas',
                            color: 'rgb(16, 185, 129)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            color: 'rgb(16, 185, 129)'
                        }
                    },
                    y2: {
                        type: 'linear',
                        display: false,
                        position: 'right',
                        title: {
                            display: false,
                            text: 'Valor Médio (R$)',
                            color: 'rgb(245, 158, 11)'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Evolução Month over Month - Produtividade Médica',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 0) {
                                    label += context.parsed.y.toFixed(1) + ' notas/usuário';
                                } else if (context.datasetIndex === 1) {
                                    label += context.parsed.y.toLocaleString('pt-BR') + ' notas';
                                } else if (context.datasetIndex === 2) {
                                    label += 'R$ ' + context.parsed.y.toLocaleString('pt-BR', {minimumFractionDigits: 2});
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    }

});
</script>
{% endblock %}