<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data:; font-src 'self' data:;">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perfil - DataHub Amigo One</title>
    <script src="https://cdn.tailwindcss.com" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous"></script>
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                primary: '#0087EB',
                secondary: '#98CFFF',
                tertiary: '#E0F1FF',
                success: '#34C759',
                warning: '#FF9500',
                danger: '#FF3B30'
              }
            }
          }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" integrity="sha384-iw3OoTErCYJJB9mCa8LNS2hbK9KjsvyHKj4dKt8B8lCYJJB9mCa8LNS2hbK9Kjs" crossorigin="anonymous">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen py-8">
        <div class="max-w-4xl mx-auto px-4">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-2xl text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">{{ user.username|e }}</h1>
                            <p class="text-gray-600">{{ user.role|title }} - {{ user.domain|title }}</p>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        {% if user.role == 'admin' %}
                        <a href="{{ url_for('auth.admin_dashboard') }}" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                            <i class="fas fa-user-shield mr-2"></i>Painel Admin
                        </a>
                        <a href="/api/monitoring/dashboard" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-chart-line mr-2"></i>Monitoramento
                        </a>
                        {% endif %}
                        <a href="{{ url_for('auth.change_password') }}" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                            <i class="fas fa-key mr-2"></i>Alterar Senha
                        </a>
                        <a href="{{ url_for('auth.logout') }}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                            <i class="fas fa-sign-out-alt mr-2"></i>Logout
                        </a>
                    </div>
                </div>
            </div>

            <!-- User Info -->
            <div class="grid md:grid-cols-2 gap-6">
                <!-- Basic Info -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-info-circle mr-2 text-primary"></i>Informações Básicas
                    </h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Usuário</label>
                            <p class="mt-1 text-gray-900">{{ user.username|e }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Função</label>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                {% if user.role == 'admin' %}bg-red-100 text-red-800
                                {% elif user.role == 'manager' %}bg-yellow-100 text-yellow-800
                                {% elif user.role == 'analyst' %}bg-blue-100 text-blue-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {% if user.role == 'admin' %}<i class="fas fa-crown mr-1"></i>
                                {% elif user.role == 'manager' %}<i class="fas fa-users mr-1"></i>
                                {% elif user.role == 'analyst' %}<i class="fas fa-chart-bar mr-1"></i>
                                {% else %}<i class="fas fa-eye mr-1"></i>{% endif %}
                                {{ user.role|title }}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Domínio</label>
                            <p class="mt-1 text-gray-900">{{ user.domain|title }}</p>
                        </div>
                    </div>
                </div>

                <!-- Permissions -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-shield-alt mr-2 text-primary"></i>Permissões
                    </h2>
                    <div class="space-y-2">
                        {% for permission in user.permissions %}
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check-circle text-success text-sm"></i>
                                <span class="text-sm text-gray-700">{{ permission|e }}</span>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Session Info -->
            <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-clock mr-2 text-primary"></i>Informações da Sessão
                </h2>
                <div class="grid md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Status da Sessão</label>
                        <div class="flex items-center mt-1">
                            <div class="w-3 h-3 bg-success rounded-full mr-2"></div>
                            <span class="text-sm text-gray-900">Ativa</span>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Token de Sessão</label>
                        <p class="mt-1 text-sm text-gray-600 font-mono">{{ (user.session_token[:16] if user.session_token else 'N/A')|e }}...</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Segurança</label>
                        <div class="flex items-center mt-1">
                            <i class="fas fa-lock text-success mr-2"></i>
                            <span class="text-sm text-gray-900">Protegida</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-bolt mr-2 text-primary"></i>Ações Rápidas
                </h2>
                <div class="grid md:grid-cols-4 gap-4">
                    <a href="{{ url_for('main.dashboard') }}" class="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="text-center">
                            <i class="fas fa-tachometer-alt text-2xl text-primary mb-2"></i>
                            <p class="text-sm font-medium text-gray-900">Dashboard</p>
                        </div>
                    </a>

                    {% if 'leads.view' in user.permissions or 'admin' in user.permissions %}
                    <a href="{{ url_for('lead.leads') }}" class="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="text-center">
                            <i class="fas fa-users text-2xl text-primary mb-2"></i>
                            <p class="text-sm font-medium text-gray-900">Leads</p>
                        </div>
                    </a>
                    {% endif %}

                    {% if 'opportunities.view' in user.permissions or 'admin' in user.permissions %}
                    <a href="{{ url_for('opportunity.opportunities') }}" class="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="text-center">
                            <i class="fas fa-handshake text-2xl text-primary mb-2"></i>
                            <p class="text-sm font-medium text-gray-900">Oportunidades</p>
                        </div>
                    </a>
                    {% endif %}

                    {% if 'users.manage' in user.permissions or 'admin' in user.permissions %}
                    <a href="/admin/users" class="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="text-center">
                            <i class="fas fa-user-cog text-2xl text-primary mb-2"></i>
                            <p class="text-sm font-medium text-gray-900">Usuários</p>
                        </div>
                    </a>
                    {% endif %}
                </div>
            </div>

            <!-- Back to Dashboard -->
            <div class="mt-6 text-center">
                <a href="{{ url_for('main.dashboard') }}" class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-blue-600 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Voltar ao Dashboard
                </a>
            </div>
        </div>
    </div>
</body>
</html>
