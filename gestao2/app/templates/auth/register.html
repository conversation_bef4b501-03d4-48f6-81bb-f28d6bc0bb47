<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data:; font-src 'self' data:;">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro - DataHub Amigo One</title>
    <script src="https://cdn.tailwindcss.com" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous"></script>
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                primary: '#0087EB',
                secondary: '#98CFFF',
                tertiary: '#E0F1FF',
                success: '#34C759',
                warning: '#FF9500',
                danger: '#FF3B30'
              }
            }
          }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" integrity="sha384-iw3OoTErCYJJB9mCa8LNS2hbK9KjsvyHKj4dKt8B8lCYJJB9mCa8LNS2hbK9Kjs" crossorigin="anonymous">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        .hero-gradient {
            background: linear-gradient(135deg, #0087EB 0%, #007AFF 50%, #5856D6 100%);
        }
        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="min-h-screen hero-gradient flex items-center justify-center p-4">
    <div class="w-full max-w-md mx-auto">
        <div class="glass-effect rounded-2xl shadow-2xl p-8">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-user-plus text-2xl text-white"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">Criar Conta</h3>
                <p class="text-gray-600 mt-2">Complete seu registro no DataHub</p>
            </div>

            {% if error %}
                <div class="mb-4 p-4 rounded-lg bg-red-50 border border-red-200 text-red-700">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        {{ error|e }}
                    </div>
                </div>
            {% endif %}

            {% if invitation %}
                <!-- Invitation Info -->
                <div class="mb-6 p-4 rounded-lg bg-blue-50 border border-blue-200">
                    <h4 class="font-semibold text-blue-900 mb-2">
                        <i class="fas fa-envelope mr-2"></i>Convite Recebido
                    </h4>
                    <div class="text-sm text-blue-700 space-y-1">
                        <p><strong>Email:</strong> {{ invitation.email|e }}</p>
                        <p><strong>Função:</strong> {{ invitation.role|title }}</p>
                        <p><strong>Domínio:</strong> {{ invitation.domain|title }}</p>
                    </div>
                </div>

                <!-- Registration Form -->
                <form method="POST" class="space-y-6">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <input type="hidden" name="token" value="{{ invitation.token|e }}">

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-envelope mr-2 text-primary"></i>Email
                        </label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            value="{{ invitation.email|e }}"
                            readonly
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
                        >
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock mr-2 text-primary"></i>Senha
                        </label>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            required
                            minlength="8"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white"
                            placeholder="Digite sua senha (mín. 8 caracteres)"
                        >
                    </div>

                    <div>
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock mr-2 text-primary"></i>Confirmar Senha
                        </label>
                        <input
                            type="password"
                            id="confirm_password"
                            name="confirm_password"
                            required
                            minlength="8"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white"
                            placeholder="Confirme sua senha"
                        >
                    </div>

                    <!-- Password Requirements -->
                    <div class="text-xs text-gray-500 space-y-1">
                        <p class="font-medium">Requisitos da senha:</p>
                        <ul class="list-disc list-inside space-y-1">
                            <li>Mínimo de 8 caracteres</li>
                            <li>Recomendado: letras maiúsculas e minúsculas</li>
                            <li>Recomendado: números e símbolos</li>
                        </ul>
                    </div>

                    <button
                        type="submit"
                        class="w-full bg-primary hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 focus:ring-2 focus:ring-primary focus:ring-offset-2"
                    >
                        <i class="fas fa-user-plus mr-2"></i>Criar Conta
                    </button>
                </form>

                <!-- Permissions Info -->
                <div class="mt-6 p-4 rounded-lg bg-gray-50 border border-gray-200">
                    <h4 class="font-semibold text-gray-900 mb-2">
                        <i class="fas fa-shield-alt mr-2"></i>Suas Permissões
                    </h4>
                    <div class="text-sm text-gray-700">
                        <p class="mb-2"><strong>Função:</strong> {{ invitation.role|title }}</p>
                        <div class="space-y-1">
                            {% for permission in invitation.permissions %}
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-check-circle text-success text-xs"></i>
                                    <span>{{ permission|e }}</span>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% else %}
                <!-- No invitation -->
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-4xl text-warning mb-4"></i>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Convite Necessário</h4>
                    <p class="text-gray-600 mb-4">
                        Você precisa de um convite válido para criar uma conta.
                    </p>
                    <a href="{{ url_for('auth.login') }}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>Voltar ao Login
                    </a>
                </div>
            {% endif %}

            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="text-center">
                    <p class="text-sm text-gray-500 mb-3">
                        <i class="fas fa-shield-alt mr-1 text-success"></i>
                        Registro seguro com validação de convite
                    </p>
                    <div class="flex items-center justify-center space-x-4 text-xs text-gray-400">
                        <span><i class="fas fa-lock mr-1"></i>Criptografia SSL</span>
                        <span><i class="fas fa-user-shield mr-1"></i>Dados Protegidos</span>
                        <span><i class="fas fa-envelope-open mr-1"></i>Convite Validado</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back to Login -->
        <div class="mt-6 text-center">
            <a href="{{ url_for('auth.login') }}" class="text-white hover:text-blue-200 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Voltar ao Login
            </a>
        </div>
    </div>

    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;

            if (password && confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('As senhas não coincidem');
            } else {
                this.setCustomValidity('');
            }
        });

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (password !== confirmPassword) {
                e.preventDefault();
                alert('As senhas não coincidem.');
                return;
            }

            if (password.length < 8) {
                e.preventDefault();
                alert('A senha deve ter pelo menos 8 caracteres.');
                return;
            }
        });
    </script>
</body>
</html>
