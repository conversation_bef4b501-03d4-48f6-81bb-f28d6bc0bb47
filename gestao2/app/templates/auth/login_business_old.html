<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data:; font-src 'self' data:;">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - DataHub Amigo One Negócios</title>
    <script src="https://cdn.tailwindcss.com" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous"></script>
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                primary: '#0087EB',
                secondary: '#98CFFF',
                tertiary: '#E0F1FF',
                success: '#34C759',
                warning: '#FF9500',
                danger: '#FF3B30',
                systemBlue: '#007AFF',
                systemGreen: '#34C759',
                systemGray: {
                  DEFAULT: '#8E8E93',
                  light: '#AEAEB2',
                  lighter: '#C7C7CC',
                  lightest: '#D1D1D6',
                  extralight: '#E5E5EA',
                  ultralight: '#F2F2F7'
                }
              }
            }
          }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" integrity="sha384-iw3OoTErCYJJB9mCa8LNS2hbK9KjsvyHKj4dKt8B8lCYJJB9mCa8LNS2hbK9Kjs" crossorigin="anonymous">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        .hero-gradient {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
        }
        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body class="min-h-screen hero-gradient flex items-center justify-center p-4">
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-white opacity-10 rounded-full floating-animation"></div>
        <div class="absolute bottom-1/4 right-1/4 w-48 h-48 bg-white opacity-5 rounded-full floating-animation" style="animation-delay: -3s;"></div>
        <div class="absolute top-3/4 left-1/3 w-32 h-32 bg-white opacity-10 rounded-full floating-animation" style="animation-delay: -1.5s;"></div>
    </div>

    <div class="relative z-10 w-full max-w-6xl mx-auto">
        <div class="grid lg:grid-cols-2 gap-8 items-center">
            <!-- Hero Section -->
            <div class="text-white space-y-8">
                <div class="space-y-6">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-xl flex items-center justify-center p-2">
                            <img src="{{ url_for('static', filename='img/image.png') }}" alt="Amigo One" class="w-full h-full object-contain">
                        </div>
                        <div>
                            <h1 class="text-3xl lg:text-4xl font-bold">DataHub Amigo One</h1>
                            <p class="text-blue-100 text-lg">Gestão Comercial</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h2 class="text-2xl lg:text-3xl font-semibold leading-tight">
                            Gestão comercial <span class="text-yellow-300">inteligente</span>
                        </h2>
                        <p class="text-blue-100 text-lg leading-relaxed">
                            Plataforma interna para captação e gestão de médicos na nossa plataforma.
                            Monitore leads, acompanhe oportunidades de negócio e analise a performance
                            da equipe comercial em tempo real.
                        </p>
                    </div>

                    <!-- Features -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="flex items-center space-x-3 bg-white bg-opacity-10 rounded-lg p-4">
                            <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-user-md text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="font-medium">Captação de Médicos</p>
                                <p class="text-blue-100 text-sm">Pipeline de leads</p>
                            </div>
                        </div>

                        <div class="flex items-center space-x-3 bg-white bg-opacity-10 rounded-lg p-4">
                            <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-line text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="font-medium">Performance Comercial</p>
                                <p class="text-blue-100 text-sm">Métricas da equipe</p>
                            </div>
                        </div>

                        <div class="flex items-center space-x-3 bg-white bg-opacity-10 rounded-lg p-4">
                            <div class="w-8 h-8 bg-blue-400 rounded-lg flex items-center justify-center">
                                <i class="fas fa-handshake text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="font-medium">Gestão de Oportunidades</p>
                                <p class="text-blue-100 text-sm">Conversões e fechamentos</p>
                            </div>
                        </div>

                        <div class="flex items-center space-x-3 bg-white bg-opacity-10 rounded-lg p-4">
                            <div class="w-8 h-8 bg-blue-700 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users-cog text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="font-medium">Gestão de Equipe</p>
                                <p class="text-blue-100 text-sm">Responsáveis e metas</p>
                            </div>
                        </div>
                    </div>

                    <!-- Stats -->
                    <div class="flex items-center space-x-8 pt-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-300">CRM</div>
                            <div class="text-blue-100 text-sm">Completo</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-300">24/7</div>
                            <div class="text-blue-100 text-sm">Suporte</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-300">100%</div>
                            <div class="text-blue-100 text-sm">Interno</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Form -->
            <div class="w-full max-w-md mx-auto">
                <div class="glass-effect rounded-2xl shadow-2xl p-8">
                    <div class="text-center mb-8">
                        <div class="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-4 p-2">
                            <img src="{{ url_for('static', filename='img/image.png') }}" alt="Amigo One" class="w-full h-full object-contain">
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">Bem-vindo de volta</h3>
                        <p class="text-gray-600 mt-2">Acesse a gestão comercial</p>
                    </div>

                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="mb-4 p-4 rounded-lg {% if category == 'error' %}bg-red-50 border border-red-200 text-red-700{% else %}bg-green-50 border border-green-200 text-green-700{% endif %}">
                                    <div class="flex items-center">
                                        <i class="fas {% if category == 'error' %}fa-exclamation-triangle{% else %}fa-check-circle{% endif %} mr-2"></i>
                                        {{ message|e }}
                                    </div>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form method="POST" class="space-y-6">
                {{ csrf_token() }}
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-user mr-2 text-primary"></i>Usuário
                            </label>
                            <input
                                type="text"
                                id="username"
                                name="username"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white"
                                placeholder="Digite seu usuário"
                            >
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-lock mr-2 text-primary"></i>Senha
                            </label>
                            <input
                                type="password"
                                id="password"
                                name="password"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white"
                                placeholder="Digite sua senha"
                            >
                        </div>

                        <button
                            type="submit"
                            class="w-full bg-primary hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 focus:ring-2 focus:ring-primary focus:ring-offset-2"
                        >
                            <i class="fas fa-sign-in-alt mr-2"></i>Entrar na Plataforma
                        </button>
                    </form>

                    <div class="mt-8 pt-6 border-t border-gray-200">
                        <div class="text-center">
                            <p class="text-sm text-gray-500 mb-3">
                                <i class="fas fa-shield-alt mr-1 text-success"></i>
                                Acesso seguro com autenticação DataMesh
                            </p>
                            <div class="flex items-center justify-center space-x-4 text-xs text-gray-400">
                                <span><i class="fas fa-lock mr-1"></i>Criptografia SSL</span>
                                <span><i class="fas fa-eye-slash mr-1"></i>Dados Protegidos</span>
                                <span><i class="fas fa-clock mr-1"></i>Sessão Segura</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Credentials Info -->
                <div class="mt-6 text-center">
                    <div class="bg-white bg-opacity-20 rounded-lg p-4 text-white text-sm">
                        <p class="font-medium mb-2">👤 Credenciais de Acesso:</p>
                        <p><strong>admin</strong> / admin123</p>
                        <p><strong>TTK</strong> / TTK2024!</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            });
        }, 5000);

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                e.preventDefault();
                alert('Por favor, preencha todos os campos.');
            }
        });
    </script>
</body>
</html>
