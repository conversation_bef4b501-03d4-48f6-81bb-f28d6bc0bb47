{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table, table_filters, advanced_insight %}

{% block title %}Responsáveis - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Análise de Responsáveis",
    subtitle="Acompanhe o desempenho por responsável, visualize métricas de conversão e receita.",
    stats=[
        {
            "label": "Total de Responsáveis",
            "value": total_responsibles,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />'
        },
        {
            "label": "Receita Total",
            "value": total_revenue,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />'
        }
    ],
    bg_class="bg-gray-100"
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Filter Section -->
    {% if class_filter or university_filter %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Filtros Aplicados</h3>
                <div class="mt-2 flex flex-wrap gap-2">
                    {% if class_filter %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        Turma: {{ class_filter.replace('-', ' ')|e }}
                        <a href="{{ url_for('responsible.index', university=university_filter) }}" class="ml-2 text-blue-600 hover:text-blue-800">
                            <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </a>
                    </span>
                    {% endif %}

                    {% if university_filter %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        Universidade: {{ university_filter|e }}
                        <a href="{{ url_for('responsible.index', class=class_filter) }}" class="ml-2 text-green-600 hover:text-green-800">
                            <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </a>
                    </span>
                    {% endif %}
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="{{ url_for('responsible.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    Limpar Todos os Filtros
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Insights Section -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-2">Insights de Responsáveis</h2>
        <p class="text-sm text-gray-500 mb-6">Análises e recomendações baseadas no desempenho dos responsáveis.</p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {{ advanced_insight(
                title="Dados Ilustrativos",
                content="Os insights abaixo são baseados em dados ilustrativos para demonstração. Recomendamos implementar análises baseadas em dados reais para decisões de negócio.",
                icon_type="info",
                color="blue",
                metrics=[
                    {"label": "Nota", "value": "Demonstração", "trend": "Ilustrativo", "trend_positive": false}
                ]
            ) }}

            {{ advanced_insight(
                title="Qualidade de Dados",
                content="A qualidade dos dados de responsáveis impacta diretamente na precisão das análises e na tomada de decisões.",
                icon_type="success",
                color="green",
                metrics=[
                    {"label": "Score Atual", "value": data_quality.overall_score|string, "trend": data_quality.overall_level, "trend_positive": data_quality.overall_score >= 3.5}
                ]
            ) }}

            {{ advanced_insight(
                title="Campos Críticos",
                content="Campos com baixo preenchimento podem comprometer análises importantes. Priorize o preenchimento dos campos críticos.",
                icon_type="warning",
                color="yellow",
                metrics=[
                    {"label": "Campos com Problemas", "value": data_quality.fields_with_issues|string, "trend": "Necessitam Atenção", "trend_positive": false}
                ]
            ) }}
        </div>
    </div>

    <!-- Qualidade dos Dados Section -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Qualidade dos Dados de Responsáveis</h3>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <!-- Score de Qualidade -->
            <div class="bg-white rounded-lg border border-gray-200 p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Score de Qualidade</p>
                        <h3 class="text-3xl font-bold text-gray-900 mt-1">{{ data_quality.overall_score|default('N/A') }}</h3>
                    </div>
                    <div class="h-12 w-12 rounded-full flex items-center justify-center
                        {% if data_quality.overall_score >= 4.5 %}bg-green-100 text-green-600
                        {% elif data_quality.overall_score >= 3.5 %}bg-blue-100 text-blue-600
                        {% elif data_quality.overall_score >= 2.5 %}bg-yellow-100 text-yellow-600
                        {% elif data_quality.overall_score >= 1.5 %}bg-orange-100 text-orange-600
                        {% else %}bg-red-100 text-red-600{% endif %}">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <p class="text-sm font-medium mt-2
                    {% if data_quality.overall_score >= 4.5 %}text-green-600
                    {% elif data_quality.overall_score >= 3.5 %}text-blue-600
                    {% elif data_quality.overall_score >= 2.5 %}text-yellow-600
                    {% elif data_quality.overall_score >= 1.5 %}text-orange-600
                    {% else %}text-red-600{% endif %}">
                    {{ data_quality.overall_level|default('N/A') }}
                </p>
            </div>

            <!-- Campos Analisados -->
            <div class="bg-white rounded-lg border border-gray-200 p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Campos Analisados</p>
                        <h3 class="text-3xl font-bold text-gray-900 mt-1">{{ data_quality.fields_count|default('0') }}</h3>
                    </div>
                    <div class="h-12 w-12 rounded-full flex items-center justify-center bg-blue-100 text-blue-600">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                </div>
                <p class="text-sm font-medium mt-2 text-gray-600">
                    Campos de responsáveis
                </p>
            </div>

            <!-- Campos com Problemas -->
            <div class="bg-white rounded-lg border border-gray-200 p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Campos com Problemas</p>
                        <h3 class="text-3xl font-bold text-gray-900 mt-1">{{ data_quality.fields_with_issues|default('0') }}</h3>
                    </div>
                    <div class="h-12 w-12 rounded-full flex items-center justify-center bg-yellow-100 text-yellow-600">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                </div>
                <p class="text-sm font-medium mt-2 text-gray-600">
                    Necessitam atenção
                </p>
            </div>

            <!-- Recomendações -->
            <div class="bg-white rounded-lg border border-gray-200 p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Recomendações</p>
                        <h3 class="text-3xl font-bold text-gray-900 mt-1">{{ data_quality.recommendations|length|default('0') }}</h3>
                    </div>
                    <div class="h-12 w-12 rounded-full flex items-center justify-center bg-green-100 text-green-600">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                </div>
                <p class="text-sm font-medium mt-2 text-gray-600">
                    Ações sugeridas
                </p>
            </div>
        </div>

        <!-- Completude dos Dados -->
        <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3">Completude dos Dados</h4>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campo</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completude</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nível</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for field, data in data_quality.completeness.items() %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ field|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="h-2.5 rounded-full
                                        {% if data.percentage >= 95 %}bg-green-500
                                        {% elif data.percentage >= 85 %}bg-blue-500
                                        {% elif data.percentage >= 70 %}bg-yellow-500
                                        {% elif data.percentage >= 50 %}bg-orange-500
                                        {% else %}bg-red-500{% endif %}"
                                        style="width: {{ data.percentage|e }}%"></div>
                                </div>
                                <span class="text-xs mt-1 inline-block">{{ data.percentage|e }}%</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    {% if data.level == 'Excelente' %}bg-green-100 text-green-800
                                    {% elif data.level == 'Bom' %}bg-blue-100 text-blue-800
                                    {% elif data.level == 'Razoável' %}bg-yellow-100 text-yellow-800
                                    {% elif data.level == 'Precisa de Atenção' %}bg-orange-100 text-orange-800
                                    {% elif data.level == 'Crítico' %}bg-red-100 text-red-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ data.level|e }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% if data.required %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    Obrigatório
                                </span>
                                {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                    Opcional
                                </span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Qualidade por Responsável -->
        {% if data_quality.responsible_quality %}
        <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3">Qualidade de Preenchimento por Responsável</h4>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Responsável</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Implantações</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Finalizadas</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nível</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campos Problemáticos</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for responsible, data in data_quality.responsible_quality.items() %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ responsible|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ data.implementations|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ data.finalized|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ data.score|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    {% if data.level == 'Excelente' %}bg-green-100 text-green-800
                                    {% elif data.level == 'Bom' %}bg-blue-100 text-blue-800
                                    {% elif data.level == 'Razoável' %}bg-yellow-100 text-yellow-800
                                    {% elif data.level == 'Precisa de Atenção' %}bg-orange-100 text-orange-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                                    {{ data.level|e }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                {% if data.problem_fields %}
                                <ul class="list-disc pl-5">
                                    {% for field in data.problem_fields %}
                                    <li>
                                        {{ field.field|e }}
                                        <span class="text-xs
                                            {% if field.percentage < 50 %}text-red-600
                                            {% elif field.percentage < 70 %}text-orange-600
                                            {% elif field.percentage < 90 %}text-yellow-600
                                            {% else %}text-blue-600{% endif %}">
                                            ({{ field.percentage|e }}%)
                                        </span>
                                        {% if field.required %}
                                        <span class="text-xs bg-red-100 text-red-800 px-1 rounded">Obrigatório</span>
                                        {% endif %}
                                    </li>
                                    {% endfor %}
                                </ul>
                                {% else %}
                                <span class="text-green-600">Sem problemas identificados</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Recomendações -->
        {% if data_quality.recommendations %}
        <div>
            <h4 class="text-md font-semibold text-gray-800 mb-3">Recomendações</h4>
            <ul class="space-y-2 text-sm text-gray-600">
                {% for recommendation in data_quality.recommendations %}
                <li class="flex items-start">
                    <svg class="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {{ recommendation|e }}
                </li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Chart: Responsáveis por Função -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Responsáveis por Função</h3>
            <div class="h-64">
                {{ chart(
                    id="role-chart",
                    type="pie",
                    data=role_data,
                    colors=["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6"]
                )|e }}
            </div>
        </div>

        <!-- Chart: Status de Implantação -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Status de Implantação</h3>
            <div class="h-64">
                {{ chart(
                    id="implementation-chart",
                    type="pie",
                    data=implementation_data,
                    colors=["#10B981", "#3B82F6"]
                )|e }}
            </div>
        </div>

        <!-- Chart: Top Responsáveis por Receita -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Responsáveis por Receita</h3>
            <div class="h-64">
                {{ chart(
                    id="revenue-chart",
                    type="bar",
                    data=top_revenue_data,
                    colors=["#3B82F6"]
                )|e }}
            </div>
        </div>
    </div>

    <!-- Responsibles Table Section -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-medium text-gray-900">Lista de Responsáveis</h3>
        </div>

        <!-- Table Filters -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            {{ table_filters(
                id="responsibles-table-filters",
                filters=[
                    {
                        "id": "role-filter",
                        "type": "select",
                        "placeholder": "Filtrar por função",
                        "options": [
                            {"value": "all", "label": "Todas"},
                            {"value": "vendas", "label": "Vendas"},
                            {"value": "onboarding", "label": "Onboarding"},
                            {"value": "ongoing", "label": "Ongoing"},
                            {"value": "contabil", "label": "Contábil"},
                            {"value": "societario", "label": "Societário"}
                        ]
                    }
                ]
            ) }}
        </div>

        <div class="overflow-x-auto">
            <div class="overflow-y-auto max-h-[500px]">
                <table class="min-w-full divide-y divide-gray-200" id="responsibles-table">
                    <thead class="bg-gray-100 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Nome</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Função</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Oportunidades</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Implantações</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Finalizadas</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Universidades</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Turmas</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Receita</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">MRR</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Ações</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for responsible in responsibles %}
                        <tr class="hover:bg-gray-50 transition-colors duration-150"
                            data-role="{{ responsible.role|lower|replace('/', ' ')|replace(',', ' ') }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ responsible.name|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ responsible.role|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ responsible.opportunities|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ responsible.implementations|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ responsible.finalized|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ responsible.universities|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ responsible.classes|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ responsible.revenue_formatted|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ responsible.mrr_formatted|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <a href="{{ url_for('responsible.detail', responsible_id=responsible.id) }}"
                                   class="text-primary hover:underline inline-flex items-center">
                                    <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                    Detalhes
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Table filtering
        const roleFilter = document.getElementById('role-filter');
        const table = document.getElementById('responsibles-table');
        const rows = table.querySelectorAll('tbody tr');

        function filterTable() {
            const roleValue = roleFilter.value.toLowerCase();

            rows.forEach(row => {
                const roleMatch = roleValue === 'all' || row.dataset.role.includes(roleValue);

                if (roleMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        if (roleFilter) {
            roleFilter.addEventListener('change', filterTable);
        }
    });
</script>
{% endblock %}
