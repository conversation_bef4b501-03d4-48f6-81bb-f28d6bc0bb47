{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table, table_filters %}

{% block title %}{{ university_name|e }} - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title=university_name,
    subtitle="Análise detalhada da universidade, incluindo métricas de desempenho, cursos e turmas.",
    stats=[
        {
            "label": "Implantações Finalizadas",
            "value": finalized_count,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />'
        },
        {
            "label": "Implantações Ativas",
            "value": active_count,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />'
        },
        {
            "label": "Oportunidades Ativas",
            "value": opportunity_count,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />'
        }
    ],
    bg_class="bg-gray-100"
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- University Info Card -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">{{ university_name|e }}</h2>
                <p class="text-gray-500 mt-1">{{ university_city|e }}, {{ university_state|e }}</p>
            </div>
            <div class="mt-4 md:mt-0">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    {{ university_status|e }}
                </span>
            </div>
        </div>

        <div class="mt-6 border-t border-gray-200 pt-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <h3 class="text-sm font-medium text-gray-500">Receita Mensal Recorrente</h3>
                    <p class="mt-1 text-lg font-semibold text-gray-900">{{ mrr_total|e }}</p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-gray-500">Receita Potencial</h3>
                    <p class="mt-1 text-lg font-semibold text-gray-900">{{ potential_revenue|e }}</p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-gray-500">Taxa de Conversão</h3>
                    <p class="mt-1 text-lg font-semibold text-gray-900">{{ conversion_rate|e }}%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Implementation Status Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <h3 class="text-base font-medium text-gray-900 mb-1">Status de Implantação</h3>
            <p class="text-sm text-gray-500 mb-4">Distribuição por status</p>
            <div class="h-64">
                <canvas id="statusChart"></canvas>
            </div>
        </div>

        <!-- Products Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <h3 class="text-base font-medium text-gray-900 mb-1">Produtos Contratados</h3>
            <p class="text-sm text-gray-500 mb-4">Distribuição por produto</p>
            <div class="h-64">
                <canvas id="productsChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Courses Section -->
    <div class="mb-8">
        {{ section_header(
            title="Cursos",
            subtitle="Análise por curso da universidade"
        )|e }}

        <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
            <div class="p-4 bg-gray-50 border-b border-gray-200">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Lista de Cursos</h3>
                        <p class="text-sm text-gray-500 mt-1">Detalhamento por curso com métricas de desempenho</p>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <span class="text-sm text-gray-500">Total: <span class="font-medium">{{ courses|length }}</span> cursos</span>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <div class="overflow-y-auto max-h-[500px]">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-100 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Curso</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Alunos</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Implantações</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Receita</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Ações</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for course in courses %}
                            <tr class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ course.name|e }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ course.students|e }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ course.implementations|e }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ course.revenue|e }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div class="flex space-x-3">
                                        <button type="button"
                                                onclick="toggleCourseDetails('{{ course.id|e }}')"
                                                class="text-primary hover:text-blue-700 hover:underline transition-colors duration-150 inline-flex items-center">
                                            <span id="course-btn-text-{{ course.id|e }}">Ver Turmas</span>
                                            <svg id="course-btn-icon-{{ course.id|e }}" class="w-4 h-4 ml-1 transform transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </button>

                                        {% if course.implementation_ids %}
                                        <a href="{{ url_for('implementation.index') }}?course={{ course.name|e }}&university={{ university_name|e }}"
                                           class="text-green-600 hover:text-green-800 hover:underline transition-colors duration-150 inline-flex items-center">
                                            Implantações
                                            <svg class="ml-1 w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                            </svg>
                                        </a>
                                        {% endif %}

                                        {% if course.opportunity_ids %}
                                        <a href="{{ url_for('opportunity.index') }}?course={{ course.name|e }}&university={{ university_name|e }}"
                                           class="text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-150 inline-flex items-center">
                                            Oportunidades
                                            <svg class="ml-1 w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                            </svg>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            <tr id="course-details-{{ course.id|e }}" class="hidden bg-gray-50">
                                <td colspan="5" class="px-6 py-4">
                                    <div class="mb-3">
                                        <div class="flex justify-between items-center mb-2">
                                            <h4 class="text-sm font-medium text-gray-900">Turmas de {{ course.name|e }}</h4>
                                            <button type="button"
                                                    onclick="toggleClassList('{{ course.id|e }}')"
                                                    class="text-xs text-gray-500 hover:text-gray-700 hover:underline transition-colors duration-150 inline-flex items-center"
                                                    id="{{ course.id|e }}-toggle-btn">
                                                Ocultar Turmas
                                                <svg class="ml-1 w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                                </svg>
                                            </button>
                                        </div>
                                        <div class="overflow-x-auto" id="{{ course.id|e }}-class-list">
                                            <table class="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
                                                <thead class="bg-gray-100">
                                                    <tr>
                                                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Turma</th>
                                                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Período</th>
                                                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Alunos</th>
                                                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Status</th>
                                                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Produto</th>
                                                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Ações</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="bg-white divide-y divide-gray-200">
                                                    {% for class in course.classes %}
                                                    <tr class="hover:bg-gray-50">
                                                        <td class="px-4 py-2 text-sm font-medium text-gray-900">{{ class.name|e }}</td>
                                                        <td class="px-4 py-2 text-sm text-gray-500">{{ class.period|e }}</td>
                                                        <td class="px-4 py-2 text-sm text-gray-500">{{ class.students|e }}</td>
                                                        <td class="px-4 py-2 text-sm text-gray-500">
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                                {% if class.status == 'Ativo' %}
                                                                    bg-green-100 text-green-800
                                                                {% elif class.status == 'Pendente' %}
                                                                    bg-yellow-100 text-yellow-800
                                                                {% else %}
                                                                    bg-gray-100 text-gray-800
                                                                {% endif %}
                                                            ">
                                                                {{ class.status|e }}
                                                            </span>
                                                        </td>
                                                        <td class="px-4 py-2 text-sm text-gray-500">{{ class.product|e }}</td>
                                                        <td class="px-4 py-2 text-sm text-gray-500">
                                                            <a href="{{ url_for('class.detail', class_name=class.name|lower|replace(' ', '-')) }}"
                                                               class="text-primary hover:underline inline-flex items-center">
                                                                <svg class="w-3 h-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                                </svg>
                                                                Detalhes
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Implementations Section -->
    <div class="mb-8">
        {{ section_header(
            title="Implantações",
            subtitle="Detalhes das implantações na universidade"
        )|e }}

        <!-- Tabs -->
        <div class="mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button type="button"
                            onclick="showTab('finalized')"
                            id="finalized-tab"
                            class="border-primary text-primary hover:text-primary hover:border-primary px-1 py-4 font-medium text-sm border-b-2">
                        Finalizadas ({{ finalized_implementations|length }})
                    </button>
                    <button type="button"
                            onclick="showTab('active')"
                            id="active-tab"
                            class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 font-medium text-sm border-b-2">
                        Em Andamento ({{ active_implementations|length }})
                    </button>
                    <button type="button"
                            onclick="showTab('opportunities')"
                            id="opportunities-tab"
                            class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 font-medium text-sm border-b-2">
                        Oportunidades ({{ opportunities|length }})
                    </button>
                </nav>
            </div>
        </div>

        <!-- Finalized Implementations Table -->
        <div id="finalized-content" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                <div class="overflow-x-auto">
                    <div class="overflow-y-auto max-h-[500px]">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100 sticky top-0 z-10">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Lead</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Produto</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Data de Finalização</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Valor Mensal</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Responsável</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for impl in finalized_implementations %}
                                <tr class="hover:bg-gray-50 transition-colors duration-150">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ impl.lead_name|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.product|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.finalization_date|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.monthly_value|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.responsible|e }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Implementations Table -->
        <div id="active-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                <div class="overflow-x-auto">
                    <div class="overflow-y-auto max-h-[500px]">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100 sticky top-0 z-10">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Lead</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Produto</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Fase</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Data Prevista</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Responsável</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for impl in active_implementations %}
                                <tr class="hover:bg-gray-50 transition-colors duration-150">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ impl.lead_name|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.product|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ impl.phase|e }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.expected_end_date|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl.responsible|e }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Opportunities Table -->
        <div id="opportunities-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                <div class="overflow-x-auto">
                    <div class="overflow-y-auto max-h-[500px]">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100 sticky top-0 z-10">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Lead</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Produto</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Etapa do Funil</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Valor Potencial</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Responsável</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for opp in opportunities %}
                                <tr class="hover:bg-gray-50 transition-colors duration-150">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ opp.lead_name|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ opp.product|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            {{ opp.stage|e }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ opp.value|e }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ opp.responsible|e }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded for university detail page');

        // Initialize charts
        initializeCharts();

        // Show the first tab by default
        showTab('finalized');
    });

    // Function to toggle course details
    function toggleCourseDetails(courseId) {
        const detailsRow = document.getElementById(`course-details-${courseId}`);
        const btnIcon = document.getElementById(`course-btn-icon-${courseId}`);
        const btnText = document.getElementById(`course-btn-text-${courseId}`);

        if (detailsRow.classList.contains('hidden')) {
            detailsRow.classList.remove('hidden');
            btnIcon.classList.add('rotate-180');
            btnText.textContent = 'Ocultar Turmas';
        } else {
            detailsRow.classList.add('hidden');
            btnIcon.classList.remove('rotate-180');
            btnText.textContent = 'Ver Turmas';
        }
    }

    // Function to toggle class list visibility
    function toggleClassList(courseId) {
        const classList = document.getElementById(`${courseId}-class-list`);
        const toggleBtn = document.getElementById(`${courseId}-toggle-btn`);

        if (classList.classList.contains('hidden')) {
            classList.classList.remove('hidden');
            toggleBtn.innerHTML = 'Ocultar Turmas <svg class="ml-1 w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>';
        } else {
            classList.classList.add('hidden');
            toggleBtn.innerHTML = 'Mostrar Turmas <svg class="ml-1 w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>';
        }
    }

    // Function to show tab content
    function showTab(tabName) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.add('hidden');
        });

        // Show selected tab content
        document.getElementById(`${tabName}-content`).classList.remove('hidden');

        // Update tab styles
        document.querySelectorAll('button[id$="-tab"]').forEach(tab => {
            tab.classList.remove('border-primary', 'text-primary');
            tab.classList.add('border-transparent', 'text-gray-500');
        });

        document.getElementById(`${tabName}-tab`).classList.remove('border-transparent', 'text-gray-500');
        document.getElementById(`${tabName}-tab`).classList.add('border-primary', 'text-primary');
    }

    // Function to initialize charts
    function initializeCharts() {
        try {
            // Status Chart
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            const statusData = {{ status_data|tojson }};

            const statusLabels = Object.keys(statusData);
            const statusValues = Object.values(statusData).map(v => parseInt(v));

            // Define colors based on status labels
            const statusColors = statusLabels.map(status => {
                if (status === 'Finalizado') return '#34C759';
                if (status === 'Cancelado') return '#FF3B30';
                if (status === 'Em Andamento') return '#0087EB';
                return '#6B7280'; // Default for other statuses
            });

            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: statusLabels,
                    datasets: [{
                        data: statusValues,
                        backgroundColor: statusColors,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });

            // Products Chart
            const productsCtx = document.getElementById('productsChart').getContext('2d');
            const productsData = {{ products_data|tojson }};

            const productLabels = Object.keys(productsData);
            const productValues = Object.values(productsData).map(v => parseInt(v));

            new Chart(productsCtx, {
                type: 'pie',
                data: {
                    labels: productLabels,
                    datasets: [{
                        data: productValues,
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.7)',
                            'rgba(16, 185, 129, 0.7)',
                            'rgba(245, 158, 11, 0.7)',
                            'rgba(99, 102, 241, 0.7)',
                            'rgba(236, 72, 153, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });
        } catch (error) {
            console.error('Error initializing charts:', error);
        }
    }
</script>
{% endblock %}
