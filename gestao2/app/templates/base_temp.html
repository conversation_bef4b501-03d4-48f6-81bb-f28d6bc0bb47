<!DOCTYPE html>
<html lang="pt-br">
<head>
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data:; font-src 'self' data:;">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ app_name|e }} - Visão 360°{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous"></script>
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                primary: '{{ colors.primary|e }}',
                secondary: '{{ colors.secondary|e }}',
                tertiary: '{{ colors.tertiary|e }}',
                success: '{{ colors.success|e }}',
                warning: '{{ colors.warning|e }}',
                danger: '{{ colors.danger|e }}',
                gray: {
                  DEFAULT: '#8E8E93',
                  light: '#AEAEB2',
                  lighter: '#C7C7CC',
                  lightest: '#D1D1D6',
                  extralight: '#E5E5EA',
                  ultralight: '#F2F2F7'
                },
              }
            }
          }
        }
    </script>
    {% block head %}{% endblock %}
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- Header - Fixed at top -->
    <header class="w-full bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
        <div class="w-full px-4 sm:px-6 lg:px-8 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <a href="{{ url_for('dashboard.index') }}" class="flex items-center">
                    <img src="{{ url_for('static', filename='img/image.png') }}" alt="Logo" class="h-16 mr-2">
                    <span class="text-blue-500 mx-2 font-medium">|</span>
                    <span class="text-2xl font-bold text-gray-900">Data<span class="text-primary">Hub</span></span>
                </a>
                <span class="ml-2 text-sm text-label-secondary">Visão 360°</span>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-sm text-label-secondary">Bem-vindo, Bruno Abreu</span>
            </div>
        </div>
    </header>

    <!-- Spacer to account for fixed header -->
    <div class="h-20"></div>

    <!-- Sidebar and Main Content -->
    <div class="flex min-h-screen">
        <!-- Sidebar - Colapsável em dispositivos móveis -->
        <aside class="w-56 bg-white shadow-sm hidden md:block">
            <nav class="mt-5 px-2">
                <a href="{{ url_for('dashboard.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if request.endpoint == 'dashboard.index' %}bg-primary text-white{% else %}text-gray-600 hover:bg-gray-50 hover:text-primary{% endif %}">
                    <svg class="mr-3 h-5 w-5 {% if request.endpoint == 'dashboard.index' %}text-white{% else %}text-gray-400 group-hover:text-primary{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    Dashboard
                </a>

                <a href="{{ url_for('lead.index') }}" class="mt-1 group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if request.endpoint.startswith('lead.') %}bg-primary text-white{% else %}text-gray-600 hover:bg-gray-50 hover:text-primary{% endif %}">
                    <svg class="mr-3 h-5 w-5 {% if request.endpoint.startswith('lead.') %}text-white{% else %}text-gray-400 group-hover:text-primary{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                    Leads
                </a>

                <a href="{{ url_for('opportunity.index') }}" class="mt-1 group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if request.endpoint.startswith('opportunity.') %}bg-primary text-white{% else %}text-gray-600 hover:bg-gray-50 hover:text-primary{% endif %}">
                    <svg class="mr-3 h-5 w-5 {% if request.endpoint.startswith('opportunity.') %}text-white{% else %}text-gray-400 group-hover:text-primary{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Oportunidades
                </a>

                <a href="{{ url_for('implementation.index') }}" class="mt-1 group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if request.endpoint.startswith('implementation.') %}bg-primary text-white{% else %}text-gray-600 hover:bg-gray-50 hover:text-primary{% endif %}">
                    <svg class="mr-3 h-5 w-5 {% if request.endpoint.startswith('implementation.') %}text-white{% else %}text-gray-400 group-hover:text-primary{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Implantações
                </a>

                <a href="{{ url_for('university.index') }}" class="mt-1 group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if request.endpoint.startswith('university.') %}bg-primary text-white{% else %}text-gray-600 hover:bg-gray-50 hover:text-primary{% endif %}">
                    <svg class="mr-3 h-5 w-5 {% if request.endpoint.startswith('university.') %}text-white{% else %}text-gray-400 group-hover:text-primary{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path d="M12 14l9-5-9-5-9 5 9 5z" />
                        <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998a12.078 12.078 0 01.665-6.479L12 14z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                    </svg>
                    Universidades
                </a>

                <a href="{{ url_for('conversion.index') }}" class="mt-1 group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if request.endpoint.startswith('conversion.') %}bg-primary text-white{% else %}text-gray-600 hover:bg-gray-50 hover:text-primary{% endif %}">
                    <svg class="mr-3 h-5 w-5 {% if request.endpoint.startswith('conversion.') %}text-white{% else %}text-gray-400 group-hover:text-primary{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                    Conversão
                </a>

                <a href="{{ url_for('subscription.index') }}" class="mt-1 group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if request.endpoint.startswith('subscription.') %}bg-primary text-white{% else %}text-gray-600 hover:bg-gray-50 hover:text-primary{% endif %}">
                    <svg class="mr-3 h-5 w-5 {% if request.endpoint.startswith('subscription.') %}text-white{% else %}text-gray-400 group-hover:text-primary{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Assinaturas (MRR)
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 w-full">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="m-4 p-3 rounded-md {% if category == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %}">
                            {{ message|e }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Page Content -->
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 py-3">
        <div class="w-full px-4 sm:px-6 lg:px-8">
            <p class="text-center text-xs text-label-secondary">
                &copy; 2023 {{ app_name|e }} - Visão 360° | Versão {{ app_version|e }}
            </p>
        </div>
    </footer>

    <!-- Common Scripts -->
    <script src="{{ url_for('static', filename='js/shared.js') }}"></script>

    <!-- Page-specific Scripts -->
    {% block scripts %}{% endblock %}
</body>
</html>
