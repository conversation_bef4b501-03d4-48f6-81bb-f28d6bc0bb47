<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data:; font-src 'self' data:;">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Painel Administrativo - DataHub Amigo One</title>
    <script src="https://cdn.tailwindcss.com" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous"></script>
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                primary: '#0087EB',
                secondary: '#98CFFF',
                tertiary: '#E0F1FF',
                success: '#34C759',
                warning: '#FF9500',
                danger: '#FF3B30'
              }
            }
          }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" integrity="sha384-iw3OoTErCYJJB9mCa8LNS2hbK9KjsvyHKj4dKt8B8lCYJJB9mCa8LNS2hbK9Kjs" crossorigin="anonymous">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-shield text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900">Painel Administrativo</h1>
                            <p class="text-sm text-gray-600">DataHub Amigo One - Negócios</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{{ url_for('main.dashboard') }}" class="text-gray-600 hover:text-primary transition-colors">
                            <i class="fas fa-tachometer-alt mr-2"></i>Dashboard Principal
                        </a>
                        <a href="/api/monitoring/dashboard" class="text-gray-600 hover:text-primary transition-colors">
                            <i class="fas fa-chart-line mr-2"></i>Monitoramento
                        </a>
                        <a href="{{ url_for('auth.logout') }}" class="text-gray-600 hover:text-danger transition-colors">
                            <i class="fas fa-sign-out-alt mr-2"></i>Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-primary text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total de Usuários</p>
                            <p class="text-2xl font-bold text-gray-900" id="total-users">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-check text-success text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Usuários Ativos</p>
                            <p class="text-2xl font-bold text-gray-900" id="active-users">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-envelope text-warning text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Convites Pendentes</p>
                            <p class="text-2xl font-bold text-gray-900" id="pending-invitations">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-shield-alt text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Administradores</p>
                            <p class="text-2xl font-bold text-gray-900" id="admin-users">-</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- User Management -->
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-semibold text-gray-900">
                                <i class="fas fa-users mr-2 text-primary"></i>Gerenciar Usuários
                            </h2>
                            <button onclick="showInviteModal()" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                <i class="fas fa-user-plus mr-2"></i>Convidar Usuário
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4" id="users-list">
                            <!-- Users will be loaded here -->
                            <div class="text-center py-8 text-gray-500">
                                <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                                <p>Carregando usuários...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invitation Management -->
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-envelope mr-2 text-primary"></i>Convites Enviados
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4" id="invitations-list">
                            <!-- Invitations will be loaded here -->
                            <div class="text-center py-8 text-gray-500">
                                <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                                <p>Carregando convites...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Info -->
            <div class="mt-8 bg-white rounded-lg shadow-sm p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-info-circle mr-2 text-primary"></i>Informações do Sistema
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">Fonte de Dados</h3>
                        <p class="text-sm text-gray-600" id="data-source">Carregando...</p>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">Total de Registros</h3>
                        <p class="text-sm text-gray-600" id="total-records">Carregando...</p>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">Última Atualização</h3>
                        <p class="text-sm text-gray-600" id="last-update">Carregando...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invite User Modal -->
    <div id="invite-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Convidar Usuário</h3>
                        <button onclick="hideInviteModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form id="invite-form" class="space-y-4">
                        <div>
                            <label for="invite-email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" id="invite-email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="invite-role" class="block text-sm font-medium text-gray-700 mb-2">Função</label>
                            <select id="invite-role" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="viewer">Visualizador</option>
                                <option value="analyst">Analista</option>
                                <option value="manager">Gerente</option>
                                <option value="admin">Administrador</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="invite-expires" class="block text-sm font-medium text-gray-700 mb-2">Expira em (dias)</label>
                            <input type="number" id="invite-expires" value="7" min="1" max="30" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        
                        <div class="flex space-x-3 pt-4">
                            <button type="submit" class="flex-1 bg-primary text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors">
                                <i class="fas fa-paper-plane mr-2"></i>Enviar Convite
                            </button>
                            <button type="button" onclick="hideInviteModal()" class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors">
                                Cancelar
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadUsers();
            loadInvitations();
            loadSystemInfo();
        });

        // Modal functions
        function showInviteModal() {
            document.getElementById('invite-modal').classList.remove('hidden');
        }

        function hideInviteModal() {
            document.getElementById('invite-modal').classList.add('hidden');
            document.getElementById('invite-form').reset();
        }

        // Load functions will be implemented with actual API calls
        function loadStats() {
            // Placeholder - implement with actual API calls
            document.getElementById('total-users').textContent = '5';
            document.getElementById('active-users').textContent = '4';
            document.getElementById('pending-invitations').textContent = '2';
            document.getElementById('admin-users').textContent = '2';
        }

        function loadUsers() {
            // Placeholder - implement with actual API calls
            const usersList = document.getElementById('users-list');
            usersList.innerHTML = `
                <div class="text-center py-4 text-gray-500">
                    <p>Funcionalidade em desenvolvimento</p>
                    <p class="text-sm">Use as APIs diretamente por enquanto</p>
                </div>
            `;
        }

        function loadInvitations() {
            // Placeholder - implement with actual API calls
            const invitationsList = document.getElementById('invitations-list');
            invitationsList.innerHTML = `
                <div class="text-center py-4 text-gray-500">
                    <p>Funcionalidade em desenvolvimento</p>
                    <p class="text-sm">Use as APIs diretamente por enquanto</p>
                </div>
            `;
        }

        function loadSystemInfo() {
            // Placeholder - implement with actual API calls
            document.getElementById('data-source').textContent = 'Excel/CSV Files';
            document.getElementById('total-records').textContent = 'Carregando...';
            document.getElementById('last-update').textContent = 'Agora';
        }

        // Handle invite form submission
        document.getElementById('invite-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('invite-email').value;
            const role = document.getElementById('invite-role').value;
            const expires = document.getElementById('invite-expires').value;
            
            // Placeholder - implement with actual API call
            alert(`Convite seria enviado para ${email} com função ${role} (expira em ${expires} dias)`);
            hideInviteModal();
        });
    </script>
</body>
</html>
