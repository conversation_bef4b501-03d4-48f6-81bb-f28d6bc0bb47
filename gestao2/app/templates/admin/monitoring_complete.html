{% extends "base.html" %}

{% block title %}Dashboard de Monitoramento - DataHub Amigo One Negócios{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Dashboard de Monitoramento</h1>
                <p class="text-gray-600 mt-1">Monitoramento em tempo real do sistema - Domínio Negócios</p>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-green-600">Sistema Online</span>
                <button onclick="refreshMonitoring()" class="ml-4 bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors text-sm">
                    Atualizar
                </button>
            </div>
        </div>
    </div>

    <!-- System Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Uptime</p>
                    <p class="text-2xl font-semibold text-gray-900" id="uptime">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Usuários Ativos</p>
                    <p class="text-2xl font-semibold text-gray-900" id="active-users">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Tempo de Resposta</p>
                    <p class="text-2xl font-semibold text-gray-900" id="response-time">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Taxa de Erro</p>
                    <p class="text-2xl font-semibold text-gray-900" id="error-rate">-</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Page Views -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Visualizações de Página</h3>
                <div class="flex space-x-2 text-sm">
                    <span class="text-gray-500">Últimas 24h:</span>
                    <span class="font-medium text-blue-600" id="page-views-24h">-</span>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Última hora</span>
                    <span class="font-medium" id="page-views-1h">-</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Logins hoje</span>
                    <span class="font-medium" id="logins-24h">-</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Exportações de dados</span>
                    <span class="font-medium" id="exports-24h">-</span>
                </div>
            </div>
        </div>

        <!-- Top Pages -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Páginas Mais Acessadas</h3>
            <div id="top-pages" class="space-y-3">
                <!-- Top pages will be loaded here -->
                <div class="text-center text-gray-500 py-4">
                    <svg class="w-6 h-6 mx-auto mb-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Carregando...
                </div>
            </div>
        </div>
    </div>

    <!-- User Activity Chart -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Atividade de Usuários por Hora</h3>
            <div class="flex space-x-2">
                <button onclick="changeTimeRange('24h')" class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg">24h</button>
                <button onclick="changeTimeRange('7d')" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg">7d</button>
            </div>
        </div>
        <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <canvas id="activity-chart" width="800" height="200"></canvas>
        </div>
    </div>

    <!-- Recent Activity Log -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Log de Atividades Recentes</h3>
                <div class="flex items-center space-x-2">
                    <select id="activity-filter" class="text-sm border border-gray-300 rounded-lg px-2 py-1">
                        <option value="">Todas as ações</option>
                        <option value="login">Logins</option>
                        <option value="page_view">Visualizações</option>
                        <option value="data_export">Exportações</option>
                    </select>
                    <button onclick="loadActivityLogs()" class="text-sm text-blue-600 hover:text-blue-800">Atualizar</button>
                </div>
            </div>
        </div>
        
        <div class="max-h-96 overflow-y-auto">
            <div id="activity-logs" class="divide-y divide-gray-200">
                <!-- Activity logs will be loaded here -->
                <div class="p-6 text-center text-gray-500">
                    <svg class="w-6 h-6 mx-auto mb-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Carregando logs de atividade...
                </div>
            </div>
        </div>
    </div>

    <!-- System Resources -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-md font-semibold text-gray-900">CPU</h4>
                <span class="text-sm font-medium text-gray-600" id="cpu-usage">-</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div id="cpu-bar" class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-md font-semibold text-gray-900">Memória</h4>
                <span class="text-sm font-medium text-gray-600" id="memory-usage">-</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div id="memory-bar" class="bg-green-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-md font-semibold text-gray-900">Disco</h4>
                <span class="text-sm font-medium text-gray-600" id="disk-usage">-</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div id="disk-bar" class="bg-yellow-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
        </div>
    </div>
</div>

<script>
let monitoringData = {};
let activityChart = null;

// Initialize monitoring dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadMonitoringData();
    loadActivityLogs();
    
    // Auto-refresh every 30 seconds
    setInterval(loadMonitoringData, 30000);
    
    // Setup activity filter
    document.getElementById('activity-filter').addEventListener('change', loadActivityLogs);
});

async function loadMonitoringData() {
    try {
        const response = await fetch('/api/admin/monitoring/dashboard');
        if (response.ok) {
            monitoringData = await response.json();
            updateDashboard(monitoringData);
        }
    } catch (error) {
        console.error('Error loading monitoring data:', error);
    }
}

function updateDashboard(data) {
    const metrics = data.metrics || {};
    
    // Update system status
    document.getElementById('uptime').textContent = metrics.uptime || '-';
    document.getElementById('active-users').textContent = metrics.active_users || 0;
    document.getElementById('response-time').textContent = metrics.response_time || '-';
    document.getElementById('error-rate').textContent = metrics.error_rate || '-';
    
    // Update activity metrics
    document.getElementById('page-views-24h').textContent = metrics.page_views_24h || 0;
    document.getElementById('page-views-1h').textContent = metrics.page_views_1h || 0;
    document.getElementById('logins-24h').textContent = metrics.logins_24h || 0;
    document.getElementById('exports-24h').textContent = metrics.exports_24h || 0;
    
    // Update system resources
    updateResourceBar('cpu', metrics.cpu_usage);
    updateResourceBar('memory', metrics.memory_usage);
    updateResourceBar('disk', metrics.disk_usage);
    
    // Update top pages
    updateTopPages(metrics.top_pages || {});
    
    // Update activity chart
    updateActivityChart(metrics.hourly_activity || {});
}

function updateResourceBar(resource, usage) {
    const percentage = parseInt(usage) || 0;
    document.getElementById(`${resource}-usage`).textContent = usage || '-';
    document.getElementById(`${resource}-bar`).style.width = `${percentage}%`;
    
    // Change color based on usage
    const bar = document.getElementById(`${resource}-bar`);
    if (percentage > 80) {
        bar.className = bar.className.replace(/bg-\w+-500/, 'bg-red-500');
    } else if (percentage > 60) {
        bar.className = bar.className.replace(/bg-\w+-500/, 'bg-yellow-500');
    } else {
        bar.className = bar.className.replace(/bg-\w+-500/, 'bg-green-500');
    }
}

function updateTopPages(topPages) {
    const container = document.getElementById('top-pages');
    
    if (Object.keys(topPages).length === 0) {
        container.innerHTML = '<div class="text-center text-gray-500 py-4">Nenhum dado disponível</div>';
        return;
    }
    
    container.innerHTML = Object.entries(topPages)
        .slice(0, 5)
        .map(([page, views]) => `
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-700 truncate">${page}</span>
                <span class="text-sm font-medium text-gray-900">${views}</span>
            </div>
        `).join('');
}

function updateActivityChart(hourlyData) {
    const canvas = document.getElementById('activity-chart');
    const ctx = canvas.getContext('2d');
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    if (Object.keys(hourlyData).length === 0) {
        ctx.fillStyle = '#9CA3AF';
        ctx.font = '14px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText('Nenhum dado disponível', canvas.width / 2, canvas.height / 2);
        return;
    }
    
    // Simple bar chart
    const hours = Array.from({length: 24}, (_, i) => i);
    const maxValue = Math.max(...Object.values(hourlyData), 1);
    const barWidth = canvas.width / 24;
    const maxBarHeight = canvas.height - 40;
    
    hours.forEach(hour => {
        const value = hourlyData[hour] || 0;
        const barHeight = (value / maxValue) * maxBarHeight;
        const x = hour * barWidth;
        const y = canvas.height - barHeight - 20;
        
        // Draw bar
        ctx.fillStyle = '#3B82F6';
        ctx.fillRect(x + 2, y, barWidth - 4, barHeight);
        
        // Draw hour label
        ctx.fillStyle = '#6B7280';
        ctx.font = '10px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(hour.toString().padStart(2, '0'), x + barWidth / 2, canvas.height - 5);
    });
}

async function loadActivityLogs() {
    try {
        const filter = document.getElementById('activity-filter').value;
        const url = filter ? `/api/admin/activity?action=${filter}` : '/api/admin/activity';
        
        const response = await fetch(url);
        if (response.ok) {
            const data = await response.json();
            updateActivityLogs(data.logs || []);
        }
    } catch (error) {
        console.error('Error loading activity logs:', error);
        document.getElementById('activity-logs').innerHTML = `
            <div class="p-6 text-center text-red-500">
                Erro ao carregar logs de atividade
            </div>
        `;
    }
}

function updateActivityLogs(logs) {
    const container = document.getElementById('activity-logs');
    
    if (logs.length === 0) {
        container.innerHTML = '<div class="p-6 text-center text-gray-500">Nenhuma atividade recente</div>';
        return;
    }
    
    container.innerHTML = logs.slice(0, 20).map(log => `
        <div class="p-4 hover:bg-gray-50">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">${log.description || log.action}</p>
                        <p class="text-xs text-gray-500">
                            ${log.metadata?.user_id || 'Sistema'} • 
                            ${new Date(log.timestamp).toLocaleString('pt-BR')}
                        </p>
                    </div>
                </div>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    ${log.action}
                </span>
            </div>
        </div>
    `).join('');
}

function refreshMonitoring() {
    loadMonitoringData();
    loadActivityLogs();
}

function changeTimeRange(range) {
    // Update button states
    document.querySelectorAll('button[onclick^="changeTimeRange"]').forEach(btn => {
        btn.className = btn.className.replace('bg-blue-100 text-blue-700', 'bg-gray-100 text-gray-700');
    });
    event.target.className = event.target.className.replace('bg-gray-100 text-gray-700', 'bg-blue-100 text-blue-700');
    
    // TODO: Implement time range filtering
    console.log('Time range changed to:', range);
}

// Log current page view
if (typeof fetch !== 'undefined') {
    fetch('/api/admin/monitoring/log', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            user_id: '{{ session.get("username", "unknown")|e }}',
            action: 'page_view',
            page: 'monitoring_dashboard'
        })
    }).catch(console.error);
}
</script>
{% endblock %}
