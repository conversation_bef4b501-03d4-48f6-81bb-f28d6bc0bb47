{% extends "base.html" %}

{% block title %}Gerenciamento de Usuários - DataHub Amigo One Negócios{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Gerenciamento de Usuários</h1>
                <p class="text-gray-600 mt-1">Administre usuários e permissões do domínio Negócios</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="showInviteModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                    Convidar Usuário
                </button>
                <button onclick="refreshData()" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Atualizar
                </button>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Usuários</p>
                    <p class="text-2xl font-semibold text-gray-900" id="total-users">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Usuários Ativos</p>
                    <p class="text-2xl font-semibold text-gray-900" id="active-users">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Administradores</p>
                    <p class="text-2xl font-semibold text-gray-900" id="admin-users">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Logins Recentes</p>
                    <p class="text-2xl font-semibold text-gray-900" id="recent-logins">-</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-900">Lista de Usuários</h2>
                <div class="flex items-center space-x-3">
                    <div class="relative">
                        <input type="text" id="search-users" placeholder="Buscar usuários..."
                               class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <svg class="w-5 h-5 text-gray-400 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <select id="filter-role" class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">Todos os papéis</option>
                        <option value="admin">Administrador</option>
                        <option value="user">Usuário</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usuário</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Papel</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Último Login</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                </thead>
                <tbody id="users-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- Users will be loaded here -->
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                            <svg class="w-8 h-8 mx-auto mb-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Carregando usuários...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Invite User Modal -->
<div id="invite-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg max-w-md w-full mx-4">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Convidar Novo Usuário</h3>
                <button onclick="hideInviteModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <form id="invite-form" class="p-6">
            <div class="space-y-4">
                <div>
                    <label for="invite-email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input type="email" id="invite-email" required
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="<EMAIL>">
                </div>

                <div>
                    <label for="invite-role" class="block text-sm font-medium text-gray-700 mb-1">Papel</label>
                    <select id="invite-role" required
                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">Selecione um papel</option>
                        <option value="user">Usuário</option>
                        <option value="admin">Administrador</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Permissões Iniciais</label>
                    <div id="permissions-list" class="space-y-2 max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-3">
                        <!-- Permissions will be loaded here -->
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="hideInviteModal()"
                        class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                    Cancelar
                </button>
                <button type="submit"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Enviar Convite
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Permissions Modal -->
<div id="permissions-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Gerenciar Permissões</h3>
                <button onclick="hidePermissionsModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <div class="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
            <div class="mb-4">
                <p class="text-sm text-gray-600">Usuário: <span id="permissions-username" class="font-medium"></span></p>
            </div>

            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <h4 class="text-md font-medium text-gray-900">Permissões de Páginas</h4>
                    <div class="flex space-x-2">
                        <button onclick="selectAllPermissions()" class="text-sm text-blue-600 hover:text-blue-800">Selecionar Todas</button>
                        <button onclick="clearAllPermissions()" class="text-sm text-gray-600 hover:text-gray-800">Limpar Todas</button>
                    </div>
                </div>

                <div id="user-permissions-list" class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <!-- Permissions checkboxes will be loaded here -->
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                <button onclick="hidePermissionsModal()"
                        class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                    Cancelar
                </button>
                <button onclick="saveUserPermissions()"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Salvar Permissões
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentUsers = [];
let availablePermissions = {};
let currentEditingUser = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadAdminStats();
    loadUsers();
    loadPermissions();

    // Setup search and filter
    document.getElementById('search-users').addEventListener('input', filterUsers);
    document.getElementById('filter-role').addEventListener('change', filterUsers);
});

async function loadAdminStats() {
    try {
        const response = await fetch('/api/admin/stats');
        if (response.ok) {
            const stats = await response.json();
            document.getElementById('total-users').textContent = stats.total_users || 0;
            document.getElementById('active-users').textContent = stats.active_users || 0;
            document.getElementById('admin-users').textContent = stats.admin_users || 0;
            document.getElementById('recent-logins').textContent = stats.recent_logins || 0;
        }
    } catch (error) {
        console.error('Error loading admin stats:', error);
    }
}

async function loadUsers() {
    try {
        const response = await fetch('/api/admin/users');
        if (response.ok) {
            const data = await response.json();
            currentUsers = data.users || [];
            renderUsers(currentUsers);
        }
    } catch (error) {
        console.error('Error loading users:', error);
        document.getElementById('users-table-body').innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-12 text-center text-red-500">
                    Erro ao carregar usuários
                </td>
            </tr>
        `;
    }
}

function renderUsers(users) {
    const tbody = document.getElementById('users-table-body');

    if (users.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                    Nenhum usuário encontrado
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = users.map(user => `
        <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        ${user.username.charAt(0).toUpperCase()}
                    </div>
                    <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">${user.username}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.email}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${user.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'}">
                        ${user.role === 'admin' ? 'Administrador' : 'Usuário'}
                    </span>
                    ${user.username === 'bruno@abreu' ? '<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">Nativo</span>' : ''}
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                    ${user.is_active ? 'Ativo' : 'Inativo'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${user.last_login ? new Date(user.last_login).toLocaleDateString('pt-BR') : 'Nunca'}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                    <button onclick="editUserPermissions('${user.username}')"
                            class="text-blue-600 hover:text-blue-900">Permissões</button>
                    ${user.username !== 'bruno@abreu' ?
                        (user.role === 'admin' ?
                            `<button onclick="removeAdmin('${user.username}')"
                                     class="text-orange-600 hover:text-orange-900">Remover Admin</button>` :
                            `<button onclick="makeAdmin('${user.username}')"
                                     class="text-purple-600 hover:text-purple-900">Tornar Admin</button>`
                        ) : ''
                    }
                    ${user.is_active ?
                        `<button onclick="deactivateUser('${user.username}')"
                                 class="text-red-600 hover:text-red-900">Desativar</button>` :
                        `<button onclick="activateUser('${user.username}')"
                                 class="text-green-600 hover:text-green-900">Ativar</button>`
                    }
                </div>
            </td>
        </tr>
    `).join('');
}

function filterUsers() {
    const searchTerm = document.getElementById('search-users').value.toLowerCase();
    const roleFilter = document.getElementById('filter-role').value;

    const filteredUsers = currentUsers.filter(user => {
        const matchesSearch = user.username.toLowerCase().includes(searchTerm) ||
                            user.email.toLowerCase().includes(searchTerm);
        const matchesRole = !roleFilter || user.role === roleFilter;

        return matchesSearch && matchesRole;
    });

    renderUsers(filteredUsers);
}

async function loadPermissions() {
    try {
        const response = await fetch('/api/admin/permissions');
        if (response.ok) {
            const data = await response.json();
            availablePermissions = data.permissions || {};
        }
    } catch (error) {
        console.error('Error loading permissions:', error);
    }
}

function showInviteModal() {
    document.getElementById('invite-modal').classList.remove('hidden');
    loadInvitePermissions();
}

function hideInviteModal() {
    document.getElementById('invite-modal').classList.add('hidden');
    document.getElementById('invite-form').reset();
}

function loadInvitePermissions() {
    const permissionsList = document.getElementById('permissions-list');

    // Define view permissions for pages
    const viewPermissions = {
        'Dashboard': 'dashboard.view',
        'Leads': 'leads.view',
        'Oportunidades': 'opportunities.view',
        'Implementações': 'implementations.view',
        'Universidades': 'universities.view',
        'Responsáveis': 'responsibles.view',
        'Turmas': 'classes.view',
        'Cupons': 'coupons.view',
        'Conversões': 'conversion.view',
        'Assinaturas': 'subscriptions.view',
        'Clustering': 'clustering.view',
        'Inteligência Comercial': 'commercial_intelligence.view',
        'Qualidade dos Dados': 'data_quality.view',
        'Regras de Negócio': 'business_rules.view'
    };

    permissionsList.innerHTML = Object.entries(viewPermissions).map(([pageName, permission]) => `
        <label class="flex items-center">
            <input type="checkbox" value="${permission}"
                   ${permission === 'dashboard.view' ? 'checked disabled' : ''}
                   class="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
            <span class="text-sm text-gray-700">${pageName}</span>
            ${permission === 'dashboard.view' ? '<span class="text-xs text-gray-500 ml-2">(Obrigatório)</span>' : ''}
        </label>
    `).join('');
}

document.getElementById('invite-form').addEventListener('submit', async function(e) {
    e.preventDefault();

    const email = document.getElementById('invite-email').value;
    const role = document.getElementById('invite-role').value;
    const permissions = Array.from(document.querySelectorAll('#permissions-list input:checked')).map(cb => cb.value);

    try {
        const response = await fetch('/api/admin/invite', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: email,
                role: role,
                permissions: permissions
            })
        });

        if (response.ok) {
            const result = await response.json();
            alert(`Usuário convidado com sucesso!\nUsuário: ${result.username}\nSenha temporária: ${result.temporary_password}`);
            hideInviteModal();
            loadUsers();
            loadAdminStats();
        } else {
            const error = await response.json();
            alert('Erro ao convidar usuário: ' + error.error);
        }
    } catch (error) {
        console.error('Error inviting user:', error);
        alert('Erro ao convidar usuário');
    }
});

function editUserPermissions(username) {
    const user = currentUsers.find(u => u.username === username);
    if (!user) return;

    currentEditingUser = username;
    document.getElementById('permissions-username').textContent = username;

    // Define view permissions for pages
    const viewPermissions = {
        'Dashboard': 'dashboard.view',
        'Leads': 'leads.view',
        'Oportunidades': 'opportunities.view',
        'Implementações': 'implementations.view',
        'Universidades': 'universities.view',
        'Responsáveis': 'responsibles.view',
        'Turmas': 'classes.view',
        'Cupons': 'coupons.view',
        'Conversões': 'conversion.view',
        'Assinaturas': 'subscriptions.view',
        'Clustering': 'clustering.view',
        'Inteligência Comercial': 'commercial_intelligence.view',
        'Qualidade dos Dados': 'data_quality.view',
        'Regras de Negócio': 'business_rules.view'
    };

    const permissionsList = document.getElementById('user-permissions-list');
    permissionsList.innerHTML = Object.entries(viewPermissions).map(([pageName, permission]) => `
        <label class="flex items-center p-2 border border-gray-200 rounded-lg hover:bg-gray-50">
            <input type="checkbox" value="${permission}"
                   ${user.permissions.includes(permission) || permission === 'dashboard.view' ? 'checked' : ''}
                   ${permission === 'dashboard.view' ? 'disabled' : ''}
                   class="mr-3 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
            <span class="text-sm text-gray-700">${pageName}</span>
            ${permission === 'dashboard.view' ? '<span class="text-xs text-gray-500 ml-2">(Obrigatório)</span>' : ''}
        </label>
    `).join('');

    document.getElementById('permissions-modal').classList.remove('hidden');
}

function hidePermissionsModal() {
    document.getElementById('permissions-modal').classList.add('hidden');
    currentEditingUser = null;
}

function selectAllPermissions() {
    document.querySelectorAll('#user-permissions-list input[type="checkbox"]').forEach(cb => {
        cb.checked = true;
    });
}

function clearAllPermissions() {
    document.querySelectorAll('#user-permissions-list input[type="checkbox"]').forEach(cb => {
        cb.checked = false;
    });
}

async function saveUserPermissions() {
    if (!currentEditingUser) return;

    let permissions = Array.from(document.querySelectorAll('#user-permissions-list input:checked')).map(cb => cb.value);

    // Always ensure dashboard.view is included
    if (!permissions.includes('dashboard.view')) {
        permissions.push('dashboard.view');
    }

    try {
        const response = await fetch(`/api/admin/users/${currentEditingUser}/permissions`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                permissions: permissions
            })
        });

        if (response.ok) {
            alert('Permissões atualizadas com sucesso!');
            hidePermissionsModal();
            loadUsers();
        } else {
            const error = await response.json();
            alert('Erro ao atualizar permissões: ' + error.error);
        }
    } catch (error) {
        console.error('Error updating permissions:', error);
        alert('Erro ao atualizar permissões');
    }
}

async function makeAdmin(username) {
    if (!confirm(`Tem certeza que deseja tornar ${username} um administrador?`)) {
        return;
    }

    try {
        const response = await fetch(`/api/admin/users/${username}/role`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                role: 'admin'
            })
        });

        if (response.ok) {
            alert(`${username} agora é administrador!`);
            loadUsers();
            loadAdminStats();
        } else {
            const error = await response.json();
            alert('Erro ao promover usuário: ' + error.error);
        }
    } catch (error) {
        console.error('Error making admin:', error);
        alert('Erro ao promover usuário');
    }
}

async function removeAdmin(username) {
    if (!confirm(`Tem certeza que deseja remover privilégios de administrador de ${username}?`)) {
        return;
    }

    try {
        const response = await fetch(`/api/admin/users/${username}/role`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                role: 'user'
            })
        });

        if (response.ok) {
            alert(`Privilégios de administrador removidos de ${username}!`);
            loadUsers();
            loadAdminStats();
        } else {
            const error = await response.json();
            alert('Erro ao remover privilégios: ' + error.error);
        }
    } catch (error) {
        console.error('Error removing admin:', error);
        alert('Erro ao remover privilégios');
    }
}

async function activateUser(username) {
    if (!confirm(`Tem certeza que deseja ativar o usuário ${username}?`)) {
        return;
    }

    try {
        const response = await fetch(`/api/admin/users/${username}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                is_active: true
            })
        });

        if (response.ok) {
            alert(`Usuário ${username} ativado!`);
            loadUsers();
            loadAdminStats();
        } else {
            const error = await response.json();
            alert('Erro ao ativar usuário: ' + error.error);
        }
    } catch (error) {
        console.error('Error activating user:', error);
        alert('Erro ao ativar usuário');
    }
}

async function deactivateUser(username) {
    if (username === 'bruno@abreu') {
        alert('Não é possível desativar o usuário admin nativo.');
        return;
    }

    if (!confirm(`Tem certeza que deseja desativar o usuário ${username}?`)) {
        return;
    }

    try {
        const response = await fetch(`/api/admin/users/${username}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                is_active: false
            })
        });

        if (response.ok) {
            alert(`Usuário ${username} desativado!`);
            loadUsers();
            loadAdminStats();
        } else {
            const error = await response.json();
            alert('Erro ao desativar usuário: ' + error.error);
        }
    } catch (error) {
        console.error('Error deactivating user:', error);
        alert('Erro ao desativar usuário');
    }
}



function refreshData() {
    loadAdminStats();
    loadUsers();
    loadPermissions();
}
</script>
{% endblock %}
