{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table %}

{% block title %}Inteligência Comercial - Captação de Turmas - {{ app_name|e }}{% endblock %}

{% block content %}

<!-- Status de Execução Automática -->
<div class="w-full px-4 sm:px-6 lg:px-8 py-4">
    {% if analysis_complete and analysis_success_rate >= 75 %}
    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">
                    ✅ Análises Executadas com Sucesso ({{ "%.0f"|format(analysis_success_rate) }}%)
                </h3>
                <div class="mt-2 text-sm text-green-700">
                    <p>{{ successful_analyses }}/{{ total_analyses }} análises concluídas: Captação Intelligence, Índice Comercial, Segmentação ML, Análise de Churn, Previsões e Relatórios.</p>
                </div>
                <div class="mt-3">
                    <div class="flex space-x-4 text-xs text-green-600">
                        {% if captacao_intelligence %}<span>🎯 Captação Intelligence</span>{% endif %}
                        {% if commercial_index %}<span>📊 Índice Comercial</span>{% endif %}
                        {% if segmentation %}<span>🔄 Segmentação ML</span>{% endif %}
                        {% if churn_analysis %}<span>⚠️ Análise de Risco</span>{% endif %}
                        {% if revenue_forecast %}<span>📈 Previsões</span>{% endif %}
                        {% if comprehensive_report %}<span>📋 Relatório Completo</span>{% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% elif analysis_complete and analysis_success_rate >= 50 %}
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">
                    ⚠️ Análises Parcialmente Concluídas ({{ "%.0f"|format(analysis_success_rate) }}%)
                </h3>
                <div class="mt-2 text-sm text-yellow-700">
                    <p>{{ successful_analyses }}/{{ total_analyses }} análises concluídas. Algumas análises podem ter falhado devido a dados insuficientes.</p>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                    ❌ Falha nas Análises ({{ "%.0f"|format(analysis_success_rate or 0) }}%)
                </h3>
                <div class="mt-2 text-sm text-red-700">
                    <p>{{ successful_analyses or 0 }}/{{ total_analyses or 8 }} análises concluídas. Verifique os dados de entrada e dependências do sistema.</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Enhanced Hero Section for Captacao Intelligence -->
{% if captacao_intelligence and 'error' not in captacao_intelligence %}
{{ hero_section(
    title="🎯 Inteligência de Captação",
    subtitle="Análise avançada com Machine Learning para otimização da captação de turmas de alto nível.",
    stats=[
        {
            "label": "Turmas Analisadas",
            "value": captacao_intelligence.get('resumo', {}).get('total_turmas', 0),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />'
        },
        {
            "label": "Score Médio Captação",
            "value": "%.1f"|format(captacao_intelligence.get('resumo', {}).get('score_medio_captacao', 0)),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />'
        },
        {
            "label": "Total de Leads",
            "value": "{:,}".format(captacao_intelligence.get('resumo', {}).get('total_leads', 0)),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />'
        },
        {
            "label": "Receita Potencial",
            "value": "R$ {:,.0f}".format(captacao_intelligence.get('resumo', {}).get('receita_potencial_total', 0)),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />'
        }
    ]
) }}
{% else %}
<!-- Fallback Hero Section -->
{{ hero_section(
    title="Inteligência Comercial",
    subtitle="Análise avançada com Machine Learning para segmentação e classificação de turmas.",
    stats=[
        {
            "label": "Turmas Analisadas",
            "value": commercial_index.get('summary_stats', {}).get('total_classes', 0),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />'
        },
        {
            "label": "Índice Médio",
            "value": "%.1f"|format(commercial_index.get('summary_stats', {}).get('avg_index', 0)),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />'
        },
        {
            "label": "Clusters Identificados",
            "value": segmentation.get('n_clusters', 0),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />'
        }
    ]
) }}
{% endif %}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Dashboard Principal - Todas as Análises Carregadas -->
    <div class="dashboard-content">

        <!-- Captacao Intelligence Dashboard -->
        {% if captacao_intelligence and 'error' not in captacao_intelligence %}

        <!-- Score de Captação e Distribuição de Potencial -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">🎯 Sistema de Score de Captação</h3>

            <!-- Resumo Executivo -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-900">{{ "%.1f"|format(captacao_intelligence.get('resumo', {}).get('score_medio_captacao', 0)) }}</div>
                    <div class="text-sm text-blue-600">Score Médio de Captação</div>
                </div>

                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-900">{{ "{:.1%}"|format((captacao_intelligence.get('resumo', {}).get('taxa_conversao_media', 0) or 0) / 100) }}</div>
                    <div class="text-sm text-green-600">Taxa Conversão Média</div>
                </div>

                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-900">R$ {{ "{:,.0f}"|format(captacao_intelligence.get('resumo', {}).get('total_receita', 0) or 0) }}</div>
                    <div class="text-sm text-purple-600">Receita Total</div>
                </div>

                <div class="text-center p-4 bg-orange-50 rounded-lg">
                    <div class="text-2xl font-bold text-orange-900">R$ {{ "{:,.0f}"|format(captacao_intelligence.get('resumo', {}).get('receita_potencial_total', 0) or 0) }}</div>
                    <div class="text-sm text-orange-600">Receita Potencial</div>
                </div>
            </div>

            <!-- Distribuição por Potencial -->
            <div class="mb-6">
                <h4 class="text-md font-semibold text-gray-800 mb-3">Distribuição por Potencial de Captação</h4>
                <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
                    {% for potencial, quantidade in captacao_intelligence.get('resumo', {}).get('distribuicao_potencial', {}).items() %}
                    <div class="text-center p-3 rounded-lg
                        {% if potencial == 'Alto Potencial' %}bg-green-100 text-green-800
                        {% elif potencial == 'Bom Potencial' %}bg-blue-100 text-blue-800
                        {% elif potencial == 'Potencial Moderado' %}bg-yellow-100 text-yellow-800
                        {% elif potencial == 'Baixo Potencial' %}bg-orange-100 text-orange-800
                        {% else %}bg-red-100 text-red-800{% endif %}">
                        <div class="text-xl font-bold">{{ quantidade|e }}</div>
                        <div class="text-xs">{{ potencial|e }}</div>
                    </div>
                    {% else %}
                    <div class="col-span-full text-center py-4 text-gray-500">
                        <p>Dados de distribuição não disponíveis</p>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Critérios do Score -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-3">⚖️ Critérios do Score de Captação</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3">
                    {% for criterio, descricao in captacao_intelligence.get('criterios_score', {}).items() %}
                    <div class="text-center p-3 bg-white rounded-lg border">
                        <div class="font-semibold text-sm text-gray-800">{{ criterio|e }}</div>
                        <div class="text-xs text-gray-600 mt-1">{{ descricao|e }}</div>
                    </div>
                    {% else %}
                    <div class="col-span-full text-center py-4 text-gray-500">
                        <p>Critérios de score não disponíveis</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Top Performers vs Baixo Potencial -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Top Performers de Captação -->
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">🏆 Top Performers - Captação</h3>
                <div class="space-y-3">
                    {% for turma in captacao_intelligence.top_captacao[:8] %}
                    <div class="flex items-center justify-between p-3 rounded-lg
                        {% if turma.potencial == 'Alto Potencial' %}bg-green-50
                        {% elif turma.potencial == 'Bom Potencial' %}bg-blue-50
                        {% else %}bg-gray-50{% endif %}">
                        <div class="flex-1">
                            <div class="font-medium
                                {% if turma.potencial == 'Alto Potencial' %}text-green-900
                                {% elif turma.potencial == 'Bom Potencial' %}text-blue-900
                                {% else %}text-gray-900{% endif %}">{{ turma.turma|e }}</div>
                            <div class="text-xs text-gray-600">
                                {{ turma.total_leads|e }} leads • R$ {{ "{:,.0f}"|format(turma.receita_total) }} • {{ "{:.1%}"|format(turma.taxa_conversao_geral / 100) }} conversão
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold
                                {% if turma.potencial == 'Alto Potencial' %}text-green-600
                                {% elif turma.potencial == 'Bom Potencial' %}text-blue-600
                                {% else %}text-gray-600{% endif %}">{{ "%.1f"|format(turma.score_captacao) }}</div>
                            <div class="text-xs
                                {% if turma.potencial == 'Alto Potencial' %}text-green-600
                                {% elif turma.potencial == 'Bom Potencial' %}text-blue-600
                                {% else %}text-gray-600{% endif %}">{{ turma.prioridade|e }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Turmas de Baixo Potencial -->
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">⚠️ Necessitam Atenção - Captação</h3>
                <div class="space-y-3">
                    {% for turma in captacao_intelligence.baixo_potencial[:8] %}
                    <div class="flex items-center justify-between p-3 rounded-lg
                        {% if turma.potencial == 'Potencial Crítico' %}bg-red-50
                        {% elif turma.potencial == 'Baixo Potencial' %}bg-orange-50
                        {% else %}bg-yellow-50{% endif %}">
                        <div class="flex-1">
                            <div class="font-medium
                                {% if turma.potencial == 'Potencial Crítico' %}text-red-900
                                {% elif turma.potencial == 'Baixo Potencial' %}text-orange-900
                                {% else %}text-yellow-900{% endif %}">{{ turma.turma|e }}</div>
                            <div class="text-xs text-gray-600">
                                {{ turma.total_leads|e }} leads • R$ {{ "{:,.0f}"|format(turma.receita_total) }} • {{ "{:.1%}"|format(turma.taxa_conversao_geral / 100) }} conversão
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold
                                {% if turma.potencial == 'Potencial Crítico' %}text-red-600
                                {% elif turma.potencial == 'Baixo Potencial' %}text-orange-600
                                {% else %}text-yellow-600{% endif %}">{{ "%.1f"|format(turma.score_captacao) }}</div>
                            <div class="text-xs
                                {% if turma.potencial == 'Potencial Crítico' %}text-red-600
                                {% elif turma.potencial == 'Baixo Potencial' %}text-orange-600
                                {% else %}text-yellow-600{% endif %}">{{ turma.prioridade|e }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if not captacao_intelligence.baixo_potencial %}
                <div class="text-center py-8 text-gray-500">
                    <div class="text-sm">✅ Todas as turmas com bom potencial de captação!</div>
                </div>
                {% endif %}
            </div>
        </div>

        {% else %}
        <!-- Fallback Business Metrics Dashboard -->
        {% if business_metrics %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">📈 Métricas de Negócio</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-900">R$ {{ "{:,.2f}".format(business_metrics.mrr_total) }}</div>
                    <div class="text-sm text-green-600">MRR Total</div>
                </div>

                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-900">R$ {{ "{:,.2f}".format(business_metrics.avg_ticket) }}</div>
                    <div class="text-sm text-blue-600">Ticket Médio</div>
                </div>

                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-900">{{ "{:.1%}".format(business_metrics.lead_to_opp_rate / 100) }}</div>
                    <div class="text-sm text-purple-600">Taxa Conversão Lead→Opp</div>
                </div>

                <div class="text-center p-4 bg-orange-50 rounded-lg">
                    <div class="text-2xl font-bold text-orange-900">{{ business_metrics.avg_lead_to_opp_days|e }} dias</div>
                    <div class="text-sm text-orange-600">Tempo Médio Conversão</div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-xl font-bold text-gray-900">{{ business_metrics.finalized_count|e }}</div>
                    <div class="text-sm text-gray-600">Implementações Finalizadas</div>
                </div>

                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-xl font-bold text-gray-900">{{ business_metrics.active_implementations|e }}</div>
                    <div class="text-sm text-gray-600">Implementações Ativas</div>
                </div>

                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-xl font-bold text-gray-900">R$ {{ "{:,.2f}".format(business_metrics.potential_revenue) }}</div>
                    <div class="text-sm text-gray-600">Receita Potencial</div>
                </div>
            </div>
        </div>
        {% endif %}
        {% endif %}

        <!-- Commercial Index Summary -->
        {% if commercial_index and 'error' not in commercial_index %}

        <!-- Regras de Classificação e Critérios -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">📋 Sistema de Classificação de Turmas</h3>

            <!-- Distribuição por Performance -->
            {% if commercial_index.summary_stats.distribuicao_performance %}
            <div class="mb-6">
                <h4 class="text-md font-semibold text-gray-800 mb-3">Distribuição Atual</h4>
                <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
                    {% for classificacao, quantidade in commercial_index.summary_stats.distribuicao_performance.items() %}
                    <div class="text-center p-3 rounded-lg
                        {% if classificacao == 'Excelente' %}bg-green-100 text-green-800
                        {% elif classificacao == 'Boa' %}bg-blue-100 text-blue-800
                        {% elif classificacao == 'Moderada' %}bg-yellow-100 text-yellow-800
                        {% elif classificacao == 'Baixa' %}bg-orange-100 text-orange-800
                        {% else %}bg-red-100 text-red-800{% endif %}">
                        <div class="text-xl font-bold">{{ quantidade|e }}</div>
                        <div class="text-xs">{{ classificacao|e }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Regras de Classificação -->
                {% if commercial_index.regras_classificacao %}
                <div>
                    <h4 class="text-md font-semibold text-gray-800 mb-3">🎯 Regras de Classificação</h4>
                    <div class="space-y-3">
                        {% for classificacao, descricao in commercial_index.regras_classificacao.items() %}
                        <div class="p-3 rounded-lg border-l-4
                            {% if classificacao == 'Excelente' %}border-green-500 bg-green-50
                            {% elif classificacao == 'Boa' %}border-blue-500 bg-blue-50
                            {% elif classificacao == 'Moderada' %}border-yellow-500 bg-yellow-50
                            {% elif classificacao == 'Baixa' %}border-orange-500 bg-orange-50
                            {% else %}border-red-500 bg-red-50{% endif %}">
                            <div class="font-semibold text-sm">{{ classificacao|e }}</div>
                            <div class="text-xs text-gray-600">{{ descricao|e }}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Critérios do Score -->
                {% if commercial_index.criterios_score %}
                <div>
                    <h4 class="text-md font-semibold text-gray-800 mb-3">⚖️ Critérios do Score</h4>
                    <div class="space-y-3">
                        {% for criterio, descricao in commercial_index.criterios_score.items() %}
                        <div class="p-3 bg-gray-50 rounded-lg">
                            <div class="font-semibold text-sm text-gray-800">{{ criterio|e }}</div>
                            <div class="text-xs text-gray-600">{{ descricao|e }}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Métricas Resumo -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-900">{{ "%.1f"|format(commercial_index.summary_stats.avg_commercial_index) }}</div>
                        <div class="text-sm text-gray-600">Score Médio</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-900">{{ "{:.1%}"|format(commercial_index.summary_stats.avg_conversion_rate / 100) }}</div>
                        <div class="text-sm text-gray-600">Conversão Média</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-900">{{ "{:.1%}"|format(commercial_index.summary_stats.avg_cancellation_rate / 100) }}</div>
                        <div class="text-sm text-gray-600">Taxa Cancelamento</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-900">{{ "{:.1%}"|format(commercial_index.summary_stats.avg_coupon_usage / 100) }}</div>
                        <div class="text-sm text-gray-600">Uso de Cupons</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Top Performers -->
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">🏆 Top Performers</h3>
                <div class="space-y-3">
                    {% for turma in commercial_index.summary_stats.top_performers[:8] %}
                    <div class="flex items-center justify-between p-3 rounded-lg
                        {% if turma.classificacao == 'Excelente' %}bg-green-50
                        {% elif turma.classificacao == 'Boa' %}bg-blue-50
                        {% else %}bg-gray-50{% endif %}">
                        <div class="flex-1">
                            <div class="font-medium
                                {% if turma.classificacao == 'Excelente' %}text-green-900
                                {% elif turma.classificacao == 'Boa' %}text-blue-900
                                {% else %}text-gray-900{% endif %}">{{ turma.Turma|e }}</div>
                            <div class="text-xs text-gray-600">
                                R$ {{ "{:,.0f}"|format(turma.receita_total) }} • {{ "{:.1%}"|format(turma.taxa_conversao_geral / 100) }} conversão
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold
                                {% if turma.classificacao == 'Excelente' %}text-green-600
                                {% elif turma.classificacao == 'Boa' %}text-blue-600
                                {% else %}text-gray-600{% endif %}">{{ "%.1f"|format(turma.indice_comercial) }}</div>
                            <div class="text-xs
                                {% if turma.classificacao == 'Excelente' %}text-green-600
                                {% elif turma.classificacao == 'Boa' %}text-blue-600
                                {% else %}text-gray-600{% endif %}">{{ turma.classificacao|e }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Bottom Performers -->
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">⚠️ Necessitam Atenção</h3>
                <div class="space-y-3">
                    {% for turma in commercial_index.summary_stats.bottom_performers %}
                    <div class="flex items-center justify-between p-3 rounded-lg
                        {% if turma.classificacao == 'Crítica' %}bg-red-50
                        {% elif turma.classificacao == 'Baixa' %}bg-orange-50
                        {% else %}bg-yellow-50{% endif %}">
                        <div class="flex-1">
                            <div class="font-medium
                                {% if turma.classificacao == 'Crítica' %}text-red-900
                                {% elif turma.classificacao == 'Baixa' %}text-orange-900
                                {% else %}text-yellow-900{% endif %}">{{ turma.Turma|e }}</div>
                            <div class="text-xs text-gray-600">
                                R$ {{ "{:,.0f}"|format(turma.receita_total) }} • {{ "{:.1%}"|format(turma.taxa_conversao_geral / 100) }} conversão
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold
                                {% if turma.classificacao == 'Crítica' %}text-red-600
                                {% elif turma.classificacao == 'Baixa' %}text-orange-600
                                {% else %}text-yellow-600{% endif %}">{{ "%.1f"|format(turma.indice_comercial) }}</div>
                            <div class="text-xs
                                {% if turma.classificacao == 'Crítica' %}text-red-600
                                {% elif turma.classificacao == 'Baixa' %}text-orange-600
                                {% else %}text-yellow-600{% endif %}">{{ turma.classificacao|e }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Overview Charts -->
        {% if commercial_index and 'summary_stats' in commercial_index %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">📊 Visão Geral dos Dados</h3>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Classification Distribution Chart -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="h-80">
                        <canvas id="classificationChart"></canvas>
                    </div>
                </div>

                <!-- Risk vs Performance Overview -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="h-80">
                        <canvas id="riskPerformanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Classification Distribution -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">📊 Distribuição por Classificação</h3>
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                {% for classification, count in commercial_index['summary_stats'].get('classification_distribution', {}).items() %}
                <div class="text-center p-4 rounded-lg
                    {% if classification == 'Excelente' %}bg-green-100 text-green-800
                    {% elif classification == 'Alto Potencial' %}bg-blue-100 text-blue-800
                    {% elif classification == 'Potencial Médio' %}bg-yellow-100 text-yellow-800
                    {% elif classification == 'Potencial Limitado' %}bg-orange-100 text-orange-800
                    {% else %}bg-red-100 text-red-800{% endif %}">
                    <div class="text-2xl font-bold">{{ count|e }}</div>
                    <div class="text-sm">{{ classification|e }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Segmentation Overview -->
        {% if segmentation_data %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">🎯 Segmentação de Turmas</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for cluster in segmentation_data %}
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-gray-900">Cluster {{ cluster.cluster_id + 1|e }}</h4>
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                            {{ cluster.size|e }} turmas
                        </span>
                    </div>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div>Receita Média: R$ {{ "%.2f"|format(cluster.avg_revenue) }}</div>
                        <div>Conversão Média: {{ "%.1f"|format(cluster.avg_conversion * 100) }}%</div>
                        <div>Alunos Médios: {{ "%.0f"|format(cluster.avg_students) }}</div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Risk Distribution -->
        {% if risk_distribution %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">⚡ Distribuição de Risco</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                {% for risk_level, count in risk_distribution.items() %}
                <div class="text-center p-4 rounded-lg
                    {% if risk_level == 'Baixo' %}bg-green-100 text-green-800
                    {% elif risk_level == 'Médio' %}bg-yellow-100 text-yellow-800
                    {% elif risk_level == 'Alto' %}bg-orange-100 text-orange-800
                    {% else %}bg-red-100 text-red-800{% endif %}">
                    <div class="text-2xl font-bold">{{ count|e }}</div>
                    <div class="text-sm">Risco {{ risk_level|e }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Análises Específicas -->
        {% if commercial_index and 'summary_stats' in commercial_index %}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Turmas com Alta Taxa de Cancelamento -->
            {% if commercial_index.summary_stats.turmas_alta_taxa_cancelamento %}
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">⚠️ Alta Taxa de Cancelamento</h3>
                <div class="space-y-3">
                    {% for turma in commercial_index.summary_stats.turmas_alta_taxa_cancelamento %}
                    <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                        <div class="flex-1">
                            <div class="font-medium text-red-900">{{ turma.Turma|e }}</div>
                            <div class="text-xs text-gray-600">{{ turma.total_implementacoes|e }} implementações</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-red-600">{{ "{:.1%}"|format(turma.taxa_cancelamento / 100) }}</div>
                            <div class="text-xs text-red-600">Cancelamento</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if not commercial_index.summary_stats.turmas_alta_taxa_cancelamento %}
                <div class="text-center py-8 text-gray-500">
                    <div class="text-sm">✅ Nenhuma turma com alta taxa de cancelamento</div>
                </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- Turmas com Boa Performance em Cupons -->
            {% if commercial_index.summary_stats.turmas_bom_uso_cupom %}
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">🎟️ Boa Performance com Cupons</h3>
                <div class="space-y-3">
                    {% for turma in commercial_index.summary_stats.turmas_bom_uso_cupom %}
                    <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                        <div class="flex-1">
                            <div class="font-medium text-purple-900">{{ turma.Turma|e }}</div>
                            <div class="text-xs text-gray-600">{{ "{:.1%}"|format(turma.taxa_conversao_geral / 100) }} conversão</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-purple-600">{{ "{:.1%}"|format(turma.taxa_uso_cupom / 100) }}</div>
                            <div class="text-xs text-purple-600">Uso de Cupons</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if not commercial_index.summary_stats.turmas_bom_uso_cupom %}
                <div class="text-center py-8 text-gray-500">
                    <div class="text-sm">📊 Nenhuma turma com alto uso de cupons</div>
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- Insights e Ações Rápidas -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">💡 Insights e Ações Rápidas</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Universidades -->
                <div class="p-4 border border-gray-200 rounded-lg bg-blue-50">
                    <div class="flex items-center mb-2">
                        <svg class="w-6 h-6 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10" />
                        </svg>
                        <h4 class="text-sm font-medium text-blue-900">Universidades</h4>
                    </div>
                    <div class="text-2xl font-bold text-blue-900">{{ commercial_index.get('summary_stats', {}).get('total_universities', 0) or 'N/A' }}</div>
                    <div class="text-xs text-blue-700">Acompanhe o desempenho por universidade, visualize métricas de conversão e receita recorrente.</div>
                </div>

                <!-- Análise de Risco -->
                <div class="p-4 border border-gray-200 rounded-lg bg-orange-50">
                    <div class="flex items-center mb-2">
                        <svg class="w-6 h-6 text-orange-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        <h4 class="text-sm font-medium text-orange-900">Análise de Risco</h4>
                    </div>
                    <div class="text-2xl font-bold text-orange-900">{{ (risk_distribution.get('Alto', 0) + risk_distribution.get('Crítico', 0)) or 'N/A' }}</div>
                    <div class="text-xs text-orange-700">Identifique turmas com alto risco de cancelamento e implemente ações preventivas.</div>
                </div>

                <!-- Segmentação ML -->
                <div class="p-4 border border-gray-200 rounded-lg bg-purple-50">
                    <div class="flex items-center mb-2">
                        <svg class="w-6 h-6 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <h4 class="text-sm font-medium text-purple-900">Segmentação ML</h4>
                    </div>
                    <div class="text-2xl font-bold text-purple-900">{{ segmentation.get('n_clusters', 0) or 'N/A' }}</div>
                    <div class="text-xs text-purple-700">Clusters identificados por Machine Learning para otimização de estratégias comerciais.</div>
                </div>

                <!-- Previsão de Receita -->
                <div class="p-4 border border-gray-200 rounded-lg bg-green-50">
                    <div class="flex items-center mb-2">
                        <svg class="w-6 h-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        <h4 class="text-sm font-medium text-green-900">Previsão de Receita</h4>
                    </div>
                    <div class="text-2xl font-bold text-green-900">{{ "R$ {:,.0f}".format(revenue_forecast.get('forecast_summary', {}).get('total_forecast', 0)) if revenue_forecast else 'N/A' }}</div>
                    <div class="text-xs text-green-700">Projeção de receita baseada em análise preditiva e tendências históricas.</div>
                </div>
            </div>
        </div>

        <!-- Recomendações Estratégicas -->
        {% if captacao_intelligence and 'strategic_recommendations' in captacao_intelligence %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">📋 Recomendações Estratégicas para Captação</h3>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {% for recommendation in captacao_intelligence.strategic_recommendations %}
                <div class="border border-gray-200 rounded-lg p-6
                    {% if recommendation.prioridade == 'Alta' %}border-l-4 border-l-red-500
                    {% elif recommendation.prioridade == 'Média' %}border-l-4 border-l-yellow-500
                    {% else %}border-l-4 border-l-green-500{% endif %}">

                    <div class="flex items-start justify-between mb-4">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900">{{ recommendation.titulo|e }}</h4>
                            <div class="flex items-center space-x-2 mt-1">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if recommendation.prioridade == 'Alta' %}bg-red-100 text-red-800
                                    {% elif recommendation.prioridade == 'Média' %}bg-yellow-100 text-yellow-800
                                    {% else %}bg-green-100 text-green-800{% endif %}">
                                    {{ recommendation.prioridade|e }} Prioridade
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ recommendation.categoria|e }}
                                </span>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">{{ recommendation.impacto_estimado|e }} Impacto</div>
                            <div class="text-xs text-gray-500">{{ recommendation.prazo|e }}</div>
                        </div>
                    </div>

                    <p class="text-gray-700 mb-4">{{ recommendation.descricao|e }}</p>

                    <div>
                        <h5 class="text-sm font-semibold text-gray-900 mb-2">Ações Sugeridas:</h5>
                        <ul class="space-y-1">
                            {% for acao in recommendation.acoes %}
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm text-gray-700">{{ acao|e }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js" integrity="sha384-fQybjgWLrvvRgtW5LEVLyXCoLtdnb2YIQnikzDOWEI1rExsAa+dxLdEaQCyUoRiY" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

<script>
    // Global chart configurations
    Chart.defaults.font.family = 'Inter, system-ui, sans-serif';
    Chart.defaults.color = '#6B7280';

    document.addEventListener('DOMContentLoaded', function() {
        console.log('Enhanced Commercial Intelligence dashboard loaded');

        // Initialize overview charts
        {% if commercial_index and 'summary_stats' in commercial_index %}
        initializeOverviewCharts();
        {% endif %}

        // Initialize captacao intelligence charts
        {% if captacao_intelligence and 'error' not in captacao_intelligence %}
        initializeCaptacaoCharts();
        {% endif %}
    });

    function initializeCaptacaoCharts() {
        // Initialize charts specific to captacao intelligence
        {% if captacao_intelligence and 'resumo' in captacao_intelligence %}
        createCaptacaoDistributionChart();
        createCaptacaoScoreChart();
        {% endif %}
    }

    function createCaptacaoDistributionChart() {
        const ctx = document.getElementById('captacaoDistributionChart');
        if (!ctx) return;

        const distributionData = {{ captacao_intelligence.get('resumo', {}).get('distribuicao_potencial', {}) | tojson if captacao_intelligence and 'resumo' in captacao_intelligence else '{}' }};

        const labels = Object.keys(distributionData);
        const data = Object.values(distributionData);
        const colors = {
            'Alto Potencial': '#10B981',
            'Bom Potencial': '#3B82F6',
            'Potencial Moderado': '#F59E0B',
            'Baixo Potencial': '#F97316',
            'Potencial Crítico': '#EF4444'
        };

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels,
                datasets: [{
                    data,
                    backgroundColor: labels.map(label => colors[label] || '#6B7280'),
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    title: {
                        display: true,
                        text: 'Distribuição por Potencial de Captação',
                        font: { size: 16, weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} turmas (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    function createCaptacaoScoreChart() {
        const ctx = document.getElementById('captacaoScoreChart');
        if (!ctx) return;

        const topCaptacao = {{ captacao_intelligence.get('top_captacao', [])[:10] | tojson if captacao_intelligence and 'top_captacao' in captacao_intelligence else '[]' }};

        if (topCaptacao.length === 0) return;

        const labels = topCaptacao.map(t => t.turma);
        const scores = topCaptacao.map(t => t.score_captacao);
        const conversions = topCaptacao.map(t => t.taxa_conversao_geral);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels,
                datasets: [{
                    label: 'Score de Captação',
                    data: scores,
                    backgroundColor: 'rgba(59, 130, 246, 0.8)',
                    borderColor: 'rgb(59, 130, 246)',
                    borderWidth: 1,
                    yAxisID: 'y'
                }, {
                    label: 'Taxa de Conversão (%)',
                    data: conversions,
                    type: 'line',
                    borderColor: 'rgb(16, 185, 129)',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 2,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Turmas'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Score de Captação'
                        },
                        min: 0,
                        max: 100
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Taxa de Conversão (%)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        min: 0
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Top 10 Turmas - Score vs Conversão',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        display: true
                    }
                }
            }
        });
    }

    function initializeOverviewCharts() {
        // 1. Classification Distribution Chart
        createClassificationChart();

        // 2. Risk vs Performance Overview
        createRiskPerformanceChart();
    }

    function createClassificationChart() {
        const ctx = document.getElementById('classificationChart');
        if (!ctx) return;

        const classificationData = {{ commercial_index.get('summary_stats', {}).get('distribuicao_performance', {}) | tojson if commercial_index and 'summary_stats' in commercial_index else '{}' }};

        const labels = Object.keys(classificationData);
        const data = Object.values(classificationData);
        const colors = {
            'Excelente': '#10B981',
            'Boa': '#3B82F6',
            'Moderada': '#F59E0B',
            'Baixa': '#F97316',
            'Crítica': '#EF4444'
        };

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels,
                datasets: [{
                    data,
                    backgroundColor: labels.map(label => colors[label] || '#6B7280'),
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    title: {
                        display: true,
                        text: 'Distribuição por Classificação Comercial',
                        font: { size: 16, weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} turmas (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    function createRiskPerformanceChart() {
        const ctx = document.getElementById('riskPerformanceChart');
        if (!ctx) return;

        const riskDistribution = {{ risk_distribution | tojson if risk_distribution else '{}' }};
        const segmentationData = {{ segmentation_data | tojson if segmentation_data else '[]' }};

        // Create a combined view of risk and performance
        const combinedData = {
            'Alto Desempenho': segmentationData.filter(c => c.avg_revenue > 5000).length || 0,
            'Desempenho Médio': segmentationData.filter(c => c.avg_revenue >= 1000 && c.avg_revenue <= 5000).length || 0,
            'Baixo Desempenho': segmentationData.filter(c => c.avg_revenue < 1000).length || 0,
            'Alto Risco': riskDistribution['Alto'] || 0,
            'Risco Crítico': riskDistribution['Crítico'] || 0
        };

        const labels = Object.keys(combinedData);
        const data = Object.values(combinedData);
        const backgroundColors = [
            '#10B981', // Green for high performance
            '#3B82F6', // Blue for medium performance
            '#F59E0B', // Yellow for low performance
            '#F97316', // Orange for high risk
            '#EF4444'  // Red for critical risk
        ];

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels,
                datasets: [{
                    label: 'Número de Turmas',
                    data,
                    backgroundColor: backgroundColors,
                    borderColor: '#374151',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Número de Turmas'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Categoria'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Panorama de Desempenho e Risco',
                        font: { size: 16, weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.parsed.y} turmas`;
                            }
                        }
                    }
                }
            }
        });
    }
</script>
{% endblock %}
