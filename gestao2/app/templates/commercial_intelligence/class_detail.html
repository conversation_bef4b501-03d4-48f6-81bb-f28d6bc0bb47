{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table %}

{% block title %}{{ class_name|e }} - <PERSON><PERSON><PERSON><PERSON>alhada - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Análise Detalhada: " + class_name,
    subtitle="Análise completa de inteligência comercial com insights de Machine Learning para esta turma específica.",
    stats=[
        {
            "label": "Índice Comercial",
            "value": "%.1f"|format(class_data.get('commercial_metrics', {}).get('commercial_index', 0)),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />'
        },
        {
            "label": "Receita Total",
            "value": "R$ " + "{:,.2f}"|format(class_data.get('commercial_metrics', {}).get('total_revenue', 0)),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />'
        },
        {
            "label": "Taxa de Conversão",
            "value": "%.1f%%"|format((class_data.get('commercial_metrics', {}).get('conversion_rate', 0) * 100)),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />'
        }
    ]
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Navigation -->
    <div class="mb-8">
        <nav class="flex space-x-8" aria-label="Tabs">
            <a href="/commercial-intelligence/" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                ← Voltar à Visão Geral
            </a>
        </nav>
    </div>

    {% if class_data.get('commercial_metrics') %}
    <!-- Commercial Metrics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total de Alunos</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ class_data.commercial_metrics.get('total_students', 0)|e }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Oportunidades</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ class_data.commercial_metrics.get('total_opportunities', 0)|e }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Receita por Aluno</dt>
                        <dd class="text-lg font-medium text-gray-900">R$ {{ "{:,.2f}"|format(class_data.commercial_metrics.get('revenue_per_student', 0)) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Tempo Implementação</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ "%.0f"|format(class_data.commercial_metrics.get('time_to_implementation', 0)) }} dias</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Indicators -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Commercial Index Breakdown -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">📊 Breakdown do Índice Comercial</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm font-medium text-gray-700">Índice de Receita</span>
                    <div class="flex items-center">
                        <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ (class_data.commercial_metrics.get('revenue_index', 0) * 100)|e }}%"></div>
                        </div>
                        <span class="text-sm text-gray-900">{{ "%.1f"|format(class_data.commercial_metrics.get('revenue_index', 0) * 100) }}%</span>
                    </div>
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-sm font-medium text-gray-700">Índice de Conversão</span>
                    <div class="flex items-center">
                        <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                            <div class="bg-green-600 h-2 rounded-full" style="width: {{ (class_data.commercial_metrics.get('conversion_index', 0) * 100)|e }}%"></div>
                        </div>
                        <span class="text-sm text-gray-900">{{ "%.1f"|format(class_data.commercial_metrics.get('conversion_index', 0) * 100) }}%</span>
                    </div>
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-sm font-medium text-gray-700">Índice de Crescimento</span>
                    <div class="flex items-center">
                        <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                            <div class="bg-purple-600 h-2 rounded-full" style="width: {{ (class_data.commercial_metrics.get('growth_index', 0) * 100)|e }}%"></div>
                        </div>
                        <span class="text-sm text-gray-900">{{ "%.1f"|format(class_data.commercial_metrics.get('growth_index', 0) * 100) }}%</span>
                    </div>
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-sm font-medium text-gray-700">Índice de Eficiência</span>
                    <div class="flex items-center">
                        <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                            <div class="bg-yellow-600 h-2 rounded-full" style="width: {{ (class_data.commercial_metrics.get('efficiency_index', 0) * 100)|e }}%"></div>
                        </div>
                        <span class="text-sm text-gray-900">{{ "%.1f"|format(class_data.commercial_metrics.get('efficiency_index', 0) * 100) }}%</span>
                    </div>
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-sm font-medium text-gray-700">Índice de Estabilidade</span>
                    <div class="flex items-center">
                        <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                            <div class="bg-red-600 h-2 rounded-full" style="width: {{ (class_data.commercial_metrics.get('stability_index', 0) * 100)|e }}%"></div>
                        </div>
                        <span class="text-sm text-gray-900">{{ "%.1f"|format(class_data.commercial_metrics.get('stability_index', 0) * 100) }}%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Risk Assessment -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">⚠️ Avaliação de Risco</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span class="text-sm font-medium text-gray-700">Taxa de Churn</span>
                    <span class="text-sm font-medium text-gray-900">{{ "%.1f%%"|format(class_data.commercial_metrics.get('churn_rate', 0) * 100) }}</span>
                </div>
                
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span class="text-sm font-medium text-gray-700">Score de Atividade</span>
                    <span class="text-sm font-medium text-gray-900">{{ "%.1f%%"|format(class_data.commercial_metrics.get('activity_score', 0) * 100) }}</span>
                </div>
                
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span class="text-sm font-medium text-gray-700">Diversidade Geográfica</span>
                    <span class="text-sm font-medium text-gray-900">{{ class_data.commercial_metrics.get('geographic_diversity', 0)|e }}</span>
                </div>
                
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span class="text-sm font-medium text-gray-700">Diversidade de Produtos</span>
                    <span class="text-sm font-medium text-gray-900">{{ class_data.commercial_metrics.get('product_diversity', 0)|e }}</span>
                </div>
            </div>
            
            <!-- Risk Level Indicator -->
            <div class="mt-6 p-4 rounded-lg 
                {% set churn_rate = class_data.commercial_metrics.get('churn_rate', 0) %}
                {% if churn_rate > 0.6 %}bg-red-50 border border-red-200
                {% elif churn_rate > 0.4 %}bg-orange-50 border border-orange-200
                {% elif churn_rate > 0.2 %}bg-yellow-50 border border-yellow-200
                {% else %}bg-green-50 border border-green-200{% endif %}">
                <div class="flex items-center">
                    <svg class="w-5 h-5 
                        {% if churn_rate > 0.6 %}text-red-600
                        {% elif churn_rate > 0.4 %}text-orange-600
                        {% elif churn_rate > 0.2 %}text-yellow-600
                        {% else %}text-green-600{% endif %} mr-2" 
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        {% if churn_rate > 0.4 %}
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        {% else %}
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        {% endif %}
                    </svg>
                    <span class="text-sm font-medium 
                        {% if churn_rate > 0.6 %}text-red-800
                        {% elif churn_rate > 0.4 %}text-orange-800
                        {% elif churn_rate > 0.2 %}text-yellow-800
                        {% else %}text-green-800{% endif %}">
                        Nível de Risco: 
                        {% if churn_rate > 0.6 %}Crítico
                        {% elif churn_rate > 0.4 %}Alto
                        {% elif churn_rate > 0.2 %}Médio
                        {% else %}Baixo{% endif %}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Classification and Recommendations -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">🎯 Classificação e Recomendações</h3>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Classification -->
            <div>
                <h4 class="text-md font-semibold text-gray-900 mb-3">Classificação Comercial</h4>
                <div class="text-center p-6 rounded-lg 
                    {% set commercial_index = class_data.commercial_metrics.get('commercial_index', 0) %}
                    {% if commercial_index >= 80 %}bg-green-50 border border-green-200
                    {% elif commercial_index >= 60 %}bg-blue-50 border border-blue-200
                    {% elif commercial_index >= 40 %}bg-yellow-50 border border-yellow-200
                    {% elif commercial_index >= 20 %}bg-orange-50 border border-orange-200
                    {% else %}bg-red-50 border border-red-200{% endif %}">
                    <div class="text-3xl font-bold 
                        {% if commercial_index >= 80 %}text-green-600
                        {% elif commercial_index >= 60 %}text-blue-600
                        {% elif commercial_index >= 40 %}text-yellow-600
                        {% elif commercial_index >= 20 %}text-orange-600
                        {% else %}text-red-600{% endif %}">
                        {{ "%.1f"|format(commercial_index) }}
                    </div>
                    <div class="text-sm font-medium 
                        {% if commercial_index >= 80 %}text-green-800
                        {% elif commercial_index >= 60 %}text-blue-800
                        {% elif commercial_index >= 40 %}text-yellow-800
                        {% elif commercial_index >= 20 %}text-orange-800
                        {% else %}text-red-800{% endif %}">
                        {% if commercial_index >= 80 %}Excelente
                        {% elif commercial_index >= 60 %}Alto Potencial
                        {% elif commercial_index >= 40 %}Potencial Médio
                        {% elif commercial_index >= 20 %}Potencial Limitado
                        {% else %}Baixo Potencial{% endif %}
                    </div>
                </div>
            </div>

            <!-- Recommendations -->
            <div>
                <h4 class="text-md font-semibold text-gray-900 mb-3">Recomendações Estratégicas</h4>
                <div class="space-y-3">
                    {% if commercial_index >= 80 %}
                    <div class="flex items-start p-3 bg-green-50 rounded-lg">
                        <svg class="w-5 h-5 text-green-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span class="text-sm text-green-800">Manter estratégias atuais e replicar para outras turmas</span>
                    </div>
                    <div class="flex items-start p-3 bg-green-50 rounded-lg">
                        <svg class="w-5 h-5 text-green-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        <span class="text-sm text-green-800">Aumentar investimento para maximizar retorno</span>
                    </div>
                    {% elif commercial_index >= 40 %}
                    <div class="flex items-start p-3 bg-blue-50 rounded-lg">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        <span class="text-sm text-blue-800">Otimizar processo de conversão</span>
                    </div>
                    <div class="flex items-start p-3 bg-blue-50 rounded-lg">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span class="text-sm text-blue-800">Melhorar engajamento dos alunos</span>
                    </div>
                    {% else %}
                    <div class="flex items-start p-3 bg-orange-50 rounded-lg">
                        <svg class="w-5 h-5 text-orange-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        <span class="text-sm text-orange-800">Revisar estratégia comercial urgentemente</span>
                    </div>
                    <div class="flex items-start p-3 bg-orange-50 rounded-lg">
                        <svg class="w-5 h-5 text-orange-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span class="text-sm text-orange-800">Implementar programa de retenção</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    {% else %}
    <!-- No Data State -->
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">Dados não disponíveis</h3>
        <p class="mt-1 text-sm text-gray-500">Não foi possível carregar os dados de análise para esta turma.</p>
        <div class="mt-6">
            <a href="/commercial-intelligence/" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Voltar à Análise Geral
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Class detail analysis loaded for:', '{{ class_name|e }}');
    });
</script>
{% endblock %}
