{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table %}

{% block title %}Previsões de Receita - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Previsões de Receita",
    subtitle="Análise preditiva com Machine Learning para projeção de receita e identificação de potencial comercial.",
    stats=[
        {
            "label": "Modelos ML",
            "value": "Random Forest",
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />'
        },
        {
            "label": "Períodos Analisados",
            "value": forecast_results|length,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />'
        },
        {
            "label": "Acurácia Modelo",
            "value": "%.1f%%"|format((forecast_results.values()|map(attribute='model_score')|max * 100) if forecast_results else 0),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />'
        }
    ]
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Navigation -->
    <div class="mb-8">
        <nav class="flex space-x-8" aria-label="Tabs">
            <a href="/commercial-intelligence/" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                Visão Geral
            </a>
            <a href="/commercial-intelligence/segmentation" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                Segmentação
            </a>
            <a href="#forecasting" class="tab-link active border-primary text-primary border-b-2 py-2 px-1 text-sm font-medium">
                Previsões
            </a>
            <a href="/commercial-intelligence/risk-analysis" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                Análise de Risco
            </a>
        </nav>
    </div>

    {% if forecast_results %}
    <!-- Revenue Forecast Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {% for months, result in forecast_results.items() %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">{{ months|e }} meses</dt>
                        <dd class="text-lg font-medium text-gray-900">R$ {{ "{:,.2f}"|format(result.total_forecasted_revenue) }}</dd>
                    </dl>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm">
                    <span class="text-green-600 font-medium">+{{ "{:,.2f}"|format(result.total_growth_potential) }}</span>
                    <span class="text-gray-500 ml-2">crescimento</span>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Detailed Forecast Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">📈 Previsões Detalhadas por Turma</h3>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Turma</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Receita Atual</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Previsão 6m</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Potencial Crescimento</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tendência</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% if forecast_results[6] %}
                    {% for forecast in forecast_results[6].revenue_forecasts[:20] %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ forecast.turma|e }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">R$ {{ "{:,.2f}"|format(forecast.total_revenue) }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">R$ {{ "{:,.2f}"|format(forecast.forecasted_revenue) }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {% if forecast.revenue_growth_potential > 0 %}
                                <span class="text-green-600">+R$ {{ "{:,.2f}"|format(forecast.revenue_growth_potential) }}</span>
                                {% else %}
                                <span class="text-red-600">R$ {{ "{:,.2f}"|format(forecast.revenue_growth_potential) }}</span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% set growth_rate = (forecast.revenue_growth_potential / forecast.total_revenue * 100) if forecast.total_revenue > 0 else 0 %}
                            {% if growth_rate > 20 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Alto Crescimento
                            </span>
                            {% elif growth_rate > 10 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Crescimento Moderado
                            </span>
                            {% elif growth_rate > 0 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Crescimento Baixo
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Declínio
                            </span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    {% if potential_prediction and 'error' not in potential_prediction %}
    <!-- Potential Prediction -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">🎯 Predição de Potencial Comercial</h3>
            <p class="text-sm text-gray-600 mt-1">Modelo de Machine Learning para identificar turmas com alto potencial</p>
        </div>

        <div class="p-6">
            <!-- Model Performance -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">{{ "%.1f%%"|format(potential_prediction.model_accuracy * 100) }}</div>
                    <div class="text-sm text-blue-800">Acurácia do Modelo</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">{{ potential_prediction.high_potential_classes|length }}</div>
                    <div class="text-sm text-green-800">Turmas Alto Potencial</div>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">{{ potential_prediction.predictions|length }}</div>
                    <div class="text-sm text-purple-800">Total Analisadas</div>
                </div>
            </div>

            <!-- Feature Importance -->
            <div class="mb-6">
                <h4 class="text-md font-semibold text-gray-900 mb-4">🔍 Fatores Mais Importantes</h4>
                <div class="space-y-3">
                    {% for feature, importance in potential_prediction.feature_importance.items() %}
                    {% if loop.index <= 5 %}
                    <div class="flex items-center">
                        <div class="flex-1">
                            <div class="flex justify-between text-sm">
                                <span class="font-medium text-gray-700">{{ feature.replace('_', ' ').title()|e }}</span>
                                <span class="text-gray-500">{{ "%.1f%%"|format(importance * 100) }}</span>
                            </div>
                            <div class="mt-1 bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: {{ importance * 100|e }}%"></div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>

            <!-- High Potential Classes -->
            <div>
                <h4 class="text-md font-semibold text-gray-900 mb-4">⭐ Turmas de Alto Potencial</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for prediction in potential_prediction.predictions %}
                    {% if prediction.predicted_potential == 1 %}
                    <div class="border border-green-200 rounded-lg p-4 bg-green-50">
                        <div class="flex justify-between items-start mb-2">
                            <h5 class="font-medium text-green-900">{{ prediction.turma|e }}</h5>
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">
                                {{ "%.0f%%"|format(prediction.potential_probability * 100) }}
                            </span>
                        </div>
                        <div class="text-sm text-green-700">
                            Probabilidade de alto potencial: {{ "%.1f%%"|format(prediction.potential_probability * 100) }}
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Forecast Visualization Charts -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">📊 Visualização de Previsões</h3>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Revenue Forecast Timeline -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="h-80">
                        <canvas id="revenueForecastChart"></canvas>
                    </div>
                </div>

                <!-- Growth Potential Distribution -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="h-80">
                        <canvas id="growthPotentialChart"></canvas>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Top Classes by Potential -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="h-80">
                        <canvas id="topClassesChart"></canvas>
                    </div>
                </div>

                <!-- Forecast Accuracy Metrics -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="h-80">
                        <canvas id="accuracyMetricsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Forecast Comparison Chart -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">📊 Comparação de Cenários</h3>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Current vs Forecast -->
                <div>
                    <h4 class="text-md font-semibold text-gray-900 mb-4">Receita Atual vs Projetada</h4>
                    {% if forecast_results %}
                    <div class="space-y-4">
                        {% for months, result in forecast_results.items() %}
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">{{ months|e }} meses</span>
                            <div class="flex items-center space-x-4">
                                <span class="text-sm text-gray-600">R$ {{ "{:,.0f}"|format(result.total_current_revenue) }}</span>
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                </svg>
                                <span class="text-sm font-medium text-blue-600">R$ {{ "{:,.0f}"|format(result.total_forecasted_revenue) }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- Growth Potential -->
                <div>
                    <h4 class="text-md font-semibold text-gray-900 mb-4">Potencial de Crescimento</h4>
                    {% if forecast_results %}
                    <div class="space-y-4">
                        {% for months, result in forecast_results.items() %}
                        {% set growth_percentage = (result.total_growth_potential / result.total_current_revenue * 100) if result.total_current_revenue > 0 else 0 %}
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">{{ months|e }} meses</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-medium text-green-600">+{{ "%.1f%%"|format(growth_percentage) }}</span>
                                <span class="text-xs text-gray-500">(R$ {{ "{:,.0f}"|format(result.total_growth_potential) }})</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Actions and Recommendations -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">🚀 Recomendações Estratégicas</h3>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="border border-blue-200 rounded-lg p-4 bg-blue-50">
                    <h4 class="font-medium text-blue-900 mb-2">Investimento Prioritário</h4>
                    <p class="text-sm text-blue-700 mb-3">Focar recursos nas turmas de alto potencial identificadas pelo modelo</p>
                    <ul class="text-xs text-blue-600 space-y-1">
                        <li>• Aumentar orçamento de marketing</li>
                        <li>• Melhorar processo de onboarding</li>
                        <li>• Implementar acompanhamento personalizado</li>
                    </ul>
                </div>

                <div class="border border-green-200 rounded-lg p-4 bg-green-50">
                    <h4 class="font-medium text-green-900 mb-2">Otimização de Receita</h4>
                    <p class="text-sm text-green-700 mb-3">Estratégias para maximizar o crescimento projetado</p>
                    <ul class="text-xs text-green-600 space-y-1">
                        <li>• Revisar estrutura de preços</li>
                        <li>• Implementar upselling</li>
                        <li>• Reduzir churn rate</li>
                    </ul>
                </div>

                <div class="border border-purple-200 rounded-lg p-4 bg-purple-50">
                    <h4 class="font-medium text-purple-900 mb-2">Monitoramento Contínuo</h4>
                    <p class="text-sm text-purple-700 mb-3">Acompanhar performance vs previsões</p>
                    <ul class="text-xs text-purple-600 space-y-1">
                        <li>• Dashboard de acompanhamento</li>
                        <li>• Alertas de desvio</li>
                        <li>• Ajuste de modelos</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js" integrity="sha384-fQybjgWLrvvRgtW5LEVLyXCoLtdnb2YIQnikzDOWEI1rExsAa+dxLdEaQCyUoRiY" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js" integrity="sha384-uQFxMHarLhfLGc1h8zG1Lw1s7q0V8RgJgFQHrOKj7eFoE+U8kADXIhOhyOFNYWFL" crossorigin="anonymous"></script>

<script>
    // Global chart configurations
    Chart.defaults.font.family = 'Inter, system-ui, sans-serif';
    Chart.defaults.color = '#6B7280';

    document.addEventListener('DOMContentLoaded', function() {
        console.log('Forecasting dashboard loaded');

        {% if forecast_results %}
        initializeForecastCharts();
        {% endif %}
    });

    function initializeForecastCharts() {
        // 1. Revenue Forecast Timeline
        createRevenueForecastChart();

        // 2. Growth Potential Distribution
        createGrowthPotentialChart();

        // 3. Top Classes by Potential
        createTopClassesChart();

        // 4. Forecast Accuracy Metrics
        createAccuracyMetricsChart();
    }

    function createRevenueForecastChart() {
        const ctx = document.getElementById('revenueForecastChart');
        if (!ctx) return;

        const forecastData = {{ forecast_results | tojson if forecast_results else '{}' }};

        const labels = Object.keys(forecastData).map(months => `${months} meses`);
        const currentRevenue = Object.values(forecastData).map(data => data.total_current_revenue);
        const forecastedRevenue = Object.values(forecastData).map(data => data.total_forecasted_revenue);

        new Chart(ctx, {
            type: 'line',
            data: {
                labels,
                datasets: [{
                    label: 'Receita Atual',
                    data: currentRevenue,
                    borderColor: '#3B82F6',
                    backgroundColor: '#3B82F6',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4
                }, {
                    label: 'Receita Projetada',
                    data: forecastedRevenue,
                    borderColor: '#10B981',
                    backgroundColor: '#10B981',
                    borderWidth: 3,
                    borderDash: [5, 5],
                    fill: false,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Receita (R$)'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString();
                            }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Período de Previsão'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: 'Evolução da Receita - Atual vs Projetada',
                        font: { size: 16, weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: R$ ${context.parsed.y.toLocaleString()}`;
                            }
                        }
                    }
                }
            }
        });
    }

    function createGrowthPotentialChart() {
        const ctx = document.getElementById('growthPotentialChart');
        if (!ctx) return;

        const forecastData = {{ forecast_results | tojson if forecast_results else '{}' }};

        const labels = Object.keys(forecastData).map(months => `${months}m`);
        const growthPotential = Object.values(forecastData).map(data => data.total_growth_potential);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels,
                datasets: [{
                    label: 'Potencial de Crescimento',
                    data: growthPotential,
                    backgroundColor: growthPotential.map(value => {
                        if (value > 50000) return '#10B981'; // Green for high growth
                        if (value > 20000) return '#F59E0B'; // Yellow for moderate growth
                        return '#3B82F6'; // Blue for low growth
                    }),
                    borderColor: '#374151',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Potencial de Crescimento (R$)'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString();
                            }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Período'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Potencial de Crescimento por Período',
                        font: { size: 16, weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Crescimento: R$ ${context.parsed.y.toLocaleString()}`;
                            }
                        }
                    }
                }
            }
        });
    }

    function createTopClassesChart() {
        const ctx = document.getElementById('topClassesChart');
        if (!ctx) return;

        const forecastData = {{ forecast_results | tojson if forecast_results else '{}' }};

        // Get 6-month forecast data for top classes
        const sixMonthData = forecastData['6'];
        if (!sixMonthData || !sixMonthData.revenue_forecasts) return;

        const topClasses = sixMonthData.revenue_forecasts
            .sort((a, b) => b.revenue_growth_potential - a.revenue_growth_potential)
            .slice(0, 10);

        new Chart(ctx, {
            type: 'horizontalBar',
            data: {
                labels: topClasses.map(c => c.turma.length > 15 ? c.turma.substring(0, 15) + '...' : c.turma),
                datasets: [{
                    label: 'Potencial de Crescimento',
                    data: topClasses.map(c => c.revenue_growth_potential),
                    backgroundColor: '#8B5CF6',
                    borderColor: '#7C3AED',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                scales: {
                    x: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Potencial de Crescimento (R$)'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Top 10 Turmas por Potencial de Crescimento',
                        font: { size: 16, weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const classData = topClasses[context.dataIndex];
                                return [
                                    `Turma: ${classData.turma}`,
                                    `Crescimento: R$ ${context.parsed.x.toLocaleString()}`,
                                    `Receita Atual: R$ ${classData.total_revenue.toLocaleString()}`
                                ];
                            }
                        }
                    }
                }
            }
        });
    }

    function createAccuracyMetricsChart() {
        const ctx = document.getElementById('accuracyMetricsChart');
        if (!ctx) return;

        const forecastData = {{ forecast_results | tojson if forecast_results else '{}' }};
        const potentialData = {{ potential_prediction | tojson if potential_prediction and 'error' not in potential_prediction else '{}' }};

        const labels = ['Acurácia do Modelo de Receita', 'Acurácia do Modelo de Potencial'];
        const scores = [
            Object.values(forecastData)[0]?.model_score * 100 || 0,
            potentialData.model_accuracy * 100 || 0
        ];

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels,
                datasets: [{
                    data: scores,
                    backgroundColor: ['#3B82F6', '#10B981'],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    title: {
                        display: true,
                        text: 'Acurácia dos Modelos de ML',
                        font: { size: 16, weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.parsed.toFixed(1)}%`;
                            }
                        }
                    }
                }
            }
        });
    }
</script>
{% endblock %}
