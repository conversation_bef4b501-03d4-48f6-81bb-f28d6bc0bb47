{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table %}

{% block title %}Segmentação de Turmas - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Segmentação de Turmas",
    subtitle="Análise avançada de clustering com Machine Learning para identificar grupos de turmas com características similares.",
    stats=[
        {
            "label": "Algoritmo",
            "value": "K-Means",
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />'
        },
        {
            "label": "Clusters Testados",
            "value": segmentation_results|length,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />'
        },
        {
            "label": "Melhor Score",
            "value": "%.3f"|format(segmentation_results.values()|map(attribute='silhouette_score')|max if segmentation_results else 0),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />'
        }
    ]
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Navigation -->
    <div class="mb-8">
        <nav class="flex space-x-8" aria-label="Tabs">
            <a href="/commercial-intelligence/" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                Visão Geral
            </a>
            <a href="#segmentation" class="tab-link active border-primary text-primary border-b-2 py-2 px-1 text-sm font-medium">
                Segmentação
            </a>
            <a href="/commercial-intelligence/forecasting" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                Previsões
            </a>
            <a href="/commercial-intelligence/risk-analysis" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                Análise de Risco
            </a>
        </nav>
    </div>

    {% if segmentation_results %}
    <!-- Cluster Comparison -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">📊 Comparação de Clusters</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
            {% for n_clusters, result in segmentation_results.items() %}
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div class="text-center mb-4">
                    <h4 class="text-lg font-semibold text-gray-900">{{ n_clusters|e }} Clusters</h4>
                    <div class="text-sm text-gray-500">Silhouette Score</div>
                    <div class="text-2xl font-bold text-blue-600">{{ "%.3f"|format(result.silhouette_score) }}</div>
                </div>

                <div class="space-y-2 text-sm">
                    {% for cluster in result.clusters %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Cluster {{ cluster.cluster_id + 1|e }}:</span>
                        <span class="font-medium">{{ cluster.size|e }} turmas</span>
                    </div>
                    {% endfor %}
                </div>

                <button onclick="showClusterDetails({{ n_clusters|e }})"
                        class="mt-4 w-full bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-100 transition-colors">
                    Ver Detalhes
                </button>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Visualization Charts -->
    {% set best_result = segmentation_results.values()|list|sort(attribute='silhouette_score', reverse=true)|first %}
    {% if best_result %}
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">📊 Visualização dos Clusters</h3>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Cluster Size Distribution -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="h-80">
                    <canvas id="clusterSizeChart"></canvas>
                </div>
            </div>

            <!-- Revenue vs Conversion Scatter -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="h-80">
                    <canvas id="revenueConversionChart"></canvas>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Cluster Characteristics Radar -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="h-80">
                    <canvas id="clusterRadarChart"></canvas>
                </div>
            </div>

            <!-- Silhouette Score Comparison -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="h-80">
                    <canvas id="silhouetteChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Best Segmentation Details -->
    <div id="cluster-details" class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">🏆 Melhor Segmentação ({{ best_result.n_clusters|e }} Clusters)</h3>

        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {% for cluster in best_result.clusters %}
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="font-semibold text-gray-900">Cluster {{ cluster.cluster_id + 1|e }}</h4>
                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                        {{ cluster.size|e }} turmas
                    </span>
                </div>

                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Receita Média:</span>
                        <span class="text-sm font-medium">R$ {{ "%.2f"|format(cluster.avg_revenue) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Conversão Média:</span>
                        <span class="text-sm font-medium">{{ "%.1f"|format(cluster.avg_conversion * 100) }}%</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Alunos Médios:</span>
                        <span class="text-sm font-medium">{{ "%.0f"|format(cluster.avg_students) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Churn Médio:</span>
                        <span class="text-sm font-medium">{{ "%.1f"|format(cluster.avg_churn * 100) }}%</span>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="text-sm text-gray-600 mb-2">Turmas neste cluster:</div>
                    <div class="max-h-32 overflow-y-auto">
                        {% for class_name in cluster.classes %}
                        <div class="text-xs bg-gray-50 px-2 py-1 rounded mb-1">{{ class_name|e }}</div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Cluster Characteristics -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">🎯 Características dos Clusters</h3>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cluster</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tamanho</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Receita Média</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conversão</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Alunos</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Perfil</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% if best_result %}
                    {% for cluster in best_result.clusters %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full bg-blue-{{ (cluster.cluster_id % 5 + 1) * 100|e }} mr-3"></div>
                                <span class="text-sm font-medium text-gray-900">Cluster {{ cluster.cluster_id + 1|e }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ cluster.size|e }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">R$ {{ "%.2f"|format(cluster.avg_revenue) }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ "%.1f"|format(cluster.avg_conversion * 100) }}%</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ "%.0f"|format(cluster.avg_students) }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {% if cluster.avg_revenue > 5000 and cluster.avg_conversion > 0.5 %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Alto Valor
                                </span>
                            {% elif cluster.avg_conversion > 0.7 %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Alta Conversão
                                </span>
                            {% elif cluster.avg_students > 50 %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    Alto Volume
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    Padrão
                                </span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Actions -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">🚀 Ações Recomendadas</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="p-4 border border-green-200 rounded-lg bg-green-50">
                <h4 class="font-medium text-green-900 mb-2">Clusters de Alto Valor</h4>
                <p class="text-sm text-green-700 mb-3">Focar em replicar estratégias bem-sucedidas</p>
                <button class="text-green-800 text-sm font-medium hover:underline">Ver Estratégias →</button>
            </div>
            <div class="p-4 border border-blue-200 rounded-lg bg-blue-50">
                <h4 class="font-medium text-blue-900 mb-2">Clusters de Alta Conversão</h4>
                <p class="text-sm text-blue-700 mb-3">Aumentar investimento em marketing</p>
                <button class="text-blue-800 text-sm font-medium hover:underline">Ver Oportunidades →</button>
            </div>
            <div class="p-4 border border-orange-200 rounded-lg bg-orange-50">
                <h4 class="font-medium text-orange-900 mb-2">Clusters de Baixo Desempenho</h4>
                <p class="text-sm text-orange-700 mb-3">Implementar melhorias urgentes</p>
                <button class="text-orange-800 text-sm font-medium hover:underline">Ver Plano de Ação →</button>
            </div>
        </div>
    </div>

    {% else %}
    <!-- No Data State -->
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">Nenhuma segmentação disponível</h3>
        <p class="mt-1 text-sm text-gray-500">Execute a análise de segmentação para ver os resultados.</p>
        <div class="mt-6">
            <button onclick="runSegmentation()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Executar Segmentação
            </button>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js" integrity="sha384-fQybjgWLrvvRgtW5LEVLyXCoLtdnb2YIQnikzDOWEI1rExsAa+dxLdEaQCyUoRiY" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

<script>
    // Global chart configurations
    Chart.defaults.font.family = 'Inter, system-ui, sans-serif';
    Chart.defaults.color = '#6B7280';

    // Color palette for clusters
    const clusterColors = [
        '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
        '#06B6D4', '#F97316', '#84CC16', '#EC4899', '#6366F1'
    ];

    function showClusterDetails(nClusters) {
        console.log('Showing details for', nClusters, 'clusters');
        // Scroll to the detailed view
        document.getElementById('cluster-details').scrollIntoView({ behavior: 'smooth' });
    }

    function runSegmentation() {
        console.log('Running segmentation...');
        window.location.reload();
    }

    // Initialize charts when page loads
    document.addEventListener('DOMContentLoaded', function() {
        {% if best_result %}
        initializeClusterCharts();
        {% endif %}
    });

    function initializeClusterCharts() {
        // 1. Cluster Size Distribution (Pie Chart)
        createClusterSizeChart();

        // 2. Revenue vs Conversion Scatter Plot
        createRevenueConversionScatter();

        // 3. Cluster Characteristics Radar Chart
        createClusterRadarChart();

        // 4. Silhouette Score Comparison
        createSilhouetteComparisonChart();
    }

    function createClusterSizeChart() {
        const ctx = document.getElementById('clusterSizeChart');
        if (!ctx) return;

        const clusterData = {{ best_result.clusters | tojson if best_result else '[]' }};

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: clusterData.map(c => `Cluster ${c.cluster_id + 1}`),
                datasets: [{
                    data: clusterData.map(c => c.size),
                    backgroundColor: clusterColors.slice(0, clusterData.length),
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    title: {
                        display: true,
                        text: 'Distribuição de Turmas por Cluster',
                        font: { size: 16, weight: 'bold' }
                    }
                }
            }
        });
    }

    function createRevenueConversionScatter() {
        const ctx = document.getElementById('revenueConversionChart');
        if (!ctx) return;

        const clusterData = {{ best_result.clusters | tojson if best_result else '[]' }};

        const datasets = clusterData.map((cluster, index) => ({
            label: `Cluster ${cluster.cluster_id + 1}`,
            data: [{
                x: cluster.avg_conversion * 100,
                y: cluster.avg_revenue,
                r: Math.sqrt(cluster.size) * 3 // Bubble size based on cluster size
            }],
            backgroundColor: clusterColors[index] + '80', // Add transparency
            borderColor: clusterColors[index],
            borderWidth: 2
        }));

        new Chart(ctx, {
            type: 'bubble',
            data: { datasets },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Taxa de Conversão (%)'
                        },
                        beginAtZero: true
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Receita Média (R$)'
                        },
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: 'Receita vs Conversão por Cluster',
                        font: { size: 16, weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const cluster = clusterData[context.datasetIndex];
                                return [
                                    `${context.dataset.label}`,
                                    `Conversão: ${context.parsed.x.toFixed(1)}%`,
                                    `Receita: R$ ${context.parsed.y.toLocaleString()}`,
                                    `Turmas: ${cluster.size}`
                                ];
                            }
                        }
                    }
                }
            }
        });
    }

    function createClusterRadarChart() {
        const ctx = document.getElementById('clusterRadarChart');
        if (!ctx) return;

        const clusterData = {{ best_result.clusters | tojson if best_result else '[]' }};

        // Normalize data for radar chart (0-100 scale)
        const maxValues = {
            revenue: Math.max(...clusterData.map(c => c.avg_revenue)),
            conversion: 1, // Already in 0-1 scale
            students: Math.max(...clusterData.map(c => c.avg_students)),
            churn: 1 // Already in 0-1 scale (inverted for display)
        };

        const datasets = clusterData.slice(0, 3).map((cluster, index) => ({
            label: `Cluster ${cluster.cluster_id + 1}`,
            data: [
                (cluster.avg_revenue / maxValues.revenue) * 100,
                cluster.avg_conversion * 100,
                (cluster.avg_students / maxValues.students) * 100,
                (1 - cluster.avg_churn) * 100 // Inverted churn (higher is better)
            ],
            backgroundColor: clusterColors[index] + '20',
            borderColor: clusterColors[index],
            borderWidth: 2,
            pointBackgroundColor: clusterColors[index]
        }));

        new Chart(ctx, {
            type: 'radar',
            data: {
                labels: ['Receita', 'Conversão', 'Alunos', 'Retenção'],
                datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            stepSize: 20
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: 'Características dos Principais Clusters',
                        font: { size: 16, weight: 'bold' }
                    }
                }
            }
        });
    }

    function createSilhouetteComparisonChart() {
        const ctx = document.getElementById('silhouetteChart');
        if (!ctx) return;

        const segmentationData = {{ segmentation_results | tojson if segmentation_results else '{}' }};

        const labels = Object.keys(segmentationData).map(k => `${k} Clusters`);
        const scores = Object.values(segmentationData).map(v => v.silhouette_score);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels,
                datasets: [{
                    label: 'Silhouette Score',
                    data: scores,
                    backgroundColor: scores.map((score, index) => {
                        // Color based on score quality
                        if (score > 0.5) return '#10B981'; // Green for good
                        if (score > 0.3) return '#F59E0B'; // Yellow for moderate
                        return '#EF4444'; // Red for poor
                    }),
                    borderColor: '#374151',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 1,
                        title: {
                            display: true,
                            text: 'Silhouette Score'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Número de Clusters'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Qualidade da Segmentação (Silhouette Score)',
                        font: { size: 16, weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const score = context.parsed.y;
                                let quality = 'Ruim';
                                if (score > 0.5) quality = 'Boa';
                                else if (score > 0.3) quality = 'Moderada';

                                return [
                                    `Score: ${score.toFixed(3)}`,
                                    `Qualidade: ${quality}`
                                ];
                            }
                        }
                    }
                }
            }
        });
    }
</script>
{% endblock %}
