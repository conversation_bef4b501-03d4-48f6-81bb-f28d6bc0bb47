{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, data_table %}

{% block title %}Análise de Risco - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Análise de Risco",
    subtitle="Sistema de alerta precoce para identificação de turmas em risco de churn e baixo desempenho.",
    stats=[
        {
            "label": "Risco Médio",
            "value": "%.1f%%"|format((churn_analysis.get('avg_churn_risk', 0) * 100) if churn_analysis and 'error' not in churn_analysis else 0),
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />'
        },
        {
            "label": "Alto Risco",
            "value": churn_analysis.get('high_risk_classes', [])|length if churn_analysis and 'error' not in churn_analysis else 0,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />'
        },
        {
            "label": "Turmas Monitoradas",
            "value": churn_analysis.get('churn_analysis', [])|length if churn_analysis and 'error' not in churn_analysis else 0,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />'
        }
    ]
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Navigation -->
    <div class="mb-8">
        <nav class="flex space-x-8" aria-label="Tabs">
            <a href="/commercial-intelligence/" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                Visão Geral
            </a>
            <a href="/commercial-intelligence/segmentation" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                Segmentação
            </a>
            <a href="/commercial-intelligence/forecasting" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                Previsões
            </a>
            <a href="#risk-analysis" class="tab-link active border-primary text-primary border-b-2 py-2 px-1 text-sm font-medium">
                Análise de Risco
            </a>
        </nav>
    </div>

    {% if churn_analysis and 'error' not in churn_analysis %}
    <!-- Risk Distribution -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        {% for risk_level, count in churn_analysis.get('risk_distribution', {}).items() %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8
                        {% if risk_level == 'Baixo' %}bg-green-100{% elif risk_level == 'Médio' %}bg-yellow-100{% elif risk_level == 'Alto' %}bg-orange-100{% else %}bg-red-100{% endif %}
                        rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5
                            {% if risk_level == 'Baixo' %}text-green-600{% elif risk_level == 'Médio' %}text-yellow-600{% elif risk_level == 'Alto' %}text-orange-600{% else %}text-red-600{% endif %}"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            {% if risk_level == 'Baixo' %}
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            {% elif risk_level == 'Médio' %}
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            {% else %}
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            {% endif %}
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Risco {{ risk_level|e }}</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ count|e }} turmas</dd>
                    </dl>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- High Risk Classes Alert -->
    {% if churn_analysis.get('high_risk_classes', []) %}
    <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">⚠️ Alerta: Turmas de Alto Risco</h3>
                <div class="mt-2 text-sm text-red-700">
                    <p>{{ churn_analysis.get('high_risk_classes', [])|length }} turmas foram identificadas com alto risco de churn. Ação imediata recomendada.</p>
                </div>
                <div class="mt-4">
                    <div class="flex flex-wrap gap-2">
                        {% for class_name in churn_analysis.get('high_risk_classes', [])[:10] %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            {{ class_name|e }}
                        </span>
                        {% endfor %}
                        {% if churn_analysis.get('high_risk_classes', [])|length > 10 %}
                        <span class="text-xs text-red-600">+{{ churn_analysis.get('high_risk_classes', [])|length - 10 }} mais</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Detailed Risk Analysis Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">📊 Análise Detalhada de Risco</h3>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Turma</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Taxa de Churn</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score de Risco</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nível de Risco</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for analysis in churn_analysis.get('churn_analysis', [])[:20] %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ analysis.turma|e }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ "%.1f%%"|format(analysis.churn_rate * 100) }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ "%.1f%%"|format(analysis.churn_risk_score * 100) }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if analysis.risk_level == 'Baixo' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Baixo
                            </span>
                            {% elif analysis.risk_level == 'Médio' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Médio
                            </span>
                            {% elif analysis.risk_level == 'Alto' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                Alto
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Crítico
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <button onclick="showRiskDetails('{{ analysis.turma|e }}')" class="text-blue-600 hover:text-blue-900">
                                Ver Detalhes
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Recommendations -->
    {% if churn_analysis.get('recommendations', []) %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">💡 Recomendações de Ação</h3>
        </div>

        <div class="p-6">
            <div class="space-y-6">
                {% for recommendation in churn_analysis.get('recommendations', []) %}
                <div class="border border-orange-200 rounded-lg p-4 bg-orange-50">
                    <h4 class="font-medium text-orange-900 mb-3">{{ recommendation.turma|e }}</h4>
                    <ul class="space-y-2">
                        {% for action in recommendation.actions %}
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-orange-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="text-sm text-orange-800">{{ action|e }}</span>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Risk Visualization Charts -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">📊 Visualização de Riscos</h3>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Risk Distribution Pie Chart -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="h-80">
                        <canvas id="riskDistributionChart"></canvas>
                    </div>
                </div>

                <!-- Risk Score vs Churn Rate Scatter -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="h-80">
                        <canvas id="riskScatterChart"></canvas>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Risk Factors Radar -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="h-80">
                        <canvas id="riskFactorsChart"></canvas>
                    </div>
                </div>

                <!-- High Risk Classes Timeline -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="h-80">
                        <canvas id="highRiskTimelineChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Risk Factors Analysis -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">🔍 Fatores de Risco Identificados</h3>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="border border-red-200 rounded-lg p-4 bg-red-50">
                    <h4 class="font-medium text-red-900 mb-2">Alta Taxa de Churn</h4>
                    <p class="text-sm text-red-700 mb-3">Turmas com churn > 30%</p>
                    <div class="text-2xl font-bold text-red-600">
                        {{ churn_analysis.get('churn_analysis', [])|selectattr('churn_rate', '>', 0.3)|list|length }}
                    </div>
                    <div class="text-xs text-red-600">turmas afetadas</div>
                </div>

                <div class="border border-orange-200 rounded-lg p-4 bg-orange-50">
                    <h4 class="font-medium text-orange-900 mb-2">Baixa Conversão</h4>
                    <p class="text-sm text-orange-700 mb-3">Conversão < 50%</p>
                    <div class="text-2xl font-bold text-orange-600">
                        {{ commercial_index.get('class_rankings', [])|selectattr('conversion_rate', '<', 0.5)|list|length if commercial_index and 'error' not in commercial_index else 0 }}
                    </div>
                    <div class="text-xs text-orange-600">turmas afetadas</div>
                </div>

                <div class="border border-yellow-200 rounded-lg p-4 bg-yellow-50">
                    <h4 class="font-medium text-yellow-900 mb-2">Baixa Atividade</h4>
                    <p class="text-sm text-yellow-700 mb-3">Score de atividade < 50%</p>
                    <div class="text-2xl font-bold text-yellow-600">
                        {{ commercial_index.get('class_rankings', [])|selectattr('activity_score', '<', 0.5)|list|length if commercial_index and 'error' not in commercial_index else 0 }}
                    </div>
                    <div class="text-xs text-yellow-600">turmas afetadas</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Plan -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">📋 Plano de Ação Estratégico</h3>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="border border-blue-200 rounded-lg p-4 bg-blue-50">
                    <h4 class="font-medium text-blue-900 mb-2">Ação Imediata (0-30 dias)</h4>
                    <ul class="text-sm text-blue-700 space-y-2">
                        <li>• Contato direto com turmas de alto risco</li>
                        <li>• Implementar programa de retenção</li>
                        <li>• Revisar processo de onboarding</li>
                        <li>• Análise de satisfação do cliente</li>
                    </ul>
                </div>

                <div class="border border-green-200 rounded-lg p-4 bg-green-50">
                    <h4 class="font-medium text-green-900 mb-2">Ação de Médio Prazo (30-90 dias)</h4>
                    <ul class="text-sm text-green-700 space-y-2">
                        <li>• Otimizar processo de conversão</li>
                        <li>• Implementar sistema de alertas</li>
                        <li>• Treinamento da equipe comercial</li>
                        <li>• Melhorar comunicação com clientes</li>
                    </ul>
                </div>

                <div class="border border-purple-200 rounded-lg p-4 bg-purple-50">
                    <h4 class="font-medium text-purple-900 mb-2">Estratégia de Longo Prazo (90+ dias)</h4>
                    <ul class="text-sm text-purple-700 space-y-2">
                        <li>• Desenvolver modelo preditivo avançado</li>
                        <li>• Implementar automação de marketing</li>
                        <li>• Criar programa de fidelização</li>
                        <li>• Monitoramento contínuo de KPIs</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    {% else %}
    <!-- No Data State -->
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">Análise de risco não disponível</h3>
        <p class="mt-1 text-sm text-gray-500">Execute a análise de risco para identificar turmas em perigo.</p>
        <div class="mt-6">
            <button onclick="runRiskAnalysis()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Executar Análise de Risco
            </button>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js" integrity="sha384-fQybjgWLrvvRgtW5LEVLyXCoLtdnb2YIQnikzDOWEI1rExsAa+dxLdEaQCyUoRiY" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

<script>
    // Global chart configurations
    Chart.defaults.font.family = 'Inter, system-ui, sans-serif';
    Chart.defaults.color = '#6B7280';

    function showRiskDetails(className) {
        console.log('Showing risk details for:', className);
        // Implementation for showing detailed risk analysis
        // You can add modal or redirect logic here
    }

    function runRiskAnalysis() {
        console.log('Running risk analysis...');
        window.location.reload();
    }

    document.addEventListener('DOMContentLoaded', function() {
        {% if churn_analysis and 'error' not in churn_analysis %}
        initializeRiskCharts();
        {% endif %}
    });

    function initializeRiskCharts() {
        // 1. Risk Distribution Pie Chart
        createRiskDistributionChart();

        // 2. Risk Score vs Churn Rate Scatter
        createRiskScatterChart();

        // 3. Risk Factors Radar Chart
        createRiskFactorsChart();

        // 4. High Risk Classes Timeline
        createHighRiskTimelineChart();
    }

    function createRiskDistributionChart() {
        const ctx = document.getElementById('riskDistributionChart');
        if (!ctx) return;

        const riskDistribution = {{ churn_analysis.get('risk_distribution', {}) | tojson if churn_analysis and 'error' not in churn_analysis else '{}' }};

        const labels = Object.keys(riskDistribution);
        const data = Object.values(riskDistribution);
        const colors = {
            'Baixo': '#10B981',
            'Médio': '#F59E0B',
            'Alto': '#F97316',
            'Crítico': '#EF4444'
        };

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels,
                datasets: [{
                    data,
                    backgroundColor: labels.map(label => colors[label] || '#6B7280'),
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    title: {
                        display: true,
                        text: 'Distribuição de Níveis de Risco',
                        font: { size: 16, weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} turmas (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    function createRiskScatterChart() {
        const ctx = document.getElementById('riskScatterChart');
        if (!ctx) return;

        const churnData = {{ churn_analysis.get('churn_analysis', []) | tojson if churn_analysis and 'error' not in churn_analysis else '[]' }};

        // Group data by risk level
        const riskGroups = {
            'Baixo': { data: [], color: '#10B981' },
            'Médio': { data: [], color: '#F59E0B' },
            'Alto': { data: [], color: '#F97316' },
            'Crítico': { data: [], color: '#EF4444' }
        };

        churnData.forEach(item => {
            const riskLevel = item.risk_level || 'Baixo';
            if (riskGroups[riskLevel]) {
                riskGroups[riskLevel].data.push({
                    x: item.churn_rate * 100,
                    y: item.churn_risk_score * 100,
                    label: item.turma
                });
            }
        });

        const datasets = Object.entries(riskGroups).map(([level, group]) => ({
            label: `Risco ${level}`,
            data: group.data,
            backgroundColor: group.color + '80',
            borderColor: group.color,
            borderWidth: 2
        }));

        new Chart(ctx, {
            type: 'scatter',
            data: { datasets },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Taxa de Churn (%)'
                        },
                        beginAtZero: true
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Score de Risco (%)'
                        },
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: 'Relação entre Churn e Score de Risco',
                        font: { size: 16, weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const point = context.raw;
                                return [
                                    `Turma: ${point.label}`,
                                    `Churn: ${context.parsed.x.toFixed(1)}%`,
                                    `Risco: ${context.parsed.y.toFixed(1)}%`
                                ];
                            }
                        }
                    }
                }
            }
        });
    }

    function createRiskFactorsChart() {
        const ctx = document.getElementById('riskFactorsChart');
        if (!ctx) return;

        const churnData = {{ churn_analysis.get('churn_analysis', []) | tojson if churn_analysis and 'error' not in churn_analysis else '[]' }};
        const commercialData = {{ commercial_index.get('class_rankings', []) | tojson if commercial_index and 'error' not in commercial_index else '[]' }};

        // Calculate average risk factors
        const avgChurnRate = churnData.reduce((sum, item) => sum + (item.churn_rate || 0), 0) / churnData.length * 100;
        const avgRiskScore = churnData.reduce((sum, item) => sum + (item.churn_risk_score || 0), 0) / churnData.length * 100;

        // Calculate conversion and activity averages from commercial data
        const avgConversion = commercialData.reduce((sum, item) => sum + (item.conversion_rate || 0), 0) / commercialData.length * 100;
        const avgActivity = commercialData.reduce((sum, item) => sum + (item.activity_score || 0), 0) / commercialData.length * 100;

        new Chart(ctx, {
            type: 'radar',
            data: {
                labels: ['Taxa de Churn', 'Score de Risco', 'Baixa Conversão', 'Baixa Atividade'],
                datasets: [{
                    label: 'Fatores de Risco Médios',
                    data: [
                        avgChurnRate,
                        avgRiskScore,
                        100 - avgConversion, // Inverted (higher is worse)
                        100 - avgActivity    // Inverted (higher is worse)
                    ],
                    backgroundColor: '#EF444420',
                    borderColor: '#EF4444',
                    borderWidth: 2,
                    pointBackgroundColor: '#EF4444'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            stepSize: 20
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: 'Perfil de Fatores de Risco',
                        font: { size: 16, weight: 'bold' }
                    }
                }
            }
        });
    }

    function createHighRiskTimelineChart() {
        const ctx = document.getElementById('highRiskTimelineChart');
        if (!ctx) return;

        const highRiskClasses = {{ churn_analysis.get('high_risk_classes', []) | tojson if churn_analysis and 'error' not in churn_analysis else '[]' }};
        const churnData = {{ churn_analysis.get('churn_analysis', []) | tojson if churn_analysis and 'error' not in churn_analysis else '[]' }};

        // Get top 10 highest risk classes
        const topRiskClasses = churnData
            .filter(item => highRiskClasses.includes(item.turma))
            .sort((a, b) => b.churn_risk_score - a.churn_risk_score)
            .slice(0, 10);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: topRiskClasses.map(item =>
                    item.turma.length > 15 ? item.turma.substring(0, 15) + '...' : item.turma
                ),
                datasets: [{
                    label: 'Score de Risco',
                    data: topRiskClasses.map(item => item.churn_risk_score * 100),
                    backgroundColor: topRiskClasses.map(item => {
                        const score = item.churn_risk_score;
                        if (score > 0.8) return '#DC2626'; // Dark red for critical
                        if (score > 0.6) return '#EF4444'; // Red for high
                        if (score > 0.4) return '#F97316'; // Orange for moderate
                        return '#F59E0B'; // Yellow for low
                    }),
                    borderColor: '#374151',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Score de Risco (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Turmas de Alto Risco'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Top 10 Turmas de Maior Risco',
                        font: { size: 16, weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const classData = topRiskClasses[context.dataIndex];
                                return [
                                    `Turma: ${classData.turma}`,
                                    `Score de Risco: ${context.parsed.y.toFixed(1)}%`,
                                    `Taxa de Churn: ${(classData.churn_rate * 100).toFixed(1)}%`
                                ];
                            }
                        }
                    }
                }
            }
        });
    }
</script>
{% endblock %}
