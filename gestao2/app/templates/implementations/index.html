{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, table_filters, data_table, business_tooltip %}

{% block title %}Implantações - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Gestão de Implantações",
    subtitle="Acompanhe projetos em implementação, monitore o progresso e garanta a entrega de valor ao cliente.",
    stats=[
        {
            "label": "Total de Implantações",
            "value": total_implementations,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />'
        },
        {
            "label": "Implantações Ativas",
            "value": active_implementations,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />'
        },
        {
            "label": "Implantações Finalizadas",
            "value": finalized_implementations,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />'
        }
    ],
    bg_class="bg-gray-100"
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- KPI: Total de Implantações -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-primary-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Total de Implantações</p>
                        {{ business_tooltip(
                            title="Total de Implantações",
                            description="Número total de projetos de implementação criados no sistema, incluindo ativos, finalizados e cancelados.",
                            formula="COUNT(DISTINCT Oportunidade_id WHERE Data_Criacao_Implantacao IS NOT NULL)",
                            columns="Oportunidade_id, Data_Criacao_Implantacao",
                            data_source="base_dados.csv → Contagem de registros únicos com data de criação de implementação",
                            calculation_method="Contagem simples de todos os registros que possuem data de criação de implementação"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ total_implementations|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Projetos em implementação</p>
        </div>

        <!-- KPI: Implantações Ativas -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-primary-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Implantações Ativas</p>
                        {{ business_tooltip(
                            title="Implantações Ativas",
                            description="Implementações que estão em andamento, ou seja, não foram finalizadas nem canceladas.",
                            formula="COUNT(Status_Implantacao WHERE Status_Implantacao NOT IN ('Finalizado', 'Cancelado'))",
                            columns="Status_Implantacao",
                            data_source="base_dados.csv → Contagem de implementações com status diferente de 'Finalizado' e 'Cancelado'",
                            calculation_method="Contagem de registros onde o status da implementação não é 'Finalizado' nem 'Cancelado'"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ active_implementations|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Em andamento</p>
        </div>

        <!-- KPI: Implantações Finalizadas -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Implantações Finalizadas</p>
                        {{ business_tooltip(
                            title="Implantações Finalizadas",
                            description="Implementações que foram concluídas com sucesso e estão prontas para gerar receita recorrente.",
                            formula="COUNT(Status_Implantacao WHERE Status_Implantacao = 'Finalizado')",
                            columns="Status_Implantacao",
                            data_source="base_dados.csv → Contagem de implementações com status 'Finalizado'",
                            calculation_method="Contagem simples de registros onde o status da implementação é 'Finalizado'"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ finalized_implementations|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Gerando receita recorrente</p>
        </div>

        <!-- KPI: MRR Total -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">MRR Total</p>
                        {{ business_tooltip(
                            title="MRR Total das Implementações",
                            description="Receita mensal recorrente total gerada pelas implementações finalizadas, representando o valor mensal garantido.",
                            formula="SUM(Valor_Mensalidade WHERE Status_Implantacao = 'Finalizado')",
                            columns="Valor_Mensalidade, Status_Implantacao",
                            data_source="base_dados.csv → Soma dos valores de mensalidade para implementações finalizadas",
                            calculation_method="Soma de todos os valores de mensalidade das implementações com status 'Finalizado'"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ mrr_total|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Receita mensal recorrente</p>
        </div>
    </div>

    <!-- Additional Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Metric: Receita Potencial -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-100 card-hover transition-all duration-300 h-full">
            <div class="flex items-center mb-3">
                <div class="w-12 h-12 rounded-full bg-primary-50 flex items-center justify-center mr-3">
                    <svg class="w-6 h-6 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Receita Potencial</p>
                        {{ business_tooltip(
                            title="Receita Potencial",
                            description="Receita mensal estimada que será gerada quando todas as implementações ativas forem finalizadas.",
                            formula="SUM(Valor_Mensalidade WHERE Status_Implantacao != 'Finalizado' AND Status_Implantacao != 'Cancelado')",
                            columns="Valor_Mensalidade, Status_Implantacao",
                            data_source="base_dados.csv → Soma dos valores de mensalidade para implementações ativas",
                            calculation_method="Soma de todos os valores de mensalidade das implementações que ainda não foram finalizadas nem canceladas"
                        ) }}
                    </div>
                    <p class="text-2xl font-semibold text-gray-900">{{ potential_revenue }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500">Implantações ativas</p>
        </div>

        <!-- Metric: Taxa de Finalização -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-100 card-hover transition-all duration-300 h-full">
            <div class="flex items-center mb-3">
                <div class="w-12 h-12 rounded-full bg-success-50 flex items-center justify-center mr-3">
                    <svg class="w-6 h-6 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Taxa de Finalização</p>
                        {{ business_tooltip(
                            title="Taxa de Finalização",
                            description="Percentual de implementações que foram finalizadas com sucesso em relação ao total de implementações iniciadas.",
                            formula="(Implementações Finalizadas ÷ Total de Implementações) × 100",
                            columns="Status_Implantacao",
                            data_source="base_dados.csv → Divisão de implementações finalizadas pelo total",
                            calculation_method="Divisão do número de implementações finalizadas pelo total de implementações, multiplicado por 100"
                        ) }}
                    </div>
                    <p class="text-2xl font-semibold text-gray-900">{{ completion_rate|string + "%" if completion_rate else "0%" }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500">Implantações finalizadas</p>
        </div>

        <!-- Metric: Tempo Médio de Implantação -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-100 card-hover transition-all duration-300 h-full">
            <div class="flex items-center mb-3">
                <div class="w-12 h-12 rounded-full bg-primary-50 flex items-center justify-center mr-3">
                    <svg class="w-6 h-6 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Tempo Médio</p>
                        {{ business_tooltip(
                            title="Tempo Médio de Implementação",
                            description="Duração média em dias para completar uma implementação, calculada desde a data de criação até a finalização.",
                            formula="AVG(Data_Finalizacao - Data_Criacao_Implantacao) WHERE Status_Implantacao = 'Finalizado'",
                            columns="Data_Criacao_Implantacao, Data_Finalizacao, Status_Implantacao",
                            data_source="base_dados.csv → Cálculo da diferença de datas para implementações finalizadas",
                            calculation_method="Média aritmética da diferença em dias entre data de criação e finalização das implementações"
                        ) }}
                    </div>
                    <p class="text-2xl font-semibold text-gray-900">{{ avg_implementation_time|string + " dias" if avg_implementation_time else "N/A" }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500">Duração da implantação</p>
        </div>

        <!-- Metric: Receita Média por Implantação -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-100 card-hover transition-all duration-300 h-full">
            <div class="flex items-center mb-3">
                <div class="w-12 h-12 rounded-full bg-success-50 flex items-center justify-center mr-3">
                    <svg class="w-6 h-6 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Receita Média</p>
                        {{ business_tooltip(
                            title="Receita Média por Implementação",
                            description="Valor médio de mensalidade gerado por cada implementação finalizada, indicando o ticket médio dos projetos.",
                            formula="AVG(Valor_Mensalidade WHERE Status_Implantacao = 'Finalizado')",
                            columns="Valor_Mensalidade, Status_Implantacao",
                            data_source="base_dados.csv → Média dos valores de mensalidade para implementações finalizadas",
                            calculation_method="Média aritmética dos valores de mensalidade das implementações com status 'Finalizado'"
                        ) }}
                    </div>
                    <p class="text-2xl font-semibold text-gray-900">{{ avg_revenue_per_implementation }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500">Por implantação finalizada</p>
        </div>
    </div>

    <!-- Efficiency Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Metric: Eficiência de Implantação -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-100 card-hover transition-all duration-300 h-full">
            <div class="flex items-center mb-3">
                <div class="w-12 h-12 rounded-full bg-primary-50 flex items-center justify-center mr-3">
                    <svg class="w-6 h-6 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Eficiência</p>
                        {{ business_tooltip(
                            title="Eficiência de Implementação",
                            description="Métrica composta que relaciona tempo médio de implementação com taxa de conclusão, indicando a eficiência operacional.",
                            formula="(Taxa_Finalizacao × 100) ÷ Tempo_Medio_Implementacao",
                            columns="Status_Implantacao, Data_Criacao_Implantacao, Data_Finalizacao",
                            data_source="base_dados.csv → Cálculo baseado em taxa de finalização e tempo médio",
                            calculation_method="Divisão da taxa de finalização pelo tempo médio, normalizada para escala percentual"
                        ) }}
                    </div>
                    <p class="text-2xl font-semibold text-gray-900">{{ implementation_efficiency|string + "%" if implementation_efficiency else "0%" }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500">Tempo vs. Taxa de Conclusão</p>
        </div>

        <!-- Metric: Implantações por Produto -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-100 card-hover transition-all duration-300 h-full">
            <div class="flex items-center mb-3">
                <div class="w-12 h-12 rounded-full bg-primary-50 flex items-center justify-center mr-3">
                    <svg class="w-6 h-6 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Produtos</p>
                        {{ business_tooltip(
                            title="Tipos de Produtos Implementados",
                            description="Número de diferentes tipos de produtos que foram implementados, mostrando a diversidade do portfólio.",
                            formula="COUNT(DISTINCT Produto WHERE Data_Criacao_Implantacao IS NOT NULL)",
                            columns="Produto, Data_Criacao_Implantacao",
                            data_source="base_dados.csv → Contagem de produtos únicos com implementações",
                            calculation_method="Contagem de valores únicos na coluna Produto para registros com implementação"
                        ) }}
                    </div>
                    <p class="text-2xl font-semibold text-gray-900">{{ product_distribution|length|string if product_distribution else "0" }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500">Tipos de produtos implantados</p>
        </div>

        <!-- Metric: Universidades Atendidas -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-100 card-hover transition-all duration-300 h-full">
            <div class="flex items-center mb-3">
                <div class="w-12 h-12 rounded-full bg-primary-50 flex items-center justify-center mr-3">
                    <svg class="w-6 h-6 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Universidades</p>
                        {{ business_tooltip(
                            title="Universidades Atendidas",
                            description="Número de universidades diferentes que possuem implementações, indicando a penetração geográfica e institucional.",
                            formula="COUNT(DISTINCT Universidade WHERE Data_Criacao_Implantacao IS NOT NULL)",
                            columns="Universidade, Data_Criacao_Implantacao",
                            data_source="base_dados.csv → Contagem de universidades únicas com implementações",
                            calculation_method="Contagem de valores únicos na coluna Universidade para registros com implementação"
                        ) }}
                    </div>
                    <p class="text-2xl font-semibold text-gray-900">{{ university_distribution|length|string if university_distribution else "0" }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500">Instituições atendidas</p>
        </div>

        <!-- Metric: Responsáveis -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-100 card-hover transition-all duration-300 h-full">
            <div class="flex items-center mb-3">
                <div class="w-12 h-12 rounded-full bg-primary-50 flex items-center justify-center mr-3">
                    <svg class="w-6 h-6 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Responsáveis</p>
                        {{ business_tooltip(
                            title="Equipe de Implementação",
                            description="Número de responsáveis diferentes envolvidos nas implementações, indicando o tamanho da equipe de onboarding.",
                            formula="COUNT(DISTINCT ResponsableOnboarding WHERE Data_Criacao_Implantacao IS NOT NULL)",
                            columns="ResponsableOnboarding, Data_Criacao_Implantacao",
                            data_source="base_dados.csv → Contagem de responsáveis únicos com implementações",
                            calculation_method="Contagem de valores únicos na coluna ResponsableOnboarding para registros com implementação"
                        ) }}
                    </div>
                    <p class="text-2xl font-semibold text-gray-900">{{ responsible_data|length|string if responsible_data else "0" }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500">Equipe de implantação</p>
        </div>
    </div>

    <!-- Recent and Upcoming Implementations -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Recent Implementations -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <h3 class="text-base font-medium text-gray-900 mb-1">Implantações Recentes</h3>
            <p class="text-sm text-gray-500 mb-4">Últimas 5 implantações iniciadas</p>

            {% if recent_implementations %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Lead</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Produto</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Data</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for impl in recent_implementations %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-2 text-sm text-gray-900">{{ impl['Nome do Lead']|e }}</td>
                            <td class="px-4 py-2 text-sm text-gray-500">{{ impl['Produto']|e }}</td>
                            <td class="px-4 py-2 text-sm text-gray-500">{{ impl['Data_Criacao_Implantacao']|e }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="bg-gray-50 p-4 rounded-lg text-center">
                <p class="text-gray-500">Nenhuma implantação recente encontrada</p>
            </div>
            {% endif %}
        </div>

        <!-- Upcoming Finalizations -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <h3 class="text-base font-medium text-gray-900 mb-1">Próximas Finalizações</h3>
            <p class="text-sm text-gray-500 mb-4">Implantações com previsão de finalização próxima</p>

            {% if upcoming_finalizations %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Lead</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Produto</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase">Previsão</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for impl in upcoming_finalizations %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-2 text-sm text-gray-900">{{ impl['Nome do Lead']|e }}</td>
                            <td class="px-4 py-2 text-sm text-gray-500">{{ impl['Produto']|e }}</td>
                            <td class="px-4 py-2 text-sm text-gray-500">{{ impl['DataPrevistaDeFinalização']|e }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="bg-gray-50 p-4 rounded-lg text-center">
                <p class="text-gray-500">Nenhuma finalização próxima encontrada</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Charts Section - First Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Implementation Phases Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Fases de Implantação</h3>
                {{ business_tooltip(
                    title="Fases de Implementação",
                    description="Distribuição das implementações por fase do processo, permitindo identificar gargalos e otimizar o fluxo de trabalho.",
                    formula="COUNT(Oportunidade_id) GROUP BY Fase_Implantacao",
                    columns="Oportunidade_id, Fase_Implantacao",
                    data_source="base_dados.csv → Agrupamento de implementações por fase",
                    calculation_method="Contagem de implementações agrupadas por cada fase: Planejamento, Configuração, Treinamento, Testes, Go-live"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Distribuição de implantações por fase</p>
            <div class="h-64">
                <canvas id="phasesChart"></canvas>
            </div>
        </div>

        <!-- Implementation Status Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Status de Implantação</h3>
                {{ business_tooltip(
                    title="Status de Implementação",
                    description="Distribuição das implementações por status atual, mostrando quantas estão ativas, finalizadas ou canceladas.",
                    formula="COUNT(Oportunidade_id) GROUP BY Status_Implantacao",
                    columns="Oportunidade_id, Status_Implantacao",
                    data_source="base_dados.csv → Agrupamento de implementações por status",
                    calculation_method="Contagem de implementações agrupadas por status: Em Andamento, Finalizado, Cancelado, Pausado"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Distribuição por status</p>
            <div class="h-64">
                <canvas id="statusChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Charts Section - Second Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Implementation Timeline Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Evolução de Implantações</h3>
                {{ business_tooltip(
                    title="Evolução de Implementações",
                    description="Evolução temporal do número de implementações iniciadas mensalmente, permitindo identificar tendências e sazonalidade.",
                    formula="COUNT(Oportunidade_id) GROUP BY MONTH(Data_Criacao_Implantacao)",
                    columns="Oportunidade_id, Data_Criacao_Implantacao",
                    data_source="base_dados.csv → Agrupamento de implementações por mês da data de criação",
                    calculation_method="Contagem de implementações agrupadas por mês/ano da data de criação, ordenadas cronologicamente"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Implantações iniciadas por mês</p>
            <div class="h-64">
                <canvas id="timelineChart"></canvas>
            </div>
        </div>

        <!-- Implementation by Product Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Implantações por Produto</h3>
                {{ business_tooltip(
                    title="Implementações por Produto",
                    description="Distribuição das implementações por tipo de produto, mostrando quais soluções são mais implementadas.",
                    formula="COUNT(Oportunidade_id) GROUP BY Produto",
                    columns="Oportunidade_id, Produto",
                    data_source="base_dados.csv → Agrupamento de implementações por produto",
                    calculation_method="Contagem de implementações agrupadas por tipo de produto oferecido"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Distribuição por tipo de produto</p>
            <div class="h-64">
                <canvas id="productChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Filters -->
    {{ table_filters([
        {
            "id": "search-implementations",
            "type": "search",
            "placeholder": "Buscar implantações..."
        },
        {
            "id": "phase-filter",
            "type": "select",
            "placeholder": "Todas as fases",
            "options": []
        },
        {
            "id": "status-filter",
            "type": "select",
            "placeholder": "Todos os status",
            "options": []
        }
    ]) }}

    <!-- Implementations Table with Fixed Height and Enhanced UI -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
        <div class="p-4 bg-gray-50 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Lista de Implantações</h3>
            <p class="text-sm text-gray-500 mt-1">Visualize e gerencie todas as implantações ativas e finalizadas</p>
        </div>

        <!-- Fixed Height Container with Scrollbar -->
        <div class="overflow-x-auto">
            <div class="overflow-y-auto h-[600px]" id="implementations-container">
                <table class="min-w-full divide-y divide-gray-200" id="implementations-table">
                    <thead class="bg-gray-100 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Lead</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Data de Criação</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Fase</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Responsável</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Ações</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for impl in implementations %}
                        <tr class="hover:bg-gray-50 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ impl['Oportunidade_id']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 font-medium">{{ impl['Nome do Lead']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl['Data_Criacao_Implantacao']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 phase-cell">{{ impl['Fase_Implantacao']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 status-cell">
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium
                                    {% if impl['Status_Implantacao'] == 'Finalizado' %}
                                        bg-green-100 text-green-800
                                    {% elif impl['Status_Implantacao'] == 'Cancelado' %}
                                        bg-red-100 text-red-800
                                    {% elif impl['Status_Implantacao'] == 'Em Andamento' %}
                                        bg-blue-100 text-blue-800
                                    {% else %}
                                        bg-gray-100 text-gray-800
                                    {% endif %}
                                ">
                                    {{ impl['Status_Implantacao']|e }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ impl['ResponsableOnboarding']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <a href="{{ url_for('implementation.detail', opp_id=impl['Oportunidade_id']) }}" class="text-primary hover:text-blue-700 hover:underline transition-colors duration-150">Ver Detalhes</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Table Info and Pagination -->
        <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div class="mb-4 sm:mb-0">
                    <p class="text-sm text-gray-700">
                        Mostrando <span class="font-medium">{{ implementations|length }}</span> implantações
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Anterior</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                        <a href="#" aria-current="page" class="z-10 bg-primary border-primary text-white relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                            1
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Próximo</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Charts Section - Moved from after the table -->
    <div class="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Implementation by Responsible Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Implantações por Responsável</h3>
                {{ business_tooltip(
                    title="Implementações por Responsável",
                    description="Distribuição das implementações por responsável de onboarding, permitindo avaliar carga de trabalho e performance individual.",
                    formula="COUNT(Oportunidade_id) GROUP BY ResponsableOnboarding",
                    columns="Oportunidade_id, ResponsableOnboarding",
                    data_source="base_dados.csv → Agrupamento de implementações por responsável de onboarding",
                    calculation_method="Contagem de implementações agrupadas por nome do responsável de onboarding"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Distribuição por responsável de onboarding</p>
            <div class="h-64">
                <canvas id="responsibleChart"></canvas>
            </div>
        </div>

        <!-- Implementation by University Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Implantações por Universidade</h3>
                {{ business_tooltip(
                    title="Implementações por Universidade",
                    description="Top 5 universidades com maior número de implementações, mostrando a penetração institucional e geográfica.",
                    formula="COUNT(Oportunidade_id) GROUP BY Universidade ORDER BY COUNT DESC LIMIT 5",
                    columns="Oportunidade_id, Universidade",
                    data_source="base_dados.csv → Agrupamento de implementações por universidade, limitado às 5 principais",
                    calculation_method="Contagem de implementações por universidade, ordenadas por quantidade decrescente, exibindo apenas as 5 principais"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Top 5 universidades com mais implantações</p>
            <div class="h-64">
                <canvas id="universityChart"></canvas>
            </div>
        </div>

        <!-- Implementation Time Distribution Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Distribuição de Tempo</h3>
                {{ business_tooltip(
                    title="Distribuição de Tempo de Implementação",
                    description="Distribuição das implementações por faixas de tempo de duração, permitindo identificar padrões de eficiência.",
                    formula="COUNT(Oportunidade_id) GROUP BY CASE WHEN tempo <= 15 THEN '0-15 dias' WHEN tempo <= 30 THEN '16-30 dias' ... END",
                    columns="Data_Criacao_Implantacao, Data_Finalizacao",
                    data_source="base_dados.csv → Cálculo de tempo e agrupamento por faixas",
                    calculation_method="Cálculo da diferença entre datas e agrupamento em faixas: 0-15, 16-30, 31-60, 61-90, 90+ dias"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Implantações por faixa de tempo</p>
            <div class="h-64">
                <canvas id="timeDistributionChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Implementation Time by Product Chart -->
    <div class="mt-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Tempo Médio por Produto</h3>
                {{ business_tooltip(
                    title="Tempo Médio de Implementação por Produto",
                    description="Duração média em dias para implementar cada tipo de produto, permitindo identificar complexidade relativa das soluções.",
                    formula="AVG(Data_Finalizacao - Data_Criacao_Implantacao) GROUP BY Produto",
                    columns="Data_Criacao_Implantacao, Data_Finalizacao, Produto",
                    data_source="base_dados.csv → Cálculo de tempo médio agrupado por produto",
                    calculation_method="Média aritmética da diferença em dias entre data de criação e finalização, agrupada por tipo de produto"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Duração média da implantação por produto</p>
            <div class="h-64">
                <canvas id="timeByProductChart"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded for implementations page');

        // Initialize table filters
        const searchInput = document.getElementById('search-implementations');
        const phaseFilter = document.getElementById('phase-filter');
        const statusFilter = document.getElementById('status-filter');
        const rows = document.querySelectorAll('#implementations-table tbody tr');

        // Populate phase filter
        const phases = new Set();
        document.querySelectorAll('.phase-cell').forEach(cell => {
            if (cell.textContent.trim()) {
                phases.add(cell.textContent.trim());
            }
        });

        phases.forEach(phase => {
            const option = document.createElement('option');
            option.value = phase;
            option.textContent = phase;
            phaseFilter.appendChild(option);
        });

        // Populate status filter
        const statuses = new Set();
        document.querySelectorAll('.status-cell').forEach(cell => {
            const status = cell.textContent.trim();
            if (status) {
                statuses.add(status);
            }
        });

        statuses.forEach(status => {
            const option = document.createElement('option');
            option.value = status;
            option.textContent = status;
            statusFilter.appendChild(option);
        });

        // Filter function
        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const selectedPhase = phaseFilter.value;
            const selectedStatus = statusFilter.value;

            rows.forEach(row => {
                const leadName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const phase = row.querySelector('.phase-cell').textContent.trim();
                const status = row.querySelector('.status-cell').textContent.trim();

                const matchesSearch = leadName.includes(searchTerm);
                const matchesPhase = !selectedPhase || phase === selectedPhase;
                const matchesStatus = !selectedStatus || status === selectedStatus;

                row.style.display = matchesSearch && matchesPhase && matchesStatus ? '' : 'none';
            });
        }

        // Add event listeners
        searchInput.addEventListener('input', filterTable);
        phaseFilter.addEventListener('change', filterTable);
        statusFilter.addEventListener('change', filterTable);

        // Create implementation phases chart
        try {
            // Get phases data from backend
            const phasesData = {{ phases_data|tojson }};

            if (Object.keys(phasesData).length === 0) {
                document.getElementById('phasesChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de fases não disponíveis</div>';
            } else {
                const phaseLabels = Object.keys(phasesData);
                const phaseValues = Object.values(phasesData).map(v => parseInt(v));

                AmigoDH.createPieChart('phasesChart', phaseLabels, phaseValues, {
                    backgroundColor: AmigoDH.colors.blue
                });
                console.log('Phases chart created successfully');
            }
        } catch (error) {
            console.error('Error creating phases chart:', error);
            document.getElementById('phasesChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de fases</div>';
        }

        // Create implementation status chart
        try {
            // Get status data from backend
            const statusData = {{ status_data|tojson }};

            if (Object.keys(statusData).length === 0) {
                document.getElementById('statusChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de status não disponíveis</div>';
            } else {
                const statusLabels = Object.keys(statusData);
                const statusValues = Object.values(statusData).map(v => parseInt(v));

                // Define colors based on status labels
                const statusColors = statusLabels.map(status => {
                    if (status === 'Finalizado') return '#34C759';
                    if (status === 'Cancelado') return '#FF3B30';
                    if (status === 'Em Andamento') return '#0087EB';
                    return '#6B7280'; // Default for other statuses
                });

                AmigoDH.createDoughnutChart('statusChart', statusLabels, statusValues, {
                    backgroundColor: statusColors
                });
                console.log('Status chart created successfully');
            }
        } catch (error) {
            console.error('Error creating status chart:', error);
            document.getElementById('statusChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de status</div>';
        }

        // Create implementation by responsible chart
        try {
            // Get responsible data from backend
            const responsibleData = {{ responsible_data|tojson }};

            if (Object.keys(responsibleData).length === 0) {
                document.getElementById('responsibleChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de responsáveis não disponíveis</div>';
            } else {
                const responsibleLabels = Object.keys(responsibleData);
                const responsibleValues = Object.values(responsibleData).map(v => parseInt(v));

                AmigoDH.createBarChart('responsibleChart', responsibleLabels, responsibleValues, 'Implantações', {
                    backgroundColor: AmigoDH.colors.primary,
                    indexAxis: 'y' // Horizontal bar chart
                });
                console.log('Responsible chart created successfully');
            }
        } catch (error) {
            console.error('Error creating responsible chart:', error);
            document.getElementById('responsibleChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de responsáveis</div>';
        }

        // Create implementation timeline chart
        try {
            // Get timeline data from backend
            const timelineData = {{ timeline_data|tojson }};

            if (!timelineData.months || timelineData.months.length === 0) {
                document.getElementById('timelineChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de evolução não disponíveis</div>';
            } else {
                const timelineLabels = timelineData.months;
                const timelineValues = timelineData.counts.map(v => parseInt(v));

                AmigoDH.createLineChart('timelineChart', timelineLabels, timelineValues, 'Implantações', {
                    backgroundColor: 'rgba(152, 207, 255, 0.2)',
                    borderColor: AmigoDH.colors.primary,
                    fill: true
                });
                console.log('Timeline chart created successfully');
            }
        } catch (error) {
            console.error('Error creating timeline chart:', error);
            document.getElementById('timelineChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de evolução</div>';
        }

        // Create implementation by product chart
        try {
            // Get product data from backend
            const productData = {{ product_distribution|tojson }};

            if (Object.keys(productData).length === 0) {
                document.getElementById('productChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de produtos não disponíveis</div>';
            } else {
                const productLabels = Object.keys(productData);
                const productValues = Object.values(productData).map(v => parseInt(v));

                AmigoDH.createPieChart('productChart', productLabels, productValues, {
                    backgroundColor: AmigoDH.colors.green
                });
                console.log('Product chart created successfully');
            }
        } catch (error) {
            console.error('Error creating product chart:', error);
            document.getElementById('productChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de produtos</div>';
        }

        // Create implementation by university chart
        try {
            // Get university data from backend
            const universityData = {{ university_distribution|tojson }};

            if (Object.keys(universityData).length === 0) {
                document.getElementById('universityChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de universidades não disponíveis</div>';
            } else {
                // Sort universities by number of implementations and get top 5
                const sortedUniversities = Object.entries(universityData)
                    .sort((a, b) => parseInt(b[1]) - parseInt(a[1]))
                    .slice(0, 5);

                const universityLabels = sortedUniversities.map(item => item[0]);
                const universityValues = sortedUniversities.map(item => parseInt(item[1]));

                AmigoDH.createBarChart('universityChart', universityLabels, universityValues, 'Implantações', {
                    backgroundColor: AmigoDH.colors.primary,
                    indexAxis: 'y' // Horizontal bar chart
                });
                console.log('University chart created successfully');
            }
        } catch (error) {
            console.error('Error creating university chart:', error);
            document.getElementById('universityChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de universidades</div>';
        }

        // Create implementation time distribution chart
        try {
            // Get time distribution data from backend
            const timeDistributionData = {{ implementation_time_distribution|tojson }};

            if (Object.keys(timeDistributionData).length === 0) {
                document.getElementById('timeDistributionChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de distribuição de tempo não disponíveis</div>';
            } else {
                // Define the order of time ranges
                const timeRanges = ['0-15 dias', '16-30 dias', '31-60 dias', '61-90 dias', '90+ dias'];

                // Filter and sort data according to the defined order
                const timeLabels = [];
                const timeValues = [];

                timeRanges.forEach(range => {
                    if (range in timeDistributionData) {
                        timeLabels.push(range);
                        timeValues.push(parseInt(timeDistributionData[range]));
                    }
                });

                // Define colors based on time ranges (gradient from green to red)
                const timeColors = [
                    '#34C759', // 0-15 days (green)
                    '#7ED321', // 16-30 days (light green)
                    '#FFCC00', // 31-60 days (yellow)
                    '#FF9500', // 61-90 days (orange)
                    '#FF3B30'  // 90+ days (red)
                ];

                AmigoDH.createBarChart('timeDistributionChart', timeLabels, timeValues, 'Implantações', {
                    backgroundColor: timeColors
                });
                console.log('Time distribution chart created successfully');
            }
        } catch (error) {
            console.error('Error creating time distribution chart:', error);
            document.getElementById('timeDistributionChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de distribuição de tempo</div>';
        }

        // Create implementation time by product chart
        try {
            // Get time by product data from backend
            const timeByProductData = {{ implementation_time_by_product|tojson }};

            if (Object.keys(timeByProductData).length === 0) {
                document.getElementById('timeByProductChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de tempo por produto não disponíveis</div>';
            } else {
                // Sort products by implementation time
                const sortedProducts = Object.entries(timeByProductData)
                    .sort((a, b) => parseInt(b[1]) - parseInt(a[1]));

                const productLabels = sortedProducts.map(item => item[0]);
                const timeValues = sortedProducts.map(item => parseInt(item[1]));

                AmigoDH.createBarChart('timeByProductChart', productLabels, timeValues, 'Dias', {
                    backgroundColor: AmigoDH.colors.green,
                    indexAxis: 'y', // Horizontal bar chart
                    tooltipCallback: function(value) {
                        return value + ' dias';
                    }
                });
                console.log('Time by product chart created successfully');
            }
        } catch (error) {
            console.error('Error creating time by product chart:', error);
            document.getElementById('timeByProductChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de tempo por produto</div>';
        }
    });
</script>
{% endblock %}
