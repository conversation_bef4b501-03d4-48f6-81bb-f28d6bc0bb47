{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section %}

{% block title %}Detalhes da Oportunidade - {{ app_name|e }}{% endblock %}

{% block content %}
<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Back Button -->
    <div class="mb-6">
        <a href="{{ url_for('opportunity.index') }}" class="inline-flex items-center text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Voltar para a lista de oportunidades
        </a>
    </div>

    <!-- Opportunity Header -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Oportunidade: {{ opportunity['Nome do Lead']|e }}</h1>
                <p class="text-gray-500 mt-1">Criada em {{ opportunity['Data_criacao_Oportunidade']|e }}</p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-col md:flex-row md:items-center gap-2">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    {{ opportunity['Etapa do funil Comercial']|e }}
                </span>
                {% if has_implementation %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    Em Implantação
                </span>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Opportunity Information -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Opportunity Details -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 md:col-span-2">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Informações da Oportunidade</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p class="text-sm font-medium text-gray-500">ID da Oportunidade</p>
                    <p class="text-base text-gray-900">{{ opportunity['Oportunidade_id']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Data de Criação</p>
                    <p class="text-base text-gray-900">{{ opportunity['Data_criacao_Oportunidade']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Lead</p>
                    <p class="text-base text-gray-900">{{ opportunity['Nome do Lead']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">ID do Lead</p>
                    <p class="text-base text-gray-900">{{ opportunity['Lead_id']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Etapa do Funil</p>
                    <p class="text-base text-gray-900">{{ opportunity['Etapa do funil Comercial']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Produto</p>
                    <p class="text-base text-gray-900">{{ opportunity['Produto']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Valor Mensalidade</p>
                    <p class="text-base text-gray-900">{{ opportunity['Valor Mensalidade']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Responsável</p>
                    <p class="text-base text-gray-900">{{ opportunity['Nome_Responsavel']|e }}</p>
                </div>
            </div>
        </div>

        <!-- Opportunity Status -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Status da Oportunidade</h2>
            
            <div class="mb-4">
                <p class="text-sm font-medium text-gray-500 mb-1">Status</p>
                <p class="text-xl font-bold text-gray-900">{{ opportunity['Etapa do funil Comercial']|e }}</p>
            </div>
            
            <div class="mb-4">
                <p class="text-sm font-medium text-gray-500 mb-1">Implantação</p>
                {% if has_implementation %}
                <p class="text-base text-green-600 font-medium">Em andamento</p>
                {% else %}
                <p class="text-base text-gray-500">Não iniciada</p>
                {% endif %}
            </div>
            
            <div class="mt-6">
                {% if has_implementation %}
                <a href="{{ url_for('implementation.detail', opp_id=opportunity['Oportunidade_id']) }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    Ver Implantação
                </a>
                {% else %}
                <button disabled class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gray-400 cursor-not-allowed">
                    Sem Implantação
                </button>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Lead Information -->
    <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Informações do Lead</h2>
        
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <p class="text-sm font-medium text-gray-500">Nome</p>
                    <p class="text-base text-gray-900">{{ opportunity['Nome do Lead']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Data de Criação</p>
                    <p class="text-base text-gray-900">{{ opportunity['Data_criacao_lead']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Universidade</p>
                    <p class="text-base text-gray-900">{{ opportunity['Universidade']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Curso</p>
                    <p class="text-base text-gray-900">{{ opportunity['Curso']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Formação Acadêmica</p>
                    <p class="text-base text-gray-900">{{ opportunity['Formação Academica']|e }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500">Cidade/Estado</p>
                    <p class="text-base text-gray-900">{{ opportunity['Cidade']|e }}/{{ opportunity['Estado']|e }}</p>
                </div>
            </div>
            
            <div class="mt-4">
                <a href="{{ url_for('lead.detail', lead_id=opportunity['Lead_id']) }}" class="text-primary hover:text-blue-800 text-sm font-medium flex items-center">
                    Ver detalhes do lead
                    <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
