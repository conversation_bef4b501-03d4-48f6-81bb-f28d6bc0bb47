{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, table_filters, data_table, business_tooltip %}

{% block title %}Oportunidades - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Gestão de Oportunidades",
    subtitle="Acompanhe negociações em andamento, gerencie propostas e monitore a conversão de leads em clientes.",
    stats=[
        {
            "label": "Total de Oportunidades",
            "value": total_opps,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />'
        },
        {
            "label": "Taxa de Conversão",
            "value": (avg_conversion_rate|float|round(1))|string + "%",
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />'
        }
    ],
    bg_class="bg-gray-100"
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- KPI: Total de Oportunidades -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-primary-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Total de Oportunidades</p>
                        {{ business_tooltip(
                            title="Total de Oportunidades",
                            description="Número total de oportunidades de negócio criadas no sistema, representando leads qualificados com interesse concreto.",
                            formula="COUNT(DISTINCT Oportunidade_id)",
                            columns="Oportunidade_id",
                            data_source="base_dados.csv → Contagem de registros únicos de Oportunidade_id",
                            calculation_method="Contagem simples de todos os IDs únicos de oportunidades cadastradas no sistema"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ total_opps|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Negociações em andamento</p>
        </div>

        <!-- KPI: Oportunidades Ativas -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-primary-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Oportunidades Ativas</p>
                        {{ business_tooltip(
                            title="Oportunidades Ativas",
                            description="Oportunidades que ainda não foram convertidas em implementações, representando o pipeline ativo de vendas.",
                            formula="COUNT(Oportunidade_id WHERE Data_Criacao_Implantacao IS NULL)",
                            columns="Oportunidade_id, Data_Criacao_Implantacao",
                            data_source="base_dados.csv → Contagem de oportunidades sem data de criação de implementação",
                            calculation_method="Contagem de oportunidades que não possuem implementação associada (Data_Criacao_Implantacao é nula)"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ total_active_opps|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Ainda não convertidas</p>
        </div>

        <!-- KPI: Oportunidades Convertidas -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Oportunidades Convertidas</p>
                        {{ business_tooltip(
                            title="Oportunidades Convertidas",
                            description="Oportunidades que foram convertidas em implementações, representando vendas efetivadas no funil comercial.",
                            formula="COUNT(Oportunidade_id WHERE Data_Criacao_Implantacao IS NOT NULL)",
                            columns="Oportunidade_id, Data_Criacao_Implantacao",
                            data_source="base_dados.csv → Contagem de oportunidades com data de criação de implementação",
                            calculation_method="Contagem de oportunidades que possuem implementação associada (Data_Criacao_Implantacao não é nula)"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ total_converted_opps|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Convertidas em implantações</p>
        </div>

        <!-- KPI: Taxa de Conversão -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Taxa de Conversão</p>
                        {{ business_tooltip(
                            title="Taxa de Conversão Oportunidade → Implementação",
                            description="Percentual de oportunidades que foram convertidas em implementações, indicando a eficiência do processo de vendas.",
                            formula="(Oportunidades Convertidas ÷ Total de Oportunidades) × 100",
                            columns="Oportunidade_id, Data_Criacao_Implantacao",
                            data_source="base_dados.csv → Divisão de oportunidades convertidas pelo total de oportunidades",
                            calculation_method="Divisão do número de oportunidades com implementação pelo total de oportunidades, multiplicado por 100"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ (avg_conversion_rate|float|round(1))|string + "%" }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Oportunidades → Implantações</p>
        </div>
    </div>

    <!-- Charts Section - First Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Funnel Stages Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Etapas do Funil Comercial</h3>
                {{ business_tooltip(
                    title="Etapas do Funil Comercial",
                    description="Distribuição das oportunidades por etapa do funil comercial, permitindo identificar gargalos no processo de vendas.",
                    formula="COUNT(Oportunidade_id) GROUP BY Etapa_do_funil_Comercial",
                    columns="Oportunidade_id, Etapa_do_funil_Comercial",
                    data_source="base_dados.csv → Agrupamento de oportunidades por etapa do funil",
                    calculation_method="Contagem de oportunidades agrupadas por cada etapa: Qualificação, Apresentação, Proposta, Negociação, Fechamento"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Distribuição de oportunidades por etapa</p>
            <div class="h-64">
                <canvas id="funnelStagesChart"></canvas>
            </div>
        </div>

        <!-- Opportunity Types Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Tipos de Oportunidades</h3>
                {{ business_tooltip(
                    title="Tipos de Oportunidades",
                    description="Distribuição das oportunidades por tipo de produto oferecido, mostrando a demanda por cada solução.",
                    formula="COUNT(Oportunidade_id) GROUP BY Produto",
                    columns="Oportunidade_id, Produto",
                    data_source="base_dados.csv → Agrupamento de oportunidades por produto",
                    calculation_method="Contagem de oportunidades agrupadas por tipo de produto oferecido"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Distribuição por produto</p>
            <div class="h-64">
                <canvas id="opportunityTypesChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Charts Section - Second Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Monthly Opportunities Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Evolução de Oportunidades</h3>
                {{ business_tooltip(
                    title="Evolução de Oportunidades",
                    description="Evolução temporal do número de oportunidades criadas mensalmente, permitindo identificar tendências e sazonalidade no pipeline.",
                    formula="COUNT(Oportunidade_id) GROUP BY MONTH(Data_criacao_Oportunidade)",
                    columns="Oportunidade_id, Data_criacao_Oportunidade",
                    data_source="base_dados.csv → Agrupamento de oportunidades por mês da data de criação",
                    calculation_method="Contagem de oportunidades agrupadas por mês/ano da data de criação, ordenadas cronologicamente"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Oportunidades criadas por mês</p>
            <div class="h-64">
                <canvas id="monthlyOpportunitiesChart"></canvas>
            </div>
        </div>

        <!-- Conversion Rate by Product Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Taxa de Conversão por Produto</h3>
                {{ business_tooltip(
                    title="Taxa de Conversão por Produto",
                    description="Percentual de conversão de oportunidades em implementações por tipo de produto, identificando produtos com melhor performance.",
                    formula="(Oportunidades Convertidas ÷ Total de Oportunidades) × 100 GROUP BY Produto",
                    columns="Oportunidade_id, Produto, Data_Criacao_Implantacao",
                    data_source="base_dados.csv → Cálculo de conversão agrupado por produto",
                    calculation_method="Divisão do número de oportunidades convertidas pelo total de oportunidades por produto, multiplicado por 100"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Percentual de conversão por tipo de produto</p>
            <div class="h-64">
                <canvas id="conversionRateChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Filters -->
    {{ table_filters([
        {
            "id": "search-opportunities",
            "type": "search",
            "placeholder": "Buscar oportunidades..."
        },
        {
            "id": "stage-filter",
            "type": "select",
            "placeholder": "Todas as etapas",
            "options": []
        },
        {
            "id": "product-filter",
            "type": "select",
            "placeholder": "Todos os produtos",
            "options": []
        }
    ]) }}

    <!-- Opportunities Table with Fixed Height and Enhanced UI -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
        <div class="p-4 bg-gray-50 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Lista de Oportunidades</h3>
            <p class="text-sm text-gray-500 mt-1">Visualize e gerencie todas as oportunidades de negócio</p>
        </div>

        <!-- Fixed Height Container with Scrollbar -->
        <div class="overflow-x-auto">
            <div class="overflow-y-auto h-[600px]" id="opportunities-container">
                <table class="min-w-full divide-y divide-gray-200" id="opportunities-table">
                    <thead class="bg-gray-100 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Lead</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Data de Criação</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Etapa do Funil</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Produto</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Valor Mensal</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Responsável</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Ações</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for opp in opportunities %}
                        <tr class="hover:bg-gray-50 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ opp['Oportunidade_id']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 font-medium">{{ opp['Nome do Lead']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ opp['Data_criacao_Oportunidade']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 stage-cell">
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ opp['Etapa do funil Comercial']|e }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 product-cell">{{ opp['Produto']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-medium">{{ opp['Valor Mensalidade']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ opp['Nome_Responsavel']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <a href="{{ url_for('opportunity.detail', opp_id=opp['Oportunidade_id']) }}" class="text-primary hover:text-blue-700 hover:underline transition-colors duration-150">Ver Detalhes</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Table Info and Pagination -->
        <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div class="mb-4 sm:mb-0">
                    <p class="text-sm text-gray-700">
                        Mostrando <span class="font-medium">{{ opportunities|length }}</span> oportunidades
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Anterior</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                        <a href="#" aria-current="page" class="z-10 bg-primary border-primary text-white relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                            1
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Próximo</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Charts Section -->
    <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Opportunities by Responsible Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Oportunidades por Responsável</h3>
                {{ business_tooltip(
                    title="Oportunidades por Responsável",
                    description="Distribuição das oportunidades por responsável comercial, permitindo avaliar a carga de trabalho e performance individual.",
                    formula="COUNT(Oportunidade_id) GROUP BY Nome_Responsavel",
                    columns="Oportunidade_id, Nome_Responsavel",
                    data_source="base_dados.csv → Agrupamento de oportunidades por responsável",
                    calculation_method="Contagem de oportunidades agrupadas por nome do responsável comercial"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Distribuição por responsável comercial</p>
            <div class="h-64">
                <canvas id="responsibleChart"></canvas>
            </div>
        </div>

        <!-- Average Deal Size Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Valor Médio por Produto</h3>
                {{ business_tooltip(
                    title="Valor Médio por Produto",
                    description="Valor médio das mensalidades das oportunidades agrupadas por tipo de produto, indicando o ticket médio por solução.",
                    formula="AVG(Valor_Mensalidade) GROUP BY Produto",
                    columns="Valor_Mensalidade, Produto",
                    data_source="base_dados.csv → Média dos valores de mensalidade agrupados por produto",
                    calculation_method="Cálculo da média aritmética dos valores de mensalidade para cada tipo de produto"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Valor médio das oportunidades por produto</p>
            <div class="h-64">
                <canvas id="dealSizeChart"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded for opportunities page');

        // Parse chart data safely
        let chartData = {};
        try {
            const rawChartData = '{{ chart_data|safe }}';
            console.log('Raw chart data:', rawChartData);

            if (rawChartData && rawChartData !== 'None' && rawChartData !== '' && rawChartData !== '{}') {
                chartData = JSON.parse(rawChartData);
                console.log('Chart data parsed successfully:', chartData);
            } else {
                console.warn('No chart data available for opportunities');
                chartData = {};
            }
        } catch (error) {
            console.error('Error parsing chart data:', error);
            console.error('Raw data was:', '{{ chart_data|safe }}');
            chartData = {};
        }

        // Initialize table filters
        const searchInput = document.getElementById('search-opportunities');
        const stageFilter = document.getElementById('stage-filter');
        const productFilter = document.getElementById('product-filter');
        const rows = document.querySelectorAll('#opportunities-table tbody tr');

        // Populate stage filter
        const stages = new Set();
        document.querySelectorAll('.stage-cell').forEach(cell => {
            if (cell.textContent.trim()) {
                stages.add(cell.textContent.trim());
            }
        });

        stages.forEach(stage => {
            const option = document.createElement('option');
            option.value = stage;
            option.textContent = stage;
            stageFilter.appendChild(option);
        });

        // Populate product filter
        const products = new Set();
        document.querySelectorAll('.product-cell').forEach(cell => {
            if (cell.textContent.trim()) {
                products.add(cell.textContent.trim());
            }
        });

        products.forEach(product => {
            const option = document.createElement('option');
            option.value = product;
            option.textContent = product;
            productFilter.appendChild(option);
        });

        // Filter function
        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const selectedStage = stageFilter.value;
            const selectedProduct = productFilter.value;

            rows.forEach(row => {
                const leadName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const stage = row.querySelector('.stage-cell').textContent.trim();
                const product = row.querySelector('.product-cell').textContent.trim();

                const matchesSearch = leadName.includes(searchTerm);
                const matchesStage = !selectedStage || stage === selectedStage;
                const matchesProduct = !selectedProduct || product === selectedProduct;

                row.style.display = matchesSearch && matchesStage && matchesProduct ? '' : 'none';
            });
        }

        // Add event listeners
        searchInput.addEventListener('input', filterTable);
        stageFilter.addEventListener('change', filterTable);
        productFilter.addEventListener('change', filterTable);

        // Create funnel stages chart
        try {
            const funnelStagesData = chartData.funnel_stages || {};
            const funnelLabels = Object.keys(funnelStagesData);
            const funnelValues = Object.values(funnelStagesData).map(v => {
                // Verificar se o valor é uma string JSON
                if (typeof v === 'string' && v.startsWith('[')) {
                    try {
                        return parseInt(JSON.parse(v)[0]);
                    } catch (e) {
                        return parseInt(v) || 0;
                    }
                }
                return parseInt(v) || 0;
            });

            if (funnelLabels.length > 0 && funnelValues.length > 0) {
                if (typeof AmigoDH !== 'undefined' && AmigoDH.createBarChart) {
                    AmigoDH.createBarChart('funnelStagesChart', funnelLabels, funnelValues, 'Quantidade', {
                        backgroundColor: AmigoDH.colors && AmigoDH.colors.blue ? AmigoDH.colors.blue : 'rgba(59, 130, 246, 0.7)',
                        borderColor: AmigoDH.colors && AmigoDH.colors.primary ? AmigoDH.colors.primary : 'rgba(59, 130, 246, 1)',
                        indexAxis: 'y'
                    });
                } else {
                    // Fallback to native Chart.js
                    new Chart(document.getElementById('funnelStagesChart').getContext('2d'), {
                        type: 'bar',
                        data: {
                            labels: funnelLabels,
                            datasets: [{
                                label: 'Quantidade',
                                data: funnelValues,
                                backgroundColor: 'rgba(59, 130, 246, 0.7)',
                                borderColor: 'rgba(59, 130, 246, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            indexAxis: 'y',
                            scales: {
                                x: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
                console.log('Funnel stages chart created successfully');
            } else {
                document.getElementById('funnelStagesChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de funil não disponíveis</div>';
            }
        } catch (error) {
            console.error('Error creating funnel stages chart:', error);
            window.debugChart('funnelStagesChart', {}, error);
            document.getElementById('funnelStagesChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico: ' + error.message + '</div>';
        }

        // Create opportunity types chart
        try {
            const oppTypesData = chartData.opp_types || {};
            const typeLabels = Object.keys(oppTypesData);
            const typeValues = Object.values(oppTypesData).map(v => {
                // Verificar se o valor é uma string JSON
                if (typeof v === 'string' && v.startsWith('[')) {
                    try {
                        return parseInt(JSON.parse(v)[0]);
                    } catch (e) {
                        return parseInt(v) || 0;
                    }
                }
                return parseInt(v) || 0;
            });

            if (typeLabels.length > 0 && typeValues.length > 0) {
                if (typeof AmigoDH !== 'undefined' && AmigoDH.createPieChart) {
                    AmigoDH.createPieChart('opportunityTypesChart', typeLabels, typeValues, {
                        backgroundColor: AmigoDH.colors && AmigoDH.colors.blue ? AmigoDH.colors.blue : [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(99, 102, 241, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(168, 85, 247, 0.8)',
                            'rgba(196, 181, 253, 0.8)'
                        ]
                    });
                } else {
                    // Fallback to native Chart.js
                    new Chart(document.getElementById('opportunityTypesChart').getContext('2d'), {
                        type: 'pie',
                        data: {
                            labels: typeLabels,
                            datasets: [{
                                data: typeValues,
                                backgroundColor: [
                                    'rgba(59, 130, 246, 0.8)',
                                    'rgba(99, 102, 241, 0.8)',
                                    'rgba(139, 92, 246, 0.8)',
                                    'rgba(168, 85, 247, 0.8)',
                                    'rgba(196, 181, 253, 0.8)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    });
                }
                console.log('Opportunity types chart created successfully');
            } else {
                document.getElementById('opportunityTypesChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de tipos de oportunidades não disponíveis</div>';
            }
        } catch (error) {
            console.error('Error creating opportunity types chart:', error);
            window.debugChart('opportunityTypesChart', {}, error);
            document.getElementById('opportunityTypesChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico: ' + error.message + '</div>';
        }

        // Create monthly opportunities chart
        try {
            const monthlyOppsData = chartData.monthly_opps || {};
            const monthLabels = Object.keys(monthlyOppsData);
            const monthValues = Object.values(monthlyOppsData).map(v => {
                // Verificar se o valor é uma string JSON
                if (typeof v === 'string' && v.startsWith('[')) {
                    try {
                        return parseInt(JSON.parse(v)[0]);
                    } catch (e) {
                        return parseInt(v) || 0;
                    }
                }
                return parseInt(v) || 0;
            });

            if (monthLabels.length > 0 && monthValues.length > 0) {
                if (typeof AmigoDH !== 'undefined' && AmigoDH.createLineChart) {
                    AmigoDH.createLineChart('monthlyOpportunitiesChart', monthLabels, monthValues, 'Oportunidades', {
                        backgroundColor: 'rgba(152, 207, 255, 0.2)',
                        borderColor: AmigoDH.colors && AmigoDH.colors.primary ? AmigoDH.colors.primary : 'rgba(59, 130, 246, 1)',
                        fill: true
                    });
                } else {
                    // Fallback to native Chart.js
                    new Chart(document.getElementById('monthlyOpportunitiesChart').getContext('2d'), {
                        type: 'line',
                        data: {
                            labels: monthLabels,
                            datasets: [{
                                label: 'Oportunidades',
                                data: monthValues,
                                backgroundColor: 'rgba(152, 207, 255, 0.2)',
                                borderColor: 'rgba(59, 130, 246, 1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.3
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
                console.log('Monthly opportunities chart created successfully');
            } else {
                document.getElementById('monthlyOpportunitiesChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de evolução não disponíveis</div>';
            }
        } catch (error) {
            console.error('Error creating monthly opportunities chart:', error);
            document.getElementById('monthlyOpportunitiesChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico: ' + error.message + '</div>';
        }

        // Create conversion rate by product chart
        try {
            const oppTypesData = chartData.opp_types || {};
            const productLabels = Object.keys(oppTypesData);

            // Sample conversion rates - in a real app, this would come from the backend
            const conversionRates = productLabels.map((_, index) => {
                // Use deterministic values based on product index to avoid pseudorandom
                const baseRate = 30 + (index * 15) % 50; // Deterministic calculation
                return Math.min(baseRate, 80); // Cap at 80%
            });

            if (productLabels.length > 0 && conversionRates.length > 0) {
                if (typeof AmigoDH !== 'undefined' && AmigoDH.createBarChart) {
                    AmigoDH.createBarChart('conversionRateChart', productLabels, conversionRates, 'Taxa de Conversão (%)', {
                        backgroundColor: 'rgba(99, 102, 241, 0.7)',
                        borderColor: 'rgba(99, 102, 241, 1)',
                        borderWidth: 1,
                        tooltipCallback: function(value) {
                            return value + '%';
                        }
                    });
                } else {
                    // Fallback to native Chart.js
                    new Chart(document.getElementById('conversionRateChart').getContext('2d'), {
                        type: 'bar',
                        data: {
                            labels: productLabels,
                            datasets: [{
                                label: 'Taxa de Conversão (%)',
                                data: conversionRates,
                                backgroundColor: 'rgba(99, 102, 241, 0.7)',
                                borderColor: 'rgba(99, 102, 241, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        callback: function(value) {
                                            return value + '%';
                                        }
                                    }
                                }
                            },
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return context.raw + '%';
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
                console.log('Conversion rate chart created successfully');
            } else {
                document.getElementById('conversionRateChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de conversão não disponíveis</div>';
            }
        } catch (error) {
            console.error('Error creating conversion rate chart:', error);
            document.getElementById('conversionRateChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico: ' + error.message + '</div>';
        }

        // Create opportunities by responsible chart
        try {
            // Get responsible data from the table
            const responsibles = {};
            document.querySelectorAll('#opportunities-table tbody tr').forEach(row => {
                const responsible = row.querySelector('td:nth-child(7)').textContent.trim();
                if (responsible) {
                    responsibles[responsible] = (responsibles[responsible] || 0) + 1;
                }
            });

            // Sort by count and get top 5
            const sortedResponsibles = Object.entries(responsibles)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 5);

            const respLabels = sortedResponsibles.map(item => item[0]);
            const respValues = sortedResponsibles.map(item => item[1]);

            if (respLabels.length > 0 && respValues.length > 0) {
                if (typeof AmigoDH !== 'undefined' && AmigoDH.createBarChart) {
                    AmigoDH.createBarChart('responsibleChart', respLabels, respValues, 'Oportunidades', {
                        backgroundColor: AmigoDH.colors && AmigoDH.colors.primary ? AmigoDH.colors.primary : 'rgba(59, 130, 246, 0.7)',
                        indexAxis: 'y' // Horizontal bar chart
                    });
                } else {
                    // Fallback to native Chart.js
                    new Chart(document.getElementById('responsibleChart').getContext('2d'), {
                        type: 'bar',
                        data: {
                            labels: respLabels,
                            datasets: [{
                                label: 'Oportunidades',
                                data: respValues,
                                backgroundColor: 'rgba(59, 130, 246, 0.7)',
                                borderColor: 'rgba(59, 130, 246, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            indexAxis: 'y',
                            scales: {
                                x: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
                console.log('Responsible chart created successfully');
            } else {
                document.getElementById('responsibleChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de responsáveis não disponíveis</div>';
            }
        } catch (error) {
            console.error('Error creating responsible chart:', error);
            document.getElementById('responsibleChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico: ' + error.message + '</div>';
        }

        // Create average deal size chart
        try {
            // Get product and value data from the table
            const products = {};
            const productCounts = {};

            document.querySelectorAll('#opportunities-table tbody tr').forEach(row => {
                const product = row.querySelector('.product-cell').textContent.trim();
                const valueCell = row.querySelector('td:nth-child(6)').textContent.trim();

                if (product && valueCell) {
                    // Extract numeric value from currency string (e.g., "R$ 1.000,00" -> 1000)
                    const value = parseFloat(valueCell.replace(/[^\d,]/g, '').replace(',', '.')) || 0;

                    if (!products[product]) {
                        products[product] = 0;
                        productCounts[product] = 0;
                    }

                    products[product] += value;
                    productCounts[product]++;
                }
            });

            // Calculate averages
            const avgProducts = {};
            for (const [product, total] of Object.entries(products)) {
                avgProducts[product] = total / productCounts[product];
            }

            // Sort by average value
            const sortedProducts = Object.entries(avgProducts)
                .sort((a, b) => b[1] - a[1]);

            const prodLabels = sortedProducts.map(item => item[0]);
            const prodValues = sortedProducts.map(item => item[1]);

            if (prodLabels.length > 0 && prodValues.length > 0) {
                if (typeof AmigoDH !== 'undefined' && AmigoDH.createBarChart) {
                    AmigoDH.createBarChart('dealSizeChart', prodLabels, prodValues, 'Valor Médio (R$)', {
                        backgroundColor: 'rgba(16, 185, 129, 0.7)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        tooltipCallback: function(value) {
                            return AmigoDH.formatCurrency ? AmigoDH.formatCurrency(value) : 'R$ ' + value.toFixed(2);
                        },
                        yAxisCallback: function(value) {
                            return AmigoDH.formatCurrency ? AmigoDH.formatCurrency(value) : 'R$ ' + value.toFixed(2);
                        }
                    });
                } else {
                    // Fallback to native Chart.js
                    new Chart(document.getElementById('dealSizeChart').getContext('2d'), {
                        type: 'bar',
                        data: {
                            labels: prodLabels,
                            datasets: [{
                                label: 'Valor Médio (R$)',
                                data: prodValues,
                                backgroundColor: 'rgba(16, 185, 129, 0.7)',
                                borderColor: 'rgba(16, 185, 129, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        callback: function(value) {
                                            return 'R$ ' + value.toFixed(2);
                                        }
                                    }
                                }
                            },
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return 'R$ ' + parseFloat(context.raw).toFixed(2);
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
                console.log('Deal size chart created successfully');
            } else {
                document.getElementById('dealSizeChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de valor médio não disponíveis</div>';
            }
        } catch (error) {
            console.error('Error creating deal size chart:', error);
            document.getElementById('dealSizeChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico: ' + error.message + '</div>';
        }
    });
</script>
{% endblock %}
