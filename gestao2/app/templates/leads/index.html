{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section, table_filters, data_table, business_tooltip %}

{% block title %}Leads - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title="Gestão de Leads",
    subtitle="Acompanhe e gerencie potenciais clientes, visualize dados demográficos e monitore o progresso no funil de vendas.",
    stats=[
        {
            "label": "Total de Leads",
            "value": total_leads,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />'
        },
        {
            "label": "Taxa de Conversão",
            "value": lead_to_opp_rate|string + "%",
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />'
        }
    ],
    bg_class="bg-gray-100"
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- KPI: Total de Leads -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-primary-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Total de Leads</p>
                        {{ business_tooltip(
                            title="Total de Leads",
                            description="Número total de potenciais clientes cadastrados no sistema, representando pessoas que demonstraram interesse nos produtos ou serviços.",
                            formula="COUNT(DISTINCT Lead_id)",
                            columns="Lead_id",
                            data_source="base_dados.csv → Contagem de registros únicos de Lead_id",
                            calculation_method="Contagem simples de todos os IDs únicos de leads cadastrados no sistema"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ total_leads|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Potenciais clientes cadastrados</p>
        </div>

        <!-- KPI: Leads com Oportunidades -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-success-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-success-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Leads com Oportunidades</p>
                        {{ business_tooltip(
                            title="Leads com Oportunidades",
                            description="Número de leads que foram convertidos em oportunidades de negócio, representando o primeiro passo no funil de vendas.",
                            formula="COUNT(DISTINCT Lead_id WHERE Oportunidade_id IS NOT NULL)",
                            columns="Lead_id, Oportunidade_id",
                            data_source="base_dados.csv → Contagem de Lead_id únicos que possuem Oportunidade_id",
                            calculation_method="Contagem de leads únicos que possuem pelo menos uma oportunidade associada"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ leads_with_opps|e }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Leads que geraram oportunidades</p>
        </div>

        <!-- KPI: Taxa de Conversão -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-tertiary-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-tertiary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Taxa de Conversão</p>
                        {{ business_tooltip(
                            title="Taxa de Conversão Lead → Oportunidade",
                            description="Percentual de leads que foram convertidos em oportunidades, indicando a eficiência da qualificação inicial.",
                            formula="(Leads com Oportunidades ÷ Total de Leads) × 100",
                            columns="Lead_id, Oportunidade_id",
                            data_source="base_dados.csv → Divisão de leads com oportunidades pelo total de leads",
                            calculation_method="Divisão do número de leads com oportunidades pelo total de leads, multiplicado por 100"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ lead_to_opp_rate|string + "%" }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Leads convertidos em oportunidades</p>
        </div>

        <!-- KPI: Conversão Geral -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex flex-col card-hover border border-gray-100 transition-all duration-300 h-full">
            <div class="flex items-center mb-4">
                <div class="w-14 h-14 rounded-full bg-secondary-50 flex items-center justify-center mr-4">
                    <svg class="w-7 h-7 text-secondary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                </div>
                <div>
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-500 mb-1">Conversão Geral</p>
                        {{ business_tooltip(
                            title="Conversão Geral Lead → Implementação",
                            description="Percentual de leads que chegaram até a implementação finalizada, representando a eficiência completa do funil de vendas.",
                            formula="(Implementações Finalizadas ÷ Total de Leads) × 100",
                            columns="Lead_id, Status_Implantacao",
                            data_source="base_dados.csv → Divisão de implementações finalizadas pelo total de leads",
                            calculation_method="Divisão do número de implementações finalizadas pelo total de leads únicos, multiplicado por 100"
                        ) }}
                    </div>
                    <p class="text-3xl font-bold text-gray-900">{{ overall_conversion|string + "%" }}</p>
                </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Leads convertidos em implantações</p>
        </div>
    </div>

    <!-- Insights Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Insight: Top University -->
        {{ insight_card(
            title="Universidade em Destaque",
            content="<strong>" + top_university_name|e + "</strong> representa <strong>" + top_university_percentage|e + "%</strong> dos leads cadastrados.",
            tip="Considere estratégias específicas para esta universidade.",
            tags=["Universidade", "Leads"]
        ) }}

        <!-- Insight: Top Course -->
        {{ insight_card(
            title="Curso em Destaque",
            content="<strong>" + top_course_name|e + "</strong> com <strong>" + top_course_count|e + "</strong> leads cadastrados.",
            tip="Analise o perfil dos alunos deste curso para otimizar abordagens.",
            tags=["Curso", "Leads"]
        ) }}

        <!-- Insight: Conversion -->
        {{ insight_card(
            title="Análise de Conversão",
            content="A taxa de conversão geral é de <strong>" + overall_conversion|string + "%</strong>. Trabalhe para aumentar este número.",
            tip="Identifique gargalos no funil de vendas para melhorar a conversão.",
            tags=["Conversão", "Vendas"]
        ) }}
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Funnel Stages Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Distribuição no Funil de Vendas</h3>
                {{ business_tooltip(
                    title="Distribuição no Funil de Vendas",
                    description="Visualização da quantidade de leads em cada etapa do funil comercial, permitindo identificar gargalos no processo.",
                    formula="COUNT(Lead_id) GROUP BY Etapa_do_funil_Comercial",
                    columns="Lead_id, Etapa_do_funil_Comercial",
                    data_source="base_dados.csv → Agrupamento de leads por etapa do funil comercial",
                    calculation_method="Contagem de leads agrupados por cada etapa do funil: Qualificação, Apresentação, Proposta, Negociação, Fechamento"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Quantidade de leads em cada etapa do funil</p>
            <div class="relative w-full" style="height: 300px;">
                <canvas id="funnelStagesChart" class="w-full h-full"></canvas>
            </div>
        </div>

        <!-- Monthly Leads Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Leads por Mês</h3>
                {{ business_tooltip(
                    title="Leads por Mês",
                    description="Evolução temporal do número de novos leads cadastrados mensalmente, permitindo identificar tendências e sazonalidade.",
                    formula="COUNT(Lead_id) GROUP BY MONTH(Data_criacao_lead)",
                    columns="Lead_id, Data_criacao_lead",
                    data_source="base_dados.csv → Agrupamento de leads por mês da data de criação",
                    calculation_method="Contagem de leads agrupados por mês/ano da data de criação, ordenados cronologicamente"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Evolução mensal de novos leads</p>
            <div class="relative w-full" style="height: 300px;">
                <canvas id="monthlyLeadsChart" class="w-full h-full"></canvas>
            </div>
        </div>

        <!-- University Distribution Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Distribuição por Universidade</h3>
                {{ business_tooltip(
                    title="Distribuição por Universidade",
                    description="Distribuição percentual dos leads por universidade, mostrando as top 5 instituições com maior número de leads cadastrados.",
                    formula="COUNT(Lead_id) GROUP BY Universidade ORDER BY COUNT DESC LIMIT 5",
                    columns="Lead_id, Universidade",
                    data_source="base_dados.csv → Agrupamento de leads por universidade, limitado às 5 principais",
                    calculation_method="Contagem de leads por universidade, ordenados por quantidade decrescente, exibindo apenas as 5 principais"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Quantidade de leads por universidade</p>
            <div class="relative w-full" style="height: 300px;">
                <canvas id="universityDistributionChart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>

    <!-- Additional Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Conversion Rate Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Taxa de Conversão por Origem</h3>
                {{ business_tooltip(
                    title="Taxa de Conversão por Estado",
                    description="Percentual de conversão de leads por estado de origem, permitindo identificar regiões com melhor performance comercial.",
                    formula="(Implementações Finalizadas ÷ Total de Leads) × 100 GROUP BY Estado",
                    columns="Lead_id, Estado, Status_Implantacao",
                    data_source="base_dados.csv → Cálculo de conversão agrupado por estado",
                    calculation_method="Divisão do número de implementações finalizadas pelo total de leads por estado, multiplicado por 100"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Percentual de conversão de leads por canal de origem</p>
            <div class="relative w-full" style="height: 300px;">
                <canvas id="conversionRateChart" class="w-full h-full"></canvas>
            </div>
        </div>

        <!-- Course Distribution Chart -->
        <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
            <div class="flex items-center mb-1">
                <h3 class="text-base font-medium text-gray-900">Distribuição por Curso</h3>
                {{ business_tooltip(
                    title="Distribuição por Curso",
                    description="Top 10 cursos com maior número de leads cadastrados, permitindo identificar áreas de formação com maior interesse.",
                    formula="COUNT(Lead_id) GROUP BY Curso ORDER BY COUNT DESC LIMIT 10",
                    columns="Lead_id, Curso",
                    data_source="base_dados.csv → Agrupamento de leads por curso, limitado aos 10 principais",
                    calculation_method="Contagem de leads por curso, ordenados por quantidade decrescente, exibindo apenas os 10 principais"
                ) }}
            </div>
            <p class="text-sm text-gray-500 mb-4">Top 10 cursos com mais leads</p>
            <div class="relative w-full" style="height: 300px;">
                <canvas id="courseDistributionChart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>

    <!-- Leads Table Section -->
    <div class="mb-8">
        {{ section_header(
            title="Lista de Leads",
            subtitle="Visualize e gerencie todos os leads cadastrados"
        ) }}

        <!-- Filters -->
        {{ table_filters([
            {
                "id": "search-leads",
                "type": "search",
                "placeholder": "Buscar leads..."
            },
            {
                "id": "university-filter",
                "type": "select",
                "placeholder": "Todas as universidades",
                "options": []
            },
            {
                "id": "course-filter",
                "type": "select",
                "placeholder": "Todos os cursos",
                "options": []
            }
        ]) }}

        <!-- Leads Table with Fixed Height and Scrollbar -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
            <div class="overflow-x-auto">
                <div class="overflow-y-auto max-h-[500px]" id="leads-container">
                    <table class="min-w-full divide-y divide-gray-200" id="leads-table">
                        <thead class="bg-gray-100 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Nome</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Data de Criação</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Formação</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Universidade</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Curso</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Cidade/Estado</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Ações</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for lead in leads[:20] %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ lead['Nome do Lead']|e }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ lead['Data_criacao_lead']|e }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ lead['Formação Academica']|e }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 university-cell">{{ lead['Universidade']|e }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 course-cell">{{ lead['Curso']|e }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ lead['Cidade']|e }}/{{ lead['Estado']|e }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <a href="{{ url_for('lead.detail', lead_id=lead['Lead_id']) }}" class="text-primary hover:underline">Ver Detalhes</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Table Info -->
            <div class="bg-gray-50 px-6 py-3 text-sm text-gray-500 border-t border-gray-200">
                Exibindo 20 de {{ leads|length }} leads. Use os filtros acima para refinar a busca.
            </div>

            <!-- Pagination -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Anterior
                    </a>
                    <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Próximo
                    </a>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Mostrando <span class="font-medium">1</span> a <span class="font-medium">{{ leads|length }}</span> de <span class="font-medium">{{ leads|length }}</span> resultados
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Anterior</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                            <a href="#" aria-current="page" class="z-10 bg-primary border-primary text-white relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                1
                            </a>
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Próximo</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded for leads page');

        // Parse chart data safely
        let chartData = {};
        try {
            const rawChartData = '{{ chart_data|safe }}';
            console.log('Raw chart data:', rawChartData);

            if (rawChartData && rawChartData !== 'None' && rawChartData !== '' && rawChartData !== '{}') {
                chartData = JSON.parse(rawChartData);
                console.log('Chart data parsed successfully:', chartData);
            } else {
                console.warn('No chart data available for leads');
                chartData = {};
            }
        } catch (error) {
            console.error('Error parsing chart data:', error);
            console.error('Raw data was:', '{{ chart_data|safe }}');
            chartData = {};
        }

        // Initialize Funnel Stages Chart
        if (document.getElementById('funnelStagesChart')) {
            try {
                const funnelStagesCtx = document.getElementById('funnelStagesChart').getContext('2d');
                const funnelStagesData = chartData.funnel_stages || {};

                const labels = Object.keys(funnelStagesData);
                const values = Object.values(funnelStagesData).map(v => parseInt(v, 10));

                // If we have no data, show a message
                if (labels.length === 0 || values.length === 0) {
                    document.getElementById('funnelStagesChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de funil de vendas não disponíveis</div>';
                } else {
                    // Calculate percentages for each stage
                    const maxValue = Math.max(...values);
                    const percentages = values.map(v => ((v / maxValue) * 100).toFixed(1) + '%');

                    new Chart(funnelStagesCtx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'Leads',
                                data: values,
                                backgroundColor: 'rgba(59, 130, 246, 0.7)',
                                borderColor: 'rgba(59, 130, 246, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0
                                    }
                                }
                            },
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        afterLabel: function(context) {
                                            const index = context.dataIndex;
                                            const total = values.reduce((a, b) => a + b, 0);
                                            const percentage = Math.round((values[index] / total) * 100);
                                            return `${percentage}% do total`;
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            } catch (error) {
                console.error('Error creating funnel stages chart:', error);
                window.debugChart('funnelStagesChart', chartData.funnel_stages, error);
                document.getElementById('funnelStagesChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de funil de vendas</div>';
            }
        }

        // Initialize Monthly Leads Chart
        if (document.getElementById('monthlyLeadsChart')) {
            try {
                const monthlyLeadsCtx = document.getElementById('monthlyLeadsChart').getContext('2d');
                const monthlyOppsData = chartData.monthly_opps || {};

                // Sort the months chronologically
                const sortedMonths = Object.keys(monthlyOppsData).sort();
                const values = sortedMonths.map(month => parseInt(monthlyOppsData[month], 10));

                // Format month labels for display (YYYY-MM to MMM/YYYY)
                const formattedLabels = sortedMonths.map(month => {
                    try {
                        const [year, monthNum] = month.split('-');
                        const date = new Date(parseInt(year), parseInt(monthNum) - 1, 1);
                        return date.toLocaleDateString('pt-BR', { month: 'short', year: 'numeric' });
                    } catch (e) {
                        return month;
                    }
                });

                // If we have no data, show a message
                if (sortedMonths.length === 0 || values.length === 0) {
                    document.getElementById('monthlyLeadsChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de leads por mês não disponíveis</div>';
                } else {
                    new Chart(monthlyLeadsCtx, {
                        type: 'line',
                        data: {
                            labels: formattedLabels,
                            datasets: [{
                                label: 'Leads por Mês',
                                data: values,
                                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                                borderColor: 'rgba(59, 130, 246, 1)',
                                borderWidth: 2,
                                tension: 0.3,
                                fill: true
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0
                                    }
                                }
                            }
                        }
                    });
                }
            } catch (error) {
                console.error('Error creating monthly leads chart:', error);
                window.debugChart('monthlyLeadsChart', chartData.monthly_opps, error);
                document.getElementById('monthlyLeadsChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de leads por mês</div>';
            }
        }

        // Initialize University Distribution Chart
        if (document.getElementById('universityDistributionChart')) {
            try {
                const universityChartCtx = document.getElementById('universityDistributionChart').getContext('2d');
                const universityData = chartData.university_distribution || {};

                // Sort universities by count and get top 5
                const sortedUniversities = Object.entries(universityData)
                    .sort((a, b) => parseInt(b[1]) - parseInt(a[1]))
                    .slice(0, 5);

                const uniLabels = sortedUniversities.map(item => item[0]);
                const uniValues = sortedUniversities.map(item => parseInt(item[1]));

                // If we have no data, show a message
                if (uniLabels.length === 0 || uniValues.length === 0) {
                    document.getElementById('universityDistributionChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de distribuição por universidade não disponíveis</div>';
                } else {
                    new Chart(universityChartCtx, {
                        type: 'doughnut',
                        data: {
                            labels: uniLabels,
                            datasets: [{
                                data: uniValues,
                                backgroundColor: [
                                    'rgba(59, 130, 246, 0.8)',
                                    'rgba(59, 130, 246, 0.6)',
                                    'rgba(59, 130, 246, 0.4)',
                                    'rgba(156, 163, 175, 0.7)',
                                    'rgba(209, 213, 219, 0.7)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'right'
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            const label = context.label || '';
                                            const value = context.raw || 0;
                                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                            const percentage = Math.round((value / total) * 100);
                                            return `${label}: ${value} (${percentage}%)`;
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            } catch (error) {
                console.error('Error creating university distribution chart:', error);
                window.debugChart('universityDistributionChart', chartData.university_distribution, error);
                document.getElementById('universityDistributionChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de distribuição por universidade</div>';
            }
        }

        // Initialize Conversion Rate Chart
        if (document.getElementById('conversionRateChart')) {
            try {
                const conversionChartCtx = document.getElementById('conversionRateChart').getContext('2d');
                const stateConversionData = chartData.state_conversion || {};

                // Use real data from backend
                const convLabels = Object.keys(stateConversionData);
                const convValues = Object.values(stateConversionData).map(v => parseFloat(v));

                // If we have no data, show a message
                if (convLabels.length === 0 || convValues.length === 0) {
                    document.getElementById('conversionRateChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de conversão por estado não disponíveis</div>';
                } else {
                    new Chart(conversionChartCtx, {
                        type: 'bar',
                        data: {
                            labels: convLabels,
                            datasets: [{
                                label: 'Taxa de Conversão (%)',
                                data: convValues,
                                backgroundColor: 'rgba(156, 163, 175, 0.7)',
                                borderColor: 'rgba(107, 114, 128, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    ticks: {
                                        callback: function(value) {
                                            return value + '%';
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            } catch (error) {
                console.error('Error creating conversion rate chart:', error);
                window.debugChart('conversionRateChart', chartData.state_conversion, error);
                document.getElementById('conversionRateChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de taxa de conversão</div>';
            }
        }

        // Initialize Course Distribution Chart
        if (document.getElementById('courseDistributionChart')) {
            try {
                const courseChartCtx = document.getElementById('courseDistributionChart').getContext('2d');
                const courseData = chartData.course_distribution || {};

                // Sort courses by count and get top 10
                const sortedCourses = Object.entries(courseData)
                    .sort((a, b) => parseInt(b[1]) - parseInt(a[1]))
                    .slice(0, 10);

                const courseLabels = sortedCourses.map(item => item[0]);
                const courseValues = sortedCourses.map(item => parseInt(item[1]));

                // If we have no data, show a message
                if (courseLabels.length === 0 || courseValues.length === 0) {
                    document.getElementById('courseDistributionChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Dados de distribuição por curso não disponíveis</div>';
                } else {
                    new Chart(courseChartCtx, {
                        type: 'bar',
                        data: {
                            labels: courseLabels,
                            datasets: [{
                                label: 'Quantidade de Leads',
                                data: courseValues,
                                backgroundColor: 'rgba(59, 130, 246, 0.7)',
                                borderColor: 'rgba(59, 130, 246, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            indexAxis: 'y',
                            scales: {
                                x: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0
                                    }
                                }
                            },
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            const value = context.raw || 0;
                                            const total = courseValues.reduce((a, b) => a + b, 0);
                                            const percentage = Math.round((value / total) * 100);
                                            return `${value} leads (${percentage}%)`;
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            } catch (error) {
                console.error('Error creating course distribution chart:', error);
                window.debugChart('courseDistributionChart', chartData.course_distribution, error);
                document.getElementById('courseDistributionChart').innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">Erro ao criar gráfico de distribuição por curso</div>';
            }
        }

        // Initialize table filters
        const searchInput = document.getElementById('search-leads');
        const universityFilter = document.getElementById('university-filter');
        const courseFilter = document.getElementById('course-filter');
        const rows = document.querySelectorAll('#leads-table tbody tr');

        // Populate university filter
        const universities = new Set();
        document.querySelectorAll('.university-cell').forEach(cell => {
            if (cell.textContent.trim()) {
                universities.add(cell.textContent.trim());
            }
        });

        universities.forEach(university => {
            const option = document.createElement('option');
            option.value = university;
            option.textContent = university;
            universityFilter.appendChild(option);
        });

        // Populate course filter
        const courses = new Set();
        document.querySelectorAll('.course-cell').forEach(cell => {
            if (cell.textContent.trim()) {
                courses.add(cell.textContent.trim());
            }
        });

        courses.forEach(course => {
            const option = document.createElement('option');
            option.value = course;
            option.textContent = course;
            courseFilter.appendChild(option);
        });

        // Filter function
        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const selectedUniversity = universityFilter.value;
            const selectedCourse = courseFilter.value;

            rows.forEach(row => {
                const name = row.querySelector('td:nth-child(1)').textContent.toLowerCase();
                const university = row.querySelector('.university-cell').textContent.trim();
                const course = row.querySelector('.course-cell').textContent.trim();

                const matchesSearch = name.includes(searchTerm);
                const matchesUniversity = !selectedUniversity || university === selectedUniversity;
                const matchesCourse = !selectedCourse || course === selectedCourse;

                row.style.display = matchesSearch && matchesUniversity && matchesCourse ? '' : 'none';
            });
        }

        // Add event listeners
        searchInput.addEventListener('input', filterTable);
        universityFilter.addEventListener('change', filterTable);
        courseFilter.addEventListener('change', filterTable);
    });
</script>
{% endblock %}
