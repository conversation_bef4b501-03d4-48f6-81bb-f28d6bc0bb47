{% extends "base.html" %}
{% from "macros/components.html" import kpi_card, stat_card, insight_card, chart, section_header, executive_summary, hero_section %}

{% block title %}Detalhes do Lead - {{ app_name|e }}{% endblock %}

{% block content %}
<!-- Hero Section -->
{{ hero_section(
    title=lead['Nome do Lead'],
    subtitle="Detalhes completos do lead e suas oportunidades associadas.",
    stats=[
        {
            "label": "Oportunidades",
            "value": opportunities|length,
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />'
        },
        {
            "label": "Status",
            "value": "Ativo" if opportunities|length > 0 else "Inativo",
            "icon": '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />'
        }
    ],
    bg_class="bg-gradient-to-r from-blue-50 to-indigo-50"
) }}

<div class="w-full px-4 sm:px-6 lg:px-8 py-8">
    <!-- Back Button -->
    <div class="mb-6">
        <a href="{{ url_for('lead.index') }}" class="inline-flex items-center text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Voltar para a lista de leads
        </a>
    </div>

    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- KPI: Total de Oportunidades -->
        {{ kpi_card(
            title="Total de Oportunidades",
            value=opportunities|length,
            subtitle="Oportunidades associadas a este lead",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />',
            color="primary"
        ) }}

        <!-- KPI: Status do Lead -->
        {{ kpi_card(
            title="Status do Lead",
            value="Ativo" if opportunities|length > 0 else "Inativo",
            subtitle="Baseado na existência de oportunidades",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />',
            color=("success" if opportunities|length > 0 else "warning")
        ) }}

        <!-- KPI: Última Atividade -->
        {{ kpi_card(
            title="Última Atividade",
            value=lead['Data_criacao_lead'],
            subtitle="Data da última interação registrada",
            icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />',
            color="tertiary"
        )|e }}

        <!-- KPI: Universidade -->
        {{ kpi_card(
            title="Universidade",
            value=lead['Universidade'],
            subtitle="Instituição de ensino do lead",
            icon='<path d="M12 14l9-5-9-5-9 5 9 5z" /><path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />',
            color="secondary"
        )|e }}
    </div>

    <!-- Lead Information -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Lead Details Card -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 md:col-span-2">
            {{ section_header(
                title="Informações do Lead",
                subtitle="Dados pessoais e acadêmicos"
            )|e }}

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 mb-1">Nome Completo</p>
                    <p class="text-base font-semibold text-gray-900">{{ lead['Nome do Lead']|e }}</p>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 mb-1">Data de Cadastro</p>
                    <p class="text-base font-semibold text-gray-900">{{ lead['Data_criacao_lead']|e }}</p>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 mb-1">Universidade</p>
                    <p class="text-base font-semibold text-gray-900">{{ lead['Universidade']|e }}</p>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 mb-1">Curso</p>
                    <p class="text-base font-semibold text-gray-900">{{ lead['Curso']|e }}</p>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 mb-1">Formação Acadêmica</p>
                    <p class="text-base font-semibold text-gray-900">{{ lead['Formação Academica']|e }}</p>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 mb-1">Localização</p>
                    <p class="text-base font-semibold text-gray-900">{{ lead['Cidade']|e }}/{{ lead['Estado']|e }}</p>
                </div>

                {% if lead['Email'] %}
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 mb-1">Email</p>
                    <p class="text-base font-semibold text-gray-900">{{ lead['Email']|e }}</p>
                </div>
                {% endif %}

                {% if lead['Telefone'] %}
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 mb-1">Telefone</p>
                    <p class="text-base font-semibold text-gray-900">{{ lead['Telefone']|e }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Lead Status and Insights -->
        <div>
            <!-- Lead Status Card -->
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Status do Lead</h2>

                <div class="mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-{{ 'green' if opportunities|length > 0 else 'yellow' }}-100 flex items-center justify-center mr-3">
                            <svg class="w-6 h-6 text-{{ 'green' if opportunities|length > 0 else 'yellow' }}-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                {% if opportunities|length > 0 %}
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                {% else %}
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                {% endif %}
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Status Atual</p>
                            <p class="text-xl font-bold text-gray-900">{{ "Ativo" if opportunities|length > 0 else "Inativo" }}</p>
                        </div>
                    </div>
                </div>

                {% if opportunities|length > 0 %}
                <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 mb-1">Última Etapa do Funil</p>
                    <p class="text-base font-semibold text-gray-900">{{ opportunities[0]['Etapa do funil Comercial']|e }}</p>
                </div>

                <div class="p-4 bg-gray-50 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 mb-1">Responsável</p>
                    <p class="text-base font-semibold text-gray-900">{{ opportunities[0]['Nome_Responsavel']|e }}</p>
                </div>
                {% else %}
                <div class="p-4 bg-yellow-50 rounded-lg text-yellow-800 flex items-start">
                    <svg class="w-5 h-5 mr-2 mt-0.5 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <div>
                        <p class="font-medium mb-1">Lead sem oportunidades</p>
                        <p class="text-sm">Este lead ainda não possui nenhuma oportunidade associada. Considere criar uma nova oportunidade para avançar no processo de vendas.</p>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Próximos Passos Card -->
            <div class="bg-white rounded-lg p-4 border border-gray-100 shadow-sm">
                <div class="flex items-center mb-3">
                    <div class="w-6 h-6 rounded-full bg-blue-50 flex items-center justify-center mr-2">
                        <svg class="w-3 h-3 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-semibold text-gray-900">Próximos Passos</span>
                </div>

                <p class="text-sm text-gray-600 mb-3">
                    {% if opportunities|length > 0 %}
                        Acompanhe a oportunidade na etapa <strong>{{ opportunities[0]['Etapa do funil Comercial']|e }}</strong> e trabalhe para avançar no funil de vendas.
                    {% else %}
                        Crie uma nova oportunidade para este lead e inicie o processo de vendas.
                    {% endif %}
                </p>

                <div class="flex flex-wrap gap-1 mb-2">
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2 py-0.5 rounded-full">Lead</span>
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2 py-0.5 rounded-full">Oportunidade</span>
                    <span class="inline-block bg-gray-100 text-gray-700 text-xs font-medium px-2 py-0.5 rounded-full">Vendas</span>
                </div>

                <p class="text-xs text-gray-500 italic">💡 Mantenha contato regular com o lead para aumentar as chances de conversão.</p>
            </div>
        </div>
    </div>

    <!-- Opportunities Section -->
    {% if opportunities|length > 0 %}
    <div class="mb-8">
        {{ section_header(
            title="Oportunidades",
            subtitle="Lista de oportunidades associadas a este lead"
        )|e }}

        <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 mt-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-100">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Data de Criação</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Etapa do Funil</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Produto</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Valor Mensal</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Responsável</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Ações</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for opp in opportunities %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ opp['Oportunidade_id']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ opp['Data_criacao_Oportunidade']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ opp['Etapa do funil Comercial']|e }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ opp['Produto']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ opp['Valor Mensalidade']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ opp['Nome_Responsavel']|e }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <a href="{{ url_for('opportunity.detail', opp_id=opp['Oportunidade_id']) }}" class="text-primary hover:text-blue-700 font-medium flex items-center">
                                    Ver Detalhes
                                    <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <!-- No Opportunities Message -->
    <div class="bg-white rounded-lg shadow-sm p-8 border border-gray-200 mb-8 text-center">
        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma oportunidade encontrada</h3>
        <p class="text-gray-500 max-w-md mx-auto mb-6">Este lead ainda não possui nenhuma oportunidade associada. Crie uma nova oportunidade para iniciar o processo de vendas.</p>
        <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
            <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Criar Oportunidade
        </button>
    </div>
    {% endif %}
</div>
{% endblock %}
