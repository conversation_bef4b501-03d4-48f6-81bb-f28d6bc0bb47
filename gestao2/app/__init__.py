"""
Amigo DataHub - Application Factory
"""

import os
import logging
from flask import Flask
from app.config import config_by_name
from app.security import SecurityManager

def create_app(config_name='development'):
    """
    Application factory function that creates and configures the Flask application

    Args:
        config_name (str): The configuration to use (development, production, testing)

    Returns:
        Flask: The configured Flask application
    """
    app = Flask(__name__)

    # Load configuration
    app.config.from_object(config_by_name[config_name])

    # Override with environment variables for Docker
    app.config['APP_NAME'] = os.getenv('APP_NAME', app.config.get('APP_NAME', 'DataHub Amigo One - Negócios'))
    app.config['APP_VERSION'] = os.getenv('APP_VERSION', app.config.get('APP_VERSION', '1.0.0'))
    app.config['DOMAIN_TYPE'] = os.getenv('DOMAIN_TYPE', app.config.get('DOMAIN_TYPE', 'business'))

    # Ensure SECRET_KEY is set from environment variables
    secret_key = os.getenv('SECRET_KEY')
    if secret_key:
        app.config['SECRET_KEY'] = secret_key
    elif not app.config.get('SECRET_KEY'):
        # Set a default SECRET_KEY for development if none is provided
        app.config['SECRET_KEY'] = 'dev-secret-key-change-in-production-12345678901234567890'



    # Configure data sources
    data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'sources')
    excel_file = os.path.join(data_dir, 'base_dados.xlsx')
    csv_file = os.path.join(data_dir, 'base_dados.csv')

    # Set data file based on availability
    if os.path.exists(excel_file):
        app.config['DATA_FILE'] = excel_file
        app.config['DATA_SOURCE'] = 'xlsx'
        app.logger.info(f"Using Excel data source: {excel_file}")
    elif os.path.exists(csv_file):
        app.config['DATA_FILE'] = csv_file
        app.config['DATA_SOURCE'] = 'csv'
        app.logger.info(f"Using CSV data source: {csv_file}")
    else:
        app.logger.warning("No data files found. Database connection will be required.")
        app.config['DATA_SOURCE'] = 'database'

    # Configure logging
    configure_logging(app)

    # Register extensions
    register_extensions(app)

    # Register blueprints
    register_blueprints(app)

    # Register error handlers
    register_error_handlers(app)

    return app

def configure_logging(app):
    """Configure logging for the application"""
    log_level = app.config.get('LOG_LEVEL', logging.INFO)

    # Create a formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Configure the root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # Create a console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)

    # Add the console handler to the root logger
    root_logger.addHandler(console_handler)

    # Create a file handler if LOG_FILE is specified
    log_file = app.config.get('LOG_FILE')
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    # Create a logger for the app
    app_logger = logging.getLogger('amigo_datahub')
    app_logger.setLevel(log_level)

    app.logger.info('Logging configured')

def register_extensions(app):
    """Register Flask extensions"""
    from app.extensions import init_extensions
    init_extensions(app)

    # Initialize security manager - SECURITY: CSRF protection enabled
    security_manager = SecurityManager()
    security_manager.init_app(app)
    app.extensions['security_manager'] = security_manager
    app.logger.info("Security manager initialized with CSRF protection")

def register_blueprints(app):
    """Register Flask blueprints"""
    # Initialize activity logger middleware (with error handling)
    try:
        from app.middleware.activity_logger import activity_logger
        activity_logger.init_app(app)
        app.logger.info("Activity logger middleware initialized successfully")
    except Exception as e:
        app.logger.warning(f"Could not initialize activity logger: {e}")

    from app.controllers.dashboard import dashboard_bp
    from app.controllers.lead import lead_bp
    from app.controllers.opportunity import opportunity_bp
    from app.controllers.implementation import implementation_bp
    from app.controllers.university import university_bp, university_api_bp
    from app.controllers.conversion import conversion_bp
    from app.controllers.subscription import subscription_bp
    from app.controllers.class_controller import class_bp
    from app.controllers.responsible_controller import responsible_bp
    from app.controllers.coupon_controller import coupon_bp
    from app.controllers.business_rules_controller import business_rules_bp
    from app.controllers.database_quality_controller import database_quality_bp
    from app.controllers.commercial_intelligence_controller import ci_bp
    from app.controllers.dynamic_clustering_controller import clustering_bp
    from app.controllers.churn_controller import cancellation_bp
    from app.controllers.profile_controller import profile_bp
    from app.controllers.go_to_market_controller import gtm_bp
    from app.controllers.active_users_controller import active_users_bp
    from app.controllers.intelligence_controller import intelligence_bp
    from app.controllers.health_controller import health_bp

    # Import auth routes
    try:
        from app.routes.auth_routes import auth_routes
        AUTH_ROUTES_AVAILABLE = True
    except ImportError:
        AUTH_ROUTES_AVAILABLE = False

    # Import admin routes
    try:
        from app.routes.admin_routes import admin_bp
        ADMIN_ROUTES_AVAILABLE = True
    except ImportError:
        ADMIN_ROUTES_AVAILABLE = False

    # Import admin API
    try:
        from app.api.admin_api import admin_api
        ADMIN_API_AVAILABLE = True
    except ImportError:
        ADMIN_API_AVAILABLE = False

    app.register_blueprint(dashboard_bp)
    app.register_blueprint(lead_bp)
    app.register_blueprint(opportunity_bp)
    app.register_blueprint(implementation_bp)
    app.register_blueprint(university_bp)
    app.register_blueprint(university_api_bp)  # Register API blueprint
    app.register_blueprint(conversion_bp)
    app.register_blueprint(subscription_bp)
    app.register_blueprint(class_bp)  # Register class blueprint
    app.register_blueprint(responsible_bp)  # Register responsible blueprint
    app.register_blueprint(coupon_bp)  # Register coupon blueprint
    app.register_blueprint(business_rules_bp)  # Register business rules blueprint
    app.register_blueprint(database_quality_bp)  # Register database quality blueprint
    app.register_blueprint(ci_bp)  # Register commercial intelligence blueprint
    app.register_blueprint(clustering_bp)  # Register dynamic clustering blueprint
    app.register_blueprint(cancellation_bp)  # Register cancellation analysis blueprint
    app.register_blueprint(profile_bp)  # Register profile blueprint
    app.register_blueprint(gtm_bp)  # Register Go-to-Market blueprint
    app.register_blueprint(active_users_bp)  # Register active users blueprint
    app.register_blueprint(intelligence_bp)  # Register intelligence index blueprint
    app.register_blueprint(health_bp)  # Register health check blueprint

    # Register auth routes if available
    if AUTH_ROUTES_AVAILABLE:
        app.register_blueprint(auth_routes)
        app.logger.info("Auth routes registered successfully")

    # Register admin routes if available
    if ADMIN_ROUTES_AVAILABLE:
        app.register_blueprint(admin_bp)
        app.logger.info("Admin routes registered successfully")

    # Register admin API if available
    if ADMIN_API_AVAILABLE:
        app.register_blueprint(admin_api)
        app.logger.info("Admin API registered successfully")

    # Disable external permissions middleware to avoid conflicts
    PERMISSIONS_AVAILABLE = False
    app.logger.info("External permissions middleware disabled to avoid conflicts")

    # Add authentication middleware
    setup_auth_middleware(app)

def setup_auth_middleware(app):
    """Setup authentication middleware"""
    from flask import session, request, redirect, url_for

    @app.before_request
    def require_login():
        # Public endpoints that don't require authentication
        public_endpoints = [
            'auth.login',
            'static',
            'auth_api.login',  # API login endpoint
            'admin_api.test_get_users',  # Temporary test endpoint
            'clustering.api_cluster',  # Clustering API
            'clustering.api_turma_clustering_summary',  # Turma clustering summary
            'clustering.api_cluster_turmas',  # Cluster turmas API
            'clustering.api_turma_cluster',  # Turma cluster API
            'admin_api.invite_user',  # Admin invite user API
        ]

        # Check if current endpoint is public
        if request.endpoint in public_endpoints:
            return

        # Check if user is authenticated
        if 'user_id' not in session and 'username' not in session:
            # Redirect to login page
            return redirect(url_for('auth.login'))

def register_error_handlers(app):
    """Register error handlers"""
    from flask import render_template

    @app.errorhandler(403)
    def access_denied(e):
        return render_template('errors/403.html'), 403

    @app.errorhandler(404)
    def page_not_found(e):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_server_error(e):
        return render_template('errors/500.html'), 500
