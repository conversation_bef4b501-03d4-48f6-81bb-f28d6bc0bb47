"""
Amigo DataHub - Simplified Application Factory
"""

import os
import logging
from flask import Flask
from app.config import config_by_name

def register_template_filters(app):
    """Register custom template filters"""

    @app.template_filter('currency')
    def currency_filter(value):
        """Format value as currency"""
        try:
            if value is None:
                return 'R$ 0,00'
            return f'R$ {float(value):,.2f}'.replace(',', 'X').replace('.', ',').replace('X', '.')
        except (ValueError, TypeError):
            return 'R$ 0,00'

    @app.template_filter('percentage')
    def percentage_filter(value):
        """Format value as percentage"""
        try:
            if value is None:
                return '0%'
            return f'{float(value):.1f}%'
        except (ValueError, TypeError):
            return '0%'

    @app.template_filter('number')
    def number_filter(value):
        """Format value as number"""
        try:
            if value is None:
                return '0'
            return f'{int(value):,}'.replace(',', '.')
        except (ValueError, TypeError):
            return '0'

def create_app(config_name='development'):
    """
    Simplified application factory function that creates and configures the Flask application

    Args:
        config_name (str): The configuration to use (development, production, testing)

    Returns:
        Flask: The configured Flask application
    """
    app = Flask(__name__)

    # Load configuration
    app.config.from_object(config_by_name[config_name])

    # Override with environment variables for Docker
    app.config['APP_NAME'] = os.getenv('APP_NAME', app.config.get('APP_NAME', 'DataHub Amigo One - Negócios'))
    app.config['APP_VERSION'] = os.getenv('APP_VERSION', app.config.get('APP_VERSION', '1.0.0'))
    app.config['DOMAIN_TYPE'] = os.getenv('DOMAIN_TYPE', app.config.get('DOMAIN_TYPE', 'business'))

    # Ensure SECRET_KEY is set from environment variables
    secret_key = os.getenv('SECRET_KEY')
    if secret_key:
        app.config['SECRET_KEY'] = secret_key
    elif not app.config.get('SECRET_KEY'):
        # Set a default SECRET_KEY for development if none is provided
        app.config['SECRET_KEY'] = 'dev-secret-key-change-in-production-12345678901234567890'

    # Configure data sources
    data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'sources')
    excel_file = os.path.join(data_dir, 'base_dados.xlsx')
    csv_file = os.path.join(data_dir, 'base_dados.csv')

    # Set data file based on availability
    if os.path.exists(excel_file):
        app.config['DATA_FILE'] = excel_file
        app.config['DATA_SOURCE'] = 'xlsx'
        app.logger.info(f"Using Excel data source: {excel_file}")
    elif os.path.exists(csv_file):
        app.config['DATA_FILE'] = csv_file
        app.config['DATA_SOURCE'] = 'csv'
        app.logger.info(f"Using CSV data source: {csv_file}")
    else:
        app.logger.warning("No data files found. Database connection will be required.")
        app.config['DATA_SOURCE'] = 'database'

    # Configure logging
    configure_logging(app)

    # Register basic blueprints
    register_blueprints(app)

    # Register error handlers
    register_error_handlers(app)

    # Register custom template filters
    register_template_filters(app)

    return app

def configure_logging(app):
    """Configure logging for the application"""
    log_level = app.config.get('LOG_LEVEL', logging.INFO)

    # Create a formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Configure the root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # Create a console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)

    # Add the console handler to the root logger
    root_logger.addHandler(console_handler)

    # Create a file handler if LOG_FILE is specified
    log_file = app.config.get('LOG_FILE')
    if log_file:
        try:
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(log_level)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        except Exception as e:
            app.logger.warning(f"Could not create file handler: {e}")

    app.logger.info('Logging configured')

def register_blueprints(app):
    """Register essential Flask blueprints only"""
    try:
        # Register only essential controllers
        from app.controllers.dashboard import dashboard_bp
        app.register_blueprint(dashboard_bp)
        app.logger.info("Dashboard blueprint registered")
    except ImportError as e:
        app.logger.warning(f"Could not register dashboard blueprint: {e}")

    try:
        from app.controllers.lead import lead_bp
        app.register_blueprint(lead_bp)
        app.logger.info("Lead blueprint registered")
    except ImportError as e:
        app.logger.warning(f"Could not register lead blueprint: {e}")

    try:
        from app.controllers.opportunity import opportunity_bp
        app.register_blueprint(opportunity_bp)
        app.logger.info("Opportunity blueprint registered")
    except ImportError as e:
        app.logger.warning(f"Could not register opportunity blueprint: {e}")

    try:
        from app.controllers.implementation import implementation_bp
        app.register_blueprint(implementation_bp)
        app.logger.info("Implementation blueprint registered")
    except ImportError as e:
        app.logger.warning(f"Could not register implementation blueprint: {e}")

    try:
        from app.controllers.health_controller import health_bp
        app.register_blueprint(health_bp)
        app.logger.info("Health blueprint registered")
    except ImportError as e:
        app.logger.warning(f"Could not register health blueprint: {e}")

def register_error_handlers(app):
    """Register basic error handlers"""
    from flask import render_template, jsonify

    @app.errorhandler(404)
    def page_not_found(e):
        try:
            return render_template('errors/404.html'), 404
        except:
            return jsonify({'error': 'Page not found'}), 404

    @app.errorhandler(500)
    def internal_server_error(e):
        try:
            return render_template('errors/500.html'), 500
        except:
            return jsonify({'error': 'Internal server error'}), 500
