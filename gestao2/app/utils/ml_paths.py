"""
Helper para gerenciar caminhos dos arquivos de ML
"""

from pathlib import Path
from typing import Dict, Any

class MLPathManager:
    """Gerenciador de caminhos para arquivos de ML"""

    def __init__(self):
        self.base_path = Path(__file__).parent.parent.parent
        self.ml_path = self.base_path / 'data' / 'ML'

        # Estrutura de diretórios
        self.datasets_path = self.ml_path / 'datasets'
        self.models_path = self.ml_path / 'models'
        self.results_path = self.ml_path / 'results'
        self.stats_path = self.ml_path / 'stats'

    def get_dataset_path(self, filename: str) -> Path:
        """Obter caminho para arquivo de dataset"""
        return self.datasets_path / filename

    def get_model_path(self, filename: str) -> Path:
        """Obter caminho para arquivo de modelo"""
        return self.models_path / filename

    def get_results_path(self, filename: str) -> Path:
        """Obter caminho para arquivo de resultados"""
        return self.results_path / filename

    def get_stats_path(self, filename: str) -> Path:
        """Obter caminho para arquivo de estatísticas"""
        return self.stats_path / filename

    def ensure_directories(self) -> None:
        """Garantir que todos os diretórios existem"""
        for path in [self.datasets_path, self.models_path, self.results_path, self.stats_path]:
            path.mkdir(parents=True, exist_ok=True)

    def get_all_paths(self) -> Dict[str, Path]:
        """Obter todos os caminhos importantes"""
        return {
            'base': self.base_path,
            'ml': self.ml_path,
            'datasets': self.datasets_path,
            'models': self.models_path,
            'results': self.results_path,
            'stats': self.stats_path
        }

# Instância global
ml_paths = MLPathManager()
