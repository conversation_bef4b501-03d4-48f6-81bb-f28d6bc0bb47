"""
Amigo DataHub - Safe Operations
Utility functions for safe operations
"""

import logging
from typing import Dict, List, Any, Optional, Union, Tuple

logger = logging.getLogger(__name__)

def safe_division(numerator: List[Any], denominator: List[Any], default: List[Any] = 0) -> Any:
    """
    Safely divide two numbers
    
    Args:
        numerator: The numerator
        denominator: The denominator
        default: The default value to return if division fails
        
    Returns:
        float: The result of the division or the default value
    """
    try:
        if denominator == 0:
            return default
        return numerator / denominator
    except Exception as e:
        logger.error(f"Error in division: {e}")
        return default

def safe_percentage(part: List[Any], total: List[Any], default: List[Any] = 0) -> Any:
    """
    Safely calculate a percentage
    
    Args:
        part: The part
        total: The total
        default: The default value to return if calculation fails
        
    Returns:
        float: The percentage or the default value
    """
    try:
        if total == 0:
            return default
        return (part / total) * 100
    except Exception as e:
        logger.error(f"Error calculating percentage: {e}")
        return default

def safe_get(dictionary: Dict[str, Any], key: List[Any], default: List[Any] = None) -> Any:
    """
    Safely get a value from a dictionary
    
    Args:
        dictionary: The dictionary
        key: The key
        default: The default value to return if key is not found
        
    Returns:
        The value or the default value
    """
    try:
        if dictionary is None:
            return default
        return dictionary.get(key, default)
    except Exception as e:
        logger.error(f"Error getting value from dictionary: {e}")
        return default

def safe_list_get(lst: Dict[str, Any], idx: int, default: List[Any] = None) -> Any:
    """
    Safely get a value from a list
    
    Args:
        lst: The list
        idx: The index
        default: The default value to return if index is out of range
        
    Returns:
        The value or the default value
    """
    try:
        if lst is None:
            return default
        if idx < 0 or idx >= len(lst):
            return default
        return lst[idx]
    except Exception as e:
        logger.error(f"Error getting value from list: {e}")
        return default

def safe_object(obj: List[Any], default: List[Any] = None) -> Any:
    """
    Safely return an object
    
    Args:
        obj: The object
        default: The default value to return if object is None
        
    Returns:
        The object or the default value
    """
    try:
        if obj is None:
            return default if default is not None else {}
        return obj
    except Exception as e:
        logger.error(f"Error accessing object: {e}")
        return default if default is not None else {}
