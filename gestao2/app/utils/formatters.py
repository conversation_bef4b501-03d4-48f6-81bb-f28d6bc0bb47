"""
Amigo DataHub - Formatters
Utility functions for formatting data
"""

import logging
import math
from datetime import datetime
from typing import Dict, List, Any, Optional, Union, Tuple

logger = logging.getLogger(__name__)

def safe_convert_to_string(value: Any) -> str:
    """
    Safely convert a value to string

    Args:
        value: The value to convert

    Returns:
        str: The converted string
    """
    try:
        if value is None:
            return "0"
        if isinstance(value, (int, float)):
            return str(value)
        return str(value)
    except Exception as e:
        logger.error(f"Error converting value to string: {e}")
        return "0"

def safe_convert_to_int(value: Any) -> int:
    """
    Safely convert a value to integer

    Args:
        value: The value to convert

    Returns:
        int: The converted integer
    """
    try:
        if value is None:
            return 0
        if isinstance(value, str):
            # Remove non-numeric characters
            value = ''.join(c for c in value if c.isdigit() or c == '.' or c == ',')
            # Replace comma with dot
            value = value.replace(',', '.')
        return int(float(value))
    except (ValueError, TypeError) as e:
        logger.error(f"Error converting value to int: {e}")
        return 0

def safe_convert_to_float(value: Any) -> Any:
    """
    Safely convert a value to float

    Args:
        value: The value to convert

    Returns:
        float: The converted float
    """
    try:
        # Check for None or NaN values
        if value is None:
            return 0.0
        if isinstance(value, float) and (math.isnan(value) or value != value):
            return 0.0

        if isinstance(value, str):
            # Check for empty string
            if not value.strip():
                return 0.0

            # Remove non-numeric characters except dot and comma
            value = ''.join(c for c in value if c.isdigit() or c == '.' or c == ',' or c == '-')
            # Replace comma with dot
            value = value.replace(',', '.')

            # If the string is empty after cleaning, return 0
            if not value or value == '-':
                return 0.0

        return float(value)
    except (ValueError, TypeError) as e:
        logger.error(f"Error converting value to float: {e}")
        return 0.0

def format_currency(value: Any) -> str:
    """
    Format a value as currency (R$)

    Args:
        value: The value to format

    Returns:
        str: The formatted currency string
    """
    try:
        # Check for None or NaN values
        if value is None or (isinstance(value, float) and (math.isnan(value) or value != value)):
            return "R$ 0,00"

        # If it's already a string and starts with R$, return it as is
        if isinstance(value, str) and value.strip().startswith("R$"):
            return value.strip()

        value = safe_convert_to_float(value)
        return f"R$ {value:,.2f}".replace(',', '.').replace('.', ',', 1)
    except (ValueError, TypeError) as e:
        logger.error(f"Error formatting currency: {e}")
        return "R$ 0,00"

def format_percentage(value: Any, decimals: List[Any] = 1) -> str:
    """
    Format a value as percentage

    Args:
        value: The value to format
        decimals: Number of decimal places

    Returns:
        str: The formatted percentage string
    """
    try:
        value = safe_convert_to_float(value)
        return f"{value:.{decimals}f}%".replace('.', ',')
    except (ValueError, TypeError) as e:
        logger.error(f"Error formatting percentage: {e}")
        return "0,0%"

def format_date(date_value: Any, format_str: List[Any] = '%d/%m/%Y') -> str:
    """
    Format a date value

    Args:
        date_value: The date to format
        format_str: The format string

    Returns:
        str: The formatted date string
    """
    try:
        # Check for None, NaN, or NaT values
        if date_value is None or (isinstance(date_value, float) and (math.isnan(date_value) or date_value != date_value)):
            return 'Não informado'

        if isinstance(date_value, str):
            # Check for empty string
            if not date_value.strip():
                return 'Não informado'

            try:
                date_value = datetime.strptime(date_value, '%Y-%m-%d')
            except ValueError:
                try:
                    date_value = datetime.strptime(date_value, '%d/%m/%Y')
                except ValueError:
                    # If we can't parse the date, return the original string
                    return date_value

        # Check if it's a valid datetime object
        if hasattr(date_value, 'strftime'):
            return date_value.strftime(format_str)
        else:
            return 'Não informado'
    except Exception as e:
        logger.error(f"Error formatting date: {e}")
        return 'Não informado'

def convert_dict_values_to_string(data_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert all values in a dictionary to strings

    Args:
        data_dict: The dictionary to convert

    Returns:
        dict: The dictionary with string values
    """
    if not isinstance(data_dict, dict):
        return {}

    # Ensure all values are converted to strings
    result = {}
    for k, v in data_dict.items():
        if isinstance(v, float) and (math.isnan(v) or v != v):
            # Handle NaN values
            result[k] = "0"
        elif isinstance(v, (int, float)):
            # Convert numbers directly to string
            result[k] = str(v)
        elif isinstance(v, dict):
            # If it's a nested dictionary, process recursively
            result[k] = convert_dict_values_to_string(v)
        elif v is None:
            # If it's None, use empty string or "0"
            result[k] = "0"
        else:
            # For other types, use the helper function
            result[k] = safe_convert_to_string(v)

    # Additional check to ensure all values are strings
    for k, v in list(result.items()):
        if not isinstance(v, str) and not isinstance(v, dict):
            logger.warning(f"Non-string value found in {k}: {v} (type: {type(v)})")
            # Force conversion to string, handling NaN values
            if isinstance(v, float) and (math.isnan(v) or v != v):
                result[k] = "0"
            else:
                result[k] = str(v)

    return result

def prepare_chart_data(data_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare data for charts, ensuring all values are properly formatted

    Args:
        data_dict: The dictionary to prepare

    Returns:
        dict: The prepared dictionary
    """
    if not isinstance(data_dict, dict):
        return {}

    result = {}
    for key, value in data_dict.items():
        if isinstance(value, dict):
            # Process nested dictionaries
            result[key] = prepare_nested_dict(value)
        elif isinstance(value, list):
            # Handle lists (e.g., for arrays of values)
            result[key] = prepare_list_values(value)
        elif isinstance(value, float) and (math.isnan(value) or value != value):
            # Handle NaN values
            result[key] = 0
        elif isinstance(value, (int, float)):
            # Keep numbers as numbers
            result[key] = value
        else:
            # Use the helper function for other types
            result[key] = safe_convert_to_string(value)

    return result

def prepare_nested_dict(data_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare a nested dictionary for chart data

    Args:
        data_dict: The dictionary to prepare

    Returns:
        dict: The prepared dictionary
    """
    if not isinstance(data_dict, dict):
        return {}

    result = {}
    for k, v in data_dict.items():
        if isinstance(v, dict):
            # Recursively process nested dictionaries
            result[k] = prepare_nested_dict(v)
        elif isinstance(v, list):
            # Process lists
            result[k] = prepare_list_values(v)
        elif isinstance(v, float) and (math.isnan(v) or v != v):
            # Handle NaN values
            result[k] = 0
        elif isinstance(v, (int, float)):
            # Keep numbers as numbers
            result[k] = v
        else:
            # Convert other types to string
            result[k] = safe_convert_to_string(v)

    return result

def prepare_list_values(value_list: Any) -> List[Any]:
    """
    Prepare a list of values for chart data

    Args:
        value_list: The list to prepare

    Returns:
        list: The prepared list
    """
    if not isinstance(value_list, list):
        return []

    result = []
    for item in value_list:
        if isinstance(item, dict):
            # Process dictionaries in lists
            result.append(prepare_nested_dict(item))
        elif isinstance(item, list):
            # Recursively process nested lists
            result.append(prepare_list_values(item))
        elif isinstance(item, float) and (math.isnan(item) or item != item):
            # Handle NaN values
            result.append(0)
        elif isinstance(item, (int, float)):
            # Keep numbers as numbers
            result.append(item)
        else:
            # Convert other types to string
            result.append(safe_convert_to_string(item))

    return result
