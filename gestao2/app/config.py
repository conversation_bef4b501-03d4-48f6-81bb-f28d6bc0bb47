"""
Amigo DataHub - Business Domain Configuration
Uses shared DataMesh Core configuration
"""

import os
import sys
import logging
from pathlib import Path

# Add shared core to Python path
CORE_PATH = Path(__file__).parent.parent.parent / 'shared' / 'datamesh-core'
sys.path.insert(0, str(CORE_PATH))

try:
    from config.base_config import BusinessDomainConfig, get_config
    CORE_AVAILABLE = True
except ImportError:
    CORE_AVAILABLE = False
    logging.warning("DataMesh Core not available, using fallback configuration")

# Application root directory (always needed)
APP_ROOT = Path(__file__).parent.parent.absolute()

if CORE_AVAILABLE:
    class Config(BusinessDomainConfig):
        """Business Domain Configuration extending shared core"""
        # Override with local paths for business domain
        APP_ROOT = APP_ROOT
        DATA_DIR = os.path.join(APP_ROOT, 'data', 'sources')
        DATA_FILE = os.path.join(APP_ROOT, 'data', 'sources', 'base_dados.csv')
        EXCEL_DATA_FILE = os.path.join(APP_ROOT, 'data', 'sources', 'base_dados.xlsx')

        # Logging
        LOG_FILE = os.path.join(APP_ROOT, 'logs', 'app.log')

        # Ensure the logs directory exists
        os.makedirs(os.path.join(APP_ROOT, 'logs'), exist_ok=True)

else:
    # Fallback configuration if core is not available
    class Config:
        """Fallback configuration"""
        # Application root directory
        APP_ROOT = APP_ROOT

        # Data directory (fallback for CSV/Excel files)
        DATA_DIR = os.path.join(APP_ROOT, 'data', 'sources')

        # Main data file - can be CSV or XLSX (fallback)
        DATA_FILE = os.path.join(APP_ROOT, 'data', 'sources', 'base_dados.csv')

        # Alternative Excel data file - will be used if it exists (fallback)
        EXCEL_DATA_FILE = os.path.join(APP_ROOT, 'data', 'sources', 'base_dados.xlsx')

    # Database Configuration
    # PostgreSQL connection settings
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = os.getenv('DB_PORT', '5432')
    DB_NAME = os.getenv('DB_NAME', 'amigo_datahub')
    DB_USER = os.getenv('DB_USER', 'app_user')
    DB_PASSWORD = os.getenv('DB_PASSWORD', '')
    DB_SCHEMA = os.getenv('DB_SCHEMA', 'datahub')

    # Connection pool settings for performance
    DB_POOL_SIZE = int(os.getenv('DB_POOL_SIZE', '10'))
    DB_MAX_OVERFLOW = int(os.getenv('DB_MAX_OVERFLOW', '20'))
    DB_POOL_TIMEOUT = int(os.getenv('DB_POOL_TIMEOUT', '30'))
    DB_POOL_RECYCLE = int(os.getenv('DB_POOL_RECYCLE', '3600'))

    # SSL Configuration for security
    DB_SSL_MODE = os.getenv('DB_SSL_MODE', 'require')
    DB_SSL_CERT = os.getenv('DB_SSL_CERT', '')
    DB_SSL_KEY = os.getenv('DB_SSL_KEY', '')
    DB_SSL_ROOT_CERT = os.getenv('DB_SSL_ROOT_CERT', '')

    # Build database URL
    @property
    def database_url(self):
        """Build database URL with SSL parameters"""
        base_url = f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

        # Add SSL parameters if configured
        ssl_params = []
        if self.DB_SSL_MODE:
            ssl_params.append(f"sslmode={self.DB_SSL_MODE}")
        if self.DB_SSL_CERT:
            ssl_params.append(f"sslcert={self.DB_SSL_CERT}")
        if self.DB_SSL_KEY:
            ssl_params.append(f"sslkey={self.DB_SSL_KEY}")
        if self.DB_SSL_ROOT_CERT:
            ssl_params.append(f"sslrootcert={self.DB_SSL_ROOT_CERT}")

        if ssl_params:
            base_url += "?" + "&".join(ssl_params)

        return base_url

    # Cache Configuration (Redis)
    CACHE_TYPE = os.getenv('CACHE_TYPE', 'redis')
    CACHE_REDIS_HOST = os.getenv('CACHE_REDIS_HOST', 'localhost')
    CACHE_REDIS_PORT = int(os.getenv('CACHE_REDIS_PORT', '6379'))
    CACHE_REDIS_DB = int(os.getenv('CACHE_REDIS_DB', '0'))
    CACHE_REDIS_PASSWORD = os.getenv('CACHE_REDIS_PASSWORD', '')
    CACHE_DEFAULT_TIMEOUT = int(os.getenv('CACHE_DEFAULT_TIMEOUT', '300'))  # 5 minutes

    # Data source mode: 'database' or 'files'
    DATA_SOURCE_MODE = os.getenv('DATA_SOURCE_MODE', 'files')

    # Security settings - Use secure config manager
    try:
        import sys
        from pathlib import Path
        shared_path = Path(__file__).parent.parent / 'shared'
        sys.path.insert(0, str(shared_path))
        from config_manager import get_config_manager
        config_manager = get_config_manager('business')
        SECRET_KEY = config_manager.get_secret_key()
    except ImportError:
        # Fallback to environment variable or default for development
        SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production-2024')
        print(f"Using fallback SECRET_KEY for development")

    # Session security
    SESSION_COOKIE_SECURE = os.getenv('SESSION_COOKIE_SECURE', 'False').lower() == 'true'
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = int(os.getenv('PERMANENT_SESSION_LIFETIME', '3600'))  # 1 hour

    # Logging
    LOG_LEVEL = logging.INFO
    LOG_FILE = os.path.join(APP_ROOT, 'logs', 'app.log')

    # Ensure the logs directory exists
    os.makedirs(os.path.join(APP_ROOT, 'logs'), exist_ok=True)

    # Application settings
    APP_NAME = 'DataHub Amigo One - Negócios'
    APP_VERSION = '2.0.0'

    # Color scheme
    COLORS = {
        'primary': '#0087EB',
        'secondary': '#98CFFF',
        'tertiary': '#E0F1FF',
        'success': '#34C759',
        'warning': '#FF9500',
        'danger': '#FF3B30',
    }

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False
    LOG_LEVEL = logging.DEBUG

    # Development specific settings
    DATA_SOURCE_MODE = 'files'  # Use files in development
    SESSION_COOKIE_SECURE = False

    # Development database (can be local)
    DB_HOST = os.getenv('DEV_DB_HOST', 'localhost')
    DB_NAME = os.getenv('DEV_DB_NAME', 'amigo_datahub_dev')

class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = False
    TESTING = True
    LOG_LEVEL = logging.DEBUG

    # Testing specific settings
    DATA_SOURCE_MODE = 'files'  # Use files for testing
    SESSION_COOKIE_SECURE = False

    # Test database
    DB_NAME = os.getenv('TEST_DB_NAME', 'amigo_datahub_test')
    CACHE_TYPE = 'simple'  # Use simple cache for testing

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    LOG_LEVEL = logging.WARNING

    # Production specific settings
    DATA_SOURCE_MODE = os.getenv('DATA_SOURCE_MODE', 'database')  # Use database in production
    SESSION_COOKIE_SECURE = True  # Force HTTPS cookies

    # Production database settings
    DB_SSL_MODE = 'require'  # Force SSL in production

    # Enhanced security for production
    SECRET_KEY = os.getenv('SECRET_KEY')  # Must be set in production

    # Performance optimizations for production
    DB_POOL_SIZE = int(os.getenv('DB_POOL_SIZE', '20'))
    DB_MAX_OVERFLOW = int(os.getenv('DB_MAX_OVERFLOW', '40'))
    CACHE_DEFAULT_TIMEOUT = int(os.getenv('CACHE_DEFAULT_TIMEOUT', '600'))  # 10 minutes

# Configuration dictionary
config_by_name = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig
}
