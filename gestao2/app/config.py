"""
Amigo DataHub - Business Domain Configuration
Simplified configuration without shared dependencies
"""

import os
import logging
from pathlib import Path

# Application root directory
APP_ROOT = Path(__file__).parent.parent.absolute()

class Config:
    """Simplified Business Domain Configuration"""
    
    # Application root directory
    APP_ROOT = APP_ROOT
    DATA_DIR = os.path.join(APP_ROOT, 'data', 'sources')
    DATA_FILE = os.path.join(APP_ROOT, 'data', 'sources', 'base_dados.csv')
    EXCEL_DATA_FILE = os.path.join(APP_ROOT, 'data', 'sources', 'base_dados.xlsx')

    # Flask settings
    SECRET_KEY = os.getenv('SECRET_KEY', 'business_secret_key_change_in_production')
    DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

    # Static and template folders
    STATIC_FOLDER = os.path.join(APP_ROOT, 'app', 'static')
    TEMPLATE_FOLDER = os.path.join(APP_ROOT, 'app', 'templates')

    # Database Configuration
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = os.getenv('DB_PORT', '5432')
    DB_NAME = os.getenv('DB_NAME', 'amigo_datahub_business')
    DB_USER = os.getenv('DB_USER', 'business_user')
    DB_PASSWORD = os.getenv('DB_PASSWORD', '')
    DB_SCHEMA = os.getenv('BUSINESS_DB_SCHEMA', 'business')

    # Session security
    SESSION_COOKIE_SECURE = os.getenv('SESSION_COOKIE_SECURE', 'False').lower() == 'true'
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = int(os.getenv('PERMANENT_SESSION_LIFETIME', '3600'))

    # Logging
    LOG_FILE = os.path.join(APP_ROOT, 'logs', 'app.log')
    LOG_LEVEL = logging.INFO

    # Ensure the logs directory exists
    os.makedirs(os.path.join(APP_ROOT, 'logs'), exist_ok=True)

    # Application settings
    APP_NAME = 'DataHub Amigo One - Negócios'
    APP_VERSION = '2.0.0'

    # Business domain specific features
    ENABLE_RESPONSIBLE_ANALYSIS = os.getenv('ENABLE_RESPONSIBLE_ANALYSIS', 'true').lower() == 'true'
    ENABLE_CLASS_ANALYSIS = os.getenv('ENABLE_CLASS_ANALYSIS', 'true').lower() == 'true'
    ENABLE_COUPON_ANALYSIS = os.getenv('ENABLE_COUPON_ANALYSIS', 'true').lower() == 'true'
    ENABLE_DATA_QUALITY_ANALYSIS = os.getenv('ENABLE_DATA_QUALITY_ANALYSIS', 'true').lower() == 'true'

    # Integration with Product domain (for navigation only)
    PRODUCT_APP_URL = os.getenv('PRODUCT_APP_URL', 'http://localhost:5001')

    # Data source mode: 'database' or 'files'
    DATA_SOURCE_MODE = os.getenv('DATA_SOURCE_MODE', 'files')

    @property
    def database_url(self) -> str:
        """Build database URL with business schema"""
        if self.DB_PASSWORD:
            base_url = f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
            base_url += f"?options=-csearch_path%3D{self.DB_SCHEMA}"
            return base_url
        return None

    # Color scheme
    COLORS = {
        'primary': '#0087EB',
        'secondary': '#98CFFF',
        'tertiary': '#E0F1FF',
        'success': '#34C759',
        'warning': '#FF9500',
        'danger': '#FF3B30',
    }

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False
    DATA_SOURCE_MODE = 'files'

class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = False
    TESTING = True
    DATA_SOURCE_MODE = 'files'

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    DATA_SOURCE_MODE = os.getenv('DATA_SOURCE_MODE', 'database')
    SESSION_COOKIE_SECURE = True

# Configuration dictionary
config_by_name = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig
}

def get_config(environment: str = 'development'):
    """Get configuration for environment"""
    return config_by_name.get(environment, DevelopmentConfig)

# Export for direct import
config = get_config()
SECRET_KEY = config.SECRET_KEY
