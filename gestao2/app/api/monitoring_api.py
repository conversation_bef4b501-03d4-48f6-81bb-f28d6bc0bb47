"""
Business Domain - Monitoring and Metrics API
"""

import sys
from pathlib import Path
from flask import Blueprint, request, jsonify, Response
import logging
from typing import Dict, List, Any, Optional

# Add shared core to path
CORE_PATH = Path(__file__).parent.parent.parent.parent / 'shared' / 'datamesh-core'
sys.path.insert(0, str(CORE_PATH))

try:
    from security.auth_service import AuthService
    from monitoring.metrics_service import (
        MetricsCollector, PerformanceMonitor, SecurityMonitor,
        HealthChecker, AlertManager, setup_default_alert_rules
    )
    from monitoring.user_activity_service import UserActivityService
    CORE_AVAILABLE = True
except ImportError:
    CORE_AVAILABLE = False
    logging.warning("DataMesh Core not available for monitoring API")

logger = logging.getLogger(__name__)

# Create blueprint
monitoring_bp = Blueprint('monitoring', __name__, url_prefix='/api/monitoring')

# Initialize services if core is available
if CORE_AVAILABLE:
    auth_service = AuthService('business')
    metrics_collector = MetricsCollector('business')
    performance_monitor = PerformanceMonitor(metrics_collector)
    security_monitor = SecurityMonitor(metrics_collector)
    health_checker = HealthChecker(metrics_collector)
    alert_manager = AlertManager(metrics_collector)

    # Setup default alert rules
    setup_default_alert_rules(alert_manager)

    # Register health checks
    def check_database() -> bool:
        """Check database connectivity"""
        try:
            # This would check actual database connection
            # For now, return True as placeholder
            return True
        except:
            return False

    def check_cache() -> bool:
        """Check cache connectivity"""
        try:
            # This would check actual cache connection
            # For now, return True as placeholder
            return True
        except:
            return False

    health_checker.register_health_check('database', check_database)
    health_checker.register_health_check('cache', check_cache)

@monitoring_bp.route('/metrics', methods=['GET'])
@auth_service.require_permission('dashboard.view') if CORE_AVAILABLE else lambda f: f
def get_metrics() -> Optional[Dict[str, Any]]:
    """Get application metrics"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Monitoring service not available'}), 503

    try:
        metrics_summary = metrics_collector.get_metrics_summary()
        return jsonify(metrics_summary)

    except Exception as e:
        logger.error(f"Get metrics error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@monitoring_bp.route('/health', methods=['GET'])
def health_check() -> Any:
    """Health check endpoint (public)"""
    if not CORE_AVAILABLE:
        return jsonify({
            'status': 'unhealthy',
            'message': 'Monitoring service not available'
        }), 503

    try:
        health_results = health_checker.run_health_checks()

        status_code = 200 if health_results['status'] == 'healthy' else 503
        return jsonify(health_results), status_code

    except Exception as e:
        logger.error(f"Health check error: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': 'Health check failed'
        }), 503

@monitoring_bp.route('/alerts', methods=['GET'])
@auth_service.require_permission('dashboard.view') if CORE_AVAILABLE else lambda f: f
def get_alerts() -> Optional[Dict[str, Any]]:
    """Get active alerts"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Monitoring service not available'}), 503

    try:
        # Run alert checks
        alert_manager.check_alerts()

        active_alerts = alert_manager.get_active_alerts()

        return jsonify({
            'alerts': active_alerts,
            'count': len(active_alerts)
        })

    except Exception as e:
        logger.error(f"Get alerts error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@monitoring_bp.route('/performance', methods=['GET'])
@auth_service.require_permission('dashboard.view') if CORE_AVAILABLE else lambda f: f
def get_performance_metrics() -> Optional[Dict[str, Any]]:
    """Get performance metrics"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Monitoring service not available'}), 503

    try:
        metrics_summary = metrics_collector.get_metrics_summary()

        # Extract performance-specific metrics
        performance_metrics = {
            'response_times': metrics_summary.get('histograms', {}).get('business.requests.response_time_ms', {}),
            'request_counts': {
                'total': metrics_summary.get('counters', {}).get('business.requests.total', 0),
                'success': metrics_summary.get('counters', {}).get('business.requests.success', 0),
                'error': metrics_summary.get('counters', {}).get('business.requests.error', 0),
                'slow': metrics_summary.get('counters', {}).get('business.requests.slow', 0)
            },
            'system_metrics': {
                'cpu_percent': metrics_summary.get('gauges', {}).get('business.system.cpu_percent', 0),
                'memory_percent': metrics_summary.get('gauges', {}).get('business.system.memory_percent', 0),
                'memory_used_mb': metrics_summary.get('gauges', {}).get('business.system.memory_used_mb', 0),
                'disk_percent': metrics_summary.get('gauges', {}).get('business.system.disk_percent', 0)
            },
            'database_metrics': {
                'query_times': metrics_summary.get('histograms', {}).get('business.database.queries.time_ms', {}),
                'query_success': metrics_summary.get('counters', {}).get('business.database.queries.success', 0),
                'query_errors': metrics_summary.get('counters', {}).get('business.database.queries.error', 0)
            }
        }

        # Calculate error rate
        total_requests = performance_metrics['request_counts']['total']
        error_requests = performance_metrics['request_counts']['error']
        error_rate = (error_requests / max(total_requests, 1)) * 100

        performance_metrics['error_rate_percent'] = round(error_rate, 2)

        return jsonify(performance_metrics)

    except Exception as e:
        logger.error(f"Get performance metrics error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@monitoring_bp.route('/security', methods=['GET'])
@auth_service.require_permission('dashboard.view') if CORE_AVAILABLE else lambda f: f
def get_security_metrics() -> Optional[Dict[str, Any]]:
    """Get security metrics"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Monitoring service not available'}), 503

    try:
        metrics_summary = metrics_collector.get_metrics_summary()

        # Extract security-specific metrics
        security_metrics = {
            'login_attempts': {
                'success': metrics_summary.get('counters', {}).get('business.security.login.success', 0),
                'failed': metrics_summary.get('counters', {}).get('business.security.login.failed', 0)
            },
            'access_control': {
                'access_denied': metrics_summary.get('counters', {}).get('business.security.access_denied', 0)
            },
            'threats': {
                'brute_force_detected': metrics_summary.get('counters', {}).get('business.security.brute_force_detected', 0),
                'suspicious_activity': metrics_summary.get('counters', {}).get('business.security.suspicious', 0)
            }
        }

        # Calculate login success rate
        total_logins = security_metrics['login_attempts']['success'] + security_metrics['login_attempts']['failed']
        success_rate = (security_metrics['login_attempts']['success'] / max(total_logins, 1)) * 100

        security_metrics['login_success_rate_percent'] = round(success_rate, 2)

        return jsonify(security_metrics)

    except Exception as e:
        logger.error(f"Get security metrics error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# Real User Activity Monitoring Routes
@monitoring_bp.route('/user-activity', methods=['GET'])
def user_activity() -> Response:
    """Get real user activity statistics for internal team monitoring"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Monitoring service not available'}), 503

    try:
        activity_service = UserActivityService()
        if not activity_service.is_available():
            return jsonify({'error': 'Database not available'}), 503

        # Get query parameters
        domain = request.args.get('domain', 'business')
        days = int(request.args.get('days', 7))

        # Get user activity stats
        stats = activity_service.get_user_activity_stats(domain=domain, days=days)

        return jsonify({
            'status': 'success',
            'data': stats,
            'domain': domain,
            'period_days': days,
            'message': 'Real user activity data for internal team monitoring'
        })

    except Exception as e:
        logger.error(f"Get user activity error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@monitoring_bp.route('/team-overview', methods=['GET'])
def team_overview() -> Response:
    """Get team activity overview for internal monitoring"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Monitoring service not available'}), 503

    try:
        activity_service = UserActivityService()
        if not activity_service.is_available():
            return jsonify({'error': 'Database not available'}), 503

        # Get team overview for different periods
        daily_overview = activity_service.get_team_overview(days=1)
        weekly_overview = activity_service.get_team_overview(days=7)
        monthly_overview = activity_service.get_team_overview(days=30)

        return jsonify({
            'status': 'success',
            'data': {
                'daily': daily_overview,
                'weekly': weekly_overview,
                'monthly': monthly_overview
            },
            'message': 'Team activity overview - Real internal usage data'
        })

    except Exception as e:
        logger.error(f"Get team overview error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@monitoring_bp.route('/dashboard', methods=['GET'])
def monitoring_dashboard() -> Response:
    """Real user monitoring dashboard for admin panel"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Monitoring service not available'}), 503

    try:
        activity_service = UserActivityService()
        if not activity_service.is_available():
            return jsonify({'error': 'Database not available'}), 503

        # Get team overview for last 30 days
        team_data = activity_service.get_team_overview(days=30)

        # Get current session info
        current_user = {
            'user_id': request.headers.get('X-User-ID', 'unknown'),
            'domain': 'business'
        }

        return jsonify({
            'status': 'success',
            'data': team_data,
            'current_user': current_user,
            'message': 'Real user monitoring data for internal team (~20 people)'
        })

    except Exception as e:
        logger.error(f"Get monitoring dashboard error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@monitoring_bp.route('/business-metrics', methods=['GET'])
@auth_service.require_permission('dashboard.view') if CORE_AVAILABLE else lambda f: f
def get_business_metrics() -> Optional[Dict[str, Any]]:
    """Get business-specific metrics"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Monitoring service not available'}), 503

    try:
        # This would integrate with your business logic to get actual metrics
        # For now, return placeholder data
        business_metrics = {
            'leads': {
                'total_processed': metrics_collector.counters.get('business.leads.processed', 0),
                'conversion_rate': 0.0  # Would be calculated from actual data
            },
            'opportunities': {
                'total_created': metrics_collector.counters.get('business.opportunities.created', 0),
                'success_rate': 0.0  # Would be calculated from actual data
            },
            'implementations': {
                'total_completed': metrics_collector.counters.get('business.implementations.completed', 0),
                'average_time_days': 0.0  # Would be calculated from actual data
            },
            'revenue': {
                'mrr': 0.0,  # Would be calculated from actual data
                'total_revenue': 0.0  # Would be calculated from actual data
            }
        }

        return jsonify(business_metrics)

    except Exception as e:
        logger.error(f"Get business metrics error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@monitoring_bp.route('/dashboard', methods=['GET'])
@auth_service.require_permission('dashboard.view') if CORE_AVAILABLE else lambda f: f
def get_monitoring_dashboard() -> Optional[Dict[str, Any]]:
    """Get comprehensive monitoring dashboard data"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Monitoring service not available'}), 503

    try:
        # Get all metrics
        health_results = health_checker.run_health_checks()
        alert_manager.check_alerts()
        active_alerts = alert_manager.get_active_alerts()
        metrics_summary = metrics_collector.get_metrics_summary()

        # Compile dashboard data
        dashboard_data = {
            'overview': {
                'status': health_results['status'],
                'active_alerts': len(active_alerts),
                'domain': 'business',
                'timestamp': metrics_summary['timestamp']
            },
            'health': health_results,
            'alerts': active_alerts,
            'performance': {
                'avg_response_time': metrics_summary.get('histograms', {}).get('business.requests.response_time_ms', {}).get('avg', 0),
                'total_requests': metrics_summary.get('counters', {}).get('business.requests.total', 0),
                'error_rate': 0.0,  # Calculated above
                'cpu_usage': metrics_summary.get('gauges', {}).get('business.system.cpu_percent', 0),
                'memory_usage': metrics_summary.get('gauges', {}).get('business.system.memory_percent', 0)
            },
            'security': {
                'login_success_rate': 0.0,  # Calculated above
                'failed_logins': metrics_summary.get('counters', {}).get('business.security.login.failed', 0),
                'access_denied': metrics_summary.get('counters', {}).get('business.security.access_denied', 0),
                'threats_detected': metrics_summary.get('counters', {}).get('business.security.brute_force_detected', 0)
            }
        }

        # Calculate rates
        total_requests = dashboard_data['performance']['total_requests']
        error_requests = metrics_summary.get('counters', {}).get('business.requests.error', 0)
        dashboard_data['performance']['error_rate'] = (error_requests / max(total_requests, 1)) * 100

        total_logins = (metrics_summary.get('counters', {}).get('business.security.login.success', 0) +
                       metrics_summary.get('counters', {}).get('business.security.login.failed', 0))
        success_logins = metrics_summary.get('counters', {}).get('business.security.login.success', 0)
        dashboard_data['security']['login_success_rate'] = (success_logins / max(total_logins, 1)) * 100

        return jsonify(dashboard_data)

    except Exception as e:
        logger.error(f"Get monitoring dashboard error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# Decorator to automatically monitor endpoints
def monitor_endpoint(f) -> Any:
    """Decorator to automatically monitor endpoint performance"""
    if CORE_AVAILABLE:
        return performance_monitor.monitor_request(f)
    else:
        return f
