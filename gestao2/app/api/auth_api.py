"""
Business Domain - Authentication and User Management API
"""

import sys
from pathlib import Path
from flask import Blueprint, request, jsonify, session, render_template, redirect, url_for, flash, Response
import logging
from typing import Dict, List, Any, Optional, Union

# Add shared core to path
CORE_PATH = Path(__file__).parent.parent.parent.parent / 'shared' / 'datamesh-core'
sys.path.insert(0, str(CORE_PATH))

try:
    from security.auth_service import AuthService, PermissionManager, AuditLogger
    from security.user_manager import UserManager
    from security.invitation_service import InvitationService
    from monitoring.metrics_service import MetricsCollector, SecurityMonitor
    CORE_AVAILABLE = True
except ImportError:
    CORE_AVAILABLE = False
    logging.warning("DataMesh Core not available for auth API")

logger = logging.getLogger(__name__)

# Create blueprint
auth_bp = Blueprint('auth_api', __name__, url_prefix='/api/auth')

# Initialize services if core is available
if CORE_AVAILABLE:
    auth_service = AuthService('business')
    permission_manager = PermissionManager('business')
    audit_logger = AuditLogger('business')
    user_manager = UserManager('business', 'data/business_users.json')
    metrics_collector = MetricsCollector('business')
    security_monitor = SecurityMonitor(metrics_collector)
    invitation_service = InvitationService('business', 'data/business_invitations.json')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login() -> Union[Response, str]:
    """Login page and authentication"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    if request.method == 'GET':
        return render_template('auth/login.html', domain='Negócios')

    try:
        data = request.get_json() if request.is_json else request.form
        username = data.get('username', '').strip()
        password = data.get('password', '')

        if not username or not password:
            return jsonify({'error': 'Username and password required'}), 400

        # Authenticate user
        user = user_manager.authenticate_user(username, password)

        # Record login attempt
        security_monitor.record_login_attempt(
            user_id=username,
            success=user is not None,
            ip_address=request.remote_addr
        )

        if user:
            # Create session
            session_token = auth_service.create_session(
                user_id=user.user_id,
                user_data={
                    'username': user.username,
                    'email': user.email,
                    'role': user.role,
                    'permissions': user.permissions,
                    'domain': user.domain
                }
            )

            if request.is_json:
                return jsonify({
                    'success': True,
                    'user': {
                        'user_id': user.user_id,
                        'username': user.username,
                        'role': user.role,
                        'permissions': user.permissions
                    },
                    'session_token': session_token
                })
            else:
                flash('Login realizado com sucesso!', 'success')
                return redirect(url_for('main.dashboard'))
        else:
            if request.is_json:
                return jsonify({'error': 'Invalid credentials'}), 401
            else:
                flash('Credenciais inválidas!', 'error')
                return render_template('auth/login.html', domain='Negócios')

    except Exception as e:
        logger.error(f"Login error: {e}")
        if request.is_json:
            return jsonify({'error': 'Internal server error'}), 500
        else:
            flash('Erro interno do servidor!', 'error')
            return render_template('auth/login.html', domain='Negócios')

@auth_bp.route('/logout', methods=['POST'])
def logout() -> Response:
    """Logout user"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    try:
        user_id = session.get('user_id', 'unknown')

        # Log logout
        audit_logger.log_logout(user_id)

        # Destroy session
        auth_service.destroy_session()

        if request.is_json:
            return jsonify({'success': True, 'message': 'Logged out successfully'})
        else:
            flash('Logout realizado com sucesso!', 'success')
            return redirect(url_for('auth.login'))

    except Exception as e:
        logger.error(f"Logout error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/profile', methods=['GET'])
@auth_service.require_auth if CORE_AVAILABLE else lambda f: f
def profile() -> Response:
    """Get user profile"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    try:
        session_data = auth_service.validate_session()
        if not session_data:
            return jsonify({'error': 'Invalid session'}), 401

        user_data = session_data['user_data']
        accessible_pages = permission_manager.get_accessible_pages(user_data['permissions'])

        return jsonify({
            'user': user_data,
            'accessible_pages': accessible_pages,
            'domain': 'business'
        })

    except Exception as e:
        logger.error(f"Profile error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/change-password', methods=['POST'])
@auth_service.require_auth if CORE_AVAILABLE else lambda f: f
def change_password() -> Response:
    """Change user password"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    try:
        session_data = auth_service.validate_session()
        if not session_data:
            return jsonify({'error': 'Invalid session'}), 401

        data = request.get_json()
        old_password = data.get('old_password', '')
        new_password = data.get('new_password', '')

        if not old_password or not new_password:
            return jsonify({'error': 'Old and new passwords required'}), 400

        if len(new_password) < 6:
            return jsonify({'error': 'New password must be at least 6 characters'}), 400

        # Change password
        success = user_manager.change_password(
            user_id=session_data['user_id'],
            old_password=old_password,
            new_password=new_password
        )

        if success:
            return jsonify({'success': True, 'message': 'Password changed successfully'})
        else:
            return jsonify({'error': 'Failed to change password'}), 400

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Change password error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# Admin-only routes
@auth_bp.route('/admin/users', methods=['GET'])  # SECURITY: GET method is safe for read operations
@auth_service.require_permission('users.manage') if CORE_AVAILABLE else lambda f: f
def admin_get_users() -> Response:
    """Get all users (admin only)"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    try:
        users = user_manager.get_all_users()
        users_data = []

        for user in users:
            user_dict = user.to_dict()
            # Remove sensitive data
            if 'password_hash' in user_dict.get('metadata', {}):
                del user_dict['metadata']['password_hash']
            users_data.append(user_dict)

        stats = user_manager.get_user_stats()

        return jsonify({
            'users': users_data,
            'stats': stats
        })

    except Exception as e:
        logger.error(f"Get users error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/admin/users', methods=['POST'])
@auth_service.require_permission('users.manage') if CORE_AVAILABLE else lambda f: f
def admin_create_user() -> Any:
    """Create new user (admin only)"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    try:
        session_data = auth_service.validate_session()
        admin_user_id = session_data['user_id']

        data = request.get_json()
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '')
        role = data.get('role', '').strip()

        if not all([username, email, password, role]):
            return jsonify({'error': 'All fields are required'}), 400

        if len(password) < 6:
            return jsonify({'error': 'Password must be at least 6 characters'}), 400

        # Create user
        user = user_manager.create_user(
            username=username,
            email=email,
            password=password,
            role=role,
            admin_user_id=admin_user_id
        )

        user_dict = user.to_dict()
        # Remove sensitive data
        if 'password_hash' in user_dict.get('metadata', {}):
            del user_dict['metadata']['password_hash']

        return jsonify({
            'success': True,
            'message': 'User created successfully',
            'user': user_dict
        }), 201

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Create user error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/admin/users/<user_id>/role', methods=['PUT'])
@auth_service.require_permission('users.manage') if CORE_AVAILABLE else lambda f: f
def admin_update_user_role(user_id: int) -> Response:
    """Update user role (admin only)"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    try:
        session_data = auth_service.validate_session()
        admin_user_id = session_data['user_id']

        data = request.get_json()
        new_role = data.get('role', '').strip()

        if not new_role:
            return jsonify({'error': 'Role is required'}), 400

        # Update user role
        success = user_manager.update_user_role(
            user_id=user_id,
            new_role=new_role,
            admin_user_id=admin_user_id
        )

        if success:
            return jsonify({
                'success': True,
                'message': 'User role updated successfully'
            })
        else:
            return jsonify({'error': 'Failed to update user role'}), 400

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Update user role error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/admin/users/<user_id>/status', methods=['PUT'])
@auth_service.require_permission('users.manage') if CORE_AVAILABLE else lambda f: f
def admin_update_user_status(user_id: int) -> Response:
    """Update user status (admin only)"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    try:
        session_data = auth_service.validate_session()
        admin_user_id = session_data['user_id']

        data = request.get_json()
        is_active = data.get('is_active')

        if is_active is None:
            return jsonify({'error': 'is_active field is required'}), 400

        # Update user status
        if is_active:
            success = user_manager.activate_user(user_id, admin_user_id)
        else:
            success = user_manager.deactivate_user(user_id, admin_user_id)

        if success:
            status = 'activated' if is_active else 'deactivated'
            return jsonify({
                'success': True,
                'message': f'User {status} successfully'
            })
        else:
            return jsonify({'error': 'Failed to update user status'}), 400

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Update user status error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# Invitation Management Routes
@auth_bp.route('/admin/invitations', methods=['GET'])  # SECURITY: GET method is safe for read operations
def list_invitations() -> List[Dict[str, Any]]:
    """List all invitations (admin only)"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    # Check admin permission
    if 'user_id' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    user = user_manager.get_user(session['user_id'])
    if not user or 'admin' not in user.permissions:
        return jsonify({'error': 'Admin access required'}), 403

    try:
        invitations = invitation_service.get_all_invitations(include_expired=True)
        invitation_data = [inv.to_dict() for inv in invitations]

        return jsonify({
            'invitations': invitation_data,
            'stats': invitation_service.get_invitation_stats()
        })
    except Exception as e:
        logger.error(f"Error listing invitations: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/admin/invitations', methods=['POST'])
def create_invitation() -> Dict[str, Any]:
    """Create new user invitation (admin only)"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    # Check admin permission
    if 'user_id' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    user = user_manager.get_user(session['user_id'])
    if not user or 'admin' not in user.permissions:
        return jsonify({'error': 'Admin access required'}), 403

    try:
        data = request.get_json()
        email = data.get('email')
        role = data.get('role', 'viewer')
        expires_in_days = data.get('expires_in_days', 7)

        if not email:
            return jsonify({'error': 'Email is required'}), 400

        # Get permissions for role
        permissions = permission_manager.get_user_permissions(role)

        # Create invitation
        invitation = invitation_service.create_invitation(
            email=email,
            role=role,
            permissions=permissions,
            invited_by=session['user_id'],
            expires_in_days=expires_in_days
        )

        # Generate invitation URL
        base_url = request.host_url.rstrip('/')
        invitation_url = invitation_service.generate_invitation_url(invitation, base_url)

        return jsonify({
            'invitation': invitation.to_dict(),
            'invitation_url': invitation_url,
            'message': f'Invitation created for {email}'
        }), 201

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error creating invitation: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/admin/invitations/<invitation_id>', methods=['DELETE'])
@auth_service.require_permission('users.manage') if CORE_AVAILABLE else lambda f: f
def cancel_invitation(invitation_id: int) -> Response:
    """Cancel an invitation (admin only)"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    # Check admin permission
    if 'user_id' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    user = user_manager.get_user(session['user_id'])
    if not user or 'admin' not in user.permissions:
        return jsonify({'error': 'Admin access required'}), 403

    try:
        success = invitation_service.cancel_invitation(invitation_id, session['user_id'])

        if success:
            return jsonify({'message': 'Invitation cancelled successfully'})
        else:
            return jsonify({'error': 'Invitation not found'}), 404

    except Exception as e:
        logger.error(f"Error cancelling invitation: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/register', methods=['GET', 'POST'])
def register() -> str:
    """User registration with invitation token"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    if request.method == 'GET':
        token = request.args.get('token')
        if not token:
            return render_template('auth/register.html', error='Invalid invitation link')

        # Validate invitation
        invitation = invitation_service.validate_invitation(token)
        if not invitation:
            return render_template('auth/register.html', error='Invalid or expired invitation')

        return render_template('auth/register.html', invitation=invitation.to_dict())

    # POST - Process registration
    try:
        data = request.get_json() if request.is_json else request.form
        token = data.get('token')
        password = data.get('password')
        confirm_password = data.get('confirm_password')

        if not all([token, password, confirm_password]):
            error = 'All fields are required'
            if request.is_json:
                return jsonify({'error': error}), 400
            return render_template('auth/register.html', error=error)

        if password != confirm_password:
            error = 'Passwords do not match'
            if request.is_json:
                return jsonify({'error': error}), 400
            return render_template('auth/register.html', error=error)

        # Validate invitation
        invitation = invitation_service.validate_invitation(token)
        if not invitation:
            error = 'Invalid or expired invitation'
            if request.is_json:
                return jsonify({'error': error}), 400
            return render_template('auth/register.html', error=error)

        # Create user account
        user = user_manager.create_user(
            username=invitation.email.split('@')[0],  # Use email prefix as username
            email=invitation.email,
            password=password,
            role=invitation.role,
            admin_user_id='system'
        )

        # Accept invitation
        invitation_service.accept_invitation(token)

        if request.is_json:
            return jsonify({
                'message': 'Account created successfully',
                'user': {
                    'username': user.username,
                    'email': user.email,
                    'role': user.role
                }
            }), 201
        else:
            flash('Conta criada com sucesso! Você pode fazer login agora.', 'success')
            return redirect(url_for('auth.login'))

    except ValueError as e:
        error = str(e)
        if request.is_json:
            return jsonify({'error': error}), 400
        return render_template('auth/register.html', error=error)
    except Exception as e:
        logger.error(f"Registration error: {e}")
        error = 'Internal server error'
        if request.is_json:
            return jsonify({'error': error}), 500
        return render_template('auth/register.html', error=error)

@auth_bp.route('/admin/users/<user_id>/reset-password', methods=['POST'])
@auth_service.require_permission('users.manage') if CORE_AVAILABLE else lambda f: f
def admin_reset_password(user_id: int) -> Response:
    """Reset user password (admin only)"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    try:
        session_data = auth_service.validate_session()
        admin_user_id = session_data['user_id']

        data = request.get_json()
        new_password = data.get('new_password', '')

        if not new_password:
            return jsonify({'error': 'New password is required'}), 400

        if len(new_password) < 6:
            return jsonify({'error': 'Password must be at least 6 characters'}), 400

        # Reset password
        success = user_manager.reset_password(
            user_id=user_id,
            new_password=new_password,
            admin_user_id=admin_user_id
        )

        if success:
            return jsonify({
                'success': True,
                'message': 'Password reset successfully'
            })
        else:
            return jsonify({'error': 'Failed to reset password'}), 400

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Reset password error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/admin/roles', methods=['GET'])
@auth_service.require_permission('users.manage') if CORE_AVAILABLE else lambda f: f
def admin_get_roles() -> Response:
    """Get available roles and permissions (admin only)"""
    if not CORE_AVAILABLE:
        return jsonify({'error': 'Authentication service not available'}), 503

    try:
        roles_permissions = {}
        for role, permissions in permission_manager.permissions.items():
            roles_permissions[role] = {
                'permissions': permissions,
                'description': f'{role.title()} role for business domain'
            }

        return jsonify({
            'roles': roles_permissions,
            'domain': 'business'
        })

    except Exception as e:
        logger.error(f"Get roles error: {e}")
        return jsonify({'error': 'Internal server error'}), 500
