/**
 * Amigo DataHub - Custom Styles
 */

/* Custom hero gradient */
.hero-gradient {
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
}

/* Card hover effect */
.card-hover {
    transition: all 0.2s ease-in-out;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge-success {
    background-color: #D1FAE5;
    color: #065F46;
}

.status-badge-warning {
    background-color: #FEF3C7;
    color: #92400E;
}

.status-badge-danger {
    background-color: #FEE2E2;
    color: #B91C1C;
}

.status-badge-info {
    background-color: #E0F1FF;
    color: #0369A1;
}

/* Progress bar */
.progress-bar {
    width: 100%;
    background-color: #E5E7EB;
    border-radius: 9999px;
    height: 0.5rem;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    border-radius: 9999px;
    transition: width 0.5s ease-in-out;
}

.progress-bar-fill-primary {
    background-color: #0087EB;
}

.progress-bar-fill-success {
    background-color: #34C759;
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 120px;
    background-color: #374151;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Custom table styles */
.table-container {
    overflow-x: auto;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.custom-table {
    min-width: 100%;
    border-collapse: collapse;
}

.custom-table th {
    background-color: #F3F4F6;
    padding: 0.75rem 1.5rem;
    text-align: left;
    font-size: 0.75rem;
    font-weight: 600;
    color: #4B5563;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.custom-table td {
    padding: 1rem 1.5rem;
    white-space: nowrap;
    font-size: 0.875rem;
    border-bottom: 1px solid #E5E7EB;
}

.custom-table tr:hover {
    background-color: #F9FAFB;
}

/* Custom card styles */
.insight-card {
    border-radius: 0.5rem;
    padding: 1.25rem;
    background-color: white;
    border: 1px solid #E5E7EB;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease-in-out;
}

.insight-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.insight-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.5rem;
}

.insight-card-content {
    font-size: 0.875rem;
    color: #4B5563;
}

/* Custom button styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn-primary {
    background-color: #0087EB;
    color: white;
}

.btn-primary:hover {
    background-color: #0069B8;
}

.btn-secondary {
    background-color: white;
    color: #374151;
    border: 1px solid #D1D5DB;
}

.btn-secondary:hover {
    background-color: #F9FAFB;
}

/* Custom input styles */
.custom-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid #D1D5DB;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
}

.custom-input:focus {
    outline: none;
    border-color: #0087EB;
    box-shadow: 0 0 0 3px rgba(0, 135, 235, 0.2);
}

/* Custom select styles */
.custom-select {
    width: 100%;
    padding: 0.5rem 2.5rem 0.5rem 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid #D1D5DB;
    font-size: 0.875rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    appearance: none;
    transition: all 0.2s ease-in-out;
}

.custom-select:focus {
    outline: none;
    border-color: #0087EB;
    box-shadow: 0 0 0 3px rgba(0, 135, 235, 0.2);
}
