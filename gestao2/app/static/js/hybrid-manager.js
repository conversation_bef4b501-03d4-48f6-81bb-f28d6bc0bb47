/**
 * Hybrid Manager - Business Domain
 * Manages hybrid frontend architecture for business domain
 */

class HybridManager {
    constructor() {
        this.domain = 'business';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
    }

    setupEventListeners() {
        // Handle dynamic content loading
        document.addEventListener('DOMContentLoaded', () => {
            this.loadDynamicComponents();
        });

        // Handle navigation events
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-hybrid-action]')) {
                this.handleHybridAction(e);
            }
        });
    }

    initializeComponents() {
        // Initialize business domain specific components
        this.initCharts();
        this.initDataTables();
        this.initModals();
    }

    loadDynamicComponents() {
        // Load dynamic content for business domain
        const dynamicElements = document.querySelectorAll('[data-dynamic-content]');
        dynamicElements.forEach(element => {
            this.loadContent(element);
        });
    }

    handleHybridAction(event) {
        event.preventDefault();
        const action = event.target.getAttribute('data-hybrid-action');
        const target = event.target.getAttribute('data-target');

        switch (action) {
            case 'load-content':
                this.loadContent(document.querySelector(target));
                break;
            case 'refresh-data':
                this.refreshData(target);
                break;
            case 'navigate':
                this.navigate(target);
                break;
        }
    }

    loadContent(element) {
        if (!element) return;

        const url = element.getAttribute('data-url');
        if (!url) return;

        fetch(url)
            .then(response => response.text())
            .then(html => {
                element.innerHTML = html;
                this.initializeComponents();
            })
            .catch(error => {
                console.error('Error loading content:', error);
                element.innerHTML = '<p class="text-red-500">Erro ao carregar conteúdo</p>';
            });
    }

    refreshData(target) {
        // Refresh data for specific components
        const element = document.querySelector(target);
        if (element) {
            this.loadContent(element);
        }
    }

    navigate(url) {
        // Handle navigation within business domain
        window.location.href = url;
    }

    initCharts() {
        // Initialize charts for business domain
        const chartElements = document.querySelectorAll('[data-chart]');
        chartElements.forEach(element => {
            const chartType = element.getAttribute('data-chart');
            this.createChart(element, chartType);
        });
    }

    createChart(element, type) {
        // Chart creation logic specific to business domain
        if (typeof Chart !== 'undefined') {
            const ctx = element.getContext('2d');
            // Chart configuration would go here
        }
    }

    initDataTables() {
        // Initialize data tables for business domain
        const tables = document.querySelectorAll('[data-table]');
        tables.forEach(table => {
            this.enhanceTable(table);
        });
    }

    enhanceTable(table) {
        // Add sorting, filtering, pagination to tables
        // Business domain specific table enhancements
    }

    initModals() {
        // Initialize modals for business domain
        const modals = document.querySelectorAll('[data-modal]');
        modals.forEach(modal => {
            this.setupModal(modal);
        });
    }

    setupModal(modal) {
        // Modal setup logic for business domain
        const triggers = document.querySelectorAll(`[data-modal-target="${modal.id}"]`);
        triggers.forEach(trigger => {
            trigger.addEventListener('click', () => {
                this.openModal(modal);
            });
        });

        const closeButtons = modal.querySelectorAll('[data-modal-close]');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.closeModal(modal);
            });
        });
    }

    openModal(modal) {
        modal.classList.remove('hidden');
        document.body.classList.add('overflow-hidden');
    }

    closeModal(modal) {
        modal.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
    }
}

// Initialize hybrid manager for business domain
window.hybridManager = new HybridManager();
