
// Input validation added for security
function validateInput(input) {
    // Remove potentially dangerous characters
    return input.replace(/[<>"'&]/g, '');
}
/**
 * Amigo DataHub - Shared Functions
 * Shared functions for all pages
 */

document.addEventListener('DOMContentLoaded', function() {
    // // console.log('Amigo DataHub - Shared script loaded');

    // Initialize mobile menu toggle
    initMobileMenu();

    // Initialize tooltips
    initTooltips();

    // Initialize flash message dismissal
    initFlashMessages();
});

/**
 * Initialize mobile menu toggle
 */
function initMobileMenu() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const sidebar = document.querySelector('aside');

    if (mobileMenuButton && sidebar) {
        mobileMenuButton.addEventListener('click', function() {
            sidebar.classList.toggle('hidden');
        });
    }
}

/**
 * Initialize tooltips
 */
function initTooltips() {
    const tooltips = document.querySelectorAll('[data-tooltip]');

    tooltips.forEach(tooltip => {
        tooltip.addEventListener('mouseenter', function() {
            const tooltipText = this.getAttribute('data-tooltip');

            if (!tooltipText) return;

            const tooltipElement = document.createElement('div');
            tooltipElement.className = 'absolute z-50 p-2 bg-gray-800 text-white text-xs rounded shadow-lg';
            tooltipElement.style.maxWidth = '200px';
            tooltipElement.textContent = tooltipText;

            document.body.appendChild(tooltipElement);

            const rect = this.getBoundingClientRect();
            tooltipElement.style.top = `${rect.top - tooltipElement.offsetHeight - 5 + window.scrollY}px`;
            tooltipElement.style.left = `${rect.left + (rect.width / 2) - (tooltipElement.offsetWidth / 2) + window.scrollX}px`;

            this.addEventListener('mouseleave', function() {
                document.body.removeChild(tooltipElement);
            }, { once: true });
        });
    });
}

/**
 * Initialize flash message dismissal
 */
function initFlashMessages() {
    const flashMessages = document.querySelectorAll('.flash-message');

    flashMessages.forEach(message => {
        const dismissButton = message.querySelector('.dismiss-button');

        if (dismissButton) {
            dismissButton.addEventListener('click', function() {
                message.remove();
            });
        }

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            message.classList.add('opacity-0');
            setTimeout(() => {
                message.remove();
            }, 300);
        }, 5000);
    });
}

/**
 * Format a number as currency
 * @param {number} value - The value to format
 * @returns {string} - The formatted currency string
 */
function formatCurrency(value) {
    return AmigoDH.formatCurrency(value);
}

/**
 * Format a number as percentage
 * @param {number} value - The value to format
 * @returns {string} - The formatted percentage string
 */
function formatPercentage(value) {
    return AmigoDH.formatPercentage(value);
}

/**
 * Format a date
 * @param {string|Date} date - The date to format
 * @returns {string} - The formatted date string
 */
function formatDate(date) {
    return AmigoDH.formatDate(date);
}

/**
 * Initialize table filters
 * @param {string} tableId - The ID of the table
 * @param {object} filterSelects - An object mapping filter select IDs to column indices
 * @param {string} searchInputId - The ID of the search input
 * @param {number} searchColumnIndex - The index of the column to search in
 */
function initTableFilters(tableId, filterSelects, searchInputId, searchColumnIndex) {
    AmigoDH.initTableFilters(tableId, filterSelects, searchInputId, searchColumnIndex);
}

/**
 * Debug chart errors
 * @param {string} elementId - The ID of the canvas element
 * @param {object} data - The chart data
 * @param {Error} error - The error object
 */
function debugChart(elementId, data, error) {
    console.error(`Error creating chart ${elementId}:`, error);
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = `<div class="flex items-center justify-center h-full text-gray-500">
            Erro ao criar gráfico: ${error.message}
        </div>`;
    }
}

// Export functions to global scope
window.formatCurrency = formatCurrency;
window.formatPercentage = formatPercentage;
window.formatDate = formatDate;
window.initTableFilters = initTableFilters;
window.debugChart = debugChart;
