/**
 * Amigo DataHub - Chart Functions
 * Functions for creating and managing charts
 */

// Chart colors
const chartColors = {
    primary: '#0087EB',
    secondary: '#98CFFF',
    tertiary: '#E0F1FF',
    success: '#34C759',
    warning: '#FF9500',
    danger: '#FF3B30',
    gray: '#6B7280',
    lightGray: '#E5E7EB',
    darkGray: '#374151',
    white: '#FFFFFF',
    black: '#000000',
    // Blue tones for charts
    blue: [
        '#0087EB',
        '#1E96F0',
        '#3CA5F5',
        '#5AB4FA',
        '#78C3FF',
        '#98CFFF',
        '#B8DFFF',
        '#D8EEFF',
        '#E0F1FF'
    ]
};

// Default chart options
const defaultChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom',
            labels: {
                padding: 20,
                boxWidth: 12,
                usePointStyle: true,
                pointStyle: 'circle'
            }
        },
        tooltip: {
            backgroundColor: 'rgba(17, 24, 39, 0.9)',
            titleColor: '#FFFFFF',
            bodyColor: '#FFFFFF',
            bodySpacing: 4,
            padding: 12,
            boxWidth: 10,
            usePointStyle: true,
            callbacks: {
                // Default formatting for currency values
                label: function(context) {
                    let value = context.raw;
                    if (typeof value === 'number') {
                        return context.dataset.label + ': R$ ' + value.toLocaleString('pt-BR', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        });
                    }
                    return context.dataset.label + ': ' + value;
                }
            }
        }
    },
    scales: {
        x: {
            grid: {
                color: 'rgba(226, 232, 240, 0.5)'
            },
            ticks: {
                color: '#6B7280'
            }
        },
        y: {
            grid: {
                color: 'rgba(226, 232, 240, 0.5)'
            },
            ticks: {
                color: '#6B7280'
            }
        }
    }
};

/**
 * Create a bar chart
 * @param {string} elementId - The ID of the canvas element
 * @param {Array} labels - The labels for the chart
 * @param {Array} data - The data for the chart
 * @param {string} label - The label for the dataset
 * @param {object} options - Additional options for the chart
 * @returns {Chart} - The created chart
 */
function createBarChart(elementId, labels, data, label = 'Valor', options = {}) {
    const ctx = document.getElementById(elementId);
    if (!ctx) {
        console.error(`Canvas element with ID ${elementId} not found`);
        return null;
    }

    const chartData = {
        labels: labels,
        datasets: [{
            label: label,
            data: data,
            backgroundColor: Array.isArray(options.backgroundColor) ? options.backgroundColor : options.backgroundColor || chartColors.secondary,
            borderColor: options.borderColor || chartColors.primary,
            borderWidth: options.borderWidth || 1
        }]
    };

    // Merge default options with custom options
    const chartOptions = Object.assign({}, defaultChartOptions, options);

    // Add custom tooltip callback if provided
    if (options.tooltipCallback) {
        chartOptions.plugins.tooltip.callbacks.label = function(context) {
            return options.tooltipCallback(context.raw);
        };
    }

    // Add custom y-axis callback if provided
    if (options.yAxisCallback) {
        chartOptions.scales.y.ticks.callback = function(value) {
            return options.yAxisCallback(value);
        };
    }

    window[elementId + 'Chart'] = new Chart(ctx, {
        type: 'bar',
        data: chartData,
        options: chartOptions
    });

    return window[elementId + 'Chart'];
}

/**
 * Create a line chart
 * @param {string} elementId - The ID of the canvas element
 * @param {Array} labels - The labels for the chart
 * @param {Array} data - The data for the chart
 * @param {string} label - The label for the dataset
 * @param {object} options - Additional options for the chart
 * @returns {Chart} - The created chart
 */
function createLineChart(elementId, labels, data, label = 'Valor', options = {}) {
    const ctx = document.getElementById(elementId);
    if (!ctx) {
        console.error(`Canvas element with ID ${elementId} not found`);
        return null;
    }

    const chartData = {
        labels: labels,
        datasets: [{
            label: label,
            data: data,
            backgroundColor: Array.isArray(options.backgroundColor) ? options.backgroundColor : options.backgroundColor || 'rgba(152, 207, 255, 0.2)',
            borderColor: options.borderColor || chartColors.primary,
            borderWidth: options.borderWidth || 2,
            tension: options.tension || 0.1,
            fill: options.fill !== undefined ? options.fill : true
        }]
    };

    // Merge default options with custom options
    const chartOptions = Object.assign({}, defaultChartOptions, options);

    // Add custom tooltip callback if provided
    if (options.tooltipCallback) {
        chartOptions.plugins.tooltip.callbacks.label = function(context) {
            return options.tooltipCallback(context.raw);
        };
    }

    // Add custom y-axis callback if provided
    if (options.yAxisCallback) {
        chartOptions.scales.y.ticks.callback = function(value) {
            return options.yAxisCallback(value);
        };
    }

    window[elementId + 'Chart'] = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: chartOptions
    });

    return window[elementId + 'Chart'];
}

/**
 * Create a pie chart
 * @param {string} elementId - The ID of the canvas element
 * @param {Array} labels - The labels for the chart
 * @param {Array} data - The data for the chart
 * @param {object} options - Additional options for the chart
 * @returns {Chart} - The created chart
 */
function createPieChart(elementId, labels, data, options = {}) {
    const ctx = document.getElementById(elementId);
    if (!ctx) {
        console.error(`Canvas element with ID ${elementId} not found`);
        return null;
    }

    const chartData = {
        labels: labels,
        datasets: [{
            data: data,
            backgroundColor: Array.isArray(options.backgroundColor) ? options.backgroundColor : options.backgroundColor || chartColors.blue,
            borderColor: options.borderColor || chartColors.white,
            borderWidth: options.borderWidth || 1
        }]
    };

    // Pie chart specific options
    const pieOptions = {
        plugins: {
            legend: {
                position: 'right',
                labels: {
                    boxWidth: 12,
                    padding: 10
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const value = context.raw;
                        const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                        const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                        return `${context.label}: ${value} (${percentage}%)`;
                    }
                }
            }
        }
    };

    // Merge default options with pie options and custom options
    const chartOptions = Object.assign({}, defaultChartOptions, pieOptions, options);

    // Add custom tooltip callback if provided
    if (options.tooltipCallback) {
        chartOptions.plugins.tooltip.callbacks.label = function(context) {
            return options.tooltipCallback(context.raw, context.label);
        };
    }

    window[elementId + 'Chart'] = new Chart(ctx, {
        type: 'pie',
        data: chartData,
        options: chartOptions
    });

    return window[elementId + 'Chart'];
}

/**
 * Create a doughnut chart
 * @param {string} elementId - The ID of the canvas element
 * @param {Array} labels - The labels for the chart
 * @param {Array} data - The data for the chart
 * @param {object} options - Additional options for the chart
 * @returns {Chart} - The created chart
 */
function createDoughnutChart(elementId, labels, data, options = {}) {
    const ctx = document.getElementById(elementId);
    if (!ctx) {
        console.error(`Canvas element with ID ${elementId} not found`);
        return null;
    }

    const chartData = {
        labels: labels,
        datasets: [{
            data: data,
            backgroundColor: Array.isArray(options.backgroundColor) ? options.backgroundColor : options.backgroundColor || chartColors.blue,
            borderColor: options.borderColor || chartColors.white,
            borderWidth: options.borderWidth || 1
        }]
    };

    // Doughnut chart specific options
    const doughnutOptions = {
        cutout: '60%',
        plugins: {
            legend: {
                position: 'right',
                labels: {
                    boxWidth: 12,
                    padding: 10
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const value = context.raw;
                        const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                        const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                        return `${context.label}: ${value} (${percentage}%)`;
                    }
                }
            }
        }
    };

    // Merge default options with doughnut options and custom options
    const chartOptions = Object.assign({}, defaultChartOptions, doughnutOptions, options);

    // Add custom tooltip callback if provided
    if (options.tooltipCallback) {
        chartOptions.plugins.tooltip.callbacks.label = function(context) {
            return options.tooltipCallback(context.raw, context.label);
        };
    }

    window[elementId + 'Chart'] = new Chart(ctx, {
        type: 'doughnut',
        data: chartData,
        options: chartOptions
    });

    return window[elementId + 'Chart'];
}

/**
 * Create a funnel chart (using bar chart)
 * @param {string} elementId - The ID of the canvas element
 * @param {Array} labels - The labels for the chart
 * @param {Array} data - The data for the chart
 * @param {object} options - Additional options for the chart
 * @returns {Chart} - The created chart
 */
function createFunnelChart(elementId, labels, data, options = {}) {
    const ctx = document.getElementById(elementId);
    if (!ctx) {
        console.error(`Canvas element with ID ${elementId} not found`);
        return null;
    }

    const chartData = {
        labels: labels,
        datasets: [{
            label: 'Quantidade',
            data: data,
            backgroundColor: Array.isArray(options.backgroundColor) ? options.backgroundColor : options.backgroundColor || chartColors.blue,
            borderColor: options.borderColor || chartColors.white,
            borderWidth: options.borderWidth || 0
        }]
    };

    // Funnel chart specific options
    const funnelOptions = {
        indexAxis: 'y',
        scales: {
            x: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(226, 232, 240, 0.5)'
                },
                ticks: {
                    color: '#6B7280'
                }
            },
            y: {
                grid: {
                    display: false
                },
                ticks: {
                    color: '#6B7280'
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    };

    // Merge default options with funnel options and custom options
    const chartOptions = Object.assign({}, defaultChartOptions, funnelOptions, options);

    // Add custom tooltip callback if provided
    if (options.tooltipCallback) {
        chartOptions.plugins.tooltip.callbacks.label = function(context) {
            return options.tooltipCallback(context.raw);
        };
    }

    window[elementId + 'Chart'] = new Chart(ctx, {
        type: 'bar',
        data: chartData,
        options: chartOptions
    });

    return window[elementId + 'Chart'];
}

/**
 * Destroy a chart if it exists
 * @param {string} elementId - The ID of the canvas element
 */
function destroyChart(elementId) {
    if (window[elementId + 'Chart'] && typeof window[elementId + 'Chart'].destroy === 'function') {
        try {
            window[elementId + 'Chart'].destroy();
        } catch (error) {
            console.error(`Error destroying chart ${elementId}:`, error);
        }
    }
}

/**
 * Create a multi-line chart
 * @param {string} elementId - The ID of the canvas element
 * @param {Array} labels - The labels for the chart
 * @param {Array} datasets - Array of dataset objects with label, data, and color properties
 * @param {string} yAxisLabel - The label for the y-axis
 * @param {object} options - Additional options for the chart
 * @returns {Chart} - The created chart
 */
function createMultiLineChart(elementId, labels, datasets, yAxisLabel = 'Valor', options = {}) {
    const ctx = document.getElementById(elementId);
    if (!ctx) {
        console.error(`Canvas element with ID ${elementId} not found`);
        return null;
    }

    // Format datasets for Chart.js
    const chartDatasets = datasets.map(dataset => ({
        label: dataset.label,
        data: dataset.data,
        backgroundColor: dataset.backgroundColor || 'rgba(152, 207, 255, 0.2)',
        borderColor: dataset.color || chartColors.primary,
        borderWidth: dataset.borderWidth || 2,
        tension: dataset.tension || 0.3,
        fill: dataset.fill !== undefined ? dataset.fill : false
    }));

    const chartData = {
        labels: labels,
        datasets: chartDatasets
    };

    // Multi-line chart specific options
    const multiLineOptions = {
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: yAxisLabel,
                    color: '#6B7280',
                    font: {
                        weight: 'normal'
                    }
                },
                max: options.max || undefined
            }
        }
    };

    // Merge default options with multi-line options and custom options
    const chartOptions = Object.assign({}, defaultChartOptions, multiLineOptions, options);

    // Add custom tooltip callback if provided
    if (options.tooltipCallback) {
        chartOptions.plugins.tooltip.callbacks.label = function(context) {
            return context.dataset.label + ': ' + options.tooltipCallback(context.raw);
        };
    }

    // Add custom y-axis callback if provided
    if (options.yAxisCallback) {
        chartOptions.scales.y.ticks.callback = function(value) {
            return options.yAxisCallback(value);
        };
    }

    window[elementId + 'Chart'] = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: chartOptions
    });

    return window[elementId + 'Chart'];
}

// Export functions to global scope
window.AmigoDH = window.AmigoDH || {};
Object.assign(window.AmigoDH, {
    colors: chartColors,
    createBarChart,
    createLineChart,
    createPieChart,
    createDoughnutChart,
    createFunnelChart,
    createMultiLineChart,
    destroyChart
});
