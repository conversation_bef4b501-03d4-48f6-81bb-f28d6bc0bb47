"""
Security Module for DataHub Amigo One - Business Domain
Implements CSRF protection, input validation, and security headers
"""

import secrets
import hashlib
import time
from functools import wraps
from typing import Any, Dict, List
from flask import session, request, abort, g, current_app
import logging

logger = logging.getLogger(__name__)

class SecurityManager:
    """Centralized security management for the application"""

    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)

    def init_app(self, app):
        """Initialize security for Flask app"""
        app.config.setdefault('CSRF_TOKEN_TIMEOUT', 3600)  # 1 hour
        app.config.setdefault('CSRF_SECRET_KEY', secrets.token_hex(32))

        # Add security headers
        @app.after_request
        def add_security_headers(response):
            # Prevent XSS
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'

            # HTTPS enforcement (for production)
            if not app.debug:
                response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'

            # Content Security Policy
            csp = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net; "
                "style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self';"
            )
            response.headers['Content-Security-Policy'] = csp

            return response

        # Add CSRF token generation to template context
        @app.context_processor
        def inject_csrf_token() -> Any:
            return dict(csrf_token=self.generate_csrf_token)

    def generate_csrf_token(self) -> str:
        """Generate CSRF token for forms"""
        if 'csrf_token' not in session:
            session['csrf_token'] = secrets.token_hex(32)
            session['csrf_token_time'] = time.time()
        return session['csrf_token']

    def validate_csrf_token(self, token: str = None) -> bool:
        """Validate CSRF token"""
        if token is None:
            token = request.form.get('csrf_token') or request.headers.get('X-CSRF-Token')

        if not token:
            return False

        session_token = session.get('csrf_token')
        token_time = session.get('csrf_token_time', 0)

        if not session_token:
            return False

        # Check token expiration
        if time.time() - token_time > current_app.config['CSRF_TOKEN_TIMEOUT']:
            session.pop('csrf_token', None)
            session.pop('csrf_token_time', None)
            return False

        return secrets.compare_digest(session_token, token)

def csrf_protect(f) -> Any:
    """Decorator to protect routes with CSRF validation"""
    @wraps(f)
    def decorated_function(*args, **kwargs) -> Any:
        if request.method == 'POST':
            security_manager = current_app.extensions.get('security_manager')
            if security_manager and not security_manager.validate_csrf_token():
                logger.warning(f"CSRF validation failed for {request.endpoint}")
                abort(403, description="CSRF token validation failed")
        return f(*args, **kwargs)
    return decorated_function

def sanitize_input(data: Any) -> Any:
    """Sanitize user input to prevent XSS"""
    if isinstance(data, str):
        # Basic HTML escaping
        data = data.replace('&', '&amp;')
        data = data.replace('<', '&lt;')
        data = data.replace('>', '&gt;')
        data = data.replace('"', '&quot;')
        data = data.replace("'", '&#x27;')
        data = data.replace('/', '&#x2F;')
    elif isinstance(data, dict):
        return {key: sanitize_input(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [sanitize_input(item) for item in data]

    return data

def validate_file_upload(file: Any) -> tuple:
    """Validate file uploads for security"""
    if not file or not file.filename:
        return False, "No file selected"

    # Check file extension
    allowed_extensions = {'.csv', '.xlsx', '.xls', '.pdf', '.png', '.jpg', '.jpeg'}
    file_ext = '.' + file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

    if file_ext not in allowed_extensions:
        return False, f"File type {file_ext} not allowed"

    # Check file size (10MB limit)
    file.seek(0, 2)  # Seek to end
    file_size = file.tell()
    file.seek(0)  # Reset to beginning

    if file_size > 10 * 1024 * 1024:  # 10MB
        return False, "File size too large (max 10MB)"

    return True, "File is valid"

def rate_limit_check(key: str, limit: int = 100, window: int = 3600) -> bool:
    """Simple rate limiting check"""
    # This is a basic implementation - in production, use Redis or similar
    current_time = time.time()

    if not hasattr(g, 'rate_limits'):
        g.rate_limits = {}

    if key not in g.rate_limits:
        g.rate_limits[key] = []

    # Clean old entries
    g.rate_limits[key] = [
        timestamp for timestamp in g.rate_limits[key]
        if current_time - timestamp < window
    ]

    # Check limit
    if len(g.rate_limits[key]) >= limit:
        return False

    # Add current request
    g.rate_limits[key].append(current_time)
    return True

def secure_filename(filename: str) -> str:
    """Generate secure filename"""
    if not filename:
        return 'unnamed_file'

    # Remove path components
    filename = filename.split('/')[-1].split('\\')[-1]

    # Keep only alphanumeric, dots, hyphens, underscores
    import re
    filename = re.sub(r'[^a-zA-Z0-9._-]', '_', filename)

    # Limit length
    if len(filename) > 100:
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        filename = name[:95] + ('.' + ext if ext else '')

    return filename

def log_security_event(event_type: str, details: str, user_id: int = None) -> None:
    """Log security events for monitoring"""
    security_log = {
        'timestamp': time.time(),
        'event_type': event_type,
        'details': details,
        'user_id': user_id or session.get('user_id'),
        'ip_address': request.remote_addr,
        'user_agent': request.headers.get('User-Agent', ''),
        'endpoint': request.endpoint
    }

    logger.warning(f"SECURITY EVENT: {event_type} - {details}", extra=security_log)
