from typing import Dict, List, Any, Optional, Union, Tuple
"""
Amigo DataHub - Class Model
"""

class Class:
    """Class Model"""
    
    def __init__(self: Any, data: Dict[str, Any]) -> None:
        """
        Initialize a class
        
        Args:
            data (dict): The class data
        """
        self.id = data.get('id')
        self.name = data.get('name')
        self.period = data.get('period')
        self.students = data.get('students', 0)
        self.status = data.get('status')
        self.product = data.get('product')
        self.course = data.get('course')
        self.university = data.get('university')
        self.data = data
    
    def to_dict(self: Any) -> Dict[str, Any]:
        """
        Convert the class to a dictionary
        
        Returns:
            dict: The class as a dictionary
        """
        return {
            'id': self.id,
            'name': self.name,
            'period': self.period,
            'students': self.students,
            'status': self.status,
            'product': self.product,
            'course': self.course,
            'university': self.university
        }
    
    @classmethod
    def from_dict(cls: Any, data: Dict[str, Any]) -> Any:
        """
        Create a class from a dictionary
        
        Args:
            data (dict): The class data
            
        Returns:
            Class: The created class
        """
        return cls(data)
