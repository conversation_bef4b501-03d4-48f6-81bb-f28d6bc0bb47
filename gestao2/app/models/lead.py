from typing import Dict, List, Any, Optional, Union, Tuple
"""
Amigo DataHub - Lead Model
"""

class Lead:
    """Lead Model"""
    
    def __init__(self: Any, data: Dict[str, Any]) -> None:
        """
        Initialize a lead
        
        Args:
            data (dict): The lead data
        """
        self.id = data.get('Lead_id')
        self.name = data.get('Nome do Lead')
        self.creation_date = data.get('Data_criacao_lead')
        self.academic_formation = data.get('Formação Academica')
        self.university = data.get('Universidade')
        self.course = data.get('Curso')
        self.city = data.get('Cidade')
        self.state = data.get('Estado')
        self.email = data.get('Email')
        self.phone = data.get('Telefone')
        self.data = data
    
    def to_dict(self: Any) -> Dict[str, Any]:
        """
        Convert the lead to a dictionary
        
        Returns:
            dict: The lead as a dictionary
        """
        return {
            'id': self.id,
            'name': self.name,
            'creation_date': self.creation_date,
            'academic_formation': self.academic_formation,
            'university': self.university,
            'course': self.course,
            'city': self.city,
            'state': self.state,
            'email': self.email,
            'phone': self.phone
        }
    
    @classmethod
    def from_dict(cls: Any, data: Dict[str, Any]) -> Any:
        """
        Create a lead from a dictionary
        
        Args:
            data (dict): The lead data
            
        Returns:
            Lead: The created lead
        """
        return cls(data)
