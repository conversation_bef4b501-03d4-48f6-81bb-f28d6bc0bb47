from typing import Dict, List, Any, Optional, Union, Tuple
"""
Amigo DataHub - Implementation Model
"""

class Implementation:
    """Implementation Model"""
    
    def __init__(self: List[Any], data: Dict[str, Any]) -> None:
        """
        Initialize an implementation
        
        Args:
            data (dict): The implementation data
        """
        self.opportunity_id = data.get('Oportunidade_id')
        self.lead_name = data.get('Nome do Lead')
        self.creation_date = data.get('Data_Criacao_Implantacao')
        self.phase = data.get('Fase_Implantacao')
        self.status = data.get('Status_Implantacao')
        self.responsible = data.get('ResponsableOnboarding')
        self.milestone1_date = data.get('DataMarco1 - Co. Hero')
        self.milestone2_date = data.get('DateMarco2 - Constituído')
        self.milestone3_date = data.get('Marco 3 - Homologado')
        self.milestone4_date = data.get('Marco 4 - Liberação')
        self.start_date = data.get('ActualStartDate')
        self.expected_end_date = data.get('DataPrevistaDeFinalização')
        self.data = data
    
    def to_dict(self: List[Any]) -> Dict[str, Any]:
        """
        Convert the implementation to a dictionary
        
        Returns:
            dict: The implementation as a dictionary
        """
        return {
            'opportunity_id': self.opportunity_id,
            'lead_name': self.lead_name,
            'creation_date': self.creation_date,
            'phase': self.phase,
            'status': self.status,
            'responsible': self.responsible,
            'milestone1_date': self.milestone1_date,
            'milestone2_date': self.milestone2_date,
            'milestone3_date': self.milestone3_date,
            'milestone4_date': self.milestone4_date,
            'start_date': self.start_date,
            'expected_end_date': self.expected_end_date
        }
    
    @classmethod
    def from_dict(cls: List[Any], data: Dict[str, Any]) -> Any:
        """
        Create an implementation from a dictionary
        
        Args:
            data (dict): The implementation data
            
        Returns:
            Implementation: The created implementation
        """
        return cls(data)
    
    def get_milestones(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the implementation milestones
        
        Returns:
            list: The implementation milestones
        """
        milestones = [
            {
                'label': 'Criação da Conta Hero',
                'date': self.milestone1_date
            },
            {
                'label': 'Constituição',
                'date': self.milestone2_date
            },
            {
                'label': 'Homologação',
                'date': self.milestone3_date
            },
            {
                'label': 'Liberação',
                'date': self.milestone4_date
            }
        ]
        
        return milestones
    
    def calculate_progress(self: List[Any]) -> float:
        """
        Calculate the implementation progress
        
        Returns:
            float: The implementation progress percentage
        """
        milestones = self.get_milestones()
        completed = sum(1 for m in milestones if m['date'] and m['date'] != 'Não informado')
        total = len(milestones)
        
        if total == 0:
            return 0
        
        return (completed / total) * 100
