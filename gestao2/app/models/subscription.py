from typing import Dict, List, Any, Optional, Union, Tuple
"""
Amigo DataHub - Subscription Model
"""

class Subscription:
    """Subscription Model"""
    
    def __init__(self: Any, data: Dict[str, Any]) -> None:
        """
        Initialize a subscription
        
        Args:
            data (dict): The subscription data
        """
        self.opportunity_id = data.get('Oportunidade_id')
        self.lead_name = data.get('Nome do Lead')
        self.product = data.get('Produto')
        self.monthly_value = data.get('Valor Mensalidade')
        self.start_date = data.get('Data_Finalizacao')
        self.university = data.get('Universidade')
        self.responsible = data.get('ResponsableOnboarding')
        self.data = data
    
    def to_dict(self: Any) -> Dict[str, Any]:
        """
        Convert the subscription to a dictionary
        
        Returns:
            dict: The subscription as a dictionary
        """
        return {
            'opportunity_id': self.opportunity_id,
            'lead_name': self.lead_name,
            'product': self.product,
            'monthly_value': self.monthly_value,
            'start_date': self.start_date,
            'university': self.university,
            'responsible': self.responsible
        }
    
    @classmethod
    def from_dict(cls: Any, data: Dict[str, Any]) -> Any:
        """
        Create a subscription from a dictionary
        
        Args:
            data (dict): The subscription data
            
        Returns:
            Subscription: The created subscription
        """
        return cls(data)
