from typing import Dict, List, Any, Optional, Union, Tuple
"""
Amigo DataHub - Opportunity Model
"""

class Opportunity:
    """Opportunity Model"""
    
    def __init__(self: Any, data: Dict[str, Any]) -> None:
        """
        Initialize an opportunity
        
        Args:
            data (dict): The opportunity data
        """
        self.id = data.get('Oportunidade_id')
        self.lead_id = data.get('Lead_id')
        self.lead_name = data.get('Nome do Lead')
        self.creation_date = data.get('Data_criacao_Oportunidade')
        self.funnel_stage = data.get('Etapa do funil Comercial')
        self.product = data.get('Produto')
        self.monthly_value = data.get('Valor Mensalidade')
        self.responsible = data.get('Nome_Responsavel')
        self.data = data
    
    def to_dict(self: Any) -> Dict[str, Any]:
        """
        Convert the opportunity to a dictionary
        
        Returns:
            dict: The opportunity as a dictionary
        """
        return {
            'id': self.id,
            'lead_id': self.lead_id,
            'lead_name': self.lead_name,
            'creation_date': self.creation_date,
            'funnel_stage': self.funnel_stage,
            'product': self.product,
            'monthly_value': self.monthly_value,
            'responsible': self.responsible
        }
    
    @classmethod
    def from_dict(cls: Any, data: Dict[str, Any]) -> Any:
        """
        Create an opportunity from a dictionary
        
        Args:
            data (dict): The opportunity data
            
        Returns:
            Opportunity: The created opportunity
        """
        return cls(data)
