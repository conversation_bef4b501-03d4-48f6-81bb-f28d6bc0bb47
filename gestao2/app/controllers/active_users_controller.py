"""
Amigo DataHub - Active Users Controller
Advanced analysis for active users and invoice production
"""

import logging
from flask import Blueprint, render_template, flash, jsonify, request
from app.services.data_loader import DataLoader
from app.services.active_users_service import ActiveUsersService
from typing import Dict, List, Any, Optional, Union, Tuple
from flask import Response

logger = logging.getLogger(__name__)

# Constants
TEMPLATE_PATH = 'active_users/index.html'

# Create blueprint
active_users_bp = Blueprint('active_users', __name__, url_prefix='/usuarios-ativos')

@active_users_bp.route('/')
def index() -> str:
    """Active Users dashboard with comprehensive invoice analysis"""
    from flask import render_template
    import pandas as pd

    # Calcular KPIs usando o serviço completo
    try:
        from app.services.data_loader import DataLoader
        from app.services.active_users_service import ActiveUsersService

        data_loader = DataLoader()
        data_loader.load_data()
        active_users_service = ActiveUsersService(data_loader)

        # Usar o método completo que inclui inactive_producers
        kpis = active_users_service.calculate_active_users_kpis()

        logger.info(f"KPIs calculados com serviço completo: {kpis}")

    except Exception as e:
        logger.error(f"Erro ao carregar dados: {e}")
        kpis = {
            'total_producers': 0,
            'inactive_producers': 0,
            'total_invoices': 0,
            'total_revenue': 0,
            'avg_ticket': 0
        }

    # Executar todas as análises avançadas usando o mesmo serviço
    try:
        # Análises principais
        turma_analysis = active_users_service.analyze_production_by_turma()
        university_analysis = active_users_service.analyze_production_by_university()
        producer_ranking = active_users_service.get_top_producers()
        temporal_analysis = active_users_service.analyze_temporal_trends()
        quality_metrics = active_users_service.calculate_quality_metrics()

        # Novas análises avançadas
        unmapped_analysis = active_users_service.analyze_unmapped_users()
        geographic_analysis = active_users_service.analyze_geographic_distribution()
        course_analysis = active_users_service.analyze_course_performance()
        implementation_analysis = active_users_service.analyze_implementation_status()
        time_to_invoice_analysis = active_users_service.analyze_time_to_first_invoice()

        # Análise Month over Month
        monthly_trends = active_users_service.analyze_monthly_trends()

        logger.info("Todas as análises executadas com sucesso")

    except Exception as e:
        logger.error(f"Erro nas análises avançadas: {e}")
        turma_analysis = None
        university_analysis = None
        producer_ranking = None
        temporal_analysis = None
        quality_metrics = None
        unmapped_analysis = None
        geographic_analysis = None
        course_analysis = None
        implementation_analysis = None
        time_to_invoice_analysis = None
        monthly_trends = None

    return render_template('active_users/index.html',
                         kpis=kpis,
                         turma_analysis=turma_analysis,
                         university_analysis=university_analysis,
                         producer_ranking=producer_ranking,
                         temporal_analysis=temporal_analysis,
                         quality_metrics=quality_metrics,
                         unmapped_analysis=unmapped_analysis,
                         geographic_analysis=geographic_analysis,
                         course_analysis=course_analysis,
                         implementation_analysis=implementation_analysis,
                         time_to_invoice_analysis=time_to_invoice_analysis,
                         monthly_trends=monthly_trends)



@active_users_bp.route('/api/kpis')
def api_kpis() -> Response:
    """API endpoint for active users KPIs"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        active_users_service = ActiveUsersService(data_loader)
        result = active_users_service.calculate_active_users_kpis()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in active users KPIs API: {e}")
        return jsonify({"error": str(e)}), 500

@active_users_bp.route('/api/turma-analysis')
def api_turma_analysis() -> Response:
    """API endpoint for turma production analysis"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        active_users_service = ActiveUsersService(data_loader)
        result = active_users_service.analyze_production_by_turma()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in turma analysis API: {e}")
        return jsonify({"error": str(e)}), 500

@active_users_bp.route('/api/university-analysis')
def api_university_analysis() -> Response:
    """API endpoint for university production analysis"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        active_users_service = ActiveUsersService(data_loader)
        result = active_users_service.analyze_production_by_university()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in university analysis API: {e}")
        return jsonify({"error": str(e)}), 500

@active_users_bp.route('/api/producer-ranking')
def api_producer_ranking() -> Response:
    """API endpoint for top producers ranking"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        active_users_service = ActiveUsersService(data_loader)
        result = active_users_service.get_top_producers()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in producer ranking API: {e}")
        return jsonify({"error": str(e)}), 500

@active_users_bp.route('/api/temporal-trends')
def api_temporal_trends() -> Response:
    """API endpoint for temporal trends analysis"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        active_users_service = ActiveUsersService(data_loader)
        result = active_users_service.analyze_temporal_trends()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in temporal trends API: {e}")
        return jsonify({"error": str(e)}), 500

@active_users_bp.route('/api/quality-metrics')
def api_quality_metrics() -> Response:
    """API endpoint for quality metrics"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        active_users_service = ActiveUsersService(data_loader)
        result = active_users_service.calculate_quality_metrics()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in quality metrics API: {e}")
        return jsonify({"error": str(e)}), 500

@active_users_bp.route('/api/producer-details/<producer_id>')
def api_producer_details(producer_id: str) -> Response:
    """API endpoint for detailed producer information"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        active_users_service = ActiveUsersService(data_loader)
        result = active_users_service.get_producer_details(producer_id)

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in producer details API: {e}")
        return jsonify({"error": str(e)}), 500
