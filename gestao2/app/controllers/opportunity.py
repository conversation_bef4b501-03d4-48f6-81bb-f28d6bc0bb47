"""
Amigo DataHub - Opportunity Controller
"""

import json
import logging
from flask import Blueprint, render_template, redirect, url_for, flash
from app.services.data_loader import DataLoader
from app.services.business_rules import BusinessRulesService
from app.services.data_processing import DataProcessingService
from app.services.analytics import AnalyticsService
from app.utils.formatters import format_currency, prepare_chart_data
from app.models.opportunity import Opportunity
from typing import Dict, List, Any, Optional, Union, Tuple

logger = logging.getLogger(__name__)

# Create blueprint
opportunity_bp = Blueprint('opportunity', __name__, url_prefix='/opportunities')

@opportunity_bp.route('/')
def index() -> str:
    """Opportunity index page"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        business_rules = BusinessRulesService(data_loader)
        data_processing = DataProcessingService(data_loader)
        analytics = AnalyticsService(data_loader, business_rules)

        # Get opportunities list for display
        all_opps_list = data_processing.get_opportunities_list(active_only=False)

        # Calculate ALL statistics using business_rules for consistency
        _, _, _, total_opps, opps_with_impl, opp_to_impl_rate = business_rules.calculate_conversion_rates()

        # Calculate active opportunities (without implementation) using business_rules logic
        df = data_loader.get_data()
        opp_id_col = data_loader.get_opportunity_id_column()

        # Log debug information
        logger.info(f"Debug - Data shape: {df.shape}")
        logger.info(f"Debug - Opportunity ID column: {opp_id_col}")
        logger.info(f"Debug - Total opps from business_rules: {total_opps}")
        logger.info(f"Debug - Opps with impl from business_rules: {opps_with_impl}")
        logger.info(f"Debug - All opps list length: {len(all_opps_list)}")

        if opp_id_col and 'Fase_Implantacao' in df.columns:
            # Active opportunities: those without implementation phase (consistent with business_rules)
            total_active_opps = df[df['Fase_Implantacao'].isna()][opp_id_col].nunique()

            # Converted opportunities: those with finalized implementations (truly converted)
            total_converted_opps = df[df['Status_Implantacao'] == 'Finalizado'][opp_id_col].nunique()

            # Log calculated values
            logger.info(f"Debug - Active opps calculated: {total_active_opps}")
            logger.info(f"Debug - Converted opps calculated: {total_converted_opps}")
        else:
            # Fallback calculations
            total_active_opps = total_opps - opps_with_impl
            total_converted_opps = opps_with_impl
            logger.warning(f"Debug - Using fallback calculations: active={total_active_opps}, converted={total_converted_opps}")

        # Calculate average values
        avg_value = "R$ 0,00"
        converted_avg_value = "R$ 0,00"
        avg_funnel_time = 0.0
        avg_conversion_rate = opp_to_impl_rate

        # Prepare chart data
        chart_data = analytics.prepare_chart_data()

        # Ensure all values are strings
        chart_data = prepare_chart_data(chart_data)

        return render_template('opportunities/index.html',
                              opportunities=all_opps_list,
                              total_opps=total_opps,
                              total_active_opps=total_active_opps,
                              total_converted_opps=total_converted_opps,
                              avg_value=avg_value,
                              converted_avg_value=converted_avg_value,
                              avg_funnel_time=avg_funnel_time,
                              avg_conversion_rate=avg_conversion_rate,
                              chart_data=json.dumps(chart_data))
    except Exception as e:
        logger.error(f"Error in opportunity index: {e}")
        flash(f'Error loading opportunities: {str(e)}', 'error')
        return render_template('opportunities/index.html',
                              opportunities=[],
                              total_opps=0,
                              total_active_opps=0,
                              total_converted_opps=0,
                              avg_value="R$ 0,00",
                              converted_avg_value="R$ 0,00",
                              avg_funnel_time=0.0,
                              avg_conversion_rate=0.0,
                              chart_data=json.dumps({}))

@opportunity_bp.route('/<opp_id>')
def detail(opp_id: int) -> str:
    """
    # Check opportunities permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission

    if not check_permission('opportunities.view'):
        flash('Acesso negado. Permissão insuficiente para acessar esta página.', 'error')
        return redirect(url_for('auth.login'))
Opportunity detail page"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        data_processing = DataProcessingService(data_loader)

        # Get opportunity detail
        opp_data, has_implementation = data_processing.get_opportunity_detail(opp_id)

        if not opp_data:
            flash('Opportunity not found', 'error')
            return redirect(url_for('opportunity.index'))

        # Create opportunity model
        opportunity = Opportunity.from_dict(opp_data)

        return render_template('opportunities/detail.html',
                              opportunity=opp_data,
                              has_implementation=has_implementation)
    except Exception as e:
        logger.error(f"Error in opportunity detail: {e}")
        flash(f'Error loading opportunity details: {str(e)}', 'error')
        return redirect(url_for('opportunity.index'))
