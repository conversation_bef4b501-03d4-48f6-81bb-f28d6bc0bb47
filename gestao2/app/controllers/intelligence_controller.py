"""
Amigo DataHub - Intelligence Controller
Advanced behavioral analysis using composite performance index
"""

import logging
from flask import Blueprint, render_template, jsonify, request
from app.services.data_loader import DataLoader
from app.services.intelligence_index_service import IntelligenceIndexService
from typing import Dict, Any
from flask import Response

logger = logging.getLogger(__name__)

# Create blueprint
intelligence_bp = Blueprint('intelligence', __name__, url_prefix='/intelligence')

@intelligence_bp.route('/')
def index() -> str:
    """Intelligence dashboard with composite index analysis"""
    try:
        # Load data and create service
        data_loader = DataLoader()
        data_loader.load_data()
        intelligence_service = IntelligenceIndexService(data_loader)

        # Get overall intelligence summary
        intelligence_summary = intelligence_service.get_overall_intelligence_summary()
        
        # Analyze by key dimensions
        turma_analysis = intelligence_service.analyze_by_dimension('Turma')
        estado_analysis = intelligence_service.analyze_by_dimension('Estado')
        responsavel_analysis = intelligence_service.analyze_by_dimension('Nome_Responsavel')

        logger.info("Intelligence analysis completed successfully")

    except Exception as e:
        logger.error(f"Error in intelligence analysis: {e}")
        intelligence_summary = {"error": str(e)}
        turma_analysis = {"error": str(e)}
        estado_analysis = {"error": str(e)}
        responsavel_analysis = {"error": str(e)}

    return render_template('intelligence/index.html',
                         intelligence_summary=intelligence_summary,
                         turma_analysis=turma_analysis,
                         estado_analysis=estado_analysis,
                         responsavel_analysis=responsavel_analysis)

@intelligence_bp.route('/api/overall-summary')
def api_overall_summary() -> Response:
    """API endpoint for overall intelligence summary"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()
        intelligence_service = IntelligenceIndexService(data_loader)
        
        result = intelligence_service.get_overall_intelligence_summary()
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in overall summary API: {e}")
        return jsonify({"error": str(e)}), 500

@intelligence_bp.route('/api/dimension-analysis/<dimension>')
def api_dimension_analysis(dimension: str) -> Response:
    """API endpoint for dimension-specific analysis"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()
        intelligence_service = IntelligenceIndexService(data_loader)
        
        result = intelligence_service.analyze_by_dimension(dimension)
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in dimension analysis API: {e}")
        return jsonify({"error": str(e)}), 500

@intelligence_bp.route('/api/composite-index')
def api_composite_index() -> Response:
    """API endpoint for composite index calculation"""
    try:
        dimension_col = request.args.get('dimension_col')
        dimension_value = request.args.get('dimension_value')
        
        data_loader = DataLoader()
        data_loader.load_data()
        intelligence_service = IntelligenceIndexService(data_loader)
        
        result = intelligence_service.calculate_composite_index(dimension_col, dimension_value)
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in composite index API: {e}")
        return jsonify({"error": str(e)}), 500
