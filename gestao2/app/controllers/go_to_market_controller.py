"""
Go-to-Market Controller
Dedicated controller for Go-to-Market analysis and strategies
"""

from flask import Blueprint, render_template, jsonify, request, flash, redirect, url_for
from app.services.data_loader import DataLoader
from app.services.commercial_intelligence import CommercialIntelligenceService
from app.services.ML.final_clustering_service import FinalClusteringService
from app.services.intelligence_index_service import IntelligenceIndexService
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create blueprint
gtm_bp = Blueprint('go_to_market', __name__, url_prefix='/go-to-market')

@gtm_bp.route('/')
def index():
    """Go-to-Market dashboard with comprehensive analysis"""
    # Check permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission

    if not check_permission('commercial_intelligence.view'):
        flash('Acesso negado. Permissão insuficiente para acessar Go-to-Market.', 'error')
        return redirect(url_for('auth.login'))

    try:
        # Load data
        data_loader = DataLoader()
        data_loader.load_data()

        # Initialize services
        ci_service = CommercialIntelligenceService(data_loader)
        intelligence_service = IntelligenceIndexService(data_loader)

        # Execute all analyses
        logger.info("Iniciando análises Go-to-Market...")

        # 0. Intelligence Index (Nova integração)
        intelligence_summary = intelligence_service.get_overall_intelligence_summary()
        logger.info(f"Intelligence Index: {'✓' if intelligence_summary and 'error' not in intelligence_summary else '✗'}")

        # 1. ML Clustering
        clustering_service = FinalClusteringService()
        clustering_data = clustering_service.perform_clustering()
        logger.info(f"ML Clustering: {'✓' if clustering_data and 'error' not in clustering_data else '✗'}")

        # 1. Commercial Index
        commercial_index = ci_service.calculate_commercial_index()
        logger.info(f"Commercial Index: {'✓' if 'error' not in commercial_index else '✗'}")

        # 2. Captacao Intelligence
        captacao_intelligence = ci_service.analyze_captacao_intelligence()
        logger.info(f"Captacao Intelligence: {'✓' if 'error' not in captacao_intelligence else '✗'}")

        # 3. Segmentation
        segmentation = ci_service.perform_class_segmentation(n_clusters=5)
        logger.info(f"Segmentation: {'✓' if 'error' not in segmentation else '✗'}")

        # 4. Business Metrics
        business_metrics = ci_service.calculate_business_metrics()
        logger.info(f"Business Metrics: {'✓' if 'error' not in business_metrics else '✗'}")

        # 5. Churn Analysis
        churn_analysis = ci_service.analyze_churn_risk()
        logger.info(f"Churn Analysis: {'✓' if 'error' not in churn_analysis else '✗'}")

        # 6. Potential Prediction
        potential_prediction = ci_service.predict_class_potential()
        logger.info(f"Potential Prediction: {'✓' if 'error' not in potential_prediction else '✗'}")

        # 7. Revenue Forecast
        revenue_forecast = ci_service.predict_revenue_forecast(months_ahead=6)
        logger.info(f"Revenue Forecast: {'✓' if 'error' not in revenue_forecast else '✗'}")

        # 8. Comprehensive Report
        comprehensive_report = ci_service.generate_comprehensive_report()
        logger.info(f"Comprehensive Report: {'✓' if 'error' not in comprehensive_report else '✗'}")

        # 9. Processar insights Go-to-Market
        gtm_insights = process_gtm_insights(clustering_data, captacao_intelligence, business_metrics)
        expansion_strategies = generate_expansion_strategies(gtm_insights)
        logger.info(f"GTM Insights: {'✓' if gtm_insights else '✗'}")

        # Process data for template
        top_classes = []
        bottom_classes = []
        segmentation_data = []
        risk_distribution = {}

        # Extract top/bottom classes from captacao intelligence
        if 'error' not in captacao_intelligence:
            top_captacao = captacao_intelligence.get('top_captacao', [])[:5]
            bottom_captacao = captacao_intelligence.get('bottom_captacao', [])[:5]

            for turma in top_captacao:
                top_classes.append({
                    'turma': turma.get('turma', 'N/A'),
                    'score': turma.get('score_captacao', 0),
                    'conversao': turma.get('taxa_conversao_geral', 0)
                })

            for turma in bottom_captacao:
                bottom_classes.append({
                    'turma': turma.get('turma', 'N/A'),
                    'score': turma.get('score_captacao', 0),
                    'conversao': turma.get('taxa_conversao_geral', 0)
                })

        # Extract segmentation data
        if 'error' not in segmentation:
            clusters = segmentation.get('clusters', [])
            for cluster in clusters:
                segmentation_data.append({
                    'cluster_id': cluster.get('cluster_id', 0),
                    'size': cluster.get('size', 0),
                    'avg_revenue': cluster.get('avg_revenue', 0),
                    'avg_conversion': cluster.get('avg_conversion', 0),
                    'avg_students': cluster.get('avg_students', 0)
                })

        # Extract risk distribution
        if 'error' not in churn_analysis:
            risk_distribution = churn_analysis.get('risk_distribution', {})

        # Calculate success metrics
        successful_analyses = sum([
            bool(captacao_intelligence and 'error' not in captacao_intelligence),
            bool(commercial_index and 'error' not in commercial_index),
            bool(segmentation and 'error' not in segmentation),
            bool(business_metrics and 'error' not in business_metrics),
            bool(churn_analysis and 'error' not in churn_analysis),
            bool(potential_prediction and 'error' not in potential_prediction),
            bool(revenue_forecast and 'error' not in revenue_forecast),
            bool(comprehensive_report and 'error' not in comprehensive_report)
        ])

        total_analyses = 8
        analysis_success_rate = (successful_analyses / total_analyses) * 100
        analysis_complete = successful_analyses >= 4

        logger.info(f"Go-to-Market análises concluídas: {successful_analyses}/{total_analyses} ({analysis_success_rate:.1f}%)")

        return render_template('go_to_market/index.html',
                              intelligence_summary=intelligence_summary,
                              top_classes=top_classes,
                              bottom_classes=bottom_classes,
                              segmentation_data=segmentation_data,
                              risk_distribution=risk_distribution,
                              commercial_index=commercial_index,
                              captacao_intelligence=captacao_intelligence,
                              business_metrics=business_metrics,
                              segmentation=segmentation,
                              churn_analysis=churn_analysis,
                              potential_prediction=potential_prediction,
                              revenue_forecast=revenue_forecast,
                              comprehensive_report=comprehensive_report,
                              clustering_data=clustering_data,
                              gtm_insights=gtm_insights,
                              expansion_strategies=expansion_strategies,
                              analysis_complete=analysis_complete,
                              analysis_success_rate=analysis_success_rate,
                              successful_analyses=successful_analyses,
                              total_analyses=total_analyses)

    except Exception as e:
        logger.error(f"Erro no Go-to-Market: {e}")
        flash('Erro interno do sistema. Tente novamente mais tarde.', 'error')
        return render_template('go_to_market/index.html',
                              top_classes=[],
                              bottom_classes=[],
                              segmentation_data=[],
                              risk_distribution={},
                              commercial_index={},
                              captacao_intelligence={},
                              business_metrics={},
                              segmentation={},
                              churn_analysis={},
                              potential_prediction={},
                              revenue_forecast={},
                              comprehensive_report={},
                              analysis_complete=False,
                              analysis_success_rate=0,
                              successful_analyses=0,
                              total_analyses=8)

@gtm_bp.route('/api/market-analysis')
def api_market_analysis():
    """API endpoint for market analysis"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        ci_service = CommercialIntelligenceService(data_loader)
        result = ci_service.analyze_captacao_intelligence()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in market analysis API: {e}")
        return jsonify({"error": str(e)}), 500

@gtm_bp.route('/api/strategy-recommendations')
def api_strategy_recommendations():
    """API endpoint for strategy recommendations"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        ci_service = CommercialIntelligenceService(data_loader)
        result = ci_service.generate_comprehensive_report()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in strategy recommendations API: {e}")
        return jsonify({"error": str(e)}), 500

def process_gtm_insights(clustering_data, captacao_intelligence, business_metrics):
    """Processar dados para insights de Go-to-Market"""
    try:
        insights = {
            'total_turmas': 0,
            'clusters_performance': {},
            'high_potential_segments': [],
            'expansion_opportunities': [],
            'revenue_potential': 0,
            'market_penetration': {},
            'channel_effectiveness': {}
        }

        # Processar dados de clusterização ML
        if clustering_data and 'cluster_analysis' in clustering_data:
            cluster_profiles = clustering_data['cluster_analysis'].get('cluster_profiles', [])
            insights['total_turmas'] = clustering_data.get('total_turmas', 0)

            for profile in cluster_profiles:
                cluster_id = profile.get('cluster_id')
                business_metrics_cluster = profile.get('business_metrics', {})

                insights['clusters_performance'][f'cluster_{cluster_id}'] = {
                    'size': profile.get('size', 0),
                    'percentage': profile.get('percentage', 0),
                    'avg_leads': business_metrics_cluster.get('total_leads', 0),
                    'conversion_rate': business_metrics_cluster.get('taxa_conversao', 0),
                    'revenue_per_lead': business_metrics_cluster.get('receita_por_lead', 0),
                    'implementations': business_metrics_cluster.get('volume_implementacoes', 0)
                }

                # Identificar segmentos de alto potencial
                if (business_metrics_cluster.get('taxa_conversao', 0) > 20 and
                    business_metrics_cluster.get('receita_por_lead', 0) > 300):
                    insights['high_potential_segments'].append({
                        'cluster_id': cluster_id,
                        'size': profile.get('size', 0),
                        'conversion_rate': business_metrics_cluster.get('taxa_conversao', 0),
                        'revenue_potential': business_metrics_cluster.get('receita_por_lead', 0) * business_metrics_cluster.get('total_leads', 0)
                    })

        # Processar dados de captação
        if captacao_intelligence and 'error' not in captacao_intelligence:
            top_performers = captacao_intelligence.get('top_captacao', [])
            for performer in top_performers[:5]:
                insights['expansion_opportunities'].append({
                    'turma': performer.get('turma'),
                    'score': performer.get('score_captacao', 0),
                    'revenue': performer.get('receita_total', 0),
                    'conversion': performer.get('taxa_conversao_geral', 0)
                })

        # Calcular potencial de receita
        insights['revenue_potential'] = sum([
            seg['revenue_potential'] for seg in insights['high_potential_segments']
        ])

        return insights

    except Exception as e:
        logger.error(f"Erro ao processar insights GTM: {e}")
        return {}

def generate_expansion_strategies(gtm_insights):
    """Gerar estratégias de expansão baseadas nos insights"""
    try:
        strategies = {
            'omnichannel_approach': [],
            'segment_targeting': [],
            'geographic_expansion': [],
            'channel_optimization': []
        }

        # Estratégias baseadas em clusters de alto potencial
        for segment in gtm_insights.get('high_potential_segments', []):
            if segment['conversion_rate'] > 25:
                strategies['segment_targeting'].append({
                    'strategy': f"Foco intensivo no Cluster {segment['cluster_id']}",
                    'reason': f"Taxa de conversão de {segment['conversion_rate']:.1f}% com {segment['size']} turmas",
                    'action': "Aumentar investimento em marketing direcionado",
                    'priority': 'Alta'
                })

        # Estratégias omnichannel
        strategies['omnichannel_approach'] = [
            {
                'channel': 'Digital Marketing',
                'strategy': 'SEO/SEM para turmas de alta conversão',
                'investment': 'Alto',
                'expected_roi': '300%+'
            },
            {
                'channel': 'Social Media',
                'strategy': 'Campanhas segmentadas por cluster',
                'investment': 'Médio',
                'expected_roi': '200%+'
            },
            {
                'channel': 'Email Marketing',
                'strategy': 'Nurturing personalizado por perfil',
                'investment': 'Baixo',
                'expected_roi': '400%+'
            }
        ]

        return strategies

    except Exception as e:
        logger.error(f"Erro ao gerar estratégias de expansão: {e}")
        return {}
