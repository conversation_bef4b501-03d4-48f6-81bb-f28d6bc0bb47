"""
Amigo DataHub - Commercial Intelligence Controller
Advanced ML-based commercial analysis for class segmentation
"""

import logging
from flask import Blueprint, render_template, flash, jsonify, request
from app.services.data_loader import DataLoader
from app.services.commercial_intelligence import CommercialIntelligenceService
from typing import Dict, List, Any, Optional, Union, Tuple
from flask import Response

logger = logging.getLogger(__name__)

# Constants
TEMPLATE_PATH = 'commercial_intelligence/index.html'

# Create blueprint
ci_bp = Blueprint('commercial_intelligence', __name__, url_prefix='/commercial-intelligence')

@ci_bp.route('/')
def index() -> str:
    """Enhanced Commercial Intelligence dashboard with ALL analyses pre-executed"""
    # Check commercial intelligence permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission

    if not check_permission('commercial_intelligence.view'):
        flash('Acesso negado. Permissão insuficiente para acessar inteligência comercial.', 'error')
        return redirect(url_for('auth.login'))
    try:
        logger.info("Iniciando execução completa da Inteligência Comercial")

        # Initialize services with error handling
        data_loader = DataLoader()

        # Verify data loading
        try:
            data_loader.load_data()
            df = data_loader.get_data()
            if df is None or df.empty:
                raise ValueError("Dados não carregados ou vazios")
            logger.info(f"Dados carregados com sucesso: {len(df)} registros")
        except Exception as e:
            logger.error(f"Erro ao carregar dados: {e}")
            raise ValueError(f"Falha no carregamento de dados: {e}")

        ci_service = CommercialIntelligenceService(data_loader)

        # Execute analyses with error handling
        logger.info("Executando análise de captação...")
        try:
            captacao_intelligence = ci_service.analyze_captacao_intelligence()
            if 'error' in captacao_intelligence:
                logger.warning(f"Erro na análise de captação: {captacao_intelligence['error']}")
                captacao_intelligence = {}
        except Exception as e:
            logger.error(f"Erro na análise de captação: {e}")
            captacao_intelligence = {}

        logger.info("Calculando métricas de negócio...")
        try:
            from app.services.business_rules import BusinessRulesService
            business_rules = BusinessRulesService(data_loader)
            business_metrics = business_rules.calculate_business_metrics()
        except Exception as e:
            logger.error(f"Erro nas métricas de negócio: {e}")
            business_metrics = {}

        logger.info("Calculando índice comercial...")
        try:
            commercial_index = ci_service.calculate_commercial_index()
            if 'error' in commercial_index:
                logger.warning(f"Erro no índice comercial: {commercial_index['error']}")
                commercial_index = {}
        except Exception as e:
            logger.error(f"Erro no índice comercial: {e}")
            commercial_index = {}

        logger.info("Executando segmentação de classes...")
        try:
            segmentation = ci_service.perform_class_segmentation(n_clusters=5)
            if 'error' in segmentation:
                logger.warning(f"Erro na segmentação: {segmentation['error']}")
                segmentation = {}
        except Exception as e:
            logger.error(f"Erro na segmentação: {e}")
            segmentation = {}

        logger.info("Analisando risco de churn...")
        try:
            churn_analysis = ci_service.analyze_churn_risk()
            if 'error' in churn_analysis:
                logger.warning(f"Erro na análise de churn: {churn_analysis['error']}")
                churn_analysis = {}
        except Exception as e:
            logger.error(f"Erro na análise de churn: {e}")
            churn_analysis = {}

        logger.info("Gerando previsão de potencial...")
        try:
            potential_prediction = ci_service.predict_class_potential()
            if 'error' in potential_prediction:
                logger.warning(f"Erro na previsão de potencial: {potential_prediction['error']}")
                potential_prediction = {}
        except Exception as e:
            logger.error(f"Erro na previsão de potencial: {e}")
            potential_prediction = {}

        logger.info("Gerando previsão de receita...")
        try:
            revenue_forecast = ci_service.predict_revenue_forecast(months_ahead=6)
            if 'error' in revenue_forecast:
                logger.warning(f"Erro na previsão de receita: {revenue_forecast['error']}")
                revenue_forecast = {}
        except Exception as e:
            logger.error(f"Erro na previsão de receita: {e}")
            revenue_forecast = {}

        logger.info("Gerando relatório abrangente...")
        try:
            comprehensive_report = ci_service.generate_comprehensive_report()
            if 'error' in comprehensive_report:
                logger.warning(f"Erro no relatório abrangente: {comprehensive_report['error']}")
                comprehensive_report = {}
        except Exception as e:
            logger.error(f"Erro no relatório abrangente: {e}")
            comprehensive_report = {}

        logger.info("Executando segmentações múltiplas...")
        # Multiple segmentations for comparison
        segmentation_results = {}
        for n_clusters in [3, 4, 5, 6]:
            try:
                result = ci_service.perform_class_segmentation(n_clusters=n_clusters)
                if result and 'error' not in result:
                    segmentation_results[n_clusters] = result
                else:
                    logger.warning(f"Segmentação com {n_clusters} clusters retornou erro ou vazio")
            except Exception as e:
                logger.warning(f"Erro na segmentação com {n_clusters} clusters: {e}")

        logger.info("Executando previsões múltiplas...")
        # Multiple forecasts for different periods
        forecast_results = {}
        for months in [3, 6, 12]:
            try:
                result = ci_service.predict_revenue_forecast(months_ahead=months)
                if result and 'error' not in result:
                    forecast_results[months] = result
                else:
                    logger.warning(f"Previsão de {months} meses retornou erro ou vazio")
            except Exception as e:
                logger.warning(f"Erro na previsão de {months} meses: {e}")

        # Prepare data for template
        top_classes = []
        bottom_classes = []
        segmentation_data = []
        risk_distribution = {}

        # Use captacao intelligence as primary source
        if 'error' not in captacao_intelligence and 'top_captacao' in captacao_intelligence:
            top_classes = captacao_intelligence.get('top_captacao', [])[:10]
            bottom_classes = captacao_intelligence.get('baixo_potencial', [])[:10]

            # Create risk distribution from captacao potencial
            distribuicao_potencial = captacao_intelligence.get('resumo', {}).get('distribuicao_potencial', {})
            risk_mapping = {
                'Alto Potencial': 'Baixo',
                'Bom Potencial': 'Médio',
                'Potencial Moderado': 'Médio',
                'Baixo Potencial': 'Alto',
                'Potencial Crítico': 'Crítico'
            }
            for potencial, count in distribuicao_potencial.items():
                risk_level = risk_mapping.get(potencial, 'Médio')
                risk_distribution[risk_level] = risk_distribution.get(risk_level, 0) + count

        # Fallback to commercial index if captacao fails
        elif 'error' not in commercial_index:
            summary = commercial_index.get('summary_stats', {})
            top_classes = summary.get('top_performers', [])
            bottom_classes = summary.get('bottom_performers', [])

        if 'error' not in segmentation:
            segmentation_data = segmentation.get('clusters', [])

        if 'error' not in churn_analysis and not risk_distribution:
            risk_distribution = churn_analysis.get('risk_distribution', {})

        # Calculate analysis success metrics
        successful_analyses = sum([
            bool(captacao_intelligence),
            bool(commercial_index),
            bool(segmentation),
            bool(business_metrics),
            bool(churn_analysis),
            bool(potential_prediction),
            bool(revenue_forecast),
            bool(comprehensive_report)
        ])

        total_analyses = 8
        analysis_success_rate = (successful_analyses / total_analyses) * 100
        analysis_complete = successful_analyses >= 4  # At least 50% success

        logger.info(f"Análises concluídas: {successful_analyses}/{total_analyses} ({analysis_success_rate:.1f}%)")

        return render_template(TEMPLATE_PATH,
                              top_classes=top_classes,
                              bottom_classes=bottom_classes,
                              segmentation_data=segmentation_data,
                              risk_distribution=risk_distribution,
                              commercial_index=commercial_index,
                              captacao_intelligence=captacao_intelligence,
                              business_metrics=business_metrics,
                              segmentation=segmentation,
                              churn_analysis=churn_analysis,
                              potential_prediction=potential_prediction,
                              revenue_forecast=revenue_forecast,
                              comprehensive_report=comprehensive_report,
                              segmentation_results=segmentation_results,
                              forecast_results=forecast_results,
                              analysis_complete=analysis_complete,
                              analysis_success_rate=analysis_success_rate,
                              successful_analyses=successful_analyses,
                              total_analyses=total_analyses)

    except (ValueError, KeyError) as e:
        logger.error(f"Data error in commercial intelligence: {e}")
        flash('Erro nos dados para análise comercial. Verifique os dados de entrada.', 'error')
        return render_template(TEMPLATE_PATH,
                              top_classes=[],
                              bottom_classes=[],
                              segmentation_data=[],
                              risk_distribution={},
                              commercial_index={},
                              segmentation={},
                              churn_analysis={},
                              potential_prediction={},
                              revenue_forecast={},
                              comprehensive_report={},
                              segmentation_results={},
                              forecast_results={},
                              analysis_complete=False)
    except Exception as e:
        logger.error(f"Unexpected error in commercial intelligence: {e}")
        flash('Erro interno do sistema. Tente novamente mais tarde.', 'error')
        return render_template(TEMPLATE_PATH,
                              top_classes=[],
                              bottom_classes=[],
                              segmentation_data=[],
                              risk_distribution={},
                              commercial_index={},
                              segmentation={},
                              churn_analysis={},
                              potential_prediction={},
                              revenue_forecast={},
                              comprehensive_report={},
                              segmentation_results={},
                              forecast_results={},
                              analysis_complete=False)

@ci_bp.route('/api/commercial-index')
def api_commercial_index() -> Response:
    """API endpoint for commercial index"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        ci_service = CommercialIntelligenceService(data_loader)
        result = ci_service.calculate_commercial_index()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in commercial index API: {e}")
        return jsonify({"error": str(e)}), 500

@ci_bp.route('/api/segmentation')
def api_segmentation() -> Response:
    """API endpoint for class segmentation"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        ci_service = CommercialIntelligenceService(data_loader)

        # Get number of clusters from request
        n_clusters = request.args.get('clusters', 5, type=int)

        result = ci_service.perform_class_segmentation(n_clusters=n_clusters)

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in segmentation API: {e}")
        return jsonify({"error": str(e)}), 500

@ci_bp.route('/api/potential-prediction')
def api_potential_prediction() -> Response:
    """API endpoint for potential prediction"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        ci_service = CommercialIntelligenceService(data_loader)
        result = ci_service.predict_class_potential()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in potential prediction API: {e}")
        return jsonify({"error": str(e)}), 500

@ci_bp.route('/api/revenue-forecast')
def api_revenue_forecast() -> Response:
    """API endpoint for revenue forecast"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        ci_service = CommercialIntelligenceService(data_loader)

        # Get forecast period from request
        months_ahead = request.args.get('months', 6, type=int)

        result = ci_service.predict_revenue_forecast(months_ahead=months_ahead)

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in revenue forecast API: {e}")
        return jsonify({"error": str(e)}), 500

@ci_bp.route('/api/churn-analysis')
def api_churn_analysis() -> Response:
    """API endpoint for churn analysis"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        ci_service = CommercialIntelligenceService(data_loader)
        result = ci_service.analyze_churn_risk()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in churn analysis API: {e}")
        return jsonify({"error": str(e)}), 500

@ci_bp.route('/api/captacao-intelligence')
def api_captacao_intelligence() -> Response:
    """API endpoint for enhanced captacao intelligence analysis"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        ci_service = CommercialIntelligenceService(data_loader)
        result = ci_service.analyze_captacao_intelligence()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in captacao intelligence API: {e}")
        return jsonify({"error": str(e)}), 500

@ci_bp.route('/api/comprehensive-report')
def api_comprehensive_report() -> Response:
    """API endpoint for comprehensive report"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        ci_service = CommercialIntelligenceService(data_loader)
        result = ci_service.generate_comprehensive_report()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in comprehensive report API: {e}")
        return jsonify({"error": str(e)}), 500

@ci_bp.route('/segmentation')
def segmentation_detail() -> str:
    """Detailed segmentation analysis page"""
    # Check commercial intelligence permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission

    if not check_permission('commercial_intelligence.view'):
        flash('Acesso negado. Permissão insuficiente para acessar inteligência comercial.', 'error')
        return redirect(url_for('auth.login'))
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        ci_service = CommercialIntelligenceService(data_loader)

        # Get segmentation with different cluster numbers
        segmentation_results = {}
        for n_clusters in [3, 4, 5, 6, 7]:
            result = ci_service.perform_class_segmentation(n_clusters=n_clusters)
            if 'error' not in result:
                segmentation_results[n_clusters] = result

        return render_template('commercial_intelligence/segmentation.html',
                              segmentation_results=segmentation_results)

    except Exception as e:
        logger.error(f"Error in segmentation detail: {e}")
        flash(f'Erro ao carregar segmentação: {str(e)}', 'error')
        return render_template('commercial_intelligence/segmentation.html',
                              segmentation_results={})

@ci_bp.route('/forecasting')
def forecasting() -> str:
    """Revenue forecasting page"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        ci_service = CommercialIntelligenceService(data_loader)

        # Get forecasts for different periods
        forecast_results = {}
        for months in [3, 6, 12, 24]:
            result = ci_service.predict_revenue_forecast(months_ahead=months)
            if 'error' not in result:
                forecast_results[months] = result

        # Get potential predictions
        potential_prediction = ci_service.predict_class_potential()

        return render_template('commercial_intelligence/forecasting.html',
                              forecast_results=forecast_results,
                              potential_prediction=potential_prediction)

    except Exception as e:
        logger.error(f"Error in forecasting: {e}")
        flash(f'Erro ao carregar previsões: {str(e)}', 'error')
        return render_template('commercial_intelligence/forecasting.html',
                              forecast_results={},
                              potential_prediction={})

@ci_bp.route('/risk-analysis')
def risk_analysis() -> str:
    """Risk analysis page"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        ci_service = CommercialIntelligenceService(data_loader)

        # Get churn analysis
        churn_analysis = ci_service.analyze_churn_risk()

        # Get commercial index for context
        commercial_index = ci_service.calculate_commercial_index()

        return render_template('commercial_intelligence/risk_analysis.html',
                              churn_analysis=churn_analysis,
                              commercial_index=commercial_index)

    except Exception as e:
        logger.error(f"Error in risk analysis: {e}")
        flash(f'Erro ao carregar análise de risco: {str(e)}', 'error')
        return render_template('commercial_intelligence/risk_analysis.html',
                              churn_analysis={},
                              commercial_index={})

@ci_bp.route('/class/<class_name>')
def class_detail(class_name: str) -> str:
    """Detailed analysis for a specific class"""
    try:
        data_loader = DataLoader()
        data_loader.load_data()

        ci_service = CommercialIntelligenceService(data_loader)

        # Get comprehensive analysis
        comprehensive_report = ci_service.generate_comprehensive_report()

        # Extract data for the specific class
        class_data = {}

        if 'error' not in comprehensive_report:
            # Find class in commercial index
            commercial_index = comprehensive_report.get('commercial_index', {})
            if 'class_rankings' in commercial_index:
                for class_info in commercial_index['class_rankings']:
                    if class_info.get('turma') == class_name:
                        class_data['commercial_metrics'] = class_info
                        break

            # Find class in segmentation
            segmentation = comprehensive_report.get('segmentation', {})
            if 'features_df' in segmentation:
                # This would need to be processed to find the specific class
                pass

        return render_template('commercial_intelligence/class_detail.html',
                              class_name=class_name,
                              class_data=class_data,
                              comprehensive_report=comprehensive_report)

    except Exception as e:
        logger.error(f"Error in class detail: {e}")
        flash(f'Erro ao carregar detalhes da turma: {str(e)}', 'error')
        return render_template('commercial_intelligence/class_detail.html',
                              class_name=class_name,
                              class_data={},
                              comprehensive_report={})
