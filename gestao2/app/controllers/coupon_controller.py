"""
Amigo DataHub - Coupon Controller
"""

import logging
import pandas as pd
import json
import numpy as np
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from flask import Blueprint, render_template, flash
from markupsafe import Markup
from app.services.data_loader import DataLoader
from app.utils.formatters import format_currency, format_date
from typing import Dict, List, Any, Optional, Union, Tuple
import pandas as pd

logger = logging.getLogger(__name__)

# Create blueprint
coupon_bp = Blueprint('coupon', __name__, url_prefix='/coupons')

def calculate_cashflow(coupons_list: List[Any], monthly_value: Any = 0) -> float:
    """
    Calculate cash flow for coupons

    Args:
        coupons_list (list): List of coupon objects
        monthly_value (float): Default monthly value if not specified in coupon

    Returns:
        tuple: (cashflow_data, payback_periods, total_entry_payment)
    """
    # Initialize variables
    cashflow_data = {}
    payback_periods = {}
    total_entry_payment = 0

    # Current date for calculations
    current_date = datetime.now().date()

    # Process each coupon
    for coupon in coupons_list:
        try:
            # Get coupon details
            coupon_id = coupon.get('id')
            monthly_value_str = coupon.get('monthly_value', format_currency(monthly_value))
            monthly_value_float = float(monthly_value_str.replace('R$', '').replace('.', '').replace(',', '.').strip())
            exemption_months = coupon.get('exemption_months', 0)
            entry_payment_str = coupon.get('entry_payment', 'R$ 0,00')
            entry_payment = float(entry_payment_str.replace('R$', '').replace('.', '').replace(',', '.').strip())

            # Add to total entry payment
            total_entry_payment += entry_payment

            # Get graduation date
            graduation_date_str = coupon.get('graduation_date')
            if graduation_date_str and graduation_date_str != 'N/A':
                try:
                    graduation_date = datetime.strptime(graduation_date_str, '%d/%m/%Y').date()
                except:
                    graduation_date = current_date
            else:
                graduation_date = current_date

            # Calculate billing start date
            billing_start_date = graduation_date + relativedelta(months=exemption_months)

            # Calculate payback period (months needed to recover the exemption value)
            exemption_value_str = coupon.get('exemption_value', 'R$ 0,00')
            exemption_value = float(exemption_value_str.replace('R$', '').replace('.', '').replace(',', '.').strip())

            # If entry payment exists, subtract it from exemption value for payback calculation
            net_exemption_value = exemption_value - entry_payment

            # Calculate payback period in months (if monthly value is 0, set to infinity)
            if monthly_value_float > 0:
                payback_months = net_exemption_value / monthly_value_float
                payback_periods[coupon_id] = payback_months
            else:
                payback_periods[coupon_id] = float('inf')

            # Generate monthly cash flow for 24 months from current date
            for i in range(24):
                month_date = current_date + relativedelta(months=i)
                month_key = month_date.strftime('%Y-%m')

                # Initialize month if not exists
                if month_key not in cashflow_data:
                    cashflow_data[month_key] = {
                        'entry_payments': 0,
                        'monthly_payments': 0,
                        'exemption_value': 0,
                        'net_cashflow': 0,
                        'coupon_count': 0
                    }

                # Add entry payment if this is the graduation month
                if month_date.year == graduation_date.year and month_date.month == graduation_date.month:
                    cashflow_data[month_key]['entry_payments'] += entry_payment
                    cashflow_data[month_key]['net_cashflow'] += entry_payment
                    cashflow_data[month_key]['coupon_count'] += 1

                # Add monthly payment if after billing start date
                if month_date >= billing_start_date:
                    cashflow_data[month_key]['monthly_payments'] += monthly_value_float
                    cashflow_data[month_key]['net_cashflow'] += monthly_value_float

                # Add exemption value (negative cash flow) if in exemption period
                # This represents the cost of having active coupons in this month
                if month_date >= graduation_date and month_date < billing_start_date:
                    # Calculate exemption value for this month (cost of active coupon)
                    monthly_exemption = monthly_value_float
                    cashflow_data[month_key]['exemption_value'] += monthly_exemption
                    cashflow_data[month_key]['net_cashflow'] -= monthly_exemption
                    # Count this as an active coupon for this month
                    cashflow_data[month_key]['active_coupons'] = cashflow_data[month_key].get('active_coupons', 0) + 1
        except Exception as e:
            logger.error(f"Error calculating cash flow for coupon {coupon.get('id')}: {e}")

    return cashflow_data, payback_periods, total_entry_payment

@coupon_bp.route('/')
def index() -> str:
    """Coupon analysis page"""
    # Check coupons permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission

    if not check_permission('coupons.view'):
        flash('Acesso negado. Permissão insuficiente para acessar esta página.', 'error')
        return redirect(url_for('auth.login'))

    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()
        df = data_loader.get_data()

        # Log all columns for debugging
        logger.info(f"All columns in dataframe: {df.columns.tolist()}")

        # Create a minimal version with just the essential data
        coupons_list = []
        total_coupons = 0
        total_exemption_value = 0
        total_exemption_months = 0
        total_entry_payment = 0

        # Prepare data for charts and analysis
        coupon_by_university = {}
        coupon_by_responsible = {}
        coupon_by_exemption = {}
        coupon_by_month = {}
        coupon_by_year = {}

        # Current date for status calculation
        current_date = datetime.now().date()

        # Check if ID do cupom exists
        if 'ID do cupom' in df.columns:
            # Filter out rows with empty coupon ID
            df_coupons = df[df['ID do cupom'].notna()]

            # Get unique coupons
            unique_coupons = df_coupons['ID do cupom'].unique()
            total_coupons = len(unique_coupons)

            # Process each coupon
            for coupon_id in unique_coupons:
                try:
                    # Get all rows for this coupon
                    coupon_df = df_coupons[df_coupons['ID do cupom'] == coupon_id]

                    # Get the first row for this coupon
                    coupon_row = coupon_df.iloc[0]

                    # Get coupon details
                    description = str(coupon_row.get('Descrição', 'Não especificado'))

                    # Get exemption months (safely)
                    exemption_months = 0
                    if 'Isencao em Meses' in coupon_row:
                        try:
                            if pd.notna(coupon_row['Isencao em Meses']):
                                exemption_months = int(float(str(coupon_row['Isencao em Meses']).replace(',', '.')))
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Error converting exemption months for coupon {coupon_id}: {e}")

                    # Skip coupons with 0 months of exemption
                    if exemption_months == 0:
                        logger.info(f"Skipping coupon {coupon_id} with 0 months of exemption")
                        continue

                    # Update total exemption months
                    total_exemption_months += exemption_months

                    # Get graduation date
                    graduation_date = None
                    graduation_date_str = "N/A"
                    if 'Data da colacao' in coupon_row and pd.notna(coupon_row['Data da colacao']):
                        try:
                            graduation_date = pd.to_datetime(coupon_row['Data da colacao']).date()
                            graduation_date_str = graduation_date.strftime('%d/%m/%Y')

                            # Update coupon by month and year
                            month_key = graduation_date.strftime('%m/%Y')
                            year_key = str(graduation_date.year)

                            if month_key in coupon_by_month:
                                coupon_by_month[month_key] += 1
                            else:
                                coupon_by_month[month_key] = 1

                            if year_key in coupon_by_year:
                                coupon_by_year[year_key] += 1
                            else:
                                coupon_by_year[year_key] = 1
                        except Exception as e:
                            logger.warning(f"Error processing graduation date for coupon {coupon_id}: {e}")

                    # Calculate billing start date
                    billing_start_date = None
                    billing_start_date_str = "N/A"
                    if graduation_date is not None:
                        try:
                            # Billing starts after exemption period
                            billing_start_date = graduation_date + pd.DateOffset(months=exemption_months)
                            billing_start_date_str = billing_start_date.strftime('%d/%m/%Y')
                        except Exception as e:
                            logger.warning(f"Error calculating billing start date for coupon {coupon_id}: {e}")

                    # Calculate coupon status based on the business rules:
                    # - Pendente: Data de colação ainda não chegou
                    # - Ativo: Período de isenção em andamento (após colação, antes do início do faturamento)
                    # - Expirado: Período de isenção já terminou (após início do faturamento)
                    coupon_status = "Pendente"  # Default status

                    if graduation_date is not None:
                        # Log for debugging
                        logger.info(f"Coupon {coupon_id} - Current date: {current_date}")
                        logger.info(f"Coupon {coupon_id} - Graduation date: {graduation_date}")

                        if billing_start_date is not None:
                            logger.info(f"Coupon {coupon_id} - Billing start date: {billing_start_date.date()}")

                            # Determine status based on dates
                            if current_date < graduation_date:
                                # Colação ainda não ocorreu
                                coupon_status = "Pendente"
                                logger.info(f"Coupon {coupon_id} - Status: Pendente (colação futura)")
                            elif current_date < billing_start_date.date():
                                # Período de isenção em andamento
                                coupon_status = "Ativo"
                                logger.info(f"Coupon {coupon_id} - Status: Ativo (período de isenção)")
                            else:
                                # Período de isenção já terminou
                                coupon_status = "Concluído"
                                logger.info(f"Coupon {coupon_id} - Status: Concluído (isenção terminada)")

                    # Get university and responsible
                    university = str(coupon_row.get('Universidade', 'Não especificada'))
                    responsible = str(coupon_row.get('Nome_Responsavel', 'Não especificado'))

                    # Update university counts
                    if university in coupon_by_university:
                        coupon_by_university[university] += 1
                    else:
                        coupon_by_university[university] = 1

                    # Update responsible counts
                    if responsible in coupon_by_responsible:
                        coupon_by_responsible[responsible] += 1
                    else:
                        coupon_by_responsible[responsible] = 1

                    # Update exemption months distribution
                    exemption_key = f"{exemption_months} meses"
                    if exemption_key in coupon_by_exemption:
                        coupon_by_exemption[exemption_key] += 1
                    else:
                        coupon_by_exemption[exemption_key] = 1

                    # Calculate exemption value
                    monthly_value = 0
                    if 'Valor Mensalidade' in coupon_row and pd.notna(coupon_row['Valor Mensalidade']):
                        try:
                            value_str = str(coupon_row['Valor Mensalidade']).replace('R$', '').replace('.', '').replace(',', '.').strip()
                            monthly_value = float(value_str)
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Error converting monthly value for coupon {coupon_id}: {e}")

                    exemption_value = monthly_value * exemption_months
                    total_exemption_value += exemption_value

                    # Get entry payment value
                    entry_payment = 0
                    if 'Pagamento de Entrada' in coupon_row and pd.notna(coupon_row['Pagamento de Entrada']):
                        try:
                            entry_payment = float(coupon_row['Pagamento de Entrada'])
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Error converting entry payment for coupon {coupon_id}: {e}")

                    # Create coupon object for the table
                    coupon_obj = {
                        'id': str(coupon_id),
                        'description': description,
                        'exemption_months': exemption_months,
                        'entry_payment': format_currency(entry_payment),
                        'graduation_date': graduation_date_str,
                        'billing_start_date': billing_start_date_str,
                        'status': coupon_status,
                        'university': university,
                        'responsible': responsible,
                        'monthly_value': format_currency(monthly_value),
                        'exemption_value': format_currency(exemption_value)
                    }

                    coupons_list.append(coupon_obj)
                except Exception as e:
                    logger.error(f"Error processing coupon ID {coupon_id}: {e}")
                    continue
        else:
            logger.error("Column 'ID do cupom' not found in dataframe")
            flash('Estrutura de dados inválida: coluna ID do cupom não encontrada', 'error')

        # Calculate average exemption months
        avg_exemption_months = total_exemption_months / total_coupons if total_coupons > 0 else 0

        # Calculate cash flow, payback periods, and total entry payment
        cashflow_data, payback_periods, total_entry_payment = calculate_cashflow(coupons_list)

        # Calculate average payback period
        avg_payback_months = sum(payback_periods.values()) / len(payback_periods) if payback_periods else 0

        # Calculate CAC (Cost of Acquisition) - considering exemption value as cost
        total_cac = total_exemption_value - total_entry_payment
        avg_cac = total_cac / total_coupons if total_coupons > 0 else 0

        # Calculate conversion rates with and without entry payment
        df = data_loader.get_data()
        total_com_pagamento = df['Pagamento de Entrada'].notna().sum()
        total_sem_pagamento = df['Pagamento de Entrada'].isna().sum()
        finalizados_com_pagamento = df[(df['Status_Implantacao'] == 'Finalizado') & (df['Pagamento de Entrada'].notna())].shape[0]
        finalizados_sem_pagamento = df[(df['Status_Implantacao'] == 'Finalizado') & (df['Pagamento de Entrada'].isna())].shape[0]

        taxa_conversao_com_pagamento = (finalizados_com_pagamento / total_com_pagamento) * 100 if total_com_pagamento > 0 else 0
        taxa_conversao_sem_pagamento = (finalizados_sem_pagamento / total_sem_pagamento) * 100 if total_sem_pagamento > 0 else 0
        diferenca_taxa_conversao = taxa_conversao_com_pagamento - taxa_conversao_sem_pagamento

        # Prepare cash flow chart data
        cashflow_months = sorted(cashflow_data.keys())
        cashflow_chart_data = []

        # Entry payments data
        entry_payments_data = [{'name': month, 'value': cashflow_data[month]['entry_payments']} for month in cashflow_months]

        # Monthly payments data
        monthly_payments_data = [{'name': month, 'value': cashflow_data[month]['monthly_payments']} for month in cashflow_months]

        # Exemption value data (negative cash flow)
        exemption_value_data = [{'name': month, 'value': cashflow_data[month]['exemption_value']} for month in cashflow_months]

        # Net cash flow data
        net_cashflow_data = [{'name': month, 'value': cashflow_data[month]['net_cashflow']} for month in cashflow_months]

        # Format months for display
        formatted_months = [datetime.strptime(month, '%Y-%m').strftime('%b/%Y') for month in cashflow_months]

        # Prepare cumulative cash flow data with coupon impact analysis
        cumulative_cashflow = 0
        cumulative_cashflow_with_coupons = 0
        cumulative_cashflow_without_coupons = 0
        cumulative_cashflow_data = []

        for month in cashflow_months:
            # Net cashflow (current logic)
            cumulative_cashflow += cashflow_data[month]['net_cashflow']

            # Cashflow with coupons (includes exemption costs)
            cumulative_cashflow_with_coupons += (
                cashflow_data[month]['entry_payments'] +
                cashflow_data[month]['monthly_payments'] -
                cashflow_data[month]['exemption_value']
            )

            # Cashflow without coupons (theoretical - only monthly payments without exemption)
            cumulative_cashflow_without_coupons += (
                cashflow_data[month]['entry_payments'] +
                cashflow_data[month]['monthly_payments']
            )

            cumulative_cashflow_data.append({
                'name': datetime.strptime(month, '%Y-%m').strftime('%b/%Y'),
                'value': cumulative_cashflow,
                'with_coupons': cumulative_cashflow_with_coupons,
                'without_coupons': cumulative_cashflow_without_coupons,
                'coupon_impact': cumulative_cashflow_without_coupons - cumulative_cashflow_with_coupons
            })

        # Count coupon statuses
        status_counts = {"Pendente": 0, "Ativo": 0, "Concluído": 0}
        for coupon in coupons_list:
            status = coupon.get('status', 'Pendente')
            status_counts[status] = status_counts.get(status, 0) + 1

        # Prepare chart data
        coupon_status_data = [
            {'name': 'Pendentes', 'value': status_counts['Pendente']},
            {'name': 'Ativos', 'value': status_counts['Ativo']},
            {'name': 'Concluídos', 'value': status_counts['Concluído']}
        ]

        # Top universities by coupon count
        top_university_coupons = [{'name': university, 'value': count}
                                 for university, count in sorted(coupon_by_university.items(),
                                                               key=lambda x: x[1], reverse=True)[:10]]

        # Top responsibles by coupon count
        top_responsible_coupons = [{'name': responsible, 'value': count}
                                  for responsible, count in sorted(coupon_by_responsible.items(),
                                                                 key=lambda x: x[1], reverse=True)[:10]]

        # Exemption months distribution
        try:
            # Sort by number of months
            exemption_distribution = [{'name': months, 'value': count}
                                     for months, count in sorted(coupon_by_exemption.items(),
                                                               key=lambda x: int(x[0].split()[0]))]
        except Exception as e:
            logger.error(f"Error sorting exemption distribution: {e}")
            exemption_distribution = [{'name': months, 'value': count}
                                     for months, count in coupon_by_exemption.items()]

        # Monthly trend data
        monthly_trend_data = [{'name': month, 'value': count}
                             for month, count in sorted(coupon_by_month.items())]

        # Yearly trend data
        yearly_trend_data = [{'name': year, 'value': count}
                            for year, count in sorted(coupon_by_year.items())]

        # Prepare filters as JSON for the template
        filters_json = json.dumps([
            {
                "id": "status-filter",
                "type": "select",
                "placeholder": "Filtrar por status",
                "options": [
                    {"value": "all", "label": "Todos"},
                    {"value": "pending", "label": "Pendente"},
                    {"value": "active", "label": "Ativo"},
                    {"value": "expired", "label": "Concluído"}
                ]
            },
            {
                "id": "university-filter",
                "type": "select",
                "placeholder": "Filtrar por universidade",
                "options": [{"value": "all", "label": "Todas"}] + [
                    {"value": university.lower().replace(' ', '-'), "label": university}
                    for university in sorted(coupon_by_university.keys())
                ]
            },
            {
                "id": "responsible-filter",
                "type": "select",
                "placeholder": "Filtrar por responsável",
                "options": [{"value": "all", "label": "Todos"}] + [
                    {"value": responsible.lower().replace(' ', '-'), "label": responsible}
                    for responsible in sorted(coupon_by_responsible.keys())
                ]
            }
        ])

        return render_template('coupons/index.html',
                              coupons=coupons_list,
                              total_coupons=total_coupons,
                              avg_exemption_months=round(avg_exemption_months, 1),
                              total_exemption_value=format_currency(total_exemption_value),
                              total_entry_payment=format_currency(total_entry_payment),
                              avg_cac=format_currency(avg_cac),
                              avg_payback_months=round(avg_payback_months, 1),
                              taxa_conversao_com_pagamento=round(taxa_conversao_com_pagamento, 1),
                              taxa_conversao_sem_pagamento=round(taxa_conversao_sem_pagamento, 1),
                              diferenca_taxa_conversao=round(diferenca_taxa_conversao, 1),
                              total_com_pagamento=total_com_pagamento,
                              total_sem_pagamento=total_sem_pagamento,
                              finalizados_com_pagamento=finalizados_com_pagamento,
                              finalizados_sem_pagamento=finalizados_sem_pagamento,
                              coupon_status_data=coupon_status_data,
                              top_university_coupons=top_university_coupons,
                              top_responsible_coupons=top_responsible_coupons,
                              exemption_distribution=exemption_distribution,
                              monthly_trend_data=monthly_trend_data,
                              yearly_trend_data=yearly_trend_data,
                              entry_payments_data=entry_payments_data,
                              monthly_payments_data=monthly_payments_data,
                              exemption_value_data=exemption_value_data,
                              net_cashflow_data=net_cashflow_data,
                              cumulative_cashflow_data=cumulative_cashflow_data,
                              filters_json=Markup(filters_json))
    except Exception as e:
        logger.error(f"Error in coupon index page: {e}")
        flash(f'Error loading coupon data: {str(e)}', 'error')
        return render_template('coupons/index.html',
                              coupons=[],
                              total_coupons=0,
                              avg_exemption_months=0,
                              total_exemption_value=format_currency(0),
                              total_entry_payment=format_currency(0),
                              avg_cac=format_currency(0),
                              avg_payback_months=0,
                              taxa_conversao_com_pagamento=0,
                              taxa_conversao_sem_pagamento=0,
                              diferenca_taxa_conversao=0,
                              total_com_pagamento=0,
                              total_sem_pagamento=0,
                              finalizados_com_pagamento=0,
                              finalizados_sem_pagamento=0,
                              coupon_status_data=[],
                              top_university_coupons=[],
                              top_responsible_coupons=[],
                              exemption_distribution=[],
                              monthly_trend_data=[],
                              yearly_trend_data=[],
                              entry_payments_data=[],
                              monthly_payments_data=[],
                              exemption_value_data=[],
                              net_cashflow_data=[],
                              cumulative_cashflow_data=[],
                              filters_json=Markup('[]'))
