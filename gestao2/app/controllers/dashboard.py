"""
Amigo DataHub - Dashboard Controller
"""

import json
import logging
import sys
from pathlib import Path
from typing import Any, Dict, List
from flask import Blueprint, render_template, current_app, flash
from app.services.data_loader import DataLoader
from app.services.database_adapter import BusinessDatabaseAdapter
from app.services.business_rules import BusinessRulesService
from app.services.data_processing import DataProcessingService
from app.services.analytics import AnalyticsService
from app.utils.formatters import format_currency, prepare_chart_data

# Import permissions middleware
try:
    CORE_PATH = Path(__file__).parent.parent.parent.parent.parent / 'shared' / 'datamesh-core'
    sys.path.insert(0, str(CORE_PATH))

    from auth.permissions_middleware import require_business_permission, BusinessPermissions
    PERMISSIONS_AVAILABLE = True
except ImportError:
    PERMISSIONS_AVAILABLE = False
    # Fallback decorator that does nothing
    def require_business_permission(permission: List[Any]) -> Any:
        def decorator(f: Dict[str, Any]) -> Any:
            return f
        return decorator

    class BusinessPermissions:
        DASHBOARD_VIEW = 'dashboard.view'

logger = logging.getLogger(__name__)

# Create blueprint
dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@dashboard_bp.route('/index/')
def index() -> str:
    """Dashboard index page"""
    # Check dashboard permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission

    if not check_permission('dashboard.view'):
        flash('Acesso negado. Permissão insuficiente para acessar o dashboard.', 'error')
        return redirect(url_for('auth.login'))
    try:
        # Initialize database adapter
        db_adapter = BusinessDatabaseAdapter()

        # Try to get data from database first
        if db_adapter.is_database_available():
            logger.info("Using real database data for dashboard")
            dashboard_data = db_adapter.get_dashboard_data()

            # Extract KPIs from database
            kpis = dashboard_data['kpis']
            total_leads = kpis['total_leads']
            total_opportunities = kpis['total_opportunities']
            finalized_implementations = kpis['finalized_implementations']
            total_universities = kpis['total_universities']
            mrr_total = kpis['total_revenue']
            finalized_count = finalized_implementations

            # Calculate derived metrics
            active_implementations = total_opportunities - finalized_implementations
            total_implementations = total_opportunities  # Assuming all opportunities become implementations

            # Calculate average ticket
            ticket_medio = mrr_total / finalized_count if finalized_count > 0 else 0

        else:
            logger.info("Database not available, using file-based data")
            # Fallback to file-based data
            data_loader = DataLoader()
            data_loader.load_data()

            business_rules = BusinessRulesService(data_loader)

            # Get data
            df = data_loader.get_data()
            lead_id_col = data_loader.get_lead_id_column()
            opp_id_col = data_loader.get_opportunity_id_column()

            # Count leads, opportunities, and implementations
            total_leads = df[lead_id_col].dropna().nunique() if lead_id_col else 0
            total_opportunities = df[opp_id_col].dropna().nunique() if opp_id_col else 0

            # Use Data_Criacao_Implantacao to count implementations (consistent with Intelligence Index)
            total_implementations = df[df['Data_Criacao_Implantacao'].notna()][opp_id_col].nunique() if 'Data_Criacao_Implantacao' in df.columns and opp_id_col else 0

            # Count finalized implementations (Status_Implantacao = 'Finalizado')
            finalized_implementations = len(df[df['Status_Implantacao'] == 'Finalizado']) if 'Status_Implantacao' in df.columns else 0

            # Count active users (finalized implementations that generate invoices)
            active_users = len(df[(df['Status_Implantacao'] == 'Finalizado') & (df['Gerou_Nota_Fiscal'] == 'Sim')]) if 'Status_Implantacao' in df.columns and 'Gerou_Nota_Fiscal' in df.columns else 0

            # Count high activity users (3+ months of activity)
            high_activity_users = len(df[(df['Gerou_Nota_Fiscal'] == 'Sim') & (df['Meses_atividade'] >= 3)]) if 'Gerou_Nota_Fiscal' in df.columns and 'Meses_atividade' in df.columns else 0

            # Count active implementations (all except finalized)
            active_implementations = total_implementations - finalized_implementations

            # Count universities
            total_universities = df['Universidade'].dropna().nunique() if 'Universidade' in df.columns else 0

            # Calculate MRR
            mrr_total, finalized_count, _, _ = business_rules.calculate_mrr()

            # Calculate average ticket
            ticket_medio = business_rules.calculate_average_ticket(mrr_total, finalized_count)

        # Initialize common services for both database and file-based data
        if not db_adapter.is_database_available():
            data_processing = DataProcessingService(data_loader)
            analytics = AnalyticsService(data_loader, business_rules)
        else:
            # For database mode, create simplified services
            data_processing = None
            analytics = None
            business_rules = None

        # Calculate derived metrics
        arr_total = mrr_total * 12
        potential_revenue = mrr_total * 1.2  # Simplified calculation
        mrr_forecast = {1: mrr_total * 1.05, 2: mrr_total * 1.10, 3: mrr_total * 1.15}
        mrr_growth_rate = 5.0

        # Format values for display
        mrr_formatado = format_currency(mrr_total)
        ticket_medio_formatado = format_currency(ticket_medio)
        potential_revenue_formatado = format_currency(potential_revenue)
        arr_formatado = format_currency(arr_total)

        # Format MRR forecast
        mrr_forecast_formatado = {
            f"Mês {i}": format_currency(value) for i, value in mrr_forecast.items()
        }

        # Get recent leads (simplified for database mode)
        if data_processing:
            recent_leads = data_processing.get_recent_leads(limit=5)
        else:
            recent_leads = []

        # Calculate conversion rates (simplified for database mode)
        if business_rules:
            total_leads_calc, leads_with_opps, lead_to_opp_rate, total_opps, opps_with_impl, opp_to_impl_rate = business_rules.calculate_conversion_rates()
            impl_percentage = business_rules.calculate_implementation_percentage(total_opps, opps_with_impl)
            conversao_geral = business_rules.calculate_overall_conversion(total_leads_calc, opps_with_impl)
            _, _, avg_lead_to_impl_days = business_rules.calculate_time_to_conversion()
        else:
            # Simplified calculations for database mode
            leads_with_opps = total_opportunities
            lead_to_opp_rate = (total_opportunities / total_leads * 100) if total_leads > 0 else 0
            total_opps = total_opportunities
            opps_with_impl = finalized_implementations
            opp_to_impl_rate = (finalized_implementations / total_opportunities * 100) if total_opportunities > 0 else 0
            impl_percentage = opp_to_impl_rate
            conversao_geral = (finalized_implementations / total_leads * 100) if total_leads > 0 else 0
            avg_lead_to_impl_days = 90  # Default value

        # Prepare chart data (simplified for database mode)
        if analytics:
            chart_data = analytics.prepare_chart_data()
            implementation_data = analytics.get_implementation_data()
            chart_data['implementation_data'] = implementation_data
            timeline_data = implementation_data.get('timeline', {})
        else:
            chart_data = {'leads': [], 'opportunities': [], 'implementations': []}
            timeline_data = {}

        # Ensure all values are strings
        chart_data = prepare_chart_data(chart_data)

        # Calculate financial metrics
        conversion_improvement_impact = (mrr_total * 0.1) if conversao_geral > 0 else 0
        conversion_impact_formatado = format_currency(conversion_improvement_impact)

        # Calculate cost per acquisition and lifetime value
        marketing_cost_per_lead = 500
        cost_per_acquisition = marketing_cost_per_lead * (total_leads / opps_with_impl) if opps_with_impl > 0 else 0
        cost_per_acquisition_formatado = format_currency(cost_per_acquisition)

        customer_lifetime_months = 24
        lifetime_value = ticket_medio * customer_lifetime_months
        lifetime_value_formatado = format_currency(lifetime_value)

        # Simplified conversion rate analysis (only for file-based data)
        if not db_adapter.is_database_available() and 'df' in locals():
            pagamento_entrada_col = 'Pagamento de Entrada'
            total_com_pagamento = df[pagamento_entrada_col].notna().sum()
            total_sem_pagamento = df[pagamento_entrada_col].isna().sum()
            finalizados_com_pagamento = df[(df['Status_Implantacao'] == 'Finalizado') & (df[pagamento_entrada_col].notna())].shape[0]
            finalizados_sem_pagamento = df[(df['Status_Implantacao'] == 'Finalizado') & (df[pagamento_entrada_col].isna())].shape[0]

            taxa_conversao_com_pagamento = (finalizados_com_pagamento / total_com_pagamento) * 100 if total_com_pagamento > 0 else 0
            taxa_conversao_sem_pagamento = (finalizados_sem_pagamento / total_sem_pagamento) * 100 if total_sem_pagamento > 0 else 0
            diferenca_taxa_conversao = taxa_conversao_com_pagamento - taxa_conversao_sem_pagamento
        else:
            taxa_conversao_com_pagamento = 0
            taxa_conversao_sem_pagamento = 0
            diferenca_taxa_conversao = 0

        # Format conversion rates
        taxa_conversao_com_pagamento_formatada = f"{taxa_conversao_com_pagamento:.1f}%"
        taxa_conversao_sem_pagamento_formatada = f"{taxa_conversao_sem_pagamento:.1f}%"
        diferenca_taxa_conversao_formatada = f"{diferenca_taxa_conversao:.1f}%"

        return render_template('dashboard/index.html',
                              total_leads=total_leads,
                              total_opportunities=total_opportunities,
                              total_implementations=total_implementations,
                              active_implementations=active_implementations,
                              finalized_implementations=finalized_implementations,
                              active_users=active_users,
                              high_activity_users=high_activity_users,
                              total_universities=total_universities,
                              recent_leads=recent_leads,
                              mrr_total=mrr_formatado,
                              arr_total=arr_formatado,
                              finalized_count=finalized_count,
                              ticket_medio=ticket_medio_formatado,
                              impl_percentage=impl_percentage,
                              conversao_geral=conversao_geral,
                              potential_revenue=potential_revenue_formatado,
                              mrr_forecast=mrr_forecast_formatado,
                              mrr_growth_rate=mrr_growth_rate,
                              conversion_impact=conversion_impact_formatado,
                              cost_per_acquisition=cost_per_acquisition_formatado,
                              lifetime_value=lifetime_value_formatado,
                              taxa_conversao_com_pagamento=taxa_conversao_com_pagamento_formatada,
                              taxa_conversao_sem_pagamento=taxa_conversao_sem_pagamento_formatada,
                              diferenca_taxa_conversao=diferenca_taxa_conversao_formatada,
                              avg_lead_to_impl_days=avg_lead_to_impl_days,
                              chart_data=json.dumps(chart_data),
                              timeline_data=timeline_data,
                              lead_to_opp_rate=lead_to_opp_rate,
                              leads_with_opps=leads_with_opps,
                              opps_with_impl=opps_with_impl,
                              opp_to_impl_rate=opp_to_impl_rate,
                              total_opps=total_opps)
    except Exception as e:
        logger.error(f"Error in dashboard index: {e}")
        flash(f'Error loading dashboard: {str(e)}', 'error')
        return render_template('dashboard/index.html',
                              total_leads=0,
                              total_opportunities=0,
                              total_implementations=0,
                              active_implementations=0,
                              finalized_implementations=0,
                              active_users=0,
                              high_activity_users=0,
                              total_universities=0,
                              recent_leads=[],
                              mrr_total=None,
                              arr_total=None,
                              finalized_count=0,
                              ticket_medio=None,
                              impl_percentage=0,
                              conversao_geral=0,
                              potential_revenue=None,
                              mrr_forecast={},
                              mrr_growth_rate=0,
                              conversion_impact=None,
                              cost_per_acquisition=None,
                              lifetime_value=None,
                              taxa_conversao_com_pagamento="0.0%",
                              taxa_conversao_sem_pagamento="0.0%",
                              diferenca_taxa_conversao="0.0%",
                              avg_lead_to_impl_days=0,
                              chart_data='{}',
                              timeline_data={},
                              lead_to_opp_rate=0,
                              leads_with_opps=0,
                              opps_with_impl=0,
                              opp_to_impl_rate=0,
                              total_opps=0)
