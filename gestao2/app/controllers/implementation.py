"""
Amigo DataHub - Implementation Controller
"""

import logging
from flask import Blueprint, render_template, redirect, url_for, flash
from app.services.data_loader import DataLoader
from app.services.data_processing import DataProcessingService
from app.services.business_rules import BusinessRulesService
from app.services.analytics import AnalyticsService
from app.utils.formatters import format_currency
from app.models.implementation import Implementation
from typing import Dict, List, Any, Optional, Union, Tuple

logger = logging.getLogger(__name__)

# Create blueprint
implementation_bp = Blueprint('implementation', __name__, url_prefix='/implementations')

@implementation_bp.route('/')
def index() -> str:
    """Implementation index page"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        data_processing = DataProcessingService(data_loader)
        business_rules = BusinessRulesService(data_loader)
        analytics = AnalyticsService(data_loader, business_rules)

        # Get implementations list for display
        implementations_list = data_processing.get_implementations_list()

        # Calculate ALL statistics using business_rules for consistency
        df = data_loader.get_data()
        opp_id_col = data_loader.get_opportunity_id_column()

        # Log debug information
        logger.info(f"Debug - Implementation data shape: {df.shape}")
        logger.info(f"Debug - Opportunity ID column: {opp_id_col}")
        logger.info(f"Debug - Implementations list length: {len(implementations_list)}")

        if opp_id_col and 'Fase_Implantacao' in df.columns and 'Status_Implantacao' in df.columns:
            # Total implementations: opportunities with implementation phase (consistent with business_rules)
            total_implementations = df[df['Fase_Implantacao'].notna()][opp_id_col].nunique()

            # Finalized implementations: those with status 'Finalizado'
            finalized_implementations = df[df['Status_Implantacao'] == 'Finalizado'][opp_id_col].nunique()

            # Active implementations: those with implementation but not finalized or canceled
            active_implementations = df[
                (df['Fase_Implantacao'].notna()) &
                (df['Status_Implantacao'] != 'Finalizado') &
                (df['Status_Implantacao'] != 'Cancelado')
            ][opp_id_col].nunique()

            # Log calculated values
            logger.info(f"Debug - Total implementations calculated: {total_implementations}")
            logger.info(f"Debug - Finalized implementations calculated: {finalized_implementations}")
            logger.info(f"Debug - Active implementations calculated: {active_implementations}")
        else:
            # Fallback to list-based calculations
            total_implementations = len(implementations_list)
            finalized_implementations = sum(1 for impl in implementations_list if impl.get('Status_Implantacao') == 'Finalizado')
            active_implementations = total_implementations - finalized_implementations
            logger.warning(f"Debug - Using fallback calculations: total={total_implementations}, finalized={finalized_implementations}, active={active_implementations}")

        # Calculate MRR from finalized implementations
        mrr_total, _, _, _ = business_rules.calculate_mrr()
        mrr_formatted = format_currency(mrr_total)

        # Calculate potential revenue from active implementations
        potential_revenue = business_rules.calculate_potential_revenue()
        potential_revenue_formatted = format_currency(potential_revenue)

        # Get implementation data for charts
        implementation_data = analytics.get_implementation_data()

        # Calculate additional metrics
        avg_implementation_time = implementation_data.get('avg_implementation_time', 0)
        completion_rate = implementation_data.get('completion_rate', 0)

        # Get phases and status data for charts
        phases_data = implementation_data.get('phases', {})
        status_data = implementation_data.get('status', {})
        responsible_data = implementation_data.get('responsible', {})
        timeline_data = implementation_data.get('timeline', {})

        # Get additional data for charts and metrics
        university_distribution = implementation_data.get('university_distribution', {})
        product_distribution = implementation_data.get('product_distribution', {})
        implementation_by_month = implementation_data.get('implementation_by_month', {})
        implementation_by_week = implementation_data.get('implementation_by_week', {})
        implementation_time_by_product = implementation_data.get('implementation_time_by_product', {})
        implementation_time_by_responsible = implementation_data.get('implementation_time_by_responsible', {})
        implementation_time_by_university = implementation_data.get('implementation_time_by_university', {})
        implementation_time_distribution = implementation_data.get('implementation_time_distribution', {})
        recent_implementations = implementation_data.get('recent_implementations', [])
        upcoming_finalizations = implementation_data.get('upcoming_finalizations', [])

        # Calculate average revenue per implementation
        avg_revenue_per_implementation = mrr_total / finalized_implementations if finalized_implementations > 0 else 0
        avg_revenue_formatted = format_currency(avg_revenue_per_implementation)

        # Calculate implementation efficiency (avg time vs completion rate)
        implementation_efficiency = 100 - (avg_implementation_time / 90 * 100) if avg_implementation_time > 0 else 0
        implementation_efficiency = max(0, min(100, implementation_efficiency))  # Ensure between 0-100

        return render_template('implementations/index.html',
                              implementations=implementations_list,
                              total_implementations=total_implementations,
                              active_implementations=active_implementations,
                              finalized_implementations=finalized_implementations,
                              mrr_total=mrr_formatted,
                              potential_revenue=potential_revenue_formatted,
                              avg_implementation_time=avg_implementation_time,
                              completion_rate=completion_rate,
                              phases_data=phases_data,
                              status_data=status_data,
                              responsible_data=responsible_data,
                              timeline_data=timeline_data,
                              university_distribution=university_distribution,
                              product_distribution=product_distribution,
                              implementation_by_month=implementation_by_month,
                              implementation_by_week=implementation_by_week,
                              implementation_time_by_product=implementation_time_by_product,
                              implementation_time_by_responsible=implementation_time_by_responsible,
                              implementation_time_by_university=implementation_time_by_university,
                              implementation_time_distribution=implementation_time_distribution,
                              recent_implementations=recent_implementations,
                              upcoming_finalizations=upcoming_finalizations,
                              avg_revenue_per_implementation=avg_revenue_formatted,
                              implementation_efficiency=round(implementation_efficiency, 1))
    except Exception as e:
        logger.error(f"Error in implementation index: {e}")
        flash(f'Error loading implementations: {str(e)}', 'error')
        return render_template('implementations/index.html',
                              implementations=[],
                              total_implementations=0,
                              active_implementations=0,
                              finalized_implementations=0,
                              mrr_total="R$ 0,00",
                              potential_revenue="R$ 0,00",
                              avg_implementation_time=0,
                              completion_rate=0,
                              phases_data={},
                              status_data={},
                              responsible_data={},
                              timeline_data={},
                              university_distribution={},
                              product_distribution={},
                              implementation_by_month={},
                              implementation_by_week={},
                              implementation_time_by_product={},
                              implementation_time_by_responsible={},
                              implementation_time_by_university={},
                              implementation_time_distribution={},
                              recent_implementations=[],
                              upcoming_finalizations=[],
                              avg_revenue_per_implementation="R$ 0,00",
                              implementation_efficiency=0)

@implementation_bp.route('/<opp_id>')
def detail(opp_id: int) -> str:
    """
    # Check implementations permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission

    if not check_permission('implementations.view'):
        flash('Acesso negado. Permissão insuficiente para acessar esta página.', 'error')
        return redirect(url_for('auth.login'))
Implementation detail page"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        data_processing = DataProcessingService(data_loader)

        # Get implementation detail
        impl_data, milestones, progress_percentage = data_processing.get_implementation_detail(opp_id)

        if not impl_data:
            flash('Implementation not found', 'error')
            return redirect(url_for('implementation.index'))

        # Create implementation model
        implementation = Implementation.from_dict(impl_data)

        return render_template('implementations/detail.html',
                              implementation=impl_data,
                              milestones=milestones,
                              progress_percentage=progress_percentage)
    except Exception as e:
        logger.error(f"Error in implementation detail: {e}")
        flash(f'Error loading implementation details: {str(e)}', 'error')
        return redirect(url_for('implementation.index'))
