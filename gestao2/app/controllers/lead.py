"""
Amigo DataHub - Lead Controller
"""

import json
import logging
from flask import Blueprint, render_template, redirect, url_for, flash
from app.services.data_loader import DataLoader
from app.services.data_processing import DataProcessingService
from app.services.business_rules import BusinessRulesService
from app.services.analytics import AnalyticsService
from app.models.lead import Lead
from typing import Dict, List, Any, Optional, Union, Tuple

logger = logging.getLogger(__name__)

# Create blueprint
lead_bp = Blueprint('lead', __name__, url_prefix='/leads')

@lead_bp.route('/')
def index() -> str:
    """Lead index page"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        data_processing = DataProcessingService(data_loader)
        business_rules = BusinessRulesService(data_loader)
        analytics = AnalyticsService(data_loader, business_rules)

        # Get leads
        leads_list = data_processing.get_leads_list()

        # Calculate conversion rates
        total_leads, leads_with_opps, lead_to_opp_rate, total_opps, opps_with_impl, opp_to_impl_rate = business_rules.calculate_conversion_rates()

        # Calculate overall conversion rate
        overall_conversion = business_rules.calculate_overall_conversion(total_leads, opps_with_impl)

        # Get top university data
        top_university_name, top_university_percentage = analytics.get_top_university_data()

        # Get top course data
        top_course_name, top_course_count = analytics.get_top_course_data()

        # Get conversion by state
        state_conversion = analytics.get_conversion_by_state()

        # Get funnel distribution
        funnel_stages = business_rules.calculate_funnel_distribution()

        # Get monthly opportunities
        monthly_opps = analytics.get_monthly_opportunities()

        # Get university distribution
        university_distribution = analytics.get_students_by_university()

        # Get course distribution
        course_distribution = {}
        for lead in leads_list:
            course = lead.get('Curso', '')
            if course:
                course_distribution[course] = course_distribution.get(course, 0) + 1

        # Convert to strings
        course_distribution = {k: str(v) for k, v in course_distribution.items()}

        # Prepare chart data
        chart_data = {
            'funnel_stages': funnel_stages,
            'state_conversion': state_conversion,
            'monthly_opps': monthly_opps,
            'university_distribution': university_distribution,
            'course_distribution': course_distribution
        }

        return render_template('leads/index.html',
                              leads=leads_list,
                              total_leads=total_leads,
                              leads_with_opps=leads_with_opps,
                              lead_to_opp_rate=lead_to_opp_rate,
                              overall_conversion=overall_conversion,
                              top_university_name=top_university_name,
                              top_university_percentage=top_university_percentage,
                              top_course_name=top_course_name,
                              top_course_count=top_course_count,
                              chart_data=json.dumps(chart_data))
    except Exception as e:
        logger.error(f"Error in lead index: {e}")
        flash(f'Error loading leads: {str(e)}', 'error')
        return render_template('leads/index.html',
                              leads=[],
                              total_leads=0,
                              leads_with_opps=0,
                              lead_to_opp_rate=0,
                              overall_conversion=0,
                              top_university_name="Não disponível",
                              top_university_percentage="0",
                              top_course_name="Não disponível",
                              top_course_count="0",
                              chart_data=json.dumps({}))

@lead_bp.route('/<lead_id>')
def detail(lead_id: int) -> str:
    """
    # Check leads permission
    from flask import session, redirect, url_for, flash
    from app.services.permission_service import check_permission
    
    if not check_permission('leads.view'):
        flash('Acesso negado. Permissão insuficiente para acessar esta página.', 'error')
        return redirect(url_for('auth.login'))
Lead detail page"""
    try:
        # Initialize services
        data_loader = DataLoader()
        data_loader.load_data()

        data_processing = DataProcessingService(data_loader)
        business_rules = BusinessRulesService(data_loader)

        # Get lead detail
        lead_data, opportunities = data_processing.get_lead_detail(lead_id)

        if not lead_data:
            flash('Lead not found', 'error')
            return redirect(url_for('lead.index'))

        # Create lead model
        lead_model = Lead.from_dict(lead_data)

        # Calculate conversion rates
        total_leads, leads_with_opps, lead_to_opp_rate, _, _, _ = business_rules.calculate_conversion_rates()

        # Get lead metrics
        lead_metrics = {
            'total_opportunities': len(opportunities),
            'conversion_rate': lead_to_opp_rate,
            'last_activity': lead_data.get('Data_criacao_lead', 'Não disponível'),
            'status': 'Ativo' if len(opportunities) > 0 else 'Inativo'
        }

        return render_template('leads/detail.html',
                              lead=lead_data,
                              lead_model=lead_model,
                              opportunities=opportunities,
                              lead_metrics=lead_metrics)
    except Exception as e:
        logger.error(f"Error in lead detail: {e}")
        flash(f'Error loading lead details: {str(e)}', 'error')
        return redirect(url_for('lead.index'))
