"""
Amigo DataHub - Data Processing Service
Service for processing and analyzing data
"""

import pandas as pd
import numpy as np
import logging
from app.utils.formatters import format_date, safe_convert_to_float
from typing import Dict, List, Any, Optional, Union, Tuple
import pandas as pd

logger = logging.getLogger(__name__)

class DataProcessingService:
    """Data Processing Service"""

    def __init__(self: List[Any], data_loader: Dict[str, Any]) -> None:
        """
        Initialize the data processing service

        Args:
            data_loader: The data loader service
        """
        self.data_loader = data_loader

    def get_recent_leads(self: List[Any], limit: int = 10) -> Optional[Dict[str, Any]]:
        """
        Get the most recent leads

        Args:
            limit (int): The maximum number of leads to return

        Returns:
            list: The list of leads
        """
        try:
            df = self.data_loader.get_data()
            lead_id_col = self.data_loader.get_lead_id_column()

            if not lead_id_col:
                # If no lead ID column found, use Nome do Lead as a fallback
                if 'Nome do Lead' in df.columns:
                    leads_df = df.drop_duplicates(subset=['Nome do Lead'])
                    if 'Data_criacao_lead' in df.columns:
                        leads_df = leads_df.sort_values('Data_criacao_lead', ascending=False)
                else:
                    # No way to identify leads
                    return []
            else:
                # Get unique leads using the identified lead ID column
                leads_df = df.drop_duplicates(subset=[lead_id_col])
                if 'Data_criacao_lead' in df.columns:
                    leads_df = leads_df.sort_values('Data_criacao_lead', ascending=False)

            # Convert dates to string for display
            leads_df_copy = leads_df.copy()
            if 'Data_criacao_lead' in leads_df_copy.columns:
                leads_df_copy['Data_criacao_lead'] = leads_df_copy['Data_criacao_lead'].apply(
                    lambda x: format_date(x) if pd.notna(x) else 'Não informado'
                )

            # Prepare columns to select
            select_cols = []
            if lead_id_col:
                select_cols.append(lead_id_col)

            # Add other columns if they exist
            for col in ['Nome do Lead', 'Data_criacao_lead', 'Formação Academica',
                        'Universidade', 'Curso', 'Cidade', 'Estado']:
                if col in leads_df_copy.columns:
                    select_cols.append(col)

            # Select relevant columns
            if select_cols:
                leads_list = leads_df_copy[select_cols].head(limit).to_dict('records')
            else:
                leads_list = []

            return leads_list
        except Exception as e:
            logger.error(f"Error getting recent leads: {e}")
            return []

    def get_leads_list(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the list of all leads

        Returns:
            list: The list of leads
        """
        try:
            df = self.data_loader.get_data()
            lead_id_col = self.data_loader.get_lead_id_column()

            if not lead_id_col:
                return []

            # Get unique leads
            leads_df = df.drop_duplicates(subset=[lead_id_col])

            # Sort by creation date
            if 'Data_criacao_lead' in df.columns:
                leads_df = leads_df.sort_values('Data_criacao_lead', ascending=False)

            # Convert dates to string
            leads_df_copy = leads_df.copy()
            if 'Data_criacao_lead' in leads_df_copy.columns:
                leads_df_copy['Data_criacao_lead'] = leads_df_copy['Data_criacao_lead'].apply(
                    lambda x: format_date(x) if pd.notna(x) else 'Não informado'
                )

            # Prepare columns to select
            select_cols = []
            if lead_id_col:
                select_cols.append(lead_id_col)

            # Add other columns if they exist
            for col in ['Nome do Lead', 'Data_criacao_lead', 'Formação Academica',
                        'Universidade', 'Curso', 'Cidade', 'Estado']:
                if col in leads_df_copy.columns:
                    select_cols.append(col)

            # Select relevant columns
            if select_cols:
                leads_list = leads_df_copy[select_cols].to_dict('records')
            else:
                leads_list = []

            return leads_list
        except Exception as e:
            logger.error(f"Error getting leads list: {e}")
            return []

    def get_opportunities_list(self: List[Any], active_only: bool = False) -> Optional[Dict[str, Any]]:
        """
        Get the list of opportunities (consistent with business_rules logic)

        Args:
            active_only (bool): Whether to return only active opportunities

        Returns:
            list: The list of opportunities
        """
        try:
            df = self.data_loader.get_data()
            opp_id_col = self.data_loader.get_opportunity_id_column()
            lead_id_col = self.data_loader.get_lead_id_column()

            if not opp_id_col:
                logger.warning("No opportunity ID column found")
                return []

            # Filter opportunities first, then get unique ones (consistent with business_rules)
            if active_only and 'Fase_Implantacao' in df.columns:
                # Active opportunities are those that don't have an implementation phase defined
                filtered_df = df[df['Fase_Implantacao'].isna()]
            else:
                # All opportunities (those with non-null opportunity IDs)
                filtered_df = df[df[opp_id_col].notna()]

            # Get unique opportunities after filtering
            opps_df = filtered_df.drop_duplicates(subset=[opp_id_col])

            # Sort by creation date
            if 'Data_criacao_Oportunidade' in df.columns:
                opps_df = opps_df.sort_values('Data_criacao_Oportunidade', ascending=False)

            # Convert dates to string
            opps_df_copy = opps_df.copy()
            if 'Data_criacao_Oportunidade' in opps_df_copy.columns:
                opps_df_copy['Data_criacao_Oportunidade'] = opps_df_copy['Data_criacao_Oportunidade'].apply(
                    lambda x: format_date(x) if pd.notna(x) else 'Não informado'
                )

            # Prepare columns to select
            select_cols = []
            if opp_id_col:
                select_cols.append(opp_id_col)
            if lead_id_col:
                select_cols.append(lead_id_col)

            # Add other columns if they exist
            for col in ['Nome do Lead', 'Data_criacao_Oportunidade', 'Etapa do funil Comercial',
                        'Produto', 'Valor Mensalidade', 'Nome_Responsavel']:
                if col in opps_df_copy.columns:
                    select_cols.append(col)

            # Select relevant columns
            if select_cols:
                opps_list = opps_df_copy[select_cols].to_dict('records')
            else:
                opps_list = []

            return opps_list
        except Exception as e:
            logger.error(f"Error getting opportunities list: {e}")
            return []

    def get_implementations_list(self: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Get the list of implementations (consistent with business_rules logic)

        Returns:
            list: The list of implementations
        """
        try:
            df = self.data_loader.get_data()
            opp_id_col = self.data_loader.get_opportunity_id_column()

            if not opp_id_col or 'Fase_Implantacao' not in df.columns:
                logger.warning("Missing opportunity ID column or Fase_Implantacao column")
                return []

            # Filter implementations first, then get unique ones (consistent with business_rules)
            filtered_df = df[(df[opp_id_col].notna()) & (df['Fase_Implantacao'].notna())]

            # Get unique implementations after filtering
            impl_df = filtered_df.drop_duplicates(subset=[opp_id_col])

            # Sort by creation date - handle potential timezone issues
            if 'Data_Criacao_Implantacao' in df.columns:
                try:
                    # Make a copy to avoid modifying the original dataframe
                    sort_df = impl_df.copy()

                    # Ensure the date column is timezone-naive for sorting
                    if pd.api.types.is_datetime64_dtype(sort_df['Data_Criacao_Implantacao']):
                        # Convert any timezone-aware datetimes to naive
                        sort_df['Data_Criacao_Implantacao'] = sort_df['Data_Criacao_Implantacao'].apply(
                            lambda x: x.replace(tzinfo=None) if pd.notna(x) and hasattr(x, 'tzinfo') and x.tzinfo is not None else x
                        )

                    # Sort using the timezone-naive column
                    sort_order = sort_df.sort_values('Data_Criacao_Implantacao', ascending=False).index
                    impl_df = impl_df.loc[sort_order]
                except Exception as sort_error:
                    logger.warning(f"Error sorting by creation date, using unsorted data: {sort_error}")

            # Convert dates to string
            impl_df_copy = impl_df.copy()
            date_columns = ['Data_Criacao_Implantacao', 'DataMarco1 - Co. Hero', 'DateMarco2 - Constituído',
                           'Marco 3 - Homologado', 'Marco 4 - Liberação']

            for col in date_columns:
                if col in impl_df_copy.columns:
                    impl_df_copy[col] = impl_df_copy[col].apply(
                        lambda x: format_date(x) if pd.notna(x) else 'Não informado'
                    )

            # Prepare columns to select
            select_cols = []
            if opp_id_col:
                select_cols.append(opp_id_col)

            # Add other columns if they exist
            for col in ['Nome do Lead', 'Data_Criacao_Implantacao', 'Fase_Implantacao',
                        'Status_Implantacao', 'ResponsableOnboarding']:
                if col in impl_df_copy.columns:
                    select_cols.append(col)

            # Select relevant columns
            if select_cols:
                impl_list = impl_df_copy[select_cols].to_dict('records')
            else:
                impl_list = []

            return impl_list
        except Exception as e:
            logger.error(f"Error getting implementations list: {e}")
            return []

    def get_lead_detail(self: List[Any], lead_id: int) -> Optional[Dict[str, Any]]:
        """
        Get the details of a specific lead

        Args:
            lead_id (str): The ID of the lead

        Returns:
            tuple: (lead_data, opportunities)
        """
        try:
            df = self.data_loader.get_data()
            lead_id_col = self.data_loader.get_lead_id_column()
            opp_id_col = self.data_loader.get_opportunity_id_column()

            if not lead_id_col:
                return None, []

            # Filter data for specific lead
            lead_df = df[df[lead_id_col] == lead_id]

            if lead_df.empty:
                return None, []

            # Convert dates to string
            lead_df_copy = lead_df.copy()
            date_columns = ['Data_criacao_lead', 'Data_criacao_Oportunidade']
            for col in date_columns:
                if col in lead_df_copy.columns:
                    lead_df_copy[col] = lead_df_copy[col].apply(
                        lambda x: format_date(x) if pd.notna(x) else 'Não informado'
                    )

            # Get lead data
            lead_data = lead_df_copy.iloc[0].to_dict()

            # Get opportunities for this lead
            opportunities = []
            if opp_id_col and opp_id_col in lead_df_copy.columns:
                # Prepare columns to select
                select_cols = []
                for col in [opp_id_col, 'Data_criacao_Oportunidade', 'Etapa do funil Comercial',
                            'Produto', 'Valor Mensalidade', 'Nome_Responsavel']:
                    if col in lead_df_copy.columns:
                        select_cols.append(col)

                if select_cols:
                    opportunities = lead_df_copy[lead_df_copy[opp_id_col].notna()][select_cols].to_dict('records')

            return lead_data, opportunities
        except Exception as e:
            logger.error(f"Error getting lead detail: {e}")
            return None, []

    def get_opportunity_detail(self: List[Any], opp_id: int) -> Optional[Dict[str, Any]]:
        """
        Get the details of a specific opportunity

        Args:
            opp_id (str): The ID of the opportunity

        Returns:
            tuple: (opp_data, has_implementation)
        """
        try:
            df = self.data_loader.get_data()
            opp_id_col = self.data_loader.get_opportunity_id_column()

            if not opp_id_col:
                return None, False

            # Filter data for specific opportunity
            opp_df = df[df[opp_id_col] == opp_id]

            if opp_df.empty:
                return None, False

            # Convert dates to string
            opp_df_copy = opp_df.copy()
            date_columns = ['Data_criacao_Oportunidade', 'Data_criacao_lead', 'Data_Criacao_Implantacao']
            for col in date_columns:
                if col in opp_df_copy.columns:
                    opp_df_copy[col] = opp_df_copy[col].apply(
                        lambda x: format_date(x) if pd.notna(x) else 'Não informado'
                    )

            # Get opportunity data
            opp_data = opp_df_copy.iloc[0].to_dict()

            # Check if it has implementation
            has_implementation = False
            if 'Fase_Implantacao' in opp_df.columns:
                has_implementation = opp_df['Fase_Implantacao'].notna().any()

            return opp_data, has_implementation
        except Exception as e:
            logger.error(f"Error getting opportunity detail: {e}")
            return None, False

    def get_implementation_detail(self: List[Any], opp_id: int) -> Optional[Dict[str, Any]]:
        """
        Get the details of a specific implementation

        Args:
            opp_id (str): The ID of the opportunity

        Returns:
            tuple: (impl_data, milestones, progress_percentage_str)
        """
        try:
            df = self.data_loader.get_data()
            opp_id_col = self.data_loader.get_opportunity_id_column()

            # Check if implementation phase column exists
            if 'Fase_Implantacao' not in df.columns or not opp_id_col:
                return None, [], "0"

            # Filter data for specific implementation
            impl_df = df[(df[opp_id_col] == opp_id) & df['Fase_Implantacao'].notna()]

            if impl_df.empty:
                return None, [], "0"

            # Convert dates to string
            impl_df_copy = impl_df.copy()
            date_columns = ['Data_Criacao_Implantacao', 'DataMarco1 - Co. Hero', 'DateMarco2 - Constituído',
                           'Marco 3 - Homologado', 'Marco 4 - Liberação', 'ActualStartDate', 'DataPrevistaDeFinalização']

            for col in date_columns:
                if col in impl_df_copy.columns:
                    # Handle potential timezone issues before formatting
                    if pd.api.types.is_datetime64_dtype(impl_df_copy[col]):
                        impl_df_copy[col] = impl_df_copy[col].apply(
                            lambda x: x.replace(tzinfo=None) if pd.notna(x) and hasattr(x, 'tzinfo') and x.tzinfo is not None else x
                        )

                    # Format dates as strings
                    impl_df_copy[col] = impl_df_copy[col].apply(
                        lambda x: format_date(x) if pd.notna(x) else 'Não informado'
                    )

            # Get implementation data
            impl_data = impl_df_copy.iloc[0].to_dict()

            # Get milestones
            milestones = []
            milestone_columns = {
                'DataMarco1 - Co. Hero': 'Criação da Conta Hero',
                'DateMarco2 - Constituído': 'Constituição',
                'Marco 3 - Homologado': 'Homologação',
                'Marco 4 - Liberação': 'Liberação'
            }

            for col, label in milestone_columns.items():
                if col in impl_df_copy.columns:
                    milestones.append({
                        'label': label,
                        'date': impl_df_copy[col].iloc[0]
                    })

            # Count completed milestones
            completed_milestones = sum(1 for m in milestones if m['date'] != 'Não informado')
            progress_percentage = (completed_milestones / len(milestones)) * 100 if milestones else 0
            progress_percentage_str = str(progress_percentage)

            return impl_data, milestones, progress_percentage_str
        except Exception as e:
            logger.error(f"Error getting implementation detail: {e}")
            return None, [], "0"
