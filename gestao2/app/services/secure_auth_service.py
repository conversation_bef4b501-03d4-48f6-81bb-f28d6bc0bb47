"""
Secure Authentication Service
Uses SQLite database with bcrypt password hashing and secure HTTP practices
"""

import sqlite3
import bcrypt
import secrets
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Optional, List, Any
from flask import request, session
import os

logger = logging.getLogger(__name__)

class SecureAuthService:
    """Secure authentication service using SQLite database"""

    def __init__(self, db_path: str = None):
        """Initialize the secure auth service"""
        if db_path is None:
            # Default to business_admin.db in data directory
            current_dir = Path(__file__).parent.parent.parent
            self.db_path = current_dir / 'data' / 'business_admin.db'
        else:
            self.db_path = Path(db_path)

        self.ensure_database_exists()
        self.session_timeout = timedelta(hours=8)  # 8 hour session timeout

    def ensure_database_exists(self):
        """Ensure database and tables exist"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Check if users table exists
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='users'
                """)

                if not cursor.fetchone():
                    self.create_tables(conn)

        except Exception as e:
            logger.error(f"Error ensuring database exists: {e}")
            raise

    def create_tables(self, conn: sqlite3.Connection):
        """Create necessary tables"""
        cursor = conn.cursor()

        # Users table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'user',
                permissions TEXT NOT NULL DEFAULT '[]',
                domain TEXT NOT NULL,
                is_active BOOLEAN NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP NULL,
                metadata TEXT DEFAULT '{}'
            )
        """)

        # Activity logs table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                action TEXT NOT NULL,
                description TEXT NOT NULL,
                page TEXT NULL,
                ip_address TEXT NULL,
                user_agent TEXT NULL,
                domain TEXT NOT NULL,
                metadata TEXT DEFAULT '{}',
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # User sessions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                session_id TEXT UNIQUE NOT NULL,
                ip_address TEXT NULL,
                user_agent TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN NOT NULL DEFAULT 1
            )
        """)

        # Migrate existing system_logs table if it exists
        self.migrate_system_logs(cursor)

        conn.commit()
        logger.info("Database tables created successfully")

    def migrate_system_logs(self, cursor: sqlite3.Cursor):
        """Migrate system_logs table to activity_logs if needed"""
        try:
            # Check if system_logs table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='system_logs'")
            if cursor.fetchone():
                # Check if activity_logs table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='activity_logs'")
                if not cursor.fetchone():
                    # Rename system_logs to activity_logs and add missing columns
                    cursor.execute("ALTER TABLE system_logs RENAME TO activity_logs_temp")

                    # Create new activity_logs table with correct structure
                    cursor.execute("""
                        CREATE TABLE activity_logs (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            user_id TEXT NOT NULL,
                            action TEXT NOT NULL,
                            description TEXT NOT NULL,
                            page TEXT NULL,
                            ip_address TEXT NULL,
                            user_agent TEXT NULL,
                            domain TEXT NOT NULL,
                            metadata TEXT DEFAULT '{}',
                            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)

                    # Copy data from old table, adding default values for missing columns
                    cursor.execute("""
                        INSERT INTO activity_logs (user_id, action, description, page, ip_address, user_agent, domain, metadata, timestamp)
                        SELECT
                            user_id,
                            action,
                            description,
                            NULL as page,
                            ip_address,
                            user_agent,
                            'business' as domain,
                            COALESCE(metadata, '{}') as metadata,
                            timestamp
                        FROM activity_logs_temp
                    """)

                    # Drop old table
                    cursor.execute("DROP TABLE activity_logs_temp")
                    logger.info("Migrated system_logs to activity_logs successfully")
                else:
                    # Both tables exist, drop system_logs
                    cursor.execute("DROP TABLE system_logs")
                    logger.info("Removed duplicate system_logs table")
        except Exception as e:
            logger.error(f"Error migrating system_logs: {e}")

    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """
        Authenticate user with username and password

        Args:
            username: Username
            password: Plain text password

        Returns:
            User data if authentication successful, None otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Get user by username
                cursor.execute("""
                    SELECT * FROM users
                    WHERE username = ? AND is_active = 1
                """, (username,))

                user = cursor.fetchone()

                if user and self.verify_password(password, user['password_hash']):
                    # Update last login
                    cursor.execute("""
                        UPDATE users
                        SET last_login = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """, (user['id'],))

                    # Log successful login
                    self.log_activity(
                        user_id=str(user['id']),
                        action='login',
                        description=f'User {username} logged in successfully',
                        ip_address=request.remote_addr if request else None,
                        user_agent=request.headers.get('User-Agent') if request else None
                    )

                    conn.commit()

                    # Convert to dict and parse JSON fields
                    user_dict = dict(user)
                    user_dict['permissions'] = json.loads(user_dict.get('permissions', '[]'))
                    user_dict['metadata'] = json.loads(user_dict.get('metadata', '{}'))

                    return user_dict
                else:
                    # Log failed login attempt
                    self.log_activity(
                        user_id=username,
                        action='login_failed',
                        description=f'Failed login attempt for username: {username}',
                        ip_address=request.remote_addr if request else None,
                        user_agent=request.headers.get('User-Agent') if request else None
                    )
                    return None

        except Exception as e:
            logger.error(f"Error authenticating user {username}: {e}")
            return None

    def verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
        except Exception as e:
            logger.error(f"Error verifying password: {e}")
            return False

    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        try:
            salt = bcrypt.gensalt()
            return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
        except Exception as e:
            logger.error(f"Error hashing password: {e}")
            raise

    def create_user(self, username: str, email: str, password: str,
                   role: str = 'user', permissions: List[str] = None,
                   domain: str = 'business') -> bool:
        """
        Create a new user

        Args:
            username: Username
            email: Email address
            password: Plain text password
            role: User role
            permissions: List of permissions
            domain: User domain

        Returns:
            True if user created successfully, False otherwise
        """
        try:
            if permissions is None:
                permissions = []

            password_hash = self.hash_password(password)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO users (username, email, password_hash, role, permissions, domain)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (username, email, password_hash, role, json.dumps(permissions), domain))

                conn.commit()

                # Log user creation
                self.log_activity(
                    user_id='system',
                    action='user_created',
                    description=f'User {username} created with role {role}',
                    metadata={'created_user': username, 'role': role}
                )

                logger.info(f"User {username} created successfully")
                return True

        except sqlite3.IntegrityError as e:
            logger.error(f"User creation failed - duplicate username/email: {e}")
            return False
        except Exception as e:
            logger.error(f"Error creating user {username}: {e}")
            return False

    def change_password(self, user_id: str, current_password: str, new_password: str) -> bool:
        """
        Change user password

        Args:
            user_id: User ID
            current_password: Current password
            new_password: New password

        Returns:
            True if password changed successfully, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Get current user
                cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
                user = cursor.fetchone()

                if user and self.verify_password(current_password, user['password_hash']):
                    # Hash new password
                    new_hash = self.hash_password(new_password)

                    # Update password
                    cursor.execute("""
                        UPDATE users
                        SET password_hash = ?
                        WHERE id = ?
                    """, (new_hash, user_id))

                    # Log password change
                    self.log_activity(
                        user_id=user_id,
                        action='password_changed',
                        description=f'User {user["username"]} changed password'
                    )

                    conn.commit()
                    logger.info(f"Password changed for user ID {user_id}")
                    return True
                else:
                    logger.warning(f"Password change failed for user ID {user_id} - invalid current password")
                    return False

        except Exception as e:
            logger.error(f"Error changing password for user {user_id}: {e}")
            return False

    def log_activity(self, user_id: str, action: str, description: str,
                    page: str = None, ip_address: str = None,
                    user_agent: str = None, metadata: Dict = None):
        """Log user activity"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO activity_logs
                    (user_id, action, description, page, ip_address, user_agent, domain, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    user_id, action, description, page, ip_address,
                    user_agent, 'business', json.dumps(metadata or {})
                ))

                conn.commit()

        except Exception as e:
            logger.error(f"Error logging activity: {e}")

    def get_activity_logs(self, limit: int = 100, action_filter: str = None) -> List[Dict]:
        """Get activity logs from database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Build query with optional filter
                query = """
                    SELECT id, user_id, action, description, page, ip_address,
                           user_agent, domain, metadata, timestamp
                    FROM activity_logs
                """
                params = []

                if action_filter:
                    query += " WHERE action = ?"
                    params.append(action_filter)

                query += " ORDER BY timestamp DESC LIMIT ?"
                params.append(limit)

                cursor.execute(query, params)
                rows = cursor.fetchall()

                # Convert to list of dictionaries
                logs = []
                for row in rows:
                    log = {
                        'id': row[0],
                        'user_id': row[1],
                        'action': row[2],
                        'description': row[3],
                        'page': row[4],
                        'ip_address': row[5],
                        'user_agent': row[6],
                        'domain': row[7],
                        'metadata': json.loads(row[8]) if row[8] else {},
                        'timestamp': row[9]
                    }
                    logs.append(log)

                return logs

        except Exception as e:
            logger.error(f"Error getting activity logs: {e}")
            return []

    def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user by ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute("SELECT * FROM users WHERE id = ? AND is_active = 1", (user_id,))
                user = cursor.fetchone()

                if user:
                    user_dict = dict(user)
                    user_dict['permissions'] = json.loads(user_dict.get('permissions', '[]'))
                    user_dict['metadata'] = json.loads(user_dict.get('metadata', '{}'))
                    return user_dict

                return None

        except Exception as e:
            logger.error(f"Error getting user by ID {user_id}: {e}")
            return None

    def is_session_valid(self, user_id: str) -> bool:
        """Check if user session is valid"""
        try:
            # For now, just check if user exists and is active
            user = self.get_user_by_id(user_id)
            return user is not None and user.get('is_active', False)
        except Exception as e:
            logger.error(f"Error checking session validity: {e}")
            return False
