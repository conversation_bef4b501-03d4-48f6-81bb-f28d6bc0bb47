"""
Auxiliary Data Service
Service for loading and analyzing auxiliary medical production data
"""

import pandas as pd
import logging
from typing import Dict, List, Any
import os

logger = logging.getLogger(__name__)

class AuxiliaryDataService:
    """Service for handling auxiliary medical production data"""
    
    def __init__(self):
        self.auxiliary_df = None
        self.load_auxiliary_data()
    
    def load_auxiliary_data(self):
        """Load auxiliary data from CSV file"""
        try:
            auxiliary_path = os.path.join('data', 'sources', 'base_dados_auxiliar.csv')
            
            if not os.path.exists(auxiliary_path):
                logger.warning(f"Auxiliary data file not found: {auxiliary_path}")
                return
            
            self.auxiliary_df = pd.read_csv(auxiliary_path)
            
            # Convert columns to appropriate types
            if 'servico_valores_valor_servicos' in self.auxiliary_df.columns:
                self.auxiliary_df['servico_valores_valor_servicos'] = pd.to_numeric(
                    self.auxiliary_df['servico_valores_valor_servicos'], errors='coerce'
                )
            
            if 'created_at' in self.auxiliary_df.columns:
                self.auxiliary_df['created_at'] = pd.to_datetime(
                    self.auxiliary_df['created_at'], errors='coerce'
                )
            
            logger.info(f"Auxiliary data loaded: {len(self.auxiliary_df)} records")
            
        except Exception as e:
            logger.error(f"Error loading auxiliary data: {e}")
            self.auxiliary_df = None
    
    def get_user_details(self, user_id: int) -> Dict[str, Any]:
        """Get detailed invoice information for a specific user"""
        try:
            if self.auxiliary_df is None or self.auxiliary_df.empty:
                return {"error": "No auxiliary data available"}
            
            # Filter records for the specific user
            user_records = self.auxiliary_df[self.auxiliary_df['user_id'] == user_id].copy()
            
            if user_records.empty:
                return {"error": f"No records found for user_id {user_id}"}
            
            # Calculate user statistics
            total_invoices = len(user_records)
            total_value = user_records['servico_valores_valor_servicos'].sum()
            avg_value = user_records['servico_valores_valor_servicos'].mean()
            
            # Status distribution
            status_counts = user_records['status'].value_counts().to_dict()
            
            # Monthly breakdown
            user_records['month'] = user_records['created_at'].dt.to_period('M')
            monthly_breakdown = user_records.groupby('month').agg({
                'servico_valores_valor_servicos': ['sum', 'count'],
                'status': lambda x: (x == 'AUTORIZADA').sum()
            }).round(2)
            
            monthly_data = []
            for month, row in monthly_breakdown.iterrows():
                monthly_data.append({
                    'month': str(month),
                    'total_value': float(row[('servico_valores_valor_servicos', 'sum')]),
                    'invoice_count': int(row[('servico_valores_valor_servicos', 'count')]),
                    'authorized_count': int(row[('status', '<lambda>')])
                })
            
            # Recent invoices (last 10)
            recent_invoices = user_records.sort_values('created_at', ascending=False).head(10)
            invoice_list = []
            for _, invoice in recent_invoices.iterrows():
                invoice_list.append({
                    'id': invoice['id'],
                    'value': float(invoice['servico_valores_valor_servicos']) if pd.notna(invoice['servico_valores_valor_servicos']) else 0,
                    'status': invoice['status'],
                    'created_at': invoice['created_at'].strftime('%Y-%m-%d %H:%M:%S') if pd.notna(invoice['created_at']) else None
                })
            
            return {
                'user_id': user_id,
                'summary': {
                    'total_invoices': total_invoices,
                    'total_value': float(total_value) if pd.notna(total_value) else 0,
                    'avg_value': float(avg_value) if pd.notna(avg_value) else 0,
                    'status_distribution': status_counts
                },
                'monthly_breakdown': monthly_data,
                'recent_invoices': invoice_list,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Error getting user details: {e}")
            return {"error": str(e)}
    
    def get_auxiliary_stats(self) -> Dict[str, Any]:
        """Get general statistics from auxiliary data"""
        try:
            if self.auxiliary_df is None or self.auxiliary_df.empty:
                return {"error": "No auxiliary data available"}
            
            # General statistics
            total_records = len(self.auxiliary_df)
            total_value = self.auxiliary_df['servico_valores_valor_servicos'].sum()
            avg_value = self.auxiliary_df['servico_valores_valor_servicos'].mean()
            unique_users = self.auxiliary_df['user_id'].nunique()
            
            # Status distribution
            status_distribution = self.auxiliary_df['status'].value_counts().to_dict()
            
            # Value ranges
            value_ranges = [
                ('R$ 0 - R$ 100', (self.auxiliary_df['servico_valores_valor_servicos'] <= 100).sum()),
                ('R$ 101 - R$ 500', ((self.auxiliary_df['servico_valores_valor_servicos'] > 100) & (self.auxiliary_df['servico_valores_valor_servicos'] <= 500)).sum()),
                ('R$ 501 - R$ 1.000', ((self.auxiliary_df['servico_valores_valor_servicos'] > 500) & (self.auxiliary_df['servico_valores_valor_servicos'] <= 1000)).sum()),
                ('R$ 1.001 - R$ 5.000', ((self.auxiliary_df['servico_valores_valor_servicos'] > 1000) & (self.auxiliary_df['servico_valores_valor_servicos'] <= 5000)).sum()),
                ('Acima de R$ 5.000', (self.auxiliary_df['servico_valores_valor_servicos'] > 5000).sum())
            ]
            
            return {
                'total_records': total_records,
                'total_value': float(total_value) if pd.notna(total_value) else 0,
                'avg_value': float(avg_value) if pd.notna(avg_value) else 0,
                'unique_users': unique_users,
                'status_distribution': status_distribution,
                'value_distribution': [{'range': r[0], 'count': r[1]} for r in value_ranges],
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Error getting auxiliary stats: {e}")
            return {"error": str(e)}
