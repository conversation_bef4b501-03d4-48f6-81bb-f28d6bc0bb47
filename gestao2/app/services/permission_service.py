"""
Centralized Permission Service
Manages user permissions and access control
"""

import logging
from typing import List, Dict, Any, Optional
from flask import session, request, render_template, redirect, url_for
from functools import wraps
import json

logger = logging.getLogger(__name__)

class PermissionService:
    """Centralized permission management service"""

    # Define all available permissions
    PERMISSIONS = {
        # Dashboard
        'dashboard.view': 'Visualizar Dashboard',

        # Data Management
        'leads.view': 'Visualizar Leads',
        'leads.edit': 'Editar Leads',
        'opportunities.view': 'Visualizar Oportunidades',
        'opportunities.edit': 'Editar Oportunidades',
        'implementations.view': 'Visualizar Implementações',
        'implementations.edit': 'Editar Implementações',

        # Academic Data
        'universities.view': 'Visualizar Universidades',
        'universities.edit': 'Editar Universidades',
        'classes.view': 'Visualizar Turmas',
        'classes.edit': 'Editar Turmas',
        'responsibles.view': 'Visualizar Responsáveis',
        'responsibles.edit': 'Editar Responsáveis',

        # Marketing
        'coupons.view': 'Visualizar Cupons',
        'coupons.edit': 'Editar Cupons',
        'conversion.view': 'Visualizar Conversões',
        'subscriptions.view': 'Visualizar Assinaturas',
        'churn.view': 'Visualizar Cancelamentos e Churn',

        # Analytics
        'clustering.view': 'Visualizar Clustering',
        'clustering.edit': 'Configurar Clustering',
        'commercial_intelligence.view': 'Visualizar Inteligência Comercial',
        'active_users.view': 'Visualizar Usuários Ativos',
        'active_users.edit': 'Editar Usuários Ativos',

        # System
        'data_quality.view': 'Visualizar Qualidade dos Dados',
        'business_rules.view': 'Visualizar Regras de Negócio',
        'users.manage': 'Gerenciar Usuários',
        'admin.view': 'Acessar Painel Administrativo',

        # Special
        'all': 'Acesso Total'
    }

    # Default permissions by role
    DEFAULT_PERMISSIONS = {
        'admin': [
            'dashboard.view', 'leads.view', 'leads.edit',
            'opportunities.view', 'opportunities.edit',
            'implementations.view', 'implementations.edit',
            'universities.view', 'universities.edit',
            'responsibles.view', 'responsibles.edit',
            'classes.view', 'classes.edit',
            'coupons.view', 'coupons.edit',
            'conversion.view', 'subscriptions.view', 'churn.view',
            'clustering.view', 'clustering.edit',
            'commercial_intelligence.view', 'active_users.view', 'active_users.edit',
            'data_quality.view', 'business_rules.view',
            'users.manage', 'admin.view', 'all'
        ],
        'user': [
            'dashboard.view', 'leads.view',
            'opportunities.view', 'implementations.view',
            'universities.view', 'responsibles.view',
            'classes.view', 'coupons.view',
            'conversion.view', 'subscriptions.view', 'churn.view',
            'clustering.view', 'commercial_intelligence.view', 'medical_production.view'
        ],
        'viewer': [
            'dashboard.view', 'leads.view',
            'opportunities.view', 'implementations.view',
            'universities.view', 'responsibles.view',
            'classes.view', 'coupons.view',
            'conversion.view', 'subscriptions.view'
        ]
    }

    @staticmethod
    def has_permission(permission: str, user_permissions: List[str] = None, user_role: str = None) -> bool:
        """Check if user has specific permission"""
        if user_permissions is None:
            user_permissions = session.get('permissions', [])
        if user_role is None:
            user_role = session.get('role', '')

        # Admin has all permissions
        if user_role == 'admin':
            return True

        # Check for 'all' permission
        if 'all' in user_permissions:
            return True

        # Check specific permission
        return permission in user_permissions

    @staticmethod
    def is_admin(user_role: str = None) -> bool:
        """Check if user is admin"""
        if user_role is None:
            user_role = session.get('role', '')
        return user_role == 'admin'

    @staticmethod
    def get_user_permissions() -> List[str]:
        """Get current user permissions"""
        return session.get('permissions', [])

    @staticmethod
    def get_permission_description(permission: str) -> str:
        """Get human-readable description of permission"""
        return PermissionService.PERMISSIONS.get(permission, permission)

    @staticmethod
    def get_default_permissions(role: str) -> List[str]:
        """Get default permissions for a role"""
        return PermissionService.DEFAULT_PERMISSIONS.get(role, [])

    @staticmethod
    def render_access_denied(message: str = None) -> str:
        """Render access denied page with custom message"""
        if message is None:
            message = "Você não tem permissão para acessar esta página."

        # Log access denied attempt
        logger.warning(f"Access denied for user {session.get('username', 'anonymous')} to {request.endpoint}")

        return render_template('errors/403.html', message=message), 403

# Permission decorators
def require_permission(permission: str, message: str = None):
    """Decorator to require specific permission"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Check if user is logged in
            if 'user_id' not in session:
                return redirect(url_for('auth.login'))

            # Check permission
            if not PermissionService.has_permission(permission):
                return PermissionService.render_access_denied(message)

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_admin(message: str = None):
    """Decorator to require admin role"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Check if user is logged in
            if 'user_id' not in session:
                return redirect(url_for('auth.login'))

            # Check admin role
            if not PermissionService.is_admin():
                admin_message = message or "Esta página requer privilégios de administrador."
                return PermissionService.render_access_denied(admin_message)

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_any_permission(permissions: List[str], message: str = None):
    """Decorator to require any of the specified permissions"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Check if user is logged in
            if 'user_id' not in session:
                return redirect(url_for('auth.login'))

            # Check if user has any of the required permissions
            has_any = any(PermissionService.has_permission(perm) for perm in permissions)

            if not has_any:
                perm_message = message or f"Esta página requer uma das seguintes permissões: {', '.join(permissions)}"
                return PermissionService.render_access_denied(perm_message)

            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Convenience functions
def check_permission(permission: str) -> bool:
    """Simple function to check if current user has permission"""
    return PermissionService.has_permission(permission)

def is_admin() -> bool:
    """Simple function to check if current user is admin"""
    return PermissionService.is_admin()

def get_user_permissions() -> List[str]:
    """Simple function to get current user permissions"""
    return PermissionService.get_user_permissions()

# Initialize permission service
permission_service = PermissionService()
