"""
Amigo DataHub - Business Rules Service
Service for business rules and calculations
"""

import pandas as pd
import numpy as np
import logging
from app.utils.formatters import safe_convert_to_float
from app.utils.safe_operations import safe_division, safe_percentage
from typing import Dict, List, Any, Optional, Union, Tuple
import pandas as pd

logger = logging.getLogger(__name__)

class BusinessRulesService:
    """Business Rules Service"""

    def __init__(self: List[Any], data_loader: Dict[str, Any]) -> None:
        """
        Initialize the business rules service

        Args:
            data_loader: The data loader service
        """
        self.data_loader = data_loader

    def calculate_mrr(self: List[Any]) -> float:
        """
        Calculate the MRR (Monthly Recurring Revenue) for finalized implementations

        Returns:
            tuple: (mrr_total, finalized_count, responsible_mrr, product_mrr)
        """
        try:
            df = self.data_loader.get_data()

            mrr_total = 0
            finalized_count = 0
            responsible_mrr = {}
            product_mrr = {}

            if 'Status_Implantacao' in df.columns and 'Valor Mensalidade' in df.columns:
                # Filter finalized implementations
                finalized_df = df[df['Status_Implantacao'] == 'Finalizado'].copy()
                finalized_count = len(finalized_df)

                # Calculate MRR
                if finalized_count > 0:
                    # Convert currency values to float
                    finalized_df['Valor_Float'] = finalized_df['Valor Mensalidade'].apply(safe_convert_to_float)

                    # Calculate total MRR
                    mrr_total = finalized_df['Valor_Float'].sum()

                    # Calculate MRR by responsible
                    if 'ResponsableOnboarding' in finalized_df.columns:
                        responsible_mrr = finalized_df.groupby('ResponsableOnboarding')['Valor_Float'].sum().to_dict()

                    # Calculate MRR by product
                    if 'Produto' in finalized_df.columns:
                        product_mrr = finalized_df.groupby('Produto')['Valor_Float'].sum().to_dict()

            return mrr_total, finalized_count, responsible_mrr, product_mrr
        except Exception as e:
            logger.error(f"Error calculating MRR: {e}")
            return 0, 0, {}, {}

    def calculate_average_ticket(self: List[Any], mrr_total: List[Any], finalized_count: int) -> float:
        """
        Calculate the average ticket based on the MRR total and the number of finalized implementations

        Args:
            mrr_total (float): The MRR total
            finalized_count (int): The number of finalized implementations

        Returns:
            float: The average ticket
        """
        return safe_division(mrr_total, finalized_count)

    def calculate_conversion_rates(self: List[Any]) -> float:
        """
        Calculate the conversion rates of the funnel

        Returns:
            tuple: (total_leads, leads_with_opps, lead_to_opp_rate, total_opps, opps_with_impl, opp_to_impl_rate)
        """
        try:
            df = self.data_loader.get_data()
            lead_id_col = self.data_loader.get_lead_id_column()
            opp_id_col = self.data_loader.get_opportunity_id_column()

            # Initialize variables
            total_leads = 0
            leads_with_opps = 0
            lead_to_opp_rate = 0
            total_opps = 0
            opps_with_impl = 0
            opp_to_impl_rate = 0

            # Calculate total leads
            if lead_id_col:
                total_leads = df[lead_id_col].dropna().nunique()

            # Calculate leads with opportunities
            if lead_id_col and opp_id_col:
                leads_with_opps = df[df[opp_id_col].notna()][lead_id_col].nunique()

            # Calculate lead to opportunity conversion rate
            lead_to_opp_rate = safe_percentage(leads_with_opps, total_leads)

            # Calculate total opportunities
            if opp_id_col:
                total_opps = df[opp_id_col].dropna().nunique()

            # Calculate opportunities with implementations
            if opp_id_col and 'Fase_Implantacao' in df.columns:
                opps_with_impl = df[df['Fase_Implantacao'].notna()][opp_id_col].nunique()

            # Calculate opportunity to implementation conversion rate
            opp_to_impl_rate = safe_percentage(opps_with_impl, total_opps)

            return total_leads, leads_with_opps, lead_to_opp_rate, total_opps, opps_with_impl, opp_to_impl_rate
        except Exception as e:
            logger.error(f"Error calculating conversion rates: {e}")
            return 0, 0, 0, 0, 0, 0

    def calculate_funnel_distribution(self: List[Any]) -> float:
        """
        Calculate the distribution of the commercial funnel stages

        Returns:
            dict: The count of each funnel stage
        """
        try:
            df = self.data_loader.get_data()

            if 'Etapa do funil Comercial' in df.columns:
                funnel_stages_raw = df['Etapa do funil Comercial'].dropna().value_counts().sort_index().to_dict()
                funnel_stages = {k: str(v) for k, v in funnel_stages_raw.items()}
            else:
                funnel_stages = {}

            return funnel_stages
        except Exception as e:
            logger.error(f"Error calculating funnel distribution: {e}")
            return {}

    def calculate_implementation_phases(self: List[Any]) -> float:
        """
        Calculate the distribution of implementation phases

        Returns:
            dict: The count of each implementation phase
        """
        try:
            df = self.data_loader.get_data()

            if 'Fase_Implantacao' in df.columns:
                phases_raw = df['Fase_Implantacao'].dropna().value_counts().sort_index().to_dict()
                phases = {k: str(v) for k, v in phases_raw.items()}
            else:
                phases = {}

            return phases
        except Exception as e:
            logger.error(f"Error calculating implementation phases: {e}")
            return {}

    def calculate_implementation_status(self: List[Any]) -> float:
        """
        Calculate the distribution of implementation status

        Returns:
            tuple: (status_counts, status_positions)
        """
        try:
            df = self.data_loader.get_data()

            status_counts = {}
            status_positions = {}

            if 'Status_Implantacao' in df.columns:
                status_raw = df['Status_Implantacao'].dropna().value_counts().to_dict()
                status_counts = {k: str(v) for k, v in status_raw.items()}

                # Status position mapping
                status_positions = {
                    'Aguardando Início': '1',
                    'Em Andamento': '2',
                    'Aguardando Documentação': '3',
                    'Aguardando Aprovação': '4',
                    'Aguardando Pagamento': '5',
                    'Aguardando Assinatura': '6',
                    'Aguardando Homologação': '7',
                    'Aguardando Liberação': '8',
                    'Aguardando Feedback': '9',
                    'Finalizado': '10',
                    'Cancelado': '0'
                }

            return status_counts, status_positions
        except Exception as e:
            logger.error(f"Error calculating implementation status: {e}")
            return {}, {}

    def identify_active_universities(self: List[Any]) -> Any:
        """
        Identify active universities (with at least one opportunity)

        Returns:
            tuple: (total_universities, active_universities)
        """
        try:
            df = self.data_loader.get_data()
            opp_id_col = self.data_loader.get_opportunity_id_column()

            total_universities = 0
            active_universities = 0

            if 'Universidade' in df.columns:
                total_universities = df['Universidade'].dropna().nunique()

                # Active universities are those with at least one opportunity
                if opp_id_col:
                    active_universities = df[df[opp_id_col].notna()]['Universidade'].dropna().nunique()

            return total_universities, active_universities
        except Exception as e:
            logger.error(f"Error identifying active universities: {e}")
            return 0, 0

    def identify_active_students(self: List[Any]) -> Any:
        """
        Identify active students (with finalized implementations)

        Returns:
            tuple: (total_students, active_students)
        """
        try:
            df = self.data_loader.get_data()
            lead_id_col = self.data_loader.get_lead_id_column()

            total_students = 0
            active_students = 0

            if lead_id_col:
                total_students = df[lead_id_col].dropna().nunique()

                # Active students are those with finalized implementations
                if 'Status_Implantacao' in df.columns:
                    active_students = df[df['Status_Implantacao'] == 'Finalizado'][lead_id_col].dropna().nunique()

            return total_students, active_students
        except Exception as e:
            logger.error(f"Error identifying active students: {e}")
            return 0, 0

    def calculate_potential_revenue(self: List[Any]) -> float:
        """
        Calculate the potential revenue (active implementations)

        Returns:
            float: The potential revenue
        """
        try:
            df = self.data_loader.get_data()

            potential_revenue = 0

            if 'Status_Implantacao' in df.columns and 'Valor Mensalidade' in df.columns:
                # Filter active implementations (not finalized and not canceled)
                active_df = df[(df['Status_Implantacao'] != 'Finalizado') & (df['Status_Implantacao'] != 'Cancelado')].copy()

                # Calculate potential revenue
                if not active_df.empty:
                    # Convert currency values to float
                    active_df['Valor_Float'] = active_df['Valor Mensalidade'].apply(safe_convert_to_float)

                    # Calculate total potential revenue
                    potential_revenue = active_df['Valor_Float'].sum()

            return potential_revenue
        except Exception as e:
            logger.error(f"Error calculating potential revenue: {e}")
            return 0

    def calculate_active_implementations(self: List[Any]) -> float:
        """
        Calculate the number of active implementations

        Returns:
            int: The number of active implementations
        """
        try:
            df = self.data_loader.get_data()
            opp_id_col = self.data_loader.get_opportunity_id_column()

            active_implementations = 0

            if 'Status_Implantacao' in df.columns and opp_id_col:
                # Filter active implementations (not finalized and not canceled)
                active_implementations = df[(df['Status_Implantacao'] != 'Finalizado') & (df['Status_Implantacao'] != 'Cancelado')][opp_id_col].nunique()

            return active_implementations
        except Exception as e:
            logger.error(f"Error calculating active implementations: {e}")
            return 0

    def calculate_conversion_percentage(self: List[Any], numerator: List[Any], denominator: List[Any], description: str = "conversion") -> float:
        """
        Calculate a conversion percentage

        Args:
            numerator (int): The numerator (e.g., opportunities with implementations)
            denominator (int): The denominator (e.g., total opportunities)
            description (str): Description of the conversion for logging

        Returns:
            float: The conversion percentage
        """
        try:
            if denominator == 0:
                logger.warning(f"Denominator is zero when calculating {description} percentage")
                return 0

            return safe_percentage(numerator, denominator)
        except Exception as e:
            logger.error(f"Error calculating {description} percentage: {e}")
            return 0

    def calculate_overall_conversion(self: List[Any], total_leads: List[Any], opps_with_impl: List[Any]) -> float:
        """
        Calculate the overall conversion rate (from lead to implementation)

        Args:
            total_leads (int): The total number of leads
            opps_with_impl (int): The number of opportunities with implementations

        Returns:
            float: The overall conversion rate
        """
        return self.calculate_conversion_percentage(opps_with_impl, total_leads, "overall conversion")

    def calculate_implementation_percentage(self: List[Any], total_opps: List[Any], opps_with_impl: List[Any]) -> float:
        """
        Calculate the implementation percentage

        Args:
            total_opps (int): The total number of opportunities
            opps_with_impl (int): The number of opportunities with implementations

        Returns:
            float: The implementation percentage
        """
        return self.calculate_conversion_percentage(opps_with_impl, total_opps, "implementation")

    def calculate_mrr_forecast(self: List[Any], months: List[Any] = 12) -> float:
        """
        Calculate the MRR forecast for the next months

        Args:
            months (int): The number of months to forecast

        Returns:
            dict: The MRR forecast
        """
        try:
            mrr_total, _, _, _ = self.calculate_mrr()

            # Calculate the forecast
            forecast = {}

            # Assume a 5% monthly growth
            growth_rate = 0.05

            for i in range(1, months + 1):
                forecast[i] = mrr_total * (1 + growth_rate) ** i

            return forecast
        except Exception as e:
            logger.error(f"Error calculating MRR forecast: {e}")
            return {}

    def calculate_business_metrics(self) -> Dict[str, Any]:
        """
        Calculate comprehensive business metrics

        Returns:
            dict: Business metrics including MRR, conversion rates, revenue, etc.
        """
        try:
            # Calculate MRR
            mrr_total, mrr_finalized, mrr_active, mrr_growth = self.calculate_mrr()

            # Calculate conversion rates
            total_leads, leads_with_opps, lead_to_opp_rate, total_opps, opps_with_impl, opp_to_impl_rate = self.calculate_conversion_rates()

            # Calculate revenue metrics
            total_revenue = self.calculate_total_revenue()
            potential_revenue = self.calculate_potential_revenue()

            # Calculate time to conversion
            avg_lead_to_opp_days, avg_opp_to_impl_days, avg_lead_to_impl_days = self.calculate_time_to_conversion()

            # Calculate conversion velocity
            velocity_metrics = self.calculate_conversion_velocity()

            return {
                'mrr_metrics': {
                    'total_mrr': mrr_total,
                    'finalized_mrr': mrr_finalized,
                    'active_mrr': mrr_active,
                    'mrr_growth': mrr_growth
                },
                'conversion_metrics': {
                    'total_leads': total_leads,
                    'leads_with_opportunities': leads_with_opps,
                    'lead_to_opportunity_rate': lead_to_opp_rate,
                    'total_opportunities': total_opps,
                    'opportunities_with_implementations': opps_with_impl,
                    'opportunity_to_implementation_rate': opp_to_impl_rate
                },
                'revenue_metrics': {
                    'total_revenue': total_revenue,
                    'potential_revenue': potential_revenue,
                    'revenue_realization_rate': (total_revenue / potential_revenue * 100) if potential_revenue > 0 else 0
                },
                'time_metrics': {
                    'avg_lead_to_opportunity_days': avg_lead_to_opp_days,
                    'avg_opportunity_to_implementation_days': avg_opp_to_impl_days,
                    'avg_lead_to_implementation_days': avg_lead_to_impl_days
                },
                'velocity_metrics': velocity_metrics
            }

        except Exception as e:
            logger.error(f"Error calculating business metrics: {e}")
            return {
                'error': str(e),
                'mrr_metrics': {},
                'conversion_metrics': {},
                'revenue_metrics': {},
                'time_metrics': {},
                'velocity_metrics': {}
            }

    def calculate_time_to_conversion(self: List[Any]) -> float:
        """
        Calculate the average time from lead creation to opportunity creation and implementation

        Returns:
            tuple: (avg_lead_to_opp_days, avg_opp_to_impl_days, avg_lead_to_impl_days)
        """
        try:
            df = self.data_loader.get_data()

            # Check if we have the necessary date columns
            if 'Data_criacao_lead' not in df.columns or 'Data_criacao_Oportunidade' not in df.columns:
                logger.warning("Missing date columns for time to conversion calculation")
                return 0, 0, 0

            # Make sure date columns are datetime
            for col in ['Data_criacao_lead', 'Data_criacao_Oportunidade', 'Data_Criacao_Implantacao']:
                if col in df.columns and not pd.api.types.is_datetime64_any_dtype(df[col]):
                    df[col] = pd.to_datetime(df[col], errors='coerce')

            # Calculate time from lead to opportunity
            lead_to_opp_df = df.dropna(subset=['Data_criacao_lead', 'Data_criacao_Oportunidade'])
            lead_to_opp_df['lead_to_opp_days'] = (lead_to_opp_df['Data_criacao_Oportunidade'] - lead_to_opp_df['Data_criacao_lead']).dt.days

            # Filter out negative or unreasonably large values
            lead_to_opp_df = lead_to_opp_df[(lead_to_opp_df['lead_to_opp_days'] >= 0) & (lead_to_opp_df['lead_to_opp_days'] <= 365)]
            avg_lead_to_opp_days = lead_to_opp_df['lead_to_opp_days'].mean()

            # Calculate time from opportunity to implementation
            if 'Data_Criacao_Implantacao' in df.columns:
                opp_to_impl_df = df.dropna(subset=['Data_criacao_Oportunidade', 'Data_Criacao_Implantacao'])
                opp_to_impl_df['opp_to_impl_days'] = (opp_to_impl_df['Data_Criacao_Implantacao'] - opp_to_impl_df['Data_criacao_Oportunidade']).dt.days

                # Filter out negative or unreasonably large values
                opp_to_impl_df = opp_to_impl_df[(opp_to_impl_df['opp_to_impl_days'] >= 0) & (opp_to_impl_df['opp_to_impl_days'] <= 365)]
                avg_opp_to_impl_days = opp_to_impl_df['opp_to_impl_days'].mean()

                # Calculate time from lead to implementation
                lead_to_impl_df = df.dropna(subset=['Data_criacao_lead', 'Data_Criacao_Implantacao'])
                lead_to_impl_df['lead_to_impl_days'] = (lead_to_impl_df['Data_Criacao_Implantacao'] - lead_to_impl_df['Data_criacao_lead']).dt.days

                # Filter out negative or unreasonably large values
                lead_to_impl_df = lead_to_impl_df[(lead_to_impl_df['lead_to_impl_days'] >= 0) & (lead_to_impl_df['lead_to_impl_days'] <= 730)]
                avg_lead_to_impl_days = lead_to_impl_df['lead_to_impl_days'].mean()
            else:
                avg_opp_to_impl_days = 0
                avg_lead_to_impl_days = 0

            # Handle NaN values
            avg_lead_to_opp_days = 0 if pd.isna(avg_lead_to_opp_days) else int(round(avg_lead_to_opp_days))
            avg_opp_to_impl_days = 0 if pd.isna(avg_opp_to_impl_days) else int(round(avg_opp_to_impl_days))
            avg_lead_to_impl_days = 0 if pd.isna(avg_lead_to_impl_days) else int(round(avg_lead_to_impl_days))

            return avg_lead_to_opp_days, avg_opp_to_impl_days, avg_lead_to_impl_days
        except Exception as e:
            logger.error(f"Error calculating time to conversion: {e}")
            return 0, 0, 0

    def calculate_conversion_rate_by_month(self: List[Any]) -> float:
        """
        Calculate the conversion rate by month

        Returns:
            tuple: (months, lead_to_opp_rates, opp_to_impl_rates, overall_rates)
        """
        try:
            df = self.data_loader.get_data()
            lead_id_col = self.data_loader.get_lead_id_column()
            opp_id_col = self.data_loader.get_opportunity_id_column()

            # Check if we have the necessary columns
            if 'Data_criacao_lead' not in df.columns or not lead_id_col:
                logger.warning("Missing columns for conversion rate by month calculation")
                return [], [], [], []

            # Make sure date columns are datetime
            for col in ['Data_criacao_lead', 'Data_criacao_Oportunidade', 'Data_Criacao_Implantacao']:
                if col in df.columns and not pd.api.types.is_datetime64_any_dtype(df[col]):
                    df[col] = pd.to_datetime(df[col], errors='coerce')

            # Add month column for leads
            df['lead_month'] = df['Data_criacao_lead'].dt.strftime('%Y-%m')

            # Get the last 12 months
            current_date = pd.Timestamp.now()
            start_date = current_date - pd.DateOffset(months=11)
            month_range = pd.date_range(start=start_date, end=current_date, freq='M')
            months = [d.strftime('%Y-%m') for d in month_range]
            month_labels = [d.strftime('%b/%Y') for d in month_range]

            # Initialize result arrays
            lead_to_opp_rates = []
            opp_to_impl_rates = []
            overall_rates = []

            # Calculate conversion rates for each month
            for month in months:
                # Count leads created in this month
                month_leads = df[df['lead_month'] == month][lead_id_col].nunique()

                if month_leads > 0 and opp_id_col:
                    # Count opportunities from leads created in this month
                    month_opps = df[(df['lead_month'] == month) & df[opp_id_col].notna()][lead_id_col].nunique()

                    # Calculate lead to opportunity conversion rate
                    lead_to_opp_rate = safe_percentage(month_opps, month_leads)
                    lead_to_opp_rates.append(lead_to_opp_rate)

                    # Count implementations from leads created in this month
                    if 'Fase_Implantacao' in df.columns:
                        month_impls = df[(df['lead_month'] == month) & df['Fase_Implantacao'].notna()][lead_id_col].nunique()

                        # Calculate opportunity to implementation conversion rate
                        opp_to_impl_rate = safe_percentage(month_impls, month_opps) if month_opps > 0 else 0
                        opp_to_impl_rates.append(opp_to_impl_rate)

                        # Calculate overall conversion rate
                        overall_rate = safe_percentage(month_impls, month_leads)
                        overall_rates.append(overall_rate)
                    else:
                        opp_to_impl_rates.append(0)
                        overall_rates.append(0)
                else:
                    lead_to_opp_rates.append(0)
                    opp_to_impl_rates.append(0)
                    overall_rates.append(0)

            return month_labels, lead_to_opp_rates, opp_to_impl_rates, overall_rates
        except Exception as e:
            logger.error(f"Error calculating conversion rate by month: {e}")
            return [], [], [], []

    def calculate_seasonal_conversion_analysis(self: List[Any]) -> float:
        """
        Calculate the conversion rates by quarter and month of year to identify seasonal patterns

        Returns:
            tuple: (quarter_labels, quarter_rates, month_labels, month_rates)
        """
        try:
            df = self.data_loader.get_data()
            lead_id_col = self.data_loader.get_lead_id_column()

            # Check if we have the necessary columns
            if 'Data_criacao_lead' not in df.columns or not lead_id_col:
                logger.warning("Missing columns for seasonal conversion analysis")
                return [], [], [], []

            # Make sure date columns are datetime
            if not pd.api.types.is_datetime64_any_dtype(df['Data_criacao_lead']):
                df['Data_criacao_lead'] = pd.to_datetime(df['Data_criacao_lead'], errors='coerce')

            # Add quarter and month columns
            df['quarter'] = df['Data_criacao_lead'].dt.to_period('Q').astype(str)
            df['month_of_year'] = df['Data_criacao_lead'].dt.month

            # Get unique quarters and sort them
            quarters = sorted(df['quarter'].dropna().unique())

            # Get the last 8 quarters if we have enough data
            if len(quarters) > 8:
                quarters = quarters[-8:]

            quarter_labels = quarters
            quarter_rates = []

            # Calculate conversion rates for each quarter
            for quarter in quarters:
                # Count leads created in this quarter
                quarter_leads = df[df['quarter'] == quarter][lead_id_col].nunique()

                if quarter_leads > 0 and 'Fase_Implantacao' in df.columns:
                    # Count implementations from leads created in this quarter
                    quarter_impls = df[(df['quarter'] == quarter) & df['Fase_Implantacao'].notna()][lead_id_col].nunique()

                    # Calculate overall conversion rate
                    quarter_rate = safe_percentage(quarter_impls, quarter_leads)
                    quarter_rates.append(quarter_rate)
                else:
                    quarter_rates.append(0)

            # Calculate conversion rates by month of year
            month_labels = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']
            month_rates = []

            for month in range(1, 13):
                # Count leads created in this month of year
                month_leads = df[df['month_of_year'] == month][lead_id_col].nunique()

                if month_leads > 0 and 'Fase_Implantacao' in df.columns:
                    # Count implementations from leads created in this month of year
                    month_impls = df[(df['month_of_year'] == month) & df['Fase_Implantacao'].notna()][lead_id_col].nunique()

                    # Calculate overall conversion rate
                    month_rate = safe_percentage(month_impls, month_leads)
                    month_rates.append(month_rate)
                else:
                    month_rates.append(0)

            return quarter_labels, quarter_rates, month_labels, month_rates
        except Exception as e:
            logger.error(f"Error calculating seasonal conversion analysis: {e}")
            return [], [], [], []

    def calculate_conversion_velocity(self: List[Any]) -> float:
        """
        Calculate the conversion velocity (how quickly leads convert to opportunities and implementations)

        Returns:
            dict: Conversion velocity metrics
        """
        try:
            df = self.data_loader.get_data()

            # Check if we have the necessary date columns
            if 'Data_criacao_lead' not in df.columns or 'Data_criacao_Oportunidade' not in df.columns:
                logger.warning("Missing date columns for conversion velocity calculation")
                return {}

            # Make sure date columns are datetime
            for col in ['Data_criacao_lead', 'Data_criacao_Oportunidade', 'Data_Criacao_Implantacao']:
                if col in df.columns and not pd.api.types.is_datetime64_any_dtype(df[col]):
                    df[col] = pd.to_datetime(df[col], errors='coerce')

            # Calculate time from lead to opportunity
            lead_to_opp_df = df.dropna(subset=['Data_criacao_lead', 'Data_criacao_Oportunidade'])
            lead_to_opp_df['lead_to_opp_days'] = (lead_to_opp_df['Data_criacao_Oportunidade'] - lead_to_opp_df['Data_criacao_lead']).dt.days

            # Filter out negative or unreasonably large values
            lead_to_opp_df = lead_to_opp_df[(lead_to_opp_df['lead_to_opp_days'] >= 0) & (lead_to_opp_df['lead_to_opp_days'] <= 365)]

            # Calculate velocity metrics
            result = {}

            # Lead to opportunity velocity by university
            if 'Universidade' in lead_to_opp_df.columns:
                velocity_by_university = lead_to_opp_df.groupby('Universidade')['lead_to_opp_days'].mean().to_dict()
                result['velocity_by_university'] = {k: int(round(v)) for k, v in velocity_by_university.items() if not pd.isna(v)}

            # Lead to opportunity velocity by course
            if 'Curso' in lead_to_opp_df.columns:
                velocity_by_course = lead_to_opp_df.groupby('Curso')['lead_to_opp_days'].mean().to_dict()
                result['velocity_by_course'] = {k: int(round(v)) for k, v in velocity_by_course.items() if not pd.isna(v)}

            # Lead to opportunity velocity by state
            if 'Estado' in lead_to_opp_df.columns:
                velocity_by_state = lead_to_opp_df.groupby('Estado')['lead_to_opp_days'].mean().to_dict()
                result['velocity_by_state'] = {k: int(round(v)) for k, v in velocity_by_state.items() if not pd.isna(v)}

            # Lead to opportunity velocity by responsible
            if 'Nome_Responsavel' in lead_to_opp_df.columns:
                velocity_by_responsible = lead_to_opp_df.groupby('Nome_Responsavel')['lead_to_opp_days'].mean().to_dict()
                result['velocity_by_responsible'] = {k: int(round(v)) for k, v in velocity_by_responsible.items() if not pd.isna(v)}

            # Calculate velocity distribution
            time_bins = [0, 7, 14, 30, 60, 90, float('inf')]
            time_labels = ['0-7 dias', '8-14 dias', '15-30 dias', '31-60 dias', '61-90 dias', '90+ dias']

            velocity_distribution = pd.cut(lead_to_opp_df['lead_to_opp_days'], bins=time_bins, labels=time_labels).value_counts().to_dict()
            result['velocity_distribution'] = {str(k): int(v) for k, v in velocity_distribution.items()}

            return result
        except Exception as e:
            logger.error(f"Error calculating conversion velocity: {e}")
            return {}

    def analyze_turma_captacao_potential(self: List[Any]) -> Dict[str, Any]:
        """
        Análise avançada do potencial de captação por turma
        Foca em métricas específicas para captação de alto nível
        """
        try:
            df = self.data_loader.get_data()

            # Importar funções das regras de negócio
            from app.utils.formatters import safe_convert_to_float
            from app.utils.safe_operations import safe_division, safe_percentage

            # Preparar dados
            df_analysis = df.copy()
            if 'Valor Mensalidade' in df_analysis.columns:
                df_analysis['Valor_Float'] = df_analysis['Valor Mensalidade'].apply(safe_convert_to_float)
            else:
                df_analysis['Valor_Float'] = 0.0

            # Análise por turma
            turma_analysis = []

            for turma in df_analysis['Turma'].dropna().unique():
                turma_df = df_analysis[df_analysis['Turma'] == turma]

                # Métricas básicas
                total_leads = turma_df['Lead_id'].nunique() if 'Lead_id' in turma_df.columns else len(turma_df)
                total_oportunidades = turma_df['Oportunidade_id'].nunique() if 'Oportunidade_id' in turma_df.columns else 0

                # Métricas de conversão
                implementacoes_finalizadas = len(turma_df[turma_df['Status_Implantacao'] == 'Finalizado'])
                implementacoes_ativas = len(turma_df[turma_df['Status_Implantacao'].isin(['Homologacao', 'Primeiro Acesso'])])
                cancelamentos = len(turma_df[turma_df['Status_Implantacao'] == 'Cancelado'])

                # Taxas de conversão
                taxa_lead_opp = safe_percentage(total_oportunidades, total_leads)
                taxa_opp_impl = safe_percentage(implementacoes_finalizadas, total_oportunidades)
                taxa_conversao_geral = safe_percentage(implementacoes_finalizadas, total_leads)
                taxa_cancelamento = safe_percentage(cancelamentos, total_oportunidades)

                # Métricas financeiras
                receita_total = turma_df['Valor_Float'].sum()
                receita_media = turma_df['Valor_Float'].mean()
                receita_potencial = turma_df[turma_df['Status_Implantacao'].isin(['Homologacao', 'Primeiro Acesso'])]['Valor_Float'].sum()

                # Análise de canal e origem
                canais_utilizados = turma_df['canal'].nunique() if 'canal' in turma_df.columns else 0
                utm_sources = turma_df['utm_source'].nunique() if 'utm_source' in turma_df.columns else 0

                # Análise geográfica
                cidades = turma_df['Cidade'].nunique() if 'Cidade' in turma_df.columns else 0
                estados = turma_df['Estado'].nunique() if 'Estado' in turma_df.columns else 0

                # Análise de responsáveis
                responsaveis = turma_df['Nome_Responsavel'].nunique() if 'Nome_Responsavel' in turma_df.columns else 0

                # Análise de cupons e isenções
                uso_cupons = turma_df['ID do cupom'].notna().sum() if 'ID do cupom' in turma_df.columns else 0
                uso_isencoes = (pd.to_numeric(turma_df['Isencao em Meses'], errors='coerce').fillna(0) > 0).sum() if 'Isencao em Meses' in turma_df.columns else 0

                # Score de captação (0-100)
                # Baseado em: volume, conversão, receita, diversificação, retenção
                score_volume = min(100, (total_leads / 50) * 100)  # Normalizado para 50 leads = 100%
                score_conversao = taxa_conversao_geral
                score_receita = min(100, (receita_total / 50000) * 100)  # Normalizado para R$ 50k = 100%
                score_diversificacao = min(100, ((canais_utilizados + estados + responsaveis) / 10) * 100)
                score_retencao = max(0, 100 - taxa_cancelamento)

                # Score composto com pesos específicos para captação
                score_captacao = (
                    score_volume * 0.25 +           # Volume de leads
                    score_conversao * 0.30 +        # Eficiência de conversão
                    score_receita * 0.25 +          # Potencial financeiro
                    score_diversificacao * 0.10 +   # Diversificação de canais/geo
                    score_retencao * 0.10           # Capacidade de retenção
                )

                # Classificação de potencial
                if score_captacao >= 80:
                    potencial = 'Alto Potencial'
                    prioridade = 'Máxima'
                elif score_captacao >= 60:
                    potencial = 'Bom Potencial'
                    prioridade = 'Alta'
                elif score_captacao >= 40:
                    potencial = 'Potencial Moderado'
                    prioridade = 'Média'
                elif score_captacao >= 20:
                    potencial = 'Baixo Potencial'
                    prioridade = 'Baixa'
                else:
                    potencial = 'Potencial Crítico'
                    prioridade = 'Revisão'

                turma_analysis.append({
                    'turma': turma,
                    'total_leads': total_leads,
                    'total_oportunidades': total_oportunidades,
                    'implementacoes_finalizadas': implementacoes_finalizadas,
                    'implementacoes_ativas': implementacoes_ativas,
                    'cancelamentos': cancelamentos,
                    'taxa_lead_opp': taxa_lead_opp,
                    'taxa_opp_impl': taxa_opp_impl,
                    'taxa_conversao_geral': taxa_conversao_geral,
                    'taxa_cancelamento': taxa_cancelamento,
                    'receita_total': receita_total,
                    'receita_media': receita_media,
                    'receita_potencial': receita_potencial,
                    'canais_utilizados': canais_utilizados,
                    'utm_sources': utm_sources,
                    'cidades': cidades,
                    'estados': estados,
                    'responsaveis': responsaveis,
                    'uso_cupons': uso_cupons,
                    'uso_isencoes': uso_isencoes,
                    'score_captacao': round(score_captacao, 2),
                    'potencial': potencial,
                    'prioridade': prioridade
                })

            # Ordenar por score de captação
            turma_analysis.sort(key=lambda x: x['score_captacao'], reverse=True)

            # Estatísticas resumo
            total_turmas = len(turma_analysis)
            score_medio = sum(t['score_captacao'] for t in turma_analysis) / total_turmas if total_turmas > 0 else 0

            # Distribuição por potencial
            distribuicao_potencial = {}
            for turma in turma_analysis:
                pot = turma['potencial']
                distribuicao_potencial[pot] = distribuicao_potencial.get(pot, 0) + 1

            # Top performers para captação
            top_captacao = turma_analysis[:10]

            # Turmas que precisam de atenção
            baixo_potencial = [t for t in turma_analysis if t['score_captacao'] < 40]

            # Oportunidades de melhoria
            oportunidades = []
            for turma in turma_analysis:
                if turma['taxa_conversao_geral'] < 30 and turma['total_leads'] > 10:
                    oportunidades.append({
                        'turma': turma['turma'],
                        'tipo': 'Baixa Conversão',
                        'descricao': f"Taxa de conversão de {turma['taxa_conversao_geral']:.1f}% com {turma['total_leads']} leads",
                        'acao_sugerida': 'Revisar processo de qualificação e follow-up'
                    })

                if turma['canais_utilizados'] <= 1 and turma['total_leads'] > 5:
                    oportunidades.append({
                        'turma': turma['turma'],
                        'tipo': 'Baixa Diversificação',
                        'descricao': f"Apenas {turma['canais_utilizados']} canal(is) de captação",
                        'acao_sugerida': 'Expandir estratégias de marketing e captação'
                    })

                if turma['taxa_cancelamento'] > 20:
                    oportunidades.append({
                        'turma': turma['turma'],
                        'tipo': 'Alta Taxa de Cancelamento',
                        'descricao': f"Taxa de cancelamento de {turma['taxa_cancelamento']:.1f}%",
                        'acao_sugerida': 'Implementar programa de retenção e melhoria da experiência'
                    })

            return {
                'turma_analysis': turma_analysis,
                'resumo': {
                    'total_turmas': total_turmas,
                    'score_medio_captacao': round(score_medio, 2),
                    'distribuicao_potencial': distribuicao_potencial,
                    'total_leads': sum(t['total_leads'] for t in turma_analysis),
                    'total_receita': sum(t['receita_total'] for t in turma_analysis),
                    'receita_potencial_total': sum(t['receita_potencial'] for t in turma_analysis),
                    'taxa_conversao_media': sum(t['taxa_conversao_geral'] for t in turma_analysis) / total_turmas if total_turmas > 0 else 0
                },
                'top_captacao': top_captacao,
                'baixo_potencial': baixo_potencial,
                'oportunidades_melhoria': oportunidades,
                'criterios_score': {
                    'Volume (25%)': 'Quantidade de leads gerados pela turma',
                    'Conversão (30%)': 'Eficiência na conversão de leads para implementações',
                    'Receita (25%)': 'Potencial financeiro e valor gerado',
                    'Diversificação (10%)': 'Variedade de canais, geografias e responsáveis',
                    'Retenção (10%)': 'Capacidade de manter clientes (baixo cancelamento)'
                }
            }

        except Exception as e:
            logger.error(f"Error in turma captacao analysis: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"error": str(e)}
