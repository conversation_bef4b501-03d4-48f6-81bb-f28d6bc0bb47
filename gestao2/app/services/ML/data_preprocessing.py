"""
Amigo DataHub - Data Preprocessing Service
Professional data cleaning and feature engineering for ML models
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class DataPreprocessingService:
    """Professional data preprocessing service for ML models"""

    def __init__(self: List[Any]) -> None:
        """Initialize the preprocessing service"""
        self.processed_data = None
        self.feature_info = {}
        self.preprocessing_log = []

    def clean_and_prepare_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Professional data cleaning and preparation

        Args:
            df: Raw dataframe

        Returns:
            Cleaned and prepared dataframe
        """
        try:
            self.log_step("Iniciando limpeza e preparação dos dados")

            # Create a copy to avoid modifying original data
            cleaned_df = df.copy()

            # 1. Handle missing values strategically
            cleaned_df = self._handle_missing_values(cleaned_df)

            # 2. Clean and standardize data types
            cleaned_df = self._standardize_data_types(cleaned_df)

            # 3. Handle outliers
            cleaned_df = self._handle_outliers(cleaned_df)

            # 4. Create derived features
            cleaned_df = self._create_derived_features(cleaned_df)

            # 5. Encode categorical variables
            cleaned_df = self._encode_categorical_variables(cleaned_df)

            self.processed_data = cleaned_df
            self.log_step(f"Dados processados: {len(cleaned_df)} registros, {len(cleaned_df.columns)} colunas")

            return cleaned_df

        except Exception as e:
            logger.error(f"Erro na preparação dos dados: {e}")
            raise

    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """Handle missing values with domain-specific logic"""
        self.log_step("Tratando valores ausentes")

        # Financial columns - fill with 0
        financial_cols = ['Valor Mensalidade', 'Pagamento de Entrada', 'Isencao em Meses']
        for col in financial_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0)

        # Categorical columns - fill with 'Não Informado'
        categorical_cols = ['Turma', 'Curso', 'Universidade', 'Cidade', 'Estado', 'Produto']
        for col in categorical_cols:
            if col in df.columns:
                df[col] = df[col].fillna('Não Informado')

        # Status columns - fill with 'Pendente'
        status_cols = ['Status_Implantacao', 'Fase_Implantacao', 'Etapa do funil Comercial']
        for col in status_cols:
            if col in df.columns:
                df[col] = df[col].fillna('Pendente')

        # Date columns - keep as NaT for now
        date_cols = [col for col in df.columns if 'Data' in col or 'Date' in col or 'Marco' in col]

        return df

    def _standardize_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize data types for consistency"""
        self.log_step("Padronizando tipos de dados")

        # Convert financial columns to float
        financial_cols = ['Valor Mensalidade', 'Pagamento de Entrada', 'Isencao em Meses']
        for col in financial_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

        # Convert ID columns to string
        id_cols = [col for col in df.columns if 'id' in col.lower() or 'ID' in col]
        for col in id_cols:
            if col in df.columns:
                df[col] = df[col].astype(str)

        # Convert categorical columns to string and clean
        categorical_cols = ['Turma', 'Curso', 'Universidade', 'Cidade', 'Estado', 'Produto']
        for col in categorical_cols:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()

        # Handle date columns
        date_cols = [col for col in df.columns if 'Data' in col or 'Date' in col]
        for col in date_cols:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')

        return df

    def _handle_outliers(self, df: pd.DataFrame) -> pd.DataFrame:
        """Handle outliers in numerical columns"""
        self.log_step("Tratando outliers")

        numerical_cols = ['Valor Mensalidade', 'Pagamento de Entrada', 'Isencao em Meses']

        for col in numerical_cols:
            if col in df.columns and df[col].dtype in ['float64', 'int64']:
                # Use IQR method for outlier detection
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1

                # Define outlier bounds
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                # Cap outliers instead of removing them
                outliers_count = len(df[(df[col] < lower_bound) | (df[col] > upper_bound)])
                if outliers_count > 0:
                    df[col] = df[col].clip(lower=lower_bound, upper=upper_bound)
                    self.log_step(f"Outliers tratados em {col}: {outliers_count} valores")

        return df

    def _create_derived_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create derived features for better analysis"""
        self.log_step("Criando features derivadas")

        # Time-based features
        if 'Data_criacao_Oportunidade' in df.columns:
            df['Dias_Desde_Oportunidade'] = (datetime.now() - df['Data_criacao_Oportunidade']).dt.days

        if 'Data_criacao_lead' in df.columns:
            df['Dias_Desde_Lead'] = (datetime.now() - df['Data_criacao_lead']).dt.days

        # Lead to opportunity conversion time
        if 'Data_criacao_lead' in df.columns and 'Data_criacao_Oportunidade' in df.columns:
            df['Tempo_Lead_Para_Oportunidade'] = (df['Data_criacao_Oportunidade'] - df['Data_criacao_lead']).dt.days

        # Financial features
        if 'Valor Mensalidade' in df.columns:
            # Ensure the column is numeric before using pd.cut
            valor_numeric = pd.to_numeric(df['Valor Mensalidade'], errors='coerce').fillna(0)
            df['Faixa_Valor'] = pd.cut(valor_numeric,
                                     bins=[0, 500, 1000, 2000, float('inf')],
                                     labels=['Baixo', 'Médio', 'Alto', 'Premium'])

        # Status indicators
        if 'Status_Implantacao' in df.columns:
            df['Is_Finalizado'] = (df['Status_Implantacao'] == 'Finalizado').astype(int)
            df['Is_Cancelado'] = (df['Status_Implantacao'] == 'Cancelado').astype(int)

        # Coupon usage
        if 'ID do cupom' in df.columns:
            df['Usa_Cupom'] = df['ID do cupom'].notna().astype(int)

        # Exemption indicator
        if 'Isencao em Meses' in df.columns:
            df['Tem_Isencao'] = (df['Isencao em Meses'] > 0).astype(int)

        return df

    def _encode_categorical_variables(self, df: pd.DataFrame) -> pd.DataFrame:
        """Encode categorical variables for ML models"""
        self.log_step("Codificando variáveis categóricas")

        # High cardinality categorical columns - use frequency encoding
        high_card_cols = ['Turma', 'Curso', 'Universidade', 'Nome do Lead']
        for col in high_card_cols:
            if col in df.columns:
                freq_map = df[col].value_counts().to_dict()
                df[f'{col}_Frequency'] = df[col].map(freq_map)

        # Medium cardinality - use target encoding or one-hot
        medium_card_cols = ['Cidade', 'Estado', 'Produto']
        for col in medium_card_cols:
            if col in df.columns and df[col].nunique() <= 20:
                # One-hot encoding for medium cardinality
                dummies = pd.get_dummies(df[col], prefix=col, drop_first=True)
                df = pd.concat([df, dummies], axis=1)

        # Low cardinality - use one-hot encoding
        low_card_cols = ['Status_Implantacao', 'Fase_Implantacao', 'Faixa_Valor']
        for col in low_card_cols:
            if col in df.columns:
                dummies = pd.get_dummies(df[col], prefix=col, drop_first=True)
                df = pd.concat([df, dummies], axis=1)

        return df

    def prepare_features_for_clustering(self, df: pd.DataFrame, target_column: str = 'Turma') -> Tuple[pd.DataFrame, List[str]]:
        """
        Prepare features specifically for clustering analysis

        Args:
            df: Preprocessed dataframe
            target_column: Column to group by for analysis

        Returns:
            Tuple of (aggregated_features_df, feature_names)
        """
        try:
            self.log_step(f"Preparando features para clusterização por {target_column}")

            # Ensure numeric columns are properly converted
            numeric_cols = ['Valor Mensalidade', 'Pagamento de Entrada', 'Isencao em Meses']
            for col in numeric_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

            # Build aggregation dictionary dynamically based on available columns
            agg_dict = {}

            # Count metrics
            if 'Lead_id' in df.columns:
                agg_dict['Lead_id'] = 'nunique'
            if 'Oportunidade_id' in df.columns:
                agg_dict['Oportunidade_id'] = 'nunique'
            if 'Nome do Lead' in df.columns:
                agg_dict['Nome do Lead'] = 'nunique'

            # Financial metrics
            if 'Valor Mensalidade' in df.columns:
                agg_dict['Valor Mensalidade'] = ['sum', 'mean', 'std', 'count']
            if 'Pagamento de Entrada' in df.columns:
                agg_dict['Pagamento de Entrada'] = ['sum', 'mean']
            if 'Isencao em Meses' in df.columns:
                agg_dict['Isencao em Meses'] = ['sum', 'mean']

            # Conversion metrics
            if 'Is_Finalizado' in df.columns:
                agg_dict['Is_Finalizado'] = ['sum', 'mean']
            if 'Is_Cancelado' in df.columns:
                agg_dict['Is_Cancelado'] = ['sum', 'mean']
            if 'Usa_Cupom' in df.columns:
                agg_dict['Usa_Cupom'] = ['sum', 'mean']
            if 'Tem_Isencao' in df.columns:
                agg_dict['Tem_Isencao'] = ['sum', 'mean']

            # Time metrics
            if 'Dias_Desde_Oportunidade' in df.columns:
                agg_dict['Dias_Desde_Oportunidade'] = ['mean', 'std']
            if 'Dias_Desde_Lead' in df.columns:
                agg_dict['Dias_Desde_Lead'] = ['mean', 'std']
            if 'Tempo_Lead_Para_Oportunidade' in df.columns:
                agg_dict['Tempo_Lead_Para_Oportunidade'] = ['mean', 'std']

            # Diversity metrics
            if 'Cidade' in df.columns:
                agg_dict['Cidade'] = 'nunique'
            if 'Estado' in df.columns:
                agg_dict['Estado'] = 'nunique'
            if 'Produto' in df.columns:
                agg_dict['Produto'] = 'nunique'
            if 'Curso' in df.columns:
                agg_dict['Curso'] = 'nunique'

            if not agg_dict:
                raise ValueError("Nenhuma coluna válida encontrada para agregação")

            # Group by target column and aggregate features
            agg_features = df.groupby(target_column).agg(agg_dict).round(2)

            # Flatten column names
            agg_features.columns = ['_'.join(col).strip() for col in agg_features.columns]
            agg_features = agg_features.reset_index()

            # Calculate derived metrics based on available columns
            if 'Is_Finalizado_sum' in agg_features.columns and 'Oportunidade_id_nunique' in agg_features.columns:
                agg_features['Taxa_Conversao'] = (agg_features['Is_Finalizado_sum'] /
                                                agg_features['Oportunidade_id_nunique']).fillna(0) * 100
            elif 'Is_Finalizado_sum' in agg_features.columns and 'Nome do Lead_nunique' in agg_features.columns:
                agg_features['Taxa_Conversao'] = (agg_features['Is_Finalizado_sum'] /
                                                agg_features['Nome do Lead_nunique']).fillna(0) * 100

            if 'Is_Cancelado_sum' in agg_features.columns and 'Oportunidade_id_nunique' in agg_features.columns:
                agg_features['Taxa_Cancelamento'] = (agg_features['Is_Cancelado_sum'] /
                                                   agg_features['Oportunidade_id_nunique']).fillna(0) * 100
            elif 'Is_Cancelado_sum' in agg_features.columns and 'Nome do Lead_nunique' in agg_features.columns:
                agg_features['Taxa_Cancelamento'] = (agg_features['Is_Cancelado_sum'] /
                                                   agg_features['Nome do Lead_nunique']).fillna(0) * 100

            if 'Valor Mensalidade_sum' in agg_features.columns and 'Lead_id_nunique' in agg_features.columns:
                agg_features['Receita_Por_Lead'] = (agg_features['Valor Mensalidade_sum'] /
                                                  agg_features['Lead_id_nunique']).fillna(0)
            elif 'Valor Mensalidade_sum' in agg_features.columns and 'Nome do Lead_nunique' in agg_features.columns:
                agg_features['Receita_Por_Lead'] = (agg_features['Valor Mensalidade_sum'] /
                                                  agg_features['Nome do Lead_nunique']).fillna(0)

            if 'Usa_Cupom_sum' in agg_features.columns and 'Lead_id_nunique' in agg_features.columns:
                agg_features['Taxa_Uso_Cupom'] = (agg_features['Usa_Cupom_sum'] /
                                                agg_features['Lead_id_nunique']).fillna(0) * 100
            elif 'Usa_Cupom_sum' in agg_features.columns and 'Nome do Lead_nunique' in agg_features.columns:
                agg_features['Taxa_Uso_Cupom'] = (agg_features['Usa_Cupom_sum'] /
                                                agg_features['Nome do Lead_nunique']).fillna(0) * 100

            # Select features for clustering (exclude target and non-numeric)
            feature_cols = [col for col in agg_features.columns
                          if col != target_column and agg_features[col].dtype in ['float64', 'int64']]

            # Handle any remaining NaN or infinite values
            for col in feature_cols:
                agg_features[col] = agg_features[col].replace([np.inf, -np.inf], 0).fillna(0)

            self.log_step(f"Features preparadas: {len(feature_cols)} features para {len(agg_features)} grupos")

            return agg_features, feature_cols

        except Exception as e:
            logger.error(f"Erro na preparação de features: {e}")
            raise

    def log_step(self: List[Any], message: str) -> None:
        """Log preprocessing steps"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.preprocessing_log.append(log_entry)
        logger.info(log_entry)

    def get_preprocessing_summary(self) -> Dict[str, Any]:
        """Get summary of preprocessing steps"""
        return {
            'steps_executed': self.preprocessing_log,
            'total_steps': len(self.preprocessing_log),
            'processed_data_shape': self.processed_data.shape if self.processed_data is not None else None,
            'feature_info': self.feature_info
        }
