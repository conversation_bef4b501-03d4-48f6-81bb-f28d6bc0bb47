"""
Final Clustering Service - Serviço final de clusterização com dataset correto
Implementação completa que usa o dataset validado e regras de negócio
"""

import pandas as pd
import numpy as np
import logging
import os
from typing import Dict, List, Any, Tuple
import warnings
warnings.filterwarnings('ignore')

from .clustering_dataset_service import ClusteringDatasetService

logger = logging.getLogger(__name__)

class FinalClusteringService:
    """Serviço final de clusterização com dataset validado"""

    def __init__(self: List[Any]) -> None:
        """Initialize the final clustering service"""
        self.dataset_service = ClusteringDatasetService()
        self.clustering_dataset = None
        self.cluster_labels = None
        self.cluster_model_info = {}

    def simple_kmeans(self, X: np.ndarray, k: int, max_iters: int = 100) -> Tuple[np.ndarray, np.ndarray]:
        """Implementação simples e robusta do K-means"""
        try:
            n_samples, n_features = X.shape

            # Inicializar centroides usando K-means++
            np.random.seed(42)
            centroids = np.zeros((k, n_features))

            # Primeiro centroide aleatório
            centroids[0] = X[np.random.randint(n_samples)]

            # Demais centroides usando K-means++
            for i in range(1, k):
                distances = np.array([min([np.linalg.norm(x - c)**2 for c in centroids[:i]]) for x in X])
                probabilities = distances / distances.sum()
                cumulative_probabilities = probabilities.cumsum()
                r = np.random.rand()

                for j, p in enumerate(cumulative_probabilities):
                    if r < p:
                        centroids[i] = X[j]
                        break

            # Algoritmo K-means
            for iteration in range(max_iters):
                # Atribuir pontos aos centroides mais próximos
                distances = np.sqrt(((X - centroids[:, np.newaxis])**2).sum(axis=2))
                labels = np.argmin(distances, axis=0)

                # Atualizar centroides
                new_centroids = np.array([X[labels == i].mean(axis=0) if np.sum(labels == i) > 0 else centroids[i] for i in range(k)])

                # Verificar convergência
                if np.allclose(centroids, new_centroids, rtol=1e-4):
                    logger.info(f"K-means convergiu em {iteration + 1} iterações")
                    break

                centroids = new_centroids

            return labels, centroids

        except Exception as e:
            logger.error(f"Erro no K-means: {e}")
            # Fallback: divisão por percentis
            composite_score = X.mean(axis=1)
            percentiles = np.linspace(0, 100, k + 1)
            thresholds = np.percentile(composite_score, percentiles)

            labels = np.zeros(len(composite_score), dtype=int)
            for i in range(k):
                if i == k - 1:
                    mask = (composite_score >= thresholds[i]) & (composite_score <= thresholds[i + 1])
                else:
                    mask = (composite_score >= thresholds[i]) & (composite_score < thresholds[i + 1])
                labels[mask] = i

            return labels, np.zeros((k, X.shape[1]))

    def calculate_clustering_score(self, X: np.ndarray, labels: np.ndarray) -> float:
        """Calcular score de qualidade da clusterização"""
        try:
            unique_labels = np.unique(labels)

            if len(unique_labels) <= 1:
                return 0.0

            # Calcular Within-Cluster Sum of Squares (WCSS)
            wcss = 0
            for label in unique_labels:
                cluster_points = X[labels == label]
                if len(cluster_points) > 0:
                    centroid = cluster_points.mean(axis=0)
                    wcss += np.sum((cluster_points - centroid) ** 2)

            # Calcular Between-Cluster Sum of Squares (BCSS)
            overall_centroid = X.mean(axis=0)
            bcss = 0
            for label in unique_labels:
                cluster_points = X[labels == label]
                if len(cluster_points) > 0:
                    cluster_centroid = cluster_points.mean(axis=0)
                    bcss += len(cluster_points) * np.sum((cluster_centroid - overall_centroid) ** 2)

            # Score = BCSS / (BCSS + WCSS) - normalizado entre 0 e 1
            total_ss = bcss + wcss
            score = bcss / total_ss if total_ss > 0 else 0

            return float(score)

        except Exception as e:
            logger.error(f"Erro no cálculo do score: {e}")
            return 0.5

    def find_optimal_clusters(self, X: np.ndarray, max_clusters: int = 8) -> int:
        """Encontrar número ótimo de clusters usando método do cotovelo"""
        try:
            if len(X) < 4:
                return 2

            max_k = min(max_clusters, len(X) // 3, 8)  # Máximo razoável
            scores = []
            inertias = []

            for k in range(2, max_k + 1):
                try:
                    labels, centroids = self.simple_kmeans(X, k)
                    score = self.calculate_clustering_score(X, labels)

                    # Calcular inércia (WCSS)
                    inertia = 0
                    for i in range(k):
                        cluster_points = X[labels == i]
                        if len(cluster_points) > 0:
                            inertia += np.sum((cluster_points - centroids[i]) ** 2)

                    scores.append((k, score))
                    inertias.append((k, inertia))

                    logger.info(f"K={k}: score={score:.3f}, inertia={inertia:.1f}")

                except Exception as e:
                    logger.warning(f"Erro ao testar {k} clusters: {e}")
                    continue

            if not scores:
                return 3  # Fallback

            # Método do cotovelo para inércia
            if len(inertias) >= 3:
                # Calcular diferenças de segunda ordem
                second_diffs = []
                for i in range(1, len(inertias) - 1):
                    diff1 = inertias[i-1][1] - inertias[i][1]
                    diff2 = inertias[i][1] - inertias[i+1][1]
                    second_diff = abs(diff1 - diff2)
                    second_diffs.append((inertias[i][0], second_diff))

                # Escolher k com maior diferença de segunda ordem
                if second_diffs:
                    optimal_k = max(second_diffs, key=lambda x: x[1])[0]
                else:
                    optimal_k = max(scores, key=lambda x: x[1])[0]
            else:
                # Fallback: melhor score
                optimal_k = max(scores, key=lambda x: x[1])[0]

            best_score = next((score for k, score in scores if k == optimal_k), 0)
            logger.info(f"Número ótimo de clusters: {optimal_k} (score: {best_score:.3f})")

            return optimal_k

        except Exception as e:
            logger.error(f"Erro na busca do número ótimo: {e}")
            return 3

    def normalize_features(self, X: np.ndarray) -> np.ndarray:
        """Normalizar features usando Min-Max scaling robusto"""
        try:
            X_normalized = np.zeros_like(X, dtype=float)

            for i in range(X.shape[1]):
                col_data = X[:, i].astype(float)  # Garantir que é float

                # Remover valores infinitos e NaN
                col_data = np.where(np.isfinite(col_data), col_data, 0)

                col_min, col_max = np.min(col_data), np.max(col_data)

                if col_max > col_min and np.isfinite(col_max) and np.isfinite(col_min):
                    X_normalized[:, i] = (col_data - col_min) / (col_max - col_min)
                else:
                    # Se todos os valores são iguais, definir como 0.5
                    X_normalized[:, i] = 0.5

            return X_normalized

        except Exception as e:
            logger.error(f"Erro na normalização: {e}")
            # Fallback: retornar array normalizado simples
            try:
                return np.clip(X.astype(float), 0, 1)
            except:
                return np.ones_like(X, dtype=float) * 0.5

    def load_ml_dataset(self) -> pd.DataFrame:
        """Carregar dataset ML pré-processado"""
        try:
            import os

            # Caminho para o dataset ML
            ml_dataset_path = 'data/ML/datasets/clustering_dataset_ml_ready.csv'

            if not os.path.exists(ml_dataset_path):
                logger.error(f"Dataset ML não encontrado: {ml_dataset_path}")
                return None

            # Carregar dataset
            df = pd.read_csv(ml_dataset_path)
            logger.info(f"Dataset ML carregado: {len(df)} registros")

            return df

        except Exception as e:
            logger.error(f"Erro ao carregar dataset ML: {e}")
            return None

    def perform_clustering(self, df: pd.DataFrame = None, n_clusters: int = None) -> Dict[str, Any]:
        """
        Executar clusterização completa com dataset validado

        Args:
            df: DataFrame com dados brutos (opcional, usa dataset ML se não fornecido)

        Returns:
            Resultados da clusterização
        """
        try:
            logger.info("Iniciando clusterização final com dataset validado...")

            # Tentar usar dataset ML primeiro
            ml_dataset = self.load_ml_dataset()

            if ml_dataset is not None:
                logger.info("Usando dataset ML pré-processado")
                self.clustering_dataset = ml_dataset

                # Verificar se tem coluna turma_id (dataset ML) ou Turma (dataset original)
                if 'turma_id' in self.clustering_dataset.columns:
                    # Dataset ML - renomear para compatibilidade
                    self.clustering_dataset = self.clustering_dataset.rename(columns={'turma_id': 'Turma'})

            elif df is not None:
                logger.info("Usando dados brutos fornecidos")
                # Etapa 1: Criar dataset de clusterização
                self.clustering_dataset = self.dataset_service.create_clustering_dataset(df)
            else:
                return {"error": "Dados de clusterização não disponíveis"}

            if len(self.clustering_dataset) < 2:
                return {"error": "Insuficientes turmas para clusterização (mínimo 2)"}

            # Etapa 2: Preparar features para clusterização (4 features otimizadas)
            feature_columns = ['total_leads', 'taxa_conversao', 'volume_implementacoes', 'receita_por_lead']

            # Verificar se todas as features existem
            available_features = [col for col in feature_columns if col in self.clustering_dataset.columns]
            missing_features = [col for col in feature_columns if col not in self.clustering_dataset.columns]

            if missing_features:
                logger.warning(f"Features faltando no dataset: {missing_features}")

            if len(available_features) < 2:
                return {"error": f"Insuficientes features disponíveis para clusterização. Disponíveis: {available_features}"}

            # Garantir que todas as features são numéricas
            for col in available_features:
                # Converter para numérico, forçando erros para 0
                self.clustering_dataset[col] = pd.to_numeric(self.clustering_dataset[col], errors='coerce').fillna(0)
                # Remover infinitos
                self.clustering_dataset[col] = self.clustering_dataset[col].replace([np.inf, -np.inf], 0)

            X = self.clustering_dataset[available_features].values.astype(float)

            # Verificar se há variação suficiente
            feature_stds = np.std(X, axis=0)
            valid_features = feature_stds > 0

            if not np.any(valid_features):
                return {"error": "Nenhuma feature tem variação suficiente para clusterização"}

            # Usar apenas features com variação
            X_filtered = X[:, valid_features]
            used_features = [available_features[i] for i in range(len(available_features)) if valid_features[i]]

            logger.info(f"Usando {len(used_features)} features: {used_features}")

            # Etapa 3: Normalizar dados
            X_normalized = self.normalize_features(X_filtered)

            # Etapa 4: Encontrar número ótimo de clusters
            if n_clusters is not None and 2 <= n_clusters <= min(8, len(X_normalized)-1):
                optimal_k = n_clusters
                logger.info(f"Usando número de clusters especificado: {optimal_k}")
            else:
                optimal_k = self.find_optimal_clusters(X_normalized)
                logger.info(f"Número ótimo de clusters determinado automaticamente: {optimal_k}")

            # Etapa 5: Executar clusterização final
            labels, centroids = self.simple_kmeans(X_normalized, optimal_k)
            score = self.calculate_clustering_score(X_normalized, labels)

            # Salvar resultados
            self.cluster_labels = labels
            self.cluster_model_info = {
                'n_clusters': optimal_k,
                'features_used': used_features,
                'centroids': centroids.tolist(),
                'score': score
            }

            # Criar resultado
            result = {
                'algorithm': 'K-means (implementação própria)',
                'n_clusters': optimal_k,
                'silhouette_score': float(score),
                'labels': labels.tolist(),
                'turmas': self.clustering_dataset['Turma'].tolist(),
                'success': True,
                'method': 'final_clustering_service',
                'features_used': used_features,
                'total_turmas': len(self.clustering_dataset),
                'dataset_info': {
                    'original_records': len(df) if df is not None else len(self.clustering_dataset),
                    'processed_turmas': len(self.clustering_dataset),
                    'features_count': len(used_features)
                }
            }

            # Adicionar análise de clusters
            result['cluster_analysis'] = self._analyze_clusters(labels, used_features)

            # Adicionar relatório de qualidade dos dados (se disponível)
            if hasattr(self.dataset_service, 'get_data_quality_report'):
                result['data_quality'] = self.dataset_service.get_data_quality_report()
            else:
                result['data_quality'] = {"message": "Usando dataset ML pré-processado"}

            logger.info(f"Clusterização final concluída: {optimal_k} clusters, score: {score:.3f}")
            return result

        except Exception as e:
            logger.error(f"Erro na clusterização final: {e}")
            return {"error": str(e)}

    def _analyze_clusters(self, labels: np.ndarray, feature_names: List[str]) -> Dict[str, Any]:
        """Analisar clusters com dataset validado"""
        try:
            logger.info("Analisando clusters com dataset validado...")

            # Adicionar labels ao dataset
            df_analysis = self.clustering_dataset.copy()
            df_analysis['cluster'] = labels

            cluster_profiles = []
            unique_labels = np.unique(labels)

            for cluster_id in sorted(unique_labels):
                cluster_data = df_analysis[df_analysis['cluster'] == cluster_id]

                # Estatísticas básicas
                profile = {
                    'cluster_id': int(cluster_id),
                    'size': len(cluster_data),
                    'percentage': float(len(cluster_data) / len(df_analysis) * 100),
                    'turmas_sample': cluster_data['Turma'].head(5).tolist()
                }

                # Métricas de negócio por cluster
                business_metrics = {}
                all_features = ['total_leads', 'taxa_conversao', 'volume_implementacoes', 'receita_por_lead']

                for feature in all_features:
                    if feature in cluster_data.columns:
                        try:
                            mean_val = cluster_data[feature].mean()
                            median_val = cluster_data[feature].median()
                            business_metrics[feature] = {
                                'mean': float(mean_val),
                                'median': float(median_val),
                                'min': float(cluster_data[feature].min()),
                                'max': float(cluster_data[feature].max())
                            }
                        except:
                            pass

                profile['business_metrics'] = business_metrics

                # Caracterização do cluster
                profile['caracterizacao'] = self._characterize_cluster(cluster_data)

                cluster_profiles.append(profile)

            # Gerar insights e recomendações
            insights = self._generate_insights(cluster_profiles)
            recommendations = self._generate_recommendations(cluster_profiles)

            return {
                'cluster_profiles': cluster_profiles,
                'total_clusters': len(cluster_profiles),
                'insights': insights,
                'recommendations': recommendations,
                'feature_importance': self._calculate_feature_importance(df_analysis, labels, feature_names)
            }

        except Exception as e:
            logger.error(f"Erro na análise de clusters: {e}")
            return {}

    def _characterize_cluster(self, cluster_data: pd.DataFrame) -> str:
        """Caracterizar cluster baseado em métricas de negócio"""
        try:
            characteristics = []

            # Analisar volume de leads
            if 'total_leads' in cluster_data.columns:
                volume = cluster_data['total_leads'].mean()
                if volume > 50:
                    characteristics.append("Alto volume")
                elif volume > 20:
                    characteristics.append("Volume médio")
                else:
                    characteristics.append("Baixo volume")

            # Analisar taxa de conversão
            if 'taxa_conversao' in cluster_data.columns:
                conversao = cluster_data['taxa_conversao'].mean()
                if conversao > 30:
                    characteristics.append("Alta conversão")
                elif conversao > 10:
                    characteristics.append("Conversão média")
                else:
                    characteristics.append("Baixa conversão")

            # Analisar receita por lead
            if 'receita_por_lead' in cluster_data.columns:
                receita = cluster_data['receita_por_lead'].mean()
                if receita > 300:
                    characteristics.append("Alta receita/lead")
                elif receita > 150:
                    characteristics.append("Receita média/lead")
                else:
                    characteristics.append("Baixa receita/lead")

            return " | ".join(characteristics) if characteristics else "Perfil misto"

        except Exception as e:
            logger.error(f"Erro na caracterização: {e}")
            return "Perfil indefinido"

    def _generate_insights(self, cluster_profiles: List[Dict]) -> List[str]:
        """Gerar insights de negócio baseados nos clusters"""
        try:
            insights = []

            if not cluster_profiles:
                return insights

            # Insight sobre distribuição
            largest_cluster = max(cluster_profiles, key=lambda x: x['size'])
            smallest_cluster = min(cluster_profiles, key=lambda x: x['size'])

            insights.append(f"Cluster {largest_cluster['cluster_id']} concentra {largest_cluster['percentage']:.1f}% das turmas")
            insights.append(f"Cluster {smallest_cluster['cluster_id']} representa apenas {smallest_cluster['percentage']:.1f}% das turmas")

            # Insights sobre conversão
            conversao_clusters = []
            for p in cluster_profiles:
                if 'taxa_conversao' in p['business_metrics']:
                    conversao_clusters.append((p['cluster_id'], p['business_metrics']['taxa_conversao']['mean']))

            if conversao_clusters:
                best_conversion = max(conversao_clusters, key=lambda x: x[1])
                worst_conversion = min(conversao_clusters, key=lambda x: x[1])

                insights.append(f"Cluster {best_conversion[0]} tem a melhor taxa de conversão ({best_conversion[1]:.1f}%)")
                insights.append(f"Cluster {worst_conversion[0]} tem a menor taxa de conversão ({worst_conversion[1]:.1f}%)")

            # Insights sobre volume
            volume_clusters = []
            for p in cluster_profiles:
                if 'total_leads' in p['business_metrics']:
                    volume_clusters.append((p['cluster_id'], p['business_metrics']['total_leads']['mean']))

            if volume_clusters:
                highest_volume = max(volume_clusters, key=lambda x: x[1])
                lowest_volume = min(volume_clusters, key=lambda x: x[1])

                insights.append(f"Cluster {highest_volume[0]} tem o maior volume médio ({highest_volume[1]:.0f} leads)")
                insights.append(f"Cluster {lowest_volume[0]} tem o menor volume médio ({lowest_volume[1]:.0f} leads)")

            # Insights sobre receita
            receita_clusters = []
            for p in cluster_profiles:
                if 'receita_por_lead' in p['business_metrics']:
                    receita_clusters.append((p['cluster_id'], p['business_metrics']['receita_por_lead']['mean']))

            if receita_clusters:
                highest_revenue = max(receita_clusters, key=lambda x: x[1])
                lowest_revenue = min(receita_clusters, key=lambda x: x[1])

                insights.append(f"Cluster {highest_revenue[0]} tem a maior receita por lead (R$ {highest_revenue[1]:.2f})")
                insights.append(f"Cluster {lowest_revenue[0]} tem a menor receita por lead (R$ {lowest_revenue[1]:.2f})")

            return insights

        except Exception as e:
            logger.error(f"Erro na geração de insights: {e}")
            return []

    def _generate_recommendations(self, cluster_profiles: List[Dict]) -> List[str]:
        """Gerar recomendações de negócio baseadas nos clusters"""
        try:
            recommendations = []

            if not cluster_profiles:
                return recommendations

            # Recomendações gerais
            recommendations.append(f"Identificados {len(cluster_profiles)} perfis distintos de turmas para estratégias diferenciadas")

            # Recomendações específicas por cluster
            for profile in cluster_profiles:
                cluster_id = profile['cluster_id']
                caracterizacao = profile.get('caracterizacao', '')
                size = profile['size']

                if 'Alto volume' in caracterizacao and 'Alta conversão' in caracterizacao:
                    recommendations.append(f"Cluster {cluster_id} ({size} turmas): Perfil premium - foque em retenção e expansão")
                elif 'Alto volume' in caracterizacao and 'Baixa conversão' in caracterizacao:
                    recommendations.append(f"Cluster {cluster_id} ({size} turmas): Alto potencial - melhore processo de conversão")
                elif 'Baixo volume' in caracterizacao and 'Alta conversão' in caracterizacao:
                    recommendations.append(f"Cluster {cluster_id} ({size} turmas): Eficiente mas pequeno - estratégias de crescimento")
                elif 'Baixa conversão' in caracterizacao:
                    recommendations.append(f"Cluster {cluster_id} ({size} turmas): Necessita intervenção urgente no processo comercial")
                elif 'Alta receita/lead' in caracterizacao:
                    recommendations.append(f"Cluster {cluster_id} ({size} turmas): Alto valor - estratégias de upselling")
                elif 'Baixa receita/lead' in caracterizacao:
                    recommendations.append(f"Cluster {cluster_id} ({size} turmas): Revisar estratégia de precificação")

            # Recomendações estratégicas
            recommendations.append("Monitore a evolução dos clusters mensalmente para identificar tendências")
            recommendations.append("Personalize campanhas de marketing baseadas no perfil de cada cluster")
            recommendations.append("Implemente KPIs específicos para cada cluster identificado")

            return recommendations

        except Exception as e:
            logger.error(f"Erro na geração de recomendações: {e}")
            return []

    def _calculate_feature_importance(self, df: pd.DataFrame, labels: np.ndarray,
                                    feature_names: List[str]) -> Dict[str, float]:
        """Calcular importância das features para a separação dos clusters"""
        try:
            importance = {}

            for feature in feature_names:
                if feature in df.columns:
                    # Calcular F-statistic (ANOVA) entre clusters
                    feature_data = df[feature].values

                    # Variância entre grupos
                    overall_mean = np.mean(feature_data)
                    between_group_var = 0
                    within_group_var = 0

                    for cluster_id in np.unique(labels):
                        cluster_data = feature_data[labels == cluster_id]
                        if len(cluster_data) > 0:
                            cluster_mean = np.mean(cluster_data)
                            between_group_var += len(cluster_data) * (cluster_mean - overall_mean) ** 2
                            within_group_var += np.sum((cluster_data - cluster_mean) ** 2)

                    # F-statistic normalizado
                    if within_group_var > 0:
                        f_stat = between_group_var / within_group_var
                        # Normalizar para 0-1
                        importance[feature] = float(f_stat / (f_stat + 1))
                    else:
                        importance[feature] = 0.0

            return importance

        except Exception as e:
            logger.error(f"Erro no cálculo de importância: {e}")
            return {}

    def get_cluster_summary(self) -> Dict[str, Any]:
        """Obter resumo da clusterização para relatórios"""
        try:
            if self.clustering_dataset is None or self.cluster_labels is None:
                return {"error": "Nenhuma clusterização executada"}

            return {
                'total_turmas': len(self.clustering_dataset),
                'model_info': self.cluster_model_info,
                'dataset_features': self.dataset_service.get_feature_descriptions(),
                'data_quality': self.dataset_service.get_data_quality_report()
            }

        except Exception as e:
            logger.error(f"Erro no resumo: {e}")
            return {"error": str(e)}

    def get_scatter_plot_data(self, clustering_result: Dict[str, Any]) -> Dict[str, Any]:
        """Obter dados formatados para gráfico de dispersão"""
        try:
            if not clustering_result or 'error' in clustering_result:
                return {'error': 'Resultado de clusterização inválido'}

            # Carregar dataset ML se necessário
            if self.clustering_dataset is None:
                self._load_ml_dataset()

            if self.clustering_dataset is None or self.clustering_dataset.empty:
                return {'error': 'Dataset ML não disponível'}

            # Obter labels dos clusters do resultado
            cluster_labels = clustering_result.get('cluster_labels', [])
            if not cluster_labels:
                return {'error': 'Labels de cluster não encontrados'}

            # Adicionar labels ao dataset
            dataset_with_clusters = self.clustering_dataset.copy()
            dataset_with_clusters['cluster'] = cluster_labels

            # Carregar mapeamento de turmas
            turma_mapping_path = os.path.join(self.ml_data_dir, 'turma_id_mapping.csv')
            if os.path.exists(turma_mapping_path):
                turma_mapping = pd.read_csv(turma_mapping_path)
                dataset_with_clusters = dataset_with_clusters.merge(
                    turma_mapping, on='turma_id', how='left'
                )
            else:
                # Fallback: usar turma_id como nome
                dataset_with_clusters['turma_nome'] = dataset_with_clusters['turma_id'].apply(
                    lambda x: f'Turma {x}'
                )

            # Organizar dados por cluster
            clusters_data = {}
            total_turmas = len(dataset_with_clusters)

            for cluster_id in sorted(dataset_with_clusters['cluster'].unique()):
                cluster_data = dataset_with_clusters[dataset_with_clusters['cluster'] == cluster_id]

                # Preparar dados das turmas do cluster
                turmas_list = []
                for _, row in cluster_data.iterrows():
                    turma_info = {
                        'turma_id': int(row['turma_id']),
                        'turma_nome': row.get('turma_nome', f'Turma {row["turma_id"]}'),
                        'total_leads': float(row['total_leads']),
                        'taxa_conversao': float(row['taxa_conversao']),
                        'volume_implementacoes': float(row['volume_implementacoes']),
                        'receita_por_lead': float(row['receita_por_lead'])
                    }
                    turmas_list.append(turma_info)

                clusters_data[str(cluster_id)] = {
                    'size': len(cluster_data),
                    'percentage': (len(cluster_data) / total_turmas) * 100,
                    'turmas': turmas_list,
                    'avg_metrics': {
                        'total_leads': float(cluster_data['total_leads'].mean()),
                        'taxa_conversao': float(cluster_data['taxa_conversao'].mean()),
                        'volume_implementacoes': float(cluster_data['volume_implementacoes'].mean()),
                        'receita_por_lead': float(cluster_data['receita_por_lead'].mean())
                    }
                }

            scatter_data = {
                'success': True,
                'total_turmas': total_turmas,
                'n_clusters': len(clusters_data),
                'clusters_data': clusters_data,
                'features_used': ['total_leads', 'taxa_conversao', 'volume_implementacoes', 'receita_por_lead'],
                'silhouette_score': clustering_result.get('silhouette_score', 0)
            }

            logger.info(f"Dados de dispersão preparados: {total_turmas} turmas em {len(clusters_data)} clusters")
            return scatter_data

        except Exception as e:
            logger.error(f"Erro ao preparar dados de dispersão: {e}")
            return {'error': f'Erro ao preparar dados de dispersão: {str(e)}'}
