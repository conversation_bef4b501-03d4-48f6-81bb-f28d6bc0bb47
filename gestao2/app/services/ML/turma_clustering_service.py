"""
Turma Clustering Service - Serviço de clusterização para turmas
Integra o modelo K-means treinado com o sistema de clustering
"""

import pandas as pd
import numpy as np
import json
import os
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class TurmaClusteringService:
    """Serviço de clusterização para análise de turmas"""

    def __init__(self):
        self.model_loaded = False
        self.cluster_stats = None
        self.feature_columns = None
        self.turma_mapping = None
        self.clustering_results = None

    def load_clustering_data(self) -> bool:
        """Carregar dados de clusterização e mapeamento"""
        try:
            # Caminhos dos arquivos
            base_path = Path(__file__).parent.parent.parent

            # Carregar estatísticas do modelo
            stats_file = base_path / 'data' / 'ML' / 'stats' / 'clustering_stats_basic.json'
            if stats_file.exists():
                with open(stats_file, 'r') as f:
                    stats_data = json.load(f)
                    self.cluster_stats = stats_data
                    self.feature_columns = stats_data.get('feature_columns', [])
                logger.info("✅ Estatísticas de clusterização carregadas")
            else:
                logger.warning("⚠️  Arquivo de estatísticas não encontrado")
                return False

            # Carregar mapeamento de turmas
            mapping_file = base_path / 'data' / 'ML' / 'datasets' / 'turma_id_mapping.csv'
            if mapping_file.exists():
                self.turma_mapping = pd.read_csv(mapping_file)
                logger.info(f"✅ Mapeamento de turmas carregado: {len(self.turma_mapping)} turmas")
            else:
                logger.warning("⚠️  Arquivo de mapeamento não encontrado")
                return False

            # Carregar resultados de clusterização
            results_file = base_path / 'data' / 'ML' / 'results' / 'clustering_results_basic.csv'
            if results_file.exists():
                self.clustering_results = pd.read_csv(results_file)
                logger.info(f"✅ Resultados de clusterização carregados: {len(self.clustering_results)} registros")
            else:
                logger.warning("⚠️  Arquivo de resultados não encontrado")
                return False

            self.model_loaded = True
            return True

        except Exception as e:
            logger.error(f"❌ Erro ao carregar dados de clusterização: {e}")
            return False

    def get_cluster_summary(self) -> Dict[str, Any]:
        """Obter resumo dos clusters"""
        if not self.model_loaded:
            if not self.load_clustering_data():
                return {"error": "Dados de clusterização não disponíveis"}

        try:
            summary = {
                "model_info": {
                    "n_clusters": self.cluster_stats['metrics']['n_clusters'],
                    "silhouette_score": self.cluster_stats['metrics']['silhouette_score'],
                    "total_turmas": len(self.clustering_results)
                },
                "clusters": {}
            }

            # Informações detalhadas de cada cluster
            for cluster_key, cluster_data in self.cluster_stats['cluster_analysis'].items():
                cluster_id = cluster_key.split('_')[1]

                # Obter nomes das turmas neste cluster
                cluster_turmas = self.clustering_results[
                    self.clustering_results['cluster'] == int(cluster_id)
                ]['turma_id'].tolist()

                # Mapear IDs para nomes
                turma_names = []
                for turma_id in cluster_turmas:
                    mapping_row = self.turma_mapping[self.turma_mapping['turma_id'] == turma_id]
                    if not mapping_row.empty:
                        turma_names.append(mapping_row.iloc[0]['turma_nome'])

                summary["clusters"][f"cluster_{cluster_id}"] = {
                    "size": cluster_data['size'],
                    "percentage": round(cluster_data['percentage'], 1),
                    "characteristics": cluster_data['stats'],
                    "turmas": turma_names[:10],  # Primeiras 10 turmas
                    "total_turmas": len(turma_names)
                }

            return summary

        except Exception as e:
            logger.error(f"❌ Erro ao gerar resumo dos clusters: {e}")
            return {"error": str(e)}

    def get_turma_cluster(self, turma_nome: str) -> Dict[str, Any]:
        """Obter cluster de uma turma específica"""
        if not self.model_loaded:
            if not self.load_clustering_data():
                return {"error": "Dados de clusterização não disponíveis"}

        try:
            # Encontrar ID da turma
            mapping_row = self.turma_mapping[self.turma_mapping['turma_nome'] == turma_nome]
            if mapping_row.empty:
                return {"error": f"Turma '{turma_nome}' não encontrada"}

            turma_id = mapping_row.iloc[0]['turma_id']

            # Encontrar cluster da turma
            result_row = self.clustering_results[self.clustering_results['turma_id'] == turma_id]
            if result_row.empty:
                return {"error": f"Dados de cluster não encontrados para turma '{turma_nome}'"}

            cluster_id = result_row.iloc[0]['cluster']

            # Obter características da turma
            turma_data = {
                "turma_nome": turma_nome,
                "turma_id": int(turma_id),
                "cluster": int(cluster_id),
                "metrics": {
                    "total_leads": float(result_row.iloc[0]['total_leads']),
                    "taxa_conversao": float(result_row.iloc[0]['taxa_conversao']),
                    "volume_implementacoes": float(result_row.iloc[0]['volume_implementacoes']),
                    "total_registros": float(result_row.iloc[0]['total_registros']),
                    "receita_por_lead": float(result_row.iloc[0]['receita_por_lead'])
                }
            }

            # Adicionar características do cluster
            cluster_key = f"cluster_{cluster_id}"
            if cluster_key in self.cluster_stats['cluster_analysis']:
                turma_data["cluster_characteristics"] = self.cluster_stats['cluster_analysis'][cluster_key]['stats']

            return turma_data

        except Exception as e:
            logger.error(f"❌ Erro ao obter cluster da turma: {e}")
            return {"error": str(e)}

    def get_cluster_turmas(self, cluster_id: int) -> Dict[str, Any]:
        """Obter todas as turmas de um cluster específico"""
        if not self.model_loaded:
            if not self.load_clustering_data():
                return {"error": "Dados de clusterização não disponíveis"}

        try:
            # Filtrar turmas do cluster
            cluster_data = self.clustering_results[self.clustering_results['cluster'] == cluster_id]

            if cluster_data.empty:
                return {"error": f"Cluster {cluster_id} não encontrado"}

            # Mapear IDs para nomes
            turmas_list = []
            for _, row in cluster_data.iterrows():
                turma_id = row['turma_id']
                mapping_row = self.turma_mapping[self.turma_mapping['turma_id'] == turma_id]

                if not mapping_row.empty:
                    turma_nome = mapping_row.iloc[0]['turma_nome']
                    turmas_list.append({
                        "turma_nome": turma_nome,
                        "turma_id": int(turma_id),
                        "metrics": {
                            "total_leads": float(row['total_leads']),
                            "taxa_conversao": float(row['taxa_conversao']),
                            "volume_implementacoes": float(row['volume_implementacoes']),
                            "total_registros": float(row['total_registros']),
                            "receita_por_lead": float(row['receita_por_lead'])
                        }
                    })

            # Obter características do cluster
            cluster_key = f"cluster_{cluster_id}"
            cluster_characteristics = {}
            if cluster_key in self.cluster_stats['cluster_analysis']:
                cluster_characteristics = self.cluster_stats['cluster_analysis'][cluster_key]

            return {
                "cluster_id": cluster_id,
                "total_turmas": len(turmas_list),
                "characteristics": cluster_characteristics,
                "turmas": turmas_list
            }

        except Exception as e:
            logger.error(f"❌ Erro ao obter turmas do cluster: {e}")
            return {"error": str(e)}

    def get_clustering_insights(self) -> Dict[str, Any]:
        """Obter insights da análise de clusterização"""
        if not self.model_loaded:
            if not self.load_clustering_data():
                return {"error": "Dados de clusterização não disponíveis"}

        try:
            insights = {
                "model_quality": {
                    "silhouette_score": self.cluster_stats['metrics']['silhouette_score'],
                    "quality_assessment": self._assess_clustering_quality()
                },
                "business_insights": self._generate_business_insights(),
                "recommendations": self._generate_recommendations()
            }

            return insights

        except Exception as e:
            logger.error(f"❌ Erro ao gerar insights: {e}")
            return {"error": str(e)}

    def _assess_clustering_quality(self) -> str:
        """Avaliar qualidade da clusterização"""
        silhouette = self.cluster_stats['metrics']['silhouette_score']

        if silhouette >= 0.7:
            return "Excelente - Clusters bem definidos"
        elif silhouette >= 0.5:
            return "Boa - Clusters razoavelmente separados"
        elif silhouette >= 0.3:
            return "Moderada - Alguns clusters podem se sobrepor"
        else:
            return "Baixa - Clusters mal definidos"

    def _generate_business_insights(self) -> List[str]:
        """Gerar insights de negócio"""
        insights = []

        try:
            cluster_analysis = self.cluster_stats['cluster_analysis']

            # Analisar cluster 0 (alto desempenho)
            if 'cluster_0' in cluster_analysis:
                cluster_0 = cluster_analysis['cluster_0']
                insights.append(
                    f"Cluster 0 representa {cluster_0['percentage']:.1f}% das turmas "
                    f"com alta performance: {cluster_0['stats']['total_leads']:.1f} leads em média "
                    f"e taxa de conversão de {cluster_0['stats']['taxa_conversao']:.1f}%"
                )

            # Analisar cluster 1 (baixo desempenho)
            if 'cluster_1' in cluster_analysis:
                cluster_1 = cluster_analysis['cluster_1']
                insights.append(
                    f"Cluster 1 representa {cluster_1['percentage']:.1f}% das turmas "
                    f"com performance mais baixa: {cluster_1['stats']['total_leads']:.1f} leads em média "
                    f"e taxa de conversão de {cluster_1['stats']['taxa_conversao']:.1f}%"
                )

            return insights

        except Exception as e:
            logger.error(f"Erro ao gerar insights de negócio: {e}")
            return ["Erro ao gerar insights"]

    def _generate_recommendations(self) -> List[str]:
        """Gerar recomendações baseadas na clusterização"""
        recommendations = [
            "Focar estratégias de captação de leads nas turmas do Cluster 1",
            "Analisar práticas bem-sucedidas das turmas do Cluster 0",
            "Implementar ações específicas para melhorar taxa de conversão",
            "Monitorar regularmente a evolução dos clusters"
        ]

        return recommendations
