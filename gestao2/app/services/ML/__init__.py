"""
Machine Learning Services Package
Serviços especializados em Machine Learning para análise de dados e clustering
"""

# Importações principais dos serviços de ML
from .dynamic_clustering import DynamicClusteringService
from .turma_clustering_service import TurmaClusteringService

# Importações condicionais dos serviços avançados (se existirem)
try:
    from .advanced_clustering import AdvancedClusteringService
except ImportError:
    AdvancedClusteringService = None

try:
    from .clustering_dataset_service import ClusteringDatasetService
except ImportError:
    ClusteringDatasetService = None

try:
    from .data_preprocessing import DataPreprocessingService
except ImportError:
    DataPreprocessingService = None

try:
    from .final_clustering_service import FinalClusteringService
except ImportError:
    FinalClusteringService = None

try:
    from .ml_data_processor import MLDataProcessor
except ImportError:
    MLDataProcessor = None

__all__ = [
    'DynamicClusteringService',
    'TurmaClusteringService',
    'AdvancedClusteringService',
    'ClusteringDatasetService', 
    'DataPreprocessingService',
    'FinalClusteringService',
    'MLDataProcessor'
]
