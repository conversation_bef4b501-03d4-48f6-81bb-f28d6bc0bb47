"""
Clustering Dataset Service - Criação de dataset correto para clusterização
Implementação que segue as regras de negócio e trata dados vazios adequadamente
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Tuple
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class ClusteringDatasetService:
    """Serviço para criação de dataset de clusterização com regras de negócio"""

    def __init__(self: List[Any]) -> None:
        """Initialize the dataset service"""
        self.original_df = None
        self.clustering_dataset = None
        self.data_quality_report = {}
        self.id_mappings = {}  # Para armazenar mapeamentos de IDs

    def convert_brazilian_currency(self: List[Any], value: Any) -> Any:
        """Converter formato brasileiro R$399,90 para float"""
        if pd.isna(value):
            return 0.0
        if isinstance(value, (int, float)):
            return float(value)

        # Converter string
        str_value = str(value)
        # Remover R$, espaços e outros caracteres
        str_value = str_value.replace('R$', '').replace(' ', '').replace('.', '')
        # Trocar vírgula por ponto
        str_value = str_value.replace(',', '.')

        try:
            return float(str_value)
        except:
            return 0.0

    def load_or_create_id_mapping(self, column_name: str, values: pd.Series, mapping_file: str = None) -> Dict[str, int]:
        """Carregar ou criar mapeamento de IDs para uma coluna categórica"""
        try:
            # Tentar carregar mapeamento existente
            if mapping_file and pd.io.common.file_exists(mapping_file):
                logger.info(f"Carregando mapeamento existente para {column_name}: {mapping_file}")
                mapping_df = pd.read_csv(mapping_file)

                # Assumir que as colunas são: id, nome
                id_col = mapping_df.columns[0]
                name_col = mapping_df.columns[1]

                # Criar dicionário de mapeamento nome -> id
                mapping = dict(zip(mapping_df[name_col].astype(str), mapping_df[id_col]))
                logger.info(f"Mapeamento carregado: {len(mapping)} entradas")
                return mapping

            # Criar novo mapeamento
            logger.info(f"Criando novo mapeamento para {column_name}")
            unique_values = values.astype(str).unique()
            unique_values = [v for v in unique_values if v not in ['nan', 'None', 'NaT']]

            # Criar mapeamento nome -> id
            mapping = {str(value): idx + 1 for idx, value in enumerate(sorted(unique_values))}
            logger.info(f"Novo mapeamento criado: {len(mapping)} entradas")

            return mapping

        except Exception as e:
            logger.error(f"Erro ao criar mapeamento para {column_name}: {e}")
            return {}

    def convert_to_ids(self, df: pd.DataFrame) -> pd.DataFrame:
        """Converter colunas categóricas problemáticas para IDs numéricos"""
        try:
            logger.info("Convertendo colunas categóricas para IDs numéricos...")

            df_converted = df.copy()

            # 1. TURMA - usar mapeamento existente
            if 'Turma' in df_converted.columns:
                turma_mapping = self.load_or_create_id_mapping(
                    'Turma',
                    df_converted['Turma'],
                    'data/ML/datasets/turma_id_mapping.csv'
                )
                self.id_mappings['Turma'] = turma_mapping

                # Aplicar mapeamento
                df_converted['Turma_ID'] = df_converted['Turma'].astype(str).map(turma_mapping).fillna(0).astype(int)
                logger.info(f"Turma convertida: {df_converted['Turma_ID'].nunique()} IDs únicos")

            # 2. LEAD_ID - criar mapeamento
            if 'Lead_id' in df_converted.columns:
                # Converter para string primeiro
                df_converted['Lead_id'] = df_converted['Lead_id'].astype(str)
                df_converted['Lead_id'] = df_converted['Lead_id'].replace(['nan', 'None', 'NaT'], 'LEAD_VAZIO')

                lead_mapping = self.load_or_create_id_mapping('Lead_id', df_converted['Lead_id'])
                self.id_mappings['Lead_id'] = lead_mapping

                # Aplicar mapeamento
                df_converted['Lead_ID'] = df_converted['Lead_id'].map(lead_mapping).fillna(0).astype(int)
                logger.info(f"Lead_id convertido: {df_converted['Lead_ID'].nunique()} IDs únicos")

            # 3. STATUS_IMPLANTACAO - criar mapeamento
            if 'Status_Implantacao' in df_converted.columns:
                df_converted['Status_Implantacao'] = df_converted['Status_Implantacao'].astype(str)
                df_converted['Status_Implantacao'] = df_converted['Status_Implantacao'].replace(['nan', 'None'], 'SEM_STATUS')

                status_mapping = self.load_or_create_id_mapping('Status_Implantacao', df_converted['Status_Implantacao'])
                self.id_mappings['Status_Implantacao'] = status_mapping

                # Aplicar mapeamento
                df_converted['Status_ID'] = df_converted['Status_Implantacao'].map(status_mapping).fillna(0).astype(int)
                logger.info(f"Status_Implantacao convertido: {df_converted['Status_ID'].nunique()} IDs únicos")

            logger.info("Conversão para IDs concluída")
            return df_converted

        except Exception as e:
            logger.error(f"Erro na conversão para IDs: {e}")
            raise

    def analyze_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analisar qualidade dos dados antes do processamento"""
        try:
            logger.info("Analisando qualidade dos dados...")

            quality_report = {
                'total_records': len(df),
                'total_turmas': df['Turma'].nunique() if 'Turma' in df.columns else 0,
                'missing_data': {},
                'data_distribution': {},
                'value_analysis': {}
            }

            # Analisar dados faltantes nas colunas críticas
            critical_columns = ['Turma', 'Lead_id', 'Status_Implantacao', 'Valor Mensalidade']
            for col in critical_columns:
                if col in df.columns:
                    missing_count = df[col].isna().sum()
                    missing_pct = (missing_count / len(df)) * 100
                    quality_report['missing_data'][col] = {
                        'missing_count': int(missing_count),
                        'missing_percentage': float(missing_pct),
                        'valid_count': int(len(df) - missing_count)
                    }

            # Analisar distribuição de Status_Implantacao
            if 'Status_Implantacao' in df.columns:
                status_dist = df['Status_Implantacao'].value_counts().to_dict()
                quality_report['data_distribution']['status_implantacao'] = status_dist

                # Calcular taxa global de finalização
                total_with_status = df['Status_Implantacao'].notna().sum()
                finalizados = (df['Status_Implantacao'] == 'Finalizado').sum()
                global_conversion_rate = (finalizados / total_with_status * 100) if total_with_status > 0 else 0
                quality_report['data_distribution']['global_conversion_rate'] = float(global_conversion_rate)

            # Analisar valores monetários
            if 'Valor Mensalidade' in df.columns:
                df_temp = df.copy()
                df_temp['Valor_Numerico'] = df_temp['Valor Mensalidade'].apply(self.convert_brazilian_currency)

                unique_values = df_temp['Valor_Numerico'].unique()
                value_counts = df_temp['Valor_Numerico'].value_counts().to_dict()

                quality_report['value_analysis'] = {
                    'unique_values': [float(v) for v in sorted(unique_values)],
                    'value_distribution': {str(k): int(v) for k, v in value_counts.items()},
                    'min_value': float(df_temp['Valor_Numerico'].min()),
                    'max_value': float(df_temp['Valor_Numerico'].max()),
                    'mean_value': float(df_temp['Valor_Numerico'].mean())
                }

            logger.info(f"Análise de qualidade concluída: {quality_report['total_records']} registros, {quality_report['total_turmas']} turmas")
            return quality_report

        except Exception as e:
            logger.error(f"Erro na análise de qualidade: {e}")
            return {}

    def create_clustering_dataset(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Criar dataset para clusterização seguindo regras de negócio

        Args:
            df: DataFrame com dados brutos

        Returns:
            DataFrame agregado por turma com features de negócio
        """
        try:
            logger.info("Criando dataset de clusterização...")

            # Salvar dados originais
            self.original_df = df.copy()

            # Analisar qualidade dos dados
            self.data_quality_report = self.analyze_data_quality(df)

            # Verificar se temos a coluna Turma
            if 'Turma' not in df.columns:
                raise ValueError("Coluna 'Turma' não encontrada nos dados")

            # Converter valores monetários
            df['Valor_Numerico'] = df['Valor Mensalidade'].apply(self.convert_brazilian_currency) if 'Valor Mensalidade' in df.columns else 0

            # CONVERTER COLUNAS CATEGÓRICAS PARA IDs NUMÉRICOS
            df = self.convert_to_ids(df)

            logger.info("Dados convertidos para IDs numéricos")

            # Agregação por turma
            logger.info("Agregando dados por turma...")

            # 1. TOTAL_LEADS - Volume de leads únicos por turma (usando IDs numéricos)
            try:
                logger.info("Calculando total de leads por turma...")

                # Função para contar leads únicos usando IDs numéricos
                def count_unique_lead_ids(series):
                    try:
                        # Filtrar apenas valores > 0 (0 = LEAD_VAZIO)
                        valid_leads = series[series > 0]
                        return valid_leads.nunique() if len(valid_leads) > 0 else 0
                    except:
                        return 0

                leads_por_turma = df.groupby('Turma_ID').agg({
                    'Lead_ID': count_unique_lead_ids
                }).reset_index()
                leads_por_turma.columns = ['Turma_ID', 'total_leads']
                logger.info(f"Total de leads calculado para {len(leads_por_turma)} turmas")
            except Exception as e:
                logger.error(f"Erro no cálculo de leads: {e}")
                raise

            # 2. TAXA_CONVERSAO - Lead > Implantação Finalizada (regra de negócio)
            try:
                logger.info("Calculando taxa de conversão por turma...")

                # Função para contar implementações finalizadas usando IDs
                def count_finalized_by_id(series):
                    try:
                        # Assumir que 'Finalizado' tem um ID específico no mapeamento
                        finalizado_id = self.id_mappings.get('Status_Implantacao', {}).get('Finalizado', -1)
                        return (series == finalizado_id).sum() if finalizado_id != -1 else 0
                    except:
                        return 0

                conversao_por_turma = df.groupby('Turma_ID').agg({
                    'Lead_ID': count_unique_lead_ids,
                    'Status_ID': count_finalized_by_id
                }).reset_index()
                conversao_por_turma.columns = ['Turma_ID', 'leads_validos', 'implementacoes_finalizadas']
                conversao_por_turma['taxa_conversao'] = np.where(
                    conversao_por_turma['leads_validos'] > 0,
                    (conversao_por_turma['implementacoes_finalizadas'] / conversao_por_turma['leads_validos']) * 100,
                    0
                )
                logger.info(f"Taxa de conversão calculada para {len(conversao_por_turma)} turmas")
            except Exception as e:
                logger.error(f"Erro no cálculo de conversão: {e}")
                raise

            # 3. VOLUME_IMPLEMENTACOES - Número de implementações (usando IDs)
            try:
                logger.info("Calculando volume de implementações por turma...")

                def count_implementations_by_id(series):
                    try:
                        # Contar implementações válidas (> 0, pois 0 = SEM_STATUS)
                        valid_status = series[series > 0]
                        return len(valid_status) if len(valid_status) > 0 else 0
                    except:
                        return 0

                impl_por_turma = df.groupby('Turma_ID').agg({
                    'Status_ID': count_implementations_by_id
                }).reset_index()
                impl_por_turma.columns = ['Turma_ID', 'volume_implementacoes']
                logger.info(f"Volume de implementações calculado para {len(impl_por_turma)} turmas")
            except Exception as e:
                logger.error(f"Erro no cálculo de implementações: {e}")
                raise

            # Feature total_registros removida conforme solicitado

            # 5. RECEITA_POR_LEAD - Produtividade financeira
            try:
                logger.info("Calculando receita por lead por turma...")

                def sum_revenue(series):
                    try:
                        return series.sum()
                    except:
                        return 0

                receita_por_turma = df.groupby('Turma_ID').agg({
                    'Valor_Numerico': sum_revenue,
                    'Lead_ID': count_unique_lead_ids
                }).reset_index()
                receita_por_turma.columns = ['Turma_ID', 'receita_total', 'leads_para_receita']
                receita_por_turma['receita_por_lead'] = np.where(
                    receita_por_turma['leads_para_receita'] > 0,
                    receita_por_turma['receita_total'] / receita_por_turma['leads_para_receita'],
                    0
                )
                logger.info(f"Receita por lead calculada para {len(receita_por_turma)} turmas")
            except Exception as e:
                logger.error(f"Erro no cálculo de receita: {e}")
                raise

            # Combinar todas as métricas
            logger.info("Combinando métricas...")

            clustering_dataset = leads_por_turma.copy()

            # Merge com taxa de conversão
            clustering_dataset = clustering_dataset.merge(
                conversao_por_turma[['Turma_ID', 'taxa_conversao']],
                on='Turma_ID',
                how='left'
            )

            # Merge com volume de implementações
            clustering_dataset = clustering_dataset.merge(
                impl_por_turma,
                on='Turma_ID',
                how='left'
            )

            # Merge com receita por lead
            clustering_dataset = clustering_dataset.merge(
                receita_por_turma[['Turma_ID', 'receita_por_lead']],
                on='Turma_ID',
                how='left'
            )

            # Adicionar nomes das turmas de volta usando o mapeamento reverso
            if 'Turma' in self.id_mappings:
                # Criar mapeamento reverso (id -> nome)
                reverse_mapping = {v: k for k, v in self.id_mappings['Turma'].items()}
                clustering_dataset['Turma'] = clustering_dataset['Turma_ID'].map(reverse_mapping)
                logger.info("Nomes das turmas adicionados de volta ao dataset")

            # Preencher valores NaN com 0
            clustering_dataset = clustering_dataset.fillna(0)

            # Garantir que todos os valores são numéricos e finitos
            numeric_columns = ['total_leads', 'taxa_conversao', 'volume_implementacoes', 'receita_por_lead']
            for col in numeric_columns:
                if col in clustering_dataset.columns:
                    # Converter para numérico
                    clustering_dataset[col] = pd.to_numeric(clustering_dataset[col], errors='coerce').fillna(0)

                    # Remover infinitos
                    clustering_dataset[col] = clustering_dataset[col].replace([np.inf, -np.inf], 0)

                    # Garantir que é float
                    clustering_dataset[col] = clustering_dataset[col].astype(float)

            # Salvar dataset
            self.clustering_dataset = clustering_dataset

            logger.info(f"Dataset de clusterização criado: {len(clustering_dataset)} turmas, {len(numeric_columns)} features")

            return clustering_dataset

        except Exception as e:
            logger.error(f"Erro na criação do dataset: {e}")
            raise

    def validate_dataset(self, dataset: pd.DataFrame) -> Dict[str, Any]:
        """Validar o dataset criado"""
        try:
            logger.info("Validando dataset de clusterização...")

            validation_report = {
                'dataset_shape': dataset.shape,
                'columns': list(dataset.columns),
                'data_types': dataset.dtypes.to_dict(),
                'missing_values': dataset.isnull().sum().to_dict(),
                'statistics': {},
                'quality_checks': {}
            }

            # Estatísticas descritivas
            numeric_columns = ['total_leads', 'taxa_conversao', 'volume_implementacoes', 'receita_por_lead']
            for col in numeric_columns:
                if col in dataset.columns:
                    validation_report['statistics'][col] = {
                        'min': float(dataset[col].min()),
                        'max': float(dataset[col].max()),
                        'mean': float(dataset[col].mean()),
                        'std': float(dataset[col].std()),
                        'zeros': int((dataset[col] == 0).sum()),
                        'non_zeros': int((dataset[col] > 0).sum())
                    }

            # Verificações de qualidade
            validation_report['quality_checks'] = {
                'all_turmas_present': dataset['Turma'].notna().all(),
                'no_negative_values': (dataset[numeric_columns] >= 0).all().all(),
                'has_variation': all(dataset[col].std() > 0 for col in numeric_columns if col in dataset.columns),
                'reasonable_conversion_rates': (dataset['taxa_conversao'] <= 100).all() if 'taxa_conversao' in dataset.columns else True
            }

            logger.info(f"Validação concluída: {validation_report['dataset_shape'][0]} turmas, {validation_report['dataset_shape'][1]} colunas")

            return validation_report

        except Exception as e:
            logger.error(f"Erro na validação: {e}")
            return {}

    def save_dataset(self: List[Any], dataset: pd.DataFrame, filepath: str = 'data/ML/datasets/clustering_dataset_final.csv') -> bool:
        """Salvar dataset para arquivo"""
        try:
            dataset.to_csv(filepath, index=False, encoding='utf-8')
            logger.info(f"Dataset salvo em: {filepath}")
            return True
        except Exception as e:
            logger.error(f"Erro ao salvar dataset: {e}")
            return False

    def get_feature_descriptions(self) -> Dict[str, str]:
        """Obter descrições das features criadas"""
        return {
            'Turma': 'Identificador único da turma',
            'total_leads': 'Volume de leads únicos por turma (dados vazios tratados)',
            'taxa_conversao': 'Taxa de conversão de lead para implementação finalizada (%)',
            'volume_implementacoes': 'Número total de implementações por turma (dados vazios tratados)',
            'receita_por_lead': 'Receita total dividida pelo número de leads únicos (R$)'
        }

    def get_data_quality_report(self) -> Dict[str, Any]:
        """Obter relatório de qualidade dos dados"""
        return self.data_quality_report

    def get_clustering_features(self) -> List[str]:
        """Obter lista das features para clusterização (excluindo Turma)"""
        return ['total_leads', 'taxa_conversao', 'volume_implementacoes', 'receita_por_lead']
