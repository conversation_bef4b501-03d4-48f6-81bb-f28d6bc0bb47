"""
Amigo DataHub - Advanced Clustering Service
Professional ML-based clustering with multiple algorithms and evaluation metrics
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# ML Libraries
try:
    from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
    from sklearn.mixture import GaussianMixture
    from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
    from sklearn.decomposition import PCA
    from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
    from sklearn.manifold import TSNE
    import joblib
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

logger = logging.getLogger(__name__)

class AdvancedClusteringService:
    """Professional clustering service with multiple algorithms and evaluation"""

    def __init__(self: List[Any]) -> None:
        """Initialize the clustering service"""
        self.scalers = {
            'standard': StandardScaler(),
            'robust': RobustScaler(),
            'minmax': MinMaxScaler()
        }
        self.models = {}
        self.results = {}
        self.best_model = None
        self.best_score = -1

    def perform_comprehensive_clustering(self, df: pd.DataFrame, feature_cols: List[str],
                                       target_col: str = 'Turma') -> Dict[str, Any]:
        """
        Perform comprehensive clustering analysis with multiple algorithms

        Args:
            df: Dataframe with features
            feature_cols: List of feature column names
            target_col: Target column name

        Returns:
            Comprehensive clustering results
        """
        try:
            if not ML_AVAILABLE:
                return {"error": "ML libraries not available"}

            logger.info("Iniciando análise abrangente de clusterização")

            # Prepare data
            X = df[feature_cols].copy()

            # Handle target column - it might not exist in features dataframe
            target_values = None
            if target_col and target_col in df.columns:
                target_values = df[target_col].values
            else:
                # Create dummy target values for clustering
                target_values = np.arange(len(df))

            # Handle any remaining issues
            X = X.replace([np.inf, -np.inf], 0).fillna(0)

            if X.empty or len(X) < 3:
                return {"error": "Dados insuficientes para clusterização"}

            # Test different scaling methods
            scaling_results = {}
            for scaler_name, scaler in self.scalers.items():
                try:
                    X_scaled = scaler.fit_transform(X)
                    scaling_results[scaler_name] = {
                        'scaler': scaler,
                        'data': X_scaled,
                        'mean': np.mean(X_scaled),
                        'std': np.std(X_scaled)
                    }
                except Exception as e:
                    logger.warning(f"Erro no scaler {scaler_name}: {e}")
                    continue

            if not scaling_results:
                return {"error": "Erro em todos os métodos de escalonamento. Verifique se os dados contêm apenas valores numéricos."}

            # Test different clustering algorithms
            clustering_results = {}

            for scaler_name, scale_info in scaling_results.items():
                X_scaled = scale_info['data']

                # Test different numbers of clusters
                for n_clusters in range(2, min(8, len(X))):

                    # K-Means
                    try:
                        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                        kmeans_labels = kmeans.fit_predict(X_scaled)

                        if len(set(kmeans_labels)) > 1:  # More than one cluster
                            silhouette = silhouette_score(X_scaled, kmeans_labels)
                            calinski = calinski_harabasz_score(X_scaled, kmeans_labels)
                            davies = davies_bouldin_score(X_scaled, kmeans_labels)

                            clustering_results[f'kmeans_{scaler_name}_{n_clusters}'] = {
                                'algorithm': 'KMeans',
                                'scaler': scaler_name,
                                'n_clusters': n_clusters,
                                'model': kmeans,
                                'labels': kmeans_labels,
                                'silhouette_score': silhouette,
                                'calinski_harabasz_score': calinski,
                                'davies_bouldin_score': davies,
                                'composite_score': silhouette * 0.4 + (calinski / 1000) * 0.3 + (1 / davies) * 0.3
                            }
                    except Exception as e:
                        error_msg = str(e) if e else "Erro desconhecido"
                        logger.warning(f"Erro no KMeans {scaler_name} {n_clusters}: {error_msg}")

                    # Gaussian Mixture
                    try:
                        gmm = GaussianMixture(n_components=n_clusters, random_state=42)
                        gmm_labels = gmm.fit_predict(X_scaled)

                        if len(set(gmm_labels)) > 1:
                            silhouette = silhouette_score(X_scaled, gmm_labels)
                            calinski = calinski_harabasz_score(X_scaled, gmm_labels)
                            davies = davies_bouldin_score(X_scaled, gmm_labels)

                            clustering_results[f'gmm_{scaler_name}_{n_clusters}'] = {
                                'algorithm': 'GaussianMixture',
                                'scaler': scaler_name,
                                'n_clusters': n_clusters,
                                'model': gmm,
                                'labels': gmm_labels,
                                'silhouette_score': silhouette,
                                'calinski_harabasz_score': calinski,
                                'davies_bouldin_score': davies,
                                'composite_score': silhouette * 0.4 + (calinski / 1000) * 0.3 + (1 / davies) * 0.3
                            }
                    except Exception as e:
                        error_msg = str(e) if e else "Erro desconhecido"
                        logger.warning(f"Erro no GMM {scaler_name} {n_clusters}: {error_msg}")

                # DBSCAN (parameter search)
                try:
                    for eps in [0.3, 0.5, 0.7, 1.0]:
                        for min_samples in [3, 5, 7]:
                            dbscan = DBSCAN(eps=eps, min_samples=min_samples)
                            dbscan_labels = dbscan.fit_predict(X_scaled)

                            n_clusters_db = len(set(dbscan_labels)) - (1 if -1 in dbscan_labels else 0)

                            if n_clusters_db > 1:
                                # Remove noise points for scoring
                                mask = dbscan_labels != -1
                                if np.sum(mask) > 1:
                                    silhouette = silhouette_score(X_scaled[mask], dbscan_labels[mask])
                                    calinski = calinski_harabasz_score(X_scaled[mask], dbscan_labels[mask])
                                    davies = davies_bouldin_score(X_scaled[mask], dbscan_labels[mask])

                                    clustering_results[f'dbscan_{scaler_name}_{eps}_{min_samples}'] = {
                                        'algorithm': 'DBSCAN',
                                        'scaler': scaler_name,
                                        'eps': eps,
                                        'min_samples': min_samples,
                                        'n_clusters': n_clusters_db,
                                        'model': dbscan,
                                        'labels': dbscan_labels,
                                        'silhouette_score': silhouette,
                                        'calinski_harabasz_score': calinski,
                                        'davies_bouldin_score': davies,
                                        'composite_score': silhouette * 0.4 + (calinski / 1000) * 0.3 + (1 / davies) * 0.3,
                                        'noise_points': np.sum(dbscan_labels == -1)
                                    }
                except Exception as e:
                    error_msg = str(e) if e else "Erro desconhecido"
                    logger.warning(f"Erro no DBSCAN {scaler_name}: {error_msg}")

            if not clustering_results:
                return {"error": "Nenhum algoritmo de clusterização funcionou. Dados podem ser insuficientes ou inadequados para clusterização."}

            # Find best model
            best_key = max(clustering_results.keys(), key=lambda k: clustering_results[k]['composite_score'])
            best_result = clustering_results[best_key]

            # Generate comprehensive analysis
            analysis = self._generate_cluster_analysis(df, best_result, feature_cols, target_col)

            # Prepare results summary
            results_summary = {
                'best_model': {
                    'algorithm': best_result['algorithm'],
                    'scaler': best_result['scaler'],
                    'n_clusters': best_result.get('n_clusters', len(set(best_result['labels']))),
                    'silhouette_score': best_result['silhouette_score'],
                    'composite_score': best_result['composite_score']
                },
                'all_results': {k: {
                    'algorithm': v['algorithm'],
                    'scaler': v['scaler'],
                    'n_clusters': v.get('n_clusters', len(set(v['labels']))),
                    'silhouette_score': v['silhouette_score'],
                    'composite_score': v['composite_score']
                } for k, v in clustering_results.items()},
                'cluster_analysis': analysis,
                'feature_importance': self._calculate_feature_importance(X, best_result['labels'], feature_cols),
                'recommendations': self._generate_recommendations(analysis)
            }

            # Store results
            self.results = results_summary
            self.best_model = best_result

            return results_summary

        except Exception as e:
            logger.error(f"Erro na clusterização abrangente: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"error": str(e)}

    def _generate_cluster_analysis(self, df: pd.DataFrame, best_result: Dict,
                                 feature_cols: List[str], target_col: str) -> Dict[str, Any]:
        """Generate detailed cluster analysis"""
        try:
            labels = best_result['labels']

            # Add cluster labels to dataframe
            df_analysis = df.copy()
            df_analysis['Cluster'] = labels

            # Analyze each cluster
            cluster_profiles = []
            unique_clusters = sorted([c for c in set(labels) if c != -1])  # Exclude noise for DBSCAN

            for cluster_id in unique_clusters:
                cluster_data = df_analysis[df_analysis['Cluster'] == cluster_id]

                profile = {
                    'cluster_id': cluster_id,
                    'size': len(cluster_data),
                    'percentage': (len(cluster_data) / len(df_analysis)) * 100,
                    'top_turmas': cluster_data[target_col].value_counts().head(5).to_dict(),
                    'metrics': {}
                }

                # Calculate cluster metrics
                for col in feature_cols:
                    if col in cluster_data.columns:
                        profile['metrics'][col] = {
                            'mean': float(cluster_data[col].mean()),
                            'median': float(cluster_data[col].median()),
                            'std': float(cluster_data[col].std()) if cluster_data[col].std() is not None else 0
                        }

                cluster_profiles.append(profile)

            return {
                'cluster_profiles': cluster_profiles,
                'total_clusters': len(unique_clusters),
                'noise_points': int(np.sum(labels == -1)) if -1 in labels else 0
            }

        except Exception as e:
            logger.error(f"Erro na análise de clusters: {e}")
            return {}

    def _calculate_feature_importance(self, X: pd.DataFrame, labels: np.ndarray,
                                    feature_cols: List[str]) -> Dict[str, float]:
        """Calculate feature importance for clustering"""
        try:
            from sklearn.ensemble import RandomForestClassifier

            # Remove noise points
            mask = labels != -1
            X_clean = X[mask]
            labels_clean = labels[mask]

            if len(set(labels_clean)) < 2:
                return {}

            # Train a classifier to predict clusters
            rf = RandomForestClassifier(n_estimators=100, random_state=42)
            rf.fit(X_clean, labels_clean)

            # Get feature importance
            importance_dict = dict(zip(feature_cols, rf.feature_importances_))

            # Sort by importance
            sorted_importance = dict(sorted(importance_dict.items(),
                                          key=lambda x: x[1], reverse=True))

            return sorted_importance

        except Exception as e:
            logger.warning(f"Erro no cálculo de importância: {e}")
            return {}

    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations based on clustering results"""
        recommendations = []

        try:
            cluster_profiles = analysis.get('cluster_profiles', [])

            if not cluster_profiles:
                return ["Não foi possível gerar recomendações devido à falta de dados de cluster."]

            # Analyze cluster sizes
            sizes = [profile['size'] for profile in cluster_profiles]
            avg_size = np.mean(sizes)

            # Find largest and smallest clusters
            largest_cluster = max(cluster_profiles, key=lambda x: x['size'])
            smallest_cluster = min(cluster_profiles, key=lambda x: x['size'])

            recommendations.append(f"Identificados {len(cluster_profiles)} clusters distintos de turmas.")

            if largest_cluster['size'] > avg_size * 1.5:
                recommendations.append(f"Cluster {largest_cluster['cluster_id']} é dominante com {largest_cluster['size']} turmas ({largest_cluster['percentage']:.1f}%). Considere estratégias específicas para este grupo.")

            if smallest_cluster['size'] < avg_size * 0.5:
                recommendations.append(f"Cluster {smallest_cluster['cluster_id']} é pequeno com apenas {smallest_cluster['size']} turmas. Pode representar um nicho específico.")

            # Analyze performance metrics if available
            for profile in cluster_profiles:
                metrics = profile.get('metrics', {})
                if 'Taxa_Conversao' in metrics:
                    conversion = metrics['Taxa_Conversao']['mean']
                    if conversion > 50:
                        recommendations.append(f"Cluster {profile['cluster_id']} tem alta taxa de conversão ({conversion:.1f}%). Replique estratégias deste grupo.")
                    elif conversion < 20:
                        recommendations.append(f"Cluster {profile['cluster_id']} tem baixa taxa de conversão ({conversion:.1f}%). Necessita intervenção urgente.")

            recommendations.append("Use estes insights para personalizar estratégias de marketing e vendas por cluster.")

        except Exception as e:
            logger.warning(f"Erro na geração de recomendações: {e}")
            recommendations.append("Erro na geração de recomendações automáticas.")

        return recommendations

    def get_cluster_summary_for_visualization(self) -> Dict[str, Any]:
        """Get cluster summary optimized for visualization"""
        if not self.results:
            return {}

        try:
            cluster_profiles = self.results.get('cluster_analysis', {}).get('cluster_profiles', [])

            # Prepare data for charts
            cluster_sizes = [profile['size'] for profile in cluster_profiles]
            cluster_labels = [f"Cluster {profile['cluster_id']}" for profile in cluster_profiles]

            return {
                'cluster_sizes': cluster_sizes,
                'cluster_labels': cluster_labels,
                'total_clusters': len(cluster_profiles),
                'best_algorithm': self.results.get('best_model', {}).get('algorithm', 'Unknown'),
                'silhouette_score': self.results.get('best_model', {}).get('silhouette_score', 0)
            }

        except Exception as e:
            logger.error(f"Erro na preparação para visualização: {e}")
            return {}
