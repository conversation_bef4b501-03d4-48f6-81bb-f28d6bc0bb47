"""
Amigo DataHub - Dynamic Clustering Service
Professional ML-based clustering for turma analysis with advanced preprocessing
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# Import professional services
from .data_preprocessing import DataPreprocessingService
from .advanced_clustering import AdvancedClusteringService

logger = logging.getLogger(__name__)


class DynamicClusteringService:
    """Professional service for turma clustering with advanced ML techniques"""

    def __init__(self: List[Any], data_loader: Dict[str, Any]) -> None:
        self.data_loader = data_loader
        self.df = None
        self.preprocessor = DataPreprocessingService()
        self.clustering_service = AdvancedClusteringService()
        self.results = {}

    def _prepare_data(self) -> pd.DataFrame:
        """Prepara dados focando apenas em turmas e métricas comerciais"""
        try:
            logger.info("Preparando dados para segmentação de turmas")
            self.df = self.data_loader.get_data().copy()

            logger.info(f"Dados carregados: {len(self.df)} registros, {len(self.df.columns)} colunas")
            logger.info(f"Colunas disponíveis: {list(self.df.columns)}")

            # INVESTIGAR TIPOS DE DADOS EM TODAS AS COLUNAS
            logger.info("=== INVESTIGAÇÃO DE TIPOS DE DADOS ===")
            for col in self.df.columns:
                col_dtype = self.df[col].dtype
                sample_values = self.df[col].dropna().head(5).tolist()
                unique_types = set(type(x).__name__ for x in self.df[col].dropna().head(10))

                logger.info(f"Coluna '{col}':")
                logger.info(f"  Tipo pandas: {col_dtype}")
                logger.info(f"  Tipos Python únicos: {unique_types}")
                logger.info(f"  Amostra de valores: {sample_values}")

                # Verificar se há mistura de tipos problemática
                if len(unique_types) > 1:
                    logger.warning(f"  ATENÇÃO: Coluna '{col}' tem múltiplos tipos: {unique_types}")

                # Verificar especificamente por datetime
                if any('datetime' in t.lower() or 'timestamp' in t.lower() for t in unique_types):
                    logger.warning(f"  DATETIME DETECTADO em '{col}': {unique_types}")

            # Filtrar apenas registros com turma
            initial_count = len(self.df)
            self.df = self.df.dropna(subset=['Turma'])
            logger.info(f"Dados filtrados: {len(self.df)} registros com turma (removidos: {initial_count - len(self.df)})")

            # REMOVER COMPLETAMENTE TODAS AS COLUNAS DE DATA PARA EVITAR CONFLITOS
            date_columns_to_remove = [
                'Data_criacao_Oportunidade', 'Data_criacao_lead', 'Data_Criacao_Implantacao',
                'Data_Finalizacao', 'Data_Mudanca_Estagio', 'DataMarco1 - Co. Hero',
                'DateMarco2 - Constituído', 'Marco 3 - Homologado', 'Marco 4 - Liberação',
                'ActualStartDate', 'DataPrevistaDeFinalização', 'Data da colacao'
            ]

            columns_removed = []
            for col in date_columns_to_remove:
                if col in self.df.columns:
                    self.df = self.df.drop(columns=[col])
                    columns_removed.append(col)
                    logger.info(f"Removida coluna de data: {col}")

            if columns_removed:
                logger.info(f"Total de colunas de data removidas: {len(columns_removed)}")

            # Converter valores monetários
            try:
                from app.utils.formatters import safe_convert_to_float
                if 'Valor Mensalidade' in self.df.columns:
                    logger.info("Convertendo 'Valor Mensalidade' usando safe_convert_to_float")
                    self.df['Valor_Float'] = self.df['Valor Mensalidade'].apply(safe_convert_to_float)
                else:
                    self.df['Valor_Float'] = 0.0
            except ImportError:
                logger.warning("Função safe_convert_to_float não encontrada, usando conversão padrão")
                self.df['Valor_Float'] = pd.to_numeric(self.df.get('Valor Mensalidade', 0), errors='coerce').fillna(0.0)

            # VERIFICAÇÃO FINAL DE TIPOS
            logger.info("=== VERIFICAÇÃO FINAL DE TIPOS ===")
            for col in self.df.columns:
                col_dtype = self.df[col].dtype
                unique_types = set(type(x).__name__ for x in self.df[col].dropna().head(10))
                logger.info(f"Coluna '{col}': {col_dtype} | Tipos Python: {unique_types}")

                # Se ainda há datetime, forçar conversão
                if any('datetime' in t.lower() or 'timestamp' in t.lower() for t in unique_types):
                    logger.error(f"AINDA HÁ DATETIME EM '{col}'! Forçando conversão para string...")
                    self.df[col] = self.df[col].astype(str)

            logger.info(f"Dados preparados: {len(self.df)} registros, {len(self.df.columns)} colunas")
            return self.df

        except Exception as e:
            logger.error(f"Erro ao preparar dados: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return pd.DataFrame()

    def perform_professional_clustering(self, target_column: str = 'Turma') -> Dict[str, Any]:
        """
        Perform professional clustering analysis using advanced preprocessing and ML

        Args:
            target_column: Column to group by for clustering analysis

        Returns:
            Comprehensive clustering results
        """
        try:
            logger.info("Iniciando análise profissional de clusterização")

            # Load and validate data
            raw_data = self.data_loader.get_data()
            if raw_data is None or raw_data.empty:
                return {"error": "Dados não disponíveis"}

            logger.info(f"Dados carregados: {len(raw_data)} registros, {len(raw_data.columns)} colunas")

            # Check if target column exists
            if target_column not in raw_data.columns:
                return {"error": f"Coluna '{target_column}' não encontrada nos dados"}

            # Professional data preprocessing
            logger.info("Aplicando pré-processamento profissional dos dados")
            cleaned_data = self.preprocessor.clean_and_prepare_data(raw_data)

            if cleaned_data.empty:
                return {"error": "Nenhum dado válido após pré-processamento"}

            # Prepare features for clustering
            logger.info("Preparando features para clusterização")
            features_df, feature_cols = self.preprocessor.prepare_features_for_clustering(
                cleaned_data, target_column
            )

            if features_df.empty or not feature_cols:
                return {"error": "Nenhuma feature válida para clusterização"}

            logger.info(f"Features preparadas: {len(feature_cols)} features para {len(features_df)} grupos")

            # Perform comprehensive clustering
            logger.info("Executando análise abrangente de clusterização")
            clustering_results = self.clustering_service.perform_comprehensive_clustering(
                features_df, feature_cols, target_column
            )

            if 'error' in clustering_results:
                return clustering_results

            # Store results
            self.results = clustering_results

            # Add preprocessing summary
            preprocessing_summary = self.preprocessor.get_preprocessing_summary()
            clustering_results['preprocessing_summary'] = preprocessing_summary

            # Add data quality metrics
            clustering_results['data_quality'] = {
                'original_records': len(raw_data),
                'processed_records': len(cleaned_data),
                'groups_analyzed': len(features_df),
                'features_used': len(feature_cols),
                'data_completeness': (len(cleaned_data) / len(raw_data)) * 100
            }

            logger.info("Análise de clusterização concluída com sucesso")
            return clustering_results

        except Exception as e:
            logger.error(f"Erro na análise de clusterização: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"error": str(e)}

    def get_clustering_options(self) -> Dict[str, Dict]:
        """Retorna opções de segmentação focadas em turmas"""
        if self.df is None or self.df.empty:
            return {}

        options = {
            'taxa_conversao': {
                'name': 'Taxa de Conversão por Turma',
                'description': 'Segmentação por taxa de conversão lead→oportunidade→implementação',
                'available': all(col in self.df.columns for col in ['Turma', 'Nome do Lead'])
            },
            'valor_total_vendido': {
                'name': 'Valor Total Vendido por Turma',
                'description': 'Segmentação por valor total de vendas finalizadas',
                'available': 'Turma' in self.df.columns
            },
            'numero_leads': {
                'name': 'Número de Leads por Turma',
                'description': 'Segmentação por quantidade de leads gerados',
                'available': all(col in self.df.columns for col in ['Turma', 'Nome do Lead'])
            },
            'numero_implementacoes': {
                'name': 'Número de Implementações por Turma',
                'description': 'Segmentação por quantidade de implementações finalizadas',
                'available': all(col in self.df.columns for col in ['Turma', 'Status_Implantacao'])
            },
            'taxa_cancelamento': {
                'name': 'Taxa de Cancelamento por Turma',
                'description': 'Segmentação por taxa de cancelamento',
                'available': all(col in self.df.columns for col in ['Turma', 'Status_Implantacao'])
            },
            'performance_cupom': {
                'name': 'Performance com Cupom por Turma',
                'description': 'Segmentação por conversão com uso de cupons/descontos',
                'available': any(col in self.df.columns for col in ['Isenção em Meses', 'Desconto'])
            }
        }

        return {k: v for k, v in options.items() if v['available']}

    def perform_clustering(self, clustering_type: str, n_clusters: int = 5) -> Dict[str, Any]:
        """Executa segmentação baseada no tipo especificado"""
        try:
            logger.info(f"Iniciando segmentação: {clustering_type}")

            if self.df is None or self.df.empty:
                return {"error": "Dados não disponíveis"}

            options = self.get_clustering_options()
            if clustering_type not in options:
                return {"error": f"Tipo de segmentação '{clustering_type}' não disponível"}

            # Executar segmentação específica
            if clustering_type == 'taxa_conversao':
                return self._segment_by_conversion_rate(n_clusters)
            elif clustering_type == 'valor_total_vendido':
                return self._segment_by_total_sales(n_clusters)
            elif clustering_type == 'numero_leads':
                return self._segment_by_lead_count(n_clusters)
            elif clustering_type == 'numero_implementacoes':
                return self._segment_by_implementation_count(n_clusters)
            elif clustering_type == 'taxa_cancelamento':
                return self._segment_by_cancellation_rate(n_clusters)
            elif clustering_type == 'performance_cupom':
                return self._segment_by_coupon_performance(n_clusters)

            return {"error": "Tipo de segmentação não implementado"}

        except Exception as e:
            logger.error(f"Erro na segmentação: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"error": str(e)}

    def _segment_by_conversion_rate(self, n_clusters: int) -> Dict[str, Any]:
        """Segmentação por taxa de conversão"""
        try:
            # Limpar dados antes do groupby para evitar erro de tipos
            df_clean = self.df.copy()

            # Garantir que Turma é string
            df_clean['Turma'] = df_clean['Turma'].astype(str)

            # Remover valores problemáticos
            df_clean = df_clean[df_clean['Turma'].notna()]
            df_clean = df_clean[df_clean['Turma'] != 'nan']
            df_clean = df_clean[df_clean['Turma'] != '']

            # Calcular métricas por turma
            turma_metrics = df_clean.groupby('Turma').agg({
                'Nome do Lead': 'nunique',
                'Oportunidade_id': 'nunique' if 'Oportunidade_id' in df_clean.columns else lambda x: 0,
                'Status_Implantacao': lambda x: (x == 'Finalizado').sum() if 'Status_Implantacao' in df_clean.columns else 0
            }).reset_index()

            turma_metrics.columns = ['Turma', 'total_leads', 'total_oportunidades', 'implementacoes_finalizadas']

            # Calcular taxa de conversão
            turma_metrics['taxa_conversao'] = (
                turma_metrics['implementacoes_finalizadas'] / turma_metrics['total_leads'] * 100
            ).fillna(0)

            return self._create_segments(turma_metrics, 'taxa_conversao', n_clusters, 'Taxa de Conversão')

        except Exception as e:
            logger.error(f"Erro na segmentação por taxa de conversão: {e}")
            return {"error": str(e)}

    def _segment_by_total_sales(self, n_clusters: int) -> Dict[str, Any]:
        """Segmentação por valor total vendido"""
        try:
            # Limpar dados antes do groupby
            df_clean = self.df.copy()
            df_clean['Turma'] = df_clean['Turma'].astype(str)
            df_clean = df_clean[df_clean['Turma'].notna()]
            df_clean = df_clean[df_clean['Turma'] != 'nan']
            df_clean = df_clean[df_clean['Turma'] != '']

            # Calcular valor total por turma
            turma_metrics = df_clean.groupby('Turma').agg({
                'Valor_Float': 'sum',
                'Nome do Lead': 'nunique',
                'Status_Implantacao': lambda x: (x == 'Finalizado').sum() if 'Status_Implantacao' in df_clean.columns else 0
            }).reset_index()

            turma_metrics.columns = ['Turma', 'valor_total', 'total_leads', 'implementacoes_finalizadas']

            return self._create_segments(turma_metrics, 'valor_total', n_clusters, 'Valor Total Vendido')

        except Exception as e:
            logger.error(f"Erro na segmentação por valor total: {e}")
            return {"error": str(e)}

    def _segment_by_lead_count(self, n_clusters: int) -> Dict[str, Any]:
        """Segmentação por número de leads"""
        try:
            # Limpar dados antes do groupby
            df_clean = self.df.copy()
            df_clean['Turma'] = df_clean['Turma'].astype(str)
            df_clean = df_clean[df_clean['Turma'].notna()]
            df_clean = df_clean[df_clean['Turma'] != 'nan']
            df_clean = df_clean[df_clean['Turma'] != '']

            turma_metrics = df_clean.groupby('Turma').agg({
                'Nome do Lead': 'nunique',
                'Valor_Float': 'sum'
            }).reset_index()

            turma_metrics.columns = ['Turma', 'total_leads', 'valor_total']

            return self._create_segments(turma_metrics, 'total_leads', n_clusters, 'Número de Leads')

        except Exception as e:
            logger.error(f"Erro na segmentação por número de leads: {e}")
            return {"error": str(e)}

    def _segment_by_implementation_count(self, n_clusters: int) -> Dict[str, Any]:
        """Segmentação por número de implementações"""
        try:
            if 'Status_Implantacao' not in self.df.columns:
                return {"error": "Coluna Status_Implantacao não encontrada"}

            # Limpar dados antes do groupby
            df_clean = self.df.copy()
            df_clean['Turma'] = df_clean['Turma'].astype(str)
            df_clean = df_clean[df_clean['Turma'].notna()]
            df_clean = df_clean[df_clean['Turma'] != 'nan']
            df_clean = df_clean[df_clean['Turma'] != '']

            turma_metrics = df_clean.groupby('Turma').agg({
                'Status_Implantacao': lambda x: (x == 'Finalizado').sum(),
                'Nome do Lead': 'nunique',
                'Valor_Float': 'sum'
            }).reset_index()

            turma_metrics.columns = ['Turma', 'implementacoes_finalizadas', 'total_leads', 'valor_total']

            return self._create_segments(turma_metrics, 'implementacoes_finalizadas', n_clusters, 'Implementações Finalizadas')

        except Exception as e:
            logger.error(f"Erro na segmentação por implementações: {e}")
            return {"error": str(e)}

    def _segment_by_cancellation_rate(self, n_clusters: int) -> Dict[str, Any]:
        """Segmentação por taxa de cancelamento"""
        try:
            if 'Status_Implantacao' not in self.df.columns:
                return {"error": "Coluna Status_Implantacao não encontrada"}

            # Limpar dados antes do groupby
            df_clean = self.df.copy()
            df_clean['Turma'] = df_clean['Turma'].astype(str)
            df_clean = df_clean[df_clean['Turma'].notna()]
            df_clean = df_clean[df_clean['Turma'] != 'nan']
            df_clean = df_clean[df_clean['Turma'] != '']

            turma_metrics = df_clean.groupby('Turma').agg({
                'Status_Implantacao': [
                    lambda x: (x == 'Cancelado').sum(),
                    'count'
                ],
                'Nome do Lead': 'nunique'
            }).reset_index()

            turma_metrics.columns = ['Turma', 'cancelamentos', 'total_implementacoes', 'total_leads']

            # Calcular taxa de cancelamento
            turma_metrics['taxa_cancelamento'] = (
                turma_metrics['cancelamentos'] / turma_metrics['total_implementacoes'] * 100
            ).fillna(0)

            return self._create_segments(turma_metrics, 'taxa_cancelamento', n_clusters, 'Taxa de Cancelamento')

        except Exception as e:
            logger.error(f"Erro na segmentação por taxa de cancelamento: {e}")
            return {"error": str(e)}

    def _segment_by_coupon_performance(self, n_clusters: int) -> Dict[str, Any]:
        """Segmentação por performance com cupom"""
        try:
            # Limpar dados antes do groupby
            df_clean = self.df.copy()
            df_clean['Turma'] = df_clean['Turma'].astype(str)
            df_clean = df_clean[df_clean['Turma'].notna()]
            df_clean = df_clean[df_clean['Turma'] != 'nan']
            df_clean = df_clean[df_clean['Turma'] != '']

            # Identificar registros com cupom/desconto
            cupom_cols = ['Isenção em Meses', 'Desconto', 'Cupom']
            has_cupom = pd.Series(False, index=df_clean.index)

            for col in cupom_cols:
                if col in df_clean.columns:
                    if col == 'Isenção em Meses':
                        has_cupom |= (pd.to_numeric(df_clean[col], errors='coerce').fillna(0) > 0)
                    else:
                        has_cupom |= (df_clean[col].notna() & (df_clean[col] != '') & (df_clean[col] != 0))

            df_clean['tem_cupom'] = has_cupom

            turma_metrics = df_clean.groupby('Turma').agg({
                'tem_cupom': 'sum',
                'Nome do Lead': 'nunique',
                'Status_Implantacao': lambda x: (x == 'Finalizado').sum() if 'Status_Implantacao' in df_clean.columns else 0
            }).reset_index()

            turma_metrics.columns = ['Turma', 'leads_com_cupom', 'total_leads', 'implementacoes_finalizadas']

            # Calcular taxa de conversão com cupom
            turma_metrics['taxa_cupom'] = (
                turma_metrics['leads_com_cupom'] / turma_metrics['total_leads'] * 100
            ).fillna(0)

            return self._create_segments(turma_metrics, 'taxa_cupom', n_clusters, 'Performance com Cupom')

        except Exception as e:
            logger.error(f"Erro na segmentação por performance com cupom: {e}")
            return {"error": str(e)}

    def _create_segments(self, data: pd.DataFrame, metric_col: str, n_clusters: int, metric_name: str) -> Dict[str, Any]:
        """Cria segmentos baseados em uma métrica específica"""
        try:
            if len(data) == 0:
                return {"error": "Nenhum dado disponível para segmentação"}

            # Garantir que a métrica é numérica
            data[metric_col] = pd.to_numeric(data[metric_col], errors='coerce').fillna(0)

            # Criar faixas baseadas em percentis
            values = data[metric_col].values
            if len(values) == 0 or values.max() == 0:
                return {"error": f"Nenhum valor válido encontrado para {metric_name}"}

            percentis = np.linspace(0, 100, n_clusters + 1)
            faixas = np.percentile(values, percentis)

            clusters = []
            for i in range(n_clusters):
                min_val = float(faixas[i])
                max_val = float(faixas[i + 1])

                if i == n_clusters - 1:
                    mask = (data[metric_col] >= min_val) & (data[metric_col] <= max_val)
                else:
                    mask = (data[metric_col] >= min_val) & (data[metric_col] < max_val)

                cluster_data = data[mask]

                clusters.append({
                    'cluster_id': i,
                    'name': f'{metric_name} {i+1}: {min_val:.2f} - {max_val:.2f}',
                    'min_value': min_val,
                    'max_value': max_val,
                    'size': len(cluster_data),
                    'turmas': cluster_data['Turma'].tolist(),
                    'avg_metric': cluster_data[metric_col].mean(),
                    'total_metric': cluster_data[metric_col].sum()
                })

            return {
                'clustering_type': metric_col,
                'metric_name': metric_name,
                'n_clusters': n_clusters,
                'clusters': clusters,
                'summary': {
                    'total_turmas': len(data),
                    'avg_metric': values.mean(),
                    'min_metric': values.min(),
                    'max_metric': values.max()
                }
            }

        except Exception as e:
            logger.error(f"Erro ao criar segmentos: {e}")
            return {"error": str(e)}

    def get_cluster_analysis(self, clustering_result: Dict[str, Any]) -> Dict[str, Any]:
        """Análise dos clusters gerados"""
        try:
            if 'error' in clustering_result:
                return {}

            clusters = clustering_result.get('clusters', [])
            if not clusters:
                return {}

            analysis = {
                'insights': [],
                'recommendations': []
            }

            # Análise básica
            total_turmas = sum(c.get('size', 0) for c in clusters)

            for i, cluster in enumerate(clusters):
                size = cluster.get('size', 0)
                percentage = (size / total_turmas * 100) if total_turmas > 0 else 0

                if percentage > 40:
                    analysis['insights'].append(f"Cluster {i+1} concentra {percentage:.1f}% das turmas")
                elif percentage < 10:
                    analysis['insights'].append(f"Cluster {i+1} tem apenas {percentage:.1f}% das turmas")

            return analysis

        except Exception as e:
            logger.error(f"Erro na análise: {e}")
            return {}

    def perform_professional_clustering(self, target_column: str = 'Turma') -> Dict[str, Any]:
        """
        Perform professional clustering with advanced ML techniques

        Args:
            target_column: Column to group by for clustering analysis

        Returns:
            Comprehensive clustering results
        """
        try:
            logger.info(f"Iniciando clusterização profissional por {target_column}")

            # Get data
            df = self.data_loader.get_data()
            if df.empty:
                return {"error": "Dados não disponíveis"}

            # Use ML data processor for robust data preparation
            from .ml_data_processor import MLDataProcessor
            ml_processor = MLDataProcessor()

            # Process data for ML (convert all text to numbers)
            try:
                ml_ready_df = ml_processor.process_for_ml(df)
                logger.info(f"Dados processados para ML: {ml_ready_df.shape}")
            except Exception as e:
                logger.error(f"Erro no processamento ML: {e}")
                return {"error": f"Erro na preparação dos dados: {str(e)}"}

            # Prepare features for clustering
            try:
                features_df, feature_cols = ml_processor.prepare_for_clustering(df, target_column)
                logger.info(f"Features preparadas: {len(feature_cols)} colunas")
            except Exception as e:
                logger.error(f"Erro na preparação de features: {e}")
                return {"error": f"Erro na preparação de features: {str(e)}"}

            if features_df.empty or len(feature_cols) == 0:
                return {"error": "Nenhuma feature válida encontrada para clusterização"}

            # Use final clustering service with validated dataset
            from .final_clustering_service import FinalClusteringService
            final_clustering = FinalClusteringService()

            # Perform final clustering with complete validation
            clustering_result = final_clustering.perform_clustering(df)

            if 'error' in clustering_result:
                return clustering_result

            # Add data quality metrics
            data_quality = {
                'original_records': len(df),
                'processed_records': len(ml_ready_df),
                'groups_analyzed': len(features_df),
                'features_used': len(feature_cols),
                'data_completeness': 100.0  # ML processor ensures complete data
            }

            clustering_result['data_quality'] = data_quality
            clustering_result['processing_summary'] = ml_processor.get_processing_summary()

            return clustering_result

        except Exception as e:
            logger.error(f"Erro na clusterização profissional: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"error": str(e)}
