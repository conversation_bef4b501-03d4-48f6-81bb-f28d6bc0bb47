"""
Business Domain Database Adapter
Integrates SQLite database with existing business application
"""

import logging
import pandas as pd
from typing import Dict, List, Optional, Any
import sys
import os

# Add shared database to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'shared', 'datamesh-core', 'database'))

try:
    from sqlite_service import SQLiteService
    DATABASE_AVAILABLE = False  # Force disable to avoid conflicts
except ImportError:
    DATABASE_AVAILABLE = False

# Force disable shared database to prevent conflicts
DATABASE_AVAILABLE = False

logger = logging.getLogger(__name__)

class BusinessDatabaseAdapter:
    """Adapter to integrate SQLite database with business domain"""

    def __init__(self: List[Any]) -> None:
        """Initialize database adapter"""
        self.db_service = None
        if DATABASE_AVAILABLE:
            try:
                self.db_service = SQLiteService()
                logger.info("SQLite database service initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize database service: {e}")
                self.db_service = None
        else:
            logger.warning("SQLite service not available, using fallback data")

    def is_database_available(self) -> bool:
        """Check if database is available - Always return False to use file-based data"""
        # Force use of file-based data from gestao/data/sources
        return False

    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get dashboard data from database"""
        if not self.is_database_available():
            return self._get_fallback_dashboard_data()

        try:
            # Get KPIs from database
            kpis = self.db_service.execute_query("SELECT * FROM business_kpis LIMIT 1")
            if not kpis:
                return self._get_fallback_dashboard_data()

            kpi_data = kpis[0]

            # Get additional metrics
            leads_by_status = self.db_service.execute_query("""
                SELECT status, COUNT(*) as count
                FROM leads
                GROUP BY status
            """)

            opportunities_by_stage = self.db_service.execute_query("""
                SELECT stage, COUNT(*) as count, SUM(value) as total_value
                FROM opportunities
                GROUP BY stage
            """)

            implementations_by_status = self.db_service.execute_query("""
                SELECT status, COUNT(*) as count, SUM(value) as total_value
                FROM implementations
                GROUP BY status
            """)

            responsibles_performance = self.db_service.execute_query("""
                SELECT r.name, r.responsible_id,
                       COUNT(DISTINCT l.lead_id) as total_leads,
                       COUNT(DISTINCT o.opportunity_id) as total_opportunities,
                       COUNT(DISTINCT i.implementation_id) as total_implementations,
                       COALESCE(SUM(i.value), 0) as total_revenue
                FROM responsibles r
                LEFT JOIN leads l ON r.responsible_id = l.responsible_id
                LEFT JOIN opportunities o ON r.responsible_id = o.responsible_id
                LEFT JOIN implementations i ON r.responsible_id = i.responsible_id AND i.status = 'finalized'
                WHERE r.is_active = 1
                GROUP BY r.responsible_id, r.name
                ORDER BY total_revenue DESC
            """)

            return {
                'kpis': {
                    'total_leads': kpi_data.get('total_leads', 0),
                    'total_opportunities': kpi_data.get('total_opportunities', 0),
                    'finalized_implementations': kpi_data.get('finalized_implementations', 0),
                    'total_universities': kpi_data.get('total_universities', 0),
                    'active_responsibles': kpi_data.get('active_responsibles', 0),
                    'total_revenue': float(kpi_data.get('total_revenue', 0) or 0)
                },
                'leads_by_status': {item['status']: item['count'] for item in leads_by_status},
                'opportunities_by_stage': {
                    item['stage']: {
                        'count': item['count'],
                        'value': float(item['total_value'] or 0)
                    } for item in opportunities_by_stage
                },
                'implementations_by_status': {
                    item['status']: {
                        'count': item['count'],
                        'value': float(item['total_value'] or 0)
                    } for item in implementations_by_status
                },
                'responsibles_performance': [
                    {
                        'name': item['name'],
                        'responsible_id': item['responsible_id'],
                        'total_leads': item['total_leads'],
                        'total_opportunities': item['total_opportunities'],
                        'total_implementations': item['total_implementations'],
                        'total_revenue': float(item['total_revenue'] or 0)
                    } for item in responsibles_performance
                ]
            }

        except Exception as e:
            logger.error(f"Error getting dashboard data from database: {e}")
            return self._get_fallback_dashboard_data()

    def get_leads_data(self) -> pd.DataFrame:
        """Get leads data as DataFrame"""
        if not self.is_database_available():
            return pd.DataFrame()

        try:
            leads = self.db_service.execute_query("""
                SELECT l.*, u.name as university_name, r.name as responsible_name
                FROM leads l
                LEFT JOIN universities u ON l.university_id = u.university_id
                LEFT JOIN responsibles r ON l.responsible_id = r.responsible_id
                ORDER BY l.created_at DESC
            """)

            return pd.DataFrame(leads)

        except Exception as e:
            logger.error(f"Error getting leads data: {e}")
            return pd.DataFrame()

    def get_opportunities_data(self) -> pd.DataFrame:
        """Get opportunities data as DataFrame"""
        if not self.is_database_available():
            return pd.DataFrame()

        try:
            opportunities = self.db_service.execute_query("""
                SELECT o.*, l.name as lead_name, r.name as responsible_name
                FROM opportunities o
                LEFT JOIN leads l ON o.lead_id = l.lead_id
                LEFT JOIN responsibles r ON o.responsible_id = r.responsible_id
                ORDER BY o.created_at DESC
            """)

            return pd.DataFrame(opportunities)

        except Exception as e:
            logger.error(f"Error getting opportunities data: {e}")
            return pd.DataFrame()

    def get_implementations_data(self) -> pd.DataFrame:
        """Get implementations data as DataFrame"""
        if not self.is_database_available():
            return pd.DataFrame()

        try:
            implementations = self.db_service.execute_query("""
                SELECT i.*, u.name as university_name, r.name as responsible_name,
                       o.name as opportunity_name
                FROM implementations i
                LEFT JOIN universities u ON i.university_id = u.university_id
                LEFT JOIN responsibles r ON i.responsible_id = r.responsible_id
                LEFT JOIN opportunities o ON i.opportunity_id = o.opportunity_id
                ORDER BY i.created_at DESC
            """)

            return pd.DataFrame(implementations)

        except Exception as e:
            logger.error(f"Error getting implementations data: {e}")
            return pd.DataFrame()

    def get_responsibles_data(self) -> pd.DataFrame:
        """Get responsibles data as DataFrame"""
        if not self.is_database_available():
            return pd.DataFrame()

        try:
            responsibles = self.db_service.execute_query("""
                SELECT r.*,
                       COUNT(DISTINCT l.lead_id) as total_leads,
                       COUNT(DISTINCT o.opportunity_id) as total_opportunities,
                       COUNT(DISTINCT i.implementation_id) as total_implementations,
                       COALESCE(SUM(CASE WHEN i.status = 'finalized' THEN i.value ELSE 0 END), 0) as total_revenue,
                       COALESCE(SUM(CASE WHEN i.status = 'finalized' THEN i.value ELSE 0 END), 0) as mrr
                FROM responsibles r
                LEFT JOIN leads l ON r.responsible_id = l.responsible_id
                LEFT JOIN opportunities o ON r.responsible_id = o.responsible_id
                LEFT JOIN implementations i ON r.responsible_id = i.responsible_id
                GROUP BY r.responsible_id, r.name, r.email, r.phone, r.role, r.team, r.is_active, r.created_at
                ORDER BY total_revenue DESC
            """)

            return pd.DataFrame(responsibles)

        except Exception as e:
            logger.error(f"Error getting responsibles data: {e}")
            return pd.DataFrame()

    def get_universities_data(self) -> pd.DataFrame:
        """Get universities data as DataFrame"""
        if not self.is_database_available():
            return pd.DataFrame()

        try:
            universities = self.db_service.execute_query("""
                SELECT u.*,
                       COUNT(DISTINCT l.lead_id) as total_leads,
                       COUNT(DISTINCT i.implementation_id) as total_implementations
                FROM universities u
                LEFT JOIN leads l ON u.university_id = l.university_id
                LEFT JOIN implementations i ON u.university_id = i.university_id
                GROUP BY u.university_id, u.name, u.city, u.state, u.region, u.created_at
                ORDER BY u.name
            """)

            return pd.DataFrame(universities)

        except Exception as e:
            logger.error(f"Error getting universities data: {e}")
            return pd.DataFrame()

    def _get_fallback_dashboard_data(self) -> Dict[str, Any]:
        """Get fallback dashboard data when database is not available"""
        return {
            'kpis': {
                'total_leads': 0,
                'total_opportunities': 0,
                'finalized_implementations': 0,
                'total_universities': 0,
                'active_responsibles': 0,
                'total_revenue': 0.0
            },
            'leads_by_status': {},
            'opportunities_by_stage': {},
            'implementations_by_status': {},
            'responsibles_performance': []
        }
