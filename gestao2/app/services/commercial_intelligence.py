"""
Amigo DataHub - Commercial Intelligence Service
Advanced ML-based commercial index for class segmentation and analysis
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# ML Libraries
try:
    from sklearn.cluster import KMeans, DBSCAN
    from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import silhouette_score, classification_report
    from sklearn.decomposition import PCA
    import joblib
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

logger = logging.getLogger(__name__)

class CommercialIntelligenceService:
    """Advanced Commercial Intelligence Service with ML capabilities"""

    def __init__(self, data_loader) -> None:
        """
        Initialize the Commercial Intelligence Service

        Args:
            data_loader: DataLoader instance
        """
        self.data_loader = data_loader
        self.df = None
        self.scaler = StandardScaler()
        self.models = {}
        self.encoders = {}

        if not ML_AVAILABLE:
            logger.warning("ML libraries not available. Install scikit-learn for full functionality.")

    def _prepare_data(self) -> pd.DataFrame:
        """Prepare and clean data for ML analysis"""
        try:
            self.df = self.data_loader.get_data().copy()

            # Basic data cleaning
            self.df = self.df.dropna(subset=['Turma'])

            # Convert dates
            date_columns = ['Data_criacao_Oportunidade', 'Data_criacao_lead', 'Data_Criacao_Implantacao']
            for col in date_columns:
                if col in self.df.columns:
                    self.df[col] = pd.to_datetime(self.df[col], errors='coerce')

            return self.df
        except Exception as e:
            logger.error(f"Error preparing data: {e}")
            return pd.DataFrame()

    def calculate_commercial_features(self) -> pd.DataFrame:
        """Calculate commercial features for each class"""
        try:
            if self.df is None:
                self._prepare_data()

            # Group by class
            class_features = []

            for turma in self.df['Turma'].unique():
                if pd.isna(turma):
                    continue

                turma_df = self.df[self.df['Turma'] == turma]

                # Basic metrics
                total_students = turma_df['Nome do Lead'].nunique()
                total_opportunities = turma_df['Oportunidade_id'].nunique()

                # Financial metrics
                avg_monthly_value = turma_df['Valor Mensalidade'].mean() if 'Valor Mensalidade' in turma_df.columns else 0
                total_revenue = turma_df['Valor Mensalidade'].sum() if 'Valor Mensalidade' in turma_df.columns else 0

                # Conversion metrics
                finalized_count = len(turma_df[turma_df['Status_Implantacao'] == 'Finalizado'])
                conversion_rate = finalized_count / total_opportunities if total_opportunities > 0 else 0

                # Time metrics
                if 'Data_criacao_Oportunidade' in turma_df.columns and 'Data_Criacao_Implantacao' in turma_df.columns:
                    time_to_implementation = (turma_df['Data_Criacao_Implantacao'] - turma_df['Data_criacao_Oportunidade']).dt.days.mean()
                else:
                    time_to_implementation = 0

                # Geographic diversity
                unique_cities = turma_df['Cidade'].nunique() if 'Cidade' in turma_df.columns else 0
                unique_states = turma_df['Estado'].nunique() if 'Estado' in turma_df.columns else 0

                # Product diversity
                unique_products = turma_df['Produto'].nunique() if 'Produto' in turma_df.columns else 0

                # Marketing channel effectiveness
                unique_channels = turma_df['canal'].nunique() if 'canal' in turma_df.columns else 0

                # Responsible performance
                unique_responsibles = turma_df['Nome_Responsavel'].nunique() if 'Nome_Responsavel' in turma_df.columns else 0

                # Churn indicators
                cancelled_count = len(turma_df[turma_df['Status_Implantacao'] == 'Cancelado'])
                churn_rate = cancelled_count / total_opportunities if total_opportunities > 0 else 0

                # Activity level (based on implementation phases)
                active_implementations = len(turma_df[turma_df['Fase_Implantacao'].notna()])
                activity_score = active_implementations / total_opportunities if total_opportunities > 0 else 0

                class_features.append({
                    'turma': turma,
                    'total_students': total_students,
                    'total_opportunities': total_opportunities,
                    'avg_monthly_value': avg_monthly_value,
                    'total_revenue': total_revenue,
                    'conversion_rate': conversion_rate,
                    'time_to_implementation': time_to_implementation,
                    'geographic_diversity': unique_cities + unique_states,
                    'product_diversity': unique_products,
                    'channel_diversity': unique_channels,
                    'responsible_diversity': unique_responsibles,
                    'churn_rate': churn_rate,
                    'activity_score': activity_score,
                    'revenue_per_student': total_revenue / total_students if total_students > 0 else 0,
                    'opportunity_density': total_opportunities / total_students if total_students > 0 else 0
                })

            return pd.DataFrame(class_features)

        except Exception as e:
            logger.error(f"Error calculating commercial features: {e}")
            return pd.DataFrame()

    def perform_class_segmentation(self, n_clusters: int = 5) -> Dict:
        """Perform ML-based class segmentation"""
        try:
            if not ML_AVAILABLE:
                return {"error": "ML libraries not available"}

            features_df = self.calculate_commercial_features()
            if features_df.empty:
                return {"error": "No features calculated"}

            # Select features for clustering
            feature_columns = [
                'total_students', 'total_opportunities', 'avg_monthly_value',
                'conversion_rate', 'time_to_implementation', 'geographic_diversity',
                'product_diversity', 'churn_rate', 'activity_score',
                'revenue_per_student', 'opportunity_density'
            ]

            # Prepare features
            X = features_df[feature_columns].fillna(0)

            # Scale features
            X_scaled = self.scaler.fit_transform(X)

            # Perform K-Means clustering
            kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
            clusters = kmeans.fit_predict(X_scaled)

            # Calculate silhouette score
            silhouette_avg = silhouette_score(X_scaled, clusters)

            # Add clusters to dataframe
            features_df['cluster'] = clusters

            # Calculate cluster characteristics
            cluster_summary = []
            for i in range(n_clusters):
                cluster_data = features_df[features_df['cluster'] == i]

                cluster_summary.append({
                    'cluster_id': i,
                    'size': len(cluster_data),
                    'avg_revenue': cluster_data['total_revenue'].mean(),
                    'avg_conversion': cluster_data['conversion_rate'].mean(),
                    'avg_students': cluster_data['total_students'].mean(),
                    'avg_churn': cluster_data['churn_rate'].mean(),
                    'classes': cluster_data['turma'].tolist()
                })

            # Store model
            self.models['kmeans'] = kmeans

            return {
                'clusters': cluster_summary,
                'silhouette_score': silhouette_avg,
                'features_df': features_df,
                'n_clusters': n_clusters
            }

        except Exception as e:
            logger.error(f"Error in class segmentation: {e}")
            return {"error": str(e)}

    def calculate_commercial_index(self) -> Dict:
        """Calculate comprehensive commercial index for each class based on business rules"""
        try:
            if self.df is None:
                self._prepare_data()

            # Importar funções das regras de negócio
            from app.utils.formatters import safe_convert_to_float
            from app.utils.safe_operations import safe_division, safe_percentage

            # Preparar dados financeiros
            df_commercial = self.df.copy()
            if 'Valor Mensalidade' in df_commercial.columns:
                df_commercial['Valor_Float'] = df_commercial['Valor Mensalidade'].apply(safe_convert_to_float)
            else:
                df_commercial['Valor_Float'] = 0.0

            # Limpar dados da coluna Turma antes do groupby
            df_commercial = df_commercial.copy()

            # Converter valores não-string para string na coluna Turma
            if 'Turma' in df_commercial.columns:
                df_commercial['Turma'] = df_commercial['Turma'].astype(str)
                # Remover valores problemáticos
                df_commercial = df_commercial[df_commercial['Turma'].notna()]
                df_commercial = df_commercial[df_commercial['Turma'] != 'nan']
                df_commercial = df_commercial[df_commercial['Turma'] != '']

            # Calcular métricas detalhadas por turma
            turma_metrics = df_commercial.groupby('Turma').agg({
                'Nome do Lead': 'nunique',
                'Oportunidade_id': 'nunique',
                'Valor_Float': ['sum', 'mean'],
                'Status_Implantacao': [
                    lambda x: (x == 'Finalizado').sum(),
                    lambda x: (x == 'Cancelado').sum(),
                    'count'
                ],
                'Isencao em Meses': lambda x: (pd.to_numeric(x, errors='coerce').fillna(0) > 0).sum(),
                'ID do cupom': lambda x: x.notna().sum()
            }).round(2)

            # Flatten column names
            turma_metrics.columns = [
                'total_leads', 'total_oportunidades', 'receita_total', 'receita_media',
                'implementacoes_finalizadas', 'cancelamentos', 'total_implementacoes',
                'leads_com_isencao', 'leads_com_cupom'
            ]
            turma_metrics = turma_metrics.reset_index()

            # Calcular métricas derivadas
            turma_metrics['taxa_conversao_lead_opp'] = safe_percentage(turma_metrics['total_oportunidades'], turma_metrics['total_leads'])
            turma_metrics['taxa_conversao_opp_impl'] = safe_percentage(turma_metrics['implementacoes_finalizadas'], turma_metrics['total_oportunidades'])
            turma_metrics['taxa_conversao_geral'] = safe_percentage(turma_metrics['implementacoes_finalizadas'], turma_metrics['total_leads'])
            turma_metrics['taxa_cancelamento'] = safe_percentage(turma_metrics['cancelamentos'], turma_metrics['total_implementacoes'])
            turma_metrics['taxa_uso_cupom'] = safe_percentage(turma_metrics['leads_com_cupom'], turma_metrics['total_leads'])
            turma_metrics['taxa_isencao'] = safe_percentage(turma_metrics['leads_com_isencao'], turma_metrics['total_leads'])

            # Calcular score de performance (normalizado 0-100)
            # Normalizar métricas principais
            max_receita = turma_metrics['receita_total'].max() if turma_metrics['receita_total'].max() > 0 else 1
            max_leads = turma_metrics['total_leads'].max() if turma_metrics['total_leads'].max() > 0 else 1

            turma_metrics['score_receita'] = (turma_metrics['receita_total'] / max_receita) * 100
            turma_metrics['score_volume'] = (turma_metrics['total_leads'] / max_leads) * 100
            turma_metrics['score_conversao'] = turma_metrics['taxa_conversao_geral']
            turma_metrics['score_retencao'] = 100 - turma_metrics['taxa_cancelamento']

            # Score composto com pesos baseados nas regras de negócio
            turma_metrics['indice_comercial'] = (
                turma_metrics['score_receita'] * 0.35 +      # Receita é fundamental
                turma_metrics['score_conversao'] * 0.30 +    # Conversão é crítica
                turma_metrics['score_volume'] * 0.20 +       # Volume de leads
                turma_metrics['score_retencao'] * 0.15       # Retenção (baixo cancelamento)
            ).round(2)

            # Classificar turmas por performance
            def classificar_turma(score: float) -> str:
                if score >= 80:
                    return 'Excelente'
                elif score >= 60:
                    return 'Boa'
                elif score >= 40:
                    return 'Moderada'
                elif score >= 20:
                    return 'Baixa'
                else:
                    return 'Crítica'

            turma_metrics['classificacao'] = turma_metrics['indice_comercial'].apply(classificar_turma)

            # Estatísticas resumo avançadas
            summary_stats = {
                'total_classes': len(turma_metrics),
                'avg_commercial_index': turma_metrics['indice_comercial'].mean(),
                'total_revenue': turma_metrics['receita_total'].sum(),
                'total_leads': turma_metrics['total_leads'].sum(),
                'avg_conversion_rate': turma_metrics['taxa_conversao_geral'].mean(),
                'avg_cancellation_rate': turma_metrics['taxa_cancelamento'].mean(),
                'avg_coupon_usage': turma_metrics['taxa_uso_cupom'].mean(),

                # Distribuição por classificação
                'distribuicao_performance': turma_metrics['classificacao'].value_counts().to_dict(),

                # Top performers
                'top_performers': turma_metrics.nlargest(10, 'indice_comercial')[
                    ['Turma', 'indice_comercial', 'classificacao', 'receita_total', 'taxa_conversao_geral']
                ].to_dict('records'),

                # Bottom performers
                'bottom_performers': turma_metrics.nsmallest(5, 'indice_comercial')[
                    ['Turma', 'indice_comercial', 'classificacao', 'receita_total', 'taxa_conversao_geral']
                ].to_dict('records'),

                # Turmas com problemas
                'turmas_alta_taxa_cancelamento': turma_metrics[
                    turma_metrics['taxa_cancelamento'] > 20
                ][['Turma', 'taxa_cancelamento', 'total_implementacoes']].to_dict('records'),

                # Turmas com boa performance em cupons
                'turmas_bom_uso_cupom': turma_metrics[
                    turma_metrics['taxa_uso_cupom'] > 30
                ][['Turma', 'taxa_uso_cupom', 'taxa_conversao_geral']].to_dict('records')
            }

            # Limpar dados para JSON serialization
            def clean_for_json(obj: Any) -> Any:
                """Remove NaN and infinity values for JSON serialization"""
                if isinstance(obj, dict):
                    return {k: clean_for_json(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [clean_for_json(item) for item in obj]
                elif pd.isna(obj) or obj == float('inf') or obj == float('-inf'):
                    return 0
                else:
                    return obj

            result = {
                'summary_stats': clean_for_json(summary_stats),
                'turma_metrics': clean_for_json(turma_metrics.to_dict('records')),
                'regras_classificacao': {
                    'Excelente': 'Score ≥ 80 - Turmas com alta receita, conversão e baixo cancelamento',
                    'Boa': 'Score 60-79 - Turmas com boa performance geral',
                    'Moderada': 'Score 40-59 - Turmas com performance mediana, necessitam atenção',
                    'Baixa': 'Score 20-39 - Turmas com problemas, necessitam intervenção',
                    'Crítica': 'Score < 20 - Turmas com performance muito baixa, necessitam ação urgente'
                },
                'criterios_score': {
                    'Receita (35%)': 'Valor total gerado pela turma',
                    'Conversão (30%)': 'Taxa de conversão de leads para implementações finalizadas',
                    'Volume (20%)': 'Quantidade de leads gerados',
                    'Retenção (15%)': 'Capacidade de manter clientes (baixo cancelamento)'
                }
            }

            return result

        except Exception as e:
            logger.error(f"Error calculating commercial index: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"error": str(e)}

    def _normalize_metric(self, series: pd.Series) -> pd.Series:
        """Normalize metric to 0-1 scale"""
        min_val = series.min()
        max_val = series.max()
        if max_val == min_val:
            return pd.Series([0.5] * len(series), index=series.index)
        return (series - min_val) / (max_val - min_val)

    def predict_class_potential(self) -> Dict:
        """Predict commercial potential using Random Forest"""
        try:
            if not ML_AVAILABLE:
                return {"error": "ML libraries not available"}

            features_df = self.calculate_commercial_features()
            if features_df.empty:
                return {"error": "No features calculated"}

            # Create target variable (high potential = top 30% by revenue)
            revenue_threshold = features_df['total_revenue'].quantile(0.7)
            features_df['high_potential'] = (features_df['total_revenue'] >= revenue_threshold).astype(int)

            # Select features for prediction
            feature_columns = [
                'total_students', 'total_opportunities', 'avg_monthly_value',
                'conversion_rate', 'geographic_diversity', 'product_diversity',
                'churn_rate', 'activity_score', 'opportunity_density'
            ]

            X = features_df[feature_columns].fillna(0)
            y = features_df['high_potential']

            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

            # Train Random Forest
            rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
            rf_model.fit(X_train, y_train)

            # Make predictions
            y_pred = rf_model.predict(X_test)
            predictions_proba = rf_model.predict_proba(X)[:, 1]

            # Feature importance
            feature_importance = dict(zip(feature_columns, rf_model.feature_importances_))

            # Add predictions to dataframe
            features_df['potential_probability'] = predictions_proba
            features_df['predicted_potential'] = rf_model.predict(X)

            # Store model
            self.models['rf_potential'] = rf_model

            return {
                'predictions': features_df[['turma', 'potential_probability', 'predicted_potential']].to_dict('records'),
                'feature_importance': feature_importance,
                'model_accuracy': rf_model.score(X_test, y_test),
                'high_potential_classes': features_df[features_df['predicted_potential'] == 1]['turma'].tolist()
            }

        except Exception as e:
            logger.error(f"Error in potential prediction: {e}")
            return {"error": str(e)}

    def predict_revenue_forecast(self, months_ahead: int = 6) -> Dict:
        """Predict revenue forecast for classes"""
        try:
            if not ML_AVAILABLE:
                return {"error": "ML libraries not available"}

            features_df = self.calculate_commercial_features()
            if features_df.empty:
                return {"error": "No features calculated"}

            # Prepare features for revenue prediction
            feature_columns = [
                'total_students', 'total_opportunities', 'conversion_rate',
                'activity_score', 'opportunity_density', 'avg_monthly_value'
            ]

            X = features_df[feature_columns].fillna(0)
            y = features_df['total_revenue']

            # Train Random Forest Regressor
            rf_regressor = RandomForestRegressor(n_estimators=100, random_state=42)
            rf_regressor.fit(X, y)

            # Predict current revenue potential
            predicted_revenue = rf_regressor.predict(X)

            # Simple growth projection (can be enhanced with time series analysis)
            growth_factor = 1 + (months_ahead * 0.05)  # Assume 5% monthly growth
            forecasted_revenue = predicted_revenue * growth_factor

            features_df['predicted_revenue'] = predicted_revenue
            features_df['forecasted_revenue'] = forecasted_revenue
            features_df['revenue_growth_potential'] = forecasted_revenue - features_df['total_revenue']

            # Store model
            self.models['rf_revenue'] = rf_regressor

            return {
                'revenue_forecasts': features_df[['turma', 'total_revenue', 'predicted_revenue', 'forecasted_revenue', 'revenue_growth_potential']].to_dict('records'),
                'total_current_revenue': features_df['total_revenue'].sum(),
                'total_forecasted_revenue': features_df['forecasted_revenue'].sum(),
                'total_growth_potential': features_df['revenue_growth_potential'].sum(),
                'model_score': rf_regressor.score(X, y)
            }

        except Exception as e:
            logger.error(f"Error in revenue forecast: {e}")
            return {"error": str(e)}

    def analyze_churn_risk(self) -> Dict:
        """Analyze churn risk for classes"""
        try:
            features_df = self.calculate_commercial_features()
            if features_df.empty:
                return {"error": "No features calculated"}

            # Define risk levels based on churn rate and other factors
            features_df['churn_risk_score'] = (
                features_df['churn_rate'] * 0.4 +
                (1 - features_df['conversion_rate']) * 0.3 +
                (1 - features_df['activity_score']) * 0.3
            )

            # Classify risk levels
            features_df['risk_level'] = pd.cut(
                features_df['churn_risk_score'],
                bins=[0, 0.2, 0.4, 0.6, 1.0],
                labels=['Baixo', 'Médio', 'Alto', 'Crítico']
            )

            # Identify classes needing attention
            high_risk_classes = features_df[features_df['churn_risk_score'] > 0.6]

            return {
                'churn_analysis': features_df[['turma', 'churn_rate', 'churn_risk_score', 'risk_level']].to_dict('records'),
                'risk_distribution': features_df['risk_level'].value_counts().to_dict(),
                'high_risk_classes': high_risk_classes['turma'].tolist(),
                'avg_churn_risk': features_df['churn_risk_score'].mean(),
                'recommendations': self._generate_churn_recommendations(high_risk_classes)
            }

        except Exception as e:
            logger.error(f"Error in churn analysis: {e}")
            return {"error": str(e)}

    def _generate_churn_recommendations(self, high_risk_df: pd.DataFrame) -> List[Dict]:
        """Generate recommendations for high-risk classes"""
        recommendations = []

        for _, row in high_risk_df.iterrows():
            rec = {
                'turma': row['turma'],
                'actions': []
            }

            if row['conversion_rate'] < 0.3:
                rec['actions'].append("Melhorar processo de conversão")

            if row['activity_score'] < 0.5:
                rec['actions'].append("Aumentar engajamento e atividades")

            if row['churn_rate'] > 0.3:
                rec['actions'].append("Implementar programa de retenção")

            if row['time_to_implementation'] > 60:
                rec['actions'].append("Otimizar tempo de implementação")

            recommendations.append(rec)

        return recommendations

    def analyze_captacao_intelligence(self) -> Dict:
        """
        Análise avançada de inteligência para captação de turmas
        Combina business rules com ML para insights de alto nível
        """
        try:
            # Importar business rules service
            from app.services.business_rules import BusinessRulesService

            # Inicializar business rules
            business_rules = BusinessRulesService(self.data_loader)

            # Obter análise de captação das business rules
            captacao_analysis = business_rules.analyze_turma_captacao_potential()

            if 'error' in captacao_analysis:
                return captacao_analysis

            # Combinar com análises ML
            commercial_index = self.calculate_commercial_index()
            segmentation = self.perform_class_segmentation()

            # Análise de velocidade de conversão
            velocity_analysis = business_rules.calculate_conversion_velocity()

            # Análise sazonal
            seasonal_quarters, seasonal_quarter_rates, seasonal_months, seasonal_month_rates = business_rules.calculate_seasonal_conversion_analysis()

            # Análise de tendências por mês
            month_labels, lead_to_opp_rates, opp_to_impl_rates, overall_rates = business_rules.calculate_conversion_rate_by_month()

            # Combinar insights
            enhanced_analysis = captacao_analysis.copy()

            # Adicionar insights de ML se disponível
            if ML_AVAILABLE and 'error' not in segmentation:
                enhanced_analysis['ml_insights'] = {
                    'clusters_identificados': segmentation.get('n_clusters', 0),
                    'silhouette_score': segmentation.get('silhouette_score', 0),
                    'cluster_summary': segmentation.get('clusters', [])
                }

            # Adicionar análise de velocidade
            enhanced_analysis['velocity_insights'] = {
                'velocity_by_university': velocity_analysis.get('velocity_by_university', {}),
                'velocity_by_state': velocity_analysis.get('velocity_by_state', {}),
                'velocity_by_responsible': velocity_analysis.get('velocity_by_responsible', {}),
                'velocity_distribution': velocity_analysis.get('velocity_distribution', {})
            }

            # Adicionar análise sazonal
            enhanced_analysis['seasonal_insights'] = {
                'quarter_labels': seasonal_quarters,
                'quarter_rates': seasonal_quarter_rates,
                'month_labels': seasonal_months,
                'month_rates': seasonal_month_rates,
                'best_quarter': seasonal_quarters[seasonal_quarter_rates.index(max(seasonal_quarter_rates))] if seasonal_quarter_rates else None,
                'best_month': seasonal_months[seasonal_month_rates.index(max(seasonal_month_rates))] if seasonal_month_rates else None
            }

            # Adicionar tendências mensais
            enhanced_analysis['monthly_trends'] = {
                'month_labels': month_labels,
                'lead_to_opp_rates': lead_to_opp_rates,
                'opp_to_impl_rates': opp_to_impl_rates,
                'overall_rates': overall_rates,
                'trend_direction': 'crescente' if len(overall_rates) >= 2 and overall_rates[-1] > overall_rates[0] else 'decrescente'
            }

            # Gerar recomendações estratégicas
            strategic_recommendations = self._generate_strategic_recommendations(enhanced_analysis)
            enhanced_analysis['strategic_recommendations'] = strategic_recommendations

            return enhanced_analysis

        except Exception as e:
            logger.error(f"Error in captacao intelligence analysis: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"error": str(e)}

    def _generate_strategic_recommendations(self, analysis: Dict) -> List[Dict]:
        """Gerar recomendações estratégicas baseadas na análise"""
        recommendations = []

        try:
            resumo = analysis.get('resumo', {})
            top_captacao = analysis.get('top_captacao', [])
            baixo_potencial = analysis.get('baixo_potencial', [])
            oportunidades = analysis.get('oportunidades_melhoria', [])

            # Recomendação 1: Foco nas top performers
            if top_captacao:
                top_3 = top_captacao[:3]
                recommendations.append({
                    'categoria': 'Maximização de Resultados',
                    'prioridade': 'Alta',
                    'titulo': 'Ampliar Estratégias das Top Performers',
                    'descricao': f"Replicar estratégias das 3 melhores turmas: {', '.join([t['turma'] for t in top_3])}",
                    'impacto_estimado': 'Alto',
                    'prazo': '30 dias',
                    'acoes': [
                        'Analisar canais de captação mais eficazes',
                        'Identificar responsáveis com melhor performance',
                        'Documentar processos de conversão',
                        'Aplicar learnings em outras turmas'
                    ]
                })

            # Recomendação 2: Turmas de baixo potencial
            if baixo_potencial:
                recommendations.append({
                    'categoria': 'Recuperação de Performance',
                    'prioridade': 'Média',
                    'titulo': 'Plano de Recuperação para Turmas de Baixo Potencial',
                    'descricao': f"Implementar ações corretivas em {len(baixo_potencial)} turmas com score < 40",
                    'impacto_estimado': 'Médio',
                    'prazo': '60 dias',
                    'acoes': [
                        'Revisar estratégias de captação',
                        'Treinar responsáveis comerciais',
                        'Implementar campanhas direcionadas',
                        'Monitorar progresso semanalmente'
                    ]
                })

            # Recomendação 3: Oportunidades específicas
            if oportunidades:
                tipos_oportunidades = {}
                for opp in oportunidades:
                    tipo = opp['tipo']
                    tipos_oportunidades[tipo] = tipos_oportunidades.get(tipo, 0) + 1

                for tipo, quantidade in tipos_oportunidades.items():
                    if tipo == 'Baixa Conversão':
                        recommendations.append({
                            'categoria': 'Otimização de Conversão',
                            'prioridade': 'Alta',
                            'titulo': 'Programa de Melhoria de Conversão',
                            'descricao': f"Melhorar conversão em {quantidade} turmas identificadas",
                            'impacto_estimado': 'Alto',
                            'prazo': '45 dias',
                            'acoes': [
                                'Implementar processo de qualificação de leads',
                                'Treinar equipe em técnicas de follow-up',
                                'Criar scripts de abordagem personalizados',
                                'Implementar CRM para acompanhamento'
                            ]
                        })
                    elif tipo == 'Baixa Diversificação':
                        recommendations.append({
                            'categoria': 'Expansão de Canais',
                            'prioridade': 'Média',
                            'titulo': 'Diversificação de Canais de Captação',
                            'descricao': f"Expandir canais em {quantidade} turmas com baixa diversificação",
                            'impacto_estimado': 'Médio',
                            'prazo': '90 dias',
                            'acoes': [
                                'Mapear novos canais de marketing',
                                'Implementar campanhas multi-canal',
                                'Parcerias com influenciadores/instituições',
                                'Investir em marketing digital direcionado'
                            ]
                        })

            # Recomendação 4: Análise sazonal
            seasonal_insights = analysis.get('seasonal_insights', {})
            if seasonal_insights.get('best_month'):
                recommendations.append({
                    'categoria': 'Planejamento Sazonal',
                    'prioridade': 'Média',
                    'titulo': 'Otimização Baseada em Sazonalidade',
                    'descricao': f"Aproveitar pico de conversão em {seasonal_insights['best_month']}",
                    'impacto_estimado': 'Médio',
                    'prazo': 'Contínuo',
                    'acoes': [
                        'Intensificar campanhas nos meses de pico',
                        'Preparar equipe para alta demanda',
                        'Ajustar orçamento de marketing sazonalmente',
                        'Criar campanhas temáticas específicas'
                    ]
                })

            # Recomendação 5: Velocidade de conversão
            velocity_insights = analysis.get('velocity_insights', {})
            if velocity_insights.get('velocity_by_responsible'):
                velocidades = velocity_insights['velocity_by_responsible']
                if velocidades:
                    melhor_responsavel = min(velocidades.items(), key=lambda x: x[1])
                    recommendations.append({
                        'categoria': 'Otimização de Processos',
                        'prioridade': 'Alta',
                        'titulo': 'Padronização de Processos de Conversão',
                        'descricao': f"Replicar metodologia do responsável mais rápido: {melhor_responsavel[0]} ({melhor_responsavel[1]} dias)",
                        'impacto_estimado': 'Alto',
                        'prazo': '30 dias',
                        'acoes': [
                            'Documentar processo do melhor responsável',
                            'Treinar equipe na metodologia',
                            'Implementar métricas de velocidade',
                            'Criar incentivos para conversão rápida'
                        ]
                    })

            return recommendations

        except Exception as e:
            logger.error(f"Error generating strategic recommendations: {e}")
            return []

    def generate_comprehensive_report(self) -> Dict:
        """Generate comprehensive commercial intelligence report"""
        try:
            # Get all analyses including new captacao intelligence
            captacao_intelligence = self.analyze_captacao_intelligence()
            segmentation = self.perform_class_segmentation()
            commercial_index = self.calculate_commercial_index()
            potential_prediction = self.predict_class_potential()
            revenue_forecast = self.predict_revenue_forecast()
            churn_analysis = self.analyze_churn_risk()

            return {
                'timestamp': datetime.now().isoformat(),
                'captacao_intelligence': captacao_intelligence,
                'segmentation': segmentation,
                'commercial_index': commercial_index,
                'potential_prediction': potential_prediction,
                'revenue_forecast': revenue_forecast,
                'churn_analysis': churn_analysis,
                'summary': {
                    'total_classes_analyzed': len(self.calculate_commercial_features()),
                    'ml_models_available': ML_AVAILABLE,
                    'analysis_status': 'complete',
                    'captacao_score_medio': captacao_intelligence.get('resumo', {}).get('score_medio_captacao', 0) if 'error' not in captacao_intelligence else 0
                }
            }

        except Exception as e:
            logger.error(f"Error generating comprehensive report: {e}")
            return {"error": str(e)}
