#!/usr/bin/env python
"""
Amigo DataHub - Business Domain Application
"""

import sys
import argparse
from pathlib import Path
from app import create_app

# Add shared core to path
CORE_PATH = Path(__file__).parent.parent / 'shared' / 'datamesh-core'
sys.path.insert(0, str(CORE_PATH))

try:
    from monitoring.metrics_service import MetricsCollector, PerformanceMonitor
    from monitoring.activity_middleware import create_activity_tracker
    CORE_AVAILABLE = True
except ImportError:
    # Monitoring modules not available, using fallback
    CORE_AVAILABLE = False

    # Create dummy classes for fallback
    class MetricsCollector:
        def __init__(self, domain):
            self.domain = domain

        def record_response_time(self, response_time):
            pass

        def record_histogram(self, metric_name, value):
            pass

        def record_counter(self, metric_name):
            pass

    class PerformanceMonitor:
        def __init__(self, collector):
            self.collector = collector

    def create_activity_tracker(domain):
        return None

def register_apis(app):
    """Register security and monitoring APIs"""
    try:
        from app.api.auth_api import auth_bp
        from app.api.monitoring_api import monitoring_bp

        app.register_blueprint(auth_bp)
        app.register_blueprint(monitoring_bp)

        print("✅ Security and monitoring APIs registered successfully")
        return True
    except ImportError as e:
        print(f"⚠️  Warning: Could not register APIs: {e}")
        return False

def setup_monitoring(app):
    """Setup performance monitoring"""
    try:
        if CORE_AVAILABLE:
            metrics_collector = MetricsCollector('business')
            PerformanceMonitor(metrics_collector)
        else:
            # Create a simple dummy collector
            class SimpleMetricsCollector:
                def record_response_time(self, time_ms):
                    pass
                def record_histogram(self, name, value):
                    pass
                def record_counter(self, name):
                    pass

            metrics_collector = SimpleMetricsCollector()

        # Add basic performance monitoring
        @app.before_request
        def before_request():
            from flask import g
            import time
            g.start_time = time.time()

        @app.after_request
        def after_request(response):
            from flask import g
            import time

            if hasattr(g, 'start_time') and g.start_time is not None:
                try:
                    response_time = (time.time() - g.start_time) * 1000
                    try:
                        metrics_collector.record_response_time(response_time)
                    except AttributeError:
                        # Fallback if method doesn't exist
                        pass
                except (TypeError, ValueError) as e:
                    # Handle cases where start_time is not a valid number
                    print(f"Warning: Could not calculate response time: {e}")

            return response

        print("✅ Performance monitoring initialized")
        return True
    except Exception as e:
        print(f"⚠️  Warning: Could not initialize monitoring: {e}")
        return False

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='DataHub Amigo One - Business Domain')
    parser.add_argument('--port', type=int, default=5000, help='Port to run the app on')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='Host to run the app on')
    parser.add_argument('--debug', action='store_true', help='Run in debug mode')
    args = parser.parse_args()

    print("🚀 Starting DataHub Amigo One - Business Domain")
    print("📊 Domain: Negócios")
    print("🔧 Environment: Development Mode")
    print("📦 Version: 1.0.0")

    app = create_app('development')

    # Register APIs and setup monitoring
    apis_registered = register_apis(app)
    monitoring_setup = setup_monitoring(app)

    # Setup activity tracking
    activity_tracking = False
    if CORE_AVAILABLE:
        try:
            activity_tracker = create_activity_tracker('business')
            activity_tracker.init_app(app)
            activity_tracking = True
            print("✅ User activity tracking initialized")
        except Exception as e:
            print(f"⚠️  Warning: Could not initialize activity tracking: {e}")
            activity_tracking = False

    print("🔒 Security: Enabled" if apis_registered else "🔒 Security: Disabled")
    print("📈 Monitoring: Enabled" if monitoring_setup else "📈 Monitoring: Disabled")
    print("👥 Activity Tracking: Enabled" if activity_tracking else "👥 Activity Tracking: Disabled")
    print(f"🌐 URL: http://{args.host}:{args.port}")

    if apis_registered:
        print(f"🔐 Admin Panel: http://{args.host}:{args.port}/admin/users")
        print(f"📊 Monitoring: http://{args.host}:{args.port}/api/monitoring/dashboard")

    if activity_tracking:
        print("👥 User Activity: Real-time tracking enabled for team monitoring")

    app.run(host=args.host, port=args.port, debug=args.debug or True)
