#!/usr/bin/env python
"""
Amigo DataHub - Business Domain Application
Simplified without shared dependencies
"""

import sys
import argparse
from pathlib import Path
from app import create_app

def setup_monitoring(app):
    """Setup basic performance monitoring"""
    try:
        # Add basic performance monitoring
        @app.before_request
        def before_request():
            from flask import g
            import time
            g.start_time = time.time()

        @app.after_request
        def after_request(response):
            from flask import g
            import time

            if hasattr(g, 'start_time') and g.start_time is not None:
                response_time = time.time() - g.start_time
                # Simple logging instead of complex metrics
                print(f"Request completed in {response_time:.3f}s")

            return response

    except Exception as e:
        print(f"Warning: Could not setup monitoring: {e}")
        # Continue without monitoring

def register_apis(app):
    """Register API blueprints"""
    try:
        from app.api.business_api import business_api
        from app.api.admin_api import admin_api
        
        app.register_blueprint(business_api, url_prefix='/api')
        app.register_blueprint(admin_api, url_prefix='/api/admin')
        
        print("✅ APIs registered successfully")
        return True
    except ImportError as e:
        print(f"⚠️  Warning: Could not register APIs: {e}")
        return False

def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(description='Amigo DataHub - Business Domain')
    parser.add_argument('--port', type=int, default=5000, help='Port to run the application on')
    parser.add_argument('--host', default='0.0.0.0', help='Host to run the application on')
    parser.add_argument('--debug', action='store_true', help='Run in debug mode')
    parser.add_argument('--env', default='development', choices=['development', 'testing', 'production'], 
                       help='Environment to run in')
    
    args = parser.parse_args()
    
    try:
        # Create Flask application
        app = create_app(config_name=args.env)
        
        if not app:
            print("❌ Failed to create Flask application")
            sys.exit(1)
        
        print(f"🚀 Starting Business Domain Application")
        print(f"   Environment: {args.env}")
        print(f"   Host: {args.host}")
        print(f"   Port: {args.port}")
        print(f"   Debug: {args.debug}")
        
        # Setup monitoring
        setup_monitoring(app)
        
        # Register APIs
        register_apis(app)
        
        # Run the application
        app.run(
            host=args.host,
            port=args.port,
            debug=args.debug,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n🛑 Application stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
