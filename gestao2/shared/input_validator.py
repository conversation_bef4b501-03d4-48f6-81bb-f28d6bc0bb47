"""
Input Validation System for DataHub Amigo One
Provides secure input validation for all endpoints
"""

import re
import logging
from typing import Dict, Any, List, Optional, Union
from functools import wraps
from flask import request, jsonify

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Custom validation error"""
    pass

class InputValidator:
    """Secure input validation with sanitization"""
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format"""
        if not email or len(email) > 254:
            return False
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def validate_password(password: str) -> Dict[str, Any]:
        """Validate password strength"""
        if not password:
            return {'valid': False, 'message': 'Password is required'}
        
        if len(password) < 8:
            return {'valid': False, 'message': 'Password must be at least 8 characters'}
        
        if len(password) > 128:
            return {'valid': False, 'message': 'Password too long (max 128 characters)'}
        
        # Check for basic complexity
        has_upper = bool(re.search(r'[A-Z]', password))
        has_lower = bool(re.search(r'[a-z]', password))
        has_digit = bool(re.search(r'\d', password))
        
        if not (has_upper and has_lower and has_digit):
            return {
                'valid': False, 
                'message': 'Password must contain uppercase, lowercase, and numbers'
            }
        
        return {'valid': True, 'message': 'Password is valid'}
    
    @staticmethod
    def validate_username(username: str) -> bool:
        """Validate username format"""
        if not username or len(username) < 3 or len(username) > 50:
            return False
        
        # Allow alphanumeric, underscore, hyphen, dot
        pattern = r'^[a-zA-Z0-9._-]+$'
        return bool(re.match(pattern, username))
    
    @staticmethod
    def sanitize_string(value: str, max_length: int = 255) -> str:
        """Sanitize string input"""
        if not isinstance(value, str):
            return str(value)[:max_length]
        
        # Remove potentially dangerous characters
        sanitized = re.sub(r'[<>"\']', '', value)
        return sanitized.strip()[:max_length]
    
    @staticmethod
    def validate_integer(value: Any, min_val: int = None, max_val: int = None) -> Optional[int]:
        """Validate and convert to integer"""
        try:
            int_val = int(value)
            
            if min_val is not None and int_val < min_val:
                raise ValidationError(f"Value must be at least {min_val}")
            
            if max_val is not None and int_val > max_val:
                raise ValidationError(f"Value must be at most {max_val}")
            
            return int_val
        except (ValueError, TypeError):
            raise ValidationError("Invalid integer value")
    
    @staticmethod
    def validate_float(value: Any, min_val: float = None, max_val: float = None) -> Optional[float]:
        """Validate and convert to float"""
        try:
            float_val = float(value)
            
            if min_val is not None and float_val < min_val:
                raise ValidationError(f"Value must be at least {min_val}")
            
            if max_val is not None and float_val > max_val:
                raise ValidationError(f"Value must be at most {max_val}")
            
            return float_val
        except (ValueError, TypeError):
            raise ValidationError("Invalid float value")
    
    @staticmethod
    def validate_date_string(date_str: str) -> bool:
        """Validate date string format (YYYY-MM-DD)"""
        if not date_str:
            return False
        
        pattern = r'^\d{4}-\d{2}-\d{2}$'
        if not re.match(pattern, date_str):
            return False
        
        try:
            from datetime import datetime
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_file_upload(file) -> Dict[str, Any]:
        """Validate file upload"""
        if not file:
            return {'valid': False, 'message': 'No file provided'}
        
        if not file.filename:
            return {'valid': False, 'message': 'No filename provided'}
        
        # Check file extension
        allowed_extensions = {'.xlsx', '.xls', '.csv', '.txt'}
        file_ext = '.' + file.filename.rsplit('.', 1)[-1].lower()
        
        if file_ext not in allowed_extensions:
            return {
                'valid': False, 
                'message': f'File type not allowed. Allowed: {", ".join(allowed_extensions)}'
            }
        
        # Check file size (16MB max)
        file.seek(0, 2)  # Seek to end
        file_size = file.tell()
        file.seek(0)  # Reset to beginning
        
        max_size = 16 * 1024 * 1024  # 16MB
        if file_size > max_size:
            return {'valid': False, 'message': 'File too large (max 16MB)'}
        
        return {'valid': True, 'message': 'File is valid'}

class ValidationSchema:
    """Schema-based validation"""
    
    def __init__(self, schema: Dict[str, Dict[str, Any]]):
        self.schema = schema
    
    def validate(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data against schema"""
        errors = {}
        validated_data = {}
        
        for field, rules in self.schema.items():
            value = data.get(field)
            
            # Check required fields
            if rules.get('required', False) and not value:
                errors[field] = f"{field} is required"
                continue
            
            # Skip validation if field is optional and empty
            if not value and not rules.get('required', False):
                continue
            
            try:
                # Type validation
                field_type = rules.get('type', 'string')
                
                if field_type == 'email':
                    if not InputValidator.validate_email(value):
                        errors[field] = "Invalid email format"
                    else:
                        validated_data[field] = value
                
                elif field_type == 'password':
                    result = InputValidator.validate_password(value)
                    if not result['valid']:
                        errors[field] = result['message']
                    else:
                        validated_data[field] = value
                
                elif field_type == 'username':
                    if not InputValidator.validate_username(value):
                        errors[field] = "Invalid username format"
                    else:
                        validated_data[field] = value
                
                elif field_type == 'string':
                    max_length = rules.get('max_length', 255)
                    validated_data[field] = InputValidator.sanitize_string(value, max_length)
                
                elif field_type == 'integer':
                    min_val = rules.get('min_value')
                    max_val = rules.get('max_value')
                    validated_data[field] = InputValidator.validate_integer(value, min_val, max_val)
                
                elif field_type == 'float':
                    min_val = rules.get('min_value')
                    max_val = rules.get('max_value')
                    validated_data[field] = InputValidator.validate_float(value, min_val, max_val)
                
                elif field_type == 'date':
                    if not InputValidator.validate_date_string(value):
                        errors[field] = "Invalid date format (YYYY-MM-DD)"
                    else:
                        validated_data[field] = value
                
            except ValidationError as e:
                errors[field] = str(e)
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'data': validated_data
        }

def validate_json_input(schema: Dict[str, Dict[str, Any]]):
    """Decorator for JSON input validation"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return jsonify({'error': 'Content-Type must be application/json'}), 400
            
            data = request.get_json()
            if not data:
                return jsonify({'error': 'Invalid JSON data'}), 400
            
            validator = ValidationSchema(schema)
            result = validator.validate(data)
            
            if not result['valid']:
                return jsonify({
                    'error': 'Validation failed',
                    'details': result['errors']
                }), 400
            
            # Add validated data to kwargs
            kwargs['validated_data'] = result['data']
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def validate_form_input(schema: Dict[str, Dict[str, Any]]):
    """Decorator for form input validation"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            data = request.form.to_dict()
            
            if not data:
                return jsonify({'error': 'No form data provided'}), 400
            
            validator = ValidationSchema(schema)
            result = validator.validate(data)
            
            if not result['valid']:
                return jsonify({
                    'error': 'Validation failed',
                    'details': result['errors']
                }), 400
            
            # Add validated data to kwargs
            kwargs['validated_data'] = result['data']
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

# Common validation schemas
LOGIN_SCHEMA = {
    'username': {'type': 'username', 'required': True},
    'password': {'type': 'password', 'required': True}
}

REGISTER_SCHEMA = {
    'username': {'type': 'username', 'required': True},
    'email': {'type': 'email', 'required': True},
    'password': {'type': 'password', 'required': True},
    'full_name': {'type': 'string', 'required': True, 'max_length': 100}
}

USER_UPDATE_SCHEMA = {
    'email': {'type': 'email', 'required': False},
    'full_name': {'type': 'string', 'required': False, 'max_length': 100},
    'role': {'type': 'string', 'required': False, 'max_length': 50}
}
