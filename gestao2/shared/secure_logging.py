"""
Secure Logging System for DataHub Amigo One
Prevents information disclosure through logs while maintaining debugging capability
"""

import logging
import re
import json
import hashlib
from typing import Any, Dict, List, Optional
from datetime import datetime
from pathlib import Path

class SecureFormatter(logging.Formatter):
    """Secure log formatter that sanitizes sensitive information"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Patterns for sensitive information
        self.sensitive_patterns = [
            # Passwords and secrets - SECURITY: Use environment variables for credentials
            (r'password["\']?\s*[:=]\s*["\']?([^"\'\s,}]+)', r'password="***REDACTED***"'),
            (r'secret["\']?\s*[:=]\s*["\']?([^"\'\s,}]+)', r'secret="***REDACTED***"'),
            (r'token["\']?\s*[:=]\s*["\']?([^"\'\s,}]+)', r'token="***REDACTED***"'),
            (r'api_key["\']?\s*[:=]\s*["\']?([^"\'\s,}]+)', r'api_key="***REDACTED***"'),

            # Email addresses (partial redaction)
            (r'([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', self._redact_email),

            # Credit card numbers
            (r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b', r'****-****-****-****'),

            # CPF/CNPJ (Brazilian documents)
            (r'\b\d{3}\.?\d{3}\.?\d{3}-?\d{2}\b', r'***.***.***-**'),
            (r'\b\d{2}\.?\d{3}\.?\d{3}/?\d{4}-?\d{2}\b', r'**.***.***/****-**'),

            # IP addresses (partial redaction)
            (r'\b(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})\b', r'\1.\2.***.**'),

            # File paths (keep only filename)
            (r'/[^/\s]+(/[^/\s]+)*(/[^/\s]*\.(py|js|html|css|json))', r'***/\3'),
        ]

    def _redact_email(self, match):
        """Redact email address partially"""
        local, domain = match.groups()
        if len(local) <= 2:
            return f"***@{domain}"
        return f"{local[:2]}***@{domain}"

    def format(self, record):
        """Format log record with sensitive data redaction"""
        # Get the original formatted message
        formatted = super().format(record)

        # Apply sensitive data redaction
        for pattern, replacement in self.sensitive_patterns:
            if callable(replacement):
                formatted = re.sub(pattern, replacement, formatted)
            else:
                formatted = re.sub(pattern, replacement, formatted, flags=re.IGNORECASE)

        return formatted

class SecureLogger:
    """Secure logger with context and sanitization"""

    def __init__(self, name: str, domain: str = 'shared'):
        self.name = name
        self.domain = domain
        self.logger = logging.getLogger(f"{domain}.{name}")
        self.setup_secure_logging()

    def setup_secure_logging(self):
        """Setup secure logging configuration"""
        # Create logs directory
        log_dir = Path('logs')
        log_dir.mkdir(exist_ok=True)

        # Configure formatter
        formatter = SecureFormatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # File handler for general logs
        file_handler = logging.FileHandler(
            log_dir / f'{self.domain}_{self.name}.log',
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.INFO)

        # File handler for security logs
        security_handler = logging.FileHandler(
            log_dir / f'{self.domain}_security.log',
            encoding='utf-8'
        )
        security_handler.setFormatter(formatter)
        security_handler.setLevel(logging.WARNING)

        # Console handler (development only)
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.DEBUG)

        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(security_handler)

        # Add console handler only in development
        import os
        if os.getenv('FLASK_ENV', 'production') == 'development':
            self.logger.addHandler(console_handler)

        self.logger.setLevel(logging.DEBUG)

    def _add_context(self, message: str, extra_context: Dict[str, Any] = None) -> str:
        """Add context information to log message"""
        context = {
            'domain': self.domain,
            'timestamp': datetime.now().isoformat(),
            'logger': self.name
        }

        if extra_context:
            context.update(extra_context)

        # Try to get Flask request context
        try:
            from flask import request, g
            if request:
                context.update({
                    'method': request.method,
                    'path': request.path,
                    'remote_addr': request.remote_addr,
                    'user_agent': request.headers.get('User-Agent', 'Unknown')[:100]
                })

                if hasattr(g, 'user') and g.user:
                    context['user_id'] = getattr(g.user, 'id', 'unknown')
                    context['username'] = getattr(g.user, 'username', 'unknown')
        except:
            pass  # Outside Flask context

        return f"{message} | Context: {json.dumps(context, default=str)}"

    def debug(self, message: str, **kwargs):
        """Log debug message with context"""
        self.logger.debug(self._add_context(message, kwargs))

    def info(self, message: str, **kwargs):
        """Log info message with context"""
        self.logger.info(self._add_context(message, kwargs))

    def warning(self, message: str, **kwargs):
        """Log warning message with context"""
        self.logger.warning(self._add_context(message, kwargs))

    def error(self, message: str, exception: Exception = None, **kwargs):
        """Log error message with context and exception details"""
        if exception:
            # SECURITY: Use cryptographically secure hash instead of MD5
            import secrets
            # Generate secure hash for exception tracking without exposing details
            exc_hash = hashlib.sha256(str(exception).encode()).hexdigest()[:16]
            kwargs['exception_hash'] = exc_hash
            kwargs['exception_type'] = type(exception).__name__

            # In development, include more details
            import os
            if os.getenv('FLASK_ENV', 'production') == 'development':
                kwargs['exception_details'] = str(exception)

        self.logger.error(self._add_context(message, kwargs))

    def critical(self, message: str, **kwargs):
        """Log critical message with context"""
        self.logger.critical(self._add_context(message, kwargs))

    def security_event(self, event_type: str, details: Dict[str, Any]):
        """Log security event"""
        security_message = f"SECURITY_EVENT: {event_type}"

        # Sanitize security details
        sanitized_details = {}
        for key, value in details.items():
            if key.lower() in ['password', 'secret', 'token', 'api_key']:
                sanitized_details[key] = '***REDACTED***'
            else:
                sanitized_details[key] = value

        self.logger.warning(self._add_context(security_message, sanitized_details))

    def audit_log(self, action: str, resource: str, user_id: str = None, **kwargs):
        """Log audit event"""
        audit_message = f"AUDIT: {action} on {resource}"

        audit_context = {
            'action': action,
            'resource': resource,
            'user_id': user_id or 'system',
            **kwargs
        }

        self.logger.info(self._add_context(audit_message, audit_context))

class LogSanitizer:
    """Utility to sanitize existing log files"""

    def __init__(self):
        # SECURITY: Patterns to detect and redact sensitive information
        self.sensitive_patterns = [
            (r'password["\']?\s*[:=]\s*["\']?([^"\'\s,}]+)', r'password="***REDACTED***"'),
            (r'secret["\']?\s*[:=]\s*["\']?([^"\'\s,}]+)', r'secret="***REDACTED***"'),
            (r'token["\']?\s*[:=]\s*["\']?([^"\'\s,}]+)', r'token="***REDACTED***"'),
            (r'([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', r'\1***@\2'),
            (r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b', r'****-****-****-****'),
        ]

    def sanitize_file(self, file_path: str) -> bool:
        """Sanitize a log file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            original_content = content

            # Apply sanitization patterns
            for pattern, replacement in self.sensitive_patterns:
                content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)

            if content != original_content:
                # Backup original file
                backup_path = f"{file_path}.backup"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)

                # Write sanitized content
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                return True

            return False

        except Exception as e:
            print(f"Error sanitizing {file_path}: {e}")
            return False

    def sanitize_directory(self, directory: str) -> Dict[str, int]:
        """Sanitize all log files in directory"""
        results = {'files_processed': 0, 'files_sanitized': 0}

        log_dir = Path(directory)
        if not log_dir.exists():
            return results

        for log_file in log_dir.glob('*.log'):
            results['files_processed'] += 1
            if self.sanitize_file(str(log_file)):
                results['files_sanitized'] += 1

        return results

# Global secure loggers for each domain
business_logger = SecureLogger('business', 'business')
product_logger = SecureLogger('product', 'product')
shared_logger = SecureLogger('shared', 'shared')

def get_secure_logger(domain: str, name: str = None) -> SecureLogger:
    """Get secure logger for specific domain"""
    if domain == 'business':
        return business_logger if not name else SecureLogger(name, 'business')
    elif domain == 'product':
        return product_logger if not name else SecureLogger(name, 'product')
    else:
        return shared_logger if not name else SecureLogger(name, 'shared')

def setup_secure_logging_for_app(app, domain: str):
    """Setup secure logging for Flask app"""
    # Replace default Flask logger
    app.logger.handlers.clear()

    secure_logger = get_secure_logger(domain, 'app')

    # Add secure handlers to Flask logger
    for handler in secure_logger.logger.handlers:
        app.logger.addHandler(handler)

    app.logger.setLevel(logging.INFO)

    # Log app startup
    secure_logger.info(f"Secure logging initialized for {domain} domain")

    return secure_logger
