"""
LGPD Compliance Manager for DataHub Amigo One
Handles data protection, user rights, and compliance requirements
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class LGPDComplianceManager:
    """LGPD compliance management system"""
    
    def __init__(self, domain: str):
        self.domain = domain
        self.audit_log_path = Path(f'logs/compliance_{domain}.log')
        self.consent_db_path = Path(f'data/consent_{domain}.json')
        self.data_retention_config = self._load_retention_config()
        
        # Ensure directories exist
        self.audit_log_path.parent.mkdir(exist_ok=True)
        self.consent_db_path.parent.mkdir(exist_ok=True)
    
    def _load_retention_config(self) -> Dict[str, int]:
        """Load data retention configuration"""
        return {
            'user_data': 2555,  # 7 years in days (healthcare requirement)
            'session_logs': 90,  # 3 months
            'audit_logs': 2555,  # 7 years
            'analytics_data': 1095,  # 3 years
            'temporary_files': 30,  # 1 month
            'backup_data': 2555  # 7 years
        }
    
    def log_data_access(self, user_id: str, data_type: str, action: str, 
                       ip_address: str = None, user_agent: str = None) -> None:
        """Log data access for audit trail"""
        try:
            audit_entry = {
                'timestamp': datetime.now().isoformat(),
                'domain': self.domain,
                'user_id': user_id,
                'data_type': data_type,
                'action': action,
                'ip_address': ip_address,
                'user_agent': user_agent,
                'compliance_version': '1.0'
            }
            
            with open(self.audit_log_path, 'a', encoding='utf-8') as f:
                f.write(json.dumps(audit_entry) + '\n')
                
        except Exception as e:
            logger.error(f"Failed to log data access: {e}")
    
    def record_consent(self, user_id: str, consent_type: str, 
                      granted: bool, purpose: str) -> bool:
        """Record user consent"""
        try:
            # Load existing consents
            consents = self._load_consents()
            
            consent_record = {
                'user_id': user_id,
                'consent_type': consent_type,
                'granted': granted,
                'purpose': purpose,
                'timestamp': datetime.now().isoformat(),
                'domain': self.domain,
                'ip_address': self._get_request_ip(),
                'version': '1.0'
            }
            
            # Add to consents
            if user_id not in consents:
                consents[user_id] = []
            
            consents[user_id].append(consent_record)
            
            # Save consents
            self._save_consents(consents)
            
            # Log the consent action
            self.log_data_access(
                user_id, 
                'consent_record', 
                f'consent_{consent_type}_{"granted" if granted else "revoked"}'
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to record consent: {e}")
            return False
    
    def check_consent(self, user_id: str, consent_type: str) -> bool:
        """Check if user has granted specific consent"""
        try:
            consents = self._load_consents()
            user_consents = consents.get(user_id, [])
            
            # Get latest consent for this type
            latest_consent = None
            for consent in reversed(user_consents):
                if consent['consent_type'] == consent_type:
                    latest_consent = consent
                    break
            
            return latest_consent and latest_consent.get('granted', False)
            
        except Exception as e:
            logger.error(f"Failed to check consent: {e}")
            return False
    
    def export_user_data(self, user_id: str) -> Dict[str, Any]:
        """Export all user data (Right to Data Portability)"""
        try:
            self.log_data_access(user_id, 'user_export', 'data_portability_request')
            
            user_data = {
                'export_info': {
                    'user_id': user_id,
                    'domain': self.domain,
                    'export_date': datetime.now().isoformat(),
                    'format': 'JSON',
                    'version': '1.0'
                },
                'personal_data': self._get_user_personal_data(user_id),
                'activity_data': self._get_user_activity_data(user_id),
                'consent_history': self._get_user_consent_history(user_id),
                'data_processing_history': self._get_user_processing_history(user_id)
            }
            
            return user_data
            
        except Exception as e:
            logger.error(f"Failed to export user data: {e}")
            return {'error': 'Export failed'}
    
    def delete_user_data(self, user_id: str, deletion_reason: str = 'user_request') -> bool:
        """Delete user data (Right to Erasure)"""
        try:
            # Log deletion request
            self.log_data_access(user_id, 'user_deletion', f'erasure_request_{deletion_reason}')
            
            # Mark user for deletion (don't delete immediately for audit)
            deletion_record = {
                'user_id': user_id,
                'deletion_date': datetime.now().isoformat(),
                'reason': deletion_reason,
                'domain': self.domain,
                'status': 'scheduled',
                'retention_end_date': (datetime.now() + timedelta(days=30)).isoformat()
            }
            
            # Save deletion record
            deletions_path = Path(f'data/deletions_{self.domain}.json')
            deletions = self._load_json_file(deletions_path, {})
            deletions[user_id] = deletion_record
            self._save_json_file(deletions_path, deletions)
            
            # Schedule actual deletion (implement in background job)
            self._schedule_data_deletion(user_id, deletion_record)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete user data: {e}")
            return False
    
    def anonymize_user_data(self, user_id: str) -> bool:
        """Anonymize user data while preserving analytics"""
        try:
            self.log_data_access(user_id, 'user_anonymization', 'data_anonymization')
            
            # Create anonymized ID
            import hashlib
            anonymous_id = hashlib.sha256(f"{user_id}_{datetime.now()}".encode()).hexdigest()[:16]
            
            # Record anonymization
            anonymization_record = {
                'original_user_id': user_id,
                'anonymous_id': anonymous_id,
                'anonymization_date': datetime.now().isoformat(),
                'domain': self.domain
            }
            
            # Save anonymization record
            anon_path = Path(f'data/anonymizations_{self.domain}.json')
            anonymizations = self._load_json_file(anon_path, {})
            anonymizations[user_id] = anonymization_record
            self._save_json_file(anon_path, anonymizations)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to anonymize user data: {e}")
            return False
    
    def generate_compliance_report(self) -> Dict[str, Any]:
        """Generate LGPD compliance report"""
        try:
            # Count data subjects
            consents = self._load_consents()
            total_users = len(consents)
            
            # Count consent types
            consent_stats = {}
            for user_consents in consents.values():
                for consent in user_consents:
                    consent_type = consent['consent_type']
                    if consent_type not in consent_stats:
                        consent_stats[consent_type] = {'granted': 0, 'revoked': 0}
                    
                    if consent['granted']:
                        consent_stats[consent_type]['granted'] += 1
                    else:
                        consent_stats[consent_type]['revoked'] += 1
            
            # Load deletion requests
            deletions_path = Path(f'data/deletions_{self.domain}.json')
            deletions = self._load_json_file(deletions_path, {})
            
            # Load anonymizations
            anon_path = Path(f'data/anonymizations_{self.domain}.json')
            anonymizations = self._load_json_file(anon_path, {})
            
            report = {
                'report_info': {
                    'domain': self.domain,
                    'generated_date': datetime.now().isoformat(),
                    'period': 'all_time',
                    'version': '1.0'
                },
                'data_subjects': {
                    'total_users': total_users,
                    'active_consents': sum(stats['granted'] for stats in consent_stats.values()),
                    'revoked_consents': sum(stats['revoked'] for stats in consent_stats.values())
                },
                'consent_breakdown': consent_stats,
                'data_rights_requests': {
                    'deletion_requests': len(deletions),
                    'anonymization_requests': len(anonymizations),
                    'export_requests': self._count_export_requests()
                },
                'data_retention': self.data_retention_config,
                'compliance_status': 'compliant'
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate compliance report: {e}")
            return {'error': 'Report generation failed'}
    
    def _load_consents(self) -> Dict[str, List[Dict]]:
        """Load consent records"""
        return self._load_json_file(self.consent_db_path, {})
    
    def _save_consents(self, consents: Dict[str, List[Dict]]) -> None:
        """Save consent records"""
        self._save_json_file(self.consent_db_path, consents)
    
    def _load_json_file(self, file_path: Path, default: Any) -> Any:
        """Load JSON file with fallback"""
        try:
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load {file_path}: {e}")
        
        return default
    
    def _save_json_file(self, file_path: Path, data: Any) -> None:
        """Save data to JSON file"""
        try:
            file_path.parent.mkdir(exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to save {file_path}: {e}")
    
    def _get_request_ip(self) -> Optional[str]:
        """Get request IP address"""
        try:
            from flask import request
            return request.remote_addr
        except:
            return None
    
    def _get_user_personal_data(self, user_id: str) -> Dict[str, Any]:
        """Get user personal data"""
        # Implement based on your user data structure
        return {'user_id': user_id, 'note': 'Implement based on actual user data structure'}
    
    def _get_user_activity_data(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user activity data"""
        # Implement based on your activity logging
        return [{'note': 'Implement based on actual activity logging'}]
    
    def _get_user_consent_history(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user consent history"""
        consents = self._load_consents()
        return consents.get(user_id, [])
    
    def _get_user_processing_history(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user data processing history"""
        # Parse audit logs for this user
        try:
            processing_history = []
            if self.audit_log_path.exists():
                with open(self.audit_log_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            entry = json.loads(line.strip())
                            if entry.get('user_id') == user_id:
                                processing_history.append(entry)
                        except json.JSONDecodeError:
                            continue
            
            return processing_history
        except Exception as e:
            logger.error(f"Failed to get processing history: {e}")
            return []
    
    def _count_export_requests(self) -> int:
        """Count data export requests from audit logs"""
        try:
            count = 0
            if self.audit_log_path.exists():
                with open(self.audit_log_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            entry = json.loads(line.strip())
                            if entry.get('action') == 'data_portability_request':
                                count += 1
                        except json.JSONDecodeError:
                            continue
            return count
        except Exception as e:
            logger.error(f"Failed to count export requests: {e}")
            return 0
    
    def _schedule_data_deletion(self, user_id: str, deletion_record: Dict[str, Any]) -> None:
        """Schedule actual data deletion (implement with background job)"""
        # This would typically be implemented with a background job system
        # For now, just log the scheduling
        logger.info(f"Scheduled data deletion for user {user_id} in domain {self.domain}")

# Global compliance managers
business_compliance = LGPDComplianceManager('business')
product_compliance = LGPDComplianceManager('product')

def get_compliance_manager(domain: str) -> LGPDComplianceManager:
    """Get compliance manager for specific domain"""
    if domain == 'business':
        return business_compliance
    elif domain == 'product':
        return product_compliance
    else:
        return LGPDComplianceManager(domain)
