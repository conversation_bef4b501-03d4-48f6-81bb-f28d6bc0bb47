"""
SSL/HTTPS Middleware for DataHub Amigo One
Forces HTTPS connections and implements security headers
"""

import logging
from flask import request, redirect, url_for, g
from functools import wraps
from typing import Dict, Any

logger = logging.getLogger(__name__)

class SSLMiddleware:
    """SSL/HTTPS enforcement middleware"""

    def __init__(self, app=None, force_https=True):
        self.force_https = force_https
        self.security_headers = {
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;"
        }

        if app:
            self.init_app(app)

    def init_app(self, app):
        """Initialize SSL middleware with Flask app"""
        app.before_request(self.force_https_redirect)
        app.after_request(self.add_security_headers)

        # Configure SSL context for production
        if app.config.get('FORCE_HTTPS', False):
            app.config['PREFERRED_URL_SCHEME'] = 'https'

        logger.info("SSL middleware initialized")

    def force_https_redirect(self):
        """Force HTTPS redirect if enabled"""
        if not self.force_https:
            return

        # Skip for localhost and development
        if request.host.startswith('localhost') or request.host.startswith('127.0.0.1'):
            return

        # Skip for health checks
        if request.path in ['/health', '/status', '/ping']:
            return

        # Redirect HTTP to HTTPS - SECURITY: Use HTTPS instead of HTTP
        if not request.is_secure and request.headers.get('X-Forwarded-Proto') != 'https':
            logger.warning(f"Redirecting HTTP to HTTPS: {request.url}")
            https_url = request.url.replace('http://', 'https://', 1)
            return redirect(https_url, code=301)

    def add_security_headers(self, response):
        """Add security headers to all responses"""
        for header, value in self.security_headers.items():
            response.headers[header] = value

        # Add HSTS only for HTTPS
        if request.is_secure:
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'

        return response

def require_https(f):
    """Decorator to require HTTPS for specific endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not request.is_secure and not request.host.startswith(('localhost', '127.0.0.1')):
            logger.warning(f"HTTPS required for {request.endpoint}")
            https_url = request.url.replace('http://', 'https://', 1)
            return redirect(https_url, code=301)
        return f(*args, **kwargs)
    return decorated_function

def security_headers(additional_headers: Dict[str, str] = None):
    """Decorator to add custom security headers"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            response = f(*args, **kwargs)

            if additional_headers:
                for header, value in additional_headers.items():
                    response.headers[header] = value

            return response
        return decorated_function
    return decorator

class SecurityHeadersManager:
    """Manage security headers for different environments"""

    def __init__(self):
        self.development_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'SAMEORIGIN',  # Less restrictive for dev
            'X-XSS-Protection': '1; mode=block'
        }

        self.production_headers = {
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;",
            'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
        }

    def get_headers(self, is_production: bool = False) -> Dict[str, str]:
        """Get appropriate headers for environment"""
        return self.production_headers if is_production else self.development_headers

    def apply_headers(self, response, is_production: bool = False):
        """Apply security headers to response"""
        headers = self.get_headers(is_production)
        for header, value in headers.items():
            response.headers[header] = value
        return response

def create_ssl_context(cert_file: str = None, key_file: str = None):
    """Create SSL context for HTTPS"""
    try:
        import ssl

        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.minimum_version = ssl.TLSVersion.TLSv1_2

        # Configure cipher suites
        context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')

        if cert_file and key_file:
            context.load_cert_chain(cert_file, key_file)
            logger.info("SSL context created with custom certificates")
        else:
            # Use adhoc certificates for development
            logger.warning("Using adhoc SSL certificates for development")
            return 'adhoc'

        return context

    except Exception as e:
        logger.error(f"Failed to create SSL context: {e}")
        return None

def validate_ssl_config(app):
    """Validate SSL configuration"""
    issues = []

    # Check if HTTPS is enforced
    if not app.config.get('FORCE_HTTPS', False):
        issues.append("HTTPS not enforced")

    # Check session cookie security
    if not app.config.get('SESSION_COOKIE_SECURE', False):
        issues.append("Session cookies not secure")

    # Check if running on HTTP in production
    if app.config.get('ENV') == 'production' and not app.config.get('PREFERRED_URL_SCHEME') == 'https':
        issues.append("Production app not configured for HTTPS")

    return {
        'valid': len(issues) == 0,
        'issues': issues,
        'recommendations': [
            "Set FORCE_HTTPS=True in production",
            "Configure SESSION_COOKIE_SECURE=True",
            "Use proper SSL certificates",
            "Enable HSTS headers",
            "Configure CSP headers"
        ]
    }

# Global SSL middleware instances
ssl_middleware = SSLMiddleware()
security_headers_manager = SecurityHeadersManager()

def init_ssl_security(app, force_https: bool = None):
    """Initialize SSL security for Flask app"""
    if force_https is None:
        force_https = app.config.get('FORCE_HTTPS', False)

    # Initialize SSL middleware
    ssl_middleware.force_https = force_https
    ssl_middleware.init_app(app)

    # Apply security configuration
    session_config = app.config.get('SESSION_CONFIG', {})
    for key, value in session_config.items():
        app.config[key] = value

    logger.info(f"SSL security initialized (force_https={force_https})")

    return ssl_middleware
