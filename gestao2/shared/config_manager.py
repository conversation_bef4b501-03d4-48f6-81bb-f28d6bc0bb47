"""
Secure Configuration Manager for DataHub Amigo One
Handles secrets and environment variables securely
"""

import os
import secrets
import logging
from pathlib import Path
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class SecureConfigManager:
    """Secure configuration management with fallbacks"""

    def __init__(self, domain: str = 'shared'):
        self.domain = domain
        self._config_cache = {}
        self._secrets_cache = {}

    def get_secret_key(self) -> str:
        """Get or generate secure secret key"""
        # Try environment variable first
        secret_key = os.getenv('SECRET_KEY')
        if secret_key and len(secret_key) >= 32:
            return secret_key

        # Try domain-specific environment variable
        domain_secret = os.getenv(f'{self.domain.upper()}_SECRET_KEY')
        if domain_secret and len(domain_secret) >= 32:
            return domain_secret

        # Generate secure random key as fallback
        logger.warning(f"No secure SECRET_KEY found for {self.domain}, generating random key")
        generated_key = secrets.token_hex(32)

        # Save to environment for consistency during runtime
        os.environ[f'{self.domain.upper()}_SECRET_KEY'] = generated_key

        return generated_key

    def get_database_url(self) -> Optional[str]:
        """Get database URL with fallback"""
        # Try environment variable
        db_url = os.getenv('DATABASE_URL')
        if db_url:
            return db_url

        # Try domain-specific
        domain_db_url = os.getenv(f'{self.domain.upper()}_DATABASE_URL')
        if domain_db_url:
            return domain_db_url

        # Build from components
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '5432')
        db_name = os.getenv('DB_NAME', f'datahub_{self.domain}')
        db_user = os.getenv('DB_USER', 'datahub_user')
        db_password = os.getenv('DB_PASSWORD', '')

        if db_password:
            return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

        return None

    def get_config_value(self, key: str, default: Any = None, required: bool = False) -> Any:
        """Get configuration value with caching"""
        if key in self._config_cache:
            return self._config_cache[key]

        # Try environment variable
        value = os.getenv(key, default)

        if required and value is None:
            raise ValueError(f"Required configuration {key} not found")

        self._config_cache[key] = value
        return value

    def is_production(self) -> bool:
        """Check if running in production"""
        env = os.getenv('FLASK_ENV', 'development').lower()
        return env in ['production', 'prod']

    def is_debug_mode(self) -> bool:
        """Check if debug mode should be enabled"""
        if self.is_production():
            return False
        return os.getenv('FLASK_DEBUG', 'True').lower() == 'true'

    def get_cors_origins(self) -> list:
        """Get CORS allowed origins"""
        origins = os.getenv('CORS_ORIGINS', '')
        if origins:
            return [origin.strip() for origin in origins.split(',')]

        # Default safe origins for development
        if not self.is_production():
            return [
                'http://localhost:3000',
                'http://localhost:5000',
                'http://localhost:5001',
                'https://5mmmbhl1-5000.brs.devtunnels.ms',
                'https://5mmmbhl1-5001.brs.devtunnels.ms'
            ]

        return []

    def get_session_config(self) -> Dict[str, Any]:
        """Get session configuration"""
        return {
            'SESSION_COOKIE_SECURE': True,  # Always force HTTPS cookies
            'SESSION_COOKIE_HTTPONLY': True,
            'SESSION_COOKIE_SAMESITE': 'Lax',
            'PERMANENT_SESSION_LIFETIME': int(os.getenv('SESSION_TIMEOUT', '3600')),
            'SESSION_COOKIE_NAME': f'datahub_{self.domain}_session',
            'FORCE_HTTPS': self.is_production()  # Force HTTPS redirect in production
        }

    def get_security_config(self) -> Dict[str, Any]:
        """Get security configuration"""
        return {
            'CSRF_TOKEN_TIMEOUT': int(os.getenv('CSRF_TIMEOUT', '3600')),
            'MAX_CONTENT_LENGTH': int(os.getenv('MAX_UPLOAD_SIZE', '16777216')),  # 16MB
            'RATE_LIMIT_ENABLED': os.getenv('RATE_LIMIT_ENABLED', 'true').lower() == 'true',
            'RATE_LIMIT_DEFAULT': os.getenv('RATE_LIMIT_DEFAULT', '100 per hour'),
            'SECURITY_HEADERS_ENABLED': True
        }

    def validate_config(self) -> Dict[str, Any]:
        """Validate current configuration"""
        issues = []
        warnings = []

        # Check secret key
        secret_key = self.get_secret_key()
        if len(secret_key) < 32:
            issues.append("SECRET_KEY too short (minimum 32 characters)")

        # Check production settings
        if self.is_production():
            if self.is_debug_mode():
                issues.append("Debug mode enabled in production")

            if not os.getenv('SECRET_KEY'):
                warnings.append("Using generated SECRET_KEY in production")

        # Check database
        db_url = self.get_database_url()
        if not db_url:
            warnings.append("No database URL configured, using file fallback")

        return {
            'domain': self.domain,
            'is_production': self.is_production(),
            'issues': issues,
            'warnings': warnings,
            'config_valid': len(issues) == 0
        }

# Global instances for each domain
business_config = SecureConfigManager('business')
product_config = SecureConfigManager('product')
shared_config = SecureConfigManager('shared')

def get_config_manager(domain: str) -> SecureConfigManager:
    """Get configuration manager for specific domain"""
    if domain == 'business':
        return business_config
    elif domain == 'product':
        return product_config
    else:
        return shared_config
