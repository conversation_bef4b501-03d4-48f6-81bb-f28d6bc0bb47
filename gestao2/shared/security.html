<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Segurança Enterprise - DataHub Amigo One</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        systemBlue: '#007AFF',
                        systemGreen: '#34C759',
                        systemGray: {
                            DEFAULT: '#8E8E93',
                            light: '#AEAEB2',
                            lighter: '#C7C7CC',
                            lightest: '#D1D1D6',
                            extralight: '#E5E5EA',
                            ultralight: '#F2F2F7'
                        },
                        label: {
                            DEFAULT: '#000000',
                            secondary: 'rgba(60, 60, 67, 0.6)',
                            tertiary: 'rgba(60, 60, 67, 0.3)',
                            quaternary: 'rgba(60, 60, 67, 0.18)'
                        },
                        chartPurple: '#7B61FF',
                    },
                    borderRadius: {
                        'view': '10px',
                        'control': '7px'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        .hero-gradient {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-xl font-bold text-gray-900">DataHub Amigo One</a>
                    <span class="ml-2 text-sm text-gray-500">/ Segurança</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="text-gray-600 hover:text-systemBlue transition-colors">← Voltar</a>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        🔒 Enterprise Security
                    </span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-gradient text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="mb-6">
                <span class="text-6xl">🔒</span>
                <h1 class="text-5xl font-bold mb-4">Segurança Enterprise</h1>
                <p class="text-xl mb-8 opacity-90">Proteção de nível empresarial para dados médicos sensíveis</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-view p-4">
                    <div class="text-2xl mb-2">✅</div>
                    <h3 class="font-semibold mb-1">CSRF Protection</h3>
                    <p class="text-sm opacity-80">Implementado</p>
                </div>
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-view p-4">
                    <div class="text-2xl mb-2">🔐</div>
                    <h3 class="font-semibold mb-1">HTTPS Enforcement</h3>
                    <p class="text-sm opacity-80">SSL/TLS</p>
                </div>
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-view p-4">
                    <div class="text-2xl mb-2">🛡️</div>
                    <h3 class="font-semibold mb-1">Resource Integrity</h3>
                    <p class="text-sm opacity-80">Verificado</p>
                </div>
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-view p-4">
                    <div class="text-2xl mb-2">🎲</div>
                    <h3 class="font-semibold mb-1">Secure Random</h3>
                    <p class="text-sm opacity-80">Criptográfico</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Security Framework -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Framework de Segurança</h2>
                <p class="text-xl text-gray-600">Múltiplas camadas de proteção para máxima segurança</p>
            </div>

            <!-- Security Layers -->
            <div class="space-y-8">
                <!-- Layer 1: Network Security -->
                <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-view p-8">
                    <div class="flex items-center mb-6">
                        <div class="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center mr-4">
                            <span class="text-xl">🌐</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Camada 1: Segurança de Rede</h3>
                            <p class="text-gray-600">Proteção no nível de infraestrutura</p>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">🔐 HTTPS Enforcement</h4>
                            <p class="text-sm text-gray-600">Todas as comunicações criptografadas com TLS 1.3</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">🛡️ WAF Protection</h4>
                            <p class="text-sm text-gray-600">Web Application Firewall contra ataques comuns</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">⚡ DDoS Protection</h4>
                            <p class="text-sm text-gray-600">Rate limiting e proteção contra ataques distribuídos</p>
                        </div>
                    </div>
                </div>

                <!-- Layer 2: Application Security -->
                <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-view p-8">
                    <div class="flex items-center mb-6">
                        <div class="bg-green-500 text-white w-12 h-12 rounded-full flex items-center justify-center mr-4">
                            <span class="text-xl">🔧</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Camada 2: Segurança de Aplicação</h3>
                            <p class="text-gray-600">Proteção no código e funcionalidades</p>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">✅ CSRF Protection</h4>
                            <p class="text-sm text-gray-600">Tokens CSRF em todos os formulários</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">🧹 Input Validation</h4>
                            <p class="text-sm text-gray-600">Sanitização e validação de todos os inputs</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">🔒 XSS Protection</h4>
                            <p class="text-sm text-gray-600">Auto-escaping e Content Security Policy</p>
                        </div>
                    </div>
                </div>

                <!-- Layer 3: Data Security -->
                <div class="bg-gradient-to-r from-purple-50 to-purple-100 rounded-view p-8">
                    <div class="flex items-center mb-6">
                        <div class="bg-purple-500 text-white w-12 h-12 rounded-full flex items-center justify-center mr-4">
                            <span class="text-xl">🗃️</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Camada 3: Segurança de Dados</h3>
                            <p class="text-gray-600">Proteção de dados sensíveis e médicos</p>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">🔐 Encryption at Rest</h4>
                            <p class="text-sm text-gray-600">Dados criptografados no banco de dados</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">🎭 Data Anonymization</h4>
                            <p class="text-sm text-gray-600">Anonimização de dados médicos sensíveis</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">🔑 Access Control</h4>
                            <p class="text-sm text-gray-600">Controle granular de acesso aos dados</p>
                        </div>
                    </div>
                </div>

                <!-- Layer 4: Authentication & Authorization -->
                <div class="bg-gradient-to-r from-orange-50 to-orange-100 rounded-view p-8">
                    <div class="flex items-center mb-6">
                        <div class="bg-orange-500 text-white w-12 h-12 rounded-full flex items-center justify-center mr-4">
                            <span class="text-xl">👤</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Camada 4: Autenticação & Autorização</h3>
                            <p class="text-gray-600">Controle de identidade e acesso</p>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">🔐 Secure Hashing</h4>
                            <p class="text-sm text-gray-600">bcrypt com salt para senhas</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">🎫 Session Management</h4>
                            <p class="text-sm text-gray-600">Sessões seguras com timeout</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">👥 Role-Based Access</h4>
                            <p class="text-sm text-gray-600">RBAC com permissões granulares</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Compliance Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Compliance e Certificações</h2>
                <p class="text-xl text-gray-600">Conformidade com padrões internacionais de segurança</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- LGPD -->
                <div class="bg-white rounded-view p-8 shadow-sm card-hover transition-all duration-300">
                    <div class="text-center mb-6">
                        <div class="bg-green-100 text-green-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">🇧🇷</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">LGPD</h3>
                        <p class="text-gray-600">Lei Geral de Proteção de Dados</p>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="bg-green-100 text-green-600 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-1">
                                <span class="text-xs">✓</span>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Consentimento Explícito</h4>
                                <p class="text-sm text-gray-600">Coleta de dados apenas com consentimento</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-green-100 text-green-600 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-1">
                                <span class="text-xs">✓</span>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Direito ao Esquecimento</h4>
                                <p class="text-sm text-gray-600">Exclusão de dados quando solicitado</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-green-100 text-green-600 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-1">
                                <span class="text-xs">✓</span>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Portabilidade</h4>
                                <p class="text-sm text-gray-600">Exportação de dados em formato legível</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- HIPAA -->
                <div class="bg-white rounded-view p-8 shadow-sm card-hover transition-all duration-300">
                    <div class="text-center mb-6">
                        <div class="bg-blue-100 text-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">🏥</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">HIPAA</h3>
                        <p class="text-gray-600">Health Insurance Portability</p>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="bg-blue-100 text-blue-600 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-1">
                                <span class="text-xs">✓</span>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">PHI Protection</h4>
                                <p class="text-sm text-gray-600">Proteção de informações de saúde</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-blue-100 text-blue-600 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-1">
                                <span class="text-xs">✓</span>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Audit Trails</h4>
                                <p class="text-sm text-gray-600">Rastreamento completo de acessos</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-blue-100 text-blue-600 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-1">
                                <span class="text-xs">✓</span>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Access Controls</h4>
                                <p class="text-sm text-gray-600">Controles rigorosos de acesso</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ISO 27001 -->
                <div class="bg-white rounded-view p-8 shadow-sm card-hover transition-all duration-300">
                    <div class="text-center mb-6">
                        <div class="bg-purple-100 text-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">🔒</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">ISO 27001</h3>
                        <p class="text-gray-600">Information Security Management</p>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="bg-purple-100 text-purple-600 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-1">
                                <span class="text-xs">✓</span>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Risk Management</h4>
                                <p class="text-sm text-gray-600">Gestão sistemática de riscos</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-purple-100 text-purple-600 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-1">
                                <span class="text-xs">✓</span>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Security Controls</h4>
                                <p class="text-sm text-gray-600">Controles de segurança abrangentes</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-purple-100 text-purple-600 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-1">
                                <span class="text-xs">✓</span>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Continuous Improvement</h4>
                                <p class="text-sm text-gray-600">Melhoria contínua dos processos</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h3 class="text-lg font-semibold mb-4">DataHub Amigo One - Segurança Enterprise</h3>
            <p class="text-gray-400 text-sm mb-4">Proteção de nível empresarial para dados médicos sensíveis</p>
            <a href="index.html" class="inline-flex items-center text-systemBlue hover:text-blue-300 transition-colors">
                ← Voltar para a documentação principal
            </a>
        </div>
    </footer>

    <script>
        // Add scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        document.querySelectorAll('.card-hover').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
