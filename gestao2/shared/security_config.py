"""
Security Configuration for DataHub Amigo One
Secure password and credential management
"""

import os
import secrets
import logging
from typing import Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class SecurityConfig:
    """Secure configuration management for passwords and credentials"""
    
    def __init__(self):
        self.config_file = Path('.env.security')
        self.generated_passwords = {}
        
    def get_secure_password(self, key: str, default_length: int = 16) -> str:
        """Get secure password from environment or generate one"""
        # Check environment variable first
        password = os.getenv(key)
        
        if password:
            return password
        
        # Generate secure password if not found
        if key not in self.generated_passwords:
            self.generated_passwords[key] = secrets.token_urlsafe(default_length)
            logger.info(f"Generated secure password for {key}")
        
        return self.generated_passwords[key]
    
    def get_admin_credentials(self) -> Dict[str, Dict[str, Any]]:
        """Get admin user credentials securely"""
        return {
            'admin': {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': self.get_secure_password('ADMIN_PASSWORD'),
                'role': 'admin'
            },
            'TTK': {
                'username': 'TTK',
                'email': '<EMAIL>',
                'password': self.get_secure_password('TTK_PASSWORD'),
                'role': 'admin'
            },
            'bruno@abreu': {
                'username': 'bruno@abreu',
                'email': '<EMAIL>',
                'password': self.get_secure_password('BRUNO_ABREU_PASSWORD'),
                'role': 'admin'
            },
            'bruno@bruno': {
                'username': 'bruno@bruno',
                'email': '<EMAIL>',
                'password': self.get_secure_password('BRUNO_BRUNO_PASSWORD'),
                'role': 'admin'
            }
        }
    
    def get_sample_user_credentials(self) -> Dict[str, Dict[str, Any]]:
        """Get sample user credentials securely"""
        return {
            'sergio': {
                'username': 'sergio',
                'email': '<EMAIL>',
                'password': self.get_secure_password('SERGIO_PASSWORD'),
                'role': 'user'
            },
            'bruno': {
                'username': 'bruno',
                'email': '<EMAIL>',
                'password': self.get_secure_password('BRUNO_PASSWORD'),
                'role': 'user'
            },
            'gabi': {
                'username': 'gabi',
                'email': '<EMAIL>',
                'password': self.get_secure_password('GABI_PASSWORD'),
                'role': 'user'
            },
            'pedro': {
                'username': 'pedro',
                'email': '<EMAIL>',
                'password': self.get_secure_password('PEDRO_PASSWORD'),
                'role': 'user'
            }
        }
    
    def save_generated_passwords(self) -> bool:
        """Save generated passwords to secure file"""
        try:
            if not self.generated_passwords:
                return True
            
            # Create secure environment file
            with open(self.config_file, 'w') as f:
                f.write("# DataHub Amigo One - Security Configuration\n")
                f.write("# Generated passwords - KEEP THIS FILE SECURE!\n")
                f.write("# Add this file to .gitignore\n\n")
                
                for key, password in self.generated_passwords.items():
                    f.write(f"{key}={password}\n")
            
            # Set secure file permissions (Unix/Linux/macOS)
            try:
                os.chmod(self.config_file, 0o600)  # Read/write for owner only
            except:
                pass  # Windows doesn't support chmod
            
            logger.info(f"Generated passwords saved to {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving generated passwords: {e}")
            return False
    
    def load_from_file(self) -> bool:
        """Load configuration from secure file"""
        try:
            if not self.config_file.exists():
                return False
            
            with open(self.config_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '=' in line:
                            key, value = line.split('=', 1)
                            os.environ[key.strip()] = value.strip()
            
            logger.info(f"Security configuration loaded from {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading security configuration: {e}")
            return False
    
    def print_credentials_summary(self):
        """Print credentials summary for first-time setup"""
        print("\n🔐 DATAHUB AMIGO ONE - CREDENCIAIS DE ACESSO")
        print("=" * 60)
        
        print("\n👑 ADMINISTRADORES:")
        admin_creds = self.get_admin_credentials()
        for username, creds in admin_creds.items():
            env_key = f"{username.upper().replace('@', '_').replace('.', '_')}_PASSWORD"
            if env_key in self.generated_passwords:
                print(f"   • {username} / {creds['password']}")
            else:
                print(f"   • {username} / [definido via {env_key}]")
        
        print("\n👥 USUÁRIOS DE EXEMPLO:")
        sample_creds = self.get_sample_user_credentials()
        for username, creds in sample_creds.items():
            env_key = f"{username.upper()}_PASSWORD"
            if env_key in self.generated_passwords:
                print(f"   • {username} / {creds['password']}")
            else:
                print(f"   • {username} / [definido via {env_key}]")
        
        print("\n⚠️  IMPORTANTE:")
        print("   • Senhas hardcoded foram removidas por segurança")
        print("   • Configure variáveis de ambiente ou use as senhas geradas")
        print(f"   • Arquivo de configuração: {self.config_file}")
        print("   • Adicione .env.security ao .gitignore")
        
        if self.generated_passwords:
            print(f"\n💾 {len(self.generated_passwords)} senhas foram geradas automaticamente")
            print("   Execute save_generated_passwords() para salvar em arquivo")
    
    def generate_env_template(self) -> str:
        """Generate environment template file"""
        template = """# DataHub Amigo One - Environment Variables Template
# Copy this file to .env and set your secure passwords

# Admin Passwords
ADMIN_PASSWORD=your_secure_admin_password_here
TTK_PASSWORD=your_secure_ttk_password_here
BRUNO_ABREU_PASSWORD=your_secure_bruno_abreu_password_here
BRUNO_BRUNO_PASSWORD=your_secure_bruno_bruno_password_here

# Sample User Passwords
SERGIO_PASSWORD=your_secure_sergio_password_here
BRUNO_PASSWORD=your_secure_bruno_password_here
GABI_PASSWORD=your_secure_gabi_password_here
PEDRO_PASSWORD=your_secure_pedro_password_here

# Database Configuration
DATABASE_URL=sqlite:///datahub.db
SECRET_KEY=your_flask_secret_key_here

# Security Settings
FLASK_ENV=development
SECURITY_PASSWORD_SALT=your_password_salt_here

# Logging
LOG_LEVEL=INFO
LOG_FILE=datahub.log
"""
        return template
    
    def create_env_template(self) -> bool:
        """Create .env template file"""
        try:
            template_file = Path('.env.template')
            with open(template_file, 'w') as f:
                f.write(self.generate_env_template())
            
            logger.info(f"Environment template created: {template_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating environment template: {e}")
            return False

# Global security configuration instance
security_config = SecurityConfig()

def get_security_config() -> SecurityConfig:
    """Get global security configuration instance"""
    return security_config

def setup_secure_environment():
    """Setup secure environment configuration"""
    config = get_security_config()
    
    # Try to load existing configuration
    config.load_from_file()
    
    # Create environment template if it doesn't exist
    if not Path('.env.template').exists():
        config.create_env_template()
    
    return config

def get_secure_admin_credentials() -> Dict[str, Dict[str, Any]]:
    """Get secure admin credentials"""
    return security_config.get_admin_credentials()

def get_secure_sample_credentials() -> Dict[str, Dict[str, Any]]:
    """Get secure sample user credentials"""
    return security_config.get_sample_user_credentials()

if __name__ == "__main__":
    # Setup and display credentials
    config = setup_secure_environment()
    config.print_credentials_summary()
    
    # Save generated passwords
    if config.generated_passwords:
        config.save_generated_passwords()
        print(f"\n💾 Senhas salvas em {config.config_file}")
