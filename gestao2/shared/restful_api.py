"""
RESTful API Framework for DataHub Amigo One
Provides standardized REST endpoints with proper HTTP methods and status codes
"""

import logging
from functools import wraps
from typing import Dict, Any, Optional, List, Tuple
from flask import Blueprint, request, jsonify, g
from datetime import datetime

logger = logging.getLogger(__name__)

class RESTfulAPI:
    """RESTful API framework with standardized responses"""
    
    def __init__(self, name: str, url_prefix: str = '/api/v1'):
        self.blueprint = Blueprint(name, __name__, url_prefix=url_prefix)
        self.name = name
        
    def create_response(self, data: Any = None, message: str = None, 
                       status_code: int = 200, errors: List[str] = None) -> Tuple[Dict[str, Any], int]:
        """Create standardized API response"""
        response = {
            'success': 200 <= status_code < 300,
            'timestamp': datetime.now().isoformat(),
            'api_version': 'v1'
        }
        
        if data is not None:
            response['data'] = data
        
        if message:
            response['message'] = message
        
        if errors:
            response['errors'] = errors
        
        if status_code >= 400:
            response['error_code'] = status_code
        
        return response, status_code
    
    def handle_exceptions(self, f):
        """Decorator to handle exceptions in API endpoints"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                return f(*args, **kwargs)
            except ValueError as e:
                logger.warning(f"Validation error in {f.__name__}: {e}")
                return self.create_response(
                    message="Validation error",
                    errors=[str(e)],
                    status_code=400
                )
            except PermissionError as e:
                logger.warning(f"Permission error in {f.__name__}: {e}")
                return self.create_response(
                    message="Access denied",
                    errors=[str(e)],
                    status_code=403
                )
            except FileNotFoundError as e:
                logger.warning(f"Resource not found in {f.__name__}: {e}")
                return self.create_response(
                    message="Resource not found",
                    errors=[str(e)],
                    status_code=404
                )
            except Exception as e:
                logger.error(f"Unexpected error in {f.__name__}: {e}")
                return self.create_response(
                    message="Internal server error",
                    errors=["An unexpected error occurred"],
                    status_code=500
                )
        
        return decorated_function
    
    def require_auth(self, f):
        """Decorator to require authentication"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Check if user is authenticated
            if not hasattr(g, 'user') or not g.user:
                return self.create_response(
                    message="Authentication required",
                    status_code=401
                )
            
            return f(*args, **kwargs)
        
        return decorated_function
    
    def require_permission(self, permission: str):
        """Decorator to require specific permission"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                if not hasattr(g, 'user') or not g.user:
                    return self.create_response(
                        message="Authentication required",
                        status_code=401
                    )
                
                # Check permission (implement based on your permission system)
                if not self._check_permission(g.user, permission):
                    return self.create_response(
                        message=f"Permission '{permission}' required",
                        status_code=403
                    )
                
                return f(*args, **kwargs)
            
            return decorated_function
        return decorator
    
    def validate_json(self, schema: Dict[str, Any]):
        """Decorator to validate JSON input"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                if not request.is_json:
                    return self.create_response(
                        message="Content-Type must be application/json",
                        status_code=400
                    )
                
                data = request.get_json()
                if not data:
                    return self.create_response(
                        message="Invalid JSON data",
                        status_code=400
                    )
                
                # Validate against schema (implement validation logic)
                validation_result = self._validate_data(data, schema)
                if not validation_result['valid']:
                    return self.create_response(
                        message="Validation failed",
                        errors=validation_result['errors'],
                        status_code=400
                    )
                
                # Add validated data to kwargs
                kwargs['validated_data'] = validation_result['data']
                return f(*args, **kwargs)
            
            return decorated_function
        return decorator
    
    def paginate(self, default_per_page: int = 20, max_per_page: int = 100):
        """Decorator to add pagination support"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                try:
                    page = int(request.args.get('page', 1))
                    per_page = int(request.args.get('per_page', default_per_page))
                    
                    if page < 1:
                        page = 1
                    
                    if per_page > max_per_page:
                        per_page = max_per_page
                    
                    kwargs['page'] = page
                    kwargs['per_page'] = per_page
                    kwargs['offset'] = (page - 1) * per_page
                    
                    return f(*args, **kwargs)
                    
                except ValueError:
                    return self.create_response(
                        message="Invalid pagination parameters",
                        status_code=400
                    )
            
            return decorated_function
        return decorator
    
    def rate_limit(self, requests_per_minute: int = 60):
        """Decorator to add rate limiting"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                # Implement rate limiting logic
                client_ip = request.remote_addr
                
                if self._is_rate_limited(client_ip, requests_per_minute):
                    return self.create_response(
                        message="Rate limit exceeded",
                        status_code=429
                    )
                
                return f(*args, **kwargs)
            
            return decorated_function
        return decorator
    
    def log_api_call(self, f):
        """Decorator to log API calls"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            start_time = datetime.now()
            
            # Log request
            logger.info(f"API Call: {request.method} {request.path} from {request.remote_addr}")
            
            try:
                result = f(*args, **kwargs)
                
                # Log successful response
                duration = (datetime.now() - start_time).total_seconds()
                logger.info(f"API Response: {request.method} {request.path} - Success in {duration:.3f}s")
                
                return result
                
            except Exception as e:
                # Log error
                duration = (datetime.now() - start_time).total_seconds()
                logger.error(f"API Error: {request.method} {request.path} - {str(e)} in {duration:.3f}s")
                raise
        
        return decorated_function
    
    def _check_permission(self, user: Any, permission: str) -> bool:
        """Check if user has permission"""
        # Implement based on your permission system
        if hasattr(user, 'permissions'):
            return permission in user.permissions
        
        if hasattr(user, 'role'):
            # Simple role-based check
            admin_permissions = ['users.create', 'users.read', 'users.update', 'users.delete', 'admin.access']
            manager_permissions = ['users.read', 'users.update', 'data.read', 'data.update']
            analyst_permissions = ['data.read', 'reports.read']
            viewer_permissions = ['data.read']
            
            role_permissions = {
                'admin': admin_permissions,
                'manager': manager_permissions,
                'analyst': analyst_permissions,
                'viewer': viewer_permissions
            }
            
            user_permissions = role_permissions.get(user.role, [])
            return permission in user_permissions
        
        return False
    
    def _validate_data(self, data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data against schema"""
        # Simple validation implementation
        errors = []
        validated_data = {}
        
        for field, rules in schema.items():
            value = data.get(field)
            
            # Check required fields
            if rules.get('required', False) and not value:
                errors.append(f"{field} is required")
                continue
            
            # Type validation
            expected_type = rules.get('type')
            if value is not None and expected_type:
                if expected_type == 'string' and not isinstance(value, str):
                    errors.append(f"{field} must be a string")
                elif expected_type == 'integer' and not isinstance(value, int):
                    errors.append(f"{field} must be an integer")
                elif expected_type == 'float' and not isinstance(value, (int, float)):
                    errors.append(f"{field} must be a number")
                elif expected_type == 'boolean' and not isinstance(value, bool):
                    errors.append(f"{field} must be a boolean")
                elif expected_type == 'list' and not isinstance(value, list):
                    errors.append(f"{field} must be a list")
                elif expected_type == 'dict' and not isinstance(value, dict):
                    errors.append(f"{field} must be an object")
            
            if value is not None:
                validated_data[field] = value
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'data': validated_data
        }
    
    def _is_rate_limited(self, client_ip: str, requests_per_minute: int) -> bool:
        """Check if client is rate limited"""
        # Simple in-memory rate limiting (implement with Redis for production)
        if not hasattr(self, '_rate_limit_cache'):
            self._rate_limit_cache = {}
        
        now = datetime.now()
        minute_key = now.strftime('%Y-%m-%d-%H-%M')
        cache_key = f"{client_ip}:{minute_key}"
        
        current_requests = self._rate_limit_cache.get(cache_key, 0)
        
        if current_requests >= requests_per_minute:
            return True
        
        self._rate_limit_cache[cache_key] = current_requests + 1
        
        # Clean old entries
        keys_to_remove = []
        for key in self._rate_limit_cache:
            if not key.startswith(f"{client_ip}:{now.strftime('%Y-%m-%d-%H')}"):
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self._rate_limit_cache[key]
        
        return False

class UserAPI(RESTfulAPI):
    """User management API endpoints"""
    
    def __init__(self):
        super().__init__('user_api', '/api/v1/users')
        self.register_routes()
    
    def register_routes(self):
        """Register user API routes"""
        
        @self.blueprint.route('', methods=['GET'])
        @self.handle_exceptions
        @self.require_permission('users.read')
        @self.paginate()
        @self.log_api_call
        def list_users(page: int, per_page: int, offset: int):
            """List users with pagination"""
            # Implement user listing logic
            users = []  # Get from database
            total = 0   # Get total count
            
            return self.create_response(
                data={
                    'users': users,
                    'pagination': {
                        'page': page,
                        'per_page': per_page,
                        'total': total,
                        'pages': (total + per_page - 1) // per_page
                    }
                }
            )
        
        @self.blueprint.route('/<int:user_id>', methods=['GET'])
        @self.handle_exceptions
        @self.require_permission('users.read')
        @self.log_api_call
        def get_user(user_id: int):
            """Get specific user"""
            # Implement user retrieval logic
            user = None  # Get from database
            
            if not user:
                return self.create_response(
                    message="User not found",
                    status_code=404
                )
            
            return self.create_response(data={'user': user})
        
        @self.blueprint.route('', methods=['POST'])
        @self.handle_exceptions
        @self.require_permission('users.create')
        @self.validate_json({
            'username': {'type': 'string', 'required': True},
            'email': {'type': 'string', 'required': True},
            'role': {'type': 'string', 'required': True}
        })
        @self.log_api_call
        def create_user(validated_data: Dict[str, Any]):
            """Create new user"""
            # Implement user creation logic
            new_user = validated_data  # Create in database
            
            return self.create_response(
                data={'user': new_user},
                message="User created successfully",
                status_code=201
            )
        
        @self.blueprint.route('/<int:user_id>', methods=['PUT'])
        @self.handle_exceptions
        @self.require_permission('users.update')
        @self.validate_json({
            'email': {'type': 'string', 'required': False},
            'role': {'type': 'string', 'required': False}
        })
        @self.log_api_call
        def update_user(user_id: int, validated_data: Dict[str, Any]):
            """Update user"""
            # Implement user update logic
            updated_user = validated_data  # Update in database
            
            return self.create_response(
                data={'user': updated_user},
                message="User updated successfully"
            )
        
        @self.blueprint.route('/<int:user_id>', methods=['DELETE'])
        @self.handle_exceptions
        @self.require_permission('users.delete')
        @self.log_api_call
        def delete_user(user_id: int):
            """Delete user"""
            # Implement user deletion logic
            # deleted = delete_from_database(user_id)
            
            return self.create_response(
                message="User deleted successfully",
                status_code=204
            )

def create_restful_apis() -> List[Blueprint]:
    """Create all RESTful API blueprints"""
    apis = [
        UserAPI()
    ]
    
    return [api.blueprint for api in apis]
