#!/usr/bin/env python
"""
DataHub Amigo One - DataMesh Setup Script
Initial setup for DataMesh architecture with Business and Product domains
"""

import os
import sys
import shutil
import logging
from pathlib import Path
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('datamesh_setup.log')
    ]
)
logger = logging.getLogger(__name__)

class DataMeshSetup:
    """Setup utility for DataMesh architecture"""
    
    def __init__(self):
        """Initialize the setup"""
        self.root_dir = Path(__file__).parent.parent.parent.parent
        self.shared_dir = self.root_dir / 'shared' / 'datamesh-core'
        self.business_dir = self.root_dir / 'gestao'
        self.product_dir = self.root_dir / 'produto' / 'uz-gabi'
        
    def setup_shared_core(self):
        """Setup shared core infrastructure"""
        logger.info("Setting up shared DataMesh core...")
        
        try:
            # Create necessary directories
            directories = [
                self.shared_dir / 'database',
                self.shared_dir / 'config', 
                self.shared_dir / 'security',
                self.shared_dir / 'utils',
                self.shared_dir / 'monitoring',
                self.shared_dir / 'scripts'
            ]
            
            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)
                
                # Create __init__.py files
                init_file = directory / '__init__.py'
                if not init_file.exists():
                    init_file.write_text('"""DataMesh Core Module"""')
            
            logger.info("Shared core directories created successfully")
            
            # Copy environment template
            env_example = self.shared_dir / 'config' / '.env.example'
            if env_example.exists():
                # Copy to business domain
                business_env = self.business_dir / '.env.example'
                if not business_env.exists():
                    shutil.copy2(env_example, business_env)
                    logger.info("Environment template copied to business domain")
                
                # Copy to product domain
                product_env = self.product_dir / '.env.example'
                if not product_env.exists():
                    shutil.copy2(env_example, product_env)
                    logger.info("Environment template copied to product domain")
            
        except Exception as e:
            logger.error(f"Failed to setup shared core: {e}")
            raise
    
    def setup_business_domain(self):
        """Setup business domain configuration"""
        logger.info("Setting up Business domain...")
        
        try:
            # Create business-specific directories
            directories = [
                self.business_dir / 'logs',
                self.business_dir / 'data',
                self.business_dir / 'exports'
            ]
            
            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)
            
            # Create business domain .env if it doesn't exist
            env_file = self.business_dir / '.env'
            if not env_file.exists():
                env_content = """# Business Domain Configuration
FLASK_ENV=development
DOMAIN_NAME=business
BUSINESS_PORT=5000
BUSINESS_DB_SCHEMA=business
DATA_SOURCE_MODE=files

# Database (will be configured by DB team)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=amigo_datahub
DB_USER=app_user
DB_PASSWORD=

# Cache
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300

# Security
SECRET_KEY=business_secret_key_change_in_production
SESSION_COOKIE_SECURE=false

# Features
ENABLE_RESPONSIBLE_ANALYSIS=true
ENABLE_CLASS_ANALYSIS=true
ENABLE_COUPON_ANALYSIS=true
ENABLE_DATA_QUALITY_ANALYSIS=true

# Integration
PRODUCT_APP_URL=http://localhost:5001
"""
                env_file.write_text(env_content)
                logger.info("Business domain .env file created")
            
            logger.info("Business domain setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup business domain: {e}")
            raise
    
    def setup_product_domain(self):
        """Setup product domain configuration"""
        logger.info("Setting up Product domain...")
        
        try:
            # Create product-specific directories
            directories = [
                self.product_dir / 'logs',
                self.product_dir / 'data',
                self.product_dir / 'exports'
            ]
            
            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)
            
            # Create product domain .env if it doesn't exist
            env_file = self.product_dir / '.env'
            if not env_file.exists():
                env_content = """# Product Domain Configuration
FLASK_ENV=development
DOMAIN_NAME=product
PRODUCT_PORT=5001
PRODUCT_DB_SCHEMA=product
DATA_SOURCE_MODE=mock

# Database (will be configured by DB team)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=amigo_datahub
DB_USER=app_user
DB_PASSWORD=

# Cache
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300

# Security
SECRET_KEY=product_secret_key_change_in_production
SESSION_COOKIE_SECURE=false

# Features
ENABLE_USER_ANALYTICS=true
ENABLE_FEATURE_TRACKING=true
ENABLE_PAYMENT_ANALYTICS=true

# Integration
BUSINESS_APP_URL=https://5mmmbhl1-5000.brs.devtunnels.ms/
"""
                env_file.write_text(env_content)
                logger.info("Product domain .env file created")
            
            logger.info("Product domain setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup product domain: {e}")
            raise
    
    def create_domain_mapping_sql(self):
        """Create SQL script for domain mapping table"""
        logger.info("Creating domain mapping SQL script...")
        
        try:
            sql_content = """-- DataMesh Domain Mapping Schema
-- Creates the shared schema and domain mapping table

-- Create shared schema
CREATE SCHEMA IF NOT EXISTS shared;

-- Set search path
SET search_path TO shared;

-- Create domain mapping table
CREATE TABLE IF NOT EXISTS domain_mapping (
    id SERIAL PRIMARY KEY,
    business_entity_id VARCHAR(255) NOT NULL,
    business_entity_type VARCHAR(50) NOT NULL,
    product_user_id VARCHAR(255),
    product_entity_id VARCHAR(255),
    product_entity_type VARCHAR(50),
    mapping_status VARCHAR(20) DEFAULT 'active',
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE(business_entity_id, business_entity_type, product_user_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_domain_mapping_business 
    ON domain_mapping(business_entity_id, business_entity_type);
CREATE INDEX IF NOT EXISTS idx_domain_mapping_product 
    ON domain_mapping(product_user_id, product_entity_type);
CREATE INDEX IF NOT EXISTS idx_domain_mapping_status 
    ON domain_mapping(mapping_status);
CREATE INDEX IF NOT EXISTS idx_domain_mapping_created 
    ON domain_mapping(created_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_domain_mapping_updated_at ON domain_mapping;
CREATE TRIGGER update_domain_mapping_updated_at 
    BEFORE UPDATE ON domain_mapping 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create audit log table
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    domain VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id VARCHAR(255) NOT NULL,
    action VARCHAR(50) NOT NULL,
    user_id VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index for audit logs
CREATE INDEX IF NOT EXISTS idx_audit_logs_domain 
    ON audit_logs(domain, entity_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created 
    ON audit_logs(created_at);

-- Create system metrics table
CREATE TABLE IF NOT EXISTS system_metrics (
    id SERIAL PRIMARY KEY,
    domain VARCHAR(50) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4),
    metric_unit VARCHAR(20),
    metadata JSONB,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index for system metrics
CREATE INDEX IF NOT EXISTS idx_system_metrics_domain_name 
    ON system_metrics(domain, metric_name);
CREATE INDEX IF NOT EXISTS idx_system_metrics_recorded 
    ON system_metrics(recorded_at);

-- Grant permissions (to be executed by DB team)
-- GRANT USAGE ON SCHEMA shared TO app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA shared TO app_user;
-- GRANT USAGE ON ALL SEQUENCES IN SCHEMA shared TO app_user;

COMMENT ON SCHEMA shared IS 'Shared schema for DataMesh cross-domain functionality';
COMMENT ON TABLE domain_mapping IS 'Maps entities between Business and Product domains';
COMMENT ON TABLE audit_logs IS 'Audit trail for all domain operations';
COMMENT ON TABLE system_metrics IS 'System performance and business metrics';
"""
            
            sql_file = self.shared_dir / 'scripts' / 'create_domain_mapping.sql'
            sql_file.write_text(sql_content)
            
            logger.info(f"Domain mapping SQL script created: {sql_file}")
            
        except Exception as e:
            logger.error(f"Failed to create domain mapping SQL: {e}")
            raise
    
    def create_readme_files(self):
        """Create README files for each domain"""
        logger.info("Creating README files...")
        
        try:
            # Business domain README
            business_readme = self.business_dir / 'DATAMESH_README.md'
            business_content = """# DataHub Amigo One - Business Domain

## Configuração DataMesh

Este domínio é responsável pela gestão do funil comercial e operacional.

### Configuração Inicial

1. Copie o arquivo `.env.example` para `.env`
2. Configure as variáveis de ambiente
3. Execute: `pip install -r requirements.txt`
4. Execute: `python run.py`

### Integração com Produto

- URL do Produto: http://localhost:5001
- Chave de conexão: `implementation_id` → `user_email`

### Responsabilidades

- Leads e Oportunidades
- Implantações e Responsáveis
- Universidades e Turmas
- Análise de Conversão
- Qualidade de Dados
"""
            business_readme.write_text(business_content)
            
            # Product domain README
            product_readme = self.product_dir / 'DATAMESH_README.md'
            product_content = """# DataHub Amigo One - Product Domain

## Configuração DataMesh

Este domínio é responsável pela experiência do usuário e features do produto.

### Configuração Inicial

1. Copie o arquivo `.env.example` para `.env`
2. Configure as variáveis de ambiente
3. Execute: `pip install -r requirements.txt`
4. Execute: `python app.py`

### Integração com Negócios

- URL de Negócios: https://5mmmbhl1-5000.brs.devtunnels.ms/
- Chave de conexão: `user_email` → `implementation_id`

### Responsabilidades

- Usuários e Perfis
- Features e Analytics
- Pagamentos e Transações
- Agenda e Prontuários
- Rede Social Médica
"""
            product_readme.write_text(product_content)
            
            logger.info("README files created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create README files: {e}")
            raise
    
    def run_setup(self):
        """Run complete DataMesh setup"""
        logger.info("Starting DataMesh setup...")
        
        try:
            self.setup_shared_core()
            self.setup_business_domain()
            self.setup_product_domain()
            self.create_domain_mapping_sql()
            self.create_readme_files()
            
            logger.info("DataMesh setup completed successfully!")
            
            # Print next steps
            print("\n" + "="*60)
            print("🎉 DataMesh Setup Completed Successfully!")
            print("="*60)
            print("\n📋 Next Steps:")
            print("1. Configure database connection with DB team")
            print("2. Review and update .env files in each domain")
            print("3. Install dependencies: pip install -r shared/datamesh-core/requirements.txt")
            print("4. Run domain mapping SQL script (coordinate with DB team)")
            print("5. Start Business domain: cd gestao && python run.py")
            print("6. Start Product domain: cd produto/uz-gabi && python app.py")
            print("\n📚 Documentation:")
            print("- Architecture: shared/datamesh-core/DATAMESH_ARCHITECTURE.md")
            print("- Security: shared/datamesh-core/security/SECURITY_PERFORMANCE_GUIDE.md")
            print("- Business: gestao/DATAMESH_README.md")
            print("- Product: produto/uz-gabi/DATAMESH_README.md")
            print("\n🔗 URLs:")
            print("- Business Domain: https://5mmmbhl1-5000.brs.devtunnels.ms/")
            print("- Product Domain: http://localhost:5001")
            
        except Exception as e:
            logger.error(f"Setup failed: {e}")
            raise

def main():
    """Main setup function"""
    setup = DataMeshSetup()
    setup.run_setup()

if __name__ == '__main__':
    main()
