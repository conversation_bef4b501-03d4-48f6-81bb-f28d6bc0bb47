#!/usr/bin/env python
"""
DataHub Amigo One - Data Migration Script
Migrates data from CSV/Excel files to PostgreSQL database
"""

import os
import sys
import logging
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.services.database_service import DatabaseService
from app.services.data_loader import DataLoader
from app.config import config_by_name

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('migration.log')
    ]
)
logger = logging.getLogger(__name__)

class DataMigrator:
    """
    Data migration utility for moving data from files to PostgreSQL
    """
    
    def __init__(self, config_name='development'):
        """Initialize the migrator"""
        self.config = config_by_name[config_name]
        self.database_service = None
        self.file_loader = None
        self._initialize_services()
    
    def _initialize_services(self):
        """Initialize database and file services"""
        try:
            # Initialize database service
            self.database_service = DatabaseService()
            logger.info("Database service initialized")
            
            # Initialize file loader
            self.file_loader = DataLoader()
            logger.info("File loader initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            raise
    
    def create_schema(self):
        """Create database schema and tables"""
        try:
            logger.info("Creating database schema...")
            
            # Schema creation SQL
            schema_sql = f"""
            -- Create schema if not exists
            CREATE SCHEMA IF NOT EXISTS {self.config.DB_SCHEMA};
            
            -- Set search path
            SET search_path TO {self.config.DB_SCHEMA};
            
            -- Create tables
            
            -- Universidades
            CREATE TABLE IF NOT EXISTS universidades (
                id SERIAL PRIMARY KEY,
                nome VARCHAR(255) NOT NULL UNIQUE,
                cidade VARCHAR(100),
                estado VARCHAR(2),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Produtos
            CREATE TABLE IF NOT EXISTS produtos (
                id SERIAL PRIMARY KEY,
                nome VARCHAR(255) NOT NULL UNIQUE,
                valor_base DECIMAL(10,2),
                descricao TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Cursos
            CREATE TABLE IF NOT EXISTS cursos (
                id SERIAL PRIMARY KEY,
                nome VARCHAR(255) NOT NULL,
                universidade_id INTEGER REFERENCES universidades(id),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Turmas
            CREATE TABLE IF NOT EXISTS turmas (
                id SERIAL PRIMARY KEY,
                nome VARCHAR(255) NOT NULL,
                curso_id INTEGER REFERENCES cursos(id),
                periodo VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Responsaveis
            CREATE TABLE IF NOT EXISTS responsaveis (
                id SERIAL PRIMARY KEY,
                nome VARCHAR(255) NOT NULL,
                tipo VARCHAR(50) NOT NULL,
                email VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Leads
            CREATE TABLE IF NOT EXISTS leads (
                id SERIAL PRIMARY KEY,
                nome VARCHAR(255) NOT NULL,
                email VARCHAR(255),
                telefone VARCHAR(20),
                data_criacao TIMESTAMP,
                formacao_academica VARCHAR(100),
                cidade VARCHAR(100),
                estado VARCHAR(2),
                universidade_id INTEGER REFERENCES universidades(id),
                curso_id INTEGER REFERENCES cursos(id),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Oportunidades
            CREATE TABLE IF NOT EXISTS oportunidades (
                id SERIAL PRIMARY KEY,
                lead_id INTEGER REFERENCES leads(id),
                data_criacao TIMESTAMP,
                etapa_funil VARCHAR(100),
                produto_id INTEGER REFERENCES produtos(id),
                valor_mensalidade DECIMAL(10,2),
                status VARCHAR(50) DEFAULT 'Ativo',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Implantacoes
            CREATE TABLE IF NOT EXISTS implantacoes (
                id SERIAL PRIMARY KEY,
                oportunidade_id INTEGER REFERENCES oportunidades(id),
                data_criacao TIMESTAMP,
                data_finalizacao TIMESTAMP,
                fase_implantacao VARCHAR(100),
                status_implantacao VARCHAR(100),
                turma_id INTEGER REFERENCES turmas(id),
                responsavel_onboarding_id INTEGER REFERENCES responsaveis(id),
                responsavel_ongoing_id INTEGER REFERENCES responsaveis(id),
                responsavel_contabil_id INTEGER REFERENCES responsaveis(id),
                responsavel_societario_id INTEGER REFERENCES responsaveis(id),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Cupons
            CREATE TABLE IF NOT EXISTS cupons (
                id SERIAL PRIMARY KEY,
                implantacao_id INTEGER REFERENCES implantacoes(id),
                codigo VARCHAR(100),
                data_colacao TIMESTAMP,
                pagamento_entrada DECIMAL(10,2),
                isencao_meses INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Create indexes for performance
            CREATE INDEX IF NOT EXISTS idx_leads_universidade ON leads(universidade_id);
            CREATE INDEX IF NOT EXISTS idx_leads_data_criacao ON leads(data_criacao);
            CREATE INDEX IF NOT EXISTS idx_oportunidades_lead ON oportunidades(lead_id);
            CREATE INDEX IF NOT EXISTS idx_oportunidades_data_criacao ON oportunidades(data_criacao);
            CREATE INDEX IF NOT EXISTS idx_implantacoes_oportunidade ON implantacoes(oportunidade_id);
            CREATE INDEX IF NOT EXISTS idx_implantacoes_status ON implantacoes(status_implantacao);
            CREATE INDEX IF NOT EXISTS idx_implantacoes_data_finalizacao ON implantacoes(data_finalizacao);
            
            -- Create updated_at trigger function
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ language 'plpgsql';
            
            -- Create triggers for updated_at
            DROP TRIGGER IF EXISTS update_universidades_updated_at ON universidades;
            CREATE TRIGGER update_universidades_updated_at BEFORE UPDATE ON universidades FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            
            DROP TRIGGER IF EXISTS update_produtos_updated_at ON produtos;
            CREATE TRIGGER update_produtos_updated_at BEFORE UPDATE ON produtos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            
            DROP TRIGGER IF EXISTS update_cursos_updated_at ON cursos;
            CREATE TRIGGER update_cursos_updated_at BEFORE UPDATE ON cursos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            
            DROP TRIGGER IF EXISTS update_turmas_updated_at ON turmas;
            CREATE TRIGGER update_turmas_updated_at BEFORE UPDATE ON turmas FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            
            DROP TRIGGER IF EXISTS update_responsaveis_updated_at ON responsaveis;
            CREATE TRIGGER update_responsaveis_updated_at BEFORE UPDATE ON responsaveis FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            
            DROP TRIGGER IF EXISTS update_leads_updated_at ON leads;
            CREATE TRIGGER update_leads_updated_at BEFORE UPDATE ON leads FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            
            DROP TRIGGER IF EXISTS update_oportunidades_updated_at ON oportunidades;
            CREATE TRIGGER update_oportunidades_updated_at BEFORE UPDATE ON oportunidades FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            
            DROP TRIGGER IF EXISTS update_implantacoes_updated_at ON implantacoes;
            CREATE TRIGGER update_implantacoes_updated_at BEFORE UPDATE ON implantacoes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            
            DROP TRIGGER IF EXISTS update_cupons_updated_at ON cupons;
            CREATE TRIGGER update_cupons_updated_at BEFORE UPDATE ON cupons FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            """
            
            # Execute schema creation
            with self.database_service.get_session() as session:
                for statement in schema_sql.split(';'):
                    if statement.strip():
                        session.execute(statement)
            
            logger.info("Database schema created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create schema: {e}")
            raise
    
    def migrate_dimension_tables(self):
        """Migrate dimension tables (universidades, produtos, responsaveis)"""
        try:
            logger.info("Migrating dimension tables...")
            
            # Get dimension tables from files
            dimension_tables = self.file_loader.get_dimension_tables()
            
            for table_name, df in dimension_tables.items():
                if df.empty:
                    logger.warning(f"Dimension table {table_name} is empty, skipping")
                    continue
                
                logger.info(f"Migrating {table_name} with {len(df)} records")
                
                # Map table names to database table names
                db_table_name = table_name
                if table_name == 'universidades':
                    self._migrate_universidades(df)
                elif table_name == 'produtos':
                    self._migrate_produtos(df)
                elif table_name == 'responsaveis':
                    self._migrate_responsaveis(df)
                else:
                    logger.warning(f"Unknown dimension table: {table_name}")
            
            logger.info("Dimension tables migration completed")
            
        except Exception as e:
            logger.error(f"Failed to migrate dimension tables: {e}")
            raise
    
    def _migrate_universidades(self, df: pd.DataFrame):
        """Migrate universidades table"""
        try:
            # Prepare data
            records = []
            for _, row in df.iterrows():
                record = {
                    'nome': row.get('nome', ''),
                    'cidade': row.get('cidade', ''),
                    'estado': row.get('estado', '')
                }
                records.append(record)
            
            # Insert data
            if records:
                insert_sql = """
                INSERT INTO universidades (nome, cidade, estado)
                VALUES (%(nome)s, %(cidade)s, %(estado)s)
                ON CONFLICT (nome) DO UPDATE SET
                    cidade = EXCLUDED.cidade,
                    estado = EXCLUDED.estado,
                    updated_at = CURRENT_TIMESTAMP
                """
                
                with self.database_service.get_session() as session:
                    session.execute(insert_sql, records)
                
                logger.info(f"Migrated {len(records)} universidades")
            
        except Exception as e:
            logger.error(f"Failed to migrate universidades: {e}")
            raise
    
    def _migrate_produtos(self, df: pd.DataFrame):
        """Migrate produtos table"""
        try:
            # Prepare data
            records = []
            for _, row in df.iterrows():
                record = {
                    'nome': row.get('nome', ''),
                    'valor_base': row.get('valor_base', 0),
                    'descricao': row.get('descricao', '')
                }
                records.append(record)
            
            # Insert data
            if records:
                insert_sql = """
                INSERT INTO produtos (nome, valor_base, descricao)
                VALUES (%(nome)s, %(valor_base)s, %(descricao)s)
                ON CONFLICT (nome) DO UPDATE SET
                    valor_base = EXCLUDED.valor_base,
                    descricao = EXCLUDED.descricao,
                    updated_at = CURRENT_TIMESTAMP
                """
                
                with self.database_service.get_session() as session:
                    session.execute(insert_sql, records)
                
                logger.info(f"Migrated {len(records)} produtos")
            
        except Exception as e:
            logger.error(f"Failed to migrate produtos: {e}")
            raise
    
    def _migrate_responsaveis(self, df: pd.DataFrame):
        """Migrate responsaveis table"""
        try:
            # Prepare data
            records = []
            for _, row in df.iterrows():
                record = {
                    'nome': row.get('nome', ''),
                    'tipo': row.get('tipo', ''),
                    'email': row.get('email', '')
                }
                records.append(record)
            
            # Insert data
            if records:
                insert_sql = """
                INSERT INTO responsaveis (nome, tipo, email)
                VALUES (%(nome)s, %(tipo)s, %(email)s)
                ON CONFLICT DO NOTHING
                """
                
                with self.database_service.get_session() as session:
                    session.execute(insert_sql, records)
                
                logger.info(f"Migrated {len(records)} responsaveis")
            
        except Exception as e:
            logger.error(f"Failed to migrate responsaveis: {e}")
            raise
