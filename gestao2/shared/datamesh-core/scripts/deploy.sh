#!/bin/bash

# DataHub Amigo One - DataMesh Deployment Script
# Automated deployment for Business and Product domains with shared core

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="DataHub Amigo One - DataMesh"
BASE_DIR="/opt/datahub-amigo-one"
SHARED_DIR="$BASE_DIR/shared"
BUSINESS_DIR="$BASE_DIR/business"
PRODUCT_DIR="$BASE_DIR/product"
LOG_DIR="/var/log/datahub"

# Services
BUSINESS_SERVICE="datahub-business"
PRODUCT_SERVICE="datahub-product"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking system requirements..."

    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root for security reasons"
        exit 1
    fi

    # Check Python version
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required but not installed"
        exit 1
    fi

    PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
    if [[ $(echo "$PYTHON_VERSION < 3.8" | bc -l) -eq 1 ]]; then
        log_error "Python 3.8+ is required, found $PYTHON_VERSION"
        exit 1
    fi

    # Check required packages
    for package in git curl nginx redis-server postgresql-client; do
        if ! command -v $package &> /dev/null; then
            log_warning "$package is not installed, please install it manually"
        fi
    done

    log_success "System requirements check completed"
}

setup_environment() {
    log_info "Setting up environment..."

    # Create application directory
    sudo mkdir -p $APP_DIR
    sudo chown $USER:$USER $APP_DIR

    # Create log directory
    sudo mkdir -p $LOG_DIR
    sudo chown $USER:$USER $LOG_DIR

    # Create virtual environment
    if [ ! -d "$VENV_DIR" ]; then
        log_info "Creating Python virtual environment..."
        python3 -m venv $VENV_DIR
    fi

    # Activate virtual environment
    source $VENV_DIR/bin/activate

    # Upgrade pip
    pip install --upgrade pip

    log_success "Environment setup completed"
}

install_dependencies() {
    log_info "Installing Python dependencies..."

    # Activate virtual environment
    source $VENV_DIR/bin/activate

    # Install dependencies
    pip install -r requirements.txt

    log_success "Dependencies installed successfully"
}

configure_application() {
    log_info "Configuring application..."

    # Copy environment file if it doesn't exist
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_warning "Created .env file from .env.example. Please configure it before running the application."
        else
            log_error ".env.example file not found"
            exit 1
        fi
    fi

    # Set secure permissions on .env file
    chmod 600 .env

    # Create logs directory in app
    mkdir -p logs

    log_success "Application configuration completed"
}

setup_database() {
    log_info "Setting up database..."

    # Check if database migration script exists
    if [ -f "scripts/migrate_data.py" ]; then
        log_info "Running database migration..."
        source $VENV_DIR/bin/activate
        python scripts/migrate_data.py
        log_success "Database migration completed"
    else
        log_warning "Database migration script not found, skipping..."
    fi
}

configure_nginx() {
    log_info "Configuring Nginx..."

    # Create Nginx configuration
    sudo tee /etc/nginx/sites-available/$SERVICE_NAME > /dev/null <<EOF
server {
    listen 80;
    server_name _;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Static files
    location /static {
        alias $APP_DIR/app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Health check
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

    # Enable site
    sudo ln -sf /etc/nginx/sites-available/$SERVICE_NAME /etc/nginx/sites-enabled/

    # Test Nginx configuration
    sudo nginx -t

    # Reload Nginx
    sudo systemctl reload nginx

    log_success "Nginx configuration completed"
}

create_systemd_service() {
    log_info "Creating systemd service..."

    # Create systemd service file
    sudo tee /etc/systemd/system/$SERVICE_NAME.service > /dev/null <<EOF
[Unit]
Description=$APP_NAME
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=exec
User=$USER
Group=$USER
WorkingDirectory=$APP_DIR
Environment=PATH=$VENV_DIR/bin
ExecStart=$VENV_DIR/bin/gunicorn --bind 127.0.0.1:5000 --workers 4 --worker-class sync --timeout 60 --keep-alive 2 --max-requests 1000 --max-requests-jitter 100 --preload run:app
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$APP_DIR $LOG_DIR

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

    # Reload systemd
    sudo systemctl daemon-reload

    # Enable service
    sudo systemctl enable $SERVICE_NAME

    log_success "Systemd service created and enabled"
}

setup_monitoring() {
    log_info "Setting up monitoring..."

    # Create log rotation configuration
    sudo tee /etc/logrotate.d/$SERVICE_NAME > /dev/null <<EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        systemctl reload $SERVICE_NAME
    endscript
}
EOF

    # Create health check script
    tee $APP_DIR/health_check.sh > /dev/null <<'EOF'
#!/bin/bash
HEALTH_URL="http://localhost:5000/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ "$RESPONSE" = "200" ]; then
    echo "Application is healthy"
    exit 0
else
    echo "Application is unhealthy (HTTP $RESPONSE)"
    exit 1
fi
EOF

    chmod +x $APP_DIR/health_check.sh

    log_success "Monitoring setup completed"
}

deploy_application() {
    log_info "Deploying application..."

    # Copy application files
    rsync -av --exclude='.git' --exclude='__pycache__' --exclude='*.pyc' --exclude='.env' . $APP_DIR/

    # Set proper permissions
    find $APP_DIR -type f -exec chmod 644 {} \;
    find $APP_DIR -type d -exec chmod 755 {} \;
    chmod +x $APP_DIR/scripts/*.sh 2>/dev/null || true

    # Start/restart service
    sudo systemctl restart $SERVICE_NAME

    # Wait for service to start
    sleep 5

    # Check service status
    if sudo systemctl is-active --quiet $SERVICE_NAME; then
        log_success "Application deployed and running successfully"
    else
        log_error "Application failed to start"
        sudo systemctl status $SERVICE_NAME
        exit 1
    fi
}

run_tests() {
    log_info "Running application tests..."

    # Activate virtual environment
    source $VENV_DIR/bin/activate

    # Run health check
    if $APP_DIR/health_check.sh; then
        log_success "Health check passed"
    else
        log_error "Health check failed"
        exit 1
    fi

    # Run unit tests if available
    if [ -f "test_app.py" ]; then
        python -m pytest test_app.py -v
        log_success "Unit tests passed"
    fi
}

show_status() {
    log_info "Application Status:"
    echo "===================="
    echo "Service Status: $(sudo systemctl is-active $SERVICE_NAME)"
    echo "Service Enabled: $(sudo systemctl is-enabled $SERVICE_NAME)"
    echo "Application URL: http://localhost"
    echo "Logs: sudo journalctl -u $SERVICE_NAME -f"
    echo "Health Check: $APP_DIR/health_check.sh"
    echo "===================="
}

# Main deployment process
main() {
    log_info "Starting deployment of $APP_NAME..."

    check_requirements
    setup_environment
    install_dependencies
    configure_application
    setup_database
    configure_nginx
    create_systemd_service
    setup_monitoring
    deploy_application
    run_tests
    show_status

    log_success "Deployment completed successfully!"
    log_info "Please review and configure the .env file with your specific settings"
    log_info "Don't forget to configure SSL/TLS certificates for production"
}

# Parse command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "status")
        show_status
        ;;
    "restart")
        sudo systemctl restart $SERVICE_NAME
        log_success "Application restarted"
        ;;
    "logs")
        sudo journalctl -u $SERVICE_NAME -f
        ;;
    "health")
        $APP_DIR/health_check.sh
        ;;
    *)
        echo "Usage: $0 {deploy|status|restart|logs|health}"
        exit 1
        ;;
esac
