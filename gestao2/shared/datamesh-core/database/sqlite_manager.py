"""
SQLite Database Manager for DataHub Amigo One
Manages separate SQLite databases for Business and Product domains
"""

import sqlite3
import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import bcrypt

logger = logging.getLogger(__name__)

class SQLiteManager:
    """Manages SQLite databases for each domain"""

    def __init__(self, domain: str):
        self.domain = domain
        self.db_path = self._get_db_path()
        self._init_database()

    def _get_db_path(self) -> str:
        """Get database path for domain"""
        if self.domain == 'business':
            db_dir = Path('gestao/data')
            db_dir.mkdir(parents=True, exist_ok=True)
            return str(db_dir / 'business_admin.db')
        else:
            db_dir = Path('produto/ux-gabi/data')
            db_dir.mkdir(parents=True, exist_ok=True)
            return str(db_dir / 'product_admin.db')

    def _init_database(self):
        """Initialize database with required tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Users table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    role TEXT NOT NULL DEFAULT 'user',
                    permissions TEXT NOT NULL DEFAULT '[]',
                    domain TEXT NOT NULL,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP NULL,
                    metadata TEXT DEFAULT '{}'
                )
            ''')

            # Activity logs table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS activity_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    action TEXT NOT NULL,
                    description TEXT NOT NULL,
                    page TEXT NULL,
                    ip_address TEXT NULL,
                    user_agent TEXT NULL,
                    domain TEXT NOT NULL,
                    metadata TEXT DEFAULT '{}',
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # User sessions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    session_id TEXT UNIQUE NOT NULL,
                    ip_address TEXT NULL,
                    user_agent TEXT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN NOT NULL DEFAULT 1
                )
            ''')

            # Permissions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    permission_key TEXT UNIQUE NOT NULL,
                    permission_name TEXT NOT NULL,
                    description TEXT NULL,
                    domain TEXT NOT NULL,
                    category TEXT NULL,
                    is_admin_only BOOLEAN NOT NULL DEFAULT 0
                )
            ''')

            conn.commit()

            # Initialize default data
            self._init_default_data()

    def _init_default_data(self):
        """Initialize default users and permissions"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Check if we already have data
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]

            if user_count == 0:
                # Create default admin users
                self._create_default_admin_users(cursor)

            # Initialize permissions
            self._init_permissions(cursor)

            conn.commit()

    def _create_default_admin_users(self, cursor):
        """Create default admin users"""
        # SECURITY: Use environment variables for passwords
        import os
        import secrets

        # Get passwords from environment or generate secure ones
        admin_password = os.getenv('ADMIN_PASSWORD', secrets.token_urlsafe(16))
        ttk_password = os.getenv('TTK_PASSWORD', secrets.token_urlsafe(16))
        bruno_password = os.getenv('BRUNO_BRUNO_PASSWORD', secrets.token_urlsafe(16))

        admin_users = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': admin_password,
                'role': 'admin'
            },
            {
                'username': 'TTK',
                'email': '<EMAIL>',
                'password': ttk_password,
                'role': 'admin'
            },
            {
                'username': 'bruno@bruno',
                'email': '<EMAIL>',
                'password': bruno_password,
                'role': 'admin'
            }
        ]

        # Log generated passwords for first-time setup (remove in production)
        if not os.getenv('ADMIN_PASSWORD'):
            print(f"   🔑 Generated password for admin: {admin_password}")
        if not os.getenv('TTK_PASSWORD'):
            print(f"   🔑 Generated password for TTK: {ttk_password}")
        if not os.getenv('BRUNO_BRUNO_PASSWORD'):
            print(f"   🔑 Generated password for bruno@bruno: {bruno_password}")

        for user in admin_users:
            password_hash = bcrypt.hashpw(user['password'].encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            permissions = json.dumps(self._get_default_permissions(user['role']))

            cursor.execute('''
                INSERT OR IGNORE INTO users
                (username, email, password_hash, role, permissions, domain, is_active, metadata)
                VALUES (?, ?, ?, ?, ?, ?, 1, ?)
            ''', (
                user['username'],
                user['email'],
                password_hash,
                user['role'],
                permissions,
                self.domain,
                json.dumps({'is_default': True})
            ))

    def _init_permissions(self, cursor):
        """Initialize permissions for domain"""
        if self.domain == 'business':
            permissions = [
                ('dashboard.view', 'Dashboard', 'Visualizar dashboard principal', 'Visualização'),
                ('leads.view', 'Leads', 'Visualizar leads', 'Vendas'),
                ('opportunities.view', 'Oportunidades', 'Visualizar oportunidades', 'Vendas'),
                ('implementations.view', 'Implementações', 'Visualizar implementações', 'Operações'),
                ('universities.view', 'Universidades', 'Visualizar universidades', 'Dados'),
                ('responsibles.view', 'Responsáveis', 'Visualizar responsáveis', 'Gestão'),
                ('classes.view', 'Classes', 'Visualizar classes', 'Educação'),
                ('coupons.view', 'Cupons', 'Visualizar cupons', 'Marketing'),
                ('conversion.view', 'Conversão', 'Visualizar conversões', 'Analytics'),
                ('subscriptions.view', 'Assinaturas', 'Visualizar assinaturas', 'Financeiro'),
                ('data_quality.view', 'Qualidade de Dados', 'Visualizar qualidade de dados', 'Dados'),
                ('business_rules.view', 'Regras de Negócio', 'Visualizar regras de negócio', 'Configuração'),
                ('users.manage', 'Gerenciar Usuários', 'Gerenciar usuários e permissões', 'Administração')
            ]
        else:
            permissions = [
                ('dashboard.view', 'Dashboard', 'Visualizar dashboard principal', 'Visualização'),
                ('users.view', 'Usuários', 'Visualizar usuários', 'Gestão'),
                ('features.view', 'Features', 'Visualizar features', 'Produto'),
                ('connections.view', 'Conexões', 'Visualizar conexões', 'Rede'),
                ('payments.view', 'Pagamentos', 'Visualizar pagamentos', 'Financeiro'),
                ('agenda.view', 'Agenda', 'Visualizar agenda', 'Operações'),
                ('medical_records.view', 'Prontuários', 'Visualizar prontuários', 'Clínico'),
                ('accounting.view', 'Contabilidade', 'Visualizar contabilidade', 'Financeiro'),
                ('analytics.view', 'Analytics', 'Visualizar analytics', 'Analytics'),
                ('users.manage', 'Gerenciar Usuários', 'Gerenciar usuários e permissões', 'Administração')
            ]

        for perm_key, perm_name, description, category in permissions:
            is_admin_only = 1 if perm_key == 'users.manage' else 0
            cursor.execute('''
                INSERT OR IGNORE INTO permissions
                (permission_key, permission_name, description, domain, category, is_admin_only)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (perm_key, perm_name, description, self.domain, category, is_admin_only))

    def _get_default_permissions(self, role: str) -> List[str]:
        """Get default permissions for role"""
        if role == 'admin':
            if self.domain == 'business':
                return [
                    'dashboard.view', 'leads.view', 'opportunities.view', 'implementations.view',
                    'universities.view', 'responsibles.view', 'classes.view', 'coupons.view',
                    'conversion.view', 'subscriptions.view', 'data_quality.view',
                    'business_rules.view', 'users.manage'
                ]
            else:
                return [
                    'dashboard.view', 'users.view', 'features.view', 'connections.view',
                    'payments.view', 'agenda.view', 'medical_records.view', 'accounting.view',
                    'analytics.view', 'users.manage'
                ]
        else:
            return ['dashboard.view']

    # User Management Methods
    def get_user_stats(self) -> Dict[str, Any]:
        """Get user statistics"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Total users
            cursor.execute("SELECT COUNT(*) FROM users WHERE domain = ?", (self.domain,))
            total_users = cursor.fetchone()[0]

            # Active users
            cursor.execute("SELECT COUNT(*) FROM users WHERE domain = ? AND is_active = 1", (self.domain,))
            active_users = cursor.fetchone()[0]

            # Admin users
            cursor.execute("SELECT COUNT(*) FROM users WHERE domain = ? AND role = 'admin'", (self.domain,))
            admin_users = cursor.fetchone()[0]

            # Recent logins (last 24 hours)
            cursor.execute('''
                SELECT COUNT(DISTINCT user_id) FROM activity_logs
                WHERE domain = ? AND action = 'login'
                AND timestamp > datetime('now', '-24 hours')
            ''', (self.domain,))
            recent_logins = cursor.fetchone()[0]

            return {
                'total_users': total_users,
                'active_users': active_users,
                'admin_users': admin_users,
                'recent_logins': recent_logins
            }

    def get_all_users(self) -> List[Dict[str, Any]]:
        """Get all users"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT username, email, role, is_active, created_at, last_login, permissions, metadata
                FROM users WHERE domain = ?
                ORDER BY created_at DESC
            ''', (self.domain,))

            users = []
            for row in cursor.fetchall():
                username, email, role, is_active, created_at, last_login, permissions, metadata = row

                users.append({
                    'username': username,
                    'email': email,
                    'role': role,
                    'is_active': bool(is_active),
                    'created_at': created_at,
                    'last_login': last_login,
                    'permissions': json.loads(permissions) if permissions else [],
                    'metadata': json.loads(metadata) if metadata else {}
                })

            return users

    def create_user(self, username: str, email: str, password: str,
                   role: str = 'user', permissions: List[str] = None) -> Dict[str, Any]:
        """Create a new user"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Check if user exists
            cursor.execute("SELECT username FROM users WHERE username = ? OR email = ?", (username, email))
            if cursor.fetchone():
                raise ValueError(f"User {username} or email {email} already exists")

            # Hash password
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            # Default permissions
            if permissions is None:
                permissions = self._get_default_permissions(role)

            # Insert user
            cursor.execute('''
                INSERT INTO users
                (username, email, password_hash, role, permissions, domain, is_active, metadata)
                VALUES (?, ?, ?, ?, ?, ?, 1, ?)
            ''', (
                username, email, password_hash, role,
                json.dumps(permissions), self.domain,
                json.dumps({'created_by': 'admin'})
            ))

            # Log activity
            self.log_activity(username, 'user_created', f'User {username} created')

            return {
                'username': username,
                'email': email,
                'role': role,
                'permissions': permissions,
                'domain': self.domain
            }

    def update_user_permissions(self, username: str, permissions: List[str]) -> bool:
        """Update user permissions"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE users SET permissions = ?
                WHERE username = ? AND domain = ?
            ''', (json.dumps(permissions), username, self.domain))

            if cursor.rowcount > 0:
                self.log_activity(username, 'permissions_updated', f'Permissions updated for {username}')
                return True

            return False

    def deactivate_user(self, username: str) -> bool:
        """Deactivate a user"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE users SET is_active = 0
                WHERE username = ? AND domain = ?
            ''', (username, self.domain))

            if cursor.rowcount > 0:
                self.log_activity(username, 'user_deactivated', f'User {username} deactivated')
                return True

            return False

    def activate_user(self, username: str) -> bool:
        """Activate a user"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE users SET is_active = 1
                WHERE username = ? AND domain = ?
            ''', (username, self.domain))

            if cursor.rowcount > 0:
                self.log_activity(username, 'user_activated', f'User {username} activated')
                return True

            return False

    def get_permissions(self) -> Dict[str, str]:
        """Get all permissions for domain"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT permission_name, permission_key
                FROM permissions WHERE domain = ?
                ORDER BY category, permission_name
            ''', (self.domain,))

            return {name: key for name, key in cursor.fetchall()}

    def log_activity(self, user_id: str, action: str, description: str,
                    page: str = None, ip_address: str = None, user_agent: str = None,
                    metadata: Dict[str, Any] = None):
        """Log user activity"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO activity_logs
                (user_id, action, description, page, ip_address, user_agent, domain, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id, action, description, page, ip_address, user_agent,
                self.domain, json.dumps(metadata or {})
            ))

    def get_activity_logs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent activity logs"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT user_id, action, description, page, ip_address,
                       timestamp, metadata
                FROM activity_logs
                WHERE domain = ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (self.domain, limit))

            logs = []
            for row in cursor.fetchall():
                user_id, action, description, page, ip_address, timestamp, metadata = row

                logs.append({
                    'user_id': user_id,
                    'action': action,
                    'description': description,
                    'page': page,
                    'ip_address': ip_address,
                    'timestamp': timestamp,
                    'metadata': json.loads(metadata) if metadata else {}
                })

            return logs

    def update_last_login(self, username: str):
        """Update user's last login timestamp"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE users SET last_login = CURRENT_TIMESTAMP
                WHERE username = ? AND domain = ?
            ''', (username, self.domain))

    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user by username"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT username, email, password_hash, role, permissions,
                       is_active, created_at, last_login, metadata
                FROM users
                WHERE username = ? AND domain = ?
            ''', (username, self.domain))

            row = cursor.fetchone()
            if row:
                username, email, password_hash, role, permissions, is_active, created_at, last_login, metadata = row

                return {
                    'username': username,
                    'email': email,
                    'password_hash': password_hash,
                    'role': role,
                    'permissions': json.loads(permissions) if permissions else [],
                    'is_active': bool(is_active),
                    'created_at': created_at,
                    'last_login': last_login,
                    'metadata': json.loads(metadata) if metadata else {}
                }

            return None
