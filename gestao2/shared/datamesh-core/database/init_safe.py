#!/usr/bin/env python3
"""
Safe SQLite Database Initialization for DataHub Amigo One
Initializes databases safely even when servers are running
"""

import sys
import os
import sqlite3
import json
import bcrypt
from pathlib import Path
from datetime import datetime

def init_business_database_safe():
    """Safely initialize Business domain database"""
    print("🏢 Inicializando banco Business (modo seguro)...")
    
    db_path = Path('gestao/data/business_admin.db')
    db_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        # Use WAL mode for concurrent access
        conn = sqlite3.connect(str(db_path), timeout=30.0)
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA synchronous=NORMAL")
        conn.execute("PRAGMA cache_size=1000")
        conn.execute("PRAGMA temp_store=memory")
        
        cursor = conn.cursor()
        
        # Create tables if they don't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'user',
                permissions TEXT NOT NULL DEFAULT '[]',
                domain TEXT NOT NULL,
                is_active BOOLEAN NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP NULL,
                metadata TEXT DEFAULT '{}'
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                action TEXT NOT NULL,
                description TEXT NOT NULL,
                page TEXT NULL,
                ip_address TEXT NULL,
                user_agent TEXT NULL,
                domain TEXT NOT NULL,
                metadata TEXT DEFAULT '{}',
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                permission_key TEXT UNIQUE NOT NULL,
                permission_name TEXT NOT NULL,
                description TEXT NULL,
                domain TEXT NOT NULL,
                category TEXT NULL,
                is_admin_only BOOLEAN NOT NULL DEFAULT 0
            )
        ''')
        
        # Check if we need to add default users
        cursor.execute("SELECT COUNT(*) FROM users WHERE domain = 'business'")
        user_count = cursor.fetchone()[0]
        
        if user_count == 0:
            print("   📝 Criando usuários padrão...")
            
            # Default admin users
            admin_users = [
                ('admin', '<EMAIL>', 'admin123', 'admin'),
                ('TTK', '<EMAIL>', 'ttk123', 'admin'),
                ('bruno@bruno', '<EMAIL>', 'bruno@bruno', 'admin')
            ]
            
            for username, email, password, role in admin_users:
                password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                permissions = json.dumps([
                    'dashboard.view', 'leads.view', 'opportunities.view', 'implementations.view',
                    'universities.view', 'responsibles.view', 'classes.view', 'coupons.view',
                    'conversion.view', 'subscriptions.view', 'data_quality.view', 
                    'business_rules.view', 'users.manage'
                ])
                
                cursor.execute('''
                    INSERT OR IGNORE INTO users 
                    (username, email, password_hash, role, permissions, domain, metadata)
                    VALUES (?, ?, ?, ?, ?, 'business', '{"is_default": true}')
                ''', (username, email, password_hash, role, permissions))
            
            # Sample users
            sample_users = [
                ('sergio', '<EMAIL>', 'sergio123', 'user', [
                    'dashboard.view', 'leads.view', 'opportunities.view',
                    'implementations.view', 'universities.view', 'data_quality.view'
                ]),
                ('bruno', '<EMAIL>', 'bruno123', 'user', [
                    'dashboard.view', 'leads.view', 'opportunities.view',
                    'implementations.view', 'universities.view', 'responsibles.view',
                    'classes.view', 'coupons.view', 'conversion.view',
                    'subscriptions.view', 'business_rules.view'
                ])
            ]
            
            for username, email, password, role, perms in sample_users:
                password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                permissions = json.dumps(perms)
                
                cursor.execute('''
                    INSERT OR IGNORE INTO users 
                    (username, email, password_hash, role, permissions, domain, metadata)
                    VALUES (?, ?, ?, ?, ?, 'business', '{"created_by": "init_script"}')
                ''', (username, email, password_hash, role, permissions))
        
        # Initialize permissions
        business_permissions = [
            ('dashboard.view', 'Dashboard', 'Visualizar dashboard principal', 'Visualização'),
            ('leads.view', 'Leads', 'Visualizar leads', 'Vendas'),
            ('opportunities.view', 'Oportunidades', 'Visualizar oportunidades', 'Vendas'),
            ('implementations.view', 'Implementações', 'Visualizar implementações', 'Operações'),
            ('universities.view', 'Universidades', 'Visualizar universidades', 'Dados'),
            ('responsibles.view', 'Responsáveis', 'Visualizar responsáveis', 'Gestão'),
            ('classes.view', 'Classes', 'Visualizar classes', 'Educação'),
            ('coupons.view', 'Cupons', 'Visualizar cupons', 'Marketing'),
            ('conversion.view', 'Conversão', 'Visualizar conversões', 'Analytics'),
            ('subscriptions.view', 'Assinaturas', 'Visualizar assinaturas', 'Financeiro'),
            ('data_quality.view', 'Qualidade de Dados', 'Visualizar qualidade de dados', 'Dados'),
            ('business_rules.view', 'Regras de Negócio', 'Visualizar regras de negócio', 'Configuração'),
            ('users.manage', 'Gerenciar Usuários', 'Gerenciar usuários e permissões', 'Administração')
        ]
        
        for perm_key, perm_name, description, category in business_permissions:
            is_admin_only = 1 if perm_key == 'users.manage' else 0
            cursor.execute('''
                INSERT OR IGNORE INTO permissions 
                (permission_key, permission_name, description, domain, category, is_admin_only)
                VALUES (?, ?, ?, 'business', ?, ?)
            ''', (perm_key, perm_name, description, category, is_admin_only))
        
        conn.commit()
        conn.close()
        
        print("   ✅ Banco Business inicializado com sucesso!")
        
    except Exception as e:
        print(f"   ❌ Erro: {e}")

def init_product_database_safe():
    """Safely initialize Product domain database"""
    print("\n🛍️  Inicializando banco Product (modo seguro)...")
    
    db_path = Path('produto/ux-gabi/data/product_admin.db')
    db_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        # Use WAL mode for concurrent access
        conn = sqlite3.connect(str(db_path), timeout=30.0)
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA synchronous=NORMAL")
        conn.execute("PRAGMA cache_size=1000")
        conn.execute("PRAGMA temp_store=memory")
        
        cursor = conn.cursor()
        
        # Create tables if they don't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'user',
                permissions TEXT NOT NULL DEFAULT '[]',
                domain TEXT NOT NULL,
                is_active BOOLEAN NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP NULL,
                metadata TEXT DEFAULT '{}'
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                action TEXT NOT NULL,
                description TEXT NOT NULL,
                page TEXT NULL,
                ip_address TEXT NULL,
                user_agent TEXT NULL,
                domain TEXT NOT NULL,
                metadata TEXT DEFAULT '{}',
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                permission_key TEXT UNIQUE NOT NULL,
                permission_name TEXT NOT NULL,
                description TEXT NULL,
                domain TEXT NOT NULL,
                category TEXT NULL,
                is_admin_only BOOLEAN NOT NULL DEFAULT 0
            )
        ''')
        
        # Check if we need to add default users
        cursor.execute("SELECT COUNT(*) FROM users WHERE domain = 'product'")
        user_count = cursor.fetchone()[0]
        
        if user_count == 0:
            print("   📝 Criando usuários padrão...")
            
            # Default admin users
            admin_users = [
                ('admin', '<EMAIL>', 'admin123', 'admin'),
                ('TTK', '<EMAIL>', 'ttk123', 'admin'),
                ('bruno@bruno', '<EMAIL>', 'bruno@bruno', 'admin')
            ]
            
            for username, email, password, role in admin_users:
                password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                permissions = json.dumps([
                    'dashboard.view', 'users.view', 'features.view', 'connections.view',
                    'payments.view', 'agenda.view', 'medical_records.view', 'accounting.view',
                    'analytics.view', 'users.manage'
                ])
                
                cursor.execute('''
                    INSERT OR IGNORE INTO users 
                    (username, email, password_hash, role, permissions, domain, metadata)
                    VALUES (?, ?, ?, ?, ?, 'product', '{"is_default": true}')
                ''', (username, email, password_hash, role, permissions))
            
            # Sample users
            sample_users = [
                ('sergio', '<EMAIL>', 'sergio123', 'user', [
                    'dashboard.view', 'users.view', 'features.view', 'analytics.view'
                ]),
                ('bruno', '<EMAIL>', 'bruno123', 'user', [
                    'dashboard.view', 'users.view', 'features.view',
                    'connections.view', 'payments.view', 'agenda.view',
                    'medical_records.view', 'accounting.view', 'analytics.view'
                ])
            ]
            
            for username, email, password, role, perms in sample_users:
                password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                permissions = json.dumps(perms)
                
                cursor.execute('''
                    INSERT OR IGNORE INTO users 
                    (username, email, password_hash, role, permissions, domain, metadata)
                    VALUES (?, ?, ?, ?, ?, 'product', '{"created_by": "init_script"}')
                ''', (username, email, password_hash, role, permissions))
        
        # Initialize permissions
        product_permissions = [
            ('dashboard.view', 'Dashboard', 'Visualizar dashboard principal', 'Visualização'),
            ('users.view', 'Usuários', 'Visualizar usuários', 'Gestão'),
            ('features.view', 'Features', 'Visualizar features', 'Produto'),
            ('connections.view', 'Conexões', 'Visualizar conexões', 'Rede'),
            ('payments.view', 'Pagamentos', 'Visualizar pagamentos', 'Financeiro'),
            ('agenda.view', 'Agenda', 'Visualizar agenda', 'Operações'),
            ('medical_records.view', 'Prontuários', 'Visualizar prontuários', 'Clínico'),
            ('accounting.view', 'Contabilidade', 'Visualizar contabilidade', 'Financeiro'),
            ('analytics.view', 'Analytics', 'Visualizar analytics', 'Analytics'),
            ('users.manage', 'Gerenciar Usuários', 'Gerenciar usuários e permissões', 'Administração')
        ]
        
        for perm_key, perm_name, description, category in product_permissions:
            is_admin_only = 1 if perm_key == 'users.manage' else 0
            cursor.execute('''
                INSERT OR IGNORE INTO permissions 
                (permission_key, permission_name, description, domain, category, is_admin_only)
                VALUES (?, ?, ?, 'product', ?, ?)
            ''', (perm_key, perm_name, description, category, is_admin_only))
        
        conn.commit()
        conn.close()
        
        print("   ✅ Banco Product inicializado com sucesso!")
        
    except Exception as e:
        print(f"   ❌ Erro: {e}")

def verify_databases_safe():
    """Safely verify databases"""
    print("\n🔍 Verificando bancos de dados...")
    
    try:
        # Business database
        business_db = Path('gestao/data/business_admin.db')
        if business_db.exists():
            conn = sqlite3.connect(str(business_db), timeout=10.0)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM users WHERE domain = 'business'")
            business_users = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM users WHERE domain = 'business' AND is_active = 1")
            business_active = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM permissions WHERE domain = 'business'")
            business_perms = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"   🏢 Business: {business_users} usuários, {business_active} ativos, {business_perms} permissões")
        
        # Product database
        product_db = Path('produto/ux-gabi/data/product_admin.db')
        if product_db.exists():
            conn = sqlite3.connect(str(product_db), timeout=10.0)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM users WHERE domain = 'product'")
            product_users = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM users WHERE domain = 'product' AND is_active = 1")
            product_active = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM permissions WHERE domain = 'product'")
            product_perms = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"   🛍️  Product: {product_users} usuários, {product_active} ativos, {product_perms} permissões")
        
        print("   ✅ Verificação concluída!")
        
    except Exception as e:
        print(f"   ❌ Erro na verificação: {e}")

def main():
    """Main function"""
    print("🔧 INICIALIZAÇÃO SEGURA DOS BANCOS SQLITE")
    print("=" * 50)
    
    # Change to project root
    project_root = Path(__file__).parent.parent.parent.parent
    os.chdir(project_root)
    print(f"📂 Diretório: {project_root}")
    
    # Initialize databases
    init_business_database_safe()
    init_product_database_safe()
    
    # Verify
    verify_databases_safe()
    
    print("\n" + "=" * 50)
    print("✅ INICIALIZAÇÃO SEGURA CONCLUÍDA!")
    print("\n💡 Os painéis de admin agora usam SQLite")
    print("🔐 Usuários admin: admin, TTK, bruno@bruno")
    print("👥 Usuários exemplo: sergio, bruno")

if __name__ == "__main__":
    main()
