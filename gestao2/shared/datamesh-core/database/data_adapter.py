"""
Amigo DataHub - Data Adapter
Unified interface for accessing data from files or database
"""

import os
import logging
import pandas as pd
from typing import Optional, Dict, Any, List
from flask import current_app
from .database_service import DatabaseService
from .data_loader import DataLoader

logger = logging.getLogger(__name__)

class DataAdapter:
    """
    Unified data adapter that can work with both files and database
    Provides transparent switching between data sources
    """
    
    def __init__(self):
        """Initialize the data adapter"""
        self.database_service = None
        self.file_loader = None
        self.data_source_mode = None
        self._initialize()
    
    def _initialize(self):
        """Initialize data sources based on configuration"""
        try:
            # Get data source mode from configuration
            self.data_source_mode = current_app.config.get('DATA_SOURCE_MODE', 'files')
            
            # Initialize database service
            try:
                self.database_service = DatabaseService()
                logger.info("Database service initialized")
            except Exception as e:
                logger.warning(f"Database service initialization failed: {e}")
                self.database_service = None
            
            # Initialize file loader (always available as fallback)
            try:
                self.file_loader = DataLoader()
                logger.info("File loader initialized")
            except Exception as e:
                logger.error(f"File loader initialization failed: {e}")
                self.file_loader = None
            
            # Determine effective data source
            self._determine_effective_source()
            
        except Exception as e:
            logger.error(f"Failed to initialize data adapter: {e}")
            raise
    
    def _determine_effective_source(self):
        """Determine which data source to actually use"""
        if self.data_source_mode == 'database':
            if self.database_service and self.database_service.is_database_available():
                logger.info("Using database as primary data source")
                return
            else:
                logger.warning("Database requested but not available, falling back to files")
                self.data_source_mode = 'files'
        
        if self.data_source_mode == 'files':
            if self.file_loader:
                logger.info("Using files as data source")
                return
            else:
                logger.error("No data source available")
                raise RuntimeError("No data source available")
    
    def get_main_data(self) -> pd.DataFrame:
        """
        Get main dataset (leads, opportunities, implementations)
        
        Returns:
            DataFrame: Main dataset
        """
        try:
            if self.data_source_mode == 'database' and self.database_service:
                return self._get_main_data_from_database()
            else:
                return self._get_main_data_from_files()
                
        except Exception as e:
            logger.error(f"Failed to get main data: {e}")
            # Try fallback
            return self._get_fallback_data()
    
    def _get_main_data_from_database(self) -> pd.DataFrame:
        """Get main data from database"""
        try:
            # Main query to get all business data
            query = """
            SELECT 
                l.id as lead_id,
                l.nome as "Nome do Lead",
                l.data_criacao as "Data_criacao_lead",
                l.formacao_academica as "Formação Academica",
                l.cidade,
                l.estado,
                
                o.id as oportunidade_id,
                o.data_criacao as "Data_criacao_Oportunidade",
                o.etapa_funil as "Etapa do funil Comercial",
                o.valor_mensalidade as "Valor Mensalidade",
                
                i.id as implantacao_id,
                i.data_criacao as "Data_Criacao_Implantacao",
                i.data_finalizacao as "Data_Finalizacao",
                i.fase_implantacao as "Fase_Implantacao",
                i.status_implantacao as "Status_Implantacao",
                
                u.nome as "Universidade",
                p.nome as "Produto",
                c.nome as "Curso",
                t.nome as "Turma",
                
                r1.nome as "ResponsableOnboarding",
                r2.nome as "ResponsableOngoing",
                r3.nome as "ResponsableContabil",
                r4.nome as "ResponsableSocietário"
                
            FROM leads l
            LEFT JOIN oportunidades o ON l.id = o.lead_id
            LEFT JOIN implantacoes i ON o.id = i.oportunidade_id
            LEFT JOIN universidades u ON l.universidade_id = u.id
            LEFT JOIN produtos p ON o.produto_id = p.id
            LEFT JOIN cursos c ON l.curso_id = c.id
            LEFT JOIN turmas t ON i.turma_id = t.id
            LEFT JOIN responsaveis r1 ON i.responsavel_onboarding_id = r1.id
            LEFT JOIN responsaveis r2 ON i.responsavel_ongoing_id = r2.id
            LEFT JOIN responsaveis r3 ON i.responsavel_contabil_id = r3.id
            LEFT JOIN responsaveis r4 ON i.responsavel_societario_id = r4.id
            
            ORDER BY l.data_criacao DESC
            """
            
            df = self.database_service.execute_query(query)
            
            # Process the data (similar to file processing)
            df = self._process_database_data(df)
            
            logger.info(f"Retrieved {len(df)} records from database")
            return df
            
        except Exception as e:
            logger.error(f"Failed to get data from database: {e}")
            raise
    
    def _get_main_data_from_files(self) -> pd.DataFrame:
        """Get main data from files"""
        try:
            if not self.file_loader:
                raise RuntimeError("File loader not available")
                
            df = self.file_loader.load_data()
            logger.info(f"Retrieved {len(df)} records from files")
            return df
            
        except Exception as e:
            logger.error(f"Failed to get data from files: {e}")
            raise
    
    def _get_fallback_data(self) -> pd.DataFrame:
        """Get data using fallback method"""
        try:
            # Try files first as fallback
            if self.file_loader:
                logger.info("Using files as fallback data source")
                return self._get_main_data_from_files()
            
            # If files also fail, return empty DataFrame
            logger.warning("All data sources failed, returning empty DataFrame")
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"Fallback data retrieval failed: {e}")
            return pd.DataFrame()
    
    def _process_database_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process data retrieved from database"""
        try:
            # Convert date columns
            date_columns = [
                'Data_criacao_lead', 'Data_criacao_Oportunidade', 
                'Data_Criacao_Implantacao', 'Data_Finalizacao'
            ]
            
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce')
            
            # Add status position (similar to file processing)
            if 'Status_Implantacao' in df.columns:
                status_positions = {
                    'Aguardando Início': 1,
                    'Em Andamento': 2,
                    'Aguardando Documentação': 3,
                    'Aguardando Aprovação': 4,
                    'Aguardando Pagamento': 5,
                    'Aguardando Assinatura': 6,
                    'Aguardando Homologação': 7,
                    'Aguardando Liberação': 8,
                    'Aguardando Feedback': 9,
                    'Finalizado': 10,
                    'Cancelado': 0
                }
                df['StatusPosition'] = df['Status_Implantacao'].map(status_positions)
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to process database data: {e}")
            return df
    
    def get_dimension_data(self, table_name: str) -> pd.DataFrame:
        """
        Get dimension table data
        
        Args:
            table_name (str): Name of the dimension table
            
        Returns:
            DataFrame: Dimension data
        """
        try:
            if self.data_source_mode == 'database' and self.database_service:
                return self._get_dimension_from_database(table_name)
            else:
                return self._get_dimension_from_files(table_name)
                
        except Exception as e:
            logger.error(f"Failed to get dimension data for {table_name}: {e}")
            return pd.DataFrame()
    
    def _get_dimension_from_database(self, table_name: str) -> pd.DataFrame:
        """Get dimension data from database"""
        try:
            query = f"SELECT * FROM {table_name} ORDER BY nome"
            return self.database_service.execute_query(query)
            
        except Exception as e:
            logger.error(f"Failed to get dimension {table_name} from database: {e}")
            return pd.DataFrame()
    
    def _get_dimension_from_files(self, table_name: str) -> pd.DataFrame:
        """Get dimension data from files"""
        try:
            if not self.file_loader:
                return pd.DataFrame()
                
            return self.file_loader.get_dimension_table(table_name)
            
        except Exception as e:
            logger.error(f"Failed to get dimension {table_name} from files: {e}")
            return pd.DataFrame()
    
    def invalidate_cache(self):
        """Invalidate all cached data"""
        try:
            if self.database_service:
                self.database_service.invalidate_cache()
                logger.info("Database cache invalidated")
                
        except Exception as e:
            logger.warning(f"Failed to invalidate cache: {e}")
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of all data sources"""
        status = {
            'data_source_mode': self.data_source_mode,
            'file_loader_available': self.file_loader is not None,
            'database_service_available': self.database_service is not None
        }
        
        if self.database_service:
            status['database'] = self.database_service.get_health_status()
        
        return status
    
    def switch_data_source(self, mode: str):
        """
        Switch data source mode
        
        Args:
            mode (str): 'database' or 'files'
        """
        if mode not in ['database', 'files']:
            raise ValueError("Mode must be 'database' or 'files'")
        
        self.data_source_mode = mode
        self._determine_effective_source()
        
        # Invalidate cache when switching
        self.invalidate_cache()
        
        logger.info(f"Switched data source to: {mode}")
