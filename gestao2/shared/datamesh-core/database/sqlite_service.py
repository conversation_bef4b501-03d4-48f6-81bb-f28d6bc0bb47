"""
DataHub Amigo One - SQLite Database Service
Real database implementation with SQLite for both Business and Product domains
"""

import sqlite3
import json
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from contextlib import contextmanager
import uuid
import bcrypt
import secrets  # SECURITY: Use cryptographically secure random generator

logger = logging.getLogger(__name__)

class SQLiteService:
    """SQLite Database Service for DataHub Amigo One"""

    def __init__(self, db_path: str = None):
        """Initialize database service"""
        if db_path is None:
            # Default to shared database location
            current_dir = os.path.dirname(os.path.abspath(__file__))
            self.db_path = os.path.join(current_dir, 'datahub.db')
        else:
            self.db_path = db_path

        self.schema_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'schema.sql')
        self._initialize_database()

    def _initialize_database(self):
        """Initialize database with schema"""
        try:
            # Create database directory if it doesn't exist
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

            # Create database and tables
            with self.get_connection() as conn:
                # Read and execute schema
                if os.path.exists(self.schema_path):
                    with open(self.schema_path, 'r') as f:
                        schema_sql = f.read()
                    conn.executescript(schema_sql)
                    logger.info("Database schema initialized successfully")
                else:
                    logger.warning(f"Schema file not found: {self.schema_path}")

            # Populate with initial data
            self._populate_initial_data()

        except Exception as e:
            logger.error(f"Error initializing database: {e}")

    @contextmanager
    def get_connection(self):
        """Get database connection with context manager"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Enable dict-like access
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """Execute a SELECT query and return results"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

                # Convert rows to dictionaries
                columns = [description[0] for description in cursor.description]
                results = []
                for row in cursor.fetchall():
                    results.append(dict(zip(columns, row)))

                return results
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            return []

    def execute_update(self, query: str, params: tuple = None) -> int:
        """Execute an INSERT/UPDATE/DELETE query and return affected rows"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            logger.error(f"Error executing update: {e}")
            return 0

    def _populate_initial_data(self):
        """Populate database with initial data"""
        try:
            # Check if data already exists
            existing_users = self.execute_query("SELECT COUNT(*) as count FROM business_users")
            if existing_users and existing_users[0]['count'] > 0:
                logger.info("Database already populated, skipping initial data")
                return

            logger.info("Populating database with initial data...")

            # Create admin users
            self._create_admin_users()

            # Create sample business data
            self._create_sample_business_data()

            # Create sample product data
            self._create_sample_product_data()

            logger.info("Initial data populated successfully")

        except Exception as e:
            logger.error(f"Error populating initial data: {e}")

    def _create_admin_users(self):
        """Create admin users for both domains"""
        # SECURITY: Use environment variables for passwords
        import os
        import secrets

        # Get passwords from environment or generate secure ones
        admin_password = os.getenv('ADMIN_PASSWORD', secrets.token_urlsafe(16))
        ttk_password = os.getenv('TTK_PASSWORD', secrets.token_urlsafe(16))
        bruno_abreu_password = os.getenv('BRUNO_ABREU_PASSWORD', secrets.token_urlsafe(16))
        bruno_bruno_password = os.getenv('BRUNO_BRUNO_PASSWORD', secrets.token_urlsafe(16))

        # Business domain admin users
        business_admins = [
            {
                'user_id': 'admin',
                'username': 'admin',
                'email': '<EMAIL>',
                'password': admin_password,
                'role': 'admin'
            },
            {
                'user_id': 'TTK',
                'username': 'TTK',
                'email': '<EMAIL>',
                'password': ttk_password,
                'role': 'admin'
            },
            {
                'user_id': 'bruno@abreu',
                'username': 'bruno@abreu',
                'email': '<EMAIL>',
                'password': bruno_abreu_password,
                'role': 'admin'
            },
            {
                'user_id': 'bruno@bruno',
                'username': 'bruno@bruno',
                'email': '<EMAIL>',
                'password': bruno_bruno_password,
                'role': 'admin'
            }
        ]

        # Log generated passwords for first-time setup (remove in production)
        if not os.getenv('ADMIN_PASSWORD'):
            logger.info(f"Generated password for admin: {admin_password}")
        if not os.getenv('TTK_PASSWORD'):
            logger.info(f"Generated password for TTK: {ttk_password}")
        if not os.getenv('BRUNO_ABREU_PASSWORD'):
            logger.info(f"Generated password for bruno@abreu: {bruno_abreu_password}")
        if not os.getenv('BRUNO_BRUNO_PASSWORD'):
            logger.info(f"Generated password for bruno@bruno: {bruno_bruno_password}")

        business_permissions = [
            "dashboard.view", "leads.view", "leads.edit", "opportunities.view",
            "opportunities.edit", "implementations.view", "implementations.edit",
            "universities.view", "universities.edit", "responsibles.view",
            "responsibles.edit", "classes.view", "classes.edit", "coupons.view",
            "coupons.edit", "conversion.view", "subscriptions.view",
            "data_quality.view", "business_rules.view", "users.manage"
        ]

        for admin in business_admins:
            password_hash = bcrypt.hashpw(admin['password'].encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            self.execute_update("""
                INSERT OR REPLACE INTO business_users
                (user_id, username, email, password_hash, role, permissions, domain, is_active, created_at, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                admin['user_id'], admin['username'], admin['email'], password_hash,
                admin['role'], json.dumps(business_permissions), 'business', 1,
                datetime.now().isoformat(), json.dumps({'is_default': True})
            ))

        # Product domain admin users
        product_permissions = [
            "dashboard.view", "users.view", "users.edit", "features.view",
            "features.edit", "connections.view", "connections.edit", "payments.view",
            "payments.edit", "agenda.view", "agenda.edit", "medical_records.view",
            "medical_records.edit", "accounting.view", "accounting.edit",
            "analytics.view", "users.manage"
        ]

        for admin in business_admins:  # Same admins for product domain
            password_hash = bcrypt.hashpw(admin['password'].encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            self.execute_update("""
                INSERT OR REPLACE INTO product_users
                (user_id, username, email, password_hash, role, permissions, domain, is_active, created_at, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                admin['user_id'], admin['username'], admin['email'], password_hash,
                admin['role'], json.dumps(product_permissions), 'product', 1,
                datetime.now().isoformat(), json.dumps({'is_default': True})
            ))

    def get_health_status(self) -> Dict[str, Any]:
        """Get database health status"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()

                # Get table counts
                business_users = self.execute_query("SELECT COUNT(*) as count FROM business_users")[0]['count']
                product_users = self.execute_query("SELECT COUNT(*) as count FROM product_users")[0]['count']
                medical_users = self.execute_query("SELECT COUNT(*) as count FROM medical_users")[0]['count']

                return {
                    'database': {
                        'available': True,
                        'path': self.db_path,
                        'business_users': business_users,
                        'product_users': product_users,
                        'medical_users': medical_users
                    },
                    'cache': {'available': True}
                }
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                'database': {'available': False, 'error': str(e)},
                'cache': {'available': False}
            }

    def is_database_available(self) -> bool:
        """Check if database is available"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                return True
        except Exception:
            return False

    def _create_sample_business_data(self):
        """Create sample business data"""
        # Universities
        universities = [
            ('U001', 'Universidade de São Paulo', 'São Paulo', 'SP', 'Sudeste'),
            ('U002', 'Universidade Federal do Rio de Janeiro', 'Rio de Janeiro', 'RJ', 'Sudeste'),
            ('U003', 'Universidade Federal de Minas Gerais', 'Belo Horizonte', 'MG', 'Sudeste'),
            ('U004', 'Universidade de Brasília', 'Brasília', 'DF', 'Centro-Oeste'),
            ('U005', 'Universidade Federal do Paraná', 'Curitiba', 'PR', 'Sul'),
            ('U006', 'Universidade Federal da Bahia', 'Salvador', 'BA', 'Nordeste'),
            ('U007', 'Universidade Federal do Rio Grande do Sul', 'Porto Alegre', 'RS', 'Sul'),
            ('U008', 'Universidade Federal de Pernambuco', 'Recife', 'PE', 'Nordeste'),
            ('U009', 'Universidade Estadual de Campinas', 'Campinas', 'SP', 'Sudeste'),
            ('U010', 'Universidade Federal de Santa Catarina', 'Florianópolis', 'SC', 'Sul')
        ]

        for univ in universities:
            self.execute_update("""
                INSERT OR REPLACE INTO universities (university_id, name, city, state, region)
                VALUES (?, ?, ?, ?, ?)
            """, univ)

        # Responsibles
        responsibles = [
            ('R001', 'Ana Silva', '<EMAIL>', '11999999001', 'Gerente Comercial', 'Vendas'),
            ('R002', 'Carlos Santos', '<EMAIL>', '11999999002', 'Consultor de Vendas', 'Vendas'),
            ('R003', 'Maria Oliveira', '<EMAIL>', '11999999003', 'Especialista em Implementação', 'Implementação'),
            ('R004', 'João Pereira', '<EMAIL>', '11999999004', 'Consultor de Vendas', 'Vendas'),
            ('R005', 'Fernanda Costa', '<EMAIL>', '11999999005', 'Gerente de Implementação', 'Implementação'),
            ('R006', 'Ricardo Lima', '<EMAIL>', '11999999006', 'Consultor Senior', 'Vendas'),
            ('R007', 'Juliana Rocha', '<EMAIL>', '11999999007', 'Analista de Implementação', 'Implementação'),
            ('R008', 'Pedro Almeida', '<EMAIL>', '***********', 'Coordenador Comercial', 'Vendas')
        ]

        for resp in responsibles:
            self.execute_update("""
                INSERT OR REPLACE INTO responsibles (responsible_id, name, email, phone, role, team, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, resp + (1,))

        # Generate leads, opportunities, and implementations
        self._generate_business_pipeline_data()

        logger.info("Sample business data created successfully")

    def _generate_business_pipeline_data(self):
        """Generate realistic business pipeline data"""
        # Get universities and responsibles
        universities = self.execute_query("SELECT university_id FROM universities")
        responsibles = self.execute_query("SELECT responsible_id FROM responsibles")

        univ_ids = [u['university_id'] for u in universities]
        resp_ids = [r['responsible_id'] for r in responsibles]

        # Generate leads (500 leads)
        lead_statuses = ['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost']
        sources = ['website', 'referral', 'cold_call', 'event', 'social_media', 'email_campaign']

        for i in range(1, 501):
            lead_id = f'L{i:04d}'
            name = f'Lead {i}'
            email = f'lead{i}@universidade.edu.br'
            phone = f'11999{i:06d}'
            university_id = secrets.choice(univ_ids)
            responsible_id = secrets.choice(resp_ids)
            source = secrets.choice(sources)
            status = secrets.choice(lead_statuses)

            # Create lead with random date in last 12 months
            days_ago = secrets.randbelow(365) + 1
            created_at = (datetime.now() - timedelta(days=days_ago)).isoformat()

            self.execute_update("""
                INSERT OR REPLACE INTO leads
                (lead_id, name, email, phone, university_id, responsible_id, source, status, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (lead_id, name, email, phone, university_id, responsible_id, source, status, created_at, created_at))

        # Generate opportunities (200 opportunities from qualified leads)
        qualified_leads = self.execute_query("SELECT lead_id FROM leads WHERE status IN ('qualified', 'proposal', 'negotiation', 'closed_won')")
        opp_stages = ['qualification', 'proposal', 'negotiation', 'closed_won', 'closed_lost']

        for i, lead in enumerate(qualified_leads[:200], 1):
            opp_id = f'O{i:04d}'
            name = f'Oportunidade {i}'
            value = secrets.randbelow(450000) + 50000  # R$ 50k to 500k
            probability = secrets.randbelow(81) + 10  # 10-90
            stage = secrets.choice(opp_stages)
            responsible_id = secrets.choice(resp_ids)

            # Expected close date
            days_ahead = secrets.randbelow(151) + 30  # 30-180
            expected_close = (datetime.now() + timedelta(days=days_ahead)).date().isoformat()

            days_ago = secrets.randbelow(180) + 1
            created_at = (datetime.now() - timedelta(days=days_ago)).isoformat()

            self.execute_update("""
                INSERT OR REPLACE INTO opportunities
                (opportunity_id, lead_id, name, value, probability, stage, responsible_id, expected_close_date, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (opp_id, lead['lead_id'], name, value, probability, stage, responsible_id, expected_close, created_at, created_at))

        # Generate implementations (80 implementations from won opportunities)
        won_opportunities = self.execute_query("SELECT opportunity_id, value FROM opportunities WHERE stage = 'closed_won'")
        impl_statuses = ['pending', 'in_progress', 'finalized', 'cancelled']

        for i, opp in enumerate(won_opportunities[:80], 1):
            impl_id = f'I{i:04d}'
            university_id = secrets.choice(univ_ids)
            responsible_id = secrets.choice(resp_ids)
            status = secrets.choice(impl_statuses)

            # Implementation dates
            days_ago_start = secrets.randbelow(91) + 30  # 30-120
            start_date = (datetime.now() - timedelta(days=days_ago_start)).date().isoformat()

            if status == 'finalized':
                days_ago_end = secrets.randbelow(30) + 1
                end_date = (datetime.now() - timedelta(days=days_ago_end)).date().isoformat()
            else:
                end_date = None

            created_at = (datetime.now() - timedelta(days=days_ago_start + 10)).isoformat()

            self.execute_update("""
                INSERT OR REPLACE INTO implementations
                (implementation_id, opportunity_id, university_id, responsible_id, status, start_date, end_date, value, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (impl_id, opp['opportunity_id'], university_id, responsible_id, status, start_date, end_date, opp['value'], created_at, created_at))

    def _create_sample_product_data(self):
        """Create sample product data"""
        # Medical specialties for random generation
        specialties = [
            'Cardiologia', 'Dermatologia', 'Ortopedia', 'Pediatria',
            'Ginecologia', 'Neurologia', 'Oftalmologia', 'Psiquiatria',
            'Urologia', 'Endocrinologia', 'Gastroenterologia', 'Pneumologia'
        ]

        states = ['SP', 'RJ', 'MG', 'BA', 'PR', 'RS', 'PE', 'DF', 'SC', 'GO']
        cities = {
            'SP': ['São Paulo', 'Campinas', 'Santos', 'Ribeirão Preto'],
            'RJ': ['Rio de Janeiro', 'Niterói', 'Petrópolis', 'Nova Friburgo'],
            'MG': ['Belo Horizonte', 'Uberlândia', 'Juiz de Fora', 'Contagem'],
            'BA': ['Salvador', 'Feira de Santana', 'Vitória da Conquista'],
            'PR': ['Curitiba', 'Londrina', 'Maringá', 'Ponta Grossa'],
            'RS': ['Porto Alegre', 'Caxias do Sul', 'Pelotas', 'Santa Maria'],
            'PE': ['Recife', 'Olinda', 'Caruaru', 'Petrolina'],
            'DF': ['Brasília'],
            'SC': ['Florianópolis', 'Joinville', 'Blumenau', 'Chapecó'],
            'GO': ['Goiânia', 'Aparecida de Goiânia', 'Anápolis']
        }

        # Create medical users (doctors) - 1876 total users as per mock data
        for i in range(1, 1877):
            user_id = f'DOC{i:04d}'
            name = f'Dr. Médico {i}'
            email = f'medico{i}@email.com'
            specialty = secrets.choice(specialties)
            crm = f'{secrets.randbelow(90000) + 10000}'  # 10000-99999
            state = secrets.choice(states)
            city = secrets.choice(cities[state])
            device_type = secrets.choice(['iOS', 'Android'])
            subscription_type = secrets.choice(['free', 'premium']) if secrets.randbelow(100) >= 60 else 'free'  # 40% premium
            age_range = secrets.choice(['25-30', '31-35', '36-40', '41-45', '46-50', '51+'])
            is_active = 1 if secrets.randbelow(100) >= 18 else 0  # 82% active (1542/1876)

            # Random last login (some users never logged in)
            if secrets.randbelow(100) >= 20:  # 80% have logged in
                days_ago = secrets.randbelow(90) + 1
                last_login = (datetime.now() - timedelta(days=days_ago)).isoformat()
            else:
                last_login = None

            self.execute_update("""
                INSERT OR REPLACE INTO medical_users
                (user_id, name, email, specialty, crm, state, city, device_type,
                 subscription_type, age_range, is_active, last_login)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (user_id, name, email, specialty, crm, state, city, device_type,
                  subscription_type, age_range, is_active, last_login))

        # Create patients (12,450 total patients)
        active_doctors = self.execute_query("SELECT user_id FROM medical_users WHERE is_active = 1")
        doctor_ids = [d['user_id'] for d in active_doctors]

        for i in range(1, 12451):
            patient_id = f'PAT{i:05d}'
            doctor_id = secrets.choice(doctor_ids)
            name = f'Paciente {i}'
            age = secrets.randbelow(91)  # 0-90
            gender = secrets.choice(['Masculino', 'Feminino', 'Outro'])
            insurance_type = secrets.choice(['particular', 'convenio', 'sus'])

            days_ago = secrets.randbelow(730) + 1  # Up to 2 years ago
            created_at = (datetime.now() - timedelta(days=days_ago)).isoformat()

            self.execute_update("""
                INSERT OR REPLACE INTO patients
                (patient_id, doctor_id, name, age, gender, insurance_type, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (patient_id, doctor_id, name, age, gender, insurance_type, created_at))

        # Create feature usage data
        features = [
            'prontuario_eletronico', 'prescricao_digital', 'agenda_consultas',
            'telemedicina', 'exames_laboratoriais', 'historico_paciente',
            'medicamentos_manuais', 'relatorios_medicos', 'backup_dados',
            'integracao_laboratorio', 'chat_paciente', 'notificacoes'
        ]

        for doctor in active_doctors[:1000]:  # Feature usage for 1000 active doctors
            for feature in features:
                if secrets.randbelow(100) >= 30:  # 70% chance doctor uses this feature
                    usage_id = f'FU{secrets.randbelow(900000) + 100000}'  # 100000-999999
                    usage_count = secrets.randbelow(100) + 1  # 1-100
                    days_ago = secrets.randbelow(30) + 1  # 1-30
                    last_used = (datetime.now() - timedelta(days=days_ago)).isoformat()

                    self.execute_update("""
                        INSERT OR REPLACE INTO feature_usage
                        (usage_id, user_id, feature_name, usage_count, last_used)
                        VALUES (?, ?, ?, ?, ?)
                    """, (usage_id, doctor['user_id'], feature, usage_count, last_used))

        logger.info("Sample product data created successfully")

    def add_bruno_bruno_user(self):
        """Add bruno@bruno user to both domains"""
        try:
            import json
            import bcrypt
            import secrets
            import os
            from datetime import datetime

            # Business domain permissions
            business_permissions = [
                "dashboard.view", "leads.view", "leads.edit", "opportunities.view",
                "opportunities.edit", "implementations.view", "implementations.edit",
                "universities.view", "universities.edit", "responsibles.view",
                "responsibles.edit", "classes.view", "classes.edit", "coupons.view",
                "coupons.edit", "conversion.view", "subscriptions.view",
                "data_quality.view", "business_rules.view", "users.manage"
            ]

            # Product domain permissions
            product_permissions = [
                "dashboard.view", "users.view", "users.edit", "features.view",
                "features.edit", "connections.view", "connections.edit",
                "payments.view", "payments.edit", "agenda.view", "agenda.edit",
                "medical_records.view", "medical_records.edit", "accounting.view",
                "accounting.edit", "analytics.view", "users.manage"
            ]

            # SECURITY: Use environment variable for password
            bruno_password = os.getenv('BRUNO_BRUNO_PASSWORD', secrets.token_urlsafe(16))

            # Create bruno@bruno for business domain
            password_hash = bcrypt.hashpw(bruno_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            # Log generated password for first-time setup (remove in production)
            if not os.getenv('BRUNO_BRUNO_PASSWORD'):
                logger.info(f"Generated password for bruno@bruno: {bruno_password}")

            self.execute_update("""
                INSERT OR REPLACE INTO business_users
                (user_id, username, email, password_hash, role, permissions, domain, is_active, created_at, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                'bruno@bruno', 'bruno@bruno', '<EMAIL>', password_hash,
                'admin', json.dumps(business_permissions), 'business', 1,
                datetime.now().isoformat(), json.dumps({'is_default': True})
            ))

            # Create bruno@bruno for product domain
            self.execute_update("""
                INSERT OR REPLACE INTO product_users
                (user_id, username, email, password_hash, role, permissions, domain, is_active, created_at, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                'bruno@bruno', 'bruno@bruno', '<EMAIL>', password_hash,
                'admin', json.dumps(product_permissions), 'product', 1,
                datetime.now().isoformat(), json.dumps({'is_default': True})
            ))

            logger.info("bruno@bruno user added successfully")
            return True

        except Exception as e:
            logger.error(f"Error adding bruno@bruno user: {e}")
            return False
