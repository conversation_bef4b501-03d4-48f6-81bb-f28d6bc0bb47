"""
DataMesh Core - Connection Manager
Manages connections between Business and Product domains
"""

import logging
import pandas as pd
from typing import Optional, Dict, Any, List
from datetime import datetime
from .database_service import DatabaseService

logger = logging.getLogger(__name__)

class DomainConnectionManager:
    """
    Manages data connections and mappings between Business and Product domains
    """
    
    def __init__(self, database_service: DatabaseService):
        """Initialize the connection manager"""
        self.db_service = database_service
        self._ensure_mapping_table()
    
    def _ensure_mapping_table(self):
        """Ensure the domain mapping table exists"""
        try:
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS shared.domain_mapping (
                id SERIAL PRIMARY KEY,
                business_entity_id VARCHAR(255) NOT NULL,
                business_entity_type VARCHAR(50) NOT NULL,
                product_user_id VARCHAR(255),
                product_entity_id VARCHAR(255),
                product_entity_type VARCHAR(50),
                mapping_status VARCHAR(20) DEFAULT 'active',
                metadata JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                -- Indexes for performance
                UNIQUE(business_entity_id, business_entity_type, product_user_id)
            );
            
            -- Create indexes
            CREATE INDEX IF NOT EXISTS idx_domain_mapping_business 
                ON shared.domain_mapping(business_entity_id, business_entity_type);
            CREATE INDEX IF NOT EXISTS idx_domain_mapping_product 
                ON shared.domain_mapping(product_user_id, product_entity_type);
            CREATE INDEX IF NOT EXISTS idx_domain_mapping_status 
                ON shared.domain_mapping(mapping_status);
            """
            
            self.db_service.execute_query(create_table_sql)
            logger.info("Domain mapping table ensured")
            
        except Exception as e:
            logger.error(f"Failed to ensure mapping table: {e}")
            raise
    
    def create_mapping(self, 
                      business_entity_id: str,
                      business_entity_type: str,
                      product_user_id: str,
                      product_entity_id: Optional[str] = None,
                      product_entity_type: Optional[str] = None,
                      metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Create a mapping between business and product entities
        
        Args:
            business_entity_id (str): ID from business domain (e.g., implementation_id)
            business_entity_type (str): Type of business entity ('lead', 'opportunity', 'implementation')
            product_user_id (str): User ID in product domain
            product_entity_id (str, optional): Specific entity ID in product domain
            product_entity_type (str, optional): Type of product entity
            metadata (dict, optional): Additional metadata
            
        Returns:
            bool: Success status
        """
        try:
            insert_sql = """
            INSERT INTO shared.domain_mapping 
            (business_entity_id, business_entity_type, product_user_id, 
             product_entity_id, product_entity_type, metadata)
            VALUES (%(business_entity_id)s, %(business_entity_type)s, %(product_user_id)s,
                    %(product_entity_id)s, %(product_entity_type)s, %(metadata)s)
            ON CONFLICT (business_entity_id, business_entity_type, product_user_id)
            DO UPDATE SET
                product_entity_id = EXCLUDED.product_entity_id,
                product_entity_type = EXCLUDED.product_entity_type,
                metadata = EXCLUDED.metadata,
                updated_at = CURRENT_TIMESTAMP
            """
            
            params = {
                'business_entity_id': business_entity_id,
                'business_entity_type': business_entity_type,
                'product_user_id': product_user_id,
                'product_entity_id': product_entity_id,
                'product_entity_type': product_entity_type,
                'metadata': metadata
            }
            
            self.db_service.execute_query(insert_sql, params)
            
            logger.info(f"Created mapping: {business_entity_type}:{business_entity_id} -> user:{product_user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create mapping: {e}")
            return False
    
    def get_product_user_from_business(self, 
                                     business_entity_id: str,
                                     business_entity_type: str) -> Optional[str]:
        """
        Get product user ID from business entity
        
        Args:
            business_entity_id (str): Business entity ID
            business_entity_type (str): Business entity type
            
        Returns:
            Optional[str]: Product user ID if found
        """
        try:
            query = """
            SELECT product_user_id 
            FROM shared.domain_mapping 
            WHERE business_entity_id = %(business_entity_id)s 
              AND business_entity_type = %(business_entity_type)s
              AND mapping_status = 'active'
            LIMIT 1
            """
            
            params = {
                'business_entity_id': business_entity_id,
                'business_entity_type': business_entity_type
            }
            
            result = self.db_service.execute_query(query, params)
            
            if not result.empty:
                return result.iloc[0]['product_user_id']
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get product user: {e}")
            return None
    
    def get_business_entities_from_user(self, 
                                      product_user_id: str,
                                      business_entity_type: Optional[str] = None) -> pd.DataFrame:
        """
        Get business entities associated with a product user
        
        Args:
            product_user_id (str): Product user ID
            business_entity_type (str, optional): Filter by business entity type
            
        Returns:
            DataFrame: Business entities
        """
        try:
            query = """
            SELECT business_entity_id, business_entity_type, metadata, created_at, updated_at
            FROM shared.domain_mapping 
            WHERE product_user_id = %(product_user_id)s
              AND mapping_status = 'active'
            """
            
            params = {'product_user_id': product_user_id}
            
            if business_entity_type:
                query += " AND business_entity_type = %(business_entity_type)s"
                params['business_entity_type'] = business_entity_type
            
            query += " ORDER BY updated_at DESC"
            
            return self.db_service.execute_query(query, params)
            
        except Exception as e:
            logger.error(f"Failed to get business entities: {e}")
            return pd.DataFrame()
    
    def update_mapping_status(self, 
                            business_entity_id: str,
                            business_entity_type: str,
                            product_user_id: str,
                            status: str) -> bool:
        """
        Update mapping status
        
        Args:
            business_entity_id (str): Business entity ID
            business_entity_type (str): Business entity type
            product_user_id (str): Product user ID
            status (str): New status ('active', 'inactive', 'pending')
            
        Returns:
            bool: Success status
        """
        try:
            update_sql = """
            UPDATE shared.domain_mapping 
            SET mapping_status = %(status)s,
                updated_at = CURRENT_TIMESTAMP
            WHERE business_entity_id = %(business_entity_id)s
              AND business_entity_type = %(business_entity_type)s
              AND product_user_id = %(product_user_id)s
            """
            
            params = {
                'business_entity_id': business_entity_id,
                'business_entity_type': business_entity_type,
                'product_user_id': product_user_id,
                'status': status
            }
            
            self.db_service.execute_query(update_sql, params)
            
            logger.info(f"Updated mapping status to {status}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update mapping status: {e}")
            return False
    
    def get_cross_domain_analytics(self) -> Dict[str, Any]:
        """
        Get analytics data that spans both domains
        
        Returns:
            Dict[str, Any]: Cross-domain analytics
        """
        try:
            # Get mapping statistics
            stats_query = """
            SELECT 
                business_entity_type,
                mapping_status,
                COUNT(*) as count
            FROM shared.domain_mapping
            GROUP BY business_entity_type, mapping_status
            ORDER BY business_entity_type, mapping_status
            """
            
            stats_df = self.db_service.execute_query(stats_query)
            
            # Get recent activity
            activity_query = """
            SELECT 
                business_entity_type,
                COUNT(*) as recent_mappings
            FROM shared.domain_mapping
            WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
            GROUP BY business_entity_type
            ORDER BY recent_mappings DESC
            """
            
            activity_df = self.db_service.execute_query(activity_query)
            
            # Get user engagement (users with multiple business entities)
            engagement_query = """
            SELECT 
                product_user_id,
                COUNT(DISTINCT business_entity_id) as business_entities_count,
                COUNT(DISTINCT business_entity_type) as entity_types_count
            FROM shared.domain_mapping
            WHERE mapping_status = 'active'
            GROUP BY product_user_id
            HAVING COUNT(DISTINCT business_entity_id) > 1
            ORDER BY business_entities_count DESC
            LIMIT 10
            """
            
            engagement_df = self.db_service.execute_query(engagement_query)
            
            return {
                'mapping_statistics': stats_df.to_dict('records') if not stats_df.empty else [],
                'recent_activity': activity_df.to_dict('records') if not activity_df.empty else [],
                'high_engagement_users': engagement_df.to_dict('records') if not engagement_df.empty else [],
                'total_mappings': len(stats_df) if not stats_df.empty else 0,
                'active_mappings': stats_df[stats_df['mapping_status'] == 'active']['count'].sum() if not stats_df.empty else 0
            }
            
        except Exception as e:
            logger.error(f"Failed to get cross-domain analytics: {e}")
            return {}
    
    def sync_implementation_to_user(self, 
                                  implementation_id: str,
                                  user_email: str,
                                  user_name: str,
                                  metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Sync a business implementation to a product user
        
        Args:
            implementation_id (str): Implementation ID from business domain
            user_email (str): User email (used as user ID in product)
            user_name (str): User name
            metadata (dict, optional): Additional sync metadata
            
        Returns:
            bool: Success status
        """
        try:
            # Create or update the mapping
            sync_metadata = {
                'user_email': user_email,
                'user_name': user_name,
                'sync_timestamp': datetime.utcnow().isoformat(),
                'sync_source': 'business_implementation'
            }
            
            if metadata:
                sync_metadata.update(metadata)
            
            return self.create_mapping(
                business_entity_id=implementation_id,
                business_entity_type='implementation',
                product_user_id=user_email,  # Using email as user ID
                product_entity_type='user_account',
                metadata=sync_metadata
            )
            
        except Exception as e:
            logger.error(f"Failed to sync implementation to user: {e}")
            return False
    
    def get_implementation_users(self, implementation_ids: List[str]) -> pd.DataFrame:
        """
        Get product users associated with business implementations
        
        Args:
            implementation_ids (List[str]): List of implementation IDs
            
        Returns:
            DataFrame: Implementation to user mappings
        """
        try:
            if not implementation_ids:
                return pd.DataFrame()
            
            # Convert list to SQL array format
            ids_str = "','".join(implementation_ids)
            
            query = f"""
            SELECT 
                business_entity_id as implementation_id,
                product_user_id as user_id,
                metadata,
                created_at,
                updated_at
            FROM shared.domain_mapping
            WHERE business_entity_id IN ('{ids_str}')
              AND business_entity_type = 'implementation'
              AND mapping_status = 'active'
            ORDER BY updated_at DESC
            """
            
            return self.db_service.execute_query(query)
            
        except Exception as e:
            logger.error(f"Failed to get implementation users: {e}")
            return pd.DataFrame()
    
    def cleanup_inactive_mappings(self, days_old: int = 90) -> int:
        """
        Clean up old inactive mappings
        
        Args:
            days_old (int): Remove mappings older than this many days
            
        Returns:
            int: Number of mappings removed
        """
        try:
            cleanup_sql = """
            DELETE FROM shared.domain_mapping
            WHERE mapping_status = 'inactive'
              AND updated_at < CURRENT_DATE - INTERVAL '%s days'
            """
            
            result = self.db_service.execute_query(cleanup_sql % days_old)
            
            logger.info(f"Cleaned up old inactive mappings")
            return 0  # PostgreSQL doesn't return affected rows in pandas
            
        except Exception as e:
            logger.error(f"Failed to cleanup mappings: {e}")
            return 0
