#!/usr/bin/env python3
"""
Initialize SQLite databases for DataHub Amigo One
Creates separate databases for Business and Product domains
"""

import sys
import os
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from sqlite_manager import SQLiteManager

def init_business_database():
    """Initialize Business domain database"""
    print("🏢 Inicializando banco de dados Business...")

    try:
        db = SQLiteManager('business')

        # SECURITY: Use environment variables for passwords
        import os
        import secrets

        # Generate secure random passwords if not provided via environment
        sergio_password = os.getenv('SERGIO_PASSWORD', secrets.token_urlsafe(16))
        bruno_password = os.getenv('BRUNO_PASSWORD', secrets.token_urlsafe(16))

        # Create some sample users
        sample_users = [
            {
                'username': 'sergio',
                'email': '<EMAIL>',
                'password': sergio_password,
                'role': 'user',
                'permissions': [
                    'dashboard.view', 'leads.view', 'opportunities.view',
                    'implementations.view', 'universities.view', 'data_quality.view'
                ]
            },
            {
                'username': 'bruno',
                'email': '<EMAIL>',
                'password': bruno_password,
                'role': 'user',
                'permissions': [
                    'dashboard.view', 'leads.view', 'opportunities.view',
                    'implementations.view', 'universities.view', 'responsibles.view',
                    'classes.view', 'coupons.view', 'conversion.view',
                    'subscriptions.view', 'business_rules.view'
                ]
            }
        ]

        # Log generated passwords for first-time setup (remove in production)
        if not os.getenv('SERGIO_PASSWORD'):
            print(f"   🔑 Generated password for sergio: {sergio_password}")
        if not os.getenv('BRUNO_PASSWORD'):
            print(f"   🔑 Generated password for bruno: {bruno_password}")

        for user in sample_users:
            try:
                db.create_user(
                    username=user['username'],
                    email=user['email'],
                    password=user['password'],
                    role=user['role'],
                    permissions=user['permissions']
                )
                print(f"   ✅ Usuário {user['username']} criado")
            except ValueError as e:
                print(f"   ⚠️  Usuário {user['username']} já existe")

        # Log some sample activities
        db.log_activity('sergio', 'login', 'Usuário fez login no sistema')
        db.log_activity('bruno', 'page_view', 'Acessou página Dashboard', page='dashboard.index')

        print("   ✅ Banco Business inicializado com sucesso!")

        # Show stats
        stats = db.get_user_stats()
        print(f"   📊 Estatísticas: {stats['total_users']} usuários, {stats['active_users']} ativos")

    except Exception as e:
        print(f"   ❌ Erro ao inicializar banco Business: {e}")

def init_product_database():
    """Initialize Product domain database"""
    print("\n🛍️  Inicializando banco de dados Product...")

    try:
        db = SQLiteManager('product')

        # SECURITY: Use environment variables for passwords
        import os
        import secrets

        # Generate secure random passwords if not provided via environment
        sergio_password = os.getenv('SERGIO_PASSWORD', secrets.token_urlsafe(16))
        bruno_password = os.getenv('BRUNO_PASSWORD', secrets.token_urlsafe(16))

        # Create some sample users
        sample_users = [
            {
                'username': 'sergio',
                'email': '<EMAIL>',
                'password': sergio_password,
                'role': 'user',
                'permissions': [
                    'dashboard.view', 'users.view', 'features.view', 'analytics.view'
                ]
            },
            {
                'username': 'bruno',
                'email': '<EMAIL>',
                'password': bruno_password,
                'role': 'user',
                'permissions': [
                    'dashboard.view', 'users.view', 'features.view',
                    'connections.view', 'payments.view', 'agenda.view',
                    'medical_records.view', 'accounting.view', 'analytics.view'
                ]
            }
        ]

        # Log generated passwords for first-time setup (remove in production)
        if not os.getenv('SERGIO_PASSWORD'):
            print(f"   🔑 Generated password for sergio: {sergio_password}")
        if not os.getenv('BRUNO_PASSWORD'):
            print(f"   🔑 Generated password for bruno: {bruno_password}")

        for user in sample_users:
            try:
                db.create_user(
                    username=user['username'],
                    email=user['email'],
                    password=user['password'],
                    role=user['role'],
                    permissions=user['permissions']
                )
                print(f"   ✅ Usuário {user['username']} criado")
            except ValueError as e:
                print(f"   ⚠️  Usuário {user['username']} já existe")

        # Log some sample activities
        db.log_activity('sergio', 'login', 'Usuário fez login no sistema')
        db.log_activity('bruno', 'page_view', 'Acessou página Usuários', page='users')

        print("   ✅ Banco Product inicializado com sucesso!")

        # Show stats
        stats = db.get_user_stats()
        print(f"   📊 Estatísticas: {stats['total_users']} usuários, {stats['active_users']} ativos")

    except Exception as e:
        print(f"   ❌ Erro ao inicializar banco Product: {e}")

def verify_databases():
    """Verify that databases are working correctly"""
    print("\n🔍 Verificando bancos de dados...")

    try:
        # Test Business database
        business_db = SQLiteManager('business')
        business_users = business_db.get_all_users()
        business_stats = business_db.get_user_stats()

        print(f"   🏢 Business: {len(business_users)} usuários, {business_stats['active_users']} ativos")

        # Test Product database
        product_db = SQLiteManager('product')
        product_users = product_db.get_all_users()
        product_stats = product_db.get_user_stats()

        print(f"   🛍️  Product: {len(product_users)} usuários, {product_stats['active_users']} ativos")

        # Verify isolation
        business_usernames = {user['username'] for user in business_users}
        product_usernames = {user['username'] for user in product_users}
        shared_users = business_usernames.intersection(product_usernames)

        print(f"   🔒 Usuários compartilhados: {len(shared_users)} ({', '.join(shared_users)})")

        # Test permissions
        business_perms = business_db.get_permissions()
        product_perms = product_db.get_permissions()

        print(f"   🔑 Permissões Business: {len(business_perms)}")
        print(f"   🔑 Permissões Product: {len(product_perms)}")

        print("   ✅ Verificação concluída com sucesso!")

    except Exception as e:
        print(f"   ❌ Erro na verificação: {e}")

def show_database_info():
    """Show database file locations and sizes"""
    print("\n📁 Informações dos bancos de dados:")

    # Business database
    business_db_path = Path('gestao/data/business_admin.db')
    if business_db_path.exists():
        size_mb = business_db_path.stat().st_size / (1024 * 1024)
        print(f"   🏢 Business: {business_db_path} ({size_mb:.2f} MB)")
    else:
        print(f"   🏢 Business: {business_db_path} (não existe)")

    # Product database
    product_db_path = Path('produto/ux-gabi/data/product_admin.db')
    if product_db_path.exists():
        size_mb = product_db_path.stat().st_size / (1024 * 1024)
        print(f"   🛍️  Product: {product_db_path} ({size_mb:.2f} MB)")
    else:
        print(f"   🛍️  Product: {product_db_path} (não existe)")

def main():
    """Main initialization function"""
    print("🚀 INICIALIZANDO BANCOS DE DADOS SQLITE - DATAHUB AMIGO ONE")
    print("=" * 70)

    # Change to project root directory
    project_root = Path(__file__).parent.parent.parent.parent
    os.chdir(project_root)
    print(f"📂 Diretório de trabalho: {project_root}")

    # Initialize databases
    init_business_database()
    init_product_database()

    # Verify everything is working
    verify_databases()

    # Show database info
    show_database_info()

    print("\n" + "=" * 70)
    print("🎉 INICIALIZAÇÃO CONCLUÍDA!")
    print("\n💡 Para usar os bancos SQLite:")
    print("   - Os painéis de admin agora consumirão dados do SQLite")
    print("   - Usuários são isolados por domínio")
    print("   - Logs de atividade são armazenados no banco")
    print("   - Fallback para JSON ainda disponível se SQLite falhar")

    print("\n🔐 Usuários admin padrão:")
    print("   - admin / [definido via ADMIN_PASSWORD]")
    print("   - TTK / [definido via TTK_PASSWORD]")
    print("   - bruno@bruno / [definido via BRUNO_BRUNO_PASSWORD]")

    print("\n👥 Usuários de exemplo:")
    print("   - sergio / [definido via SERGIO_PASSWORD ou gerado automaticamente]")
    print("   - bruno / [definido via BRUNO_PASSWORD ou gerado automaticamente]")

    print("\n⚠️  SEGURANÇA: Senhas hardcoded foram removidas!")
    print("   Configure as variáveis de ambiente ou use as senhas geradas automaticamente.")

if __name__ == "__main__":
    main()
