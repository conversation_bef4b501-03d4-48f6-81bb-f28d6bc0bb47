# DataHub Amigo One - Guia de Segurança e Performance

## Visão Geral

Este documento define as melhores práticas de segurança e performance para o DataHub Amigo One, considerando a separação de responsabilidades entre a equipe de aplicação e a equipe de banco de dados.

## 🔒 Arquitetura de Segurança

### Separação de Responsabilidades

#### 🔵 Equipe de Aplicação (Vocês)
- **Autenticação e Autorização**: Controle de acesso à aplicação
- **Validação de Dados**: Sanitização e validação de inputs
- **Segurança da Sessão**: Gerenciamento seguro de sessões
- **Logs de Auditoria**: Monitoramento de ações dos usuários
- **Criptografia de Dados Sensíveis**: Proteção de dados em trânsito
- **Configuração Segura**: Gerenciamento de secrets e configurações

#### 🔴 Equipe de Banco (Eles)
- **Segurança de Rede**: Firewall, VPC, Security Groups
- **Criptografia em Repouso**: Encryption at rest no RDS
- **Backup e Recovery**: Estratégias de backup seguro
- **Monitoramento de Banco**: Detecção de anomalias
- **Patches e Updates**: Manutenção de segurança do PostgreSQL
- **Controle de Acesso ao Banco**: Usuários e permissões de banco

### Implementação de Segurança

#### 1. Configuração de Conexão Segura

```python
# Configuração SSL obrigatória em produção
DB_SSL_MODE=require
SESSION_COOKIE_SECURE=true
SECRET_KEY=your_super_secure_key_minimum_32_characters
```

#### 2. Princípio do Menor Privilégio

```sql
-- Usuário da aplicação com permissões mínimas
CREATE USER app_user WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE amigo_datahub TO app_user;
GRANT USAGE ON SCHEMA datahub TO app_user;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA datahub TO app_user;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA datahub TO app_user;
```

#### 3. Validação e Sanitização

```python
# Sempre usar parâmetros em queries
query = "SELECT * FROM leads WHERE id = %(lead_id)s"
params = {'lead_id': validated_lead_id}
```

## ⚡ Otimização de Performance

### 1. Connection Pooling

```python
# Configuração otimizada de pool de conexões
DB_POOL_SIZE=20          # Conexões ativas
DB_MAX_OVERFLOW=40       # Conexões extras em pico
DB_POOL_TIMEOUT=30       # Timeout para obter conexão
DB_POOL_RECYCLE=3600     # Reciclar conexões a cada hora
```

### 2. Cache Strategy

```python
# Cache em múltiplas camadas
CACHE_DEFAULT_TIMEOUT=600  # 10 minutos para dados frequentes
```

**Estratégia de Cache:**
- **L1 - Application Cache**: Dados estáticos (dimensões)
- **L2 - Redis Cache**: Resultados de queries complexas
- **L3 - Database Cache**: Query plan cache (gerenciado pelo DB team)

### 3. Query Optimization

```python
# Queries otimizadas com índices apropriados
query = """
SELECT l.nome, o.data_criacao, i.status_implantacao
FROM leads l
JOIN oportunidades o ON l.id = o.lead_id
JOIN implantacoes i ON o.id = i.oportunidade_id
WHERE i.data_finalizacao >= %(start_date)s
  AND i.status_implantacao = 'Finalizado'
ORDER BY o.data_criacao DESC
LIMIT 100
"""
```

## 🛡️ Medidas de Segurança Implementadas

### 1. Autenticação e Sessões

```python
# Configuração segura de sessões
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE='Lax'
PERMANENT_SESSION_LIFETIME=3600  # 1 hora
```

### 2. Proteção contra Ataques

- **SQL Injection**: Uso obrigatório de parâmetros
- **XSS**: Sanitização automática via Jinja2
- **CSRF**: Tokens CSRF em formulários
- **Session Hijacking**: Cookies seguros e HTTPOnly

### 3. Monitoramento e Auditoria

```python
# Logs estruturados para auditoria
logger.info("User action", extra={
    'user_id': user_id,
    'action': 'data_access',
    'resource': 'leads',
    'timestamp': datetime.utcnow(),
    'ip_address': request.remote_addr
})
```

## 📊 Monitoramento de Performance

### Métricas da Aplicação

1. **Response Time**: < 200ms para queries simples
2. **Throughput**: > 100 requests/segundo
3. **Error Rate**: < 1%
4. **Cache Hit Rate**: > 80%

### Métricas de Banco (Monitoradas pelo DB Team)

1. **Connection Pool Usage**: < 80%
2. **Query Performance**: Slow queries < 1s
3. **Lock Contention**: Minimal blocking
4. **Storage Usage**: Monitored growth

## 🔧 Configuração por Ambiente

### Development
```bash
DATA_SOURCE_MODE=files
DB_POOL_SIZE=5
CACHE_DEFAULT_TIMEOUT=60
SESSION_COOKIE_SECURE=false
```

### Production
```bash
DATA_SOURCE_MODE=database
DB_POOL_SIZE=20
DB_SSL_MODE=require
CACHE_DEFAULT_TIMEOUT=600
SESSION_COOKIE_SECURE=true
```

## 🚨 Plano de Contingência

### Fallback Strategy

1. **Database Unavailable**: Automatic fallback to cached data
2. **Cache Unavailable**: Direct database queries with rate limiting
3. **Performance Degradation**: Circuit breaker pattern

### Disaster Recovery

1. **Data Backup**: Managed by DB team (daily backups, PITR)
2. **Application Recovery**: Containerized deployment with health checks
3. **Monitoring**: Automated alerts for critical failures

## 📋 Checklist de Segurança

### Pré-Produção
- [ ] SSL/TLS configurado corretamente
- [ ] Secrets não expostos no código
- [ ] Validação de inputs implementada
- [ ] Logs de auditoria funcionando
- [ ] Connection pooling configurado
- [ ] Cache funcionando corretamente

### Produção
- [ ] Monitoramento ativo
- [ ] Backups testados
- [ ] Plano de disaster recovery documentado
- [ ] Acesso restrito por IP/VPN
- [ ] Rotação regular de passwords
- [ ] Auditoria de segurança periódica

## 🤝 Interface com Equipe de Banco

### Comunicação Regular

1. **Weekly Sync**: Status de performance e segurança
2. **Monthly Review**: Análise de métricas e otimizações
3. **Incident Response**: Protocolo de comunicação em emergências

### SLA Acordado

- **Availability**: 99.9% uptime
- **Response Time**: < 100ms para queries simples
- **Backup Recovery**: RTO < 4 horas, RPO < 1 hora
- **Security Patches**: Aplicados em 48 horas

### Responsabilidades Compartilhadas

| Área | Aplicação | Banco |
|------|-----------|-------|
| Performance Monitoring | ✅ App metrics | ✅ DB metrics |
| Security Updates | ✅ App dependencies | ✅ PostgreSQL |
| Backup Strategy | ✅ Config backup | ✅ Data backup |
| Incident Response | ✅ App issues | ✅ DB issues |
| Capacity Planning | ✅ Load testing | ✅ Storage planning |

## 📞 Contatos de Emergência

- **Equipe de Aplicação**: [Seus contatos]
- **Equipe de Banco**: [Contatos do DB team]
- **DevOps/Infraestrutura**: [Contatos de infra]

## 📚 Referências

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [PostgreSQL Security](https://www.postgresql.org/docs/current/security.html)
- [Flask Security](https://flask.palletsprojects.com/en/2.3.x/security/)
- [Redis Security](https://redis.io/docs/management/security/)
