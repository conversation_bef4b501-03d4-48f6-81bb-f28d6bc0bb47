"""
DataMesh Core - User Management Service
Manages users, roles, and permissions for each domain
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from .auth_service import AuthService, PermissionManager, AuditLogger

logger = logging.getLogger(__name__)

class User:
    """User model"""

    def __init__(self, user_id: str, username: str, email: str,
                 role: str, permissions: List[str], domain: str,
                 created_at: str = None, last_login: str = None,
                 is_active: bool = True, metadata: Dict[str, Any] = None):
        """Initialize user"""
        self.user_id = user_id
        self.username = username
        self.email = email
        self.role = role
        self.permissions = permissions
        self.domain = domain
        self.created_at = created_at or datetime.utcnow().isoformat()
        self.last_login = last_login
        self.is_active = is_active
        self.metadata = metadata or {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert user to dictionary"""
        return {
            'user_id': self.user_id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'permissions': self.permissions,
            'domain': self.domain,
            'created_at': self.created_at,
            'last_login': self.last_login,
            'is_active': self.is_active,
            'metadata': self.metadata
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """Create user from dictionary"""
        return cls(**data)

class UserManager:
    """Manages users for a specific domain"""

    def __init__(self, domain_name: str, storage_path: str = None):
        """Initialize user manager"""
        self.domain_name = domain_name
        self.storage_path = storage_path or f"{domain_name}_users.json"
        self.users = {}
        self.auth_service = AuthService(domain_name)
        self.permission_manager = PermissionManager(domain_name)
        self.audit_logger = AuditLogger(domain_name)

        # Load existing users
        self._load_users()

        # Create default admin user if no users exist
        if not self.users:
            self._create_default_admin()

    def _load_users(self):
        """Load users from storage"""
        try:
            with open(self.storage_path, 'r') as f:
                users_data = json.load(f)
                self.users = {
                    user_id: User.from_dict(user_data)
                    for user_id, user_data in users_data.items()
                }
            logger.info(f"Loaded {len(self.users)} users for domain {self.domain_name}")
        except FileNotFoundError:
            logger.info(f"No existing users file found for domain {self.domain_name}")
            self.users = {}
        except Exception as e:
            logger.error(f"Error loading users: {e}")
            self.users = {}

    def _save_users(self):
        """Save users to storage"""
        try:
            # Create directory if it doesn't exist
            import os
            os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)

            users_data = {
                user_id: user.to_dict()
                for user_id, user in self.users.items()
            }
            with open(self.storage_path, 'w') as f:
                json.dump(users_data, f, indent=2)
            logger.info(f"Saved {len(self.users)} users for domain {self.domain_name}")
        except Exception as e:
            logger.error(f"Error saving users: {e}")
            raise

    def _create_default_admin(self):
        """Create default admin users"""
        try:
            admin_permissions = self.permission_manager.get_user_permissions('admin')

            # Create default admin user
            admin_user = User(
                user_id='admin',
                username='admin',
                email='<EMAIL>',
                role='admin',
                permissions=admin_permissions,
                domain=self.domain_name,
                metadata={'is_default': True}
            )

            # Set default password
            password_hash = self.auth_service.hash_password('admin123')
            admin_user.metadata['password_hash'] = password_hash

            self.users['admin'] = admin_user

            # Create TTK admin user
            ttk_user = User(
                user_id='TTK',
                username='TTK',
                email='<EMAIL>',
                role='admin',
                permissions=admin_permissions,
                domain=self.domain_name,
                metadata={
                    'is_default': True,
                    'description': 'TTK Administrator Account'
                }
            )

            # Set TTK password
            ttk_password_hash = self.auth_service.hash_password('TTK2024!')
            ttk_user.metadata['password_hash'] = ttk_password_hash

            self.users['TTK'] = ttk_user

            self._save_users()

            logger.info(f"Created default admin users (admin, TTK) for domain {self.domain_name}")

        except Exception as e:
            logger.error(f"Error creating default admin: {e}")
            raise

    def create_user(self, username: str, email: str, password: str,
                   role: str, admin_user_id: str) -> Optional[User]:
        """Create new user"""
        try:
            # Validate role
            if role not in self.permission_manager.permissions:
                raise ValueError(f"Invalid role: {role}")

            # Check if user already exists
            user_id = username.lower()
            if user_id in self.users:
                raise ValueError(f"User {username} already exists")

            # Get permissions for role
            permissions = self.permission_manager.get_user_permissions(role)

            # Create user
            user = User(
                user_id=user_id,
                username=username,
                email=email,
                role=role,
                permissions=permissions,
                domain=self.domain_name
            )

            # Hash password
            password_hash = self.auth_service.hash_password(password)
            user.metadata['password_hash'] = password_hash

            # Save user
            self.users[user_id] = user
            self._save_users()

            # Log audit event
            self.audit_logger.log_permission_change(
                admin_user_id=admin_user_id,
                target_user_id=user_id,
                old_permissions=[],
                new_permissions=permissions
            )

            logger.info(f"Created user {username} with role {role}")
            return user

        except Exception as e:
            logger.error(f"Error creating user: {e}")
            raise

    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate user"""
        try:
            user_id = username.lower()
            user = self.users.get(user_id)

            if not user or not user.is_active:
                self.audit_logger.log_login(user_id, False)
                return None

            password_hash = user.metadata.get('password_hash')
            if not password_hash:
                self.audit_logger.log_login(user_id, False)
                return None

            if self.auth_service.verify_password(password, password_hash):
                # Update last login
                user.last_login = datetime.utcnow().isoformat()
                self._save_users()

                self.audit_logger.log_login(user_id, True)
                return user
            else:
                self.audit_logger.log_login(user_id, False)
                return None

        except Exception as e:
            logger.error(f"Error authenticating user: {e}")
            return None

    def update_user_role(self, user_id: str, new_role: str, admin_user_id: str) -> bool:
        """Update user role and permissions"""
        try:
            user = self.users.get(user_id)
            if not user:
                raise ValueError(f"User {user_id} not found")

            if new_role not in self.permission_manager.permissions:
                raise ValueError(f"Invalid role: {new_role}")

            old_permissions = user.permissions.copy()
            new_permissions = self.permission_manager.get_user_permissions(new_role)

            user.role = new_role
            user.permissions = new_permissions

            self._save_users()

            # Log audit event
            self.audit_logger.log_permission_change(
                admin_user_id=admin_user_id,
                target_user_id=user_id,
                old_permissions=old_permissions,
                new_permissions=new_permissions
            )

            logger.info(f"Updated user {user_id} role to {new_role}")
            return True

        except Exception as e:
            logger.error(f"Error updating user role: {e}")
            raise

    def deactivate_user(self, user_id: str, admin_user_id: str) -> bool:
        """Deactivate user"""
        try:
            user = self.users.get(user_id)
            if not user:
                raise ValueError(f"User {user_id} not found")

            user.is_active = False
            self._save_users()

            # Log audit event
            self.audit_logger.log_permission_change(
                admin_user_id=admin_user_id,
                target_user_id=user_id,
                old_permissions=user.permissions,
                new_permissions=[]
            )

            logger.info(f"Deactivated user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error deactivating user: {e}")
            raise

    def activate_user(self, user_id: str, admin_user_id: str) -> bool:
        """Activate user"""
        try:
            user = self.users.get(user_id)
            if not user:
                raise ValueError(f"User {user_id} not found")

            user.is_active = True
            self._save_users()

            # Log audit event
            self.audit_logger.log_permission_change(
                admin_user_id=admin_user_id,
                target_user_id=user_id,
                old_permissions=[],
                new_permissions=user.permissions
            )

            logger.info(f"Activated user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error activating user: {e}")
            raise

    def change_password(self, user_id: str, old_password: str, new_password: str) -> bool:
        """Change user password"""
        try:
            user = self.users.get(user_id)
            if not user:
                raise ValueError(f"User {user_id} not found")

            # Verify old password
            old_password_hash = user.metadata.get('password_hash')
            if not old_password_hash or not self.auth_service.verify_password(old_password, old_password_hash):
                raise ValueError("Invalid old password")

            # Set new password
            new_password_hash = self.auth_service.hash_password(new_password)
            user.metadata['password_hash'] = new_password_hash

            self._save_users()

            logger.info(f"Password changed for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error changing password: {e}")
            raise

    def reset_password(self, user_id: str, new_password: str, admin_user_id: str) -> bool:
        """Reset user password (admin only)"""
        try:
            user = self.users.get(user_id)
            if not user:
                raise ValueError(f"User {user_id} not found")

            # Set new password
            new_password_hash = self.auth_service.hash_password(new_password)
            user.metadata['password_hash'] = new_password_hash

            self._save_users()

            logger.info(f"Password reset for user {user_id} by admin {admin_user_id}")
            return True

        except Exception as e:
            logger.error(f"Error resetting password: {e}")
            raise

    def get_user(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        return self.users.get(user_id)

    def get_all_users(self) -> List[User]:
        """Get all users"""
        return list(self.users.values())

    def get_active_users(self) -> List[User]:
        """Get all active users"""
        return [user for user in self.users.values() if user.is_active]

    def get_users_by_role(self, role: str) -> List[User]:
        """Get users by role"""
        return [user for user in self.users.values() if user.role == role]

    def get_user_stats(self) -> Dict[str, Any]:
        """Get user statistics"""
        total_users = len(self.users)
        active_users = len(self.get_active_users())

        role_counts = {}
        for user in self.users.values():
            role_counts[user.role] = role_counts.get(user.role, 0) + 1

        return {
            'total_users': total_users,
            'active_users': active_users,
            'inactive_users': total_users - active_users,
            'role_distribution': role_counts,
            'domain': self.domain_name
        }
