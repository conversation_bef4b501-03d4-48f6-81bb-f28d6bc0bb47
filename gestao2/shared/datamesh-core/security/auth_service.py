"""
DataMesh Core - Authentication Service
Shared authentication and authorization service for all domains
"""

import os
import hashlib
import secrets
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from functools import wraps
from flask import session, request, jsonify, redirect, url_for, current_app
import bcrypt

logger = logging.getLogger(__name__)

class AuthService:
    """Shared authentication service for DataMesh domains"""
    
    def __init__(self, domain_name: str):
        """Initialize auth service for specific domain"""
        self.domain_name = domain_name
        self.session_timeout = 3600  # 1 hour
        
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        try:
            salt = bcrypt.gensalt()
            hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
            return hashed.decode('utf-8')
        except Exception as e:
            logger.error(f"Error hashing password: {e}")
            raise
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except Exception as e:
            logger.error(f"Error verifying password: {e}")
            return False
    
    def generate_session_token(self) -> str:
        """Generate secure session token"""
        return secrets.token_urlsafe(32)
    
    def create_session(self, user_id: str, user_data: Dict[str, Any]) -> str:
        """Create user session"""
        try:
            session_token = self.generate_session_token()
            
            # Store in Flask session
            session['user_id'] = user_id
            session['user_data'] = user_data
            session['domain'] = self.domain_name
            session['session_token'] = session_token
            session['created_at'] = datetime.utcnow().isoformat()
            session['last_activity'] = datetime.utcnow().isoformat()
            session.permanent = True
            
            logger.info(f"Session created for user {user_id} in domain {self.domain_name}")
            return session_token
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            raise
    
    def validate_session(self) -> Optional[Dict[str, Any]]:
        """Validate current session"""
        try:
            if 'user_id' not in session:
                return None
            
            # Check session timeout
            last_activity = datetime.fromisoformat(session.get('last_activity', ''))
            if datetime.utcnow() - last_activity > timedelta(seconds=self.session_timeout):
                self.destroy_session()
                return None
            
            # Update last activity
            session['last_activity'] = datetime.utcnow().isoformat()
            
            return {
                'user_id': session['user_id'],
                'user_data': session.get('user_data', {}),
                'domain': session.get('domain'),
                'session_token': session.get('session_token')
            }
            
        except Exception as e:
            logger.error(f"Error validating session: {e}")
            return None
    
    def destroy_session(self):
        """Destroy current session"""
        try:
            user_id = session.get('user_id', 'unknown')
            session.clear()
            logger.info(f"Session destroyed for user {user_id}")
        except Exception as e:
            logger.error(f"Error destroying session: {e}")
    
    def require_auth(self, f):
        """Decorator to require authentication"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            session_data = self.validate_session()
            if not session_data:
                if request.is_json:
                    return jsonify({'error': 'Authentication required'}), 401
                return redirect(url_for(f'{self.domain_name}.login'))
            return f(*args, **kwargs)
        return decorated_function
    
    def require_permission(self, permission: str):
        """Decorator to require specific permission"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                session_data = self.validate_session()
                if not session_data:
                    if request.is_json:
                        return jsonify({'error': 'Authentication required'}), 401
                    return redirect(url_for(f'{self.domain_name}.login'))
                
                user_permissions = session_data.get('user_data', {}).get('permissions', [])
                if permission not in user_permissions and 'admin' not in user_permissions:
                    if request.is_json:
                        return jsonify({'error': 'Insufficient permissions'}), 403
                    return jsonify({'error': 'Access denied'}), 403
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator

class PermissionManager:
    """Manages permissions for domain-specific pages and features"""
    
    def __init__(self, domain_name: str):
        """Initialize permission manager for specific domain"""
        self.domain_name = domain_name
        self.permissions = self._load_domain_permissions()
    
    def _load_domain_permissions(self) -> Dict[str, List[str]]:
        """Load permissions specific to domain"""
        if self.domain_name == 'business':
            return {
                'admin': [
                    'dashboard.view', 'leads.view', 'leads.edit', 'opportunities.view', 
                    'opportunities.edit', 'implementations.view', 'implementations.edit',
                    'universities.view', 'universities.edit', 'responsibles.view', 
                    'responsibles.edit', 'classes.view', 'classes.edit', 'coupons.view',
                    'coupons.edit', 'conversion.view', 'subscriptions.view', 
                    'data_quality.view', 'business_rules.view', 'users.manage'
                ],
                'manager': [
                    'dashboard.view', 'leads.view', 'opportunities.view', 
                    'implementations.view', 'universities.view', 'responsibles.view',
                    'classes.view', 'coupons.view', 'conversion.view', 'subscriptions.view',
                    'data_quality.view'
                ],
                'analyst': [
                    'dashboard.view', 'leads.view', 'opportunities.view',
                    'implementations.view', 'conversion.view', 'data_quality.view'
                ],
                'viewer': [
                    'dashboard.view', 'leads.view', 'opportunities.view'
                ]
            }
        elif self.domain_name == 'product':
            return {
                'admin': [
                    'dashboard.view', 'users.view', 'users.edit', 'features.view',
                    'features.edit', 'connections.view', 'connections.edit',
                    'payments.view', 'payments.edit', 'agenda.view', 'agenda.edit',
                    'medical_records.view', 'medical_records.edit', 'accounting.view',
                    'accounting.edit', 'analytics.view', 'users.manage'
                ],
                'manager': [
                    'dashboard.view', 'users.view', 'features.view', 'connections.view',
                    'payments.view', 'agenda.view', 'analytics.view'
                ],
                'analyst': [
                    'dashboard.view', 'users.view', 'features.view', 'analytics.view'
                ],
                'viewer': [
                    'dashboard.view', 'users.view'
                ]
            }
        else:
            return {}
    
    def get_user_permissions(self, user_role: str) -> List[str]:
        """Get permissions for user role"""
        return self.permissions.get(user_role, [])
    
    def has_permission(self, user_permissions: List[str], required_permission: str) -> bool:
        """Check if user has required permission"""
        return required_permission in user_permissions or 'admin' in user_permissions
    
    def get_accessible_pages(self, user_permissions: List[str]) -> List[Dict[str, str]]:
        """Get list of pages user can access"""
        accessible_pages = []
        
        if self.domain_name == 'business':
            page_permissions = {
                'Dashboard': 'dashboard.view',
                'Leads': 'leads.view',
                'Oportunidades': 'opportunities.view',
                'Implantações': 'implementations.view',
                'Universidades': 'universities.view',
                'Responsáveis': 'responsibles.view',
                'Turmas': 'classes.view',
                'Cupons': 'coupons.view',
                'Conversão': 'conversion.view',
                'Assinaturas': 'subscriptions.view',
                'Qualidade de Dados': 'data_quality.view',
                'Regras de Negócio': 'business_rules.view',
                'Gerenciar Usuários': 'users.manage'
            }
        elif self.domain_name == 'product':
            page_permissions = {
                'Dashboard': 'dashboard.view',
                'Usuários': 'users.view',
                'Features': 'features.view',
                'Conexões': 'connections.view',
                'Pagamentos': 'payments.view',
                'Agenda': 'agenda.view',
                'Prontuários': 'medical_records.view',
                'Contabilidade': 'accounting.view',
                'Analytics': 'analytics.view',
                'Gerenciar Usuários': 'users.manage'
            }
        else:
            page_permissions = {}
        
        for page_name, permission in page_permissions.items():
            if self.has_permission(user_permissions, permission):
                accessible_pages.append({
                    'name': page_name,
                    'permission': permission
                })
        
        return accessible_pages

class AuditLogger:
    """Audit logging for security events"""
    
    def __init__(self, domain_name: str):
        """Initialize audit logger for specific domain"""
        self.domain_name = domain_name
    
    def log_login(self, user_id: str, success: bool, ip_address: str = None):
        """Log login attempt"""
        try:
            event_data = {
                'domain': self.domain_name,
                'event_type': 'login',
                'user_id': user_id,
                'success': success,
                'ip_address': ip_address or request.remote_addr,
                'user_agent': request.headers.get('User-Agent', ''),
                'timestamp': datetime.utcnow().isoformat()
            }
            
            logger.info(f"Login attempt: {event_data}")
            # Here you could also store in database for audit trail
            
        except Exception as e:
            logger.error(f"Error logging login attempt: {e}")
    
    def log_logout(self, user_id: str):
        """Log logout"""
        try:
            event_data = {
                'domain': self.domain_name,
                'event_type': 'logout',
                'user_id': user_id,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            logger.info(f"Logout: {event_data}")
            
        except Exception as e:
            logger.error(f"Error logging logout: {e}")
    
    def log_access(self, user_id: str, resource: str, action: str, success: bool):
        """Log resource access"""
        try:
            event_data = {
                'domain': self.domain_name,
                'event_type': 'access',
                'user_id': user_id,
                'resource': resource,
                'action': action,
                'success': success,
                'ip_address': request.remote_addr,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            logger.info(f"Resource access: {event_data}")
            
        except Exception as e:
            logger.error(f"Error logging access: {e}")
    
    def log_permission_change(self, admin_user_id: str, target_user_id: str, 
                            old_permissions: List[str], new_permissions: List[str]):
        """Log permission changes"""
        try:
            event_data = {
                'domain': self.domain_name,
                'event_type': 'permission_change',
                'admin_user_id': admin_user_id,
                'target_user_id': target_user_id,
                'old_permissions': old_permissions,
                'new_permissions': new_permissions,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            logger.warning(f"Permission change: {event_data}")
            
        except Exception as e:
            logger.error(f"Error logging permission change: {e}")
