"""
User Invitation Service
Handles user invitations and registration process
"""

import json
import uuid
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
import secrets
import string

logger = logging.getLogger(__name__)

class UserInvitation:
    """User invitation model"""
    
    def __init__(self, invitation_id: str, email: str, role: str, permissions: List[str], 
                 domain: str, invited_by: str, expires_at: str, status: str = 'pending',
                 created_at: str = None, token: str = None):
        self.invitation_id = invitation_id
        self.email = email
        self.role = role
        self.permissions = permissions
        self.domain = domain
        self.invited_by = invited_by
        self.expires_at = expires_at
        self.status = status  # pending, accepted, expired, cancelled
        self.created_at = created_at or datetime.now().isoformat()
        self.token = token or self._generate_token()
    
    def _generate_token(self):
        """Generate secure invitation token"""
        return secrets.token_urlsafe(32)
    
    def is_expired(self):
        """Check if invitation is expired"""
        try:
            expires = datetime.fromisoformat(self.expires_at)
            return datetime.now() > expires
        except:
            return True
    
    def is_valid(self):
        """Check if invitation is valid"""
        return self.status == 'pending' and not self.is_expired()
    
    def to_dict(self):
        """Convert to dictionary"""
        return {
            'invitation_id': self.invitation_id,
            'email': self.email,
            'role': self.role,
            'permissions': self.permissions,
            'domain': self.domain,
            'invited_by': self.invited_by,
            'expires_at': self.expires_at,
            'status': self.status,
            'created_at': self.created_at,
            'token': self.token
        }
    
    @classmethod
    def from_dict(cls, data: Dict):
        """Create from dictionary"""
        return cls(**data)

class InvitationService:
    """Service to manage user invitations"""
    
    def __init__(self, domain_name: str, storage_path: str = None):
        self.domain_name = domain_name
        self.storage_path = storage_path or f'data/{domain_name}_invitations.json'
        self.invitations: Dict[str, UserInvitation] = {}
        self._load_invitations()
    
    def _load_invitations(self):
        """Load invitations from storage"""
        try:
            if Path(self.storage_path).exists():
                with open(self.storage_path, 'r') as f:
                    data = json.load(f)
                    self.invitations = {
                        inv_id: UserInvitation.from_dict(inv_data)
                        for inv_id, inv_data in data.items()
                    }
                logger.info(f"Loaded {len(self.invitations)} invitations for domain {self.domain_name}")
            else:
                logger.info(f"No existing invitations file found for domain {self.domain_name}")
        except Exception as e:
            logger.error(f"Error loading invitations: {e}")
            self.invitations = {}
    
    def _save_invitations(self):
        """Save invitations to storage"""
        try:
            # Create directory if it doesn't exist
            import os
            os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
            
            data = {
                inv_id: invitation.to_dict()
                for inv_id, invitation in self.invitations.items()
            }
            
            with open(self.storage_path, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Saved {len(self.invitations)} invitations for domain {self.domain_name}")
        except Exception as e:
            logger.error(f"Error saving invitations: {e}")
            raise
    
    def create_invitation(self, email: str, role: str, permissions: List[str], 
                         invited_by: str, expires_in_days: int = 7) -> UserInvitation:
        """Create a new user invitation"""
        
        # Check if user already has a pending invitation
        existing = self.get_invitation_by_email(email)
        if existing and existing.is_valid():
            raise ValueError(f"User {email} already has a pending invitation")
        
        # Generate invitation
        invitation_id = str(uuid.uuid4())
        expires_at = (datetime.now() + timedelta(days=expires_in_days)).isoformat()
        
        invitation = UserInvitation(
            invitation_id=invitation_id,
            email=email,
            role=role,
            permissions=permissions,
            domain=self.domain_name,
            invited_by=invited_by,
            expires_at=expires_at
        )
        
        self.invitations[invitation_id] = invitation
        self._save_invitations()
        
        logger.info(f"Created invitation for {email} with role {role} in domain {self.domain_name}")
        return invitation
    
    def get_invitation(self, invitation_id: str) -> Optional[UserInvitation]:
        """Get invitation by ID"""
        return self.invitations.get(invitation_id)
    
    def get_invitation_by_email(self, email: str) -> Optional[UserInvitation]:
        """Get pending invitation by email"""
        for invitation in self.invitations.values():
            if invitation.email == email and invitation.status == 'pending':
                return invitation
        return None
    
    def get_invitation_by_token(self, token: str) -> Optional[UserInvitation]:
        """Get invitation by token"""
        for invitation in self.invitations.values():
            if invitation.token == token:
                return invitation
        return None
    
    def validate_invitation(self, token: str) -> Optional[UserInvitation]:
        """Validate invitation token"""
        invitation = self.get_invitation_by_token(token)
        
        if not invitation:
            return None
        
        if not invitation.is_valid():
            return None
        
        return invitation
    
    def accept_invitation(self, token: str) -> Optional[UserInvitation]:
        """Accept an invitation"""
        invitation = self.validate_invitation(token)
        
        if not invitation:
            return None
        
        invitation.status = 'accepted'
        self._save_invitations()
        
        logger.info(f"Invitation accepted for {invitation.email}")
        return invitation
    
    def cancel_invitation(self, invitation_id: str, cancelled_by: str) -> bool:
        """Cancel an invitation"""
        invitation = self.get_invitation(invitation_id)
        
        if not invitation:
            return False
        
        invitation.status = 'cancelled'
        self._save_invitations()
        
        logger.info(f"Invitation cancelled for {invitation.email} by {cancelled_by}")
        return True
    
    def get_all_invitations(self, include_expired: bool = False) -> List[UserInvitation]:
        """Get all invitations"""
        invitations = list(self.invitations.values())
        
        if not include_expired:
            invitations = [inv for inv in invitations if not inv.is_expired()]
        
        # Sort by creation date (newest first)
        invitations.sort(key=lambda x: x.created_at, reverse=True)
        return invitations
    
    def get_pending_invitations(self) -> List[UserInvitation]:
        """Get all pending invitations"""
        return [inv for inv in self.invitations.values() if inv.is_valid()]
    
    def cleanup_expired_invitations(self) -> int:
        """Remove expired invitations"""
        expired_count = 0
        expired_ids = []
        
        for inv_id, invitation in self.invitations.items():
            if invitation.is_expired() and invitation.status == 'pending':
                invitation.status = 'expired'
                expired_count += 1
                expired_ids.append(inv_id)
        
        if expired_count > 0:
            self._save_invitations()
            logger.info(f"Marked {expired_count} invitations as expired")
        
        return expired_count
    
    def get_invitation_stats(self) -> Dict:
        """Get invitation statistics"""
        total = len(self.invitations)
        pending = len([inv for inv in self.invitations.values() if inv.status == 'pending'])
        accepted = len([inv for inv in self.invitations.values() if inv.status == 'accepted'])
        expired = len([inv for inv in self.invitations.values() if inv.status == 'expired'])
        cancelled = len([inv for inv in self.invitations.values() if inv.status == 'cancelled'])
        
        return {
            'total': total,
            'pending': pending,
            'accepted': accepted,
            'expired': expired,
            'cancelled': cancelled
        }
    
    def generate_invitation_url(self, invitation: UserInvitation, base_url: str) -> str:
        """Generate invitation URL"""
        return f"{base_url}/auth/register?token={invitation.token}"
