# DataHub Amigo One - Environment Configuration
# Copy this file to .env and configure your environment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
FLASK_ENV=development
SECRET_KEY=your_super_secret_key_change_in_production

# Data source mode: 'files' or 'database'
DATA_SOURCE_MODE=files

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL RDS)
# =============================================================================
# Production Database (managed by DB team)
DB_HOST=your-rds-endpoint.region.rds.amazonaws.com
DB_PORT=5432
DB_NAME=amigo_datahub
DB_USER=app_user
DB_PASSWORD=your_secure_password
DB_SCHEMA=datahub

# Development Database (optional local instance)
DEV_DB_HOST=localhost
DEV_DB_NAME=amigo_datahub_dev

# Test Database
TEST_DB_NAME=amigo_datahub_test

# =============================================================================
# DATABASE PERFORMANCE SETTINGS
# =============================================================================
# Connection pool settings for optimal performance
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Production settings (higher values for production)
# DB_POOL_SIZE=20
# DB_MAX_OVERFLOW=40

# =============================================================================
# DATABASE SECURITY SETTINGS
# =============================================================================
# SSL Configuration (required for RDS)
DB_SSL_MODE=require
# DB_SSL_CERT=/path/to/client-cert.pem
# DB_SSL_KEY=/path/to/client-key.pem
# DB_SSL_ROOT_CERT=/path/to/ca-cert.pem

# =============================================================================
# CACHE CONFIGURATION (Redis)
# =============================================================================
CACHE_TYPE=redis
CACHE_REDIS_HOST=localhost
CACHE_REDIS_PORT=6379
CACHE_REDIS_DB=0
# CACHE_REDIS_PASSWORD=your_redis_password

# Cache timeout in seconds
CACHE_DEFAULT_TIMEOUT=300

# Production cache settings (longer timeout for production)
# CACHE_DEFAULT_TIMEOUT=600

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# Session security
SESSION_COOKIE_SECURE=false
PERMANENT_SESSION_LIFETIME=3600

# Production security settings
# SESSION_COOKIE_SECURE=true

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================
# Application monitoring
LOG_LEVEL=INFO

# =============================================================================
# INTEGRATION SETTINGS
# =============================================================================
# Product app integration
PRODUCT_APP_URL=http://localhost:5001

# =============================================================================
# PERFORMANCE OPTIMIZATION
# =============================================================================
# Enable/disable specific features for performance
ENABLE_QUERY_CACHE=true
ENABLE_DATA_COMPRESSION=true

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Development specific settings
FLASK_DEBUG=true

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================
# Uncomment and configure for production deployment

# Application
# FLASK_ENV=production
# SECRET_KEY=your_production_secret_key_minimum_32_characters

# Database
# DATA_SOURCE_MODE=database
# DB_SSL_MODE=require

# Security
# SESSION_COOKIE_SECURE=true

# Performance
# DB_POOL_SIZE=20
# DB_MAX_OVERFLOW=40
# CACHE_DEFAULT_TIMEOUT=600

# =============================================================================
# AWS SPECIFIC SETTINGS (if using AWS services)
# =============================================================================
# AWS_REGION=us-east-1
# AWS_ACCESS_KEY_ID=your_access_key
# AWS_SECRET_ACCESS_KEY=your_secret_key

# =============================================================================
# BACKUP AND DISASTER RECOVERY
# =============================================================================
# Backup settings (managed by DB team, but app needs to know)
# BACKUP_RETENTION_DAYS=30
# POINT_IN_TIME_RECOVERY=true

# =============================================================================
# COMPLIANCE AND AUDIT
# =============================================================================
# Audit logging
# ENABLE_AUDIT_LOG=true
# AUDIT_LOG_LEVEL=INFO

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable features
ENABLE_RESPONSIBLE_ANALYSIS=true
ENABLE_CLASS_ANALYSIS=true
ENABLE_COUPON_ANALYSIS=true
ENABLE_DATA_QUALITY_ANALYSIS=true

# =============================================================================
# EXTERNAL INTEGRATIONS
# =============================================================================
# If you have external APIs or services
# EXTERNAL_API_URL=https://api.example.com
# EXTERNAL_API_KEY=your_api_key

# =============================================================================
# NOTES FOR PRODUCTION DEPLOYMENT
# =============================================================================
# 1. Never commit .env files to version control
# 2. Use environment-specific .env files (.env.production, .env.staging)
# 3. Ensure DB_PASSWORD and SECRET_KEY are properly secured
# 4. Enable SSL in production (DB_SSL_MODE=require)
# 5. Use strong passwords and rotate them regularly
# 6. Monitor connection pool usage and adjust as needed
# 7. Set up proper logging and monitoring
# 8. Configure backup and disaster recovery procedures
# 9. Implement proper access controls and network security
# 10. Regular security audits and updates
