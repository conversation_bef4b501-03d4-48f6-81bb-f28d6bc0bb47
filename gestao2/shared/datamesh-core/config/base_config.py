"""
DataMesh Core - Base Configuration
Shared configuration for all DataHub Amigo One applications
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any

class BaseConfig:
    """Base configuration shared across all domains"""
    
    # Application root directory (shared core)
    CORE_ROOT = Path(__file__).parent.parent.absolute()
    
    # Database Configuration (Shared)
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = os.getenv('DB_PORT', '5432')
    DB_NAME = os.getenv('DB_NAME', 'amigo_datahub')
    DB_USER = os.getenv('DB_USER', 'app_user')
    DB_PASSWORD = os.getenv('DB_PASSWORD', '')
    
    # Connection pool settings for performance
    DB_POOL_SIZE = int(os.getenv('DB_POOL_SIZE', '10'))
    DB_MAX_OVERFLOW = int(os.getenv('DB_MAX_OVERFLOW', '20'))
    DB_POOL_TIMEOUT = int(os.getenv('DB_POOL_TIMEOUT', '30'))
    DB_POOL_RECYCLE = int(os.getenv('DB_POOL_RECYCLE', '3600'))
    
    # SSL Configuration for security
    DB_SSL_MODE = os.getenv('DB_SSL_MODE', 'require')
    DB_SSL_CERT = os.getenv('DB_SSL_CERT', '')
    DB_SSL_KEY = os.getenv('DB_SSL_KEY', '')
    DB_SSL_ROOT_CERT = os.getenv('DB_SSL_ROOT_CERT', '')
    
    # Cache Configuration (Redis)
    CACHE_TYPE = os.getenv('CACHE_TYPE', 'redis')
    CACHE_REDIS_HOST = os.getenv('CACHE_REDIS_HOST', 'localhost')
    CACHE_REDIS_PORT = int(os.getenv('CACHE_REDIS_PORT', '6379'))
    CACHE_REDIS_DB = int(os.getenv('CACHE_REDIS_DB', '0'))
    CACHE_REDIS_PASSWORD = os.getenv('CACHE_REDIS_PASSWORD', '')
    CACHE_DEFAULT_TIMEOUT = int(os.getenv('CACHE_DEFAULT_TIMEOUT', '300'))
    
    # Security settings
    SECRET_KEY = os.getenv('SECRET_KEY', 'change_in_production')
    
    # Session security
    SESSION_COOKIE_SECURE = os.getenv('SESSION_COOKIE_SECURE', 'False').lower() == 'true'
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = int(os.getenv('PERMANENT_SESSION_LIFETIME', '3600'))
    
    # Logging
    LOG_LEVEL = getattr(logging, os.getenv('LOG_LEVEL', 'INFO').upper())
    
    # DataMesh Configuration
    DATA_SOURCE_MODE = os.getenv('DATA_SOURCE_MODE', 'files')
    
    # Shared schemas
    SHARED_SCHEMA = os.getenv('SHARED_SCHEMA', 'shared')
    
    # Color scheme (shared across apps)
    COLORS = {
        'primary': '#0087EB',
        'secondary': '#98CFFF',
        'tertiary': '#E0F1FF',
        'success': '#34C759',
        'warning': '#FF9500',
        'danger': '#FF3B30',
    }
    
    @property
    def database_url(self):
        """Build database URL with SSL parameters"""
        base_url = f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
        
        # Add SSL parameters if configured
        ssl_params = []
        if self.DB_SSL_MODE:
            ssl_params.append(f"sslmode={self.DB_SSL_MODE}")
        if self.DB_SSL_CERT:
            ssl_params.append(f"sslcert={self.DB_SSL_CERT}")
        if self.DB_SSL_KEY:
            ssl_params.append(f"sslkey={self.DB_SSL_KEY}")
        if self.DB_SSL_ROOT_CERT:
            ssl_params.append(f"sslrootcert={self.DB_SSL_ROOT_CERT}")
            
        if ssl_params:
            base_url += "?" + "&".join(ssl_params)
            
        return base_url

class BusinessDomainConfig(BaseConfig):
    """Configuration for Business Domain (Negócios)"""
    
    # Domain specific settings
    DOMAIN_NAME = 'business'
    APP_NAME = 'DataHub Amigo One - Negócios'
    APP_VERSION = '2.0.0'
    APP_PORT = int(os.getenv('BUSINESS_PORT', '5000'))
    
    # Domain specific database schema
    DB_SCHEMA = os.getenv('BUSINESS_DB_SCHEMA', 'business')
    
    # Domain specific cache database
    CACHE_REDIS_DB = int(os.getenv('BUSINESS_CACHE_DB', '1'))
    
    # Business domain data sources
    BUSINESS_DATA_DIR = os.getenv('BUSINESS_DATA_DIR', 'gestao/dimensoes')
    BUSINESS_MAIN_FILE = os.getenv('BUSINESS_MAIN_FILE', 'gestao/base_dados.xlsx')
    
    # Business domain specific features
    ENABLE_RESPONSIBLE_ANALYSIS = os.getenv('ENABLE_RESPONSIBLE_ANALYSIS', 'true').lower() == 'true'
    ENABLE_CLASS_ANALYSIS = os.getenv('ENABLE_CLASS_ANALYSIS', 'true').lower() == 'true'
    ENABLE_COUPON_ANALYSIS = os.getenv('ENABLE_COUPON_ANALYSIS', 'true').lower() == 'true'
    ENABLE_DATA_QUALITY_ANALYSIS = os.getenv('ENABLE_DATA_QUALITY_ANALYSIS', 'true').lower() == 'true'
    
    # Integration with Product domain
    PRODUCT_APP_URL = os.getenv('PRODUCT_APP_URL', 'http://localhost:5001')
    
    @property
    def database_url(self):
        """Build database URL with business schema"""
        base_url = super().database_url
        # Add schema to connection options
        if '?' in base_url:
            base_url += f"&options=-csearch_path%3D{self.DB_SCHEMA}"
        else:
            base_url += f"?options=-csearch_path%3D{self.DB_SCHEMA}"
        return base_url

class ProductDomainConfig(BaseConfig):
    """Configuration for Product Domain (Produto)"""
    
    # Domain specific settings
    DOMAIN_NAME = 'product'
    APP_NAME = 'DataHub Amigo One - Produto'
    APP_VERSION = '2.0.0'
    APP_PORT = int(os.getenv('PRODUCT_PORT', '5001'))
    
    # Domain specific database schema
    DB_SCHEMA = os.getenv('PRODUCT_DB_SCHEMA', 'product')
    
    # Domain specific cache database
    CACHE_REDIS_DB = int(os.getenv('PRODUCT_CACHE_DB', '2'))
    
    # Product domain specific features
    ENABLE_USER_ANALYTICS = os.getenv('ENABLE_USER_ANALYTICS', 'true').lower() == 'true'
    ENABLE_FEATURE_TRACKING = os.getenv('ENABLE_FEATURE_TRACKING', 'true').lower() == 'true'
    ENABLE_PAYMENT_ANALYTICS = os.getenv('ENABLE_PAYMENT_ANALYTICS', 'true').lower() == 'true'
    
    # Integration with Business domain
    BUSINESS_APP_URL = os.getenv('BUSINESS_APP_URL', 'https://5mmmbhl1-5000.brs.devtunnels.ms/')
    
    @property
    def database_url(self):
        """Build database URL with product schema"""
        base_url = super().database_url
        # Add schema to connection options
        if '?' in base_url:
            base_url += f"&options=-csearch_path%3D{self.DB_SCHEMA}"
        else:
            base_url += f"?options=-csearch_path%3D{self.DB_SCHEMA}"
        return base_url

class DevelopmentConfig:
    """Development environment configuration"""
    DEBUG = True
    TESTING = False
    
    # Development specific settings
    DATA_SOURCE_MODE = 'files'
    SESSION_COOKIE_SECURE = False
    LOG_LEVEL = logging.DEBUG
    
    # Development database
    DB_HOST = os.getenv('DEV_DB_HOST', 'localhost')
    DB_NAME = os.getenv('DEV_DB_NAME', 'amigo_datahub_dev')

class TestingConfig:
    """Testing environment configuration"""
    DEBUG = False
    TESTING = True
    
    # Testing specific settings
    DATA_SOURCE_MODE = 'files'
    SESSION_COOKIE_SECURE = False
    LOG_LEVEL = logging.DEBUG
    
    # Test database
    DB_NAME = os.getenv('TEST_DB_NAME', 'amigo_datahub_test')
    CACHE_TYPE = 'simple'

class ProductionConfig:
    """Production environment configuration"""
    DEBUG = False
    TESTING = False
    
    # Production specific settings
    DATA_SOURCE_MODE = os.getenv('DATA_SOURCE_MODE', 'database')
    SESSION_COOKIE_SECURE = True
    LOG_LEVEL = logging.WARNING
    
    # Production database settings
    DB_SSL_MODE = 'require'
    
    # Enhanced security for production
    SECRET_KEY = os.getenv('SECRET_KEY')  # Must be set in production
    
    # Performance optimizations for production
    DB_POOL_SIZE = int(os.getenv('DB_POOL_SIZE', '20'))
    DB_MAX_OVERFLOW = int(os.getenv('DB_MAX_OVERFLOW', '40'))
    CACHE_DEFAULT_TIMEOUT = int(os.getenv('CACHE_DEFAULT_TIMEOUT', '600'))

# Configuration factory
def get_config(domain: str, environment: str = 'development') -> Dict[str, Any]:
    """
    Get configuration for specific domain and environment
    
    Args:
        domain (str): 'business' or 'product'
        environment (str): 'development', 'testing', or 'production'
        
    Returns:
        Dict[str, Any]: Configuration dictionary
    """
    # Base domain config
    if domain == 'business':
        base_config = BusinessDomainConfig()
    elif domain == 'product':
        base_config = ProductDomainConfig()
    else:
        raise ValueError(f"Unknown domain: {domain}")
    
    # Environment specific config
    env_configs = {
        'development': DevelopmentConfig(),
        'testing': TestingConfig(),
        'production': ProductionConfig()
    }
    
    if environment not in env_configs:
        raise ValueError(f"Unknown environment: {environment}")
    
    env_config = env_configs[environment]
    
    # Merge configurations
    config = {}
    
    # Add base config
    for attr in dir(base_config):
        if not attr.startswith('_'):
            config[attr] = getattr(base_config, attr)
    
    # Override with environment config
    for attr in dir(env_config):
        if not attr.startswith('_'):
            config[attr] = getattr(env_config, attr)
    
    return config

# Domain mapping for cross-domain communication
DOMAIN_MAPPING = {
    'business': {
        'port': 5000,
        'url': 'https://5mmmbhl1-5000.brs.devtunnels.ms/',
        'schema': 'business',
        'entities': ['leads', 'opportunities', 'implementations', 'universities', 'responsibles']
    },
    'product': {
        'port': 5001,
        'url': 'http://localhost:5001',
        'schema': 'product', 
        'entities': ['users', 'features', 'connections', 'payments', 'agenda']
    }
}
