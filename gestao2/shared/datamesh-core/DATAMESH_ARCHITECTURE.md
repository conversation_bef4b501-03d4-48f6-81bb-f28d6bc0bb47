# DataMesh Architecture - DataHub Amigo One

## Visão Geral da Arquitetura

Esta documentação detalha a implementação da arquitetura DataMesh para o DataHub Amigo One, com separação clara entre os domínios de Negócios e Produto, mantendo máxima performance e segurança.

## Princípios DataMesh Implementados

### 1. Domain Ownership (Propriedade de Domínio)

#### 🔵 Domínio Negócios (Business Domain)
**Responsável**: Equipe de Negócios
**Porta**: 5000
**Schema DB**: `business`
**URL**: https://5mmmbhl1-5000.brs.devtunnels.ms/

**Entidades Gerenciadas**:
- **Leads**: Potenciais clientes
- **Oportunidades**: Negociações em andamento
- **Implantações**: Projetos de implementação
- **Universidades**: Instituições parceiras
- **Responsáveis**: Equipe responsável por cada etapa
- **Turmas**: Classes e grupos de estudantes
- **Cupons**: Descontos e promoções

**Regras de Negócio**:
- Funil comercial completo
- Gestão de responsáveis por etapa
- Análise de conversão
- Qualidade de dados
- MRR (Monthly Recurring Revenue)

#### 🔴 Domínio Produto (Product Domain)
**Responsável**: Equipe de Produto
**Porta**: 5001
**Schema DB**: `product`
**URL**: http://localhost:5001

**Entidades Gerenciadas**:
- **Usuários**: Usuários da plataforma
- **Features**: Funcionalidades utilizadas
- **Conexões**: Rede social médica
- **Pagamentos**: Transações financeiras
- **Agenda**: Eventos e consultas
- **Prontuários**: Registros médicos
- **Contabilidade**: Gestão fiscal

**Regras de Negócio**:
- Experiência do usuário
- Analytics de uso
- Gestão de pagamentos
- Rede social médica
- Compliance médico

### 2. Data as a Product (Dados como Produto)

#### APIs de Integração

**Business Domain APIs**:
```
GET  /api/v1/business/implementations/{impl_id}
GET  /api/v1/business/user/{user_id}/implementations
POST /api/v1/business/implementation/{impl_id}/activate
GET  /api/v1/business/analytics/revenue
GET  /api/v1/business/analytics/conversion
```

**Product Domain APIs**:
```
GET  /api/v1/product/user/{user_id}/profile
GET  /api/v1/product/user/{user_id}/usage
POST /api/v1/product/user/{user_id}/feature-usage
GET  /api/v1/product/analytics/engagement
GET  /api/v1/product/analytics/payments
```

**Cross-Domain APIs**:
```
GET  /api/v1/cross/user/{user_id}/complete-profile
POST /api/v1/cross/sync/implementation-to-user
GET  /api/v1/cross/analytics/combined
```

### 3. Self-Serve Data Infrastructure

#### Shared Core Components

```
shared/datamesh-core/
├── database/                    # Camada de dados unificada
│   ├── database_service.py      # Conexões e pooling
│   ├── data_adapter.py          # Adaptador multi-fonte
│   └── connection_manager.py    # Conexões entre domínios
├── config/                      # Configurações compartilhadas
│   ├── base_config.py           # Config base para todos os domínios
│   └── .env.example             # Template de ambiente
├── security/                    # Segurança compartilhada
│   └── SECURITY_GUIDE.md        # Guias de segurança
├── monitoring/                  # Monitoramento compartilhado
└── scripts/                     # Scripts de infraestrutura
    ├── deploy.sh                # Deploy automatizado
    └── migrate_data.py           # Migração de dados
```

### 4. Federated Governance (Governança Federada)

#### Padrões Comuns

**Segurança**:
- SSL/TLS obrigatório em produção
- Autenticação baseada em sessões
- Validação de inputs
- Logs de auditoria

**Performance**:
- Connection pooling configurável
- Cache Redis multi-camadas
- Queries otimizadas
- Monitoramento de métricas

**Qualidade de Dados**:
- Validação automática
- Métricas de qualidade
- Alertas de inconsistência
- Documentação de schemas

## Conectividade Entre Domínios

### Chave de Conexão Principal

A conexão entre os domínios é feita através da tabela `shared.domain_mapping`:

```sql
CREATE TABLE shared.domain_mapping (
    id SERIAL PRIMARY KEY,
    business_entity_id VARCHAR(255) NOT NULL,    -- ID da implantação
    business_entity_type VARCHAR(50) NOT NULL,   -- 'implementation'
    product_user_id VARCHAR(255),                -- Email do usuário
    product_entity_id VARCHAR(255),              -- ID específico no produto
    product_entity_type VARCHAR(50),             -- 'user_account'
    mapping_status VARCHAR(20) DEFAULT 'active', -- Status do mapeamento
    metadata JSONB,                              -- Dados adicionais
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Fluxo de Sincronização

1. **Implantação Finalizada** (Business) → **Usuário Ativo** (Product)
2. **Usuário Cadastrado** (Product) → **Lead Qualificado** (Business)
3. **Pagamento Realizado** (Product) → **Receita Confirmada** (Business)

### Exemplo de Integração

```python
# No domínio Business - quando implantação é finalizada
from shared.datamesh_core.database import DomainConnectionManager

connection_manager = DomainConnectionManager(database_service)
connection_manager.sync_implementation_to_user(
    implementation_id="IMPL_001",
    user_email="<EMAIL>",
    user_name="João Silva",
    metadata={
        "university": "Universidade A",
        "course": "Direito",
        "implementation_date": "2024-01-15"
    }
)

# No domínio Product - buscar dados do business
business_entities = connection_manager.get_business_entities_from_user(
    product_user_id="<EMAIL>",
    business_entity_type="implementation"
)
```

## Schemas de Banco de Dados

### Business Schema
```sql
-- Leads, Oportunidades, Implantações
-- Universidades, Responsáveis, Turmas
-- Cupons, Análises de Conversão
```

### Product Schema  
```sql
-- Usuários, Features, Conexões
-- Pagamentos, Agenda, Prontuários
-- Analytics de Uso, Contabilidade
```

### Shared Schema
```sql
-- domain_mapping (conexões entre domínios)
-- audit_logs (logs de auditoria)
-- system_metrics (métricas do sistema)
-- configurations (configurações globais)
```

## Deployment e Configuração

### Estrutura de Ambientes

**Development**:
```bash
# Business Domain
BUSINESS_PORT=5000
BUSINESS_DB_SCHEMA=business_dev
DATA_SOURCE_MODE=files

# Product Domain  
PRODUCT_PORT=5001
PRODUCT_DB_SCHEMA=product_dev
DATA_SOURCE_MODE=mock

# Shared
SHARED_DB_SCHEMA=shared_dev
```

**Production**:
```bash
# Business Domain
BUSINESS_PORT=5000
BUSINESS_DB_SCHEMA=business
DATA_SOURCE_MODE=database

# Product Domain
PRODUCT_PORT=5001  
PRODUCT_DB_SCHEMA=product
DATA_SOURCE_MODE=database

# Shared
SHARED_DB_SCHEMA=shared
DB_SSL_MODE=require
```

### Scripts de Deploy

```bash
# Deploy completo
./shared/datamesh-core/scripts/deploy.sh

# Deploy por domínio
./shared/datamesh-core/scripts/deploy.sh deploy-business
./shared/datamesh-core/scripts/deploy.sh deploy-product

# Migração de dados
python shared/datamesh-core/scripts/migrate_data.py
```

## Monitoramento e Observabilidade

### Métricas por Domínio

**Business Domain**:
- Taxa de conversão Lead → Oportunidade
- Tempo médio de implantação
- MRR (Monthly Recurring Revenue)
- Qualidade de dados por responsável

**Product Domain**:
- Usuários ativos diários/mensais
- Engagement por feature
- Volume de transações
- Tempo de resposta da aplicação

**Cross-Domain**:
- Sincronização entre domínios
- Integridade de dados
- Performance de APIs
- Disponibilidade dos serviços

### Alertas Configurados

- Falha na sincronização entre domínios
- Performance degradada (> 2s response time)
- Erro de conexão com banco de dados
- Cache hit rate < 70%
- Inconsistências nos dados

## Benefícios da Arquitetura

### Para a Equipe de Negócios
- **Autonomia**: Evolução independente do domínio
- **Performance**: Otimizações específicas para analytics
- **Qualidade**: Métricas de qualidade de dados
- **Escalabilidade**: Crescimento sem impacto no produto

### Para a Equipe de Produto
- **Flexibilidade**: Mudanças sem afetar negócios
- **Inovação**: Experimentação rápida de features
- **User Experience**: Foco na experiência do usuário
- **Analytics**: Dados detalhados de uso

### Para a Organização
- **Governança**: Padrões comuns com flexibilidade local
- **Segurança**: Controles centralizados
- **Eficiência**: Reutilização de componentes
- **Visibilidade**: Métricas end-to-end

## Próximos Passos

1. **Fase 1**: Configurar core compartilhado
2. **Fase 2**: Migrar dados para PostgreSQL
3. **Fase 3**: Implementar APIs de integração
4. **Fase 4**: Configurar monitoramento avançado
5. **Fase 5**: Otimizações de performance
6. **Fase 6**: Expansão para novos domínios
