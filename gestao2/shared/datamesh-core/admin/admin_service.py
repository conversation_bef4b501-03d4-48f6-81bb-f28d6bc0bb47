"""
Admin Service for DataHub Amigo One
Provides administration functionality for both Business and Product domains
Now using SQLite for data persistence
"""

import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import bcrypt
import uuid
import sys

# Import SQLite manager
try:
    from database.sqlite_manager import SQLiteManager
    SQLITE_AVAILABLE = True
except ImportError:
    SQLITE_AVAILABLE = False

logger = logging.getLogger(__name__)

class AdminService:
    """Service for admin operations"""

    def __init__(self, domain: str):
        self.domain = domain

        # Use SQLite if available, fallback to JSON files
        if SQLITE_AVAILABLE:
            self.db = SQLiteManager(domain)
            self.use_sqlite = True
        else:
            self.use_sqlite = False
            self.users_file = self._get_users_file()
            self.activity_file = self._get_activity_file()

    def _get_users_file(self) -> str:
        """Get users file path for domain"""
        if self.domain == 'business':
            return 'gestao/data/business_users.json'
        else:
            return 'produto/ux-gabi/data/product_users.json'

    def _get_activity_file(self) -> str:
        """Get activity file path for domain"""
        if self.domain == 'business':
            return 'gestao/data/business_activity.json'
        else:
            return 'produto/ux-gabi/data/product_activity.json'

    def get_user_stats(self) -> Dict[str, Any]:
        """Get user statistics for admin dashboard"""
        if self.use_sqlite:
            return self.db.get_user_stats()
        else:
            # Fallback to JSON files
            users = self._load_users()

            total_users = len(users)
            active_users = len([u for u in users.values() if u.get('is_active', True)])
            admin_users = len([u for u in users.values() if u.get('role') == 'admin'])

            # Get recent activity
            activity = self._load_activity()
            recent_logins = len([a for a in activity if
                               a.get('action') == 'login' and
                               self._is_recent(a.get('timestamp'))])

            return {
                'total_users': total_users,
                'active_users': active_users,
                'admin_users': admin_users,
                'recent_logins': recent_logins,
                'pending_invitations': 0
            }

    def get_all_users(self) -> List[Dict[str, Any]]:
        """Get all users for admin management"""
        if self.use_sqlite:
            return self.db.get_all_users()
        else:
            # Fallback to JSON files
            users = self._load_users()
            user_list = []

            for user_id, user_data in users.items():
                user_info = {
                    'user_id': user_id,
                    'username': user_data.get('username', user_id),
                    'email': user_data.get('email', ''),
                    'role': user_data.get('role', 'user'),
                    'is_active': user_data.get('is_active', True),
                    'created_at': user_data.get('created_at', ''),
                    'last_login': user_data.get('last_login'),
                    'permissions': user_data.get('permissions', [])
                }
                user_list.append(user_info)

            return sorted(user_list, key=lambda x: x['created_at'], reverse=True)

    def create_user(self, username: str, email: str, password: str,
                   role: str = 'user', permissions: List[str] = None) -> Dict[str, Any]:
        """Create a new user"""
        if self.use_sqlite:
            try:
                result = self.db.create_user(username, email, password, role, permissions)
                return {
                    'username': result['username'],
                    'email': result['email'],
                    'role': result['role'],
                    'permissions': result['permissions'],
                    'temporary_password': password
                }
            except ValueError as e:
                raise e
        else:
            # Fallback to JSON files
            users = self._load_users()

            if username in users:
                raise ValueError(f"User {username} already exists")

            # Hash password
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            # Default permissions based on role
            if permissions is None:
                permissions = self._get_default_permissions(role)

            user_data = {
                'user_id': username,
                'username': username,
                'email': email,
                'role': role,
                'permissions': permissions,
                'domain': self.domain,
                'created_at': datetime.now().isoformat(),
                'last_login': None,
                'is_active': True,
                'metadata': {
                    'password_hash': password_hash,
                    'created_by': 'admin'
                }
            }

            users[username] = user_data
            self._save_users(users)

            # Log activity
            self._log_activity('user_created', f'User {username} created', {'user_id': username})

            return user_data

    def update_user_permissions(self, username: str, permissions: List[str]) -> bool:
        """Update user permissions"""
        if self.use_sqlite:
            return self.db.update_user_permissions(username, permissions)
        else:
            # Fallback to JSON files
            users = self._load_users()

            if username not in users:
                return False

            old_permissions = users[username].get('permissions', [])
            users[username]['permissions'] = permissions
            self._save_users(users)

            # Log activity
            self._log_activity('permissions_updated',
                              f'Permissions updated for {username}',
                              {'user_id': username, 'old_permissions': old_permissions, 'new_permissions': permissions})

            return True

    def deactivate_user(self, username: str) -> bool:
        """Deactivate a user"""
        users = self._load_users()

        if username not in users:
            return False

        users[username]['is_active'] = False
        self._save_users(users)

        # Log activity
        self._log_activity('user_deactivated', f'User {username} deactivated', {'user_id': username})

        return True

    def get_activity_logs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent activity logs"""
        activity = self._load_activity()
        return sorted(activity, key=lambda x: x.get('timestamp', ''), reverse=True)[:limit]

    def get_page_permissions(self) -> Dict[str, str]:
        """Get available page permissions for domain"""
        if self.use_sqlite:
            return self.db.get_permissions()
        else:
            # Fallback to hardcoded permissions
            if self.domain == 'business':
                return {
                    'Dashboard': 'dashboard.view',
                    'Leads': 'leads.view',
                    'Oportunidades': 'opportunities.view',
                    'Implementações': 'implementations.view',
                    'Universidades': 'universities.view',
                    'Responsáveis': 'responsibles.view',
                    'Classes': 'classes.view',
                    'Cupons': 'coupons.view',
                    'Conversão': 'conversion.view',
                    'Assinaturas': 'subscriptions.view',
                    'Qualidade de Dados': 'data_quality.view',
                    'Regras de Negócio': 'business_rules.view',
                    'Gerenciar Usuários': 'users.manage'
                }
            else:
                return {
                    'Dashboard': 'dashboard.view',
                    'Usuários': 'users.view',
                    'Features': 'features.view',
                    'Conexões': 'connections.view',
                    'Pagamentos': 'payments.view',
                    'Agenda': 'agenda.view',
                    'Prontuários': 'medical_records.view',
                    'Contabilidade': 'accounting.view',
                    'Analytics': 'analytics.view',
                    'Gerenciar Usuários': 'users.manage'
                }

    def _load_users(self) -> Dict[str, Any]:
        """Load users from file"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading users: {e}")
        return {}

    def _save_users(self, users: Dict[str, Any]) -> None:
        """Save users to file"""
        try:
            os.makedirs(os.path.dirname(self.users_file), exist_ok=True)
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(users, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving users: {e}")

    def _load_activity(self) -> List[Dict[str, Any]]:
        """Load activity logs from file"""
        try:
            if os.path.exists(self.activity_file):
                with open(self.activity_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading activity: {e}")
        return []

    def _save_activity(self, activity: List[Dict[str, Any]]) -> None:
        """Save activity logs to file"""
        try:
            os.makedirs(os.path.dirname(self.activity_file), exist_ok=True)
            with open(self.activity_file, 'w', encoding='utf-8') as f:
                json.dump(activity, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving activity: {e}")

    def _log_activity(self, action: str, description: str, metadata: Dict[str, Any] = None) -> None:
        """Log an activity"""
        activity = self._load_activity()

        log_entry = {
            'id': str(uuid.uuid4()),
            'timestamp': datetime.now().isoformat(),
            'domain': self.domain,
            'action': action,
            'description': description,
            'metadata': metadata or {}
        }

        activity.append(log_entry)

        # Keep only last 1000 entries
        if len(activity) > 1000:
            activity = activity[-1000:]

        self._save_activity(activity)

    def _get_default_permissions(self, role: str) -> List[str]:
        """Get default permissions for role"""
        if role == 'admin':
            page_perms = self.get_page_permissions()
            return list(page_perms.values()) + ['users.manage']
        else:
            return ['dashboard.view']

    def _is_recent(self, timestamp: str, hours: int = 24) -> bool:
        """Check if timestamp is recent"""
        try:
            ts = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return datetime.now() - ts < timedelta(hours=hours)
        except:
            return False
