"""
Monitoring Service for DataHub Amigo One
Provides real-time monitoring and analytics for admin dashboards
"""

import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict, Counter
import uuid

logger = logging.getLogger(__name__)

class MonitoringService:
    """Service for monitoring user activity and system metrics"""
    
    def __init__(self, domain: str):
        self.domain = domain
        self.activity_file = self._get_activity_file()
        self.metrics_file = self._get_metrics_file()
        
    def _get_activity_file(self) -> str:
        """Get activity file path for domain"""
        if self.domain == 'business':
            return 'gestao/data/business_activity.json'
        else:
            return 'produto/ux-gabi/data/product_activity.json'
    
    def _get_metrics_file(self) -> str:
        """Get metrics file path for domain"""
        if self.domain == 'business':
            return 'gestao/data/business_metrics.json'
        else:
            return 'produto/ux-gabi/data/product_metrics.json'
    
    def log_user_activity(self, user_id: str, action: str, page: str, 
                         details: Dict[str, Any] = None) -> None:
        """Log user activity"""
        activity = self._load_activity()
        
        log_entry = {
            'id': str(uuid.uuid4()),
            'timestamp': datetime.now().isoformat(),
            'domain': self.domain,
            'user_id': user_id,
            'action': action,
            'page': page,
            'details': details or {},
            'session_id': self._get_session_id(user_id)
        }
        
        activity.append(log_entry)
        
        # Keep only last 10000 entries
        if len(activity) > 10000:
            activity = activity[-10000:]
        
        self._save_activity(activity)
        
        # Update real-time metrics
        self._update_metrics(log_entry)
    
    def get_dashboard_metrics(self) -> Dict[str, Any]:
        """Get metrics for monitoring dashboard"""
        activity = self._load_activity()
        metrics = self._load_metrics()
        
        # Calculate time ranges
        now = datetime.now()
        last_hour = now - timedelta(hours=1)
        last_24h = now - timedelta(hours=24)
        last_7d = now - timedelta(days=7)
        
        # Filter recent activity
        recent_activity = [
            a for a in activity 
            if self._parse_timestamp(a.get('timestamp')) > last_24h
        ]
        
        # Active users (logged in last 24h)
        active_users = len(set(a.get('user_id') for a in recent_activity if a.get('action') == 'page_view'))
        
        # Page views
        page_views_24h = len([a for a in recent_activity if a.get('action') == 'page_view'])
        page_views_1h = len([
            a for a in recent_activity 
            if a.get('action') == 'page_view' and 
            self._parse_timestamp(a.get('timestamp')) > last_hour
        ])
        
        # Login activity
        logins_24h = len([a for a in recent_activity if a.get('action') == 'login'])
        
        # Data exports
        exports_24h = len([a for a in recent_activity if a.get('action') == 'data_export'])
        
        # Most accessed pages
        page_counter = Counter(a.get('page') for a in recent_activity if a.get('action') == 'page_view')
        top_pages = dict(page_counter.most_common(10))
        
        # User activity by hour
        hourly_activity = defaultdict(int)
        for a in recent_activity:
            if a.get('action') == 'page_view':
                hour = self._parse_timestamp(a.get('timestamp')).hour
                hourly_activity[hour] += 1
        
        # System health
        error_rate = self._calculate_error_rate(recent_activity)
        avg_response_time = metrics.get('avg_response_time', 120)
        
        return {
            'system_status': 'healthy' if error_rate < 0.05 else 'warning',
            'uptime': '99.9%',
            'active_users': active_users,
            'total_sessions': len(set(a.get('session_id') for a in recent_activity)),
            'page_views_24h': page_views_24h,
            'page_views_1h': page_views_1h,
            'logins_24h': logins_24h,
            'exports_24h': exports_24h,
            'response_time': f'{avg_response_time}ms',
            'error_rate': f'{error_rate:.1%}',
            'cpu_usage': '45%',
            'memory_usage': '62%',
            'disk_usage': '78%',
            'network_io': '1.2 GB/s',
            'top_pages': top_pages,
            'hourly_activity': dict(hourly_activity)
        }
    
    def get_user_analytics(self) -> Dict[str, Any]:
        """Get user analytics for admin dashboard"""
        activity = self._load_activity()
        
        # Calculate time ranges
        now = datetime.now()
        last_7d = now - timedelta(days=7)
        last_30d = now - timedelta(days=30)
        
        # Filter activity
        recent_activity = [
            a for a in activity 
            if self._parse_timestamp(a.get('timestamp')) > last_30d
        ]
        
        # User engagement
        user_sessions = defaultdict(set)
        user_page_views = defaultdict(int)
        user_last_seen = {}
        
        for a in recent_activity:
            user_id = a.get('user_id')
            if user_id:
                user_sessions[user_id].add(a.get('session_id'))
                if a.get('action') == 'page_view':
                    user_page_views[user_id] += 1
                user_last_seen[user_id] = max(
                    user_last_seen.get(user_id, ''), 
                    a.get('timestamp', '')
                )
        
        # Calculate metrics
        total_users = len(user_sessions)
        avg_sessions_per_user = sum(len(sessions) for sessions in user_sessions.values()) / max(total_users, 1)
        avg_page_views_per_user = sum(user_page_views.values()) / max(total_users, 1)
        
        # Active users by day
        daily_active_users = defaultdict(set)
        for a in recent_activity:
            if a.get('action') == 'page_view':
                day = self._parse_timestamp(a.get('timestamp')).date().isoformat()
                daily_active_users[day].add(a.get('user_id'))
        
        daily_active_counts = {day: len(users) for day, users in daily_active_users.items()}
        
        return {
            'total_users': total_users,
            'avg_sessions_per_user': round(avg_sessions_per_user, 1),
            'avg_page_views_per_user': round(avg_page_views_per_user, 1),
            'daily_active_users': daily_active_counts,
            'user_retention': self._calculate_retention(recent_activity)
        }
    
    def get_page_analytics(self) -> Dict[str, Any]:
        """Get page-specific analytics"""
        activity = self._load_activity()
        
        # Calculate time ranges
        now = datetime.now()
        last_7d = now - timedelta(days=7)
        
        # Filter recent page views
        page_views = [
            a for a in activity 
            if a.get('action') == 'page_view' and 
            self._parse_timestamp(a.get('timestamp')) > last_7d
        ]
        
        # Page statistics
        page_stats = defaultdict(lambda: {
            'views': 0,
            'unique_users': set(),
            'avg_time_on_page': 0,
            'bounce_rate': 0
        })
        
        for view in page_views:
            page = view.get('page', 'unknown')
            page_stats[page]['views'] += 1
            page_stats[page]['unique_users'].add(view.get('user_id'))
        
        # Convert sets to counts
        for page, stats in page_stats.items():
            stats['unique_users'] = len(stats['unique_users'])
        
        return dict(page_stats)
    
    def _load_activity(self) -> List[Dict[str, Any]]:
        """Load activity logs from file"""
        try:
            if os.path.exists(self.activity_file):
                with open(self.activity_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading activity: {e}")
        return []
    
    def _save_activity(self, activity: List[Dict[str, Any]]) -> None:
        """Save activity logs to file"""
        try:
            os.makedirs(os.path.dirname(self.activity_file), exist_ok=True)
            with open(self.activity_file, 'w', encoding='utf-8') as f:
                json.dump(activity, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving activity: {e}")
    
    def _load_metrics(self) -> Dict[str, Any]:
        """Load metrics from file"""
        try:
            if os.path.exists(self.metrics_file):
                with open(self.metrics_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading metrics: {e}")
        return {}
    
    def _save_metrics(self, metrics: Dict[str, Any]) -> None:
        """Save metrics to file"""
        try:
            os.makedirs(os.path.dirname(self.metrics_file), exist_ok=True)
            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving metrics: {e}")
    
    def _update_metrics(self, log_entry: Dict[str, Any]) -> None:
        """Update real-time metrics"""
        metrics = self._load_metrics()
        
        # Update counters
        metrics['total_events'] = metrics.get('total_events', 0) + 1
        metrics['last_updated'] = datetime.now().isoformat()
        
        # Update action counters
        action = log_entry.get('action')
        action_key = f'total_{action}'
        metrics[action_key] = metrics.get(action_key, 0) + 1
        
        self._save_metrics(metrics)
    
    def _parse_timestamp(self, timestamp: str) -> datetime:
        """Parse timestamp string to datetime"""
        try:
            return datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        except:
            return datetime.min
    
    def _get_session_id(self, user_id: str) -> str:
        """Get or create session ID for user"""
        # Simple session tracking - in production, use proper session management
        return f"{user_id}_{datetime.now().strftime('%Y%m%d_%H')}"
    
    def _calculate_error_rate(self, activity: List[Dict[str, Any]]) -> float:
        """Calculate error rate from activity"""
        total_requests = len([a for a in activity if a.get('action') in ['page_view', 'api_call']])
        errors = len([a for a in activity if a.get('action') == 'error'])
        return errors / max(total_requests, 1)
    
    def _calculate_retention(self, activity: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate user retention metrics"""
        # Simple retention calculation
        now = datetime.now()
        week_ago = now - timedelta(days=7)
        
        users_this_week = set(
            a.get('user_id') for a in activity 
            if self._parse_timestamp(a.get('timestamp')) > week_ago
        )
        
        users_last_week = set(
            a.get('user_id') for a in activity 
            if week_ago - timedelta(days=7) < self._parse_timestamp(a.get('timestamp')) <= week_ago
        )
        
        retained_users = users_this_week.intersection(users_last_week)
        retention_rate = len(retained_users) / max(len(users_last_week), 1)
        
        return {
            'weekly_retention': round(retention_rate * 100, 1),
            'total_users_this_week': len(users_this_week),
            'total_users_last_week': len(users_last_week),
            'retained_users': len(retained_users)
        }
