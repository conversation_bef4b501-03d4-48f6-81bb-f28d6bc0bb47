"""
DataMesh Core - Shared Infrastructure for DataHub Amigo One
"""

__version__ = "2.0.0"
__author__ = "DataHub Amigo One Team"

# Core modules
from .config.base_config import (
    BaseConfig,
    BusinessDomainConfig, 
    ProductDomainConfig,
    get_config,
    DOMAIN_MAPPING
)

from .database.database_service import DatabaseService
from .database.data_adapter import DataAdapter
from .database.connection_manager import DomainConnectionManager

__all__ = [
    'BaseConfig',
    'BusinessDomainConfig',
    'ProductDomainConfig', 
    'get_config',
    'DOMAIN_MAPPING',
    'DatabaseService',
    'DataAdapter',
    'DomainConnectionManager'
]
