# DataMesh Core - DataHub Amigo One

## Visão Geral

Este é o núcleo compartilhado do DataMesh para os aplicativos DataHub Amigo One, seguindo os princípios de arquitetura DataMesh com separação clara de domínios e responsabilidades.

## Arquitetura DataMesh

```
┌─────────────────────────────────────────────────────────────────┐
│                    SHARED DATAMESH CORE                        │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │  Database   │  │  Security   │  │      Configuration      │  │
│  │   Layer     │  │   Layer     │  │        Layer            │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │ Monitoring  │  │    Utils    │  │       Scripts           │  │
│  │   Layer     │  │   Layer     │  │        Layer            │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
┌───────────────▼───┐   ┌───────▼───────┐   ┌───▼─────────────┐
│   NEGÓCIOS APP    │   │ PRODUTO APP   │   │  FUTURE APPS    │
│   (Port 5000)     │   │ (Port 5001)   │   │                 │
│                   │   │               │   │                 │
│ • Leads           │   │ • Usuários    │   │ • Analytics     │
│ • Oportunidades   │   │ • Features    │   │ • Reporting     │
│ • Implantações    │   │ • Conexões    │   │ • BI            │
│ • Universidades   │   │ • Pagame<PERSON><PERSON>  │   │                 │
│ • Responsáveis    │   │ • Agenda      │   │                 │
└───────────────────┘   └───────────────┘   └─────────────────┘
```

## Domínios de Dados

### 🔵 Domínio Negócios (Business Domain)
**Responsabilidade**: Gestão do funil comercial e operacional
- **Entidades**: Leads, Oportunidades, Implantações, Universidades, Responsáveis
- **Chave de Conexão**: `business_entity_id` (conecta com produto via usuário)
- **Porta**: 5000
- **Schema DB**: `business`

### 🔴 Domínio Produto (Product Domain)
**Responsabilidade**: Gestão do produto e experiência do usuário
- **Entidades**: Usuários, Features, Conexões, Pagamentos, Agenda
- **Chave de Conexão**: `user_id` (conecta com negócios via implantação)
- **Porta**: 5001
- **Schema DB**: `product`

### 🟡 Domínio Compartilhado (Shared Domain)
**Responsabilidade**: Infraestrutura comum e conectividade
- **Entidades**: Configurações, Logs, Métricas, Integrações
- **Schema DB**: `shared`

## Estrutura de Diretórios

```
shared/datamesh-core/
├── README.md                    # Este arquivo
├── DATAMESH_ARCHITECTURE.md     # Arquitetura detalhada
├── database/                    # Camada de dados compartilhada
│   ├── __init__.py
│   ├── database_service.py      # Serviço de banco compartilhado
│   ├── data_adapter.py          # Adaptador de dados
│   ├── connection_manager.py    # Gerenciador de conexões
│   └── schema/                  # Schemas de banco
│       ├── business_schema.sql
│       ├── product_schema.sql
│       └── shared_schema.sql
├── security/                    # Camada de segurança
│   ├── __init__.py
│   ├── auth_service.py          # Autenticação compartilhada
│   ├── encryption.py            # Criptografia
│   └── SECURITY_GUIDE.md        # Guia de segurança
├── config/                      # Configurações compartilhadas
│   ├── __init__.py
│   ├── base_config.py           # Configuração base
│   ├── .env.example             # Template de ambiente
│   └── domain_config.py         # Configurações por domínio
├── utils/                       # Utilitários compartilhados
│   ├── __init__.py
│   ├── formatters.py            # Formatadores
│   ├── validators.py            # Validadores
│   └── helpers.py               # Funções auxiliares
├── monitoring/                  # Monitoramento compartilhado
│   ├── __init__.py
│   ├── metrics.py               # Métricas
│   ├── logging.py               # Logs estruturados
│   └── health_check.py          # Health checks
└── scripts/                     # Scripts de infraestrutura
    ├── deploy.sh                # Deploy automatizado
    ├── migrate_data.py           # Migração de dados
    ├── setup_domains.py         # Setup de domínios
    └── backup_restore.py        # Backup e restore
```

## Princípios DataMesh Implementados

### 1. **Domain Ownership** (Propriedade de Domínio)
- Cada app é responsável por seu domínio de dados
- Negócios: Funil comercial e operacional
- Produto: Experiência do usuário e features

### 2. **Data as a Product** (Dados como Produto)
- APIs bem definidas para cada domínio
- Contratos de dados versionados
- Documentação clara de cada endpoint

### 3. **Self-Serve Data Infrastructure** (Infraestrutura Self-Service)
- Camada compartilhada fornece ferramentas comuns
- Cada domínio pode evoluir independentemente
- Deployment e monitoramento automatizados

### 4. **Federated Governance** (Governança Federada)
- Padrões comuns definidos no core
- Implementação específica por domínio
- Políticas de segurança centralizadas

## Conectividade Entre Domínios

### Chave de Conexão Principal
```sql
-- Tabela de mapeamento entre domínios
CREATE TABLE shared.domain_mapping (
    id SERIAL PRIMARY KEY,
    business_entity_id VARCHAR(255),  -- ID da implantação (negócios)
    product_user_id VARCHAR(255),     -- ID do usuário (produto)
    entity_type VARCHAR(50),          -- 'lead', 'opportunity', 'implementation'
    mapping_status VARCHAR(20),       -- 'active', 'inactive', 'pending'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### APIs de Integração
```python
# Negócios -> Produto
GET /api/v1/business/user/{user_id}/implementations
POST /api/v1/business/implementation/{impl_id}/activate

# Produto -> Negócios  
GET /api/v1/product/implementation/{impl_id}/user
POST /api/v1/product/user/{user_id}/usage-metrics
```

## Configuração por Ambiente

### Development
```bash
# Negócios
BUSINESS_DB_SCHEMA=business_dev
BUSINESS_PORT=5000

# Produto  
PRODUCT_DB_SCHEMA=product_dev
PRODUCT_PORT=5001

# Compartilhado
SHARED_DB_SCHEMA=shared_dev
DATA_SOURCE_MODE=files
```

### Production
```bash
# Negócios
BUSINESS_DB_SCHEMA=business
BUSINESS_PORT=5000

# Produto
PRODUCT_DB_SCHEMA=product  
PRODUCT_PORT=5001

# Compartilhado
SHARED_DB_SCHEMA=shared
DATA_SOURCE_MODE=database
```

## Como Usar

### 1. Configurar Ambiente
```bash
# Copiar configuração base
cp shared/datamesh-core/config/.env.example .env

# Configurar variáveis específicas do domínio
```

### 2. Importar Serviços Compartilhados
```python
# No app de negócios
from shared.datamesh_core.database import DatabaseService
from shared.datamesh_core.security import AuthService

# No app de produto
from shared.datamesh_core.database import DatabaseService
from shared.datamesh_core.monitoring import MetricsService
```

### 3. Deploy
```bash
# Deploy do core compartilhado
./shared/datamesh-core/scripts/deploy.sh setup-core

# Deploy do domínio negócios
./shared/datamesh-core/scripts/deploy.sh deploy-business

# Deploy do domínio produto
./shared/datamesh-core/scripts/deploy.sh deploy-product
```

## Benefícios da Arquitetura

1. **Separação Clara**: Cada domínio tem responsabilidades bem definidas
2. **Reutilização**: Código comum compartilhado eficientemente
3. **Escalabilidade**: Domínios podem escalar independentemente
4. **Manutenibilidade**: Mudanças isoladas por domínio
5. **Governança**: Padrões comuns com flexibilidade local
6. **Performance**: Otimizações específicas por domínio

## Próximos Passos

1. **Configurar Core**: Instalar dependências compartilhadas
2. **Migrar Dados**: Executar scripts de migração
3. **Configurar Domínios**: Adaptar apps para usar o core
4. **Testes de Integração**: Validar conectividade entre domínios
5. **Monitoramento**: Implementar métricas e alertas
6. **Documentação**: Completar documentação de APIs
