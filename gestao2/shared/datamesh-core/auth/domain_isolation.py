"""
Domain Isolation Validator for DataHub Amigo One
Ensures complete data and user isolation between Business and Product domains
"""

import json
import os
import logging
from typing import Dict, List, Set, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

class DomainIsolationValidator:
    """Validates and enforces domain isolation"""
    
    def __init__(self):
        self.business_data_path = Path("gestao/data")
        self.product_data_path = Path("produto/ux-gabi/data")
        
    def validate_user_isolation(self) -> Dict[str, any]:
        """Validate that users are properly isolated between domains"""
        results = {
            "isolated": True,
            "issues": [],
            "business_users": set(),
            "product_users": set(),
            "shared_users": set(),
            "stats": {}
        }
        
        try:
            # Load business users
            business_file = self.business_data_path / "business_users.json"
            if business_file.exists():
                with open(business_file, 'r', encoding='utf-8') as f:
                    business_data = json.load(f)
                    results["business_users"] = set(business_data.keys())
            
            # Load product users
            product_file = self.product_data_path / "product_users.json"
            if product_file.exists():
                with open(product_file, 'r', encoding='utf-8') as f:
                    product_data = json.load(f)
                    results["product_users"] = set(product_data.keys())
            
            # Check for shared users (this is allowed but should be tracked)
            results["shared_users"] = results["business_users"].intersection(results["product_users"])
            
            # Validate domain field in user data
            for username in results["business_users"]:
                user = business_data.get(username, {})
                if user.get("domain") != "business":
                    results["issues"].append(f"Business user {username} has incorrect domain: {user.get('domain')}")
                    results["isolated"] = False
            
            for username in results["product_users"]:
                user = product_data.get(username, {})
                if user.get("domain") != "product":
                    results["issues"].append(f"Product user {username} has incorrect domain: {user.get('domain')}")
                    results["isolated"] = False
            
            # Generate stats
            results["stats"] = {
                "total_business_users": len(results["business_users"]),
                "total_product_users": len(results["product_users"]),
                "shared_users_count": len(results["shared_users"]),
                "isolation_percentage": (
                    (len(results["business_users"]) + len(results["product_users"]) - len(results["shared_users"])) /
                    max(len(results["business_users"]) + len(results["product_users"]), 1) * 100
                )
            }
            
        except Exception as e:
            results["isolated"] = False
            results["issues"].append(f"Error validating user isolation: {str(e)}")
            logger.error(f"Error in user isolation validation: {e}")
        
        return results
    
    def validate_permission_isolation(self) -> Dict[str, any]:
        """Validate that permissions are domain-specific"""
        results = {
            "isolated": True,
            "issues": [],
            "business_permissions": set(),
            "product_permissions": set(),
            "cross_domain_permissions": set()
        }
        
        try:
            # Expected domain-specific permissions
            business_permissions = {
                "dashboard.view", "leads.view", "opportunities.view", "implementations.view",
                "universities.view", "responsibles.view", "classes.view", "coupons.view",
                "conversion.view", "subscriptions.view", "data_quality.view", "business_rules.view"
            }
            
            product_permissions = {
                "dashboard.view", "users.view", "features.view", "connections.view",
                "payments.view", "agenda.view", "medical_records.view", "accounting.view", "analytics.view"
            }
            
            # Common permissions (allowed across domains)
            common_permissions = {"users.manage", "dashboard.view"}
            
            # Load and validate business users permissions
            business_file = self.business_data_path / "business_users.json"
            if business_file.exists():
                with open(business_file, 'r', encoding='utf-8') as f:
                    business_data = json.load(f)
                    
                for username, user in business_data.items():
                    user_permissions = set(user.get("permissions", []))
                    results["business_permissions"].update(user_permissions)
                    
                    # Check for product-specific permissions in business users
                    invalid_perms = user_permissions.intersection(product_permissions - common_permissions)
                    if invalid_perms:
                        results["issues"].append(f"Business user {username} has product permissions: {invalid_perms}")
                        results["isolated"] = False
            
            # Load and validate product users permissions
            product_file = self.product_data_path / "product_users.json"
            if product_file.exists():
                with open(product_file, 'r', encoding='utf-8') as f:
                    product_data = json.load(f)
                    
                for username, user in product_data.items():
                    user_permissions = set(user.get("permissions", []))
                    results["product_permissions"].update(user_permissions)
                    
                    # Check for business-specific permissions in product users
                    invalid_perms = user_permissions.intersection(business_permissions - common_permissions)
                    if invalid_perms:
                        results["issues"].append(f"Product user {username} has business permissions: {invalid_perms}")
                        results["isolated"] = False
            
            results["cross_domain_permissions"] = results["business_permissions"].intersection(results["product_permissions"])
            
        except Exception as e:
            results["isolated"] = False
            results["issues"].append(f"Error validating permission isolation: {str(e)}")
            logger.error(f"Error in permission isolation validation: {e}")
        
        return results
    
    def validate_data_isolation(self) -> Dict[str, any]:
        """Validate that data files are properly isolated"""
        results = {
            "isolated": True,
            "issues": [],
            "business_files": [],
            "product_files": [],
            "shared_files": []
        }
        
        try:
            # Check business data directory
            if self.business_data_path.exists():
                for file_path in self.business_data_path.rglob("*"):
                    if file_path.is_file():
                        results["business_files"].append(str(file_path.relative_to(self.business_data_path)))
            
            # Check product data directory
            if self.product_data_path.exists():
                for file_path in self.product_data_path.rglob("*"):
                    if file_path.is_file():
                        results["product_files"].append(str(file_path.relative_to(self.product_data_path)))
            
            # Check for shared files (should not exist)
            business_files_set = set(results["business_files"])
            product_files_set = set(results["product_files"])
            results["shared_files"] = list(business_files_set.intersection(product_files_set))
            
            if results["shared_files"]:
                results["issues"].append(f"Shared data files detected: {results['shared_files']}")
                results["isolated"] = False
            
        except Exception as e:
            results["isolated"] = False
            results["issues"].append(f"Error validating data isolation: {str(e)}")
            logger.error(f"Error in data isolation validation: {e}")
        
        return results
    
    def run_full_validation(self) -> Dict[str, any]:
        """Run complete domain isolation validation"""
        logger.info("Starting domain isolation validation...")
        
        results = {
            "timestamp": "2024-12-19T12:00:00Z",
            "overall_isolated": True,
            "validations": {
                "users": self.validate_user_isolation(),
                "permissions": self.validate_permission_isolation(),
                "data": self.validate_data_isolation()
            },
            "summary": {
                "total_issues": 0,
                "critical_issues": 0,
                "warnings": 0
            }
        }
        
        # Aggregate results
        for validation_name, validation_result in results["validations"].items():
            if not validation_result["isolated"]:
                results["overall_isolated"] = False
            
            issues_count = len(validation_result["issues"])
            results["summary"]["total_issues"] += issues_count
            
            # Classify issues
            for issue in validation_result["issues"]:
                if "incorrect domain" in issue or "cross-domain" in issue:
                    results["summary"]["critical_issues"] += 1
                else:
                    results["summary"]["warnings"] += 1
        
        # Log results
        if results["overall_isolated"]:
            logger.info("✅ Domain isolation validation PASSED")
        else:
            logger.warning(f"❌ Domain isolation validation FAILED - {results['summary']['total_issues']} issues found")
            for validation_name, validation_result in results["validations"].items():
                for issue in validation_result["issues"]:
                    logger.warning(f"  {validation_name.upper()}: {issue}")
        
        return results
    
    def fix_isolation_issues(self, validation_results: Dict[str, any]) -> Dict[str, any]:
        """Attempt to fix common isolation issues"""
        fixes_applied = {
            "users_fixed": 0,
            "permissions_cleaned": 0,
            "files_moved": 0,
            "errors": []
        }
        
        try:
            # Fix user domain fields
            user_validation = validation_results["validations"]["users"]
            for issue in user_validation["issues"]:
                if "incorrect domain" in issue:
                    # Extract username and fix domain
                    # This would require more sophisticated parsing
                    pass
            
            logger.info(f"Applied {sum(fixes_applied.values())} fixes")
            
        except Exception as e:
            fixes_applied["errors"].append(str(e))
            logger.error(f"Error applying fixes: {e}")
        
        return fixes_applied

def validate_domain_isolation() -> Dict[str, any]:
    """Convenience function to run domain isolation validation"""
    validator = DomainIsolationValidator()
    return validator.run_full_validation()

def check_user_domain_access(username: str, target_domain: str) -> bool:
    """Check if user has access to specific domain"""
    validator = DomainIsolationValidator()
    
    if target_domain == "business":
        file_path = validator.business_data_path / "business_users.json"
    elif target_domain == "product":
        file_path = validator.product_data_path / "product_users.json"
    else:
        return False
    
    try:
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                users_data = json.load(f)
                return username in users_data and users_data[username].get("is_active", False)
    except Exception as e:
        logger.error(f"Error checking domain access: {e}")
    
    return False
