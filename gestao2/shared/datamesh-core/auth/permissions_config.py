"""
Permissions Configuration for DataHub Amigo One
Centralized configuration for all permissions across domains
"""

# Business Domain Permissions
BUSINESS_PERMISSIONS = {
    'Dashboard': 'dashboard.view',
    'Leads': 'leads.view',
    'Oportunidades': 'opportunities.view',
    'Implementações': 'implementations.view',
    'Universidades': 'universities.view',
    'Responsáveis': 'responsibles.view',
    'Classes': 'classes.view',
    'Cupons': 'coupons.view',
    'Conversão': 'conversion.view',
    'Assinaturas': 'subscriptions.view',
    'Qualidade de Dados': 'data_quality.view',
    'Regras de Negócio': 'business_rules.view',
    'Gerenciar Usuários': 'users.manage'
}

# Product Domain Permissions
PRODUCT_PERMISSIONS = {
    'Dashboard': 'dashboard.view',
    'Usuários': 'users.view',
    'Features': 'features.view',
    'Conexões': 'connections.view',
    'Pagamentos': 'payments.view',
    'Agenda': 'agenda.view',
    'Prontuários': 'medical_records.view',
    'Contabilidade': 'accounting.view',
    'Analytics': 'analytics.view',
    'Gerenciar Usuários': 'users.manage'
}

# Default permissions for new users by role
DEFAULT_PERMISSIONS = {
    'business': {
        'admin': list(BUSINESS_PERMISSIONS.values()),
        'user': [
            'dashboard.view',
            'leads.view',
            'opportunities.view',
            'implementations.view',
            'universities.view'
        ]
    },
    'product': {
        'admin': list(PRODUCT_PERMISSIONS.values()),
        'user': [
            'dashboard.view',
            'users.view',
            'features.view',
            'analytics.view'
        ]
    }
}

# Route to permission mapping
ROUTE_PERMISSIONS = {
    'business': {
        'dashboard.index': 'dashboard.view',
        'dashboard.leads': 'leads.view',
        'dashboard.opportunities': 'opportunities.view',
        'dashboard.implementations': 'implementations.view',
        'dashboard.universities': 'universities.view',
        'dashboard.responsibles': 'responsibles.view',
        'dashboard.classes': 'classes.view',
        'dashboard.coupons': 'coupons.view',
        'dashboard.conversion': 'conversion.view',
        'dashboard.subscriptions': 'subscriptions.view',
        'dashboard.data_quality': 'data_quality.view',
        'dashboard.business_rules': 'business_rules.view',
        'admin.users': 'users.manage',
        'admin.monitoring': 'users.manage'
    },
    'product': {
        'dashboard': 'dashboard.view',
        'users': 'users.view',
        'features': 'features.view',
        'connections': 'connections.view',
        'payments': 'payments.view',
        'agenda': 'agenda.view',
        'medical_records': 'medical_records.view',
        'accounting': 'accounting.view',
        'analytics': 'analytics.view',
        'admin_users': 'users.manage',
        'admin_monitoring': 'users.manage'
    }
}

def get_permissions_for_domain(domain: str) -> dict:
    """Get all permissions for a specific domain"""
    if domain == 'business':
        return BUSINESS_PERMISSIONS
    elif domain == 'product':
        return PRODUCT_PERMISSIONS
    else:
        return {}

def get_default_permissions(domain: str, role: str) -> list:
    """Get default permissions for a user role in a domain"""
    return DEFAULT_PERMISSIONS.get(domain, {}).get(role, [])

def get_route_permission(domain: str, route: str) -> str:
    """Get required permission for a specific route"""
    return ROUTE_PERMISSIONS.get(domain, {}).get(route)

def is_admin_permission(permission: str) -> bool:
    """Check if a permission is admin-only"""
    admin_permissions = ['users.manage']
    return permission in admin_permissions

def get_permission_description(permission: str) -> str:
    """Get human-readable description for a permission"""
    descriptions = {
        # Business permissions
        'dashboard.view': 'Visualizar dashboard principal',
        'leads.view': 'Visualizar leads',
        'opportunities.view': 'Visualizar oportunidades',
        'implementations.view': 'Visualizar implementações',
        'universities.view': 'Visualizar universidades',
        'responsibles.view': 'Visualizar responsáveis',
        'classes.view': 'Visualizar classes',
        'coupons.view': 'Visualizar cupons',
        'conversion.view': 'Visualizar conversões',
        'subscriptions.view': 'Visualizar assinaturas',
        'data_quality.view': 'Visualizar qualidade de dados',
        'business_rules.view': 'Visualizar regras de negócio',

        # Product permissions
        'users.view': 'Visualizar usuários',
        'features.view': 'Visualizar features',
        'connections.view': 'Visualizar conexões',
        'payments.view': 'Visualizar pagamentos',
        'agenda.view': 'Visualizar agenda',
        'medical_records.view': 'Visualizar prontuários',
        'accounting.view': 'Visualizar contabilidade',
        'analytics.view': 'Visualizar analytics',

        # Common permissions
        'users.manage': 'Gerenciar usuários e permissões'
    }

    return descriptions.get(permission, permission)

def validate_permissions(permissions: list, domain: str) -> tuple:
    """Validate a list of permissions for a domain"""
    valid_permissions = set(get_permissions_for_domain(domain).values())
    provided_permissions = set(permissions)

    valid = provided_permissions.intersection(valid_permissions)
    invalid = provided_permissions.difference(valid_permissions)

    return list(valid), list(invalid)

def get_permission_categories(domain: str) -> dict:
    """Get permissions organized by categories"""
    if domain == 'business':
        return {
            'Visualização de Dados': [
                'dashboard.view',
                'leads.view',
                'opportunities.view',
                'implementations.view',
                'universities.view',
                'responsibles.view',
                'classes.view'
            ],
            'Análise e Relatórios': [
                'coupons.view',
                'conversion.view',
                'subscriptions.view',
                'data_quality.view'
            ],
            'Configuração': [
                'business_rules.view'
            ],
            'Administração': [
                'users.manage'
            ]
        }
    elif domain == 'product':
        return {
            'Visualização Geral': [
                'dashboard.view',
                'users.view',
                'features.view',
                'analytics.view'
            ],
            'Operações': [
                'connections.view',
                'payments.view',
                'agenda.view'
            ],
            'Dados Clínicos': [
                'medical_records.view'
            ],
            'Financeiro': [
                'accounting.view'
            ],
            'Administração': [
                'users.manage'
            ]
        }
    else:
        return {}

def get_minimum_permissions(domain: str) -> list:
    """Get minimum permissions every user should have"""
    if domain == 'business':
        return ['dashboard.view']
    elif domain == 'product':
        return ['dashboard.view']
    else:
        return []

def can_assign_permission(assigner_permissions: list, permission: str) -> bool:
    """Check if a user can assign a specific permission to another user"""
    # Only users with users.manage can assign permissions
    if 'users.manage' not in assigner_permissions:
        return False

    # Admin permissions can only be assigned by other admins
    if is_admin_permission(permission):
        return 'users.manage' in assigner_permissions

    return True

def get_admin_users() -> list:
    """Get list of admin usernames"""
    return ['admin', 'TTK', 'bruno@abreu', 'bruno@bruno']

def is_admin_user(username: str) -> bool:
    """Check if username is an admin user"""
    return username in get_admin_users()

def get_team_members() -> dict:
    """Get team member information"""
    return {
        'sergio': {
            'name': 'Sergio',
            'role': 'Engenharia de Dados',
            'department': 'Data Engineering',
            'permissions_level': 'user'
        },
        'bruno': {
            'name': 'Bruno',
            'role': 'Analista de Negócios',
            'department': 'Business Analysis',
            'permissions_level': 'user'
        },
        'TTK': {
            'name': 'TTK',
            'role': 'Administrador',
            'department': 'Administration',
            'permissions_level': 'admin'
        },
        'bruno@bruno': {
            'name': 'Bruno Admin',
            'role': 'Administrador Global',
            'department': 'Administration',
            'permissions_level': 'admin'
        }
    }

def get_permission_matrix() -> dict:
    """Get complete permission matrix for both domains"""
    return {
        'business': {
            'permissions': BUSINESS_PERMISSIONS,
            'routes': ROUTE_PERMISSIONS['business'],
            'defaults': DEFAULT_PERMISSIONS['business']
        },
        'product': {
            'permissions': PRODUCT_PERMISSIONS,
            'routes': ROUTE_PERMISSIONS['product'],
            'defaults': DEFAULT_PERMISSIONS['product']
        }
    }
