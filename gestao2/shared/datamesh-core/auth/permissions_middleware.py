"""
Permissions Middleware for DataHub Amigo One
Provides page-level access control based on user permissions
"""

import json
import os
import logging
from functools import wraps
from flask import session, request, redirect, url_for, flash, abort
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

class PermissionsMiddleware:
    """Middleware for handling user permissions"""
    
    def __init__(self, domain: str):
        self.domain = domain
        self.users_file = self._get_users_file()
        
    def _get_users_file(self) -> str:
        """Get users file path for domain"""
        if self.domain == 'business':
            return 'gestao/data/business_users.json'
        else:
            return 'produto/ux-gabi/data/product_users.json'
    
    def _load_users(self) -> Dict[str, Any]:
        """Load users from file"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading users: {e}")
        return {}
    
    def get_user_permissions(self, username: str) -> List[str]:
        """Get user permissions"""
        users = self._load_users()
        user = users.get(username, {})
        return user.get('permissions', [])
    
    def has_permission(self, username: str, permission: str) -> bool:
        """Check if user has specific permission"""
        if not username:
            return False
            
        # Admin users have all permissions
        if username in ['admin', 'TTK', 'bruno@abreu', 'bruno@bruno']:
            return True
            
        user_permissions = self.get_user_permissions(username)
        return permission in user_permissions
    
    def require_permission(self, permission: str, redirect_route: str = None):
        """Decorator to require specific permission for route access"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                username = session.get('username')
                
                if not username:
                    flash('Você precisa fazer login para acessar esta página.', 'error')
                    return redirect(url_for('auth.login'))
                
                if not self.has_permission(username, permission):
                    logger.warning(f"User {username} denied access to {permission}")
                    flash('Você não tem permissão para acessar esta página.', 'error')
                    
                    if redirect_route:
                        return redirect(url_for(redirect_route))
                    else:
                        # Redirect to dashboard or main page
                        if self.domain == 'business':
                            return redirect(url_for('dashboard.index'))
                        else:
                            return redirect(url_for('dashboard'))
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    def require_admin(self):
        """Decorator to require admin access"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                username = session.get('username')
                
                if not username:
                    flash('Você precisa fazer login para acessar esta página.', 'error')
                    return redirect(url_for('auth.login'))
                
                if username not in ['admin', 'TTK', 'bruno@abreu', 'bruno@bruno']:
                    logger.warning(f"User {username} denied admin access")
                    flash('Acesso negado. Apenas administradores podem acessar esta página.', 'error')
                    
                    if self.domain == 'business':
                        return redirect(url_for('dashboard.index'))
                    else:
                        return redirect(url_for('dashboard'))
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    def log_access_attempt(self, username: str, permission: str, granted: bool):
        """Log access attempt for monitoring"""
        try:
            # Import here to avoid circular imports
            import sys
            from pathlib import Path
            
            # Add shared core to path
            CORE_PATH = Path(__file__).parent.parent
            sys.path.insert(0, str(CORE_PATH))
            
            from admin.monitoring_service import MonitoringService
            
            monitoring = MonitoringService(self.domain)
            monitoring.log_user_activity(
                user_id=username,
                action='permission_check',
                page=permission,
                details={
                    'permission': permission,
                    'granted': granted,
                    'domain': self.domain
                }
            )
        except Exception as e:
            logger.error(f"Error logging access attempt: {e}")

# Global instances for each domain
business_permissions = PermissionsMiddleware('business')
product_permissions = PermissionsMiddleware('product')

# Convenience functions for decorators
def require_business_permission(permission: str, redirect_route: str = None):
    """Require permission for business domain"""
    return business_permissions.require_permission(permission, redirect_route)

def require_product_permission(permission: str, redirect_route: str = None):
    """Require permission for product domain"""
    return product_permissions.require_permission(permission, redirect_route)

def require_business_admin():
    """Require admin access for business domain"""
    return business_permissions.require_admin()

def require_product_admin():
    """Require admin access for product domain"""
    return product_permissions.require_admin()

# Page permission constants for business domain
class BusinessPermissions:
    DASHBOARD_VIEW = 'dashboard.view'
    LEADS_VIEW = 'leads.view'
    OPPORTUNITIES_VIEW = 'opportunities.view'
    IMPLEMENTATIONS_VIEW = 'implementations.view'
    UNIVERSITIES_VIEW = 'universities.view'
    RESPONSIBLES_VIEW = 'responsibles.view'
    CLASSES_VIEW = 'classes.view'
    COUPONS_VIEW = 'coupons.view'
    CONVERSION_VIEW = 'conversion.view'
    SUBSCRIPTIONS_VIEW = 'subscriptions.view'
    DATA_QUALITY_VIEW = 'data_quality.view'
    BUSINESS_RULES_VIEW = 'business_rules.view'
    USERS_MANAGE = 'users.manage'

# Page permission constants for product domain
class ProductPermissions:
    DASHBOARD_VIEW = 'dashboard.view'
    USERS_VIEW = 'users.view'
    FEATURES_VIEW = 'features.view'
    CONNECTIONS_VIEW = 'connections.view'
    PAYMENTS_VIEW = 'payments.view'
    AGENDA_VIEW = 'agenda.view'
    MEDICAL_RECORDS_VIEW = 'medical_records.view'
    ACCOUNTING_VIEW = 'accounting.view'
    ANALYTICS_VIEW = 'analytics.view'
    USERS_MANAGE = 'users.manage'

def check_page_permission(domain: str, username: str, page_route: str) -> bool:
    """Check if user has permission to access a specific page route"""
    if domain == 'business':
        middleware = business_permissions
    else:
        middleware = product_permissions
    
    # Map routes to permissions
    route_permissions = {
        # Business routes
        'dashboard.index': BusinessPermissions.DASHBOARD_VIEW,
        'dashboard.leads': BusinessPermissions.LEADS_VIEW,
        'dashboard.opportunities': BusinessPermissions.OPPORTUNITIES_VIEW,
        'dashboard.implementations': BusinessPermissions.IMPLEMENTATIONS_VIEW,
        'dashboard.universities': BusinessPermissions.UNIVERSITIES_VIEW,
        'dashboard.responsibles': BusinessPermissions.RESPONSIBLES_VIEW,
        'dashboard.classes': BusinessPermissions.CLASSES_VIEW,
        'dashboard.coupons': BusinessPermissions.COUPONS_VIEW,
        'dashboard.conversion': BusinessPermissions.CONVERSION_VIEW,
        'dashboard.subscriptions': BusinessPermissions.SUBSCRIPTIONS_VIEW,
        'dashboard.data_quality': BusinessPermissions.DATA_QUALITY_VIEW,
        'dashboard.business_rules': BusinessPermissions.BUSINESS_RULES_VIEW,
        
        # Product routes
        'dashboard': ProductPermissions.DASHBOARD_VIEW,
        'users': ProductPermissions.USERS_VIEW,
        'features': ProductPermissions.FEATURES_VIEW,
        'connections': ProductPermissions.CONNECTIONS_VIEW,
        'payments': ProductPermissions.PAYMENTS_VIEW,
        'agenda': ProductPermissions.AGENDA_VIEW,
        'medical_records': ProductPermissions.MEDICAL_RECORDS_VIEW,
        'accounting': ProductPermissions.ACCOUNTING_VIEW,
        'analytics': ProductPermissions.ANALYTICS_VIEW,
    }
    
    permission = route_permissions.get(page_route)
    if not permission:
        # If no specific permission is defined, allow access
        return True
    
    has_access = middleware.has_permission(username, permission)
    middleware.log_access_attempt(username, permission, has_access)
    
    return has_access

def get_user_accessible_pages(domain: str, username: str) -> List[str]:
    """Get list of pages user can access"""
    if domain == 'business':
        middleware = business_permissions
        all_permissions = {
            'Dashboard': BusinessPermissions.DASHBOARD_VIEW,
            'Leads': BusinessPermissions.LEADS_VIEW,
            'Oportunidades': BusinessPermissions.OPPORTUNITIES_VIEW,
            'Implementações': BusinessPermissions.IMPLEMENTATIONS_VIEW,
            'Universidades': BusinessPermissions.UNIVERSITIES_VIEW,
            'Responsáveis': BusinessPermissions.RESPONSIBLES_VIEW,
            'Classes': BusinessPermissions.CLASSES_VIEW,
            'Cupons': BusinessPermissions.COUPONS_VIEW,
            'Conversão': BusinessPermissions.CONVERSION_VIEW,
            'Assinaturas': BusinessPermissions.SUBSCRIPTIONS_VIEW,
            'Qualidade de Dados': BusinessPermissions.DATA_QUALITY_VIEW,
            'Regras de Negócio': BusinessPermissions.BUSINESS_RULES_VIEW,
        }
    else:
        middleware = product_permissions
        all_permissions = {
            'Dashboard': ProductPermissions.DASHBOARD_VIEW,
            'Usuários': ProductPermissions.USERS_VIEW,
            'Features': ProductPermissions.FEATURES_VIEW,
            'Conexões': ProductPermissions.CONNECTIONS_VIEW,
            'Pagamentos': ProductPermissions.PAYMENTS_VIEW,
            'Agenda': ProductPermissions.AGENDA_VIEW,
            'Prontuários': ProductPermissions.MEDICAL_RECORDS_VIEW,
            'Contabilidade': ProductPermissions.ACCOUNTING_VIEW,
            'Analytics': ProductPermissions.ANALYTICS_VIEW,
        }
    
    accessible_pages = []
    for page_name, permission in all_permissions.items():
        if middleware.has_permission(username, permission):
            accessible_pages.append(page_name)
    
    return accessible_pages
