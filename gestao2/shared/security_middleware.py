"""
Shared Security Middleware for DataHub Amigo One
Cross-domain security utilities and middleware
"""

import re
import time
import hashlib
import secrets
from functools import wraps
from flask import request, session, abort, g, current_app
import logging

logger = logging.getLogger(__name__)

class DataHubSecurityMiddleware:
    """Shared security middleware for both Business and Product domains"""
    
    def __init__(self):
        self.blocked_ips = set()
        self.suspicious_patterns = [
            r'<script[^>]*>.*?</script>',  # <PERSON>ript tags
            r'javascript:',  # JavaScript URLs
            r'on\w+\s*=',  # Event handlers
            r'eval\s*\(',  # eval() calls
            r'document\.cookie',  # Cookie access
            r'window\.location',  # Location manipulation
        ]
    
    def validate_request_data(self, data):
        """Validate and sanitize request data"""
        if isinstance(data, dict):
            return {key: self.validate_request_data(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self.validate_request_data(item) for item in data]
        elif isinstance(data, str):
            return self.sanitize_string(data)
        else:
            return data
    
    def sanitize_string(self, text):
        """Sanitize string input"""
        if not isinstance(text, str):
            return text
        
        # Check for suspicious patterns
        for pattern in self.suspicious_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                logger.warning(f"Suspicious pattern detected: {pattern} in input: {text[:100]}")
                # Remove the suspicious content
                text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        # Basic HTML escaping
        text = text.replace('&', '&amp;')
        text = text.replace('<', '&lt;')
        text = text.replace('>', '&gt;')
        text = text.replace('"', '&quot;')
        text = text.replace("'", '&#x27;')
        
        return text
    
    def check_rate_limit(self, identifier, limit=100, window=3600):
        """Advanced rate limiting with sliding window"""
        current_time = time.time()
        
        if not hasattr(g, 'rate_limits'):
            g.rate_limits = {}
        
        if identifier not in g.rate_limits:
            g.rate_limits[identifier] = []
        
        # Clean old entries
        g.rate_limits[identifier] = [
            timestamp for timestamp in g.rate_limits[identifier]
            if current_time - timestamp < window
        ]
        
        # Check if limit exceeded
        if len(g.rate_limits[identifier]) >= limit:
            self.log_security_event('RATE_LIMIT_EXCEEDED', f'Identifier: {identifier}')
            return False
        
        # Add current request
        g.rate_limits[identifier].append(current_time)
        return True
    
    def validate_file_security(self, file):
        """Comprehensive file validation"""
        if not file or not file.filename:
            return False, "No file provided"
        
        filename = file.filename.lower()
        
        # Check file extension
        allowed_extensions = {'.csv', '.xlsx', '.xls', '.pdf', '.png', '.jpg', '.jpeg', '.gif'}
        file_ext = '.' + filename.rsplit('.', 1)[1] if '.' in filename else ''
        
        if file_ext not in allowed_extensions:
            return False, f"File type {file_ext} not allowed"
        
        # Check file size
        file.seek(0, 2)
        file_size = file.tell()
        file.seek(0)
        
        max_size = 10 * 1024 * 1024  # 10MB
        if file_size > max_size:
            return False, f"File too large: {file_size} bytes (max: {max_size})"
        
        # Check for suspicious filenames
        suspicious_names = ['..', '/', '\\', 'script', 'exe', 'bat', 'cmd']
        if any(name in filename for name in suspicious_names):
            return False, "Suspicious filename detected"
        
        return True, "File is valid"
    
    def generate_secure_token(self, length=32):
        """Generate cryptographically secure token"""
        return secrets.token_urlsafe(length)
    
    def hash_sensitive_data(self, data):
        """Hash sensitive data for logging/storage"""
        if isinstance(data, str):
            return hashlib.sha256(data.encode()).hexdigest()[:16]
        return str(hash(str(data)))[:16]
    
    def log_security_event(self, event_type, details, severity='WARNING'):
        """Log security events with structured format"""
        event_data = {
            'timestamp': time.time(),
            'event_type': event_type,
            'details': details,
            'ip_address': request.remote_addr if request else 'unknown',
            'user_agent': request.headers.get('User-Agent', '') if request else '',
            'user_id': session.get('user_id', 'anonymous') if session else 'unknown',
            'endpoint': request.endpoint if request else 'unknown'
        }
        
        log_message = f"SECURITY EVENT: {event_type} - {details}"
        
        if severity == 'CRITICAL':
            logger.critical(log_message, extra=event_data)
        elif severity == 'ERROR':
            logger.error(log_message, extra=event_data)
        else:
            logger.warning(log_message, extra=event_data)

def require_secure_headers(f):
    """Decorator to ensure secure headers are present"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        response = f(*args, **kwargs)
        
        # Add security headers if not present
        if hasattr(response, 'headers'):
            response.headers.setdefault('X-Content-Type-Options', 'nosniff')
            response.headers.setdefault('X-Frame-Options', 'DENY')
            response.headers.setdefault('X-XSS-Protection', '1; mode=block')
            
            if not current_app.debug:
                response.headers.setdefault(
                    'Strict-Transport-Security', 
                    'max-age=31536000; includeSubDomains'
                )
        
        return response
    return decorated_function

def validate_input_data(required_fields=None, max_length=1000):
    """Decorator to validate input data"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            security_middleware = DataHubSecurityMiddleware()
            
            # Validate JSON data for POST requests
            if request.method == 'POST' and request.is_json:
                data = request.get_json()
                
                # Check required fields
                if required_fields:
                    missing_fields = [field for field in required_fields if field not in data]
                    if missing_fields:
                        abort(400, description=f"Missing required fields: {', '.join(missing_fields)}")
                
                # Validate and sanitize data
                sanitized_data = security_middleware.validate_request_data(data)
                request._cached_json = sanitized_data
            
            # Validate form data
            elif request.method == 'POST' and request.form:
                for key, value in request.form.items():
                    if len(value) > max_length:
                        abort(400, description=f"Field {key} exceeds maximum length")
                    
                    # Check for suspicious patterns
                    sanitized_value = security_middleware.sanitize_string(value)
                    if sanitized_value != value:
                        security_middleware.log_security_event(
                            'SUSPICIOUS_INPUT_DETECTED',
                            f'Field: {key}, Original length: {len(value)}, Sanitized length: {len(sanitized_value)}'
                        )
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def rate_limit(limit=100, window=3600, per='ip'):
    """Decorator for rate limiting"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            security_middleware = DataHubSecurityMiddleware()
            
            # Determine identifier for rate limiting
            if per == 'ip':
                identifier = f"ip_{request.remote_addr}"
            elif per == 'user':
                identifier = f"user_{session.get('user_id', 'anonymous')}"
            else:
                identifier = f"endpoint_{request.endpoint}"
            
            # Check rate limit
            if not security_middleware.check_rate_limit(identifier, limit, window):
                abort(429, description="Rate limit exceeded")
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def secure_file_upload(allowed_extensions=None, max_size=10*1024*1024):
    """Decorator for secure file uploads"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            security_middleware = DataHubSecurityMiddleware()
            
            # Check if file is in request
            if 'file' in request.files:
                file = request.files['file']
                is_valid, message = security_middleware.validate_file_security(file)
                
                if not is_valid:
                    security_middleware.log_security_event(
                        'INVALID_FILE_UPLOAD',
                        f'File: {file.filename}, Reason: {message}'
                    )
                    abort(400, description=message)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
