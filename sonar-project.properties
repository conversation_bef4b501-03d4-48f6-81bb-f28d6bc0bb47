# SonarCloud Configuration for DataHub Amigo One - Produto Domain
sonar.projectKey=brunodeabreu-art_dataapp
sonar.projectName=DataHub Amigo One - Produto Domain
sonar.projectVersion=1.0
sonar.organization=brunodeabreu-art
sonar.host.url=https://sonarcloud.io

# Source code configuration - APENAS DOMÍNIO PRODUTO
sonar.sources=produto
sonar.sourceEncoding=UTF-8

# Multi-language support
sonar.python.version=3.11
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.typescript.lcov.reportPaths=coverage/lcov.info

# Include HTML, CSS, JavaScript analysis
sonar.inclusions=**/*.py,**/*.html,**/*.js,**/*.css,**/*.ts,**/*.jsx,**/*.tsx,**/*.md

# Exclusions - Security focused (Produto domain only)
sonar.exclusions=**/__pycache__/**,**/node_modules/**,**/*.pyc,**/logs/**,**/data/**,**/venv/**,**/migrations/**,**/static/lib/**,**/static/vendor/**,**/sonar-scanner-*/**,**/.sonar/**,**/.scannerwork/**,**/users.db,**/database_init.log,**/*.log

# Test configuration (Produto domain)
sonar.tests=produto/ux-gabi/tests
sonar.test.inclusions=**/test_*.py,**/*_test.py,**/tests/**/*.py
sonar.test.exclusions=**/conftest.py

# Coverage configuration
sonar.python.coverage.reportPaths=coverage.xml,tests/coverage.xml
sonar.coverage.exclusions=**/test_*.py,**/*_test.py,**/tests/**,**/conftest.py

# Quality gate
sonar.qualitygate.wait=true

# Security Analysis - Enhanced for DataHub
sonar.security.hotspots.enabled=true
sonar.security.review.enabled=true

# DataHub specific security patterns
sonar.python.security.patterns=password,secret,token,key,api_key,private_key,auth,credential,session,jwt,oauth

# Web Security Analysis
sonar.web.security.enabled=true
sonar.html.security.enabled=true
sonar.javascript.security.enabled=true

# Code Quality Rules
sonar.python.pylint.reportPaths=pylint-report.txt
sonar.python.bandit.reportPaths=bandit-report.json

# Duplication detection
sonar.cpd.exclusions=**/test_*.py,**/*_test.py

# Analysis scope for DataMesh architecture
sonar.analysis.mode=publish
